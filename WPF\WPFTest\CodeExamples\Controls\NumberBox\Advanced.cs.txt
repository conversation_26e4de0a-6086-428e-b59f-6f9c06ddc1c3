// NumberBox C# 高级功能示例
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.ComponentModel;
using System.Windows.Media;

namespace WPFTest.ViewModels.InputControls
{
    public partial class NumberBoxAdvancedViewModel : ObservableObject
    {
        #region 基础属性

        /// <summary>
        /// 基础数值
        /// </summary>
        [ObservableProperty]
        private double basicValue = 0;

        /// <summary>
        /// 最小值
        /// </summary>
        [ObservableProperty]
        private double minimumValue = 0;

        /// <summary>
        /// 最大值
        /// </summary>
        [ObservableProperty]
        private double maximumValue = 100;

        /// <summary>
        /// 步进值
        /// </summary>
        [ObservableProperty]
        private double stepValue = 1;

        /// <summary>
        /// 是否启用范围验证
        /// </summary>
        [ObservableProperty]
        private bool enableRangeValidation = true;

        /// <summary>
        /// 是否启用格式验证
        /// </summary>
        [ObservableProperty]
        private bool enableFormatValidation = true;

        /// <summary>
        /// 温度值
        /// </summary>
        [ObservableProperty]
        private double temperatureValue = 25.5;

        /// <summary>
        /// 重量值
        /// </summary>
        [ObservableProperty]
        private double weightValue = 68.5;

        /// <summary>
        /// 华氏度值（计算属性）
        /// </summary>
        public double FahrenheitValue => TemperatureValue * 9.0 / 5.0 + 32.0;

        /// <summary>
        /// 磅值（计算属性）
        /// </summary>
        public double PoundsValue => WeightValue * 2.20462;

        #endregion

        #region 验证属性

        /// <summary>
        /// 验证结果
        /// </summary>
        [ObservableProperty]
        private NumberValidationResult validationResult = new();

        /// <summary>
        /// 验证消息
        /// </summary>
        [ObservableProperty]
        private string validationMessage = string.Empty;

        /// <summary>
        /// 验证消息颜色
        /// </summary>
        [ObservableProperty]
        private Brush validationMessageColor = Brushes.Gray;

        #endregion

        #region 构造函数

        public NumberBoxAdvancedViewModel()
        {
            // 监听属性变化
            PropertyChanged += OnPropertyChanged;

            // 初始化验证结果
            ValidationResult = new NumberValidationResult();
        }

        #endregion

        #region 属性变化处理

        private void OnPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            switch (e.PropertyName)
            {
                case nameof(BasicValue):
                    ValidateBasicValue();
                    break;
                case nameof(TemperatureValue):
                    OnPropertyChanged(nameof(FahrenheitValue));
                    break;
                case nameof(WeightValue):
                    OnPropertyChanged(nameof(PoundsValue));
                    break;
            }
        }

        #endregion

        #region 验证方法

        /// <summary>
        /// 验证基础数值
        /// </summary>
        private void ValidateBasicValue()
        {
            if (BasicValue < MinimumValue || BasicValue > MaximumValue)
            {
                ValidationMessage = $"数值应在 {MinimumValue} - {MaximumValue} 范围内";
                ValidationMessageColor = new SolidColorBrush(Colors.Orange);
            }
            else
            {
                ValidationMessage = "✅ 数值有效";
                ValidationMessageColor = new SolidColorBrush(Colors.Green);
            }
        }

        /// <summary>
        /// 验证数值
        /// </summary>
        /// <param name="value">要验证的数值</param>
        /// <returns>验证结果</returns>
        private NumberValidationResult ValidateNumberValue(double value)
        {
            var result = new NumberValidationResult();

            if (EnableRangeValidation)
            {
                if (value < MinimumValue || value > MaximumValue)
                {
                    result.Errors.Add($"数值必须在 {MinimumValue} - {MaximumValue} 范围内");
                }
            }

            if (EnableFormatValidation)
            {
                if (double.IsNaN(value) || double.IsInfinity(value))
                {
                    result.Errors.Add("数值格式无效");
                }
            }

            result.IsValid = result.Errors.Count == 0;
            return result;
        }

        #endregion

        #region 命令

        /// <summary>
        /// 验证数值命令
        /// </summary>
        [RelayCommand]
        private void ValidateNumber()
        {
            var result = ValidateNumberValue(BasicValue);
            ValidationResult = result;

            if (result.IsValid)
            {
                ValidationMessage = "✅ 数值验证通过";
                ValidationMessageColor = new SolidColorBrush(Colors.Green);
            }
            else
            {
                ValidationMessage = $"❌ {string.Join(", ", result.Errors)}";
                ValidationMessageColor = new SolidColorBrush(Colors.Red);
            }
        }

        #endregion

        #region 单位转换方法

        /// <summary>
        /// 摄氏度转华氏度
        /// </summary>
        /// <param name="celsius">摄氏度</param>
        /// <returns>华氏度</returns>
        public static double CelsiusToFahrenheit(double celsius)
        {
            return celsius * 9.0 / 5.0 + 32.0;
        }

        /// <summary>
        /// 华氏度转摄氏度
        /// </summary>
        /// <param name="fahrenheit">华氏度</param>
        /// <returns>摄氏度</returns>
        public static double FahrenheitToCelsius(double fahrenheit)
        {
            return (fahrenheit - 32.0) * 5.0 / 9.0;
        }

        /// <summary>
        /// 千克转磅
        /// </summary>
        /// <param name="kilograms">千克</param>
        /// <returns>磅</returns>
        public static double KilogramsToPounds(double kilograms)
        {
            return kilograms * 2.20462;
        }

        /// <summary>
        /// 磅转千克
        /// </summary>
        /// <param name="pounds">磅</param>
        /// <returns>千克</returns>
        public static double PoundsToKilograms(double pounds)
        {
            return pounds / 2.20462;
        }

        #endregion
    }

    /// <summary>
    /// 数字验证结果
    /// </summary>
    public class NumberValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
        public string Summary => IsValid ? "验证通过" : $"发现 {Errors.Count} 个错误";
    }
}

        /// <summary>
        /// 计算器结果
        /// </summary>
        [ObservableProperty]
        private double calculatorResult = 0;

        /// <summary>
        /// 摄氏度值
        /// </summary>
        [ObservableProperty]
        private double celsiusValue = 25;

        /// <summary>
        /// 华氏度值
        /// </summary>
        [ObservableProperty]
        private double fahrenheitValue = 77;

        /// <summary>
        /// 开尔文值
        /// </summary>
        [ObservableProperty]
        private double kelvinValue = 298.15;

        /// <summary>
        /// 米值
        /// </summary>
        [ObservableProperty]
        private double meterValue = 1;

        /// <summary>
        /// 厘米值
        /// </summary>
        [ObservableProperty]
        private double centimeterValue = 100;

        /// <summary>
        /// 英寸值
        /// </summary>
        [ObservableProperty]
        private double inchValue = 39.37;

        /// <summary>
        /// 统计输入值
        /// </summary>
        [ObservableProperty]
        private double statisticInputValue = 0;

        /// <summary>
        /// 统计数值列表
        /// </summary>
        public ObservableCollection<double> StatisticValues { get; } = new();

        /// <summary>
        /// 统计总数
        /// </summary>
        public int StatisticCount => StatisticValues.Count;

        /// <summary>
        /// 统计平均值
        /// </summary>
        public double StatisticAverage => StatisticValues.Count > 0 ? StatisticValues.Average() : 0;

        /// <summary>
        /// 统计最小值
        /// </summary>
        public double StatisticMinimum => StatisticValues.Count > 0 ? StatisticValues.Min() : 0;

        /// <summary>
        /// 统计最大值
        /// </summary>
        public double StatisticMaximum => StatisticValues.Count > 0 ? StatisticValues.Max() : 0;

        /// <summary>
        /// 构造函数
        /// </summary>
        public NumberBoxAdvancedViewModel()
        {
            // 监听属性变化
            PropertyChanged += OnPropertyChanged;
            
            // 监听集合变化
            StatisticValues.CollectionChanged += (s, e) => 
            {
                OnPropertyChanged(nameof(StatisticCount));
                OnPropertyChanged(nameof(StatisticAverage));
                OnPropertyChanged(nameof(StatisticMinimum));
                OnPropertyChanged(nameof(StatisticMaximum));
            };
        }

        /// <summary>
        /// 属性变化处理
        /// </summary>
        private void OnPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            switch (e.PropertyName)
            {
                case nameof(CelsiusValue):
                    UpdateTemperatureFromCelsius();
                    break;
                case nameof(FahrenheitValue):
                    UpdateTemperatureFromFahrenheit();
                    break;
                case nameof(KelvinValue):
                    UpdateTemperatureFromKelvin();
                    break;
                case nameof(MeterValue):
                    UpdateLengthFromMeter();
                    break;
                case nameof(CentimeterValue):
                    UpdateLengthFromCentimeter();
                    break;
                case nameof(InchValue):
                    UpdateLengthFromInch();
                    break;
            }
        }

        /// <summary>
        /// 验证数值命令
        /// </summary>
        [RelayCommand]
        private void ValidateNumber()
        {
            ValidationResult = ValidateNumberValue(ValidatedValue);
        }

        /// <summary>
        /// 计算命令
        /// </summary>
        [RelayCommand]
        private void Calculate()
        {
            // 这里可以根据选择的运算符进行计算
            // 简化示例，只做加法
            CalculatorResult = CalculatorValueA + CalculatorValueB;
        }

        /// <summary>
        /// 添加统计值命令
        /// </summary>
        [RelayCommand]
        private void AddStatisticValue()
        {
            StatisticValues.Add(StatisticInputValue);
            StatisticInputValue = 0;
        }

        /// <summary>
        /// 验证数值
        /// </summary>
        private NumberValidationResult ValidateNumberValue(double value)
        {
            var result = new NumberValidationResult();
            
            if (EnableRangeValidation)
            {
                if (value < MinimumValue)
                {
                    result.Errors.Add($"数值不能小于 {MinimumValue}");
                }
                
                if (value > MaximumValue)
                {
                    result.Errors.Add($"数值不能大于 {MaximumValue}");
                }
            }

            if (EnableFormatValidation)
            {
                if (double.IsNaN(value))
                {
                    result.Errors.Add("数值格式无效");
                }
                
                if (double.IsInfinity(value))
                {
                    result.Errors.Add("数值超出范围");
                }
            }

            result.IsValid = result.Errors.Count == 0;
            return result;
        }

        /// <summary>
        /// 从摄氏度更新温度
        /// </summary>
        private void UpdateTemperatureFromCelsius()
        {
            FahrenheitValue = CelsiusValue * 9 / 5 + 32;
            KelvinValue = CelsiusValue + 273.15;
        }

        /// <summary>
        /// 从华氏度更新温度
        /// </summary>
        private void UpdateTemperatureFromFahrenheit()
        {
            CelsiusValue = (FahrenheitValue - 32) * 5 / 9;
            KelvinValue = CelsiusValue + 273.15;
        }

        /// <summary>
        /// 从开尔文更新温度
        /// </summary>
        private void UpdateTemperatureFromKelvin()
        {
            CelsiusValue = KelvinValue - 273.15;
            FahrenheitValue = CelsiusValue * 9 / 5 + 32;
        }

        /// <summary>
        /// 从米更新长度
        /// </summary>
        private void UpdateLengthFromMeter()
        {
            CentimeterValue = MeterValue * 100;
            InchValue = MeterValue * 39.3701;
        }

        /// <summary>
        /// 从厘米更新长度
        /// </summary>
        private void UpdateLengthFromCentimeter()
        {
            MeterValue = CentimeterValue / 100;
            InchValue = CentimeterValue * 0.393701;
        }

        /// <summary>
        /// 从英寸更新长度
        /// </summary>
        private void UpdateLengthFromInch()
        {
            MeterValue = InchValue / 39.3701;
            CentimeterValue = InchValue / 0.393701;
        }

        /// <summary>
        /// 格式化数值
        /// </summary>
        /// <param name="value">数值</param>
        /// <param name="format">格式字符串</param>
        /// <returns>格式化后的字符串</returns>
        public string FormatNumber(double value, string format)
        {
            try
            {
                return value.ToString(format, CultureInfo.CurrentCulture);
            }
            catch
            {
                return value.ToString();
            }
        }

        /// <summary>
        /// 四舍五入到指定小数位
        /// </summary>
        /// <param name="value">数值</param>
        /// <param name="decimals">小数位数</param>
        /// <returns>四舍五入后的数值</returns>
        public double RoundToDecimals(double value, int decimals)
        {
            return Math.Round(value, decimals, MidpointRounding.AwayFromZero);
        }

        /// <summary>
        /// 限制数值在指定范围内
        /// </summary>
        /// <param name="value">数值</param>
        /// <param name="min">最小值</param>
        /// <param name="max">最大值</param>
        /// <returns>限制后的数值</returns>
        public double ClampValue(double value, double min, double max)
        {
            return Math.Max(min, Math.Min(max, value));
        }

        /// <summary>
        /// 计算百分比
        /// </summary>
        /// <param name="value">当前值</param>
        /// <param name="total">总值</param>
        /// <returns>百分比</returns>
        public double CalculatePercentage(double value, double total)
        {
            return total != 0 ? (value / total) * 100 : 0;
        }
    }

    /// <summary>
    /// 数字验证结果
    /// </summary>
    public class NumberValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
        public string Summary => IsValid ? "验证通过" : $"发现 {Errors.Count} 个错误";
    }
}
