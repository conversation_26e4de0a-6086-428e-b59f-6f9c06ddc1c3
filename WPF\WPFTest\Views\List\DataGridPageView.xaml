<UserControl x:Class="WPFTest.Views.List.DataGridPageView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:codeExample="clr-namespace:Zylo.WPF.Controls.CodeExample;assembly=Zylo.WPF"
             xmlns:attachedProps="clr-namespace:Zylo.WPF.AttachedProperties;assembly=Zylo.WPF"
             xmlns:converters="clr-namespace:Zylo.WPF.Converters;assembly=Zylo.WPF"
             xmlns:mvvm="http://prismlibrary.com/"
             xmlns:list="clr-namespace:WPFTest.ViewModels.List"
             mc:Ignorable="d"
             d:DesignHeight="2000" d:DesignWidth="1200"
             mvvm:ViewModelLocator.AutoWireViewModel="True"
             d:DataContext="{d:DesignInstance list:DataGridPageViewModel}">

    <UserControl.Resources>
        <!-- 🎨 所有样式来自 Zylo.WPF 项目 -->
        <!-- DataGrid 样式已在 Zylo.WPF/Resources/DataGrid/ 目录下定义 -->
        <!-- 可用样式：DataGridStyle, ModernDataGridStyle, CompactDataGridStyle -->

        <!-- 转换器 -->
        <converters:BooleanToStringConverter x:Key="BooleanToStringConverter"/>

        <!-- 🎨 继承ui:DataGrid样式并添加美观的强调色效果 -->
        <Style x:Key="EnhancedDataGridStyle" TargetType="ui:DataGrid" BasedOn="{StaticResource {x:Type ui:DataGrid}}">
            <!-- 行样式 - 继承原有样式并添加美观的悬停和选中效果 -->
            <Setter Property="RowStyle">
                <Setter.Value>
                    <Style TargetType="DataGridRow" BasedOn="{StaticResource {x:Type DataGridRow}}">
                        <Style.Triggers>
                            <!-- 🎯 鼠标悬停效果 - 很浅的强调色 -->
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background">
                                    <Setter.Value>
                                        <SolidColorBrush Color="{DynamicResource SystemAccentColorSecondary}" Opacity="0.1"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>

                            <!-- 🎯 选中效果 - 浅色强调色 -->
                            <Trigger Property="IsSelected" Value="True">
                                <Setter Property="Background">
                                    <Setter.Value>
                                        <SolidColorBrush Color="{DynamicResource SystemAccentColorPrimary}" Opacity="0.15"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>

                            <!-- 🎯 选中且鼠标悬停 - 稍深一点但仍然很浅 -->
                            <MultiTrigger>
                                <MultiTrigger.Conditions>
                                    <Condition Property="IsSelected" Value="True"/>
                                    <Condition Property="IsMouseOver" Value="True"/>
                                </MultiTrigger.Conditions>
                                <Setter Property="Background">
                                    <Setter.Value>
                                        <SolidColorBrush Color="{DynamicResource SystemAccentColorPrimary}" Opacity="0.2"/>
                                    </Setter.Value>
                                </Setter>
                            </MultiTrigger>
                        </Style.Triggers>
                    </Style>
                </Setter.Value>
            </Setter>

            <!-- 单元格样式 - 继承原有样式并优化选中效果 -->
            <Setter Property="CellStyle">
                <Setter.Value>
                    <Style TargetType="DataGridCell" BasedOn="{StaticResource {x:Type DataGridCell}}">
                        <Style.Triggers>
                            <!-- 选中单元格保持透明，让行的背景色显示 -->
                            <Trigger Property="IsSelected" Value="True">
                                <Setter Property="Background" Value="Transparent"/>
                                <Setter Property="BorderBrush" Value="Transparent"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto"
                  HorizontalScrollBarVisibility="Disabled"
                  Padding="24">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>    <!-- 标题区域 -->
                <RowDefinition Height="*"/>       <!-- 主要内容 -->
                <RowDefinition Height="Auto"/>    <!-- 底部操作栏 -->
            </Grid.RowDefinitions>

            <!-- 标题区域 -->
            <StackPanel Grid.Row="0" Margin="0,0,0,20">
                <ui:TextBlock Text="🎨 DataGrid 控件展示"
                              FontTypography="TitleLarge"
                              Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                              Margin="0,0,0,8"/>
                <ui:TextBlock Text="展示 DataGrid 控件的各种样式和交互效果，包括数据绑定、排序、筛选和编辑功能"
                              FontTypography="Body"
                              Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                              TextWrapping="Wrap"/>

                <!-- 状态显示 -->
                <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                        BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Padding="16,12"
                        Margin="0,16,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <ui:SymbolIcon Symbol="Info24"
                                       FontSize="20"
                                       Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                       VerticalAlignment="Center"
                                       Margin="0,0,12,0"/>
                        <TextBlock Grid.Column="1"
                                   Text="{Binding StatusMessage}"
                                   FontSize="14"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                   VerticalAlignment="Center"
                                   TextWrapping="Wrap"/>
                    </Grid>
                </Border>
            </StackPanel>

            <!-- 主要内容区域 -->
            <Border Grid.Row="1" Margin="0,0,0,20">
                <StackPanel>

                    <!-- DataGrid 基础功能展示 -->
                    <ui:CardExpander Header="🎯 DataGrid 基础功能" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 DataGrid 的基础功能和用法，包括数据显示、列配置、选择交互和样式设置"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 示例展示区域 -->
                            <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                    BorderThickness="1"
                                    CornerRadius="8"
                                    Padding="20"
                                    Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="员工信息表格（只读模式）："
                                               FontWeight="Medium"
                                               Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                               Margin="0,0,0,16"/>

                                    <!-- 基础 DataGrid -->
                                    <ui:DataGrid ItemsSource="{Binding EmployeeData}"
                                                 SelectedItem="{Binding SelectedEmployee, Mode=TwoWay}"
                                                 Style="{StaticResource EnhancedDataGridStyle}"
                                                 Height="200"
                                                 AutoGenerateColumns="False"
                                                 CanUserAddRows="False"
                                                 CanUserDeleteRows="False"
                                                 IsReadOnly="True"
                                                 HeadersVisibility="Column"
                                                 SelectionMode="Single"
                                                 SelectionUnit="FullRow">
                                        <ui:DataGrid.Columns>
                                            <DataGridTextColumn Header="ID" Binding="{Binding Id}" Width="60"/>
                                            <DataGridTextColumn Header="姓名" Binding="{Binding Name}" Width="120"/>
                                            <DataGridTextColumn Header="部门" Binding="{Binding Department}" Width="100"/>
                                            <DataGridTextColumn Header="职位" Binding="{Binding Position}" Width="120"/>
                                            <DataGridTextColumn Header="薪资" Binding="{Binding Salary, StringFormat=C}" Width="100"/>
                                            <DataGridTextColumn Header="状态" Binding="{Binding Status}" Width="80"/>
                                        </ui:DataGrid.Columns>
                                    </ui:DataGrid>

                                    <!-- 交互信息显示 -->
                                    <Border Background="{DynamicResource CardBackgroundFillColorSecondaryBrush}"
                                            BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                            BorderThickness="1"
                                            CornerRadius="6"
                                            Padding="16"
                                            Margin="0,16,0,0">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <StackPanel Grid.Column="0">
                                                <TextBlock Text="选择信息：" FontWeight="Medium" FontSize="12" Margin="0,0,0,8"/>
                                                <TextBlock Text="{Binding SelectedEmployee, StringFormat='选中员工: {0}'}"
                                                           FontSize="12"
                                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                                            </StackPanel>

                                            <StackPanel Grid.Column="1">
                                                <TextBlock Text="数据统计：" FontWeight="Medium" FontSize="12" Margin="0,0,0,8"/>
                                                <TextBlock Text="{Binding EmployeeData.Count, StringFormat='员工总数: {0} 人'}"
                                                           FontSize="12"
                                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </StackPanel>
                            </Border>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="DataGrid 基础用法代码示例"
                                Language="XAML"
                                Description="展示 DataGrid 的基础用法，包括数据绑定、列配置、文字居中和样式设置"
                                ShowTabs="True"
                                XamlCode="{Binding BasicXamlExample}"
                                CSharpCode="{Binding BasicCSharpExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- DataGrid 高级编辑功能展示 -->
                    <ui:CardExpander Header="⚡ DataGrid 高级编辑功能" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 DataGrid 的高级编辑功能，包括双击编辑、ComboBox选择、不同数据类型的编辑器"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 筛选面板 -->
                            <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                    BorderThickness="1"
                                    CornerRadius="8"
                                    Padding="16"
                                    Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="数据筛选："
                                               FontWeight="Medium"
                                               FontSize="12"
                                               Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                               Margin="0,0,0,12"/>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- 文本搜索 -->
                                        <ui:TextBox Grid.Column="0"
                                                    PlaceholderText="搜索姓名、部门、职位..."
                                                    Text="{Binding FilterText, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                    Margin="0,0,8,0"/>

                                        <!-- 部门筛选 -->
                                        <ComboBox Grid.Column="1"
                                                  ItemsSource="{Binding DepartmentOptions}"
                                                  SelectedItem="{Binding SelectedDepartmentFilter, Mode=TwoWay}"
                                                  Margin="0,0,8,0">
                                            <ComboBox.ItemTemplate>
                                                <DataTemplate>
                                                    <TextBlock Text="{Binding}" Padding="8,4"/>
                                                </DataTemplate>
                                            </ComboBox.ItemTemplate>
                                        </ComboBox>

                                        <!-- 状态筛选 -->
                                        <ComboBox Grid.Column="2"
                                                  ItemsSource="{Binding StatusOptions}"
                                                  SelectedItem="{Binding SelectedStatusFilter, Mode=TwoWay}"
                                                  Margin="0,0,8,0">
                                            <ComboBox.ItemTemplate>
                                                <DataTemplate>
                                                    <TextBlock Text="{Binding}" Padding="8,4"/>
                                                </DataTemplate>
                                            </ComboBox.ItemTemplate>
                                        </ComboBox>

                                        <!-- 筛选按钮 -->
                                        <StackPanel Grid.Column="3" Orientation="Horizontal">
                                            <ui:Button Content="筛选"
                                                       Command="{Binding ApplyFilterCommand}"
                                                       Appearance="Primary"
                                                       Margin="0,0,4,0"/>
                                            <ui:Button Content="清除"
                                                       Command="{Binding ClearFilterCommand}"
                                                       Appearance="Secondary"/>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </Border>

                            <!-- 控制面板 -->
                            <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                    BorderThickness="1"
                                    CornerRadius="8"
                                    Padding="16"
                                    Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="数据操作："
                                               FontWeight="Medium"
                                               FontSize="12"
                                               Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                               Margin="0,0,0,12"/>
                                    <WrapPanel>
                                        <ui:Button Content="添加员工"
                                                   Command="{Binding AddEmployeeCommand}"
                                                   Appearance="Primary"
                                                   Margin="0,0,8,8"/>
                                        <ui:Button Content="删除选中"
                                                   Command="{Binding DeleteSelectedEmployeeCommand}"
                                                   Appearance="Danger"
                                                   Margin="0,0,8,8"/>
                                        <ui:Button Content="保存更改"
                                                   Command="{Binding SaveAllChangesCommand}"
                                                   Appearance="Success"
                                                   Margin="0,0,8,8"/>
                                        <ui:Button Content="重置数据"
                                                   Command="{Binding ResetDataCommand}"
                                                   Appearance="Caution"
                                                   Margin="0,0,8,8"/>
                                        <ui:Button Content="导出数据"
                                                   Command="{Binding ExportDataCommand}"
                                                   Appearance="Info"
                                                   Margin="0,0,8,8"/>
                                        <ui:Button Content="批量调薪"
                                                   Command="{Binding BatchUpdateSalaryCommand}"
                                                   Appearance="Light"
                                                   Margin="0,0,8,8"/>
                                        <ToggleButton Content="编辑模式"
                                                      IsChecked="{Binding IsEditModeEnabled, Mode=TwoWay}"
                                                      Command="{Binding ToggleEditModeCommand}"
                                                      Margin="0,0,0,8"/>
                                    </WrapPanel>
                                </StackPanel>
                            </Border>

                            <!-- 高级编辑 DataGrid -->
                            <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                    BorderThickness="1"
                                    CornerRadius="8"
                                    Padding="20"
                                    Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="员工信息表格（可编辑模式 - 双击单元格编辑）："
                                               FontWeight="Medium"
                                               Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                               Margin="0,0,0,16"/>

                                    <ui:DataGrid ItemsSource="{Binding FilteredEmployeeData}"
                                                 SelectedItem="{Binding SelectedEmployee, Mode=TwoWay}"
                                                 Style="{StaticResource EnhancedDataGridStyle}"
                                                 Height="300"
                                                 AutoGenerateColumns="False"
                                                 CanUserAddRows="False"
                                                 CanUserDeleteRows="False"
                                                 IsReadOnly="False"
                                                 HeadersVisibility="Column"
                                                 SelectionMode="Single"
                                                 SelectionUnit="FullRow">
                                        <ui:DataGrid.Columns>
                                            <!-- ID列 - 只读 -->
                                            <DataGridTextColumn Header="ID" Binding="{Binding Id}" Width="60" IsReadOnly="True"/>

                                            <!-- 姓名列 - 文本编辑 -->
                                            <DataGridTextColumn Header="姓名" Binding="{Binding Name}" Width="120"/>

                                            <!-- 部门列 - ComboBox选择 -->
                                            <DataGridTemplateColumn Header="部门" Width="120">
                                                <DataGridTemplateColumn.CellTemplate>
                                                    <DataTemplate>
                                                        <TextBlock Text="{Binding Department}"
                                                                   HorizontalAlignment="Center"
                                                                   VerticalAlignment="Center"/>
                                                    </DataTemplate>
                                                </DataGridTemplateColumn.CellTemplate>
                                                <DataGridTemplateColumn.CellEditingTemplate>
                                                    <DataTemplate>
                                                        <ComboBox SelectedItem="{Binding Department, Mode=TwoWay}"
                                                                  ItemsSource="{Binding DataContext.DepartmentOptions, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                                  BorderThickness="0"
                                                                  Background="Transparent"/>
                                                    </DataTemplate>
                                                </DataGridTemplateColumn.CellEditingTemplate>
                                            </DataGridTemplateColumn>

                                            <!-- 职位列 - ComboBox选择 -->
                                            <DataGridTemplateColumn Header="职位" Width="140">
                                                <DataGridTemplateColumn.CellTemplate>
                                                    <DataTemplate>
                                                        <TextBlock Text="{Binding Position}"
                                                                   HorizontalAlignment="Center"
                                                                   VerticalAlignment="Center"/>
                                                    </DataTemplate>
                                                </DataGridTemplateColumn.CellTemplate>
                                                <DataGridTemplateColumn.CellEditingTemplate>
                                                    <DataTemplate>
                                                        <ComboBox SelectedItem="{Binding Position, Mode=TwoWay}"
                                                                  ItemsSource="{Binding DataContext.PositionOptions, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                                  BorderThickness="0"
                                                                  Background="Transparent"/>
                                                    </DataTemplate>
                                                </DataGridTemplateColumn.CellEditingTemplate>
                                            </DataGridTemplateColumn>

                                            <!-- 薪资列 - 数字编辑 -->
                                            <DataGridTextColumn Header="薪资" Binding="{Binding Salary, StringFormat=C}" Width="100"/>

                                            <!-- 入职日期列 - 日期选择器 -->
                                            <DataGridTemplateColumn Header="入职日期" Width="120">
                                                <DataGridTemplateColumn.CellTemplate>
                                                    <DataTemplate>
                                                        <TextBlock Text="{Binding HireDate, StringFormat=yyyy-MM-dd}"
                                                                   HorizontalAlignment="Center"
                                                                   VerticalAlignment="Center"/>
                                                    </DataTemplate>
                                                </DataGridTemplateColumn.CellTemplate>
                                                <DataGridTemplateColumn.CellEditingTemplate>
                                                    <DataTemplate>
                                                        <DatePicker SelectedDate="{Binding HireDate, Mode=TwoWay}"
                                                                    BorderThickness="0"
                                                                    Background="Transparent"/>
                                                    </DataTemplate>
                                                </DataGridTemplateColumn.CellEditingTemplate>
                                            </DataGridTemplateColumn>

                                            <!-- 状态列 - 枚举ComboBox -->
                                            <DataGridTemplateColumn Header="状态" Width="100">
                                                <DataGridTemplateColumn.CellTemplate>
                                                    <DataTemplate>
                                                        <TextBlock Text="{Binding Status}"
                                                                   HorizontalAlignment="Center"
                                                                   VerticalAlignment="Center"/>
                                                    </DataTemplate>
                                                </DataGridTemplateColumn.CellTemplate>
                                                <DataGridTemplateColumn.CellEditingTemplate>
                                                    <DataTemplate>
                                                        <ComboBox SelectedItem="{Binding Status, Mode=TwoWay}"
                                                                  ItemsSource="{Binding DataContext.StatusOptions, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                                  BorderThickness="0"
                                                                  Background="Transparent"/>
                                                    </DataTemplate>
                                                </DataGridTemplateColumn.CellEditingTemplate>
                                            </DataGridTemplateColumn>

                                            <!-- 全职状态列 - CheckBox -->
                                            <DataGridCheckBoxColumn Header="全职" Binding="{Binding IsFullTime}" Width="60"/>

                                            <!-- 工作经验列 - 数字编辑 -->
                                            <DataGridTextColumn Header="经验(年)" Binding="{Binding WorkExperience}" Width="80"/>

                                            <!-- 绩效评分列 - 数字编辑（小数） -->
                                            <DataGridTextColumn Header="绩效评分" Binding="{Binding PerformanceRating, StringFormat=F1}" Width="90"/>
                                        </ui:DataGrid.Columns>
                                    </ui:DataGrid>

                                    <!-- 数据统计和编辑说明 -->
                                    <Border Background="{DynamicResource CardBackgroundFillColorSecondaryBrush}"
                                            BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                            BorderThickness="1"
                                            CornerRadius="6"
                                            Padding="16"
                                            Margin="0,16,0,0">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- 数据统计 -->
                                            <StackPanel Grid.Column="0">
                                                <TextBlock Text="数据统计：" FontWeight="Medium" FontSize="12" Margin="0,0,0,8"/>
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                                                    <TextBlock Text="总员工数：" FontSize="11"/>
                                                    <TextBlock Text="{Binding EmployeeData.Count}" FontSize="11" FontWeight="Medium"/>
                                                </StackPanel>
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                                                    <TextBlock Text="显示记录：" FontSize="11"/>
                                                    <TextBlock Text="{Binding FilteredEmployeeData.Count}" FontSize="11" FontWeight="Medium"/>
                                                </StackPanel>
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                                                    <TextBlock Text="选中员工：" FontSize="11"/>
                                                    <TextBlock Text="{Binding SelectedEmployee.Name, FallbackValue='无'}" FontSize="11" FontWeight="Medium"/>
                                                </StackPanel>
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="编辑模式：" FontSize="11"/>
                                                    <TextBlock Text="{Binding IsEditModeEnabled}" FontSize="11" FontWeight="Medium"/>
                                                </StackPanel>
                                            </StackPanel>

                                            <!-- 编辑说明 -->
                                            <StackPanel Grid.Column="1">
                                                <TextBlock Text="编辑说明：" FontWeight="Medium" FontSize="12" Margin="0,0,0,8"/>
                                                <TextBlock FontSize="11" Foreground="{DynamicResource TextFillColorSecondaryBrush}" TextWrapping="Wrap">
                                                    • 双击单元格进入编辑模式<LineBreak/>
                                                    • 部门和职位：下拉选择框<LineBreak/>
                                                    • 入职日期：日期选择器<LineBreak/>
                                                    • 状态：枚举下拉选择<LineBreak/>
                                                    • 全职：复选框<LineBreak/>
                                                    • 薪资、经验、绩效：数字输入<LineBreak/>
                                                    • 按 Enter 确认编辑，按 Esc 取消编辑
                                                </TextBlock>
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </StackPanel>
                            </Border>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="DataGrid 高级编辑功能代码示例"
                                Language="XAML"
                                Description="展示 DataGrid 的高级编辑功能，包括各种数据类型的编辑器"
                                ShowTabs="True"
                                XamlCode="{Binding AdvancedXamlExample}"
                                CSharpCode="{Binding AdvancedCSharpExample}"/>
                        </StackPanel>
                    </ui:CardExpander>







                </StackPanel>
            </Border>

            <!-- 底部操作栏 -->
            <Border Grid.Row="2"
                    Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Padding="16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 统计信息 -->
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock Text="{Binding InteractionCount, StringFormat='交互次数: {0}'}"
                                   FontSize="14"
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   Margin="0,0,20,0"/>
                        <TextBlock Text="{Binding LastAction, StringFormat='最后操作: {0}'}"
                                   FontSize="14"
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                    </StackPanel>

                    <!-- 操作按钮 -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <ui:Button Content="重置计数"
                                   Command="{Binding ResetCountCommand}"
                                   Appearance="Secondary"
                                   Margin="0,0,8,0"/>
                        <ui:Button Content="清除状态"
                                   Command="{Binding ClearStatusCommand}"
                                   Appearance="Secondary"/>
                    </StackPanel>
                </Grid>
            </Border>

        </Grid>
    </ScrollViewer>
</UserControl>
