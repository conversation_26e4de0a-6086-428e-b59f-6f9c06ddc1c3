// 高级 MenuBar ViewModel 实现
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Wpf.Ui.Controls;
using Zylo.WPF.Models.Navigation;

namespace WPFTest.ViewModels.Navigation
{
    /// <summary>
    /// 高级 MenuBar 示例 ViewModel
    /// </summary>
    public partial class AdvancedMenuBarViewModel : ObservableObject
    {
        #region 属性

        /// <summary>
        /// 高级菜单项集合
        /// </summary>
        [ObservableProperty]
        public partial ObservableCollection<MenuItemModel> AdvancedMenuItems { get; set; } = new();

        /// <summary>
        /// 多级菜单项集合
        /// </summary>
        [ObservableProperty]
        public partial ObservableCollection<MenuItemModel> MultiLevelMenuItems { get; set; } = new();

        /// <summary>
        /// 动态菜单项集合
        /// </summary>
        [ObservableProperty]
        public partial ObservableCollection<MenuItemModel> DynamicMenuItems { get; set; } = new();

        /// <summary>
        /// 可选择菜单项集合
        /// </summary>
        [ObservableProperty]
        public partial ObservableCollection<MenuItemModel> CheckableMenuItems { get; set; } = new();

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        public partial string StatusMessage { get; set; } = "准备就绪";

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化 AdvancedMenuBarViewModel
        /// </summary>
        public AdvancedMenuBarViewModel()
        {
            InitializeMenuItems();
        }

        #endregion

        #region 命令

        /// <summary>
        /// 菜单项点击命令
        /// </summary>
        /// <param name="parameter">菜单项参数</param>
        [RelayCommand]
        private void MenuItemClick(string? parameter)
        {
            StatusMessage = $"点击了菜单项: {parameter}";
            Debug.WriteLine($"MenuBar 菜单项点击: {parameter}");
        }

        /// <summary>
        /// 添加菜单项命令
        /// </summary>
        [RelayCommand]
        private void AddMenuItem()
        {
            var newItem = new MenuItemModel($"动态项 {DynamicMenuItems.Count + 1}", MenuItemClickCommand, $"动态项{DynamicMenuItems.Count + 1}")
            {
                Icon = new SymbolIcon { Symbol = SymbolRegular.Add24 }
            };
            DynamicMenuItems.Add(newItem);
            StatusMessage = $"添加了菜单项: {newItem.Header}";
        }

        /// <summary>
        /// 清空菜单项命令
        /// </summary>
        [RelayCommand]
        private void ClearMenuItems()
        {
            var count = DynamicMenuItems.Count;
            DynamicMenuItems.Clear();
            StatusMessage = $"清空了 {count} 个菜单项";
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化菜单项
        /// </summary>
        private void InitializeMenuItems()
        {
            InitializeAdvancedMenuItems();
            InitializeMultiLevelMenuItems();
            InitializeDynamicMenuItems();
            InitializeCheckableMenuItems();
        }

        /// <summary>
        /// 初始化高级菜单项
        /// </summary>
        private void InitializeAdvancedMenuItems()
        {
            // 文件菜单（带图标）
            var fileMenu = new MenuItemModel("文件(_F)")
            {
                Icon = new SymbolIcon { Symbol = SymbolRegular.Document24 }
            };
            
            fileMenu.AddChild(new MenuItemModel("新建(_N)", MenuItemClickCommand, "新建") 
            { 
                InputGestureText = "Ctrl+N",
                Icon = new SymbolIcon { Symbol = SymbolRegular.DocumentAdd24 }
            });
            
            fileMenu.AddChild(new MenuItemModel("打开(_O)", MenuItemClickCommand, "打开") 
            { 
                InputGestureText = "Ctrl+O",
                Icon = new SymbolIcon { Symbol = SymbolRegular.FolderOpen24 }
            });

            fileMenu.AddChild(new MenuItemModel("保存(_S)", MenuItemClickCommand, "保存") 
            { 
                InputGestureText = "Ctrl+S",
                Icon = new SymbolIcon { Symbol = SymbolRegular.Save24 }
            });

            // 最近文件子菜单
            var recentMenu = new MenuItemModel("最近文件(_R)")
            {
                Icon = new SymbolIcon { Symbol = SymbolRegular.History24 }
            };
            recentMenu.AddChild(new MenuItemModel("文档1.txt", MenuItemClickCommand, "文档1")
            {
                Icon = new SymbolIcon { Symbol = SymbolRegular.Document24 }
            });
            recentMenu.AddChild(new MenuItemModel("文档2.txt", MenuItemClickCommand, "文档2")
            {
                Icon = new SymbolIcon { Symbol = SymbolRegular.Document24 }
            });
            recentMenu.AddChild(new MenuItemModel("文档3.txt", MenuItemClickCommand, "文档3")
            {
                Icon = new SymbolIcon { Symbol = SymbolRegular.Document24 }
            });
            fileMenu.AddChild(recentMenu);

            fileMenu.AddChild(new MenuItemModel { IsSeparator = true });
            fileMenu.AddChild(new MenuItemModel("退出(_X)", MenuItemClickCommand, "退出") 
            { 
                InputGestureText = "Alt+F4",
                Icon = new SymbolIcon { Symbol = SymbolRegular.Power24 }
            });

            // 编辑菜单（带图标）
            var editMenu = new MenuItemModel("编辑(_E)")
            {
                Icon = new SymbolIcon { Symbol = SymbolRegular.Edit24 }
            };
            
            editMenu.AddChild(new MenuItemModel("撤销(_U)", MenuItemClickCommand, "撤销") 
            { 
                InputGestureText = "Ctrl+Z",
                Icon = new SymbolIcon { Symbol = SymbolRegular.ArrowUndo24 }
            });
            
            editMenu.AddChild(new MenuItemModel("重做(_R)", MenuItemClickCommand, "重做") 
            { 
                InputGestureText = "Ctrl+Y",
                Icon = new SymbolIcon { Symbol = SymbolRegular.ArrowRedo24 }
            });

            AdvancedMenuItems.Add(fileMenu);
            AdvancedMenuItems.Add(editMenu);
        }

        /// <summary>
        /// 初始化多级菜单项
        /// </summary>
        private void InitializeMultiLevelMenuItems()
        {
            // 工具菜单
            var toolsMenu = new MenuItemModel("工具(_T)")
            {
                Icon = new SymbolIcon { Symbol = SymbolRegular.Wrench24 }
            };

            // 语言子菜单
            var languageMenu = new MenuItemModel("语言(_L)")
            {
                Icon = new SymbolIcon { Symbol = SymbolRegular.LocalLanguage24 }
            };
            
            // 中文子菜单
            var chineseMenu = new MenuItemModel("中文")
            {
                Icon = new SymbolIcon { Symbol = SymbolRegular.Globe24 }
            };
            chineseMenu.AddChild(new MenuItemModel("简体中文", MenuItemClickCommand, "简体中文"));
            chineseMenu.AddChild(new MenuItemModel("繁体中文", MenuItemClickCommand, "繁体中文"));
            
            languageMenu.AddChild(chineseMenu);
            languageMenu.AddChild(new MenuItemModel("English", MenuItemClickCommand, "English"));
            languageMenu.AddChild(new MenuItemModel("日本語", MenuItemClickCommand, "日本語"));
            
            toolsMenu.AddChild(languageMenu);
            toolsMenu.AddChild(new MenuItemModel("选项(_O)", MenuItemClickCommand, "选项")
            {
                Icon = new SymbolIcon { Symbol = SymbolRegular.Settings24 }
            });

            MultiLevelMenuItems.Add(toolsMenu);
        }

        /// <summary>
        /// 初始化动态菜单项
        /// </summary>
        private void InitializeDynamicMenuItems()
        {
            // 初始时为空，用户可以动态添加
        }

        /// <summary>
        /// 初始化可选择菜单项
        /// </summary>
        private void InitializeCheckableMenuItems()
        {
            // 视图菜单
            var viewMenu = new MenuItemModel("视图(_V)")
            {
                Icon = new SymbolIcon { Symbol = SymbolRegular.Eye24 }
            };

            viewMenu.AddChild(new MenuItemModel("工具栏(_T)", MenuItemClickCommand, "工具栏")
            {
                IsChecked = true,
                ItemType = MenuItemType.CheckBox,
                Icon = new SymbolIcon { Symbol = SymbolRegular.Toolbar24 }
            });

            viewMenu.AddChild(new MenuItemModel("状态栏(_S)", MenuItemClickCommand, "状态栏")
            {
                IsChecked = true,
                ItemType = MenuItemType.CheckBox,
                Icon = new SymbolIcon { Symbol = SymbolRegular.StatusBar24 }
            });

            viewMenu.AddChild(new MenuItemModel("侧边栏(_B)", MenuItemClickCommand, "侧边栏")
            {
                IsChecked = false,
                ItemType = MenuItemType.CheckBox,
                Icon = new SymbolIcon { Symbol = SymbolRegular.PanelLeft24 }
            });

            CheckableMenuItems.Add(viewMenu);
        }

        #endregion
    }
}
