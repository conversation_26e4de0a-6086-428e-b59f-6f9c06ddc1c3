using System.Collections.ObjectModel;
using System.IO;
using System.Windows.Input;
using GongSolutions.Wpf.DragDrop;
using Microsoft.Win32;
using Zylo.WPF.Controls.YFile.Model;
using Zylo.WPF.Services.YFile;
using Zylo.YLog.Runtime;

namespace Zylo.WPF.Controls.YFile;

/// <summary>
/// Windows 风格文件浏览器控件 - 优化版本1
/// 支持异步加载、文件系统监视、内存管理等功能
/// </summary>
public partial class FileExplorerControl : UserControl, IDisposable, IDropTarget, IDragSource
{
    #region 服务和日志

    /// <summary>
    /// YLogger日志记录器实例 - 用于调试拖拽和压缩功能
    /// </summary>
    private readonly YLoggerInstance _logger = YLogger.ForDebug<FileExplorerControl>();

    /// <summary>
    /// 文件拖拽服务
    /// </summary>
    private readonly FileDragService _dragService = new();

    /// <summary>
    /// 文件系统缓存服务
    /// </summary>
    private readonly FileSystemCacheService _cacheService = new();

    #endregion

    #region 常量定义

    /// <summary>
    /// 虚拟节点标识
    /// </summary>
    private const string VIRTUAL_NODE_NAME = "加载中...";

    /// <summary>
    /// 防抖延迟时间（毫秒）
    /// </summary>
    private const int DEBOUNCE_DELAY = 300;

    #endregion

    #region 数据集合

    /// <summary>
    /// 文件夹树数据
    /// </summary>
    private ObservableCollection<FolderTreeItem> _folderTree = new();

    /// <summary>
    /// 文件列表数据
    /// </summary>
    private ObservableCollection<FileItem> _files = new();

    /// <summary>
    /// 保存展开状态的字典
    /// </summary>
    private Dictionary<string, bool> _expandedStates = new();

    #endregion

    #region 状态管理

    /// <summary>
    /// 取消令牌源，用于取消异步操作
    /// </summary>
    private CancellationTokenSource? _cancellationTokenSource;

    /// <summary>
    /// 文件系统监视器
    /// </summary>
    private FileSystemWatcher? _fileSystemWatcher;

    /// <summary>
    /// 是否正在加载
    /// </summary>
    private bool _isLoading;

    /// <summary>
    /// 文件加载防抖计时器
    /// </summary>
    private Timer? _loadDebounceTimer;

    /// <summary>
    /// 拖拽处理状态标志
    /// </summary>
    private bool _isProcessingDrop = false;

    /// <summary>
    /// 资源释放状态标志
    /// </summary>
    private bool _disposed = false;

    #endregion

    #region 构造函数和初始化

    public FileExplorerControl()
    {
        InitializeComponent();
        InitializeDataSources();
        _ = InitializeAsync();
    }

    /// <summary>
    /// 初始化数据源绑定
    /// </summary>
    private void InitializeDataSources()
    {
        FolderTreeView.ItemsSource = _folderTree;
        FileListView.ItemsSource = _files;
        _logger.Debug("✅ 数据源绑定完成");
    }

    /// <summary>
    /// 异步初始化控件
    /// </summary>
    private async Task InitializeAsync()
    {
        try
        {
            _logger.Info("🚀 开始初始化 FileExplorerControl");

            await LoadFolderTreeAsync();
            await LoadCurrentFolderAsync();
            UpdateBreadcrumbNavigation();
            SetupFileSystemWatcher();

            _logger.Info("✅ FileExplorerControl 初始化完成");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 初始化失败: {ex.Message}", ex);
            StatusMessage = $"初始化失败: {ex.Message}";
            ShowErrorNotification($"初始化失败: {ex.Message}");
        }
    }

    #endregion

    #region 文件系统监视

    /// <summary>
    /// 设置文件系统监视器
    /// </summary>
    private void SetupFileSystemWatcher()
    {
        try
        {
            if (!string.IsNullOrEmpty(CurrentPath) && Directory.Exists(CurrentPath))
            {
                DisposeFileSystemWatcher();
                CreateFileSystemWatcher();
                _logger.Debug($"✅ 文件系统监视器已设置: {CurrentPath}");
            }
        }
        catch (Exception ex)
        {
            _logger.Warning($"⚠️ 设置文件系统监视器失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 创建文件系统监视器
    /// </summary>
    private void CreateFileSystemWatcher()
    {
        _fileSystemWatcher = new FileSystemWatcher(CurrentPath)
        {
            NotifyFilter = NotifyFilters.FileName | NotifyFilters.DirectoryName | NotifyFilters.LastWrite,
            IncludeSubdirectories = false
        };

        // 绑定事件处理器
        _fileSystemWatcher.Created += OnFileSystemChanged;
        _fileSystemWatcher.Deleted += OnFileSystemChanged;
        _fileSystemWatcher.Renamed += OnFileSystemChanged;
        _fileSystemWatcher.Changed += OnFileSystemChanged;
        _fileSystemWatcher.EnableRaisingEvents = true;
    }

    /// <summary>
    /// 释放文件系统监视器
    /// </summary>
    private void DisposeFileSystemWatcher()
    {
        if (_fileSystemWatcher != null)
        {
            _fileSystemWatcher.EnableRaisingEvents = false;
            _fileSystemWatcher.Dispose();
            _fileSystemWatcher = null;
        }
    }

    /// <summary>
    /// 文件系统变化事件处理（使用防抖机制）
    /// </summary>
    private void OnFileSystemChanged(object sender, FileSystemEventArgs e)
    {
        try
        {
            // 清除相关路径的缓存
            InvalidateRelatedCache(e);
            _logger.Debug($"🔄 文件系统变化: {e.ChangeType} - {e.FullPath}");

            // 使用防抖机制避免频繁刷新
            ScheduleRefreshWithDebounce();
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 文件系统变化事件处理异常: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 清除相关缓存
    /// </summary>
    private void InvalidateRelatedCache(FileSystemEventArgs e)
    {
        _cacheService.InvalidateCache(e.FullPath);
        if (e is RenamedEventArgs renamedArgs && !string.IsNullOrEmpty(renamedArgs.OldFullPath))
        {
            _cacheService.InvalidateCache(renamedArgs.OldFullPath);
        }
    }

    /// <summary>
    /// 使用防抖机制调度刷新
    /// </summary>
    private void ScheduleRefreshWithDebounce()
    {
        _loadDebounceTimer?.Dispose();
        _loadDebounceTimer = new Timer(async _ =>
        {
            try
            {
                await Dispatcher.InvokeAsync(async () =>
                {
                    await LoadCurrentFolderAsync();
                });
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 防抖刷新失败: {ex.Message}", ex);
            }
        }, null, DEBOUNCE_DELAY, Timeout.Infinite);
    }

    #endregion

    #region 依赖属性

    /// <summary>
    /// 当前路径属性
    /// </summary>
    public string CurrentPath
    {
        get { return (string)GetValue(CurrentPathProperty); }
        set { SetValue(CurrentPathProperty, value); }
    }

    public static readonly DependencyProperty CurrentPathProperty =
        DependencyProperty.Register("CurrentPath", typeof(string), typeof(FileExplorerControl), 
            new PropertyMetadata(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), OnCurrentPathChanged));

    /// <summary>
    /// 是否显示工具栏
    /// </summary>
    public bool ShowToolbar
    {
        get { return (bool)GetValue(ShowToolbarProperty); }
        set { SetValue(ShowToolbarProperty, value); }
    }

    public static readonly DependencyProperty ShowToolbarProperty =
        DependencyProperty.Register("ShowToolbar", typeof(bool), typeof(FileExplorerControl), 
            new PropertyMetadata(true));

    /// <summary>
    /// 是否显示地址栏
    /// </summary>
    public bool ShowAddressBar
    {
        get { return (bool)GetValue(ShowAddressBarProperty); }
        set { SetValue(ShowAddressBarProperty, value); }
    }

    public static readonly DependencyProperty ShowAddressBarProperty =
        DependencyProperty.Register("ShowAddressBar", typeof(bool), typeof(FileExplorerControl),
            new PropertyMetadata(true));

    /// <summary>
    /// 是否显示面包屑导航
    /// </summary>
    public bool ShowBreadcrumb
    {
        get { return (bool)GetValue(ShowBreadcrumbProperty); }
        set { SetValue(ShowBreadcrumbProperty, value); }
    }

    public static readonly DependencyProperty ShowBreadcrumbProperty =
        DependencyProperty.Register("ShowBreadcrumb", typeof(bool), typeof(FileExplorerControl),
            new PropertyMetadata(true));

    /// <summary>
    /// 是否显示搜索框
    /// </summary>
    public bool ShowSearchBox
    {
        get { return (bool)GetValue(ShowSearchBoxProperty); }
        set { SetValue(ShowSearchBoxProperty, value); }
    }

    public static readonly DependencyProperty ShowSearchBoxProperty =
        DependencyProperty.Register("ShowSearchBox", typeof(bool), typeof(FileExplorerControl),
            new PropertyMetadata(true));

    /// <summary>
    /// 搜索文本
    /// </summary>
    public string SearchText
    {
        get { return (string)GetValue(SearchTextProperty); }
        set { SetValue(SearchTextProperty, value); }
    }

    public static readonly DependencyProperty SearchTextProperty =
        DependencyProperty.Register("SearchText", typeof(string), typeof(FileExplorerControl),
            new PropertyMetadata(string.Empty, OnSearchTextChanged));

    /// <summary>
    /// 是否显示状态栏
    /// </summary>
    public bool ShowStatusBar
    {
        get { return (bool)GetValue(ShowStatusBarProperty); }
        set { SetValue(ShowStatusBarProperty, value); }
    }

    public static readonly DependencyProperty ShowStatusBarProperty =
        DependencyProperty.Register("ShowStatusBar", typeof(bool), typeof(FileExplorerControl), 
            new PropertyMetadata(true));

    /// <summary>
    /// 是否显示文件夹树
    /// </summary>
    public bool ShowFolderTree
    {
        get { return (bool)GetValue(ShowFolderTreeProperty); }
        set { SetValue(ShowFolderTreeProperty, value); }
    }

    public static readonly DependencyProperty ShowFolderTreeProperty =
        DependencyProperty.Register("ShowFolderTree", typeof(bool), typeof(FileExplorerControl), 
            new PropertyMetadata(true));

    /// <summary>
    /// 文件夹树宽度
    /// </summary>
    public double FolderTreeWidth
    {
        get { return (double)GetValue(FolderTreeWidthProperty); }
        set { SetValue(FolderTreeWidthProperty, value); }
    }

    public static readonly DependencyProperty FolderTreeWidthProperty =
        DependencyProperty.Register("FolderTreeWidth", typeof(double), typeof(FileExplorerControl), 
            new PropertyMetadata(250.0));

    /// <summary>
    /// 是否允许文件选择
    /// </summary>
    public bool AllowFileSelection
    {
        get { return (bool)GetValue(AllowFileSelectionProperty); }
        set { SetValue(AllowFileSelectionProperty, value); }
    }

    public static readonly DependencyProperty AllowFileSelectionProperty =
        DependencyProperty.Register("AllowFileSelection", typeof(bool), typeof(FileExplorerControl), 
            new PropertyMetadata(true));

    /// <summary>
    /// 是否允许文件夹选择
    /// </summary>
    public bool AllowFolderSelection
    {
        get { return (bool)GetValue(AllowFolderSelectionProperty); }
        set { SetValue(AllowFolderSelectionProperty, value); }
    }

    public static readonly DependencyProperty AllowFolderSelectionProperty =
        DependencyProperty.Register("AllowFolderSelection", typeof(bool), typeof(FileExplorerControl), 
            new PropertyMetadata(true));

    /// <summary>
    /// 文件选择模式（计算属性）
    /// </summary>
    public SelectionMode FileSelectionMode
    {
        get { return AllowFileSelection ? SelectionMode.Extended : SelectionMode.Single; }
    }

    /// <summary>
    /// 状态消息
    /// </summary>
    public string StatusMessage
    {
        get { return (string)GetValue(StatusMessageProperty); }
        set { SetValue(StatusMessageProperty, value); }
    }

    public static readonly DependencyProperty StatusMessageProperty =
        DependencyProperty.Register("StatusMessage", typeof(string), typeof(FileExplorerControl), 
            new PropertyMetadata("就绪"));

    /// <summary>
    /// 项目数量
    /// </summary>
    public string ItemCount
    {
        get { return (string)GetValue(ItemCountProperty); }
        set { SetValue(ItemCountProperty, value); }
    }

    public static readonly DependencyProperty ItemCountProperty =
        DependencyProperty.Register("ItemCount", typeof(string), typeof(FileExplorerControl), 
            new PropertyMetadata("0"));

    /// <summary>
    /// 面包屑导航项集合
    /// </summary>
    public ObservableCollection<BreadcrumbItem> BreadcrumbItems
    {
        get { return (ObservableCollection<BreadcrumbItem>)GetValue(BreadcrumbItemsProperty); }
        set { SetValue(BreadcrumbItemsProperty, value); }
    }

    public static readonly DependencyProperty BreadcrumbItemsProperty =
        DependencyProperty.Register("BreadcrumbItems", typeof(ObservableCollection<BreadcrumbItem>), typeof(FileExplorerControl), 
            new PropertyMetadata(new ObservableCollection<BreadcrumbItem>()));

    #endregion

    #region 依赖属性变化回调

    /// <summary>
    /// 当前路径变化回调
    /// </summary>
    private static void OnCurrentPathChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is FileExplorerControl control && e.NewValue is string newPath)
        {
            control.HandleCurrentPathChanged(newPath);
        }
    }

    /// <summary>
    /// 处理当前路径变化
    /// </summary>
    private void HandleCurrentPathChanged(string newPath)
    {
        try
        {
            _logger.Debug($"📁 路径变化: {newPath}");
            SetPath(newPath);
            SetupFileSystemWatcher(); // 更新文件系统监视器
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 处理路径变化失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 搜索文本变化回调
    /// </summary>
    private static void OnSearchTextChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is FileExplorerControl control && e.NewValue is string searchText)
        {
            control.HandleSearchTextChanged(searchText);
        }
    }

    /// <summary>
    /// 处理搜索文本变化
    /// </summary>
    private void HandleSearchTextChanged(string searchText)
    {
        try
        {
            _logger.Debug($"🔍 搜索文本变化: {searchText}");

            // 如果搜索文本为空，显示所有文件
            if (string.IsNullOrWhiteSpace(searchText))
            {
                RefreshCurrentPath();
                return;
            }

            // 执行搜索过滤
            PerformSearch(searchText);
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 处理搜索文本变化失败: {ex.Message}", ex);
        }
    }

    #endregion

    #region UI事件处理

    #region 文件夹树事件

    /// <summary>
    /// 文件夹树选择变化事件
    /// </summary>
    private void FolderTreeView_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
    {
        try
        {
            if (e.NewValue is FolderTreeItem item && !item.IsVirtual)
            {
                _logger.Debug($"📁 文件夹树选择: {item.FullPath}");
                NavigateToFolder(item.FullPath);
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 文件夹树选择处理失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// TreeViewItem 展开事件 - 支持动态加载
    /// </summary>
    private void TreeViewItem_Expanded(object sender, RoutedEventArgs e)
    {
        try
        {
            if (sender is System.Windows.Controls.TreeViewItem treeViewItem &&
                treeViewItem.DataContext is FolderTreeItem item)
            {
                _logger.Debug($"📂 展开文件夹: {item.FullPath}");
                OnTreeViewItemExpanded(item);
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 文件夹展开处理失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// TreeViewItem 折叠事件
    /// </summary>
    private void TreeViewItem_Collapsed(object sender, RoutedEventArgs e)
    {
        try
        {
            if (sender is System.Windows.Controls.TreeViewItem treeViewItem &&
                treeViewItem.DataContext is FolderTreeItem item)
            {
                _logger.Debug($"📁 折叠文件夹: {item.FullPath}");
                OnTreeViewItemCollapsed(item);
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 文件夹折叠处理失败: {ex.Message}", ex);
        }
    }

    #endregion

    #region 文件列表事件

    /// <summary>
    /// 文件列表双击事件 - 支持文件夹导航和文件打开
    /// </summary>
    private void FileListView_MouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
    {
        try
        {
            if (FileListView.SelectedItem is FileItem fileItem)
            {
                _logger.Debug($"🖱️ 双击项目: {fileItem.Name}");

                if (fileItem.Type == "文件夹")
                {
                    NavigateToFolder(fileItem.FullPath);
                }
                else
                {
                    OpenFile(fileItem);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 双击事件处理失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 文件列表键盘事件 - 支持键盘导航
    /// </summary>
    private void FileListView_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
    {
        try
        {
            switch (e.Key)
            {
                case System.Windows.Input.Key.Enter:
                    HandleEnterKey();
                    e.Handled = true;
                    break;

                case System.Windows.Input.Key.Back:
                    GoToParent_Click(sender, e);
                    e.Handled = true;
                    break;

                case System.Windows.Input.Key.F5:
                    Refresh_Click(sender, e);
                    e.Handled = true;
                    break;

                case System.Windows.Input.Key.Delete:
                    // 可以在这里添加删除功能
                    e.Handled = true;
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 键盘事件处理失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 处理 Enter 键事件
    /// </summary>
    private void HandleEnterKey()
    {
        if (FileListView.SelectedItem is FileItem fileItem)
        {
            _logger.Debug($"⌨️ Enter键操作: {fileItem.Name}");

            if (fileItem.Type == "文件夹")
            {
                NavigateToFolder(fileItem.FullPath);
            }
            else
            {
                OpenFile(fileItem);
            }
        }
    }

    #endregion

    #region 导航事件

    /// <summary>
    /// 地址栏键盘事件 - 支持 Enter 键导航
    /// </summary>
    private void AddressBar_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
    {
        try
        {
            if (e.Key == System.Windows.Input.Key.Enter)
            {
                var textBox = sender as System.Windows.Controls.TextBox;
                if (textBox != null && !string.IsNullOrWhiteSpace(textBox.Text))
                {
                    _logger.Debug($"🔍 地址栏导航: {textBox.Text}");
                    SetPath(textBox.Text);
                }
                e.Handled = true;
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 地址栏事件处理失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 面包屑导航点击事件
    /// </summary>
    private void BreadcrumbBar_ItemClicked(object sender, Wpf.Ui.Controls.BreadcrumbBarItemClickedEventArgs e)
    {
        try
        {
            if (e.Item is BreadcrumbItem breadcrumbItem)
            {
                _logger.Debug($"🍞 面包屑导航: {breadcrumbItem.Path}");
                SetPath(breadcrumbItem.Path);
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 面包屑导航失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 文件标题单击事件 - 返回上级目录
    /// </summary>
    private void FileTitle_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            _logger.Debug("📄 文件标题点击 - 返回上级");
            GoToParent_Click(sender, e);
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 文件标题点击处理失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 文件夹标题单击事件 - 返回上级目录
    /// </summary>
    private void FolderTitle_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            _logger.Debug("📁 文件夹标题点击 - 返回上级");
            GoToParent_Click(sender, e);
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 文件夹标题点击处理失败: {ex.Message}", ex);
        }
    }

    #endregion

    #endregion

    #region 公共方法

    /// <summary>
    /// 刷新文件浏览器
    /// </summary>
    public async void Refresh()
    {
        await LoadCurrentFolderAsync();
    }

    /// <summary>
    /// 导航到指定路径
    /// </summary>
    /// <param name="path">目标路径</param>
    public async void NavigateTo(string path)
    {
        await SetPathAsync(path);
    }

    /// <summary>
    /// 异步刷新文件浏览器
    /// </summary>
    public async Task RefreshAsync()
    {
        await LoadCurrentFolderAsync();
    }

    /// <summary>
    /// 异步导航到指定路径
    /// </summary>
    /// <param name="path">目标路径</param>
    public async Task NavigateToAsync(string path)
    {
        await SetPathAsync(path);
    }

    /// <summary>
    /// 获取选中的文件列表
    /// </summary>
    /// <returns>选中的文件列表</returns>
    public List<FileItem> GetSelectedFiles()
    {
        var selectedFiles = new List<FileItem>();
        foreach (FileItem item in FileListView.SelectedItems)
        {
            selectedFiles.Add(item);
        }
        return selectedFiles;
    }

    /// <summary>
    /// 获取选中的文件夹
    /// </summary>
    /// <returns>选中的文件夹</returns>
    public FolderTreeItem? GetSelectedFolder()
    {
        return FolderTreeView.SelectedItem as FolderTreeItem;
    }

    #endregion

    #region 事件处理方法

    /// <summary>
    /// 返回根目录按钮点击事件
    /// </summary>
    private void GoToRoot_Click(object sender, RoutedEventArgs e)
    {
        SetPath(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments));
    }

    /// <summary>
    /// 返回上级目录按钮点击事件
    /// </summary>
    private void GoToParent_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var parentDir = Directory.GetParent(CurrentPath);
            if (parentDir != null && parentDir.Exists)
            {
                SetPath(parentDir.FullName);
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"无法访问上级目录: {ex.Message}";
        }
    }

    /// <summary>
    /// 刷新按钮点击事件
    /// </summary>
    private async void Refresh_Click(object sender, RoutedEventArgs e)
    {
        await RefreshAsync();
    }

    /// <summary>
    /// 浏览按钮点击事件
    /// </summary>
    private async void Browse_Click(object sender, RoutedEventArgs e)
    {
        var dialog = new OpenFolderDialog
        {
            Title = "选择文件夹",
            InitialDirectory = CurrentPath
        };

        if (dialog.ShowDialog() == true)
        {
            await SetPathAsync(dialog.FolderName);
        }
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 设置路径
    /// </summary>
    private void SetPath(string path)
    {
        _ = SetPathAsync(path);
    }

    /// <summary>
    /// 异步设置路径
    /// </summary>
    private async Task SetPathAsync(string path)
    {
        if (Directory.Exists(path))
        {
            CurrentPath = path;
            await LoadCurrentFolderAsync();
            UpdateBreadcrumbNavigation();
            SetupFileSystemWatcher();
        }
    }

    /// <summary>
    /// 更新面包屑导航
    /// </summary>
    private void UpdateBreadcrumbNavigation()
    {
        try
        {
            BreadcrumbItems.Clear();

            if (string.IsNullOrEmpty(CurrentPath))
                return;

            // 处理 Windows 路径
            var normalizedPath = CurrentPath.Replace('/', '\\');
            var pathParts = normalizedPath.Split('\\', StringSplitOptions.RemoveEmptyEntries);
            var currentPath = string.Empty;

            for (int i = 0; i < pathParts.Length; i++)
            {
                var part = pathParts[i];
                if (string.IsNullOrEmpty(part)) continue;

                // 构建当前路径
                if (string.IsNullOrEmpty(currentPath))
                {
                    // 第一个部分是驱动器
                    currentPath = part + "\\";
                }
                else
                {
                    currentPath = Path.Combine(currentPath, part);
                }

                var breadcrumbItem = new BreadcrumbItem
                {
                    Name = part,
                    Path = currentPath,
                    IsDrive = i == 0 && part.Length == 2 && part.EndsWith(":"),
                    IsRoot = i == 0
                };

                // 设置图标和名称
                if (breadcrumbItem.IsDrive)
                {
                    breadcrumbItem.Icon = "💾";
                    breadcrumbItem.Name = $"{part} ({GetDriveLabel(part)})";
                }
                else if (i == 0)
                {
                    breadcrumbItem.Icon = "🏠";
                }
                else
                {
                    breadcrumbItem.Icon = "📁";
                }

                BreadcrumbItems.Add(breadcrumbItem);
            }

            // 调试信息
            _logger.Debug($"🍞 面包屑导航更新: {CurrentPath}");
            foreach (var item in BreadcrumbItems)
            {
                _logger.Debug($"  - {item.Name} -> {item.Path}");
            }
        }
        catch (Exception ex)
        {
            // 忽略路径解析错误
            _logger.Warning($"⚠️ 更新面包屑导航失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取驱动器标签
    /// </summary>
    private string GetDriveLabel(string driveLetter)
    {
        try
        {
            var driveInfo = new DriveInfo(driveLetter);
            return string.IsNullOrEmpty(driveInfo.VolumeLabel) ? "本地磁盘" : driveInfo.VolumeLabel;
        }
        catch
        {
            return "本地磁盘";
        }
    }

    /// <summary>
    /// 导航到文件夹
    /// </summary>
    private void NavigateToFolder(string path)
    {
        _ = NavigateToFolderAsync(path);
    }

    /// <summary>
    /// 异步导航到文件夹
    /// </summary>
    private async Task NavigateToFolderAsync(string path)
    {
        if (Directory.Exists(path))
        {
            await SetPathAsync(path);
        }
    }

    /// <summary>
    /// 打开文件
    /// </summary>
    private async void OpenFile(FileItem fileItem)
    {
        var success = await FileOperationService.OpenAsync(fileItem.FullPath);
        if (!success)
        {
            await YMessageBox.ShowErrorAsync($"无法打开文件: {fileItem.Name}", "错误");
        }
    }

    /// <summary>
    /// 树节点展开事件处理 - 动态加载子节点
    /// </summary>
    private void OnTreeViewItemExpanded(FolderTreeItem node)
    {
        if (node == null) return;

        try
        {
            // 检查是否有虚拟节点
            if (node.Children.Count == 1 && node.Children[0].Name == VIRTUAL_NODE_NAME)
            {
                // 清除虚拟节点
                node.Children.Clear();

                // 动态加载真实的子文件夹
                LoadSubFolders(node);

                // 保存展开状态
                _expandedStates[node.FullPath] = true;
            }
        }
        catch (Exception ex)
        {
            // 可以在这里添加错误处理
        }
    }

    /// <summary>
    /// 树节点折叠事件处理
    /// </summary>
    private void OnTreeViewItemCollapsed(FolderTreeItem node)
    {
        if (node != null)
        {
            _expandedStates[node.FullPath] = false;
        }
    }

    /// <summary>
    /// 加载文件夹树 - 使用动态加载机制
    /// </summary>
    private void LoadFolderTree()
    {
        _ = LoadFolderTreeAsync();
    }

    /// <summary>
    /// 异步加载文件夹树 - 使用动态加载机制
    /// </summary>
    private async Task LoadFolderTreeAsync()
    {
        try
        {
            _folderTree.Clear();

            // 在后台线程获取驱动器信息
            var drives = await Task.Run(() => DriveInfo.GetDrives().Where(d => d.IsReady).ToList());

            // 在UI线程更新界面
            foreach (var drive in drives)
            {
                var driveItem = new FolderTreeItem
                {
                    Name = $"{drive.Name} ({drive.VolumeLabel})",
                    FullPath = drive.RootDirectory.FullName,
                    Icon = FileIconService.GetDriveIcon(drive.DriveType),
                    IsExpanded = drive.RootDirectory.FullName.StartsWith(CurrentPath)
                };

                // 为驱动器添加虚拟节点，表示有子文件夹
                await AddVirtualNodeIfNeededAsync(driveItem);

                _folderTree.Add(driveItem);
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"加载文件夹树失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 加载子文件夹 - 动态加载实现
    /// </summary>
    private void LoadSubFolders(FolderTreeItem parent)
    {
        _ = LoadSubFoldersAsync(parent);
    }

    /// <summary>
    /// 异步加载子文件夹 - 动态加载实现
    /// </summary>
    private async Task LoadSubFoldersAsync(FolderTreeItem parent)
    {
        try
        {
            if (Directory.Exists(parent.FullPath))
            {
                // 在后台线程获取目录信息（使用缓存）
                var directories = await Task.Run(() =>
                {
                    try
                    {
                        return _cacheService.GetSubDirectories(parent.FullPath);
                    }
                    catch
                    {
                        return new List<DirectoryInfo>();
                    }
                });

                // 在UI线程更新界面
                foreach (var dirInfo in directories)
                {
                    var child = new FolderTreeItem
                    {
                        Name = dirInfo.Name,
                        FullPath = dirInfo.FullName,
                        Icon = "📁"
                    };

                    // 为子文件夹添加虚拟节点，表示可能有子文件夹
                    await AddVirtualNodeIfNeededAsync(child);

                    parent.Children.Add(child);
                }
            }
        }
        catch (Exception ex)
        {
            // 忽略访问权限错误
            _logger.Warning($"⚠️ 加载子文件夹失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 为文件夹添加虚拟节点（如果该文件夹有子文件夹）
    /// </summary>
    private void AddVirtualNodeIfNeeded(FolderTreeItem folder)
    {
        _ = AddVirtualNodeIfNeededAsync(folder);
    }

    /// <summary>
    /// 异步为文件夹添加虚拟节点（如果该文件夹有子文件夹）
    /// </summary>
    private async Task AddVirtualNodeIfNeededAsync(FolderTreeItem folder)
    {
        try
        {
            if (Directory.Exists(folder.FullPath))
            {
                // 在后台线程检查是否有子目录（使用缓存）
                var hasSubDirectories = await Task.Run(() =>
                {
                    try
                    {
                        return _cacheService.HasSubDirectories(folder.FullPath);
                    }
                    catch
                    {
                        return false;
                    }
                });

                if (hasSubDirectories)
                {
                    // 添加虚拟节点
                    folder.Children.Add(new FolderTreeItem
                    {
                        Name = VIRTUAL_NODE_NAME,
                        FullPath = "",
                        Icon = "⏳",
                        IsVirtual = true
                    });
                }
            }
        }
        catch
        {
            // 忽略访问权限错误
        }
    }

    /// <summary>
    /// 加载当前文件夹的文件
    /// </summary>
    private void LoadCurrentFolder()
    {
        _ = LoadCurrentFolderAsync();
    }

    /// <summary>
    /// 异步加载当前文件夹的文件
    /// </summary>
    private async Task LoadCurrentFolderAsync()
    {
        // 取消之前的加载操作
        _cancellationTokenSource?.Cancel();
        _cancellationTokenSource = new CancellationTokenSource();
        var cancellationToken = _cancellationTokenSource.Token;

        try
        {
            _isLoading = true;
            StatusMessage = "正在加载...";

            // 在UI线程获取当前路径
            var currentPath = CurrentPath;

            if (!Directory.Exists(currentPath))
            {
                _files.Clear();
                StatusMessage = "路径不存在";
                return;
            }

            // 在后台线程获取文件和文件夹信息（使用缓存）
            var result = await Task.Run(() =>
            {
                try
                {
                    // 使用缓存服务获取目录和文件信息
                    var directories = _cacheService.GetSubDirectories(currentPath);
                    var files = _cacheService.GetFiles(currentPath);

                    return new { Directories = directories, Files = files };
                }
                catch (Exception ex)
                {
                    throw new InvalidOperationException($"无法访问目录: {ex.Message}", ex);
                }
            }, cancellationToken);

            var directories = result.Directories;
            var files = result.Files;

            // 检查是否被取消
            cancellationToken.ThrowIfCancellationRequested();

            // 在UI线程更新界面
            _files.Clear();

            // 添加文件夹
            foreach (var dir in directories)
            {
                cancellationToken.ThrowIfCancellationRequested();

                _files.Add(new FileItem
                {
                    Name = dir.Name,
                    FullPath = dir.FullName,
                    Icon = "📁",
                    Type = "文件夹",
                    SizeText = "",
                    ModifiedDate = dir.LastWriteTime.ToString("yyyy-MM-dd HH:mm")
                });
            }

            // 添加文件
            foreach (var file in files)
            {
                cancellationToken.ThrowIfCancellationRequested();

                _files.Add(new FileItem
                {
                    Name = file.Name,
                    FullPath = file.FullName,
                    Icon = FileIconService.GetFileIcon(file.Extension),
                    Type = string.IsNullOrEmpty(file.Extension) ? "文件" : file.Extension.ToUpper().TrimStart('.'),
                    SizeText = FileSizeService.FormatFileSize(file.Length),
                    ModifiedDate = file.LastWriteTime.ToString("yyyy-MM-dd HH:mm")
                });
            }

            // 更新状态
            ItemCount = _files.Count.ToString();
            StatusMessage = $"已加载 {_files.Count} 个项目";

            // 调试信息
            _logger.Info($"📁 FileExplorerControl: 异步加载了 {_files.Count} 个项目");
        }
        catch (OperationCanceledException)
        {
            StatusMessage = "加载已取消";
        }
        catch (InvalidOperationException ex)
        {
            StatusMessage = ex.Message;
        }
        catch (Exception ex)
        {
            StatusMessage = $"加载文件夹失败: {ex.Message}";
        }
        finally
        {
            _isLoading = false;
        }
    }

    /// <summary>
    /// 执行搜索过滤
    /// </summary>
    private void PerformSearch(string searchText)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(searchText))
            {
                return;
            }

            // 获取当前显示的所有文件项
            var allItems = _files.ToList();

            // 执行搜索过滤
            var filteredItems = allItems.Where(item =>
                item.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                item.Type.Contains(searchText, StringComparison.OrdinalIgnoreCase)
            ).ToList();

            // 更新显示的文件列表
            _files.Clear();
            foreach (var item in filteredItems)
            {
                _files.Add(item);
            }

            // 更新状态
            ItemCount = _files.Count.ToString();
            StatusMessage = $"搜索到 {_files.Count} 个匹配项";

            _logger.Debug($"🔍 搜索完成: 关键词='{searchText}', 结果={_files.Count}项");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 搜索失败: {ex.Message}", ex);
            StatusMessage = $"搜索失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 刷新当前路径（清除搜索过滤）
    /// </summary>
    private async void RefreshCurrentPath()
    {
        try
        {
            await RefreshAsync();
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 刷新当前路径失败: {ex.Message}", ex);
        }
    }

    #endregion

    #region 通知辅助方法

    /// <summary>
    /// 显示成功通知
    /// </summary>
    /// <param name="message">消息内容</param>
    private void ShowSuccessNotification(string message)
    {
        NotificationInfoBar.Title = "操作成功";
        NotificationInfoBar.Message = message;
        NotificationInfoBar.Severity = Wpf.Ui.Controls.InfoBarSeverity.Success;
        NotificationInfoBar.IsOpen = true;

        // 3秒后自动关闭
        var timer = new System.Windows.Threading.DispatcherTimer
        {
            Interval = TimeSpan.FromSeconds(3)
        };
        timer.Tick += (s, e) =>
        {
            NotificationInfoBar.IsOpen = false;
            timer.Stop();
        };
        timer.Start();
    }

    /// <summary>
    /// 显示错误通知
    /// </summary>
    /// <param name="message">消息内容</param>
    private void ShowErrorNotification(string message)
    {
        NotificationInfoBar.Title = "操作失败";
        NotificationInfoBar.Message = message;
        NotificationInfoBar.Severity = Wpf.Ui.Controls.InfoBarSeverity.Error;
        NotificationInfoBar.IsOpen = true;

        // 5秒后自动关闭（错误信息显示时间稍长）
        var timer = new System.Windows.Threading.DispatcherTimer
        {
            Interval = TimeSpan.FromSeconds(5)
        };
        timer.Tick += (s, e) =>
        {
            NotificationInfoBar.IsOpen = false;
            timer.Stop();
        };
        timer.Start();
    }

    /// <summary>
    /// 显示信息通知
    /// </summary>
    /// <param name="message">消息内容</param>
    private void ShowInfoNotification(string message)
    {
        NotificationInfoBar.Title = "信息";
        NotificationInfoBar.Message = message;
        NotificationInfoBar.Severity = Wpf.Ui.Controls.InfoBarSeverity.Informational;
        NotificationInfoBar.IsOpen = true;

        // 3秒后自动关闭
        var timer = new System.Windows.Threading.DispatcherTimer
        {
            Interval = TimeSpan.FromSeconds(3)
        };
        timer.Tick += (s, e) =>
        {
            NotificationInfoBar.IsOpen = false;
            timer.Stop();
        };
        timer.Start();
    }

    /// <summary>
    /// 显示警告通知
    /// </summary>
    /// <param name="message">消息内容</param>
    private void ShowWarningNotification(string message)
    {
        NotificationInfoBar.Title = "警告";
        NotificationInfoBar.Message = message;
        NotificationInfoBar.Severity = Wpf.Ui.Controls.InfoBarSeverity.Warning;
        NotificationInfoBar.IsOpen = true;

        // 4秒后自动关闭
        var timer = new System.Windows.Threading.DispatcherTimer
        {
            Interval = TimeSpan.FromSeconds(4)
        };
        timer.Tick += (s, e) =>
        {
            NotificationInfoBar.IsOpen = false;
            timer.Stop();
        };
        timer.Start();
    }

    #endregion

    #region 右键菜单事件处理

    /// <summary>
    /// 右键菜单 - 打开
    /// </summary>
    private void ContextMenu_Open_Click(object sender, RoutedEventArgs e)
    {
        if (FileListView.SelectedItem is FileItem fileItem)
        {
            if (fileItem.Type == "文件夹")
            {
                NavigateToFolder(fileItem.FullPath);
            }
            else
            {
                OpenFile(fileItem);
            }
        }
    }

    /// <summary>
    /// 右键菜单 - 在新窗口中打开
    /// </summary>
    private async void ContextMenu_OpenInNewWindow_Click(object sender, RoutedEventArgs e)
    {
        if (FileListView.SelectedItem is FileItem fileItem)
        {
            if (fileItem.Type == "文件夹")
            {
                var success = await FileOperationService.OpenInNewWindowAsync(fileItem.FullPath);
                if (!success)
                {
                    await YMessageBox.ShowErrorAsync($"无法在新窗口中打开文件夹: {fileItem.Name}", "错误");
                }
            }
            else
            {
                OpenFile(fileItem);
            }
        }
    }

    /// <summary>
    /// 右键菜单 - 复制
    /// </summary>
    private void ContextMenu_Copy_Click(object sender, RoutedEventArgs e)
    {
        var selectedFiles = GetSelectedFiles();
        if (selectedFiles.Count > 0)
        {
            PerformCopy(selectedFiles);
        }
        else
        {
            StatusMessage = "请先选择要复制的文件或文件夹";
        }
    }

    /// <summary>
    /// 右键菜单 - 剪切
    /// </summary>
    private void ContextMenu_Cut_Click(object sender, RoutedEventArgs e)
    {
        var selectedFiles = GetSelectedFiles();
        if (selectedFiles.Count > 0)
        {
            PerformCut(selectedFiles);
        }
        else
        {
            StatusMessage = "请先选择要剪切的文件或文件夹";
        }
    }

    /// <summary>
    /// 右键菜单 - 粘贴
    /// </summary>
    private async void ContextMenu_Paste_Click(object sender, RoutedEventArgs e)
    {
        await PerformPaste();
    }

    /// <summary>
    /// 右键菜单 - 删除
    /// </summary>
    private async void ContextMenu_Delete_Click(object sender, RoutedEventArgs e)
    {
        var selectedFiles = GetSelectedFiles();
        if (selectedFiles.Count > 0)
        {
            await PerformDelete(selectedFiles);
        }
        else
        {
            StatusMessage = "请先选择要删除的文件或文件夹";
        }
    }

    /// <summary>
    /// 右键菜单 - 重命名
    /// </summary>
    private async void ContextMenu_Rename_Click(object sender, RoutedEventArgs e)
    {
        if (FileListView.SelectedItem is FileItem fileItem)
        {
            await PerformRename(fileItem);
        }
        else
        {
            StatusMessage = "请先选择要重命名的文件或文件夹";
        }
    }

    /// <summary>
    /// 右键菜单 - 属性
    /// </summary>
    private async void ContextMenu_Properties_Click(object sender, RoutedEventArgs e)
    {
        if (FileListView.SelectedItem is FileItem fileItem)
        {
            try
            {
                var properties = await FileOperationService.GetPropertiesAsync(fileItem.FullPath);

                // 创建滚动查看器显示属性
                var scrollViewer = new ScrollViewer
                {
                    MaxHeight = 400,
                    VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                    HorizontalScrollBarVisibility = ScrollBarVisibility.Auto
                };

                var textBlock = new System.Windows.Controls.TextBlock
                {
                    Text = properties,
                    TextWrapping = TextWrapping.Wrap,
                    FontFamily = new System.Windows.Media.FontFamily("Consolas"),
                    FontSize = 12,
                    Margin = new Thickness(8),
                    MinWidth = 400
                };

                scrollViewer.Content = textBlock;

                // 使用封装的 YContentDialog
                await YContentDialog.ShowContentAsync("文件属性", scrollViewer);
            }
            catch (Exception ex)
            {
                StatusMessage = $"获取属性失败: {ex.Message}";
            }
        }
        else
        {
            StatusMessage = "请先选择要查看属性的文件或文件夹";
        }
    }

    #endregion

    #region 键盘快捷键处理

    /// <summary>
    /// 键盘按键处理
    /// </summary>
    private async void UserControl_KeyDown(object sender, KeyEventArgs e)
    {
        try
        {
            // F2 - 重命名
            if (e.Key == Key.F2)
            {
                await HandleRenameShortcut();
                e.Handled = true;
                return;
            }

            // Delete - 删除
            if (e.Key == Key.Delete)
            {
                await HandleDeleteShortcut();
                e.Handled = true;
                return;
            }

            // Ctrl+C - 复制
            if (e.Key == Key.C && Keyboard.Modifiers == ModifierKeys.Control)
            {
                HandleCopyShortcut();
                e.Handled = true;
                return;
            }

            // Ctrl+X - 剪切
            if (e.Key == Key.X && Keyboard.Modifiers == ModifierKeys.Control)
            {
                HandleCutShortcut();
                e.Handled = true;
                return;
            }

            // Ctrl+V - 粘贴
            if (e.Key == Key.V && Keyboard.Modifiers == ModifierKeys.Control)
            {
                await HandlePasteShortcut();
                e.Handled = true;
                return;
            }

            // F9 - 测试拖拽功能
            if (e.Key == Key.F9)
            {
                TestDragFunction();
                e.Handled = true;
                return;
            }

            // Ctrl+A - 全选
            if (e.Key == Key.A && Keyboard.Modifiers == ModifierKeys.Control)
            {
                HandleSelectAllShortcut();
                e.Handled = true;
                return;
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"快捷键操作失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 处理重命名快捷键
    /// </summary>
    private async Task HandleRenameShortcut()
    {
        if (FileListView.SelectedItem is FileItem fileItem)
        {
            await PerformRename(fileItem);
        }
        else
        {
            StatusMessage = "请先选择要重命名的文件或文件夹";
        }
    }

    /// <summary>
    /// 处理删除快捷键
    /// </summary>
    private async Task HandleDeleteShortcut()
    {
        var selectedFiles = GetSelectedFiles();
        if (selectedFiles.Count > 0)
        {
            await PerformDelete(selectedFiles);
        }
        else
        {
            StatusMessage = "请先选择要删除的文件或文件夹";
        }
    }

    /// <summary>
    /// 处理复制快捷键
    /// </summary>
    private void HandleCopyShortcut()
    {
        var selectedFiles = GetSelectedFiles();
        if (selectedFiles.Count > 0)
        {
            PerformCopy(selectedFiles);
        }
        else
        {
            StatusMessage = "请先选择要复制的文件或文件夹";
        }
    }

    /// <summary>
    /// 处理剪切快捷键
    /// </summary>
    private void HandleCutShortcut()
    {
        var selectedFiles = GetSelectedFiles();
        if (selectedFiles.Count > 0)
        {
            PerformCut(selectedFiles);
        }
        else
        {
            StatusMessage = "请先选择要剪切的文件或文件夹";
        }
    }

    /// <summary>
    /// 处理粘贴快捷键
    /// </summary>
    private async Task HandlePasteShortcut()
    {
        await PerformPaste();
    }

    /// <summary>
    /// 处理全选快捷键
    /// </summary>
    private void HandleSelectAllShortcut()
    {
        FileListView.SelectAll();
        StatusMessage = $"已选择 {FileListView.SelectedItems.Count} 个项目";
    }

    #endregion

    #region 右键菜单动态显示

    /// <summary>
    /// 右键菜单打开时的处理
    /// </summary>
    private void ContextMenu_Opened(object sender, RoutedEventArgs e)
    {
        // 检查选中的文件是否为 ZIP 压缩文件（仅支持 ZIP）
        bool isZipFile = false;
        bool hasSelectedFiles = FileListView.SelectedItems.Count > 0;
        bool isZipLibAvailable = ArchiveService.Is360ZipInstalled();
        bool hasNonArchiveFiles = false;

        if (FileListView.SelectedItem is FileItem fileItem)
        {
            isZipFile = ArchiveService.IsZipFile(fileItem.FullPath);
            _logger.Debug($"🔍 右键菜单检查文件: {fileItem.Name}, 是否为ZIP: {isZipFile}");
        }

        // 检查选中的文件中是否有非压缩文件（可以压缩的文件）
        if (hasSelectedFiles)
        {
            var selectedFiles = FileListView.SelectedItems.Cast<FileItem>().ToArray();
            hasNonArchiveFiles = selectedFiles.Any(f => !ArchiveService.IsArchiveFile(f.FullPath));
        }

        // 动态显示/隐藏解压菜单项（仅对 ZIP 文件显示）
        ExtractMenuItem.Visibility = isZipFile && isZipLibAvailable ? Visibility.Visible : Visibility.Collapsed;
        ExtractToMenuItem.Visibility = isZipFile && isZipLibAvailable ? Visibility.Visible : Visibility.Collapsed;
        ExtractSeparator.Visibility = isZipFile && isZipLibAvailable ? Visibility.Visible : Visibility.Collapsed;

        // 动态显示/隐藏压缩菜单项（仅当有非压缩文件时显示）
        CompressMenuItem.Visibility = hasNonArchiveFiles && isZipLibAvailable ? Visibility.Visible : Visibility.Collapsed;
        CompressHereMenuItem.Visibility = hasNonArchiveFiles && isZipLibAvailable ? Visibility.Visible : Visibility.Collapsed;
        CompressSeparator.Visibility = hasNonArchiveFiles && isZipLibAvailable ? Visibility.Visible : Visibility.Collapsed;

        _logger.Debug($"🎛️ 右键菜单状态 - ZIP文件: {isZipFile}, 有选中文件: {hasSelectedFiles}, 有非压缩文件: {hasNonArchiveFiles}, 库可用: {isZipLibAvailable}");
    }

    #endregion

    #region 压缩功能

    /// <summary>
    /// 右键菜单 - 添加到压缩文件
    /// </summary>
    private async void ContextMenu_CompressToZip_Click(object sender, RoutedEventArgs e)
    {
        var selectedFiles = GetSelectedFiles();
        _logger.Info($"📦 压缩文件，选中文件数量: {selectedFiles.Count}");

        if (selectedFiles.Count > 0)
        {
            try
            {
                foreach (var file in selectedFiles)
                {
                    _logger.Debug($"📄 选中的文件: {file.FullPath}");
                }

                // 让用户选择压缩文件保存位置和名称
                var saveDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Title = "创建压缩文件",
                    Filter = "ZIP 文件 (*.zip)|*.zip",
                    DefaultExt = ".zip",
                    InitialDirectory = CurrentPath,
                    FileName = selectedFiles.Count == 1 ?
                        Path.GetFileNameWithoutExtension(selectedFiles[0].Name) :
                        "新建压缩文件"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    _logger.Info($"💾 用户选择的压缩文件路径: {saveDialog.FileName}");

                    // 检查是否选择了目标压缩文件本身
                    var validFiles = selectedFiles.Where(f =>
                        !string.Equals(Path.GetFullPath(f.FullPath), Path.GetFullPath(saveDialog.FileName), StringComparison.OrdinalIgnoreCase))
                        .ToArray();

                    if (validFiles.Length == 0)
                    {
                        StatusMessage = "无法压缩：选中的文件包含目标压缩文件本身";
                        ShowWarningNotification("无法压缩自身到压缩包中，请选择其他文件");
                        return;
                    }

                    if (validFiles.Length < selectedFiles.Count)
                    {
                        ShowInfoNotification($"已排除目标压缩文件，将压缩 {validFiles.Length} 个文件");
                    }

                    StatusMessage = "正在使用 SharpZipLib 创建 ZIP 压缩文件...";
                    var filePaths = validFiles.Select(f => f.FullPath).ToArray();
                    var success = await ArchiveService.Create360ZipAsync(filePaths, saveDialog.FileName);

                    if (success)
                    {
                        StatusMessage = $"已创建 ZIP 压缩文件: {Path.GetFileName(saveDialog.FileName)}";
                        _logger.Info("✅ 压缩成功，刷新文件列表");
                        await RefreshAsync(); // 刷新文件列表
                        ShowSuccessNotification($"已创建压缩文件: {Path.GetFileName(saveDialog.FileName)}");
                    }
                    else
                    {
                        StatusMessage = "压缩失败：请检查选中的文件是否有效";
                        _logger.Error("❌ 压缩失败");
                        ShowErrorNotification("压缩失败：请检查选中的文件是否有效");
                    }
                }
                else
                {
                    _logger.Debug("🚫 用户取消了压缩操作");
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"压缩失败: {ex.Message}";
            }
        }
        else
        {
            StatusMessage = "请先选择要压缩的文件或文件夹";
        }
    }

    /// <summary>
    /// 右键菜单 - 压缩到当前文件夹
    /// </summary>
    private async void ContextMenu_CompressToZipHere_Click(object sender, RoutedEventArgs e)
    {
        var selectedFiles = GetSelectedFiles();
        if (selectedFiles.Count > 0)
        {
            try
            {
                // 自动生成压缩文件名
                string archiveName;
                if (selectedFiles.Count == 1)
                {
                    archiveName = Path.GetFileNameWithoutExtension(selectedFiles[0].Name) + ".zip";
                }
                else
                {
                    archiveName = $"压缩文件_{DateTime.Now:yyyyMMdd_HHmmss}.zip";
                }

                var archivePath = Path.Combine(CurrentPath, archiveName);

                // 检查是否选择了目标压缩文件本身
                var validFiles = selectedFiles.Where(f =>
                    !string.Equals(Path.GetFullPath(f.FullPath), Path.GetFullPath(archivePath), StringComparison.OrdinalIgnoreCase))
                    .ToArray();

                if (validFiles.Length == 0)
                {
                    StatusMessage = "无法压缩：选中的文件包含目标压缩文件本身";
                    ShowWarningNotification("无法压缩自身到压缩包中，请选择其他文件");
                    return;
                }

                if (validFiles.Length < selectedFiles.Count)
                {
                    ShowInfoNotification($"已排除目标压缩文件，将压缩 {validFiles.Length} 个文件");
                }

                StatusMessage = "正在使用 SharpZipLib 创建 ZIP 压缩文件...";
                var filePaths = validFiles.Select(f => f.FullPath).ToArray();
                var success = await ArchiveService.Create360ZipAsync(filePaths, archivePath);

                if (success)
                {
                    StatusMessage = $"已创建 ZIP 压缩文件: {archiveName}";
                    await RefreshAsync(); // 刷新文件列表
                    ShowSuccessNotification($"已创建压缩文件: {archiveName}");
                }
                else
                {
                    StatusMessage = "创建 ZIP 压缩文件失败，请检查文件是否有效";
                    ShowErrorNotification("创建压缩文件失败，请检查文件是否有效");
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"压缩失败: {ex.Message}";
            }
        }
        else
        {
            StatusMessage = "请先选择要压缩的文件或文件夹";
        }
    }

    #endregion

    #region 解压缩功能

    /// <summary>
    /// 右键菜单 - 解压到此处
    /// </summary>
    private async void ContextMenu_Extract_Click(object sender, RoutedEventArgs e)
    {
        if (FileListView.SelectedItem is FileItem fileItem)
        {
            _logger.Info($"🗜️ 右键解压文件: {fileItem.FullPath}");

            if (ArchiveService.IsZipFile(fileItem.FullPath))
            {
                try
                {
                    _logger.Debug("✅ 文件是 ZIP 格式");

                    if (!ArchiveService.Is360ZipInstalled())
                    {
                        _logger.Warning("⚠️ SharpZipLib 库不可用");
                        StatusMessage = "SharpZipLib 库不可用";
                        return;
                    }

                    StatusMessage = "正在使用 SharpZipLib 解压文件...";
                    _logger.Info("🚀 开始调用 ExtractHereAsync");
                    var success = await ArchiveService.ExtractHereAsync(fileItem.FullPath);
                    _logger.Info($"📊 ExtractHereAsync 返回结果: {success}");

                    if (success)
                    {
                        StatusMessage = $"已成功解压 {fileItem.Name}";
                        _logger.Info("✅ 解压成功，刷新文件列表");
                        await RefreshAsync(); // 刷新文件列表
                        ShowSuccessNotification($"已成功解压 {fileItem.Name}");
                    }
                    else
                    {
                        StatusMessage = "解压失败，请检查 ZIP 文件是否损坏";
                        _logger.Error("❌ 解压失败");
                        ShowErrorNotification("解压失败，请检查 ZIP 文件是否损坏");
                    }
                }
                catch (Exception ex)
                {
                    StatusMessage = $"解压失败: {ex.Message}";
                    _logger.Error($"🚨 解压异常: {ex.Message}", ex);
                    ShowErrorNotification($"解压失败: {ex.Message}");
                }
            }
            else
            {
                StatusMessage = "选中的文件不是 ZIP 格式，仅支持 ZIP 文件解压";
                _logger.Warning($"⚠️ 不支持的文件格式: {fileItem.FullPath}");
                ShowWarningNotification("仅支持 ZIP 文件解压");
            }
        }
        else
        {
            StatusMessage = "请先选择要解压的压缩文件";
        }
    }

    /// <summary>
    /// 右键菜单 - 解压到指定位置
    /// </summary>
    private async void ContextMenu_ExtractTo_Click(object sender, RoutedEventArgs e)
    {
        if (FileListView.SelectedItem is FileItem fileItem)
        {
            _logger.Info($"🗜️ 右键解压到指定位置: {fileItem.FullPath}");

            if (ArchiveService.IsZipFile(fileItem.FullPath))
            {
                try
                {
                    _logger.Debug("✅ 文件是 ZIP 格式");

                    if (!ArchiveService.Is360ZipInstalled())
                    {
                        _logger.Warning("⚠️ SharpZipLib 库不可用");
                        StatusMessage = "SharpZipLib 库不可用";
                        return;
                    }

                    StatusMessage = "正在使用 SharpZipLib 解压文件...";
                    _logger.Info("🚀 开始调用 ExtractToAsync");
                    var success = await ArchiveService.ExtractToAsync(fileItem.FullPath);
                    _logger.Info($"📊 ExtractToAsync 返回结果: {success}");

                    if (success)
                    {
                        StatusMessage = $"已成功解压 {fileItem.Name}";
                        _logger.Info("✅ 解压成功");
                        ShowSuccessNotification($"已成功解压 {fileItem.Name}");
                    }
                    else
                    {
                        StatusMessage = "解压操作已取消或失败";
                        _logger.Warning("⚠️ 解压操作已取消或失败");
                        ShowInfoNotification("解压操作已取消");
                    }
                }
                catch (Exception ex)
                {
                    StatusMessage = $"解压失败: {ex.Message}";
                    _logger.Error($"🚨 解压异常: {ex.Message}", ex);
                    ShowErrorNotification($"解压失败: {ex.Message}");
                }
            }
            else
            {
                StatusMessage = "选中的文件不是 ZIP 格式，仅支持 ZIP 文件解压";
                _logger.Warning($"⚠️ 不支持的文件格式: {fileItem.FullPath}");
                ShowWarningNotification("仅支持 ZIP 文件解压");
            }
        }
        else
        {
            StatusMessage = "请先选择要解压的压缩文件";
            _logger.Warning("⚠️ 没有选中文件");
        }
    }

    #endregion

    #region 重构的操作方法

    /// <summary>
    /// 执行重命名操作
    /// </summary>
    private async Task PerformRename(FileItem fileItem)
    {
        var currentNameWithoutExt = Path.GetFileNameWithoutExtension(fileItem.Name);
        var currentExtension = Path.GetExtension(fileItem.Name);
        var isFile = !string.IsNullOrEmpty(currentExtension);

        // 使用封装的 YContentDialog
        var (success, newName, newExtension) = await YContentDialog.ShowRenameAsync(
            currentNameWithoutExt, currentExtension, isFile);

        if (success && !string.IsNullOrEmpty(newName))
        {
            try
            {
                var fullNewName = isFile ? newName + newExtension : newName;
                var renameSuccess = await FileOperationService.RenameAsync(fileItem.FullPath, fullNewName);

                if (renameSuccess)
                {
                    StatusMessage = $"已重命名为 {fullNewName}";
                    await RefreshAsync(); // 刷新文件列表
                }
                else
                {
                    StatusMessage = "重命名失败，文件名可能已存在或包含无效字符";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"重命名失败: {ex.Message}";
            }
        }
    }

    /// <summary>
    /// 执行删除操作
    /// </summary>
    private async Task PerformDelete(List<FileItem> selectedFiles)
    {
        // 使用封装的 YMessageBox
        var result = await YMessageBox.ShowDeleteConfirmAsync("", selectedFiles.Count);

        if (result == YMessageBoxResult.Primary)
        {
            try
            {
                var filePaths = selectedFiles.Select(f => f.FullPath).ToArray();
                var success = await FileOperationService.DeleteToRecycleBinAsync(filePaths);

                if (success)
                {
                    StatusMessage = $"已删除 {selectedFiles.Count} 个项目到回收站";
                    await RefreshAsync(); // 刷新文件列表
                }
                else
                {
                    StatusMessage = "删除操作失败";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"删除失败: {ex.Message}";
            }
        }
    }

    /// <summary>
    /// 执行复制操作
    /// </summary>
    private void PerformCopy(List<FileItem> selectedFiles)
    {
        try
        {
            var filePaths = selectedFiles.Select(f => f.FullPath).ToArray();
            FileOperationService.CopyToClipboard(filePaths);
            StatusMessage = $"已复制 {selectedFiles.Count} 个项目到剪贴板";
        }
        catch (Exception ex)
        {
            StatusMessage = $"复制失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 执行剪切操作
    /// </summary>
    private void PerformCut(List<FileItem> selectedFiles)
    {
        try
        {
            var filePaths = selectedFiles.Select(f => f.FullPath).ToArray();
            FileOperationService.CutToClipboard(filePaths);
            StatusMessage = $"已剪切 {selectedFiles.Count} 个项目到剪贴板";
        }
        catch (Exception ex)
        {
            StatusMessage = $"剪切失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 执行粘贴操作
    /// </summary>
    private async Task PerformPaste()
    {
        try
        {
            if (System.Windows.Clipboard.ContainsFileDropList())
            {
                var files = System.Windows.Clipboard.GetFileDropList();
                var filePaths = files.Cast<string>().ToArray();
                var success = await FileOperationService.PasteFromClipboardAsync(filePaths, CurrentPath);

                if (success)
                {
                    StatusMessage = "粘贴操作完成";
                    await RefreshAsync(); // 刷新文件列表
                }
                else
                {
                    StatusMessage = "粘贴操作失败";
                }
            }
            else
            {
                StatusMessage = "剪贴板中没有文件";
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"粘贴失败: {ex.Message}";
        }
    }

    #endregion

    #region 资源释放

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 释放资源的具体实现
    /// </summary>
    /// <param name="disposing">是否正在释放托管资源</param>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                // 取消所有正在进行的异步操作
                _cancellationTokenSource?.Cancel();

                // 停止文件系统监视
                if (_fileSystemWatcher != null)
                {
                    _fileSystemWatcher.EnableRaisingEvents = false;
                    _fileSystemWatcher.Dispose();
                    _fileSystemWatcher = null;
                }

                // 停止防抖计时器
                if (_loadDebounceTimer != null)
                {
                    _loadDebounceTimer.Dispose();
                    _loadDebounceTimer = null;
                }

                // 释放取消令牌源
                if (_cancellationTokenSource != null)
                {
                    _cancellationTokenSource.Dispose();
                    _cancellationTokenSource = null;
                }

                // 清理缓存服务
                _cacheService?.Dispose();

                // 清理集合数据
                _files?.Clear();
                _folderTree?.Clear();
                _expandedStates?.Clear();

                // 清理事件处理器（如果有的话）
                // 注意：WPF控件的事件通常会自动清理，但如果有自定义事件可以在这里清理
            }

            _disposed = true;
        }
    }

    #region 拖拽功能

    /// <summary>
    /// 预览拖拽事件 - 确保拖拽事件能够正常工作
    /// </summary>
    private void FileListView_PreviewDragOver(object sender, DragEventArgs e)
    {
        // 设置为已处理，确保拖拽事件能够继续
        e.Handled = true;
    }

    /// <summary>
    /// 拖拽进入事件
    /// </summary>
    private void FileListView_DragOver(object sender, DragEventArgs e)
    {
        try
        {
            _logger.Debug("🖱️ DragOver事件触发");
            StatusMessage = "正在拖拽文件...";

            // 检查是否包含文件
            if (e.Data.GetDataPresent(DataFormats.FileDrop))
            {
                e.Effects = DragDropEffects.Copy;
                _logger.Debug("✅ 检测到文件拖拽，允许放置");
                StatusMessage = "可以放置文件到此处";
            }
            else
            {
                e.Effects = DragDropEffects.None;
                _logger.Debug("❌ 不支持的拖拽数据格式");
                StatusMessage = "不支持的拖拽数据格式";
            }

            e.Handled = true;
        }
        catch (Exception ex)
        {
            _logger.Error($"🚨 DragOver异常: {ex.Message}", ex);
            StatusMessage = $"拖拽异常: {ex.Message}";
        }
    }

    /// <summary>
    /// 拖拽放置事件
    /// </summary>
    private void FileListView_Drop(object sender, DragEventArgs e)
    {
        try
        {
            if (_isProcessingDrop)
            {
                _logger.Info("⚠️ 原生Drop事件被跳过，正在处理中");
                return;
            }

            _isProcessingDrop = true;

            // 详细记录拖拽事件信息
            _logger.Info("📁 原生Drop事件触发");
            _logger.Info($"🔍 事件详情: AllowedEffects={e.AllowedEffects}, Effects={e.Effects}");
            _logger.Info($"🔍 数据格式: {string.Join(", ", e.Data.GetFormats())}");
            _logger.Info($"🔍 鼠标位置: {System.Windows.Forms.Control.MousePosition}");

            StatusMessage = "正在处理拖拽的文件...";

            if (e.Data.GetDataPresent(DataFormats.FileDrop))
            {
                var files = (string[])e.Data.GetData(DataFormats.FileDrop);
                _logger.Info($"📊 拖拽的文件数量: {files?.Length ?? 0}");

                if (files != null && files.Length > 0)
                {
                    StatusMessage = $"接收到 {files.Length} 个外部文件";
                    foreach (var file in files)
                    {
                        _logger.Debug($"📄 拖拽的文件: {file}");
                    }

                    // 异步处理拖拽的文件 - 在UI线程中获取当前路径
                    var currentPath = CurrentPath;
                    _ = Task.Run(async () => await HandleDroppedFilesAsync(files, currentPath));
                }
            }
            else
            {
                _logger.Warning("⚠️ 拖拽的数据不是文件格式");
                StatusMessage = "拖拽的数据不是文件格式";
            }

            e.Handled = true;
        }
        catch (Exception ex)
        {
            _logger.Error($"🚨 Drop异常: {ex.Message}", ex);
            StatusMessage = $"拖拽处理异常: {ex.Message}";
        }
        finally
        {
            _isProcessingDrop = false;
        }
    }

    /// <summary>
    /// 处理拖拽的文件
    /// </summary>
    private async Task HandleDroppedFilesAsync(string[] files, string targetPath)
    {
        try
        {
            using (_logger.Monitor("处理拖拽文件"))
            {
                _logger.Info($"🚀 开始处理拖拽文件，数量: {files.Length}");
                _logger.Info($"📂 目标路径: {targetPath}");

                // 详细记录所有拖入的文件
                for (int i = 0; i < files.Length; i++)
                {
                    _logger.Info($"📄 拖入文件[{i}]: {files[i]}");
                }

                if (string.IsNullOrEmpty(targetPath))
                {
                    _logger.Warning("⚠️ 目标路径为空，无法处理拖拽");
                    await Dispatcher.InvokeAsync(() => StatusMessage = "目标路径为空，无法处理拖拽");
                    return;
                }

                _logger.Info($"📂 目标路径: {targetPath}");
                await Dispatcher.InvokeAsync(() => StatusMessage = $"正在复制 {files.Length} 个文件到 {targetPath}");

                // 统计变量
                int successCount = 0;
                int failedCount = 0;
                int skippedCount = 0;

                foreach (var sourceFile in files)
                {
                    try
                    {
                        var fileName = Path.GetFileName(sourceFile);
                        var targetFilePath = Path.Combine(targetPath, fileName);

                        _logger.Debug($"📋 复制: {sourceFile} -> {targetFilePath}");

                        if (File.Exists(sourceFile))
                        {
                            // 复制文件 - 添加文件占用检测
                            try
                            {
                                File.Copy(sourceFile, targetFilePath, true);
                                _logger.Info($"✅ 文件复制成功: {fileName}");
                                successCount++;
                            }
                            catch (IOException ioEx) when (ioEx.Message.Contains("being used by another process"))
                            {
                                _logger.Warning($"⚠️ 文件被占用，跳过: {fileName} - {ioEx.Message}");
                                skippedCount++;
                            }
                            catch (Exception fileEx)
                            {
                                _logger.Error($"❌ 文件复制失败: {fileName} - {fileEx.Message}");
                                failedCount++;
                            }
                        }
                        else if (Directory.Exists(sourceFile))
                        {
                            // 复制目录
                            try
                            {
                                await CopyDirectoryAsync(sourceFile, targetFilePath);
                                _logger.Info($"✅ 目录复制成功: {fileName}");
                                successCount++;
                            }
                            catch (Exception dirEx)
                            {
                                _logger.Error($"❌ 目录复制失败: {fileName} - {dirEx.Message}");
                                failedCount++;
                            }
                        }
                        else
                        {
                            _logger.Warning($"⚠️ 源文件不存在: {sourceFile}");
                            skippedCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Error($"❌ 复制文件失败: {sourceFile}", ex);
                    }
                }

                // 显示处理结果统计
                var resultMessage = $"拖拽完成: 成功 {successCount}, 失败 {failedCount}, 跳过 {skippedCount}";
                _logger.Info($"✅ {resultMessage}");

                // 刷新文件列表
                await Dispatcher.InvokeAsync(async () =>
                {
                    _logger.Debug("🔄 刷新文件列表");
                    await RefreshAsync();
                    StatusMessage = $"✅ {resultMessage}";
                });
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"🚨 处理拖拽文件异常: {ex.Message}", ex);
            await Dispatcher.InvokeAsync(() => StatusMessage = $"处理拖拽文件失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 递归复制目录
    /// </summary>
    private async Task CopyDirectoryAsync(string sourceDir, string targetDir)
    {
        await Task.Run(() =>
        {
            if (!Directory.Exists(targetDir))
                Directory.CreateDirectory(targetDir);

            // 复制文件
            foreach (var file in Directory.GetFiles(sourceDir))
            {
                try
                {
                    var fileName = Path.GetFileName(file);
                    var targetFile = Path.Combine(targetDir, fileName);
                    File.Copy(file, targetFile, true);
                    _logger.Debug($"✅ 目录文件复制成功: {fileName}");
                }
                catch (IOException ioEx) when (ioEx.Message.Contains("being used by another process"))
                {
                    var fileName = Path.GetFileName(file);
                    _logger.Warning($"⚠️ 目录文件被占用，跳过: {fileName}");
                }
                catch (Exception ex)
                {
                    var fileName = Path.GetFileName(file);
                    _logger.Error($"❌ 目录文件复制失败: {fileName} - {ex.Message}");
                }
            }

            // 递归复制子目录
            foreach (var subDir in Directory.GetDirectories(sourceDir))
            {
                var dirName = Path.GetFileName(subDir);
                var targetSubDir = Path.Combine(targetDir, dirName);
                CopyDirectoryAsync(subDir, targetSubDir).Wait();
            }
        });
    }

    #endregion

    #region GongSolutions.WPF.DragDrop 接口实现

    /// <summary>
    /// 拖拽悬停处理 - IDropTarget接口实现
    /// </summary>
    void IDropTarget.DragOver(IDropInfo dropInfo)
    {
        try
        {
            _logger.Debug("🖱️ GongSolutions DragOver事件触发");

            // 检查是否包含外部文件
            if (dropInfo.Data is System.Windows.DataObject dataObject &&
                dataObject.GetDataPresent(DataFormats.FileDrop))
            {
                var files = dataObject.GetData(DataFormats.FileDrop) as string[];
                if (files != null && files.Length > 0)
                {
                    dropInfo.Effects = System.Windows.DragDropEffects.Copy;
                    dropInfo.DropTargetAdorner = DropTargetAdorners.Highlight;
                    _logger.Debug($"✅ 检测到 {files.Length} 个外部文件拖拽，允许放置");
                    StatusMessage = $"可以放置 {files.Length} 个文件到此处";
                    return;
                }
            }

            // 其他情况不允许拖拽
            dropInfo.Effects = System.Windows.DragDropEffects.None;
            _logger.Debug("❌ 不支持的拖拽数据格式");
        }
        catch (Exception ex)
        {
            _logger.Error($"🚨 DragOver异常: {ex.Message}", ex);
            dropInfo.Effects = System.Windows.DragDropEffects.None;
        }
    }

    /// <summary>
    /// 拖拽放置处理 - IDropTarget接口实现
    /// </summary>
    void IDropTarget.Drop(IDropInfo dropInfo)
    {
        try
        {
            if (_isProcessingDrop)
            {
                _logger.Info("⚠️ GongSolutions Drop事件被跳过，正在处理中");
                return;
            }

            _isProcessingDrop = true;

            // 详细记录拖拽事件信息
            _logger.Info("📁 GongSolutions Drop事件触发");
            _logger.Info($"🔍 DropInfo详情: Effects={dropInfo.Effects}, DropPosition={dropInfo.DropPosition}");
            _logger.Info($"🔍 数据类型: {dropInfo.Data?.GetType().Name ?? "null"}");
            _logger.Info($"🔍 鼠标位置: {System.Windows.Forms.Control.MousePosition}");

            // 检查是否包含外部文件
            if (dropInfo.Data is System.Windows.DataObject dataObject &&
                dataObject.GetDataPresent(DataFormats.FileDrop))
            {
                var files = dataObject.GetData(DataFormats.FileDrop) as string[];
                if (files != null && files.Length > 0)
                {
                    _logger.Info($"📊 拖拽的文件数量: {files.Length}");
                    StatusMessage = $"接收到 {files.Length} 个文件";

                    foreach (var file in files)
                    {
                        _logger.Debug($"📄 拖拽的文件: {file}");
                    }

                    // 异步处理拖拽的文件 - 在UI线程中获取当前路径
                    var currentPath = CurrentPath;
                    _ = Task.Run(async () => await HandleDroppedFilesAsync(files, currentPath));
                    return;
                }
            }

            _logger.Warning("⚠️ 拖拽的数据不是文件格式");
            StatusMessage = "拖拽的数据不是文件格式";
        }
        catch (Exception ex)
        {
            _logger.Error($"🚨 Drop异常: {ex.Message}", ex);
            StatusMessage = $"拖拽处理异常: {ex.Message}";
        }
        finally
        {
            _isProcessingDrop = false;
        }
    }

    #endregion

    #region 拖拽测试方法

    /// <summary>
    /// 测试拖拽功能 - 手动触发拖拽
    /// </summary>
    private void TestDragFunction()
    {
        try
        {
            var selectedFiles = GetSelectedFiles();
            _logger.Info($"🧪 F9测试拖拽 - 选中文件数量: {selectedFiles.Count}");

            if (selectedFiles.Count > 0)
            {
                _logger.Info($"🧪 手动测试拖拽: {selectedFiles[0].Name}");

                // 创建拖拽数据
                var dragData = _dragService.CreateDragDataObject(selectedFiles[0]);

                // 手动启动拖拽
                var result = System.Windows.DragDrop.DoDragDrop(
                    FileListView,
                    dragData,
                    DragDropEffects.Copy | DragDropEffects.Move
                );

                _logger.Info($"🧪 手动拖拽结果: {result}");
                StatusMessage = $"🧪 测试拖拽完成: {result}";
            }
            else
            {
                _logger.Warning("🧪 没有选中的文件进行测试");
                StatusMessage = "🧪 请先选择一个文件再按F9测试拖拽";
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 手动拖拽测试失败: {ex.Message}");
            StatusMessage = $"❌ 拖拽测试失败: {ex.Message}";
        }
    }

    #endregion



    #region IDragSource 接口实现 - 支持拖拽到外部

    /// <summary>
    /// 检查是否可以开始拖拽
    /// </summary>
    public bool CanStartDrag(IDragInfo dragInfo)
    {
        try
        {
            _logger.Info("🔍 CanStartDrag 被调用");

            // 检查是否有选中的文件
            var selectedFiles = GetSelectedFiles();
            _logger.Info($"🔍 选中文件数量: {selectedFiles.Count}");

            if (selectedFiles.Count == 0)
            {
                _logger.Warning("❌ 没有选中的文件，无法开始拖拽");
                return false;
            }

            // 检查选中的文件是否都存在
            var validFiles = selectedFiles.Where(f => File.Exists(f.FullPath) || Directory.Exists(f.FullPath)).ToList();
            _logger.Info($"🔍 有效文件数量: {validFiles.Count}");

            if (validFiles.Count == 0)
            {
                _logger.Warning("❌ 选中的文件都不存在，无法开始拖拽");
                return false;
            }

            _logger.Info($"✅ 可以开始拖拽，选中文件: {validFiles[0].Name}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 检查拖拽条件失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 开始拖拽操作 - 直接使用原生Windows拖拽确保外部兼容性
    /// </summary>
    public void StartDrag(IDragInfo dragInfo)
    {
        try
        {
            var selectedFiles = GetSelectedFiles();
            if (selectedFiles.Count == 0)
            {
                _logger.Warning("❌ 没有选中的文件");
                return;
            }

            var firstFile = selectedFiles[0];
            _logger.Info($"🚀 StartDrag: {firstFile.Name}");

            // 直接启动原生Windows拖拽
            StatusMessage = $"🚀 正在拖拽: {firstFile.Name}";

            // 创建标准的Windows文件拖拽数据
            var filePaths = new[] { firstFile.FullPath };
            var dataObject = new DataObject();
            dataObject.SetData(DataFormats.FileDrop, filePaths);

            _logger.Info($"🚀 启动原生拖拽: {firstFile.FullPath}");

            var result = System.Windows.DragDrop.DoDragDrop(
                FileListView,
                dataObject,
                DragDropEffects.Copy | DragDropEffects.Move
            );

            var message = result switch
            {
                DragDropEffects.Copy => $"✅ 文件已复制: {firstFile.Name}",
                DragDropEffects.Move => $"✅ 文件已移动: {firstFile.Name}",
                DragDropEffects.None => $"⚠️ 拖拽取消: {firstFile.Name}",
                _ => $"✅ 拖拽完成: {firstFile.Name}"
            };

            _logger.Info($"🎯 拖拽结果: {result} - {firstFile.Name}");
            StatusMessage = message;
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 拖拽启动失败: {ex.Message}");
            StatusMessage = $"❌ 拖拽失败: {ex.Message}";
        }
    }



    /// <summary>
    /// 创建增强的拖拽数据对象 - 最大化外部应用程序兼容性
    /// </summary>
    private DataObject CreateEnhancedDragDataObject(List<FileItem> files)
    {
        try
        {
            var dataObject = new DataObject();
            var filePaths = files.Select(f => f.FullPath).ToArray();

            // 🎯 核心：Windows标准文件拖拽格式
            dataObject.SetData(DataFormats.FileDrop, filePaths);

            // 🎯 增强兼容性：多种文本格式
            var pathsText = string.Join(Environment.NewLine, filePaths);
            dataObject.SetData(DataFormats.Text, pathsText);
            dataObject.SetData(DataFormats.UnicodeText, pathsText);
            dataObject.SetData(DataFormats.StringFormat, pathsText);

            // 🎯 Windows Shell格式 - 提高外部应用程序识别率
            if (files.Count == 1)
            {
                var fileName = Path.GetFileName(files[0].FullPath);
                dataObject.SetData("FileName", fileName);
                dataObject.SetData("FileNameW", fileName);
            }

            // 🎯 设置拖拽偏好 - 帮助外部应用程序理解操作意图
            dataObject.SetData("Preferred DropEffect", (int)DragDropEffects.Copy);

            _logger.Debug($"📋 增强拖拽数据已创建: {files.Count} 个文件");
            _logger.Debug($"🔧 数据格式: FileDrop + Text + FileName + PreferredDropEffect");

            return dataObject;
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 创建拖拽数据对象失败: {ex.Message}");
            return new DataObject();
        }
    }

    /// <summary>
    /// 拖拽操作完成回调 - 智能处理内部/外部拖拽结果
    /// </summary>
    public void DragDropOperationFinished(System.Windows.DragDropEffects operationResult, IDragInfo dragInfo)
    {
        try
        {
            var selectedFiles = GetSelectedFiles();
            var fileCount = selectedFiles.Count;
            var displayName = fileCount == 1 ? selectedFiles[0].Name : $"{fileCount} 个文件";

            // 🎯 关键修复：检查当前鼠标位置判断是否为外部拖拽
            var currentMousePos = System.Windows.Forms.Control.MousePosition;
            var isOutsideApp = IsMouseOutsideApplication(currentMousePos);

            _logger.Info($"🔍 拖拽完成位置检查: 鼠标位置({currentMousePos.X}, {currentMousePos.Y}), 外部拖拽: {isOutsideApp}");

            // 🎯 如果鼠标在应用程序外部，强制使用Windows API拖拽
            if (isOutsideApp)
            {
                _logger.Info($"🔄 检测到外部拖拽，启动Windows底层API");

                Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                {
                    _logger.Info($"🚀 开始Windows底层API拖拽: {displayName}");
                    var success = _dragService.StartWindowsApiDrag(selectedFiles[0]);

                    if (success)
                    {
                        _logger.Info($"✅ Windows底层API拖拽成功: {displayName}");
                        StatusMessage = $"✅ 外部拖拽完成: {displayName}";
                    }
                    else
                    {
                        _logger.Warning($"❌ Windows底层API拖拽失败: {displayName}");
                        StatusMessage = $"❌ 外部拖拽失败: {displayName}";
                    }
                }));
                return;
            }

            // 🎯 应用程序内部拖拽 - 处理GongSolutions的结果
            if (operationResult == System.Windows.DragDropEffects.None)
            {
                _logger.Warning($"⚠️ 内部拖拽无效果: {displayName}");
                StatusMessage = $"⚠️ 拖拽取消: {displayName}";
                return;
            }

            // 处理成功的内部拖拽
            var message = operationResult switch
            {
                System.Windows.DragDropEffects.Copy => $"✅ 文件已复制: {displayName}",
                System.Windows.DragDropEffects.Move => $"✅ 文件已移动: {displayName}",
                _ => $"✅ 拖拽完成: {displayName}"
            };

            _logger.Info($"✅ 内部拖拽成功: {displayName} - {operationResult}");
            StatusMessage = message;
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 拖拽完成处理失败: {ex.Message}");
            StatusMessage = "❌ 拖拽处理失败";
        }
    }

    /// <summary>
    /// 拖拽完成后的处理（IDragSource接口要求）
    /// </summary>
    public void Dropped(IDropInfo dropInfo)
    {
        try
        {
            var selectedFiles = GetSelectedFiles();
            var displayName = selectedFiles.Count == 1 ? selectedFiles[0].Name : $"{selectedFiles.Count} 个文件";

            _logger.Info($"📍 拖拽已放置到外部: {displayName}");
            StatusMessage = $"📍 已放置到外部: {displayName}";
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 拖拽放置处理失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 处理拖拽过程中的异常（IDragSource接口要求）
    /// </summary>
    public bool TryCatchOccurredException(Exception exception)
    {
        // 记录异常并返回true表示已处理
        _logger.Error($"❌ 拖拽过程中发生异常: {exception.Message}");
        StatusMessage = $"❌ 拖拽异常: {exception.Message}";
        return true; // 表示异常已被处理
    }

    /// <summary>
    /// 拖拽被取消时的处理（IDragSource接口要求）
    /// </summary>
    public void DragCancelled()
    {
        try
        {
            var selectedFiles = GetSelectedFiles();
            var displayName = selectedFiles.Count == 1 ? selectedFiles[0].Name : $"{selectedFiles.Count} 个文件";

            _logger.Info($"🚫 拖拽已取消: {displayName}");
            StatusMessage = $"🚫 拖拽已取消: {displayName}";
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 拖拽取消处理失败: {ex.Message}");
            StatusMessage = "🚫 拖拽已取消";
        }
    }

    #endregion

    #region 拖拽辅助方法

    /// <summary>
    /// 检查鼠标是否在应用程序外部
    /// </summary>
    private bool IsMouseOutsideApplication(System.Drawing.Point mousePosition)
    {
        try
        {
            var mainWindow = Application.Current.MainWindow;
            if (mainWindow == null) return true;

            // 获取窗口边界
            var windowBounds = new System.Drawing.Rectangle(
                (int)mainWindow.Left,
                (int)mainWindow.Top,
                (int)mainWindow.Width,
                (int)mainWindow.Height
            );

            // 检查鼠标是否在窗口外部
            var isOutside = !windowBounds.Contains(mousePosition);

            _logger.Debug($"🔍 鼠标位置: {mousePosition}, 窗口边界: {windowBounds}, 外部拖拽: {isOutside}");

            return isOutside;
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 检查鼠标位置失败: {ex.Message}");
            return true; // 出错时假设是外部拖拽
        }
    }

    #endregion

    /// <summary>
    /// 析构函数
    /// </summary>
    ~FileExplorerControl()
    {
        Dispose(false);
    }

    #endregion
}