# 🚀 Zylo.YData

## 基于 FreeSql 的现代化数据访问层

[![.NET](https://img.shields.io/badge/.NET-8.0+-blue.svg)](https://dotnet.microsoft.com/)
[![FreeSql](https://img.shields.io/badge/FreeSql-3.5+-green.svg)](https://freesql.net/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![Tests](https://img.shields.io/badge/Tests-99%25-brightgreen.svg)](#-测试覆盖)

## 📋 项目概述

**Zylo.YData** 是一个基于 FreeSql 的现代化数据访问层，在保持 FreeSql 强大功能的基础上，提供更简洁的 API、完善的依赖注入支持和丰富的扩展功能。

### 🎯 核心特性

- 🔥 **FreeSql 核心** - 保持 FreeSql 的所有优秀特性和高性能
- 💉 **依赖注入优先** - 完美支持 .NET 依赖注入容器
- 🚀 **简化 API** - 提供更友好的静态 API 和扩展方法
- 🔄 **多数据库支持** - 轻松管理和切换多个数据库
- 📊 **批量操作** - 高效的批量 CRUD 操作
- ✅ **数据验证** - 内置数据验证功能
- 🧪 **完整测试** - 99% 测试覆盖率，质量保证

### 🎯 支持的数据库

- SQL Server
- SQLite
- MySQL
- PostgreSQL
- Oracle
- 达梦、人大金仓等国产数据库

## 🚀 快速开始

### 1. 安装包

```bash
# 核心包
dotnet add package Zylo.YData

# 数据库提供程序（根据需要选择）
dotnet add package FreeSql.Provider.SqlServer
dotnet add package FreeSql.Provider.Sqlite
dotnet add package FreeSql.Provider.MySql
dotnet add package FreeSql.Provider.PostgreSQL
```

### 2. 基础配置

#### 方式一：静态配置（简单项目）

```csharp
using Zylo.YData;

// 零配置（从 appsettings.json 读取）
YData.ConfigureAuto();

// 单参数配置
YData.ConfigureAuto("Data Source=test.db");

// 双参数配置
YData.ConfigureAuto("Server=.;Database=MyDB;Trusted_Connection=true;", YDataType.SqlServer);

// 高级配置
YData.Configure(options =>
{
    options.ConnectionString = "Data Source=test.db";
    options.DataType = YDataType.Sqlite;
    options.EnableAutoSyncStructure = true;
    options.EnableMonitorCommand = true;
    options.DefaultQueryTimeout = TimeSpan.FromSeconds(30);
});
```

#### 方式二：依赖注入（推荐）

```csharp
using Zylo.YData;

// Program.cs 或 Startup.cs
services.AddYDataAuto("Data Source=test.db");

// 或者使用配置选项
services.AddYData(options =>
{
    options.ConnectionString = "Data Source=test.db";
    options.DataType = YDataType.Sqlite;
    options.EnableAutoSyncStructure = true;
    options.DefaultQueryTimeout = TimeSpan.FromSeconds(30);
});
```

### 3. 定义实体

```csharp
using FreeSql.DataAnnotations;

[Table(Name = "users")]
public class User
{
    [Column(IsIdentity = true, IsPrimary = true)]
    public int Id { get; set; }

    [Column(StringLength = 50)]
    public string Name { get; set; } = string.Empty;

    [Column(StringLength = 100)]
    public string Email { get; set; } = string.Empty;

    public int Age { get; set; }
    public DateTime CreateTime { get; set; } = DateTime.Now;
    public DateTime? UpdateTime { get; set; }
    public bool IsActive { get; set; } = true;
}
```

## 💡 核心功能

### 1. 基础 CRUD 操作

```csharp
// 插入
var user = new User { Name = "张三", Email = "<EMAIL>", Age = 25 };
await YData.InsertAsync(user);

// 查询单个
var user = await YData.GetAsync<User>(1);

// 查询列表
var users = await YData.Select<User>()
    .Where(u => u.IsActive)
    .ToListAsync();

// 更新
user.Age = 26;
await YData.UpdateAsync(user);

// 删除
await YData.DeleteAsync<User>(1);
```

### 2. 高级查询

```csharp
// 条件查询
var activeUsers = await YData.Select<User>()
    .Where(u => u.IsActive)
    .Where(u => u.Age >= 18)
    .OrderBy(u => u.Name)
    .ToListAsync();

// 分页查询
var pagedResult = await YData.Select<User>()
    .Where(u => u.IsActive)
    .ToPagedResultAsync(pageIndex: 1, pageSize: 10);

Console.WriteLine($"总记录数: {pagedResult.TotalCount}");
Console.WriteLine($"当前页数据: {pagedResult.Items.Count}");

// 快速分页（大数据量场景）
var fastPaged = await YData.Select<User>()
    .Where(u => u.IsActive)
    .ToFastPagedResultAsync(1, 10);
```

### 3. 扩展方法

```csharp
// WhereIf 条件查询
var result = await YData.Select<User>()
    .WhereIf(!string.IsNullOrEmpty(name), u => u.Name.Contains(name))
    .WhereIf(minAge.HasValue, u => u.Age >= minAge.Value)
    .ToListAsync();

// 批量操作扩展
var users = new List<User> { /* ... */ };

// 批量插入
var insertCount = await users.YBatchInsertAsync();

// 批量更新
var updateCount = await users.YBatchUpdateAsync();

// 批量删除
var deleteCount = await users.YBatchDeleteAsync();
```

### 4. 事务操作

```csharp
// 事务操作
var result = await YData.TransactionAsync(async () =>
{
    var user = new User { Name = "李四", Age = 30 };
    await YData.InsertAsync(user);

    var order = new Order { UserId = user.Id, Amount = 99.99m };
    await YData.InsertAsync(order);

    return true;
});
```

### 5. 多数据库支持

```csharp
// 注册多个数据库
YData.RegisterSqliteDatabases(
    ("users", "users.db"),
    ("orders", "orders.db"),
    ("products", "products.db")
);

// 使用指定数据库
var users = await YData.Select<User>("users").ToListAsync();
var orders = await YData.Select<Order>("orders").ToListAsync();

// 临时切换数据库
var result = await YDataSwitcher.WithDatabaseAsync("analytics", async () =>
{
    return await YData.Select<AnalyticsData>().CountAsync();
});

// 在所有数据库中执行操作
var userCounts = await YDataSwitcher.ExecuteInAllDatabasesAsync(async dbName =>
{
    return await YData.Select<User>().CountAsync();
});

// 并行执行
var results = await YDataSwitcher.ExecuteInAllDatabasesParallelAsync(async dbName =>
{
    return await YData.Select<User>().CountAsync();
});
```

## 💉 依赖注入使用

### 1. 服务注册

```csharp
// Program.cs
var builder = WebApplication.CreateBuilder(args);

// 零参数配置
builder.Services.AddYDataAuto();

// 单参数配置
builder.Services.AddYDataAuto("Data Source=test.db");

// 双参数配置
builder.Services.AddYDataAuto("Data Source=test.db", YDataType.Sqlite);

// 完整配置
builder.Services.AddYData(options =>
{
    options.ConnectionString = builder.Configuration.GetConnectionString("DefaultConnection")!;
    options.DataType = YDataType.Sqlite;
    options.EnableAutoSyncStructure = true;
    options.EnableMonitorCommand = true;
});

var app = builder.Build();
```

### 2. 在服务中使用

```csharp
public class UserService
{
    private readonly IYDataContext _context;
    private readonly ILogger<UserService> _logger;

    public UserService(IYDataContext context, ILogger<UserService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<List<User>> GetActiveUsersAsync()
    {
        return await _context.Select<User>()
            .Where(u => u.IsActive)
            .OrderBy(u => u.Name)
            .ToListAsync();
    }

    public async Task<int> CreateUserAsync(User user)
    {
        return await _context.InsertAsync(user);
    }
}
```

## 🔧 配置选项

```csharp
services.AddYData(options =>
{
    // 基础配置
    options.ConnectionString = "Data Source=test.db";
    options.DataType = YDataType.Sqlite;

    // 性能配置
    options.DefaultQueryTimeout = TimeSpan.FromSeconds(30);
    options.SlowQueryThreshold = TimeSpan.FromSeconds(5);

    // 功能开关
    options.EnableAutoSyncStructure = true;
    options.EnableMonitorCommand = true;
    options.EnableValidation = true;

    // 日志配置
    options.EnableSqlLogging = true;
    options.LogLevel = LogLevel.Information;
});
```

## 📊 数据验证

```csharp
// 启用验证
services.AddYData(options =>
{
    options.EnableValidation = true;
});

// 使用验证扩展
var user = new User { Name = "", Email = "invalid-email", Age = -1 };

// 验证实体
var validationResult = user.YValidate();
if (!validationResult.IsValid)
{
    foreach (var error in validationResult.Errors)
    {
        Console.WriteLine($"字段 {error.PropertyName}: {error.ErrorMessage}");
    }
}

// 验证并插入
try
{
    await user.YValidateAndInsertAsync();
}
catch (YValidationException ex)
{
    Console.WriteLine($"验证失败: {ex.Message}");
}
```

## 🧪 测试覆盖

Zylo.YData 拥有完整的测试覆盖，确保代码质量和稳定性：

- **基础功能测试**: CRUD 操作、查询功能
- **扩展方法测试**: 分页、批量操作、条件查询
- **多数据库测试**: 数据库注册、切换、并行操作
- **依赖注入测试**: 服务注册、配置选项
- **验证功能测试**: 数据验证、错误处理
- **批量操作测试**: 批量插入、更新、删除

**测试统计**: 93 个测试用例，99% 通过率

## 📁 项目结构

```text
Zylo.YData/
├── YData.cs                    # 静态 API 入口
├── YDataContext.cs             # 核心数据上下文
├── YDataManager.cs             # 多数据库管理器
├── YDataSwitcher.cs            # 数据库切换器
├── Extensions/                 # 扩展方法
│   ├── YDataExtensions.cs      # 核心扩展方法
│   ├── YMultiTableExtensions.cs # 多表操作扩展
│   └── YValidationExtensions.cs # 验证扩展
├── Models/                     # 数据模型
├── Examples/                   # 使用示例
└── Tests/                      # 内置测试
```

## 📊 智能默认配置

YData 会根据数据库类型自动应用最佳配置：

- **SQLite**: 启用自动同步结构，使用下划线命名
- **SQL Server**: 使用 PascalCase 命名
- **MySQL/PostgreSQL**: 使用下划线命名

## 🔗 相关项目

- [Zylo.Core](../Zylo.Core) - 核心基础库
- [Zylo.YIO](../Zylo.YIO) - 文件 I/O 操作
- [Zylo.AutoG](../Zylo.AutoG) - 代码生成工具
- [FreeSql](https://freesql.net/) - 底层 ORM 框架

## 📄 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如有问题或建议，请通过以下方式联系：

- 提交 [Issue](../../issues)
- 查看 [文档](./Documentation)
- 参考 [示例代码](./Examples)

---

**🎉 Zylo.YData 已完成核心功能开发！** 现在您可以开始使用 Zylo.YData 进行现代化的数据访问操作了。
