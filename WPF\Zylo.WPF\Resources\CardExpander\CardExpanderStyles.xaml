<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!--  🎨 Zylo CardExpander 样式库 - 基于 WPF-UI 官方样式扩展  -->

    <!--  =========================================  -->
    <!--  基础样式 - 继承 WPF-UI 默认样式  -->
    <!--  =========================================  -->

    <!--  基础卡片样式 - 极简设计  -->
    <Style TargetType="ui:CardExpander" x:Key="ZyloCardExpanderBaseStyle">
        <!--  极简无边框设计，只有背景色  -->
        <Setter Property="Margin" Value="0,0,0,16" />
        <Setter Property="Padding" Value="16" />
        <Setter Property="IsExpanded" Value="True" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Background" Value="{DynamicResource CardBackgroundFillColorDefaultBrush}" />
    </Style>

    <!--  =========================================  -->
    <!--  扩展样式变体  -->
    <!--  =========================================  -->

    <!--  标准卡片样式  -->
    <Style
        BasedOn="{StaticResource ZyloCardExpanderBaseStyle}"
        TargetType="ui:CardExpander"
        x:Key="ZyloCardStyle">
    </Style>

    <!--  紧凑卡片样式  -->
    <Style
        BasedOn="{StaticResource ZyloCardExpanderBaseStyle}"
        TargetType="ui:CardExpander"
        x:Key="ZyloCompactCardStyle">
        <Setter Property="Margin" Value="0,0,0,8" />
        <Setter Property="Padding" Value="12" />
    </Style>

    <!--  大型卡片样式  -->
    <Style
        BasedOn="{StaticResource ZyloCardExpanderBaseStyle}"
        TargetType="ui:CardExpander"
        x:Key="ZyloLargeCardStyle">
        <Setter Property="Margin" Value="0,0,0,24" />
        <Setter Property="Padding" Value="24" />
    </Style>

    <!--  =========================================  -->
    <!--  特殊用途样式  -->
    <!--  =========================================  -->

    <!--  信息卡片样式  -->
    <Style
        BasedOn="{StaticResource ZyloCardExpanderBaseStyle}"
        TargetType="ui:CardExpander"
        x:Key="ZyloInfoCardStyle">

    </Style>

    <!--  成功卡片样式  -->
    <Style
        BasedOn="{StaticResource ZyloCardExpanderBaseStyle}"
        TargetType="ui:CardExpander"
        x:Key="ZyloSuccessCardStyle">

    </Style>

    <!--  警告卡片样式  -->
    <Style
        BasedOn="{StaticResource ZyloCardExpanderBaseStyle}"
        TargetType="ui:CardExpander"
        x:Key="ZyloWarningCardStyle">

    </Style>

    <!--  错误卡片样式  -->
    <Style
        BasedOn="{StaticResource ZyloCardExpanderBaseStyle}"
        TargetType="ui:CardExpander"
        x:Key="ZyloErrorCardStyle">

    </Style>

    <!--  =========================================  -->
    <!--  代码示例专用样式  -->
    <!--  =========================================  -->

    <!--  代码示例卡片样式  -->
    <Style
        BasedOn="{StaticResource ZyloCardExpanderBaseStyle}"
        TargetType="ui:CardExpander"
        x:Key="ZyloCodeExampleCardStyle">
        <Setter Property="Padding" Value="20" />
        <Setter Property="Margin" Value="0,0,0,20" />
        <Setter Property="FontFamily" Value="Consolas, 'Courier New', monospace" />
    </Style>

    <!--  演示卡片样式  -->
    <Style
        BasedOn="{StaticResource ZyloCardExpanderBaseStyle}"
        TargetType="ui:CardExpander"
        x:Key="ZyloDemoCardStyle">
    </Style>

    <!--  =========================================  -->
    <!--  兼容性别名  -->
    <!--  =========================================  -->

    <!--  保持向后兼容的原始样式名称  -->
    <Style
        BasedOn="{StaticResource ZyloCardStyle}"
        TargetType="ui:CardExpander"
        x:Key="YCardStyle">
        <!--  别名，指向新的标准样式  -->
    </Style>

    <!--  默认演示样式 - 用于示例页面  -->
    <Style
        BasedOn="{StaticResource ZyloDemoCardStyle}"
        TargetType="ui:CardExpander"
        x:Key="DemoCardStyle">
        <!--  别名，指向演示卡片样式  -->
    </Style>

</ResourceDictionary>
