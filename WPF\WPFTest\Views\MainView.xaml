﻿<ui:FluentWindow
    ExtendsContentIntoTitleBar="True"
    Height="1000"
    Title="WPF-UI + Prism 导航测试"
    Width="1400"
    WindowBackdropType="Mica"
    WindowStartupLocation="CenterScreen"
    d:DataContext="{d:DesignInstance viewModels:MainViewModel}"
    mc:Ignorable="d"
    prism:ViewModelLocator.AutoWireViewModel="True"
    x:Class="WPFTest.Views.MainView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:ext="clr-namespace:Zylo.WPF.YPrism;assembly=Zylo.WPF"
    xmlns:local="clr-namespace:WPFTest"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:navigation="clr-namespace:Zylo.WPF.Controls.Navigation;assembly=Zylo.WPF"
    xmlns:prism="http://prismlibrary.com/"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:viewModels="clr-namespace:WPFTest.ViewModels"
    xmlns:views="clr-namespace:WPFTest.Views"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">


    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!--  ContentDialog 支持  -->
        <ContentPresenter
            Grid.RowSpan="2"
            Panel.ZIndex="999"
            x:Name="RootContentDialogPresenter" />

        <!--  🎯 标题栏  -->
        <ui:TitleBar
            Grid.Row="0"
            Height="35"
            Title="WPF-UI 测试应用">
            <ui:TitleBar.Header>
                <DockPanel>
                    <Button
                        Command="{Binding GoBackCommand}"
                        DockPanel.Dock="Left"
                        Style="{StaticResource NavigationButtonStyle}"
                        Visibility="{Binding ShowToggleButton, ElementName=NavigationControlRoot, Converter={StaticResource BooleanToVisibilityConverter}}"
                        Width="48">
                        <ui:SymbolIcon
                            FontSize="20"
                            Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                            Symbol="ArrowLeft20" />
                    </Button>
                </DockPanel>
            </ui:TitleBar.Header>
        </ui:TitleBar>
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <!--  🎯 NavigationControl 完整功能测试  -->
            <navigation:NavigationControl
                BottomNavigationItems="{Binding DnavigationItems}"
                DefaultPosition="Left"
                EnableDragDrop="True"
                EnableResponsiveLayout="{Binding EnableResponsiveLayout}"
                Grid.Column="0"
                HighlightSearchResults="{Binding HighlightSearchMatches}"
                ItemContextMenuCommand="{Binding NavigationItemContextMenuCommand}"
                ItemDoubleClickCommand="{Binding NavigationItemDoubleClickCommand}"
                NavigationItemSelectedCommand="{Binding NavigateToItemCommand}"
                NavigationToggleCommand="{Binding NavigationToggleCommand}"
                RightColumnWidth="250"
                SearchText="{Binding NavigationSearchText, Mode=TwoWay}"
                SearchTextChangedCommand="{Binding SearchTextChangedCommand}"
                SelectedListItem="{Binding ZyloNavigationItemT, Mode=TwoWay}"
                ShowSearchBox="{Binding ShowNavigationSearch}"
                TopNavigationItems="{Binding TnavigationItems}"
                TreeViewColumnWidth="{Binding NavigationTreeViewColumnWidth, Mode=TwoWay}"
                x:Name="NavigationSidebar" />

            <!--  🎯 主内容区域 - 显示选中的内容  -->
            <Border Grid.Column="1" Style="{DynamicResource YBorderStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <!--  🎮 NavigationControl 功能测试面板  -->
                    <Expander
                        Grid.Row="0"
                        Header="🎮 NavigationControl 功能测试"
                        IsExpanded="True"
                        Margin="8"
                        Visibility="Collapsed">
                        <StackPanel Margin="8">
                            <!--  基础信息显示  -->
                            <TextBlock
                                FontWeight="Bold"
                                Margin="0,0,0,8"
                                Text="📊 当前状态信息" />
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                    <TextBlock Text="{Binding ZyloNavigationItemT.Name, StringFormat='选中项: {0}'}" />
                                    <TextBlock Text="{Binding NavigationSearchText, StringFormat='搜索文本: {0}'}" />
                                    <TextBlock Text="{Binding TnavigationItems.Count, StringFormat='顶部项数: {0}'}" />
                                </StackPanel>

                                <StackPanel Grid.Column="1">
                                    <TextBlock Text="{Binding DnavigationItems.Count, StringFormat='底部项数: {0}'}" />
                                    <TextBlock Text="{Binding ShowNavigationSearch, StringFormat='显示搜索: {0}'}" />
                                    <TextBlock Text="{Binding EnableResponsiveLayout, StringFormat='响应式: {0}'}" />
                                </StackPanel>
                            </Grid>

                            <!--  功能测试按钮  -->
                            <TextBlock
                                FontWeight="Bold"
                                Margin="0,16,0,8"
                                Text="🎯 功能测试" />
                            <WrapPanel>
                                <Button
                                    Command="{Binding RefreshNavigationItemsCommand}"
                                    Content="🔄 刷新导航项"
                                    Margin="4" />
                                <Button
                                    Command="{Binding ClearSearchCommand}"
                                    Content="🧹 清空搜索"
                                    Margin="4" />
                                <Button
                                    Command="{Binding GoBackCommand}"
                                    Content="⬅️ 后退"
                                    Margin="4" />
                                <Button
                                    Command="{Binding GoForwardCommand}"
                                    Content="➡️ 前进"
                                    Margin="4" />
                            </WrapPanel>

                            <!--  导航项管理  -->
                            <TextBlock
                                FontWeight="Bold"
                                Margin="0,16,0,8"
                                Text="📋 导航项管理" />
                            <WrapPanel>
                                <Button
                                    Command="{Binding AddTestNavigationItemCommand}"
                                    Content="➕ 添加测试项"
                                    Margin="4" />
                                <Button
                                    Command="{Binding RemoveLastNavigationItemCommand}"
                                    Content="➖ 移除最后项"
                                    Margin="4" />
                                <Button
                                    Command="{Binding ExpandAllNavigationItemsCommand}"
                                    Content="📂 展开所有"
                                    Margin="4" />
                                <Button
                                    Command="{Binding CollapseAllNavigationItemsCommand}"
                                    Content="📁 折叠所有"
                                    Margin="4" />
                            </WrapPanel>

                            <!--  设置控制  -->
                            <TextBlock
                                FontWeight="Bold"
                                Margin="0,16,0,8"
                                Text="⚙️ 设置控制" />
                            <WrapPanel>
                                <CheckBox
                                    Content="显示搜索框"
                                    IsChecked="{Binding ShowNavigationSearch}"
                                    Margin="4" />
                                <CheckBox
                                    Content="高亮搜索结果"
                                    IsChecked="{Binding HighlightSearchMatches}"
                                    Margin="4" />
                                <CheckBox
                                    Content="响应式布局"
                                    IsChecked="{Binding EnableResponsiveLayout}"
                                    Margin="4" />
                            </WrapPanel>

                            <!--  搜索测试  -->
                            <TextBlock
                                FontWeight="Bold"
                                Margin="0,16,0,8"
                                Text="🔍 搜索测试" />
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <ui:TextBox
                                    Grid.Column="0"
                                    Margin="0,0,8,0"
                                    PlaceholderText="输入搜索关键词..."
                                    Text="{Binding NavigationSearchText, UpdateSourceTrigger=PropertyChanged}" />
                                <Button
                                    Command="{Binding ClearSearchCommand}"
                                    Content="🧹"
                                    Grid.Column="1"
                                    ToolTip="清空搜索"
                                    Width="30" />
                            </Grid>
                        </StackPanel>
                    </Expander>

                    <!--  Prism 区域 (主要内容显示区域)  -->
                    <ContentControl
                        AllowDrop="True"
                        Grid.Row="1"
                        Margin="5"
                        Visibility="Visible"
                        prism:RegionManager.RegionName="{x:Static ext:PrismManager.MainViewRegionName}"
                        x:Name="PrismMainViewRegionName" />
                </Grid>
            </Border>
        </Grid>

        <!--  🖱️ 四个角的拖拽区域 - 设置最高层级  -->
        <!--  左上角  -->
        <Border
            Background="Transparent"
            Cursor="SizeAll"
            Grid.Row="0"
            Grid.RowSpan="2"
            Height="5"
            HorizontalAlignment="Left"
            MouseLeftButtonDown="CornerDrag_MouseLeftButtonDown"
            Panel.ZIndex="1000"
            ToolTip="拖拽移动窗口"
            VerticalAlignment="Top"
            Width="5" />

        <!--  右上角  -->
        <Border
            Background="Transparent"
            Cursor="SizeAll"
            Grid.Row="0"
            Grid.RowSpan="2"
            Height="5"
            HorizontalAlignment="Right"
            MouseLeftButtonDown="CornerDrag_MouseLeftButtonDown"
            Panel.ZIndex="1000"
            ToolTip="拖拽移动窗口"
            VerticalAlignment="Top"
            Width="5" />

        <!--  左下角  -->
        <Border
            Background="Transparent"
            Cursor="SizeAll"
            Grid.Row="0"
            Grid.RowSpan="2"
            Height="5"
            HorizontalAlignment="Left"
            MouseLeftButtonDown="CornerDrag_MouseLeftButtonDown"
            Panel.ZIndex="1000"
            ToolTip="拖拽移动窗口"
            VerticalAlignment="Bottom"
            Width="5" />

        <!--  右下角  -->
        <Border
            Background="Transparent"
            Cursor="SizeAll"
            Grid.Row="0"
            Grid.RowSpan="2"
            Height="500"
            HorizontalAlignment="Right"
            MouseLeftButtonDown="CornerDrag_MouseLeftButtonDown"
            Panel.ZIndex="1000"
            ToolTip="拖拽移动窗口"
            VerticalAlignment="Bottom"
            Width="10" />

    </Grid>

</ui:FluentWindow>
