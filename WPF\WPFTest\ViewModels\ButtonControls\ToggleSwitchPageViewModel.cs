using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.IO;
using System.Reflection;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels.ButtonControls
{
    /// <summary>
    /// ToggleSwitch 页面的 ViewModel，演示各种开关控件功能
    /// </summary>
    public partial class ToggleSwitchPageViewModel : ObservableObject
    {
        private readonly YLoggerInstance _logger = YLogger.For<ToggleSwitchPageViewModel>();

        #region 属性

        /// <summary>
        /// 开关交互次数
        /// </summary>
        [ObservableProperty]
        private int toggleCount = 0;

        /// <summary>
        /// 最后执行的操作
        /// </summary>
        [ObservableProperty]
        private string lastAction = "无操作";

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        private string statusMessage = "欢迎使用 ToggleSwitch 开关控件示例！切换任意开关查看效果。";

        #region 开关状态属性

        /// <summary>
        /// 通知开关状态
        /// </summary>
        [ObservableProperty]
        private bool isNotificationEnabled = false;

        /// <summary>
        /// 深色模式开关状态
        /// </summary>
        [ObservableProperty]
        private bool isDarkModeEnabled = false;

        /// <summary>
        /// 自动保存开关状态
        /// </summary>
        [ObservableProperty]
        private bool isAutoSaveEnabled = true;

        /// <summary>
        /// 邮件通知开关状态
        /// </summary>
        [ObservableProperty]
        private bool isEmailNotificationEnabled = false;

        /// <summary>
        /// 声音提醒开关状态
        /// </summary>
        [ObservableProperty]
        private bool isSoundNotificationEnabled = true;

        /// <summary>
        /// 自动更新开关状态
        /// </summary>
        [ObservableProperty]
        private bool isAutoUpdateEnabled = true;

        #endregion

        #region 代码示例属性

        /// <summary>
        /// 基础 ToggleSwitch XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicToggleSwitchXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 基础 ToggleSwitch C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicToggleSwitchCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 带标签 ToggleSwitch XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string LabeledToggleSwitchXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 带标签 ToggleSwitch C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string LabeledToggleSwitchCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 自定义 ToggleSwitch XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string CustomToggleSwitchXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 自定义 ToggleSwitch C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string CustomToggleSwitchCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 应用场景 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string ApplicationScenarioXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 应用场景 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string ApplicationScenarioCSharpExample { get; set; } = string.Empty;

        #endregion

        #endregion

        #region 构造函数

        public ToggleSwitchPageViewModel()
        {
            StatusMessage = "ToggleSwitch 示例库已加载，开始体验各种开关功能！";
            LoadCodeExamples();
            _logger.Info("ToggleSwitchPageViewModel 初始化完成");
        }

        #endregion

        #region 命令

        /// <summary>
        /// 通用开关切换命令
        /// </summary>
        /// <param name="parameter">开关参数</param>
        [RelayCommand]
        private void HandleToggleSwitch(string? parameter)
        {
            ToggleCount++;
            LastAction = parameter ?? "未知开关";

            // 根据不同的开关类型显示不同的状态消息
            StatusMessage = parameter switch
            {
                "通知" => $"🔔 通知功能: {(IsNotificationEnabled ? "已启用" : "已禁用")}",
                "深色模式" => $"🌙 深色模式: {(IsDarkModeEnabled ? "已启用" : "已禁用")}",
                "自动保存" => $"💾 自动保存: {(IsAutoSaveEnabled ? "已启用" : "已禁用")}",
                "邮件通知" => $"📧 邮件通知: {(IsEmailNotificationEnabled ? "已启用" : "已禁用")}",
                "声音提醒" => $"🔊 声音提醒: {(IsSoundNotificationEnabled ? "已启用" : "已禁用")}",
                "自动更新" => $"🔄 自动更新: {(IsAutoUpdateEnabled ? "已启用" : "已禁用")}",
                "基础开关" => "🔘 基础开关状态已切换",
                "小尺寸" => "🔸 小尺寸开关状态已切换",
                "标准尺寸" => "🔘 标准尺寸开关状态已切换",
                "大尺寸" => "🔷 大尺寸开关状态已切换",
                _ => $"🔄 开关切换: {parameter}"
            };

            _logger.Info($"开关切换: {parameter}, 总计数: {ToggleCount}");
        }

        /// <summary>
        /// 重置计数命令
        /// </summary>
        [RelayCommand]
        private void ResetCount()
        {
            ToggleCount = 0;
            StatusMessage = "计数已重置";
            _logger.Info("重置了开关切换计数");
        }

        /// <summary>
        /// 全部启用命令
        /// </summary>
        [RelayCommand]
        private void EnableAll()
        {
            IsNotificationEnabled = true;
            IsDarkModeEnabled = true;
            IsAutoSaveEnabled = true;
            IsEmailNotificationEnabled = true;
            IsSoundNotificationEnabled = true;
            IsAutoUpdateEnabled = true;

            StatusMessage = "✅ 已启用所有开关功能";
            _logger.Info("启用了所有开关");
        }

        /// <summary>
        /// 全部禁用命令
        /// </summary>
        [RelayCommand]
        private void DisableAll()
        {
            IsNotificationEnabled = false;
            IsDarkModeEnabled = false;
            IsAutoSaveEnabled = false;
            IsEmailNotificationEnabled = false;
            IsSoundNotificationEnabled = false;
            IsAutoUpdateEnabled = false;

            StatusMessage = "❌ 已禁用所有开关功能";
            _logger.Info("禁用了所有开关");
        }

        #endregion

        #region 方法

        /// <summary>
        /// 加载代码示例
        /// </summary>
        private void LoadCodeExamples()
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                var basePath = Path.GetDirectoryName(assembly.Location) ?? "";
                var examplesPath = Path.Combine(basePath, "CodeExamples", "ToggleSwitch");

                // 基础 ToggleSwitch 示例
                BasicToggleSwitchXamlExample = LoadCodeExample(examplesPath, "BasicToggleSwitch.xaml") ?? 
                    GetDefaultBasicToggleSwitchXamlExample();
                BasicToggleSwitchCSharpExample = LoadCodeExample(examplesPath, "BasicToggleSwitch.cs") ?? 
                    GetDefaultBasicToggleSwitchCSharpExample();

                // 带标签 ToggleSwitch 示例
                LabeledToggleSwitchXamlExample = LoadCodeExample(examplesPath, "LabeledToggleSwitch.xaml") ?? 
                    GetDefaultLabeledToggleSwitchXamlExample();
                LabeledToggleSwitchCSharpExample = LoadCodeExample(examplesPath, "LabeledToggleSwitch.cs") ?? 
                    GetDefaultLabeledToggleSwitchCSharpExample();

                // 自定义 ToggleSwitch 示例
                CustomToggleSwitchXamlExample = LoadCodeExample(examplesPath, "CustomToggleSwitch.xaml") ??
                    GetDefaultCustomToggleSwitchXamlExample();
                CustomToggleSwitchCSharpExample = LoadCodeExample(examplesPath, "CustomToggleSwitch.cs") ??
                    GetDefaultCustomToggleSwitchCSharpExample();

                // 应用场景示例
                ApplicationScenarioXamlExample = LoadCodeExample(examplesPath, "ApplicationScenario.xaml") ??
                    GetDefaultApplicationScenarioXamlExample();
                ApplicationScenarioCSharpExample = LoadCodeExample(examplesPath, "ApplicationScenario.cs") ??
                    GetDefaultApplicationScenarioCSharpExample();

                _logger.Info("代码示例加载完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"加载代码示例失败: {ex.Message}");
                LoadDefaultExamples();
            }
        }

        /// <summary>
        /// 从文件加载代码示例
        /// </summary>
        private string? LoadCodeExample(string basePath, string fileName)
        {
            try
            {
                var filePath = Path.Combine(basePath, fileName);
                return File.Exists(filePath) ? File.ReadAllText(filePath) : null;
            }
            catch (Exception ex)
            {
                _logger.Warning($"无法加载代码示例文件 {fileName}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 加载默认示例
        /// </summary>
        private void LoadDefaultExamples()
        {
            BasicToggleSwitchXamlExample = GetDefaultBasicToggleSwitchXamlExample();
            BasicToggleSwitchCSharpExample = GetDefaultBasicToggleSwitchCSharpExample();
            LabeledToggleSwitchXamlExample = GetDefaultLabeledToggleSwitchXamlExample();
            LabeledToggleSwitchCSharpExample = GetDefaultLabeledToggleSwitchCSharpExample();
            CustomToggleSwitchXamlExample = GetDefaultCustomToggleSwitchXamlExample();
            CustomToggleSwitchCSharpExample = GetDefaultCustomToggleSwitchCSharpExample();
            ApplicationScenarioXamlExample = GetDefaultApplicationScenarioXamlExample();
            ApplicationScenarioCSharpExample = GetDefaultApplicationScenarioCSharpExample();
        }

        #endregion

        #region 默认代码示例

        private string GetDefaultBasicToggleSwitchXamlExample()
        {
            return @"
<!-- 基础 ToggleSwitch 示例 - MVVM 模式 -->
<ui:ToggleSwitch Command=""{Binding HandleToggleSwitchCommand}""
                 CommandParameter=""基础开关""/>

<ui:ToggleSwitch IsChecked=""True""
                 Command=""{Binding HandleToggleSwitchCommand}""
                 CommandParameter=""预设开关""/>

<ui:ToggleSwitch IsEnabled=""False""
                 IsChecked=""True""/>";
        }

        private string GetDefaultBasicToggleSwitchCSharpExample()
        {
            return @"
// 基础 ToggleSwitch MVVM 命令处理
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

public partial class ToggleSwitchPageViewModel : ObservableObject
{
    /// <summary>
    /// 开关切换次数
    /// </summary>
    [ObservableProperty]
    private int toggleCount = 0;

    /// <summary>
    /// 通用开关切换命令
    /// </summary>
    [RelayCommand]
    private void HandleToggleSwitch(string? parameter)
    {
        ToggleCount++;
        StatusMessage = parameter switch
        {
            ""基础开关"" => ""🔘 基础开关状态已切换"",
            ""预设开关"" => ""🔘 预设开关状态已切换"",
            _ => $""🔄 开关切换: {parameter}""
        };
    }
}";
        }

        private string GetDefaultLabeledToggleSwitchXamlExample()
        {
            return @"
<!-- 带标签的 ToggleSwitch 示例 - MVVM 双向绑定 -->
<StackPanel Orientation=""Horizontal"">
    <ui:ToggleSwitch IsChecked=""{Binding IsNotificationEnabled}""
                     Command=""{Binding HandleToggleSwitchCommand}""
                     CommandParameter=""通知""
                     Margin=""0,0,12,0""/>
    <TextBlock Text=""启用通知"" VerticalAlignment=""Center""/>
</StackPanel>

<StackPanel Orientation=""Horizontal"">
    <ui:ToggleSwitch IsChecked=""{Binding IsDarkModeEnabled}""
                     Command=""{Binding HandleToggleSwitchCommand}""
                     CommandParameter=""深色模式""
                     Margin=""0,0,12,0""/>
    <TextBlock Text=""深色模式"" VerticalAlignment=""Center""/>
</StackPanel>";
        }

        private string GetDefaultLabeledToggleSwitchCSharpExample()
        {
            return @"
// 带标签 ToggleSwitch MVVM 双向绑定示例
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

public partial class ToggleSwitchPageViewModel : ObservableObject
{
    /// <summary>
    /// 通知开关状态 - 双向绑定属性
    /// </summary>
    [ObservableProperty]
    private bool isNotificationEnabled = false;

    /// <summary>
    /// 深色模式开关状态 - 双向绑定属性
    /// </summary>
    [ObservableProperty]
    private bool isDarkModeEnabled = false;

    /// <summary>
    /// 状态消息
    /// </summary>
    [ObservableProperty]
    private string statusMessage = ""准备就绪"";

    /// <summary>
    /// 通用开关切换命令
    /// </summary>
    [RelayCommand]
    private void HandleToggleSwitch(string? parameter)
    {
        // 根据参数更新状态消息
        StatusMessage = parameter switch
        {
            ""通知"" => $""🔔 通知功能: {(IsNotificationEnabled ? ""已启用"" : ""已禁用"")}"",
            ""深色模式"" => $""🌙 深色模式: {(IsDarkModeEnabled ? ""已启用"" : ""已禁用"")}"",
            _ => $""🔄 开关切换: {parameter}""
        };

        // 可以在这里添加具体的业务逻辑
        if (parameter == ""通知"" && IsNotificationEnabled)
        {
            EnableNotifications();
        }
        else if (parameter == ""深色模式"" && IsDarkModeEnabled)
        {
            ApplyDarkTheme();
        }
    }

    private void EnableNotifications()
    {
        // 实际的通知启用逻辑
        // 例如：注册推送通知服务
    }

    private void ApplyDarkTheme()
    {
        // 实际的主题切换逻辑
        // 例如：更新应用程序主题资源
    }
}";
        }

        private string GetDefaultCustomToggleSwitchXamlExample()
        {
            return @"
<!-- 自定义尺寸的 ToggleSwitch 示例 - MVVM 命令绑定 -->
<!-- 小尺寸开关 -->
<StackPanel Orientation=""Horizontal"">
    <ui:ToggleSwitch Width=""40"" Height=""20""
                     Command=""{Binding HandleToggleSwitchCommand}""
                     CommandParameter=""小尺寸""
                     Margin=""0,0,12,0""/>
    <TextBlock Text=""小尺寸开关"" VerticalAlignment=""Center""/>
</StackPanel>

<!-- 标准尺寸开关 -->
<StackPanel Orientation=""Horizontal"">
    <ui:ToggleSwitch Command=""{Binding HandleToggleSwitchCommand}""
                     CommandParameter=""标准尺寸""
                     Margin=""0,0,12,0""/>
    <TextBlock Text=""标准尺寸开关"" VerticalAlignment=""Center""/>
</StackPanel>

<!-- 大尺寸开关 -->
<StackPanel Orientation=""Horizontal"">
    <ui:ToggleSwitch Width=""60"" Height=""30""
                     Command=""{Binding HandleToggleSwitchCommand}""
                     CommandParameter=""大尺寸""
                     Margin=""0,0,12,0""/>
    <TextBlock Text=""大尺寸开关"" VerticalAlignment=""Center""/>
</StackPanel>

<!-- 带样式的开关 -->
<StackPanel Orientation=""Horizontal"">
    <ui:ToggleSwitch Command=""{Binding HandleToggleSwitchCommand}""
                     CommandParameter=""自定义样式""
                     Foreground=""Blue""
                     Background=""LightGray""
                     Margin=""0,0,12,0""/>
    <TextBlock Text=""自定义样式开关"" VerticalAlignment=""Center""/>
</StackPanel>";
        }

        private string GetDefaultCustomToggleSwitchCSharpExample()
        {
            return @"
// 自定义 ToggleSwitch MVVM 命令处理示例
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

public partial class ToggleSwitchPageViewModel : ObservableObject
{
    /// <summary>
    /// 开关切换次数统计
    /// </summary>
    [ObservableProperty]
    private int toggleCount = 0;

    /// <summary>
    /// 最后操作的开关类型
    /// </summary>
    [ObservableProperty]
    private string lastAction = ""无操作"";

    /// <summary>
    /// 通用开关切换命令 - 支持不同尺寸和样式
    /// </summary>
    [RelayCommand]
    private void HandleToggleSwitch(string? parameter)
    {
        ToggleCount++;
        LastAction = parameter ?? ""未知开关"";

        // 根据不同的开关类型显示不同的状态消息和执行不同的逻辑
        StatusMessage = parameter switch
        {
            ""小尺寸"" => ""🔸 小尺寸开关状态已切换"",
            ""标准尺寸"" => ""🔘 标准尺寸开关状态已切换"",
            ""大尺寸"" => ""🔷 大尺寸开关状态已切换"",
            ""自定义样式"" => ""🎨 自定义样式开关状态已切换"",
            _ => $""🔄 开关切换: {parameter}""
        };

        // 可以根据开关类型执行特定的业务逻辑
        switch (parameter)
        {
            case ""小尺寸"":
                HandleSmallToggle();
                break;
            case ""大尺寸"":
                HandleLargeToggle();
                break;
            case ""自定义样式"":
                HandleCustomStyleToggle();
                break;
        }
    }

    private void HandleSmallToggle()
    {
        // 小尺寸开关的特定逻辑
        // 例如：紧凑模式设置
    }

    private void HandleLargeToggle()
    {
        // 大尺寸开关的特定逻辑
        // 例如：无障碍模式设置
    }

    private void HandleCustomStyleToggle()
    {
        // 自定义样式开关的特定逻辑
        // 例如：主题自定义设置
    }
}";
        }

        private string GetDefaultApplicationScenarioXamlExample()
        {
            return @"
<!-- 实际应用场景 MVVM 示例 -->
<!-- 设置面板中的开关控件 -->
<StackPanel>
    <!-- 通知设置区域 -->
    <GroupBox Header=""通知设置"" Margin=""0,0,0,16"">
        <StackPanel>
            <StackPanel Orientation=""Horizontal"" Margin=""0,0,0,8"">
                <ui:ToggleSwitch IsChecked=""{Binding IsEmailNotificationEnabled}""
                               Command=""{Binding HandleToggleSwitchCommand}""
                               CommandParameter=""邮件通知""
                               Margin=""0,0,12,0""/>
                <StackPanel>
                    <TextBlock Text=""邮件通知"" FontWeight=""Medium""/>
                    <TextBlock Text=""接收重要邮件提醒"" FontSize=""11""
                             Foreground=""Gray""/>
                </StackPanel>
            </StackPanel>

            <StackPanel Orientation=""Horizontal"">
                <ui:ToggleSwitch IsChecked=""{Binding IsSoundNotificationEnabled}""
                               Command=""{Binding HandleToggleSwitchCommand}""
                               CommandParameter=""声音提醒""
                               Margin=""0,0,12,0""/>
                <StackPanel>
                    <TextBlock Text=""声音提醒"" FontWeight=""Medium""/>
                    <TextBlock Text=""播放通知声音"" FontSize=""11""
                             Foreground=""Gray""/>
                </StackPanel>
            </StackPanel>
        </StackPanel>
    </GroupBox>

    <!-- 系统设置区域 -->
    <GroupBox Header=""系统设置"">
        <StackPanel Orientation=""Horizontal"">
            <ui:ToggleSwitch IsChecked=""{Binding IsAutoUpdateEnabled}""
                           Command=""{Binding HandleToggleSwitchCommand}""
                           CommandParameter=""自动更新""
                           Margin=""0,0,12,0""/>
            <StackPanel>
                <TextBlock Text=""自动更新"" FontWeight=""Medium""/>
                <TextBlock Text=""自动检查并安装更新"" FontSize=""11""
                         Foreground=""Gray""/>
            </StackPanel>
        </StackPanel>
    </GroupBox>
</StackPanel>";
        }

        private string GetDefaultApplicationScenarioCSharpExample()
        {
            return @"
// 实际应用场景 MVVM 完整示例
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Configuration;

public partial class ApplicationSettingsViewModel : ObservableObject
{
    #region 属性 - 双向绑定开关状态

    [ObservableProperty]
    private bool isEmailNotificationEnabled = false;

    [ObservableProperty]
    private bool isSoundNotificationEnabled = true;

    [ObservableProperty]
    private bool isAutoUpdateEnabled = true;

    [ObservableProperty]
    private string statusMessage = ""设置已准备就绪"";

    #endregion

    #region 命令

    /// <summary>
    /// 处理所有开关切换的统一命令
    /// </summary>
    [RelayCommand]
    private async Task HandleToggleSwitch(string? parameter)
    {
        try
        {
            // 更新状态消息
            StatusMessage = parameter switch
            {
                ""邮件通知"" => $""📧 邮件通知: {(IsEmailNotificationEnabled ? ""已启用"" : ""已禁用"")}"",
                ""声音提醒"" => $""🔊 声音提醒: {(IsSoundNotificationEnabled ? ""已启用"" : ""已禁用"")}"",
                ""自动更新"" => $""🔄 自动更新: {(IsAutoUpdateEnabled ? ""已启用"" : ""已禁用"")}"",
                _ => $""设置已更新: {parameter}""
            };

            // 执行具体的业务逻辑
            await ExecuteSettingChange(parameter);

            // 保存设置到配置文件
            await SaveSettingsAsync();
        }
        catch (Exception ex)
        {
            StatusMessage = $""设置更新失败: {ex.Message}"";
        }
    }

    #endregion

    #region 私有方法

    private async Task ExecuteSettingChange(string? setting)
    {
        switch (setting)
        {
            case ""邮件通知"":
                if (IsEmailNotificationEnabled)
                {
                    await EnableEmailNotifications();
                }
                else
                {
                    await DisableEmailNotifications();
                }
                break;

            case ""声音提醒"":
                ConfigureSoundNotifications(IsSoundNotificationEnabled);
                break;

            case ""自动更新"":
                ConfigureAutoUpdate(IsAutoUpdateEnabled);
                break;
        }
    }

    private async Task EnableEmailNotifications()
    {
        // 启用邮件通知的具体逻辑
        // 例如：注册邮件服务、验证邮箱等
        await Task.Delay(100); // 模拟异步操作
    }

    private async Task DisableEmailNotifications()
    {
        // 禁用邮件通知的具体逻辑
        await Task.Delay(100); // 模拟异步操作
    }

    private void ConfigureSoundNotifications(bool enabled)
    {
        // 配置声音通知
        // 例如：设置系统音量、选择提示音等
    }

    private void ConfigureAutoUpdate(bool enabled)
    {
        // 配置自动更新
        // 例如：设置更新检查间隔、下载策略等
    }

    private async Task SaveSettingsAsync()
    {
        // 保存设置到配置文件或数据库
        var config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);
        config.AppSettings.Settings[""EmailNotificationEnabled""].Value = IsEmailNotificationEnabled.ToString();
        config.AppSettings.Settings[""SoundNotificationEnabled""].Value = IsSoundNotificationEnabled.ToString();
        config.AppSettings.Settings[""AutoUpdateEnabled""].Value = IsAutoUpdateEnabled.ToString();
        config.Save();

        await Task.CompletedTask;
    }

    #endregion
}";
        }

        #endregion
    }
}
