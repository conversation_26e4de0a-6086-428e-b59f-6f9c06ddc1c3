// SplitButton 高级 C# 示例
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;

namespace WPFTest.ViewModels.ButtonControls
{
    public partial class SplitButtonAdvancedViewModel : ObservableObject
    {
        #region 高级状态属性

        /// <summary>
        /// 是否可以撤销
        /// </summary>
        [ObservableProperty]
        private bool canUndo = false;

        /// <summary>
        /// 是否可以重做
        /// </summary>
        [ObservableProperty]
        private bool canRedo = false;

        /// <summary>
        /// 是否有选择内容
        /// </summary>
        [ObservableProperty]
        private bool hasSelection = false;

        /// <summary>
        /// 是否可以粘贴
        /// </summary>
        [ObservableProperty]
        private bool canPaste = true;

        /// <summary>
        /// 当前视图模式
        /// </summary>
        [ObservableProperty]
        private string currentViewMode = "普通视图";

        /// <summary>
        /// 操作历史记录
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<string> operationHistory = new();

        #endregion

        #region 高级命令

        /// <summary>
        /// 执行撤销操作
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanUndo))]
        private void ExecuteUndo()
        {
            // 执行撤销逻辑
            if (OperationHistory.Count > 0)
            {
                var lastOperation = OperationHistory[^1];
                OperationHistory.RemoveAt(OperationHistory.Count - 1);
                
                // 更新状态
                CanRedo = true;
                CanUndo = OperationHistory.Count > 0;
                
                StatusMessage = $"已撤销操作: {lastOperation}";
            }
        }

        /// <summary>
        /// 执行重做操作
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanRedo))]
        private void ExecuteRedo()
        {
            // 执行重做逻辑
            StatusMessage = "执行了重做操作";
            CanUndo = true;
        }

        /// <summary>
        /// 处理复杂菜单选择
        /// </summary>
        [RelayCommand]
        private void HandleComplexMenuSelection(string parameter)
        {
            // 记录操作历史
            OperationHistory.Add(parameter);
            CanUndo = true;
            
            // 根据不同的操作类型执行相应逻辑
            switch (parameter)
            {
                case "新建文档":
                    CreateNewDocument();
                    break;
                case "新建项目":
                    CreateNewProject();
                    break;
                case "打开文件":
                    OpenFile();
                    break;
                case "保存":
                    SaveDocument();
                    break;
                case "另存为":
                    SaveDocumentAs();
                    break;
                default:
                    HandleGenericOperation(parameter);
                    break;
            }
            
            StatusMessage = $"执行了操作: {parameter}";
            InteractionCount++;
        }

        /// <summary>
        /// 切换视图模式
        /// </summary>
        [RelayCommand]
        private void SwitchViewMode(string viewMode)
        {
            CurrentViewMode = viewMode;
            StatusMessage = $"切换到 {viewMode}";
            
            // 根据视图模式调整界面
            switch (viewMode)
            {
                case "普通视图":
                    // 设置普通视图
                    break;
                case "大纲视图":
                    // 设置大纲视图
                    break;
                case "页面布局":
                    // 设置页面布局视图
                    break;
                case "全屏模式":
                    // 切换到全屏模式
                    break;
            }
        }

        /// <summary>
        /// 处理工具栏操作
        /// </summary>
        [RelayCommand]
        private void HandleToolbarOperation(string operation)
        {
            OperationHistory.Add(operation);
            CanUndo = true;
            
            StatusMessage = $"工具栏操作: {operation}";
            InteractionCount++;
        }

        #endregion

        #region 私有方法

        private void CreateNewDocument()
        {
            // 创建新文档的逻辑
            StatusMessage = "创建了新文档";
        }

        private void CreateNewProject()
        {
            // 创建新项目的逻辑
            StatusMessage = "创建了新项目";
        }

        private void OpenFile()
        {
            // 打开文件的逻辑
            HasSelection = true; // 模拟有内容选择
            StatusMessage = "打开了文件";
        }

        private void SaveDocument()
        {
            // 保存文档的逻辑
            StatusMessage = "文档已保存";
        }

        private void SaveDocumentAs()
        {
            // 另存为的逻辑
            StatusMessage = "文档已另存为";
        }

        private void HandleGenericOperation(string operation)
        {
            // 处理通用操作
            StatusMessage = $"执行了操作: {operation}";
        }

        #endregion

        #region 高级功能示例

        /// <summary>
        /// 动态构建菜单项
        /// </summary>
        public ObservableCollection<MenuItemViewModel> DynamicMenuItems { get; } = new();

        /// <summary>
        /// 初始化动态菜单
        /// </summary>
        private void InitializeDynamicMenu()
        {
            DynamicMenuItems.Clear();
            
            // 根据应用状态动态添加菜单项
            if (CanUndo)
            {
                DynamicMenuItems.Add(new MenuItemViewModel 
                { 
                    Header = "撤销", 
                    Command = ExecuteUndoCommand,
                    Icon = "ArrowUndo24"
                });
            }
            
            if (CanRedo)
            {
                DynamicMenuItems.Add(new MenuItemViewModel 
                { 
                    Header = "重做", 
                    Command = ExecuteRedoCommand,
                    Icon = "ArrowRedo24"
                });
            }
            
            // 添加分隔符
            if (DynamicMenuItems.Count > 0)
            {
                DynamicMenuItems.Add(new MenuItemViewModel { IsSeparator = true });
            }
            
            // 添加其他菜单项
            DynamicMenuItems.Add(new MenuItemViewModel 
            { 
                Header = "设置", 
                Command = HandleComplexMenuSelectionCommand,
                CommandParameter = "设置",
                Icon = "Settings24"
            });
        }

        /// <summary>
        /// 菜单项视图模型
        /// </summary>
        public class MenuItemViewModel
        {
            public string Header { get; set; } = string.Empty;
            public string Icon { get; set; } = string.Empty;
            public IRelayCommand? Command { get; set; }
            public object? CommandParameter { get; set; }
            public bool IsSeparator { get; set; }
            public bool IsEnabled { get; set; } = true;
        }

        #endregion

        #region 最佳实践

        /*
         * SplitButton 高级使用最佳实践：
         * 
         * 1. 状态管理
         *    - 使用属性绑定控制菜单项的启用状态
         *    - 实现 CanExecute 逻辑控制命令可用性
         *    - 维护操作历史记录支持撤销/重做
         * 
         * 2. 动态菜单
         *    - 根据应用状态动态构建菜单项
         *    - 使用 ObservableCollection 支持运行时更新
         *    - 合理使用分隔符分组相关功能
         * 
         * 3. 用户体验
         *    - 为菜单项提供图标和快捷键
         *    - 使用工具提示说明功能
         *    - 保持菜单结构的一致性
         * 
         * 4. 性能优化
         *    - 避免在菜单打开时执行耗时操作
         *    - 使用延迟加载构建复杂菜单
         *    - 合理缓存菜单项数据
         * 
         * 5. 可访问性
         *    - 为菜单项提供键盘导航支持
         *    - 使用语义化的命名
         *    - 支持屏幕阅读器
         */

        #endregion
    }
}
