// gong-wpf-dragdrop 列表拖拽高级功能 C# 示例
using System.Collections.ObjectModel;
using System.Windows;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using GongSolutions.Wpf.DragDrop;

namespace WPFTest.ViewModels.DragDrop
{
    /// <summary>
    /// 高级列表拖拽示例 ViewModel
    /// 实现 IDragSource 和 IDropTarget 接口
    /// </summary>
    public partial class AdvancedListDragDropViewModel : ObservableObject, IDragSource, IDropTarget
    {
        [ObservableProperty]
        private ObservableCollection<DragDropItem> sourceItems = new();

        [ObservableProperty]
        private ObservableCollection<DragDropItem> targetItems = new();

        [ObservableProperty]
        private ObservableCollection<DragDropItem> sortableItems = new();

        #region IDragSource 实现

        /// <summary>
        /// 开始拖拽操作
        /// </summary>
        public void StartDrag(IDragInfo dragInfo)
        {
            var sourceItem = dragInfo.SourceItem as DragDropItem;
            if (sourceItem != null)
            {
                dragInfo.Data = sourceItem;
                dragInfo.Effects = DragDropEffects.Move;
                
                // 可以根据不同条件设置不同的拖拽效果
                if (sourceItem.IsReadOnly)
                {
                    dragInfo.Effects = DragDropEffects.Copy;
                }
            }
        }

        /// <summary>
        /// 判断是否可以开始拖拽
        /// </summary>
        public bool CanStartDrag(IDragInfo dragInfo)
        {
            var item = dragInfo.SourceItem as DragDropItem;
            
            // 根据业务逻辑判断是否允许拖拽
            return item != null && 
                   item.IsEditable && 
                   !item.IsLocked;
        }

        /// <summary>
        /// 拖拽完成后的处理
        /// </summary>
        public void Dropped(IDropInfo dropInfo)
        {
            var sourceItem = dropInfo.Data as DragDropItem;
            if (sourceItem != null)
            {
                Console.WriteLine($"拖拽完成: {sourceItem.Name}");
            }
        }

        /// <summary>
        /// 拖拽操作完成
        /// </summary>
        public void DragDropOperationFinished(DragDropEffects operationResult, IDragInfo dragInfo)
        {
            var sourceItem = dragInfo.Data as DragDropItem;
            if (sourceItem != null)
            {
                Console.WriteLine($"拖拽操作完成: {sourceItem.Name}, 结果: {operationResult}");
            }
        }

        /// <summary>
        /// 处理拖拽过程中的异常
        /// </summary>
        public bool TryCatchOccurredException(Exception exception)
        {
            Console.WriteLine($"拖拽异常: {exception.Message}");
            return true; // 返回 true 表示异常已处理
        }

        /// <summary>
        /// 拖拽被取消
        /// </summary>
        public void DragCancelled()
        {
            Console.WriteLine("拖拽操作被取消");
        }

        #endregion

        #region IDropTarget 实现

        /// <summary>
        /// 拖拽悬停时的处理
        /// </summary>
        public void DragOver(IDropInfo dropInfo)
        {
            var sourceItem = dropInfo.Data as DragDropItem;
            if (sourceItem != null)
            {
                var targetCollection = dropInfo.TargetCollection;
                
                // 根据目标集合设置不同的视觉反馈
                if (targetCollection == TargetItems)
                {
                    dropInfo.DropTargetAdorner = DropTargetAdorners.Highlight;
                    dropInfo.Effects = DragDropEffects.Move;
                }
                else if (targetCollection == SortableItems)
                {
                    dropInfo.DropTargetAdorner = DropTargetAdorners.Insert;
                    dropInfo.Effects = DragDropEffects.Move;
                }
                else if (targetCollection == SourceItems)
                {
                    dropInfo.DropTargetAdorner = DropTargetAdorners.Insert;
                    dropInfo.Effects = DragDropEffects.Move;
                }
                
                // 业务规则验证
                if (!IsValidDropTarget(sourceItem, dropInfo.TargetItem as DragDropItem))
                {
                    dropInfo.Effects = DragDropEffects.None;
                }
            }
        }

        /// <summary>
        /// 执行放置操作
        /// </summary>
        public void Drop(IDropInfo dropInfo)
        {
            var sourceItem = dropInfo.Data as DragDropItem;
            if (sourceItem == null) return;

            var targetCollection = dropInfo.TargetCollection;
            var sourceCollection = dropInfo.DragInfo?.SourceCollection;

            try
            {
                if (targetCollection == TargetItems)
                {
                    HandleDropToTargetList(sourceItem, sourceCollection, dropInfo);
                }
                else if (targetCollection == SortableItems)
                {
                    HandleDropToSortableList(sourceItem, sourceCollection, dropInfo);
                }
                else if (targetCollection == SourceItems)
                {
                    HandleDropToSourceList(sourceItem, sourceCollection, dropInfo);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"放置操作失败: {ex.Message}");
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 验证是否为有效的放置目标
        /// </summary>
        private bool IsValidDropTarget(DragDropItem source, DragDropItem target)
        {
            // 实现具体的业务验证逻辑
            if (source == null) return false;
            
            // 例如：只允许相同类别的项目放置在一起
            return target == null || target.Category == source.Category;
        }

        /// <summary>
        /// 处理放置到目标列表
        /// </summary>
        private void HandleDropToTargetList(DragDropItem sourceItem, object sourceCollection, IDropInfo dropInfo)
        {
            if (sourceCollection == SourceItems)
            {
                SourceItems.Remove(sourceItem);
                TargetItems.Add(sourceItem);
            }
            else if (sourceCollection == TargetItems)
            {
                // 在目标列表内部重新排序
                var insertIndex = dropInfo.InsertIndex;
                if (insertIndex >= 0 && insertIndex < TargetItems.Count)
                {
                    TargetItems.Move(TargetItems.IndexOf(sourceItem), insertIndex);
                }
            }
        }

        /// <summary>
        /// 处理放置到可排序列表
        /// </summary>
        private void HandleDropToSortableList(DragDropItem sourceItem, object sourceCollection, IDropInfo dropInfo)
        {
            if (sourceCollection != SortableItems)
            {
                // 从其他列表移动到可排序列表
                RemoveFromSourceCollection(sourceItem, sourceCollection);
                
                var insertIndex = dropInfo.InsertIndex;
                if (insertIndex >= 0 && insertIndex <= SortableItems.Count)
                {
                    SortableItems.Insert(insertIndex, sourceItem);
                }
                else
                {
                    SortableItems.Add(sourceItem);
                }
            }
            else
            {
                // 在可排序列表内部重新排序
                var insertIndex = dropInfo.InsertIndex;
                if (insertIndex >= 0 && insertIndex < SortableItems.Count)
                {
                    SortableItems.Move(SortableItems.IndexOf(sourceItem), insertIndex);
                }
            }
        }

        /// <summary>
        /// 处理放置到源列表
        /// </summary>
        private void HandleDropToSourceList(DragDropItem sourceItem, object sourceCollection, IDropInfo dropInfo)
        {
            if (sourceCollection != SourceItems)
            {
                RemoveFromSourceCollection(sourceItem, sourceCollection);
                SourceItems.Add(sourceItem);
            }
            else
            {
                // 在源列表内部重新排序
                var insertIndex = dropInfo.InsertIndex;
                if (insertIndex >= 0 && insertIndex < SourceItems.Count)
                {
                    SourceItems.Move(SourceItems.IndexOf(sourceItem), insertIndex);
                }
            }
        }

        /// <summary>
        /// 从源集合中移除项目
        /// </summary>
        private void RemoveFromSourceCollection(DragDropItem item, object sourceCollection)
        {
            if (sourceCollection == SourceItems)
            {
                SourceItems.Remove(item);
            }
            else if (sourceCollection == TargetItems)
            {
                TargetItems.Remove(item);
            }
            else if (sourceCollection == SortableItems)
            {
                SortableItems.Remove(item);
            }
        }

        #endregion

        #region 命令

        /// <summary>
        /// 重置数据命令
        /// </summary>
        [RelayCommand]
        private void ResetData()
        {
            SourceItems.Clear();
            TargetItems.Clear();
            SortableItems.Clear();
            
            // 重新初始化数据
            InitializeData();
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            for (int i = 1; i <= 5; i++)
            {
                SourceItems.Add(new DragDropItem
                {
                    Id = i,
                    Name = $"源项目 {i}",
                    Category = i % 2 == 0 ? "偶数类别" : "奇数类别",
                    Priority = i,
                    IsEditable = true,
                    IsLocked = false
                });
            }

            for (int i = 1; i <= 3; i++)
            {
                SortableItems.Add(new DragDropItem
                {
                    Id = i + 100,
                    Name = $"排序项目 {i}",
                    Category = "排序类别",
                    Priority = i,
                    IsEditable = true,
                    IsLocked = false
                });
            }
        }

        #endregion
    }

    /// <summary>
    /// 扩展的拖拽项目数据模型
    /// </summary>
    public partial class DragDropItem : ObservableObject
    {
        [ObservableProperty]
        private int id;

        [ObservableProperty]
        private string name = string.Empty;

        [ObservableProperty]
        private string description = string.Empty;

        [ObservableProperty]
        private string category = string.Empty;

        [ObservableProperty]
        private int priority;

        [ObservableProperty]
        private bool isSelected;

        [ObservableProperty]
        private bool isEditable = true;

        [ObservableProperty]
        private bool isLocked = false;

        [ObservableProperty]
        private bool isReadOnly = false;

        public override string ToString()
        {
            return $"{Name} (ID: {Id}, Category: {Category})";
        }
    }
}
