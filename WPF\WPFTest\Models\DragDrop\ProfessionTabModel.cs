namespace WPFTest.Models.DragDrop;

/// <summary>
/// 专业 Tab 模型 - DWG管理系统专业分类模型
/// </summary>
/// <remarks>
/// 用于表示DWG文件管理系统中的专业分类：
/// - 建筑、暖通、水电、结构等专业
/// - 包含专业名称、图标、文件夹路径和文件数量
/// - 支持选中状态管理
/// </remarks>
public partial class ProfessionTabModel : ObservableObject
{
    /// <summary>
    /// 专业名称（如：建筑、暖通、水电、结构）
    /// </summary>
    [ObservableProperty]
    private string name = string.Empty;

    /// <summary>
    /// 专业图标（Emoji或图标字符）
    /// </summary>
    [ObservableProperty]
    private string icon = string.Empty;

    /// <summary>
    /// 专业文件夹路径（相对于DWG根目录）
    /// </summary>
    [ObservableProperty]
    private string folderPath = string.Empty;

    /// <summary>
    /// 该专业下的文件数量
    /// </summary>
    [ObservableProperty]
    private int fileCount;

    /// <summary>
    /// 是否被选中
    /// </summary>
    [ObservableProperty]
    private bool isSelected;

    /// <summary>
    /// 显示文本（用于UI绑定）
    /// </summary>
    public string DisplayText => $"{Icon} {Name} ({FileCount})";

    /// <summary>
    /// 专业描述信息
    /// </summary>
    public string Description => $"{Name}专业 - {FileCount}个文件";

    /// <summary>
    /// 是否有文件
    /// </summary>
    public bool HasFiles => FileCount > 0;

    /// <summary>
    /// 构造函数
    /// </summary>
    public ProfessionTabModel()
    {
        // 监听属性变化，更新计算属性
        PropertyChanged += (s, e) =>
        {
            switch (e.PropertyName)
            {
                case nameof(WPFTest.Models.DragDrop.ProfessionTabModel.Name):
                case nameof(WPFTest.Models.DragDrop.ProfessionTabModel.Icon):
                    OnPropertyChanged(nameof(DisplayText));
                    OnPropertyChanged(nameof(Description));
                    break;
                case nameof(WPFTest.Models.DragDrop.ProfessionTabModel.FileCount):
                    OnPropertyChanged(nameof(DisplayText));
                    OnPropertyChanged(nameof(Description));
                    OnPropertyChanged(nameof(HasFiles));
                    break;
            }
        };
    }

    /// <summary>
    /// 创建专业Tab模型的工厂方法
    /// </summary>
    /// <param name="name">专业名称</param>
    /// <param name="icon">专业图标</param>
    /// <param name="folderPath">文件夹路径</param>
    /// <returns>专业Tab模型实例</returns>
    public static ProfessionTabModel Create(string name, string icon, string folderPath)
    {
        return new ProfessionTabModel
        {
            Name = name,
            Icon = icon,
            FolderPath = folderPath,
            FileCount = 0,
            IsSelected = false
        };
    }

    /// <summary>
    /// 更新文件数量
    /// </summary>
    /// <param name="count">新的文件数量</param>
    public void UpdateFileCount(int count)
    {
        FileCount = Math.Max(0, count);
    }

    /// <summary>
    /// 重置选中状态
    /// </summary>
    public void ResetSelection()
    {
        IsSelected = false;
    }

    /// <summary>
    /// 设置为选中状态
    /// </summary>
    public void Select()
    {
        IsSelected = true;
    }

    /// <summary>
    /// 获取专业类型枚举（用于业务逻辑）
    /// </summary>
    public ProfessionType GetProfessionType()
    {
        return Name switch
        {
            "建筑" => ProfessionType.Architecture,
            "暖通" => ProfessionType.HVAC,
            "水电" => ProfessionType.Electrical,
            "结构" => ProfessionType.Structure,
            "给排水" => ProfessionType.Plumbing,
            "园林" => ProfessionType.Landscape,
            _ => ProfessionType.Other
        };
    }
}

/// <summary>
/// 专业类型枚举
/// </summary>
public enum ProfessionType
{
    /// <summary>建筑</summary>
    Architecture,
    /// <summary>暖通</summary>
    HVAC,
    /// <summary>水电</summary>
    Electrical,
    /// <summary>结构</summary>
    Structure,
    /// <summary>给排水</summary>
    Plumbing,
    /// <summary>园林</summary>
    Landscape,
    /// <summary>其他</summary>
    Other
}
