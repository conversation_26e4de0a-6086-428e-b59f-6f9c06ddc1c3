<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 🧭 导航专用动画 -->
    
    <!-- ========================================= -->
    <!-- 📄 页面切换动画 -->
    <!-- ========================================= -->
    
    <!-- 页面从右侧滑入 -->
    <Storyboard x:Key="ZyloPageSlideInRightAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                       From="300" To="0" Duration="0:0:0.4">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="0" To="1" Duration="0:0:0.3"/>
    </Storyboard>

    <!-- 页面从左侧滑入 -->
    <Storyboard x:Key="ZyloPageSlideInLeftAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                       From="-300" To="0" Duration="0:0:0.4">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="0" To="1" Duration="0:0:0.3"/>
    </Storyboard>

    <!-- 页面向右滑出 -->
    <Storyboard x:Key="ZyloPageSlideOutRightAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                       From="0" To="300" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseIn" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="1" To="0" Duration="0:0:0.3"/>
    </Storyboard>

    <!-- 页面向左滑出 -->
    <Storyboard x:Key="ZyloPageSlideOutLeftAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                       From="0" To="-300" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseIn" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="1" To="0" Duration="0:0:0.3"/>
    </Storyboard>

    <!-- ========================================= -->
    <!-- 🎯 导航项动画 -->
    <!-- ========================================= -->
    
    <!-- 导航项选中动画 -->
    <Storyboard x:Key="ZyloNavItemSelectAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                       To="1.02" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="1"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                       To="1.02" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="1"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <ColorAnimation Storyboard.TargetProperty="(Panel.Background).(SolidColorBrush.Color)"
                       Duration="0:0:0.2">
            <ColorAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="1"/>
            </ColorAnimation.EasingFunction>
        </ColorAnimation>
    </Storyboard>

    <!-- 导航项取消选中动画 -->
    <Storyboard x:Key="ZyloNavItemDeselectAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                       To="1" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="1"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                       To="1" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="1"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 导航项悬停进入动画 -->
    <Storyboard x:Key="ZyloNavItemHoverEnterAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                       To="5" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="1"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       To="0.8" Duration="0:0:0.2"/>
    </Storyboard>

    <!-- 导航项悬停退出动画 -->
    <Storyboard x:Key="ZyloNavItemHoverExitAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                       To="0" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="1"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       To="1" Duration="0:0:0.2"/>
    </Storyboard>

    <!-- ========================================= -->
    <!-- 📂 子菜单动画 -->
    <!-- ========================================= -->
    
    <!-- 子菜单展开动画 -->
    <Storyboard x:Key="ZyloSubMenuExpandAnimation">
        <DoubleAnimation Storyboard.TargetProperty="Height"
                       Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="0" To="1" Duration="0:0:0.2"
                       BeginTime="0:0:0.1"/>
    </Storyboard>

    <!-- 子菜单收缩动画 -->
    <Storyboard x:Key="ZyloSubMenuCollapseAnimation">
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="1" To="0" Duration="0:0:0.1"/>
        <DoubleAnimation Storyboard.TargetProperty="Height"
                       To="0" Duration="0:0:0.3"
                       BeginTime="0:0:0.1">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseIn" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 子菜单项进入动画 -->
    <Storyboard x:Key="ZyloSubMenuItemEnterAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                       From="-20" To="0" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="0" To="1" Duration="0:0:0.3"/>
    </Storyboard>

    <!-- ========================================= -->
    <!-- 🍞 面包屑动画 -->
    <!-- ========================================= -->
    
    <!-- 面包屑项添加动画 -->
    <Storyboard x:Key="ZyloBreadcrumbAddAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                       From="0.8" To="1" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <BackEase EasingMode="EaseOut" Amplitude="0.3"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                       From="0.8" To="1" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <BackEase EasingMode="EaseOut" Amplitude="0.3"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="0" To="1" Duration="0:0:0.2"/>
    </Storyboard>

    <!-- 面包屑项移除动画 -->
    <Storyboard x:Key="ZyloBreadcrumbRemoveAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                       From="1" To="0.8" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseIn" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                       From="1" To="0.8" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseIn" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="1" To="0" Duration="0:0:0.2"/>
    </Storyboard>

    <!-- ========================================= -->
    <!-- 🔍 搜索动画 -->
    <!-- ========================================= -->
    
    <!-- 搜索框展开动画 -->
    <Storyboard x:Key="ZyloSearchExpandAnimation">
        <DoubleAnimation Storyboard.TargetProperty="Width"
                       Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="0.5" To="1" Duration="0:0:0.2"/>
    </Storyboard>

    <!-- 搜索框收缩动画 -->
    <Storyboard x:Key="ZyloSearchCollapseAnimation">
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="1" To="0.5" Duration="0:0:0.1"/>
        <DoubleAnimation Storyboard.TargetProperty="Width"
                       Duration="0:0:0.3"
                       BeginTime="0:0:0.1">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseIn" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 搜索结果高亮动画 -->
    <Storyboard x:Key="ZyloSearchHighlightAnimation">
        <ColorAnimation Storyboard.TargetProperty="(Panel.Background).(SolidColorBrush.Color)"
                       To="#FFEB3B" Duration="0:0:0.2"/>
        <ColorAnimation Storyboard.TargetProperty="(Panel.Background).(SolidColorBrush.Color)"
                       Duration="0:0:1" BeginTime="0:0:0.2">
            <ColorAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </ColorAnimation.EasingFunction>
        </ColorAnimation>
    </Storyboard>

    <!-- ========================================= -->
    <!-- 📱 侧边栏动画 -->
    <!-- ========================================= -->
    
    <!-- 侧边栏滑入动画 -->
    <Storyboard x:Key="ZyloSidebarSlideInAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                       From="-250" To="0" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="0" To="1" Duration="0:0:0.2"/>
    </Storyboard>

    <!-- 侧边栏滑出动画 -->
    <Storyboard x:Key="ZyloSidebarSlideOutAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                       From="0" To="-250" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseIn" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="1" To="0" Duration="0:0:0.3"/>
    </Storyboard>

</ResourceDictionary>
