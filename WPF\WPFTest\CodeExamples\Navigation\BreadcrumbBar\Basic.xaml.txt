<!-- BreadcrumbBar 基础用法示例 -->
<Grid>
    <Grid.RowDefinitions>
        <RowDefinition Height="Auto"/>
        <RowDefinition Height="*"/>
    </Grid.RowDefinitions>

    <!-- 顶部面包屑导航 -->
    <ui:BreadcrumbBar Grid.Row="0"
                     ItemsSource="{Binding BreadcrumbItems}"
                     Margin="20,10"
                     Background="{DynamicResource LayerFillColorDefaultBrush}"
                     BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                     BorderThickness="1"
                     CornerRadius="4"
                     Padding="10,8">
        
        <!-- 自定义项模板 -->
        <ui:BreadcrumbBar.ItemTemplate>
            <DataTemplate>
                <StackPanel Orientation="Horizontal">
                    <!-- 图标 -->
                    <zylo:ZyloIcon Icon="{Binding Icon}"
                                  FontSize="14"
                                  Margin="0,0,5,0"
                                  Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                    
                    <!-- 标题 -->
                    <TextBlock Text="{Binding Title}"
                              FontSize="14"
                              VerticalAlignment="Center"
                              Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                </StackPanel>
            </DataTemplate>
        </ui:BreadcrumbBar.ItemTemplate>
        
        <!-- 自定义分隔符 -->
        <ui:BreadcrumbBar.SeparatorTemplate>
            <DataTemplate>
                <zylo:ZyloIcon Icon="ChevronRight"
                              FontSize="12"
                              Margin="8,0"
                              Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
            </DataTemplate>
        </ui:BreadcrumbBar.SeparatorTemplate>
    </ui:BreadcrumbBar>

    <!-- 主要内容区域 -->
    <ui:Card Grid.Row="1" Margin="20" Padding="20">
        <ScrollViewer>
            <StackPanel>
                <TextBlock Text="BreadcrumbBar 面包屑导航演示"
                          FontSize="18"
                          FontWeight="SemiBold"
                          Margin="0,0,0,15"
                          Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>

                <TextBlock TextWrapping="Wrap" Margin="0,0,0,15"
                          Foreground="{DynamicResource TextFillColorSecondaryBrush}">
                    这个示例展示了 BreadcrumbBar 的基础功能，包括：
                    <LineBreak/>• 层级路径显示
                    <LineBreak/>• 自定义项模板
                    <LineBreak/>• 自定义分隔符
                    <LineBreak/>• 点击导航功能
                    <LineBreak/>• 动态路径更新
                </TextBlock>

                <!-- 当前路径信息 -->
                <Border Background="{DynamicResource ControlFillColorSecondaryBrush}"
                       CornerRadius="4"
                       Padding="15"
                       Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="当前路径信息"
                                  FontWeight="SemiBold"
                                  Margin="0,0,0,10"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="当前位置: " FontWeight="SemiBold" Margin="0,0,10,5"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding CurrentPath}" Margin="0,0,0,5"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="层级深度: " FontWeight="SemiBold" Margin="0,0,10,5"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding BreadcrumbItems.Count}" Margin="0,0,0,5"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="最后更新: " FontWeight="SemiBold" Margin="0,0,10,0"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding LastUpdated, StringFormat='yyyy-MM-dd HH:mm:ss'}"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- 操作按钮区域 -->
                <StackPanel Orientation="Horizontal" Margin="0,20,0,0">
                    <ui:Button Content="🏠 返回首页"
                              Command="{Binding NavigateToHomeCommand}"
                              Margin="0,0,10,0"/>
                    
                    <ui:Button Content="⬆️ 上一级"
                              Command="{Binding NavigateUpCommand}"
                              Margin="0,0,10,0"/>
                    
                    <ui:Button Content="🔄 刷新路径"
                              Command="{Binding RefreshPathCommand}"
                              Margin="0,0,10,0"/>
                    
                    <ui:Button Content="📋 复制路径"
                              Command="{Binding CopyPathCommand}"/>
                </StackPanel>

                <!-- 快速导航区域 -->
                <Expander Header="快速导航" Margin="0,20,0,0" IsExpanded="False">
                    <StackPanel Margin="0,10,0,0">
                        <TextBlock Text="常用路径："
                                  FontWeight="SemiBold"
                                  Margin="0,0,0,10"/>
                        
                        <ItemsControl ItemsSource="{Binding QuickNavigationItems}">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <WrapPanel Orientation="Horizontal"/>
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <ui:Button Content="{Binding Title}"
                                              Command="{Binding DataContext.QuickNavigateCommand, RelativeSource={RelativeSource AncestorType=ItemsControl}}"
                                              CommandParameter="{Binding}"
                                              Margin="0,0,10,5"
                                              Padding="8,4"
                                              FontSize="12"/>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </Expander>
            </StackPanel>
        </ScrollViewer>
    </ui:Card>
</Grid>
