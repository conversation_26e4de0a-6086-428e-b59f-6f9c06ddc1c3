<!-- ListView 数据绑定示例 -->
<!-- 展示各种数据绑定场景和技术 -->

<StackPanel>
    <!-- 基础数据绑定 -->
    <TextBlock Text="1. 基础数据绑定" FontWeight="Medium" Margin="0,0,0,8"/>
    <ListView ItemsSource="{Binding SimpleItems}"
              SelectedItem="{Binding SelectedSimpleItem, Mode=TwoWay}"
              Height="100"
              Margin="0,0,0,16">
        <!-- 
        绑定说明：
        - ItemsSource: 绑定到数据源集合
        - SelectedItem: 双向绑定选中项
        - Mode=TwoWay: 确保选择变化同步到 ViewModel
        -->
    </ListView>

    <!-- 复杂对象绑定 -->
    <TextBlock Text="2. 复杂对象绑定" FontWeight="Medium" Margin="0,0,0,8"/>
    <ListView ItemsSource="{Binding DataItems}"
              SelectedItem="{Binding SelectedDataItem, Mode=TwoWay}"
              Height="150"
              Margin="0,0,0,16">
        <ListView.ItemTemplate>
            <DataTemplate>
                <StackPanel Orientation="Horizontal" Margin="4">
                    <TextBlock Text="{Binding Icon}" FontSize="16" Margin="0,0,8,0"/>
                    <StackPanel>
                        <TextBlock Text="{Binding Name}" FontWeight="Medium"/>
                        <TextBlock Text="{Binding Category}" FontSize="10" Foreground="Gray"/>
                    </StackPanel>
                </StackPanel>
            </DataTemplate>
        </ListView.ItemTemplate>
        <!-- 
        绑定说明：
        - DataTemplate 定义项目显示模板
        - 绑定到复杂对象的属性
        - 支持嵌套属性绑定
        -->
    </ListView>

    <!-- 多选绑定 -->
    <TextBlock Text="3. 多选模式绑定" FontWeight="Medium" Margin="0,0,0,8"/>
    <StackPanel>
        <CheckBox Content="启用多选模式" 
                  IsChecked="{Binding IsMultiSelectEnabled, Mode=TwoWay}"
                  Margin="0,0,0,8"/>
        <ListView ItemsSource="{Binding DataItems}"
                  SelectionMode="{Binding IsMultiSelectEnabled, Converter={StaticResource BoolToSelectionModeConverter}}"
                  Height="120"
                  Margin="0,0,0,8">
            <ListView.ItemTemplate>
                <DataTemplate>
                    <CheckBox Content="{Binding Name}"
                              IsChecked="{Binding IsSelected, Mode=TwoWay}"
                              Margin="4"/>
                </DataTemplate>
            </ListView.ItemTemplate>
        </ListView>
        <TextBlock Text="{Binding SelectedItems.Count, StringFormat='已选择 {0} 项'}"
                   FontSize="12"
                   Foreground="Blue"/>
    </StackPanel>
    <!-- 
    绑定说明：
    - SelectionMode 绑定到转换器
    - 项目内部的 CheckBox 绑定到 IsSelected
    - 实时显示选择数量
    -->

    <!-- 条件绑定和转换器 -->
    <TextBlock Text="4. 条件绑定和转换器" FontWeight="Medium" Margin="0,16,0,8"/>
    <ListView ItemsSource="{Binding DataItems}"
              Height="120"
              Margin="0,0,0,16">
        <ListView.ItemTemplate>
            <DataTemplate>
                <Border Padding="8" Margin="2">
                    <Border.Background>
                        <SolidColorBrush Color="{Binding IsEnabled, Converter={StaticResource BoolToColorConverter}}"/>
                    </Border.Background>
                    <StackPanel>
                        <TextBlock Text="{Binding Name}" FontWeight="Medium"/>
                        <TextBlock Text="{Binding IsEnabled, Converter={StaticResource BoolToStatusConverter}}"
                                   FontSize="10"/>
                        <TextBlock Text="{Binding CreatedDate, StringFormat='创建: {0:yyyy-MM-dd}'}"
                                   FontSize="10"
                                   Foreground="Gray"/>
                    </StackPanel>
                </Border>
            </DataTemplate>
        </ListView.ItemTemplate>
        <!-- 
        绑定说明：
        - 使用转换器改变背景颜色
        - 布尔值转换为状态文本
        - 日期格式化显示
        -->
    </ListView>

    <!-- 筛选和排序绑定 -->
    <TextBlock Text="5. 筛选和排序" FontWeight="Medium" Margin="0,0,0,8"/>
    <StackPanel>
        <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
            <ui:TextBox Text="{Binding FilterText, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                        PlaceholderText="输入筛选条件"
                        Width="150"
                        Margin="0,0,8,0"/>
            <ComboBox ItemsSource="{Binding Categories}"
                      SelectedItem="{Binding SelectedCategory, Mode=TwoWay}"
                      Width="100"
                      Margin="0,0,8,0"/>
            <Button Content="排序" Command="{Binding SortCommand}"/>
        </StackPanel>
        <ListView ItemsSource="{Binding FilteredItems}"
                  Height="100">
            <ListView.ItemTemplate>
                <DataTemplate>
                    <TextBlock Text="{Binding Name}" Margin="4"/>
                </DataTemplate>
            </ListView.ItemTemplate>
        </ListView>
    </StackPanel>
    <!-- 
    绑定说明：
    - FilterText 实时更新筛选
    - SelectedCategory 分类筛选
    - FilteredItems 绑定到筛选后的集合
    - UpdateSourceTrigger=PropertyChanged 实时更新
    -->

    <!-- 虚拟化绑定 -->
    <TextBlock Text="6. 虚拟化大数据集" FontWeight="Medium" Margin="0,16,0,8"/>
    <ListView ItemsSource="{Binding LargeDataSet}"
              VirtualizingPanel.IsVirtualizing="True"
              VirtualizingPanel.VirtualizationMode="Recycling"
              ScrollViewer.CanContentScroll="True"
              Height="120"
              Margin="0,0,0,16">
        <ListView.ItemTemplate>
            <DataTemplate>
                <TextBlock Text="{Binding Name}" Margin="4"/>
            </DataTemplate>
        </ListView.ItemTemplate>
        <!-- 
        虚拟化说明：
        - IsVirtualizing="True" 启用虚拟化
        - VirtualizationMode="Recycling" 回收模式
        - CanContentScroll="True" 启用内容滚动
        - 适合大数据集性能优化
        -->
    </ListView>

    <!-- 状态显示 -->
    <Border Background="LightYellow" Padding="8" CornerRadius="4">
        <StackPanel>
            <TextBlock Text="数据绑定状态：" FontWeight="Medium"/>
            <TextBlock Text="{Binding SimpleItems.Count, StringFormat='简单项数量: {0}'}"/>
            <TextBlock Text="{Binding DataItems.Count, StringFormat='数据项数量: {0}'}"/>
            <TextBlock Text="{Binding SelectedSimpleItem, StringFormat='选中简单项: {0}', TargetNullValue='无'}"/>
            <TextBlock Text="{Binding SelectedDataItem.Name, StringFormat='选中数据项: {0}', TargetNullValue='无'}"/>
        </StackPanel>
    </Border>
</StackPanel>

<!-- 
数据绑定最佳实践：

1. 使用 ObservableCollection 确保集合变化通知
2. 实现 INotifyPropertyChanged 确保属性变化通知
3. 使用双向绑定 (Mode=TwoWay) 同步选择状态
4. 使用转换器处理数据格式转换
5. 使用 UpdateSourceTrigger 控制更新时机
6. 对大数据集启用虚拟化提升性能
7. 使用 TargetNullValue 处理空值显示
8. 使用 StringFormat 格式化显示文本
-->
