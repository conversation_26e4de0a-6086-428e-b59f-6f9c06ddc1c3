using System;
using System.Windows.Controls;
using System.Windows.Threading;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Wpf.Ui.Controls;

namespace WPFTest.Examples
{
    /// <summary>
    /// 高级 InfoBadge 控件示例
    /// 使用 CommunityToolkit.MVVM 8.4.0 的 Partial Properties 语法
    /// </summary>
    public partial class AdvancedInfoBadgeExample : UserControl
    {
        public AdvancedInfoBadgeExample()
        {
            InitializeComponent();
            DataContext = new AdvancedInfoBadgeViewModel();
        }
    }

    /// <summary>
    /// 高级 InfoBadge 示例 ViewModel
    /// </summary>
    public partial class AdvancedInfoBadgeViewModel : ObservableObject
    {
        #region 私有字段

        private readonly DispatcherTimer _animationTimer;
        private readonly DispatcherTimer _dynamicTimer;
        private readonly Random _random = new();

        #endregion

        #region 动画相关属性

        /// <summary>
        /// 是否启用动画
        /// </summary>
        [ObservableProperty]
        public partial bool IsAnimationEnabled { get; set; } = false;

        /// <summary>
        /// 紧急计数
        /// </summary>
        [ObservableProperty]
        public partial int UrgentCount { get; set; } = 3;

        /// <summary>
        /// 是否有新消息
        /// </summary>
        [ObservableProperty]
        public partial bool HasNewMessages { get; set; } = true;

        /// <summary>
        /// 动态计数
        /// </summary>
        [ObservableProperty]
        public partial int DynamicCount { get; set; } = 1;

        #endregion

        #region 复杂布局属性

        /// <summary>
        /// 主要通知计数
        /// </summary>
        [ObservableProperty]
        public partial int MainNotificationCount { get; set; } = 8;

        /// <summary>
        /// 用户状态颜色
        /// </summary>
        [ObservableProperty]
        public partial string UserStatusColor { get; set; } = "Green";

        /// <summary>
        /// 文档计数
        /// </summary>
        [ObservableProperty]
        public partial int DocumentCount { get; set; } = 15;

        /// <summary>
        /// 任务计数
        /// </summary>
        [ObservableProperty]
        public partial int TaskCount { get; set; } = 5;

        /// <summary>
        /// 是否有任务
        /// </summary>
        [ObservableProperty]
        public partial bool HasTasks { get; set; } = true;

        /// <summary>
        /// 是否有错误
        /// </summary>
        [ObservableProperty]
        public partial bool HasErrors { get; set; } = false;

        /// <summary>
        /// 是否已完成
        /// </summary>
        [ObservableProperty]
        public partial bool IsCompleted { get; set; } = false;

        #endregion

        #region 自定义样式属性

        /// <summary>
        /// 通知计数
        /// </summary>
        [ObservableProperty]
        public partial int NotificationCount { get; set; } = 12;

        /// <summary>
        /// 阴影徽章计数
        /// </summary>
        [ObservableProperty]
        public partial int ShadowBadgeCount { get; set; } = 7;

        #endregion

        #region 实际应用场景属性

        /// <summary>
        /// 朋友圈计数
        /// </summary>
        [ObservableProperty]
        public partial int FriendsCircleCount { get; set; } = 6;

        /// <summary>
        /// 私信计数
        /// </summary>
        [ObservableProperty]
        public partial int PrivateMessageCount { get; set; } = 4;

        /// <summary>
        /// 点赞计数
        /// </summary>
        [ObservableProperty]
        public partial int LikeCount { get; set; } = 23;

        /// <summary>
        /// 待办计数
        /// </summary>
        [ObservableProperty]
        public partial int TodoCount { get; set; } = 9;

        /// <summary>
        /// 审批计数
        /// </summary>
        [ObservableProperty]
        public partial int ApprovalCount { get; set; } = 2;

        /// <summary>
        /// 是否有会议
        /// </summary>
        [ObservableProperty]
        public partial bool HasMeeting { get; set; } = true;

        /// <summary>
        /// 购物车商品计数
        /// </summary>
        [ObservableProperty]
        public partial int CartItemCount { get; set; } = 3;

        /// <summary>
        /// 收藏计数
        /// </summary>
        [ObservableProperty]
        public partial int FavoriteCount { get; set; } = 18;

        /// <summary>
        /// 优惠券计数
        /// </summary>
        [ObservableProperty]
        public partial int CouponCount { get; set; } = 5;

        #endregion

        #region 构造函数

        public AdvancedInfoBadgeViewModel()
        {
            // 初始化动画计时器
            _animationTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            _animationTimer.Tick += OnAnimationTimerTick;

            // 初始化动态计时器
            _dynamicTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(3)
            };
            _dynamicTimer.Tick += OnDynamicTimerTick;
            _dynamicTimer.Start();

            // 初始化默认值
            InitializeDefaults();
        }

        #endregion

        #region 命令

        /// <summary>
        /// 切换动画命令
        /// </summary>
        [RelayCommand]
        private void ToggleAnimation()
        {
            IsAnimationEnabled = !IsAnimationEnabled;
            if (IsAnimationEnabled)
            {
                _animationTimer.Start();
            }
            else
            {
                _animationTimer.Stop();
            }
        }

        /// <summary>
        /// 增加紧急计数命令
        /// </summary>
        [RelayCommand]
        private void IncreaseUrgentCount()
        {
            UrgentCount++;
        }

        /// <summary>
        /// 切换新消息状态命令
        /// </summary>
        [RelayCommand]
        private void ToggleNewMessages()
        {
            HasNewMessages = !HasNewMessages;
        }

        /// <summary>
        /// 切换用户状态命令
        /// </summary>
        [RelayCommand]
        private void ToggleUserStatus()
        {
            var colors = new[] { "Green", "Orange", "Red", "Gray" };
            var currentIndex = Array.IndexOf(colors, UserStatusColor);
            UserStatusColor = colors[(currentIndex + 1) % colors.Length];
        }

        /// <summary>
        /// 切换任务状态命令
        /// </summary>
        [RelayCommand]
        private void ToggleTaskStatus()
        {
            HasTasks = !HasTasks;
            if (!HasTasks) TaskCount = 0;
            else TaskCount = _random.Next(1, 10);
        }

        /// <summary>
        /// 切换错误状态命令
        /// </summary>
        [RelayCommand]
        private void ToggleErrorStatus()
        {
            HasErrors = !HasErrors;
        }

        /// <summary>
        /// 切换完成状态命令
        /// </summary>
        [RelayCommand]
        private void ToggleCompletionStatus()
        {
            IsCompleted = !IsCompleted;
        }

        /// <summary>
        /// 模拟社交媒体活动命令
        /// </summary>
        [RelayCommand]
        private void SimulateSocialActivity()
        {
            FriendsCircleCount += _random.Next(1, 5);
            PrivateMessageCount += _random.Next(0, 3);
            LikeCount += _random.Next(1, 8);
        }

        /// <summary>
        /// 模拟工作活动命令
        /// </summary>
        [RelayCommand]
        private void SimulateWorkActivity()
        {
            TodoCount += _random.Next(0, 3);
            ApprovalCount += _random.Next(0, 2);
            HasMeeting = _random.Next(0, 2) == 1;
        }

        /// <summary>
        /// 模拟购物活动命令
        /// </summary>
        [RelayCommand]
        private void SimulateShoppingActivity()
        {
            CartItemCount += _random.Next(0, 2);
            FavoriteCount += _random.Next(0, 4);
            CouponCount += _random.Next(0, 2);
        }

        /// <summary>
        /// 重置所有计数命令
        /// </summary>
        [RelayCommand]
        private void ResetAllCounts()
        {
            // 动画相关
            UrgentCount = 0;
            DynamicCount = 0;
            HasNewMessages = false;
            
            // 复杂布局
            MainNotificationCount = 0;
            DocumentCount = 0;
            TaskCount = 0;
            HasTasks = false;
            HasErrors = false;
            IsCompleted = false;
            
            // 自定义样式
            NotificationCount = 0;
            ShadowBadgeCount = 0;
            
            // 实际应用场景
            FriendsCircleCount = 0;
            PrivateMessageCount = 0;
            LikeCount = 0;
            TodoCount = 0;
            ApprovalCount = 0;
            HasMeeting = false;
            CartItemCount = 0;
            FavoriteCount = 0;
            CouponCount = 0;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化默认值
        /// </summary>
        private void InitializeDefaults()
        {
            // 设置默认值已在属性声明中完成
        }

        /// <summary>
        /// 动画计时器事件处理
        /// </summary>
        private void OnAnimationTimerTick(object? sender, EventArgs e)
        {
            // 随机更新紧急计数
            if (_random.Next(0, 3) == 0)
            {
                UrgentCount++;
            }
        }

        /// <summary>
        /// 动态计时器事件处理
        /// </summary>
        private void OnDynamicTimerTick(object? sender, EventArgs e)
        {
            // 动态更新计数
            DynamicCount = _random.Next(1, 100);
            
            // 随机更新其他计数
            if (_random.Next(0, 4) == 0)
            {
                MainNotificationCount += _random.Next(1, 3);
                NotificationCount += _random.Next(0, 2);
                ShadowBadgeCount += _random.Next(0, 2);
            }
        }

        #endregion
    }
}
