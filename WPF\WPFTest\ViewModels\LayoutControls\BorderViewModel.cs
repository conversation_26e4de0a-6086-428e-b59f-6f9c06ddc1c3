using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Windows;
using System.IO;
using System.Reflection;
using System.Collections.ObjectModel;
using System.ComponentModel;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels.LayoutControls
{
    /// <summary>
    /// Border 页面的 ViewModel，演示 Border 控件的各种功能
    /// </summary>
    public partial class BorderViewModel : ObservableObject
    {
        private readonly YLoggerInstance _logger = YLogger.For<BorderViewModel>();

        #region 属性

        /// <summary>
        /// 交互次数
        /// </summary>
        [ObservableProperty]
        private int interactionCount = 0;

        /// <summary>
        /// 最后执行的操作
        /// </summary>
        [ObservableProperty]
        private string lastAction = "无操作";

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        private string statusMessage = "欢迎使用 Border 示例库！";

        #region 代码示例属性

        /// <summary>
        /// 基础功能 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 基础功能 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级功能 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级功能 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 样式 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string StylesXamlExample { get; set; } = string.Empty;

        #endregion

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化 BorderViewModel
        /// </summary>
        public BorderViewModel()
        {
            try
            {
                _logger.Info("🚀 Border 页面 ViewModel 开始初始化");

                StatusMessage = "Border 示例库已加载，开始体验各种功能！";
                InitializeCodeExamples();

                _logger.Info("✅ Border 页面 ViewModel 初始化完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ Border 页面 ViewModel 初始化失败: {ex.Message}");
                StatusMessage = "初始化失败，请检查日志";
            }
        }

        #endregion

        #region 命令

        /// <summary>
        /// 通用交互命令
        /// </summary>
        /// <param name="parameter">操作参数</param>
        [RelayCommand]
        private void HandleInteraction(string? parameter)
        {
            try
            {
                InteractionCount++;
                LastAction = parameter ?? "未知操作";
                
                _logger.Info($"🎯 用户交互操作: {parameter}");
                _logger.Info($"🔢 当前交互次数: {InteractionCount}");

                // 根据不同的操作显示不同的状态消息
                StatusMessage = parameter switch
                {
                    "标准Border" => "🎯 点击了标准 Border 样式",
                    "圆角Border" => "🎯 点击了圆角 Border 样式",
                    "阴影Border" => "🎯 点击了阴影 Border 样式",
                    "渐变Border" => "🎯 点击了渐变背景 Border",
                    "虚线Border" => "🎯 点击了虚线边框 Border",
                    "动画Border" => "🎯 点击了动画 Border",
                    _ => $"🎯 执行了操作: {parameter}"
                };
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 处理用户交互失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 重置计数命令
        /// </summary>
        [RelayCommand]
        private void ResetCount()
        {
            InteractionCount = 0;
            StatusMessage = "计数已重置";
            _logger.Info("重置了交互计数");
        }

        /// <summary>
        /// 清除状态命令
        /// </summary>
        [RelayCommand]
        private void ClearStatus()
        {
            LastAction = "无操作";
            StatusMessage = "状态已清除";
            _logger.Info("清除了状态信息");
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化代码示例
        /// </summary>
        private void InitializeCodeExamples()
        {
            try
            {
                // 获取代码示例文件的基础路径
                var baseDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
                var codeExamplesPath = Path.Combine(baseDirectory, "CodeExamples", "LayoutControls", "Border");

                _logger.Info($"基础目录: {baseDirectory}");
                _logger.Info($"代码示例路径: {codeExamplesPath}");
                _logger.Info($"目录是否存在: {Directory.Exists(codeExamplesPath)}");

                // 读取各种示例文件
                BasicXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.xaml.txt"));
                BasicCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.cs.txt"));
                AdvancedXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Advanced.xaml.txt"));
                AdvancedCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Advanced.cs.txt"));
                StylesXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Styles.xaml.txt"));

                _logger.Info("Border 代码示例加载完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"加载 Border 代码示例失败: {ex.Message}");
                SetDefaultCodeExamples();
            }
        }

        /// <summary>
        /// 读取代码示例文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件内容</returns>
        private string ReadCodeExampleFile(string filePath)
        {
            _logger.Info($"尝试读取文件: {filePath}");
            
            if (File.Exists(filePath))
            {
                var content = File.ReadAllText(filePath);
                _logger.Info($"成功读取文件，内容长度: {content.Length}");
                return content;
            }
            else
            {
                _logger.Warning($"代码示例文件不存在: {filePath}");
                return $"// 代码示例文件不存在: {Path.GetFileName(filePath)}\n// 完整路径: {filePath}";
            }
        }

        /// <summary>
        /// 设置默认代码示例（当文件读取失败时使用）
        /// </summary>
        private void SetDefaultCodeExamples()
        {
            BasicXamlExample = @"<!-- Border 基础示例 -->
<Border Background=""{DynamicResource ControlFillColorDefaultBrush}""
        BorderBrush=""{DynamicResource ControlStrokeColorDefaultBrush}""
        BorderThickness=""1""
        CornerRadius=""4""
        Padding=""16"">
    <TextBlock Text=""标准 Border"" 
               HorizontalAlignment=""Center""
               VerticalAlignment=""Center""/>
</Border>";

            BasicCSharpExample = @"// Border C# 基础示例
// Border 是布局控件，通常在 XAML 中使用
// 可以通过代码动态创建和配置

var border = new Border
{
    Background = new SolidColorBrush(Colors.LightBlue),
    BorderBrush = new SolidColorBrush(Colors.Blue),
    BorderThickness = new Thickness(2),
    CornerRadius = new CornerRadius(8),
    Padding = new Thickness(16)
};

var textBlock = new TextBlock
{
    Text = ""动态创建的 Border"",
    HorizontalAlignment = HorizontalAlignment.Center,
    VerticalAlignment = VerticalAlignment.Center
};

border.Child = textBlock;";

            AdvancedXamlExample = @"<!-- Border 高级示例 -->
<!-- 渐变背景 Border -->
<Border BorderBrush=""{DynamicResource ControlStrokeColorDefaultBrush}""
        BorderThickness=""1""
        CornerRadius=""8""
        Padding=""16"">
    <Border.Background>
        <LinearGradientBrush StartPoint=""0,0"" EndPoint=""1,1"">
            <GradientStop Color=""#FF6B73FF"" Offset=""0""/>
            <GradientStop Color=""#FF9DFFAD"" Offset=""1""/>
        </LinearGradientBrush>
    </Border.Background>
    <TextBlock Text=""渐变背景"" 
               Foreground=""White""
               HorizontalAlignment=""Center""
               VerticalAlignment=""Center""/>
</Border>";

            AdvancedCSharpExample = @"// Border 高级 C# 示例
// 创建带渐变背景和动画的 Border

var border = new Border
{
    BorderBrush = Brushes.Gray,
    BorderThickness = new Thickness(1),
    CornerRadius = new CornerRadius(8),
    Padding = new Thickness(16)
};

// 创建渐变背景
var gradientBrush = new LinearGradientBrush
{
    StartPoint = new Point(0, 0),
    EndPoint = new Point(1, 1)
};
gradientBrush.GradientStops.Add(new GradientStop(Color.FromRgb(107, 115, 255), 0));
gradientBrush.GradientStops.Add(new GradientStop(Color.FromRgb(157, 255, 173), 1));

border.Background = gradientBrush;

// 添加鼠标悬停动画
border.MouseEnter += (s, e) => {
    var animation = new DoubleAnimation(1.1, TimeSpan.FromMilliseconds(200));
    border.RenderTransform = new ScaleTransform();
    border.RenderTransformOrigin = new Point(0.5, 0.5);
    border.RenderTransform.BeginAnimation(ScaleTransform.ScaleXProperty, animation);
    border.RenderTransform.BeginAnimation(ScaleTransform.ScaleYProperty, animation);
};";

            StylesXamlExample = @"<!-- Border 样式示例 -->
<!-- 在 Zylo.WPF 中定义的样式 -->

<!-- 基础样式 -->
<Style x:Key=""BorderBaseStyle"" TargetType=""Border"">
    <Setter Property=""Background"" Value=""{DynamicResource ControlFillColorDefaultBrush}""/>
    <Setter Property=""BorderBrush"" Value=""{DynamicResource ControlStrokeColorDefaultBrush}""/>
    <Setter Property=""BorderThickness"" Value=""1""/>
    <Setter Property=""CornerRadius"" Value=""4""/>
    <Setter Property=""Padding"" Value=""8""/>
</Style>

<!-- 现代化样式 -->
<Style x:Key=""ModernBorderStyle"" TargetType=""Border"" BasedOn=""{StaticResource BorderBaseStyle}"">
    <Setter Property=""CornerRadius"" Value=""8""/>
    <Setter Property=""Effect"">
        <Setter.Value>
            <DropShadowEffect Color=""Black"" Opacity=""0.1"" ShadowDepth=""4"" BlurRadius=""8""/>
        </Setter.Value>
    </Setter>
</Style>";
        }

        #endregion
    }
}
