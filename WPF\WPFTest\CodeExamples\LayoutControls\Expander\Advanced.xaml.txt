<!-- Expander 高级用法示例 -->
<StackPanel Margin="20" Spacing="15">

    <!-- 嵌套 Expander -->
    <GroupBox Header="嵌套 Expander" Padding="15">
        <Expander Header="嵌套 Expander 示例" 
                  IsExpanded="False"
                  Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                  BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                  BorderThickness="1">
            <StackPanel Padding="16">
                <TextBlock Text="这是父级 Expander 的内容。" Margin="0,0,0,12"/>
                
                <Expander Header="子级 Expander 1" 
                          IsExpanded="False"
                          Background="{DynamicResource ControlFillColorDefaultBrush}"
                          Margin="0,0,0,8">
                    <StackPanel Padding="12">
                        <TextBlock Text="这是第一个子级 Expander。"/>
                        <Button Content="子级按钮 1" 
                                HorizontalAlignment="Left"
                                Margin="0,8,0,0"/>
                    </StackPanel>
                </Expander>
                
                <Expander Header="子级 Expander 2" 
                          IsExpanded="False"
                          Background="{DynamicResource ControlFillColorDefaultBrush}">
                    <StackPanel Padding="12">
                        <TextBlock Text="这是第二个子级 Expander。"/>
                        <ComboBox Margin="0,8,0,0">
                            <ComboBoxItem Content="选项 1"/>
                            <ComboBoxItem Content="选项 2"/>
                            <ComboBoxItem Content="选项 3"/>
                        </ComboBox>
                    </StackPanel>
                </Expander>
            </StackPanel>
        </Expander>
    </GroupBox>

    <!-- 自定义样式 Expander -->
    <GroupBox Header="自定义样式 Expander" Padding="15">
        <Expander Header="自定义样式 Expander" IsExpanded="False">
            <Expander.Style>
                <Style TargetType="Expander">
                    <Setter Property="Background" Value="#FF2D3748"/>
                    <Setter Property="Foreground" Value="White"/>
                    <Setter Property="BorderBrush" Value="#FF4A5568"/>
                    <Setter Property="BorderThickness" Value="2"/>
                    <Setter Property="Padding" Value="12"/>
                </Style>
            </Expander.Style>
            <StackPanel Padding="16">
                <TextBlock Text="这是一个自定义样式的 Expander。" 
                           Foreground="White" 
                           Margin="0,0,0,12"/>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <StackPanel Grid.Column="0" Margin="0,0,8,0">
                        <TextBlock Text="左侧内容" 
                                   Foreground="White" 
                                   FontWeight="Bold" 
                                   Margin="0,0,0,8"/>
                        <TextBox Text="输入框示例" Margin="0,0,0,8"/>
                        <Button Content="左侧按钮"/>
                    </StackPanel>
                    <StackPanel Grid.Column="1" Margin="8,0,0,0">
                        <TextBlock Text="右侧内容" 
                                   Foreground="White" 
                                   FontWeight="Bold" 
                                   Margin="0,0,0,8"/>
                        <ListBox Height="80" Margin="0,0,0,8">
                            <ListBoxItem Content="项目 1"/>
                            <ListBoxItem Content="项目 2"/>
                            <ListBoxItem Content="项目 3"/>
                        </ListBox>
                        <Button Content="右侧按钮"/>
                    </StackPanel>
                </Grid>
            </StackPanel>
        </Expander>
    </GroupBox>

    <!-- 动画效果 Expander -->
    <GroupBox Header="动画效果 Expander" Padding="15">
        <Expander Header="动画效果 Expander" IsExpanded="False">
            <Expander.Style>
                <Style TargetType="Expander">
                    <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}"/>
                    <Setter Property="BorderBrush" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
                    <Setter Property="BorderThickness" Value="2"/>
                    <Style.Triggers>
                        <Trigger Property="IsExpanded" Value="True">
                            <Trigger.EnterActions>
                                <BeginStoryboard>
                                    <Storyboard>
                                        <ColorAnimation Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                        To="LightGreen" Duration="0:0:0.3"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.EnterActions>
                            <Trigger.ExitActions>
                                <BeginStoryboard>
                                    <Storyboard>
                                        <ColorAnimation Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                        To="LightGray" Duration="0:0:0.3"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.ExitActions>
                        </Trigger>
                    </Style.Triggers>
                </Style>
            </Expander.Style>
            <StackPanel Padding="16">
                <TextBlock Text="这个 Expander 在展开/折叠时有颜色动画效果。" Margin="0,0,0,12"/>
                <ProgressBar Value="60" Height="20" Margin="0,0,0,8"/>
                <TextBlock Text="展开时背景变为绿色，折叠时恢复原色。"/>
            </StackPanel>
        </Expander>
    </GroupBox>

    <!-- 复杂内容 Expander -->
    <GroupBox Header="复杂内容 Expander" Padding="15">
        <Expander Header="复杂内容展示" IsExpanded="False">
            <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Padding="16">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <!-- 标题区域 -->
                    <StackPanel Grid.Row="0" Margin="0,0,0,16">
                        <TextBlock Text="用户信息表单" 
                                   FontSize="16" 
                                   FontWeight="Bold"
                                   Margin="0,0,0,8"/>
                        <Separator/>
                    </StackPanel>
                    
                    <!-- 表单内容 -->
                    <Grid Grid.Row="1" Margin="0,0,0,16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="姓名:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                        <TextBox Grid.Row="0" Grid.Column="1" Text="张三" Margin="0,0,16,8"/>
                        
                        <TextBlock Grid.Row="0" Grid.Column="2" Text="年龄:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                        <TextBox Grid.Row="0" Grid.Column="3" Text="25" Margin="0,0,0,8"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="邮箱:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                        <TextBox Grid.Row="1" Grid.Column="1" Text="<EMAIL>" Margin="0,0,16,8"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="2" Text="电话:" VerticalAlignment="Center" Margin="0,0,8,8"/>
                        <TextBox Grid.Row="1" Grid.Column="3" Text="138****8888" Margin="0,0,0,8"/>
                        
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="备注:" VerticalAlignment="Top" Margin="0,0,8,0"/>
                        <TextBox Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="3" 
                                 Text="这是一个示例用户的备注信息。"
                                 TextWrapping="Wrap"
                                 Height="60"/>
                    </Grid>
                    
                    <!-- 操作按钮 -->
                    <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right">
                        <Button Content="重置" Margin="0,0,8,0"/>
                        <Button Content="保存"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Expander>
    </GroupBox>

</StackPanel>
