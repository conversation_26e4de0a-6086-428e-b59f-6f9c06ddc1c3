using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Windows;
using System.IO;
using System.Reflection;
using System.ComponentModel;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels.InputControls
{
    /// <summary>
    /// TimePicker 页面的 ViewModel，演示 TimePicker 控件的各种功能
    /// </summary>
    public partial class TimePickerPageViewModel : ObservableObject
    {
        private readonly YLoggerInstance _logger = YLogger.ForDebug<TimePickerPageViewModel>();

        #region 属性

        /// <summary>
        /// 交互次数
        /// </summary>
        [ObservableProperty]
        private int interactionCount = 0;

        /// <summary>
        /// 最后执行的操作
        /// </summary>
        [ObservableProperty]
        private string lastAction = "无操作";

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        private string statusMessage = "欢迎使用 TimePicker 示例库！";

        /// <summary>
        /// 选择的时间 (DateTime for TimePicker)
        /// </summary>
        [ObservableProperty]
        private DateTime? selectedTime = DateTime.Now;

        /// <summary>
        /// 工作时间 (DateTime for TimePicker)
        /// </summary>
        [ObservableProperty]
        private DateTime? workTime = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 9, 0, 0);

        /// <summary>
        /// 提醒时间 (DateTime for TimePicker)
        /// </summary>
        [ObservableProperty]
        private DateTime? reminderTime = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 14, 30, 0);

        /// <summary>
        /// 开始时间 (DateTime for TimePicker)
        /// </summary>
        [ObservableProperty]
        private DateTime? startTime = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 8, 0, 0);

        /// <summary>
        /// 结束时间 (DateTime for TimePicker)
        /// </summary>
        [ObservableProperty]
        private DateTime? endTime = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 17, 30, 0);

        /// <summary>
        /// 程序化时间 (DateTime for TimePicker)
        /// </summary>
        [ObservableProperty]
        private DateTime? programmaticTime = DateTime.Now;

        #region 代码示例属性

        /// <summary>
        /// 基础功能 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 基础功能 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级功能 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级功能 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 样式 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string StylesXamlExample { get; set; } = string.Empty;

        #endregion

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化 TimePickerPageViewModel
        /// </summary>
        public TimePickerPageViewModel()
        {
            try
            {
                _logger.Info("🚀 TimePicker 页面 ViewModel 开始初始化");

                // 调试：检查命令是否正确生成
                _logger.Info($"🔍 SetCurrentTimeCommand 是否为空: {SetCurrentTimeCommand == null}");
                _logger.Info($"🔍 ClearTimeCommand 是否为空: {ClearTimeCommand == null}");
                _logger.Info($"🔍 ResetCountCommand 是否为空: {ResetCountCommand == null}");

                StatusMessage = "TimePicker 示例库已加载，开始体验时间选择功能！";
                InitializeCodeExamples();

                // 监听属性变化
                PropertyChanged += OnPropertyChanged;

                _logger.Info("✅ TimePicker 页面 ViewModel 初始化完成");
                _logger.Info($"📊 初始状态 - 当前时间: {SelectedTime}");

                // 调试：再次检查命令
                _logger.Info($"🔍 初始化后 SetCurrentTimeCommand 是否为空: {SetCurrentTimeCommand == null}");
                if (SetCurrentTimeCommand != null)
                {
                    _logger.Info($"🔍 SetCurrentTimeCommand.CanExecute: {SetCurrentTimeCommand.CanExecute(null)}");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ TimePicker 页面 ViewModel 初始化失败: {ex.Message}");
                _logger.Error($"🔍 异常详情: {ex}");
                StatusMessage = "初始化失败，请检查日志";
            }
        }

        #endregion

        #region 命令

        /// <summary>
        /// 设置当前时间命令
        /// </summary>
        [RelayCommand]
        private void SetCurrentTime()
        {
            try
            {
                _logger.Info("🔥 SetCurrentTime 命令被调用！");
                var now = DateTime.Now;
                SelectedTime = now;
                InteractionCount++;
                LastAction = "设置当前时间";
                StatusMessage = $"🕐 已设置为当前时间: {now:HH:mm:ss}";

                _logger.Info($"🎯 设置当前时间: {now:HH:mm:ss}");

                // 强制触发UI更新
                OnPropertyChanged(nameof(SelectedTime));
                OnPropertyChanged(nameof(InteractionCount));
                OnPropertyChanged(nameof(LastAction));
                OnPropertyChanged(nameof(StatusMessage));
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 设置当前时间失败: {ex.Message}");
                StatusMessage = $"❌ 错误: {ex.Message}";
            }
        }

        /// <summary>
        /// 清除时间命令
        /// </summary>
        [RelayCommand]
        private void ClearTime()
        {
            try
            {
                _logger.Info("🔥 ClearTime 命令被调用！");
                SelectedTime = null;
                InteractionCount++;
                LastAction = "清除时间";
                StatusMessage = "🗑️ 已清除选择的时间";

                _logger.Info("🎯 清除了选择的时间");

                // 强制触发UI更新
                OnPropertyChanged(nameof(SelectedTime));
                OnPropertyChanged(nameof(InteractionCount));
                OnPropertyChanged(nameof(LastAction));
                OnPropertyChanged(nameof(StatusMessage));
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 清除时间失败: {ex.Message}");
                StatusMessage = $"❌ 错误: {ex.Message}";
            }
        }

        /// <summary>
        /// 设置特定时间命令
        /// </summary>
        [RelayCommand]
        private void SetSpecificTime(string? timeString)
        {
            try
            {
                if (string.IsNullOrEmpty(timeString))
                    return;

                if (TimeSpan.TryParse(timeString, out var timeSpan))
                {
                    var today = DateTime.Today;
                    ProgrammaticTime = today.Add(timeSpan);
                    InteractionCount++;
                    LastAction = $"设置时间为 {timeString}";
                    StatusMessage = $"⏰ 已设置程序化时间为: {timeString}";

                    _logger.Info($"🎯 设置特定时间: {timeString}");

                    // 强制触发UI更新
                    OnPropertyChanged(nameof(ProgrammaticTime));
                    OnPropertyChanged(nameof(InteractionCount));
                    OnPropertyChanged(nameof(LastAction));
                    OnPropertyChanged(nameof(StatusMessage));
                }
                else
                {
                    StatusMessage = $"❌ 无效的时间格式: {timeString}";
                    _logger.Warning($"⚠️ 无效的时间格式: {timeString}");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 设置特定时间失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 重置计数命令
        /// </summary>
        [RelayCommand]
        private void ResetCount()
        {
            _logger.Info("🔥 ResetCount 命令被调用！");
            InteractionCount = 0;
            StatusMessage = "计数已重置";
            _logger.Info("重置了交互计数");

            // 强制触发UI更新
            OnPropertyChanged(nameof(InteractionCount));
            OnPropertyChanged(nameof(StatusMessage));
        }

        /// <summary>
        /// 清除状态命令
        /// </summary>
        [RelayCommand]
        private void ClearStatus()
        {
            LastAction = "无操作";
            StatusMessage = "状态已清除";
            _logger.Info("清除了状态信息");
        }

        /// <summary>
        /// 简单测试命令
        /// </summary>
        [RelayCommand]
        private void SimpleTest()
        {
            _logger.Info("🔥 SimpleTest 命令被调用！");
            StatusMessage = $"🧪 测试命令执行成功！时间: {DateTime.Now:HH:mm:ss}";
            InteractionCount++;

            // 强制触发UI更新
            OnPropertyChanged(nameof(StatusMessage));
            OnPropertyChanged(nameof(InteractionCount));
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 属性变化处理
        /// </summary>
        private void OnPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            try
            {
                switch (e.PropertyName)
                {
                    case nameof(SelectedTime):
                        if (SelectedTime.HasValue)
                        {
                            InteractionCount++;
                            LastAction = "选择时间";
                            StatusMessage = $"🕐 选择了时间: {SelectedTime.Value:HH:mm:ss}";
                            _logger.Info($"📝 时间选择变更: {SelectedTime.Value:HH:mm:ss}");
                        }
                        break;
                    case nameof(WorkTime):
                        if (WorkTime.HasValue)
                        {
                            InteractionCount++;
                            LastAction = "设置工作时间";
                            StatusMessage = $"💼 设置工作时间: {WorkTime.Value:HH:mm}";
                            _logger.Info($"📝 工作时间变更: {WorkTime.Value:HH:mm}");
                        }
                        break;
                    case nameof(ReminderTime):
                        if (ReminderTime.HasValue)
                        {
                            InteractionCount++;
                            LastAction = "设置提醒时间";
                            StatusMessage = $"⏰ 设置提醒时间: {ReminderTime.Value:HH:mm}";
                            _logger.Info($"📝 提醒时间变更: {ReminderTime.Value:HH:mm}");
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 处理属性变化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化代码示例
        /// </summary>
        private void InitializeCodeExamples()
        {
            try
            {
                // 获取代码示例文件的基础路径
                var baseDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
                var codeExamplesPath = Path.Combine(baseDirectory, "CodeExamples", "Controls", "TimePicker");

                _logger.Info($"基础目录: {baseDirectory}");
                _logger.Info($"代码示例路径: {codeExamplesPath}");
                _logger.Info($"目录是否存在: {Directory.Exists(codeExamplesPath)}");

                // 读取各种示例文件
                BasicXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.xaml.txt"));
                BasicCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.cs.txt"));
                AdvancedXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Advanced.xaml.txt"));
                AdvancedCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Advanced.cs.txt"));
                StylesXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Styles.xaml.txt"));

                _logger.Info("TimePicker 代码示例加载完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"加载 TimePicker 代码示例失败: {ex.Message}");
                SetDefaultCodeExamples();
            }
        }

        /// <summary>
        /// 读取代码示例文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件内容</returns>
        private string ReadCodeExampleFile(string filePath)
        {
            _logger.Info($"尝试读取文件: {filePath}");
            
            if (File.Exists(filePath))
            {
                var content = File.ReadAllText(filePath);
                _logger.Info($"成功读取文件，内容长度: {content.Length}");
                return content;
            }
            else
            {
                _logger.Warning($"代码示例文件不存在: {filePath}");
                return $"// 代码示例文件不存在: {Path.GetFileName(filePath)}\n// 完整路径: {filePath}";
            }
        }

        /// <summary>
        /// 设置默认代码示例（当文件读取失败时使用）
        /// </summary>
        private void SetDefaultCodeExamples()
        {
            BasicXamlExample = "<!-- TimePicker 基础示例 -->\n<!-- 在这里添加基础用法示例 -->";
            BasicCSharpExample = "// TimePicker C# 基础示例\n// 在这里添加 C# 代码示例";
            AdvancedXamlExample = "<!-- TimePicker 高级示例 -->\n<!-- 在这里添加高级用法示例 -->";
            AdvancedCSharpExample = "// TimePicker C# 高级示例\n// 在这里添加高级 C# 代码示例";
            StylesXamlExample = "<!-- TimePicker 样式示例 -->\n<!-- 在这里添加样式使用示例 -->";
        }

        #endregion
    }
}
