<UserControl x:Class="WPFTest.Views.NewThemeSettingsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:prism="http://prismlibrary.com/"
             xmlns:viewModels="clr-namespace:WPFTest.ViewModels"
             mc:Ignorable="d"
             prism:ViewModelLocator.AutoWireViewModel="True"
             d:DataContext="{d:DesignInstance viewModels:NewThemeSettingsViewModel}"
             d:DesignHeight="2000" d:DesignWidth="600">

    <UserControl.Resources>
        <!-- 现代化主题模式按钮样式 -->
        <Style x:Key="ModernThemeModeButtonStyle" TargetType="ui:Button">
            <Setter Property="Width" Value="160"/>
            <Setter Property="Height" Value="120"/>
            <Setter Property="Margin" Value="12"/>
            <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}"/>
            <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="{DynamicResource SystemBaseLowColor}"
                                      BlurRadius="8"
                                      ShadowDepth="2"
                                      Opacity="0.1"/>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="BorderBrush" Value="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                    <Setter Property="BorderThickness" Value="2"/>
                    <Setter Property="Effect">
                        <Setter.Value>
                            <DropShadowEffect Color="{DynamicResource SystemAccentColorPrimaryColor}"
                                              BlurRadius="12"
                                              ShadowDepth="3"
                                              Opacity="0.3"/>
                        </Setter.Value>
                    </Setter>
                </Trigger>
                <DataTrigger Binding="{Binding IsSelected}" Value="True">
                    <Setter Property="BorderBrush" Value="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                    <Setter Property="BorderThickness" Value="3"/>
                    <Setter Property="Background" Value="{DynamicResource AccentFillColorTertiaryBrush}"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- 主题图标样式 -->
        <Style x:Key="ThemeIconStyle" TargetType="ui:SymbolIcon">
            <Setter Property="FontSize" Value="32"/>
            <Setter Property="Foreground" Value="{DynamicResource SystemAccentColorPrimaryBrush}"/>
            <Setter Property="Margin" Value="0,0,0,8"/>
        </Style>

        <!-- 主题标题样式 -->
        <Style x:Key="ThemeTitleStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,0,4"/>
        </Style>

        <!-- 主题描述样式 -->
        <Style x:Key="ThemeDescriptionStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="Foreground" Value="{DynamicResource TextFillColorSecondaryBrush}"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="TextAlignment" Value="Center"/>
        </Style>

        <!-- 强调色按钮样式 -->
        <Style x:Key="AccentColorButtonStyle" TargetType="Button">
            <Setter Property="Width" Value="48"/>
            <Setter Property="Height" Value="48"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="24">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="BorderBrush" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid Background="{DynamicResource ApplicationBackgroundBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 页面标题 -->
        <Border Grid.Row="0" 
                Background="{DynamicResource LayerFillColorDefaultBrush}"
                BorderBrush="{DynamicResource DividerStrokeColorDefaultBrush}"
                BorderThickness="0,0,0,1"
                Padding="32,24">
            <StackPanel Orientation="Horizontal">
                <ui:SymbolIcon Symbol="Settings24" 
                               FontSize="32" 
                               Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                               Margin="0,0,16,0"/>
                <StackPanel VerticalAlignment="Center">
                    <TextBlock Text="{Binding PageTitle}"
                               FontSize="24"
                               FontWeight="Bold"
                               Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                    <TextBlock Text="{Binding PageDescription}"
                               FontSize="14"
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- 主要内容 -->
        <ScrollViewer Grid.Row="1" 
                      VerticalScrollBarVisibility="Auto" 
                      HorizontalScrollBarVisibility="Disabled"
                      Padding="32"
                      CanContentScroll="False">
            <StackPanel>
                
                <!-- 主题模式选择 -->
                <ui:Card Padding="24" Margin="0,0,0,32">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                            <ui:SymbolIcon Symbol="WeatherSunny24" 
                                           FontSize="24"
                                           Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                            <StackPanel>
                                <TextBlock Text="主题模式"
                                           FontSize="18"
                                           FontWeight="SemiBold"
                                           Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                                <TextBlock Text="选择应用程序的主题外观"
                                           FontSize="14"
                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                            </StackPanel>
                        </StackPanel>

                        <ItemsControl ItemsSource="{Binding ThemeModes}">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <StackPanel Orientation="Horizontal"
                                                HorizontalAlignment="Center"/>
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <ui:Button Style="{StaticResource ModernThemeModeButtonStyle}"
                                               Command="{Binding DataContext.ChangeThemeCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                               CommandParameter="{Binding Theme}"
                                               Background="Transparent"
                                               BorderThickness="0"
                                               Padding="0">
                                        <Grid>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="*"/>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                            </Grid.RowDefinitions>

                                            <!-- 主题图标 -->
                                            <ui:SymbolIcon Grid.Row="0"
                                                           Symbol="{Binding Icon}"
                                                           Style="{StaticResource ThemeIconStyle}"
                                                           VerticalAlignment="Center"/>

                                            <!-- 主题名称 -->
                                            <TextBlock Grid.Row="1"
                                                       Text="{Binding Name}"
                                                       Style="{StaticResource ThemeTitleStyle}"/>

                                            <!-- 主题描述 -->
                                            <TextBlock Grid.Row="2"
                                                       Text="{Binding Description}"
                                                       Style="{StaticResource ThemeDescriptionStyle}"/>

                                            <!-- 选中指示器 -->
                                            <Border Grid.Row="0" Grid.RowSpan="3"
                                                    Background="Transparent"
                                                    CornerRadius="12">
                                                <Border.Style>
                                                    <Style TargetType="Border">
                                                        <Setter Property="Visibility" Value="Collapsed"/>
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding IsSelected}" Value="True">
                                                                <Setter Property="Visibility" Value="Visible"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </Border.Style>
                                                <ui:SymbolIcon Symbol="Checkmark24"
                                                               FontSize="20"
                                                               Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                                               HorizontalAlignment="Right"
                                                               VerticalAlignment="Top"
                                                               Margin="0,8,8,0"/>
                                            </Border>
                                        </Grid>
                                    </ui:Button>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </ui:Card>

                <!-- 强调色选择 -->
                <ui:Card Padding="24" Margin="0,0,0,32">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,24">
                            <ui:SymbolIcon Symbol="Color24" 
                                           FontSize="24"
                                           Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                            <StackPanel>
                                <TextBlock Text="强调色"
                                           FontSize="18"
                                           FontWeight="SemiBold"
                                           Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                                <TextBlock Text="选择应用程序的强调色"
                                           FontSize="14"
                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                            </StackPanel>
                        </StackPanel>

                        <ItemsControl ItemsSource="{Binding AccentColors}">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <WrapPanel Orientation="Horizontal"/>
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Button Style="{StaticResource AccentColorButtonStyle}"
                                            Background="{Binding Brush}"
                                            Command="{Binding DataContext.ChangeAccentColorCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                            CommandParameter="{Binding Color}"
                                            ToolTip="{Binding Name}"/>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </ui:Card>

                <!-- 🎨 自定义颜色选择器 -->
                <ui:Card Padding="24" Margin="0,0,0,32">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,24">
                            <ui:SymbolIcon Symbol="ColorBackground24"
                                           FontSize="24"
                                           Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                            <StackPanel Margin="16,0,0,0">
                                <TextBlock Text="🎨 自定义颜色"
                                           FontSize="18"
                                           FontWeight="SemiBold"
                                           Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                                <TextBlock Text="选择流行色彩或中性色调作为强调色"
                                           FontSize="14"
                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                            </StackPanel>
                        </StackPanel>

                        <!-- 流行色彩调色板 -->
                        <StackPanel Margin="0,0,0,24">
                            <TextBlock Text="流行色彩"
                                       FontSize="15"
                                       FontWeight="Medium"
                                       Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                       Margin="0,0,0,12"/>

                            <UniformGrid Columns="8" Rows="3">
                                <!-- 第一行：蓝色系 -->
                                <Button Background="#0078D4" Style="{StaticResource AccentColorButtonStyle}" ToolTip="Azure Blue - 微软蓝"
                                        Command="{Binding SelectCustomColorCommand}" CommandParameter="#0078D4" Margin="4"/>
                                <Button Background="#106EBE" Style="{StaticResource AccentColorButtonStyle}" ToolTip="Deep Blue - 深蓝"
                                        Command="{Binding SelectCustomColorCommand}" CommandParameter="#106EBE" Margin="4"/>
                                <Button Background="#005A9E" Style="{StaticResource AccentColorButtonStyle}" ToolTip="Navy Blue - 海军蓝"
                                        Command="{Binding SelectCustomColorCommand}" CommandParameter="#005A9E" Margin="4"/>
                                <Button Background="#004578" Style="{StaticResource AccentColorButtonStyle}" ToolTip="Dark Navy - 深海军蓝"
                                        Command="{Binding SelectCustomColorCommand}" CommandParameter="#004578" Margin="4"/>
                                <Button Background="#40E0D0" Style="{StaticResource AccentColorButtonStyle}" ToolTip="Turquoise - 绿松石"
                                        Command="{Binding SelectCustomColorCommand}" CommandParameter="#40E0D0" Margin="4"/>
                                <Button Background="#00CED1" Style="{StaticResource AccentColorButtonStyle}" ToolTip="Dark Turquoise - 深绿松石"
                                        Command="{Binding SelectCustomColorCommand}" CommandParameter="#00CED1" Margin="4"/>
                                <Button Background="#5F9EA0" Style="{StaticResource AccentColorButtonStyle}" ToolTip="Cadet Blue - 军校蓝"
                                        Command="{Binding SelectCustomColorCommand}" CommandParameter="#5F9EA0" Margin="4"/>
                                <Button Background="#4682B4" Style="{StaticResource AccentColorButtonStyle}" ToolTip="Steel Blue - 钢蓝"
                                        Command="{Binding SelectCustomColorCommand}" CommandParameter="#4682B4" Margin="4"/>

                                <!-- 第二行：绿色系 -->
                                <Button Background="#107C10" Style="{StaticResource AccentColorButtonStyle}" ToolTip="Forest Green - 森林绿"
                                        Command="{Binding SelectCustomColorCommand}" CommandParameter="#107C10" Margin="4"/>
                                <Button Background="#0E700E" Style="{StaticResource AccentColorButtonStyle}" ToolTip="Dark Green - 深绿"
                                        Command="{Binding SelectCustomColorCommand}" CommandParameter="#0E700E" Margin="4"/>
                                <Button Background="#0C5F0C" Style="{StaticResource AccentColorButtonStyle}" ToolTip="Deep Green - 墨绿"
                                        Command="{Binding SelectCustomColorCommand}" CommandParameter="#0C5F0C" Margin="4"/>
                                <Button Background="#0A4F0A" Style="{StaticResource AccentColorButtonStyle}" ToolTip="Pine Green - 松绿"
                                        Command="{Binding SelectCustomColorCommand}" CommandParameter="#0A4F0A" Margin="4"/>
                                <Button Background="#32CD32" Style="{StaticResource AccentColorButtonStyle}" ToolTip="Lime Green - 酸橙绿"
                                        Command="{Binding SelectCustomColorCommand}" CommandParameter="#32CD32" Margin="4"/>
                                <Button Background="#00FF7F" Style="{StaticResource AccentColorButtonStyle}" ToolTip="Spring Green - 春绿"
                                        Command="{Binding SelectCustomColorCommand}" CommandParameter="#00FF7F" Margin="4"/>
                                <Button Background="#98FB98" Style="{StaticResource AccentColorButtonStyle}" ToolTip="Pale Green - 淡绿"
                                        Command="{Binding SelectCustomColorCommand}" CommandParameter="#98FB98" Margin="4"/>
                                <Button Background="#90EE90" Style="{StaticResource AccentColorButtonStyle}" ToolTip="Light Green - 浅绿"
                                        Command="{Binding SelectCustomColorCommand}" CommandParameter="#90EE90" Margin="4"/>

                                <!-- 第三行：暖色系 -->
                                <Button Background="#D13438" Style="{StaticResource AccentColorButtonStyle}" ToolTip="Crimson Red - 深红"
                                        Command="{Binding SelectCustomColorCommand}" CommandParameter="#D13438" Margin="4"/>
                                <Button Background="#FF4500" Style="{StaticResource AccentColorButtonStyle}" ToolTip="Orange Red - 橙红"
                                        Command="{Binding SelectCustomColorCommand}" CommandParameter="#FF4500" Margin="4"/>
                                <Button Background="#FF8C00" Style="{StaticResource AccentColorButtonStyle}" ToolTip="Dark Orange - 深橙"
                                        Command="{Binding SelectCustomColorCommand}" CommandParameter="#FF8C00" Margin="4"/>
                                <Button Background="#FFD700" Style="{StaticResource AccentColorButtonStyle}" ToolTip="Gold - 金色"
                                        Command="{Binding SelectCustomColorCommand}" CommandParameter="#FFD700" Margin="4"/>
                                <Button Background="#9A0089" Style="{StaticResource AccentColorButtonStyle}" ToolTip="Purple - 紫色"
                                        Command="{Binding SelectCustomColorCommand}" CommandParameter="#9A0089" Margin="4"/>
                                <Button Background="#8B008B" Style="{StaticResource AccentColorButtonStyle}" ToolTip="Dark Magenta - 深洋红"
                                        Command="{Binding SelectCustomColorCommand}" CommandParameter="#8B008B" Margin="4"/>
                                <Button Background="#DA70D6" Style="{StaticResource AccentColorButtonStyle}" ToolTip="Orchid - 兰花紫"
                                        Command="{Binding SelectCustomColorCommand}" CommandParameter="#DA70D6" Margin="4"/>
                                <Button Background="#DDA0DD" Style="{StaticResource AccentColorButtonStyle}" ToolTip="Plum - 梅花紫"
                                        Command="{Binding SelectCustomColorCommand}" CommandParameter="#DDA0DD" Margin="4"/>
                            </UniformGrid>
                        </StackPanel>

                        <!-- 中性色调色板 -->
                        <StackPanel Margin="0,0,0,24">
                            <TextBlock Text="中性色调"
                                       FontSize="15"
                                       FontWeight="Medium"
                                       Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                       Margin="0,0,0,12"/>

                            <UniformGrid Columns="8" Rows="1">
                                <Button Background="#000000" Style="{StaticResource AccentColorButtonStyle}" ToolTip="Black - 黑色"
                                        Command="{Binding SelectCustomColorCommand}" CommandParameter="#000000" Margin="4"/>
                                <Button Background="#2D2D30" Style="{StaticResource AccentColorButtonStyle}" ToolTip="Dark Gray - 深灰"
                                        Command="{Binding SelectCustomColorCommand}" CommandParameter="#2D2D30" Margin="4"/>
                                <Button Background="#3E3E42" Style="{StaticResource AccentColorButtonStyle}" ToolTip="Charcoal - 炭灰"
                                        Command="{Binding SelectCustomColorCommand}" CommandParameter="#3E3E42" Margin="4"/>
                                <Button Background="#68768A" Style="{StaticResource AccentColorButtonStyle}" ToolTip="Slate Gray - 石板灰"
                                        Command="{Binding SelectCustomColorCommand}" CommandParameter="#68768A" Margin="4"/>
                                <Button Background="#8A8886" Style="{StaticResource AccentColorButtonStyle}" ToolTip="Gray - 灰色"
                                        Command="{Binding SelectCustomColorCommand}" CommandParameter="#8A8886" Margin="4"/>
                                <Button Background="#C8C6C4" Style="{StaticResource AccentColorButtonStyle}" ToolTip="Light Gray - 浅灰"
                                        Command="{Binding SelectCustomColorCommand}" CommandParameter="#C8C6C4" Margin="4"/>
                                <Button Background="#EDEBE9" Style="{StaticResource AccentColorButtonStyle}" ToolTip="Off White - 米白"
                                        Command="{Binding SelectCustomColorCommand}" CommandParameter="#EDEBE9" Margin="4"/>
                                <Button Background="#FFFFFF" Style="{StaticResource AccentColorButtonStyle}" ToolTip="White - 白色"
                                        Command="{Binding SelectCustomColorCommand}" CommandParameter="#FFFFFF" Margin="4"/>
                            </UniformGrid>
                        </StackPanel>

                        <!-- RGB 颜色选择器 -->
                        <StackPanel>
                            <TextBlock Text="🎨 RGB 颜色选择器"
                                       FontSize="15"
                                       FontWeight="Medium"
                                       Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                       Margin="0,0,0,12"/>

                            <!-- 当前选择的颜色预览 -->
                            <Border Background="{Binding CurrentRgbColor, FallbackValue=#0078D4}"
                                    Height="60"
                                    CornerRadius="8"
                                    BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                    BorderThickness="1"
                                    Margin="0,0,0,16">
                                <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                                    <TextBlock Text="当前颜色"
                                               FontWeight="SemiBold"
                                               FontSize="14"
                                               Foreground="White"
                                               HorizontalAlignment="Center"/>
                                    <TextBlock Text="{Binding CurrentRgbColorHex, FallbackValue=#0078D4}"
                                               FontSize="12"
                                               Foreground="White"
                                               HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>

                            <!-- RGB 滑块 -->
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="30"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="60"/>
                                </Grid.ColumnDefinitions>

                                <!-- Red 滑块 -->
                                <TextBlock Grid.Row="0" Grid.Column="0" Text="R"
                                           VerticalAlignment="Center"
                                           FontWeight="SemiBold"
                                           Foreground="#FF4444"/>
                                <Slider Grid.Row="0" Grid.Column="1"
                                        Minimum="0" Maximum="255"
                                        Value="{Binding RedValue, Mode=TwoWay}"
                                        Margin="8,0"/>
                                <TextBox Grid.Row="0" Grid.Column="2"
                                         Text="{Binding RedValue, Mode=TwoWay}"
                                         Margin="8,0,0,0"
                                         VerticalAlignment="Center"/>

                                <!-- Green 滑块 -->
                                <TextBlock Grid.Row="1" Grid.Column="0" Text="G"
                                           VerticalAlignment="Center"
                                           FontWeight="SemiBold"
                                           Foreground="#44FF44"
                                           Margin="0,8,0,0"/>
                                <Slider Grid.Row="1" Grid.Column="1"
                                        Minimum="0" Maximum="255"
                                        Value="{Binding GreenValue, Mode=TwoWay}"
                                        Margin="8,8,8,0"/>
                                <TextBox Grid.Row="1" Grid.Column="2"
                                         Text="{Binding GreenValue, Mode=TwoWay}"
                                         Margin="8,8,0,0"
                                         VerticalAlignment="Center"/>

                                <!-- Blue 滑块 -->
                                <TextBlock Grid.Row="2" Grid.Column="0" Text="B"
                                           VerticalAlignment="Center"
                                           FontWeight="SemiBold"
                                           Foreground="#4444FF"
                                           Margin="0,8,0,0"/>
                                <Slider Grid.Row="2" Grid.Column="1"
                                        Minimum="0" Maximum="255"
                                        Value="{Binding BlueValue, Mode=TwoWay}"
                                        Margin="8,8,8,0"/>
                                <TextBox Grid.Row="2" Grid.Column="2"
                                         Text="{Binding BlueValue, Mode=TwoWay}"
                                         Margin="8,8,0,0"
                                         VerticalAlignment="Center"/>

                                <!-- 应用按钮 -->
                                <ui:Button Grid.Row="3" Grid.Column="1"
                                           Content="🎨 应用此颜色"
                                           Command="{Binding ApplyRgbColorCommand}"
                                           Appearance="Primary"
                                           Background="{DynamicResource SystemAccentColorPrimaryBrush}"
                                           Margin="8,16,8,0"
                                           HorizontalAlignment="Stretch"/>
                            </Grid>
                        </StackPanel>
                    </StackPanel>
                </ui:Card>

                <!-- 预览效果 -->
                <ui:Card Padding="24" Margin="0,0,0,32">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,24">
                            <ui:SymbolIcon Symbol="Eye24" 
                                           FontSize="24"
                                           Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                            <StackPanel>
                                <TextBlock Text="预览效果"
                                           FontSize="18"
                                           FontWeight="SemiBold"
                                           Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                                <TextBlock Text="查看当前主题设置的效果"
                                           FontSize="14"
                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                            </StackPanel>
                        </StackPanel>

                        <StackPanel>
                            <!-- 按钮预览 -->
                            <WrapPanel Margin="0,0,0,16">
                                <ui:Button Content="主要按钮"
                                           Appearance="Primary"
                                           
                                           Background="{DynamicResource SystemAccentColorPrimaryBrush}"
                                           Margin="0,0,16,8"/>
                                <ui:Button Content="次要按钮"
                                           Appearance="Secondary"
                                           Margin="0,0,16,8"/>
                                <ui:Button Content="透明按钮"
                                           Appearance="Transparent"
                                           Margin="0,0,16,8"/>
                            </WrapPanel>

                            <!-- 文本预览 -->
                            <StackPanel Margin="0,0,0,24">
                                <TextBlock Text="这是主要文本"
                                           Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                           FontWeight="SemiBold"
                                           Margin="0,0,0,8"/>
                                <TextBlock Text="这是次要文本"
                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                           Margin="0,0,0,8"/>
                                <TextBlock Text="这是强调色文本"
                                           Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                           FontWeight="SemiBold"/>
                            </StackPanel>
                        </StackPanel>

                        <!-- 高级设置按钮 -->
                        <ui:Button Content="🎨 高级颜色设置"
                                   Appearance="Secondary"
                                   Command="{Binding NavigateToColorShowcaseCommand}"
                                   HorizontalAlignment="Left"/>
                    </StackPanel>
                </ui:Card>

                <!-- 强调色效果展示 -->
                <ui:Card Padding="24" Margin="0,0,0,32">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,24">
                            <ui:SymbolIcon Symbol="ColorBackground24"
                                           FontSize="24"
                                           Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                            <StackPanel Margin="16,0,0,0">
                                <TextBlock Text="强调色效果展示"
                                           FontSize="18"
                                           FontWeight="SemiBold"
                                           Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                                <TextBlock Text="查看当前强调色的所有变体效果"
                                           FontSize="14"
                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                            </StackPanel>
                        </StackPanel>

                        <StackPanel>
                            <!-- System强调色系列 -->
                            <TextBlock Text="System强调色系列 (用于文本、边框、图标):"
                                       FontWeight="SemiBold"
                                       Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- SystemAccentColorPrimaryBrush -->
                            <Border Background="{DynamicResource SystemAccentColorPrimaryBrush}"
                                    Height="40"
                                    CornerRadius="4"
                                    BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                    BorderThickness="1"
                                    Margin="0,0,0,8"
                                    Cursor="Hand"
                                
                                    Tag="SystemAccentColorPrimaryBrush"
                                    ToolTip="点击复制资源名称">
                                <TextBlock Text="SystemAccentColorPrimaryBrush"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"
                                           FontWeight="SemiBold"
                                           FontSize="12"
                                           Foreground="{DynamicResource TextFillColorInverseBrush}"/>
                            </Border>

                            <!-- SystemAccentColorSecondaryBrush -->
                            <Border Background="{DynamicResource SystemAccentColorSecondaryBrush}"
                                    Height="40"
                                    CornerRadius="4"
                                    BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                    BorderThickness="1"
                                    Margin="0,0,0,8"
                                    Cursor="Hand"
                              
                                    Tag="SystemAccentColorSecondaryBrush"
                                    ToolTip="点击复制资源名称">
                                <TextBlock Text="SystemAccentColorSecondaryBrush"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"
                                           FontWeight="SemiBold"
                                           FontSize="12"
                                           Foreground="{DynamicResource TextFillColorInverseBrush}"/>
                            </Border>

                            <!-- SystemAccentColorTertiaryBrush -->
                            <Border Background="{DynamicResource SystemAccentColorTertiaryBrush}"
                                    Height="40"
                                    CornerRadius="4"
                                    BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                    BorderThickness="1"
                                    Margin="0,0,0,24"
                                    Cursor="Hand"
                                
                                    Tag="SystemAccentColorTertiaryBrush"
                                    ToolTip="点击复制资源名称">
                                <TextBlock Text="SystemAccentColorTertiaryBrush"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"
                                           FontWeight="SemiBold"
                                           FontSize="12"
                                           Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                            </Border>

                            <!-- AccentFill强调色系列 -->
                            <TextBlock Text="AccentFill强调色系列 (用于按钮背景、控件填充):"
                                       FontWeight="SemiBold"
                                       Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- AccentFillColorDefaultBrush -->
                            <Border Background="{DynamicResource AccentFillColorDefaultBrush}"
                                    Height="40"
                                    CornerRadius="4"
                                    BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                    BorderThickness="1"
                                    Margin="0,0,0,8"
                                    Cursor="Hand"
                             
                                    Tag="AccentFillColorDefaultBrush"
                                    ToolTip="点击复制资源名称">
                                <TextBlock Text="AccentFillColorDefaultBrush"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"
                                           FontWeight="SemiBold"
                                           FontSize="12"
                                           Foreground="{DynamicResource TextFillColorInverseBrush}"/>
                            </Border>

                            <!-- AccentFillColorSecondaryBrush -->
                            <Border Background="{DynamicResource AccentFillColorSecondaryBrush}"
                                    Height="40"
                                    CornerRadius="4"
                                    BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                    BorderThickness="1"
                                    Margin="0,0,0,8"
                                    Cursor="Hand"
                                    
                                    Tag="AccentFillColorSecondaryBrush"
                                    ToolTip="点击复制资源名称">
                                <TextBlock Text="AccentFillColorSecondaryBrush"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"
                                           FontWeight="SemiBold"
                                           FontSize="12"
                                           Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                            </Border>

                            <!-- AccentFillColorTertiaryBrush -->
                            <Border Background="{DynamicResource AccentFillColorTertiaryBrush}"
                                    Height="40"
                                    CornerRadius="4"
                                    BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                    BorderThickness="1"
                                    Margin="0,0,0,8"
                                    Cursor="Hand"
                                   
                                    Tag="AccentFillColorTertiaryBrush"
                                    ToolTip="点击复制资源名称">
                                <TextBlock Text="AccentFillColorTertiaryBrush"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"
                                           FontWeight="SemiBold"
                                           FontSize="12"
                                           Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                            </Border>

                            <!-- AccentFillColorDisabledBrush -->
                            <Border Background="{DynamicResource AccentFillColorDisabledBrush}"
                                    Height="40"
                                    CornerRadius="4"
                                    BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                    BorderThickness="1"
                                    Margin="0,0,0,0"
                                    Cursor="Hand"
                                    
                                    Tag="AccentFillColorDisabledBrush"
                                    ToolTip="点击复制资源名称">
                                <TextBlock Text="AccentFillColorDisabledBrush"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"
                                           FontWeight="SemiBold"
                                           FontSize="12"
                                           Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                            </Border>
                        </StackPanel>
                    </StackPanel>
                </ui:Card>

            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
