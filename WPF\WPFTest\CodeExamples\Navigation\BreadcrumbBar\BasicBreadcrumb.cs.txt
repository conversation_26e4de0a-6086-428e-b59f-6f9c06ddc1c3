using System;
using System.Collections.ObjectModel;
using System.Linq;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace WPFTest.ViewModels.Navigation
{
    /// <summary>
    /// 面包屑项模型
    /// </summary>
    public class BreadcrumbItemModel
    {
        public string Name { get; set; } = string.Empty;
        public string Tag { get; set; } = string.Empty;
        public string Icon { get; set; } = "📄";
        public string Description { get; set; } = string.Empty;
        public DateTime CreatedTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 基础面包屑导航示例 ViewModel
    /// </summary>
    public partial class BasicBreadcrumbViewModel : ObservableObject
    {
        /// <summary>
        /// 面包屑项集合
        /// </summary>
        [ObservableProperty]
        public partial ObservableCollection<BreadcrumbItemModel> BreadcrumbItems { get; set; } = new();

        /// <summary>
        /// 当前路径
        /// </summary>
        [ObservableProperty]
        public partial string CurrentPath { get; set; } = "/首页";

        /// <summary>
        /// 最后更新时间
        /// </summary>
        [ObservableProperty]
        public partial DateTime LastUpdated { get; set; } = DateTime.Now;

        /// <summary>
        /// 构造函数
        /// </summary>
        public BasicBreadcrumbViewModel()
        {
            InitializeBreadcrumbItems();
        }

        /// <summary>
        /// 导航到指定页面命令
        /// </summary>
        [RelayCommand]
        private void NavigateTo(string target)
        {
            try
            {
                // 根据目标添加面包屑项
                var newItem = target switch
                {
                    "home" => new BreadcrumbItemModel
                    {
                        Name = "首页",
                        Tag = "home",
                        Icon = "🏠",
                        Description = "应用程序主页"
                    },
                    "projects" => new BreadcrumbItemModel
                    {
                        Name = "项目管理",
                        Tag = "projects",
                        Icon = "📁",
                        Description = "管理所有项目文件和配置"
                    },
                    "docs" => new BreadcrumbItemModel
                    {
                        Name = "文档中心",
                        Tag = "docs",
                        Icon = "📄",
                        Description = "查看和编辑项目文档"
                    },
                    "settings" => new BreadcrumbItemModel
                    {
                        Name = "系统设置",
                        Tag = "settings",
                        Icon = "⚙️",
                        Description = "配置系统参数和选项"
                    },
                    _ => new BreadcrumbItemModel
                    {
                        Name = target,
                        Tag = target,
                        Icon = "📄",
                        Description = $"导航到 {target}"
                    }
                };

                // 检查是否已存在相同的项
                var existingItem = BreadcrumbItems.FirstOrDefault(x => x.Tag == target);
                if (existingItem != null)
                {
                    // 移除该项之后的所有项
                    var index = BreadcrumbItems.IndexOf(existingItem);
                    for (int i = BreadcrumbItems.Count - 1; i > index; i--)
                    {
                        BreadcrumbItems.RemoveAt(i);
                    }
                }
                else
                {
                    // 添加新项
                    BreadcrumbItems.Add(newItem);
                }

                UpdateCurrentPath();
                LastUpdated = DateTime.Now;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"导航失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 向上导航命令
        /// </summary>
        [RelayCommand]
        private void NavigateUp()
        {
            try
            {
                if (BreadcrumbItems.Count > 1)
                {
                    BreadcrumbItems.RemoveAt(BreadcrumbItems.Count - 1);
                    UpdateCurrentPath();
                    LastUpdated = DateTime.Now;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"向上导航失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 重置面包屑命令
        /// </summary>
        [RelayCommand]
        private void ResetBreadcrumb()
        {
            try
            {
                InitializeBreadcrumbItems();
                UpdateCurrentPath();
                LastUpdated = DateTime.Now;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"重置面包屑失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示统计信息命令
        /// </summary>
        [RelayCommand]
        private void ShowStats()
        {
            try
            {
                var stats = $"面包屑项数量: {BreadcrumbItems.Count}\n当前路径: {CurrentPath}\n最后更新: {LastUpdated:yyyy-MM-dd HH:mm:ss}";
                System.Diagnostics.Debug.WriteLine($"面包屑统计:\n{stats}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示统计失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化面包屑项
        /// </summary>
        private void InitializeBreadcrumbItems()
        {
            BreadcrumbItems.Clear();
            BreadcrumbItems.Add(new BreadcrumbItemModel
            {
                Name = "首页",
                Tag = "home",
                Icon = "🏠",
                Description = "应用程序主页"
            });
        }

        /// <summary>
        /// 更新当前路径
        /// </summary>
        private void UpdateCurrentPath()
        {
            CurrentPath = "/" + string.Join("/", BreadcrumbItems.Select(x => x.Name));
        }
    }
}
