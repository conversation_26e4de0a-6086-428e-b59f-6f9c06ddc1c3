using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Windows;
using System.IO;
using System.Reflection;
using System.Collections.ObjectModel;
using System.ComponentModel;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels.LayoutControls
{
    /// <summary>
    /// GroupBox 页面的 ViewModel，演示 GroupBox 控件的各种功能
    /// </summary>
    public partial class GroupBoxViewModel : ObservableObject
    {
        private readonly YLoggerInstance _logger = YLogger.For<GroupBoxViewModel>();

        #region 属性

        /// <summary>
        /// 交互次数
        /// </summary>
        [ObservableProperty]
        private int interactionCount = 0;

        /// <summary>
        /// 最后执行的操作
        /// </summary>
        [ObservableProperty]
        private string lastAction = "无操作";

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        private string statusMessage = "欢迎使用 GroupBox 示例库！";

        #region 代码示例属性

        /// <summary>
        /// 基础功能 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 基础功能 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级功能 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级功能 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 样式 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string StylesXamlExample { get; set; } = string.Empty;

        #endregion

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化 GroupBoxViewModel
        /// </summary>
        public GroupBoxViewModel()
        {
            try
            {
                _logger.Info("🚀 GroupBox 页面 ViewModel 开始初始化");

                StatusMessage = "GroupBox 示例库已加载，开始体验各种分组功能！";
                InitializeCodeExamples();

                _logger.Info("✅ GroupBox 页面 ViewModel 初始化完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ GroupBox 页面 ViewModel 初始化失败: {ex.Message}");
                StatusMessage = "初始化失败，请检查日志";
            }
        }

        #endregion

        #region 命令

        /// <summary>
        /// 通用交互命令
        /// </summary>
        /// <param name="parameter">操作参数</param>
        [RelayCommand]
        private void HandleInteraction(string? parameter)
        {
            try
            {
                InteractionCount++;
                LastAction = parameter ?? "未知操作";
                
                _logger.Info($"🎯 用户交互操作: {parameter}");
                _logger.Info($"🔢 当前交互次数: {InteractionCount}");

                // 根据不同的操作显示不同的状态消息
                StatusMessage = parameter switch
                {
                    "应用设置" => "🎯 应用了设置选项",
                    "执行重要操作" => "🎯 执行了重要功能操作",
                    "保存配置" => "🎯 保存了配置信息",
                    "重置配置" => "🎯 重置了配置信息",
                    _ => $"🎯 执行了操作: {parameter}"
                };
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 处理用户交互失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 重置计数命令
        /// </summary>
        [RelayCommand]
        private void ResetCount()
        {
            InteractionCount = 0;
            StatusMessage = "计数已重置";
            _logger.Info("重置了交互计数");
        }

        /// <summary>
        /// 清除状态命令
        /// </summary>
        [RelayCommand]
        private void ClearStatus()
        {
            LastAction = "无操作";
            StatusMessage = "状态已清除";
            _logger.Info("清除了状态信息");
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化代码示例
        /// </summary>
        private void InitializeCodeExamples()
        {
            try
            {
                // 获取代码示例文件的基础路径
                var baseDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
                var codeExamplesPath = Path.Combine(baseDirectory, "CodeExamples", "LayoutControls", "GroupBox");

                _logger.Info($"基础目录: {baseDirectory}");
                _logger.Info($"代码示例路径: {codeExamplesPath}");
                _logger.Info($"目录是否存在: {Directory.Exists(codeExamplesPath)}");

                // 读取各种示例文件
                BasicXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.xaml.txt"));
                BasicCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.cs.txt"));
                AdvancedXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Advanced.xaml.txt"));
                AdvancedCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Advanced.cs.txt"));
                StylesXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Styles.xaml.txt"));

                _logger.Info("GroupBox 代码示例加载完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"加载 GroupBox 代码示例失败: {ex.Message}");
                SetDefaultCodeExamples();
            }
        }

        /// <summary>
        /// 读取代码示例文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件内容</returns>
        private string ReadCodeExampleFile(string filePath)
        {
            _logger.Info($"尝试读取文件: {filePath}");
            
            if (File.Exists(filePath))
            {
                var content = File.ReadAllText(filePath);
                _logger.Info($"成功读取文件，内容长度: {content.Length}");
                return content;
            }
            else
            {
                _logger.Warning($"代码示例文件不存在: {filePath}");
                return $"// 代码示例文件不存在: {Path.GetFileName(filePath)}\n// 完整路径: {filePath}";
            }
        }

        /// <summary>
        /// 设置默认代码示例（当文件读取失败时使用）
        /// </summary>
        private void SetDefaultCodeExamples()
        {
            BasicXamlExample = @"<!-- GroupBox 基础示例 -->
<GroupBox Header=""用户信息"" 
          Background=""{DynamicResource ControlFillColorDefaultBrush}""
          BorderBrush=""{DynamicResource ControlStrokeColorDefaultBrush}""
          BorderThickness=""1"">
    <StackPanel Margin=""12"">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width=""Auto""/>
                <ColumnDefinition Width=""*""/>
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height=""Auto""/>
                <RowDefinition Height=""Auto""/>
            </Grid.RowDefinitions>
            
            <TextBlock Grid.Row=""0"" Grid.Column=""0"" Text=""姓名:"" Margin=""0,0,8,8""/>
            <TextBox Grid.Row=""0"" Grid.Column=""1"" Text=""张三"" Margin=""0,0,0,8""/>
            
            <TextBlock Grid.Row=""1"" Grid.Column=""0"" Text=""年龄:"" Margin=""0,0,8,0""/>
            <TextBox Grid.Row=""1"" Grid.Column=""1"" Text=""25""/>
        </Grid>
    </StackPanel>
</GroupBox>";

            BasicCSharpExample = @"// GroupBox C# 基础示例
// GroupBox 是 WPF 中的分组控件，用于逻辑分组界面元素

using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace WPFTest.Examples.LayoutControls
{
    public class GroupBoxBasicExample
    {
        /// <summary>
        /// 创建基础 GroupBox 控件
        /// </summary>
        public static GroupBox CreateBasicGroupBox()
        {
            var groupBox = new GroupBox
            {
                Header = ""用户信息"",
                Background = new SolidColorBrush(Colors.LightBlue),
                BorderBrush = new SolidColorBrush(Colors.Blue),
                BorderThickness = new Thickness(1),
                Margin = new Thickness(8),
                Padding = new Thickness(12)
            };

            // 创建内容
            var grid = new Grid
            {
                Margin = new Thickness(12)
            };

            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            var nameLabel = new TextBlock
            {
                Text = ""姓名:"",
                Margin = new Thickness(0, 0, 8, 8)
            };
            Grid.SetRow(nameLabel, 0);
            Grid.SetColumn(nameLabel, 0);
            grid.Children.Add(nameLabel);

            var nameTextBox = new TextBox
            {
                Text = ""张三"",
                Margin = new Thickness(0, 0, 0, 8)
            };
            Grid.SetRow(nameTextBox, 0);
            Grid.SetColumn(nameTextBox, 1);
            grid.Children.Add(nameTextBox);

            groupBox.Content = grid;
            return groupBox;
        }
    }
}";

            AdvancedXamlExample = @"<!-- GroupBox 高级示例 -->
<!-- 嵌套 GroupBox -->
<GroupBox Header=""嵌套分组示例"">
    <StackPanel Margin=""16"">
        <TextBlock Text=""这是父级分组的内容。"" Margin=""0,0,0,12""/>
        
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width=""*""/>
                <ColumnDefinition Width=""*""/>
            </Grid.ColumnDefinitions>
            
            <GroupBox Grid.Column=""0"" Header=""子分组 A"" Margin=""0,0,8,0"">
                <StackPanel Margin=""12"">
                    <RadioButton Content=""选项 A1"" GroupName=""GroupA"" IsChecked=""True""/>
                    <RadioButton Content=""选项 A2"" GroupName=""GroupA""/>
                </StackPanel>
            </GroupBox>
            
            <GroupBox Grid.Column=""1"" Header=""子分组 B"" Margin=""8,0,0,0"">
                <StackPanel Margin=""12"">
                    <RadioButton Content=""选项 B1"" GroupName=""GroupB""/>
                    <RadioButton Content=""选项 B2"" GroupName=""GroupB"" IsChecked=""True""/>
                </StackPanel>
            </GroupBox>
        </Grid>
    </StackPanel>
</GroupBox>";

            AdvancedCSharpExample = @"// GroupBox 高级 C# 示例
// 展示如何通过代码创建复杂的 GroupBox 结构

using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace WPFTest.Examples.LayoutControls
{
    public class GroupBoxAdvancedExample
    {
        /// <summary>
        /// 创建嵌套 GroupBox
        /// </summary>
        public static GroupBox CreateNestedGroupBox()
        {
            var parentGroupBox = new GroupBox
            {
                Header = ""嵌套分组示例"",
                Background = new SolidColorBrush(Colors.LightGray),
                BorderBrush = new SolidColorBrush(Colors.Gray),
                BorderThickness = new Thickness(1),
                Margin = new Thickness(8)
            };

            var parentContent = new StackPanel
            {
                Margin = new Thickness(16)
            };

            parentContent.Children.Add(new TextBlock
            {
                Text = ""这是父级分组的内容。"",
                Margin = new Thickness(0, 0, 0, 12)
            });

            var grid = new Grid();
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            // 创建子分组 A
            var childGroupBoxA = new GroupBox
            {
                Header = ""子分组 A"",
                Background = new SolidColorBrush(Colors.LightBlue),
                Margin = new Thickness(0, 0, 8, 0)
            };

            var childContentA = new StackPanel
            {
                Margin = new Thickness(12)
            };

            childContentA.Children.Add(new RadioButton
            {
                Content = ""选项 A1"",
                GroupName = ""GroupA"",
                IsChecked = true
            });

            childContentA.Children.Add(new RadioButton
            {
                Content = ""选项 A2"",
                GroupName = ""GroupA""
            });

            childGroupBoxA.Content = childContentA;
            Grid.SetColumn(childGroupBoxA, 0);
            grid.Children.Add(childGroupBoxA);

            parentContent.Children.Add(grid);
            parentGroupBox.Content = parentContent;

            return parentGroupBox;
        }
    }
}";

            StylesXamlExample = @"<!-- GroupBox 样式示例 -->
<!-- 在 Zylo.WPF 中定义的 GroupBox 样式 -->

<!-- 基础样式 -->
<Style x:Key=""GroupBoxBaseStyle"" TargetType=""GroupBox"">
    <Setter Property=""Background"" Value=""{DynamicResource ControlFillColorDefaultBrush}""/>
    <Setter Property=""BorderBrush"" Value=""{DynamicResource ControlStrokeColorDefaultBrush}""/>
    <Setter Property=""BorderThickness"" Value=""1""/>
    <Setter Property=""Padding"" Value=""8""/>
    <Setter Property=""Margin"" Value=""4""/>
</Style>

<!-- 现代化样式 -->
<Style x:Key=""ModernGroupBoxStyle"" TargetType=""GroupBox"" BasedOn=""{StaticResource GroupBoxBaseStyle}"">
    <Setter Property=""Background"" Value=""{DynamicResource CardBackgroundFillColorDefaultBrush}""/>
    <Setter Property=""BorderThickness"" Value=""0""/>
    <Setter Property=""Effect"">
        <Setter.Value>
            <DropShadowEffect Color=""Black"" Opacity=""0.1"" ShadowDepth=""4"" BlurRadius=""8""/>
        </Setter.Value>
    </Setter>
</Style>

<!-- 强调样式 -->
<Style x:Key=""AccentGroupBoxStyle"" TargetType=""GroupBox"" BasedOn=""{StaticResource GroupBoxBaseStyle}"">
    <Setter Property=""Background"" Value=""{DynamicResource AccentFillColorDefaultBrush}""/>
    <Setter Property=""BorderBrush"" Value=""{DynamicResource AccentStrokeColorDefaultBrush}""/>
    <Setter Property=""BorderThickness"" Value=""2""/>
</Style>";
        }

        #endregion
    }
}
