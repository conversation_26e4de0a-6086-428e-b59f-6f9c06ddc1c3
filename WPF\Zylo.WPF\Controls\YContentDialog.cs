namespace Zylo.WPF.Controls
{
    /// <summary>
    /// 封装的 WPF-UI ContentDialog，简化使用
    /// </summary>
    public static class YContentDialog
    {
        /// <summary>
        /// 显示输入对话框
        /// </summary>
        /// <param name="title">标题</param>
        /// <param name="prompt">提示文本</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>输入结果 (success, inputText)</returns>
        public static async Task<(bool success, string inputText)> ShowInputAsync(string title, string prompt = "", string defaultValue = "")
        {
            var dialog = new Wpf.Ui.Controls.ContentDialog
            {
                Title = title,
                PrimaryButtonText = "确定",
                CloseButtonText = "取消",
                DefaultButton = Wpf.Ui.Controls.ContentDialogButton.Primary
            };

            var stackPanel = new StackPanel();

            if (!string.IsNullOrEmpty(prompt))
            {
                stackPanel.Children.Add(new System.Windows.Controls.TextBlock 
                { 
                    Text = prompt, 
                    Margin = new Thickness(0, 0, 0, 8),
                    FontSize = 14
                });
            }

            var textBox = new Wpf.Ui.Controls.TextBox
            {
                Text = defaultValue,
                Margin = new Thickness(0, 0, 0, 8),
                MinWidth = 300
            };
            stackPanel.Children.Add(textBox);

            dialog.Content = stackPanel;

            SetupContentDialog(dialog);
            var result = await dialog.ShowAsync();

            if (result == Wpf.Ui.Controls.ContentDialogResult.Primary)
            {
                return (true, textBox.Text?.Trim() ?? "");
            }

            return (false, "");
        }

        /// <summary>
        /// 显示重命名对话框
        /// </summary>
        /// <param name="currentName">当前名称（不含扩展名）</param>
        /// <param name="currentExtension">当前扩展名</param>
        /// <param name="isFile">是否为文件</param>
        /// <returns>重命名结果 (success, newName, newExtension)</returns>
        public static async Task<(bool success, string newName, string newExtension)> ShowRenameAsync(
            string currentName, string currentExtension, bool isFile)
        {
            var dialog = new Wpf.Ui.Controls.ContentDialog
            {
                Title = "重命名",
                PrimaryButtonText = "确定",
                CloseButtonText = "取消",
                DefaultButton = Wpf.Ui.Controls.ContentDialogButton.Primary
            };

            var stackPanel = new StackPanel();

            // 文件名输入
            stackPanel.Children.Add(new System.Windows.Controls.TextBlock 
            { 
                Text = "请输入新名称:", 
                Margin = new Thickness(0, 0, 0, 8),
                FontSize = 14
            });

            var nameTextBox = new Wpf.Ui.Controls.TextBox
            {
                Text = currentName,
                Margin = new Thickness(0, 0, 0, 8),
                MinWidth = 300
            };
            stackPanel.Children.Add(nameTextBox);

            // 如果是文件，添加扩展名选项
            Wpf.Ui.Controls.TextBox extensionTextBox = null;
            System.Windows.Controls.CheckBox changeExtensionCheckBox = null;

            if (isFile)
            {
                changeExtensionCheckBox = new System.Windows.Controls.CheckBox
                {
                    Content = "同时修改文件扩展名",
                    Margin = new Thickness(0, 8, 0, 8),
                    IsChecked = false
                };
                stackPanel.Children.Add(changeExtensionCheckBox);

                stackPanel.Children.Add(new System.Windows.Controls.TextBlock 
                { 
                    Text = "文件扩展名:", 
                    Margin = new Thickness(0, 8, 0, 4),
                    FontSize = 12
                });

                extensionTextBox = new Wpf.Ui.Controls.TextBox
                {
                    Text = currentExtension,
                    Margin = new Thickness(0, 0, 0, 8),
                    MinWidth = 300,
                    IsEnabled = false
                };
                stackPanel.Children.Add(extensionTextBox);

                // 绑定复选框事件
                changeExtensionCheckBox.Checked += (s, e) => extensionTextBox.IsEnabled = true;
                changeExtensionCheckBox.Unchecked += (s, e) =>
                {
                    extensionTextBox.IsEnabled = false;
                    extensionTextBox.Text = currentExtension;
                };
            }

            dialog.Content = stackPanel;

            SetupContentDialog(dialog);
            var result = await dialog.ShowAsync();

            if (result == Wpf.Ui.Controls.ContentDialogResult.Primary)
            {
                var newName = nameTextBox.Text?.Trim();
                var newExtension = currentExtension;

                if (isFile && changeExtensionCheckBox?.IsChecked == true && extensionTextBox != null)
                {
                    var ext = extensionTextBox.Text?.Trim();
                    if (!string.IsNullOrEmpty(ext))
                    {
                        if (!ext.StartsWith("."))
                        {
                            ext = "." + ext;
                        }
                        newExtension = ext;
                    }
                }

                return (true, newName ?? "", newExtension);
            }

            return (false, "", "");
        }

        /// <summary>
        /// 显示内容对话框
        /// </summary>
        /// <param name="title">标题</param>
        /// <param name="content">内容控件</param>
        /// <returns>对话框结果</returns>
        public static async Task<Wpf.Ui.Controls.ContentDialogResult> ShowContentAsync(string title, object content)
        {
            var dialog = new Wpf.Ui.Controls.ContentDialog
            {
                Title = title,
                Content = content,
                PrimaryButtonText = "确定",
                DefaultButton = Wpf.Ui.Controls.ContentDialogButton.Primary
            };

            SetupContentDialog(dialog);
            return await dialog.ShowAsync();
        }

        /// <summary>
        /// 设置 ContentDialog 的 DialogHost
        /// </summary>
        private static void SetupContentDialog(Wpf.Ui.Controls.ContentDialog dialog)
        {
            if (Application.Current.MainWindow != null)
            {
                var contentPresenter = Application.Current.MainWindow.FindName("RootContentDialogPresenter") as ContentPresenter;
                if (contentPresenter != null)
                {
#pragma warning disable CS0618 // 类型或成员已过时
                    dialog.DialogHost = contentPresenter;
#pragma warning restore CS0618 // 类型或成员已过时
                }
            }
        }
    }
}
