using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace WPFTest.ViewModels.Navigation
{
    /// <summary>
    /// 导航历史项模型
    /// </summary>
    public class NavigationHistoryItem
    {
        public string Name { get; set; } = string.Empty;
        public string Icon { get; set; } = "📄";
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 高级面包屑导航示例 ViewModel
    /// </summary>
    public partial class AdvancedBreadcrumbViewModel : ObservableObject
    {
        /// <summary>
        /// 面包屑项集合
        /// </summary>
        [ObservableProperty]
        public partial ObservableCollection<BreadcrumbItemModel> BreadcrumbItems { get; set; } = new();

        /// <summary>
        /// 当前路径
        /// </summary>
        [ObservableProperty]
        public partial string CurrentPath { get; set; } = "/首页";

        /// <summary>
        /// 最后更新时间
        /// </summary>
        [ObservableProperty]
        public partial DateTime LastUpdated { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否显示图标
        /// </summary>
        [ObservableProperty]
        public partial bool ShowIcons { get; set; } = true;

        /// <summary>
        /// 是否显示描述
        /// </summary>
        [ObservableProperty]
        public partial bool ShowDescriptions { get; set; } = true;

        /// <summary>
        /// 是否紧凑模式
        /// </summary>
        [ObservableProperty]
        public partial bool CompactMode { get; set; } = false;

        /// <summary>
        /// 导航历史记录
        /// </summary>
        [ObservableProperty]
        public partial ObservableCollection<NavigationHistoryItem> NavigationHistory { get; set; } = new();

        /// <summary>
        /// 总导航次数
        /// </summary>
        [ObservableProperty]
        public partial int TotalNavigations { get; set; } = 0;

        /// <summary>
        /// 当前选中的面包屑项
        /// </summary>
        [ObservableProperty]
        public partial BreadcrumbItemModel? SelectedBreadcrumbItem { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public AdvancedBreadcrumbViewModel()
        {
            InitializeBreadcrumbItems();
            InitializeNavigationHistory();
        }

        /// <summary>
        /// 导航到指定页面命令
        /// </summary>
        [RelayCommand]
        private void NavigateTo(string target)
        {
            try
            {
                // 根据目标创建面包屑项
                var newItem = CreateBreadcrumbItem(target);

                // 检查是否已存在相同的项
                var existingItem = BreadcrumbItems.FirstOrDefault(x => x.Tag == target);
                if (existingItem != null)
                {
                    // 移除该项之后的所有项
                    var index = BreadcrumbItems.IndexOf(existingItem);
                    for (int i = BreadcrumbItems.Count - 1; i > index; i--)
                    {
                        BreadcrumbItems.RemoveAt(i);
                    }
                }
                else
                {
                    // 添加新项
                    BreadcrumbItems.Add(newItem);
                }

                // 添加到导航历史
                AddToNavigationHistory(newItem);

                UpdateCurrentPath();
                LastUpdated = DateTime.Now;
                TotalNavigations++;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"导航失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 面包屑选择改变命令
        /// </summary>
        [RelayCommand]
        private void BreadcrumbSelectionChanged(object selectedItem)
        {
            try
            {
                if (selectedItem is BreadcrumbItemModel item)
                {
                    SelectedBreadcrumbItem = item;
                    
                    // 移除选中项之后的所有项
                    var index = BreadcrumbItems.IndexOf(item);
                    for (int i = BreadcrumbItems.Count - 1; i > index; i--)
                    {
                        BreadcrumbItems.RemoveAt(i);
                    }

                    UpdateCurrentPath();
                    LastUpdated = DateTime.Now;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"面包屑选择改变失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 回到首页命令
        /// </summary>
        [RelayCommand]
        private void GoHome()
        {
            try
            {
                BreadcrumbItems.Clear();
                InitializeBreadcrumbItems();
                UpdateCurrentPath();
                LastUpdated = DateTime.Now;
                TotalNavigations++;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"回到首页失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 复制路径命令
        /// </summary>
        [RelayCommand]
        private void CopyPath()
        {
            try
            {
                Clipboard.SetText(CurrentPath);
                System.Diagnostics.Debug.WriteLine($"路径已复制到剪贴板: {CurrentPath}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"复制路径失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 刷新命令
        /// </summary>
        [RelayCommand]
        private void Refresh()
        {
            try
            {
                LastUpdated = DateTime.Now;
                System.Diagnostics.Debug.WriteLine("面包屑已刷新");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"刷新失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建面包屑项
        /// </summary>
        private BreadcrumbItemModel CreateBreadcrumbItem(string target)
        {
            return target switch
            {
                "home" => new BreadcrumbItemModel
                {
                    Name = "首页",
                    Tag = "home",
                    Icon = "🏠",
                    Description = "应用程序主页"
                },
                "projects" => new BreadcrumbItemModel
                {
                    Name = "项目管理",
                    Tag = "projects",
                    Icon = "📁",
                    Description = "管理所有项目文件和配置"
                },
                "docs" => new BreadcrumbItemModel
                {
                    Name = "文档中心",
                    Tag = "docs",
                    Icon = "📄",
                    Description = "查看和编辑项目文档"
                },
                "settings" => new BreadcrumbItemModel
                {
                    Name = "系统设置",
                    Tag = "settings",
                    Icon = "⚙️",
                    Description = "配置系统参数和选项"
                },
                "team" => new BreadcrumbItemModel
                {
                    Name = "团队管理",
                    Tag = "team",
                    Icon = "👥",
                    Description = "管理团队成员和权限"
                },
                "tools" => new BreadcrumbItemModel
                {
                    Name = "开发工具",
                    Tag = "tools",
                    Icon = "🔧",
                    Description = "开发和调试工具集合"
                },
                "messages" => new BreadcrumbItemModel
                {
                    Name = "消息中心",
                    Tag = "messages",
                    Icon = "💬",
                    Description = "查看和管理消息通知"
                },
                "reports" => new BreadcrumbItemModel
                {
                    Name = "报告分析",
                    Tag = "reports",
                    Icon = "📊",
                    Description = "查看数据报告和分析"
                },
                "about" => new BreadcrumbItemModel
                {
                    Name = "关于我们",
                    Tag = "about",
                    Icon = "ℹ️",
                    Description = "应用程序信息和版本"
                },
                _ => new BreadcrumbItemModel
                {
                    Name = target,
                    Tag = target,
                    Icon = "📄",
                    Description = $"导航到 {target}"
                }
            };
        }

        /// <summary>
        /// 添加到导航历史
        /// </summary>
        private void AddToNavigationHistory(BreadcrumbItemModel item)
        {
            NavigationHistory.Insert(0, new NavigationHistoryItem
            {
                Name = item.Name,
                Icon = item.Icon,
                Timestamp = DateTime.Now
            });

            // 限制历史记录数量
            while (NavigationHistory.Count > 10)
            {
                NavigationHistory.RemoveAt(NavigationHistory.Count - 1);
            }
        }

        /// <summary>
        /// 初始化面包屑项
        /// </summary>
        private void InitializeBreadcrumbItems()
        {
            BreadcrumbItems.Clear();
            BreadcrumbItems.Add(new BreadcrumbItemModel
            {
                Name = "首页",
                Tag = "home",
                Icon = "🏠",
                Description = "应用程序主页"
            });
        }

        /// <summary>
        /// 初始化导航历史
        /// </summary>
        private void InitializeNavigationHistory()
        {
            NavigationHistory.Clear();
            NavigationHistory.Add(new NavigationHistoryItem
            {
                Name = "首页",
                Icon = "🏠",
                Timestamp = DateTime.Now
            });
        }

        /// <summary>
        /// 更新当前路径
        /// </summary>
        private void UpdateCurrentPath()
        {
            CurrentPath = "/" + string.Join("/", BreadcrumbItems.Select(x => x.Name));
        }
    }
}
