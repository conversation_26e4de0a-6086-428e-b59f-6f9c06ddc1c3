// ListView 高级功能 C# 代码示例
// 展示复杂数据模型和高级交互功能

using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;

/// <summary>
/// 高级 ListView ViewModel
/// </summary>
public partial class AdvancedListViewViewModel : ObservableObject
{
    /// <summary>
    /// 复杂数据项集合
    /// </summary>
    [ObservableProperty]
    private ObservableCollection<DataItem> dataItems = new();

    /// <summary>
    /// 选择的数据项
    /// </summary>
    [ObservableProperty]
    private DataItem? selectedDataItem;

    /// <summary>
    /// 多选项集合
    /// </summary>
    [ObservableProperty]
    private ObservableCollection<DataItem> selectedItems = new();

    /// <summary>
    /// 新项目名称
    /// </summary>
    [ObservableProperty]
    private string newItemName = "新项目";

    /// <summary>
    /// 是否启用多选
    /// </summary>
    [ObservableProperty]
    private bool isMultiSelectEnabled = false;

    /// <summary>
    /// 构造函数
    /// </summary>
    public AdvancedListViewViewModel()
    {
        InitializeData();
        PropertyChanged += OnPropertyChanged;
    }

    /// <summary>
    /// 初始化示例数据
    /// </summary>
    private void InitializeData()
    {
        DataItems.Clear();
        var sampleData = new[]
        {
            new DataItem { Id = 1, Name = "文档管理", Description = "管理和组织您的文档", Icon = "📄", Category = "办公", IsEnabled = true },
            new DataItem { Id = 2, Name = "图片编辑", Description = "编辑和处理图片文件", Icon = "🖼️", Category = "媒体", IsEnabled = true },
            new DataItem { Id = 3, Name = "音乐播放", Description = "播放您喜爱的音乐", Icon = "🎵", Category = "媒体", IsEnabled = false },
            new DataItem { Id = 4, Name = "任务管理", Description = "跟踪和管理您的任务", Icon = "📋", Category = "效率", IsEnabled = true },
            new DataItem { Id = 5, Name = "日历安排", Description = "管理您的日程安排", Icon = "📅", Category = "效率", IsEnabled = true }
        };

        foreach (var item in sampleData)
        {
            DataItems.Add(item);
        }
    }

    /// <summary>
    /// 添加数据项命令
    /// </summary>
    [RelayCommand]
    private void AddDataItem()
    {
        var newItem = new DataItem
        {
            Id = DataItems.Count + 1,
            Name = NewItemName,
            Description = $"这是 {NewItemName} 的描述",
            Icon = GetRandomIcon(),
            Category = GetRandomCategory(),
            IsEnabled = true,
            CreatedDate = DateTime.Now
        };

        DataItems.Insert(0, newItem);
        NewItemName = "新项目"; // 重置输入
    }

    /// <summary>
    /// 删除选中项命令
    /// </summary>
    [RelayCommand]
    private void DeleteSelectedItem()
    {
        if (SelectedDataItem != null)
        {
            DataItems.Remove(SelectedDataItem);
            SelectedDataItem = null;
        }
    }

    /// <summary>
    /// 切换多选模式命令
    /// </summary>
    [RelayCommand]
    private void ToggleMultiSelect()
    {
        IsMultiSelectEnabled = !IsMultiSelectEnabled;
        if (!IsMultiSelectEnabled)
        {
            SelectedItems.Clear();
        }
    }

    /// <summary>
    /// 获取随机图标
    /// </summary>
    private string GetRandomIcon()
    {
        var icons = new[] { "📄", "🖼️", "🎵", "📋", "📅", "📧", "👥", "⚙️", "📁", "⭐" };
        return icons[Random.Shared.Next(icons.Length)];
    }

    /// <summary>
    /// 获取随机分类
    /// </summary>
    private string GetRandomCategory()
    {
        var categories = new[] { "办公", "媒体", "效率", "通讯", "系统", "工具" };
        return categories[Random.Shared.Next(categories.Length)];
    }

    /// <summary>
    /// 属性变化处理
    /// </summary>
    private void OnPropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        switch (e.PropertyName)
        {
            case nameof(SelectedDataItem):
                HandleSelectionChanged();
                break;
            case nameof(IsMultiSelectEnabled):
                HandleMultiSelectModeChanged();
                break;
        }
    }

    /// <summary>
    /// 处理选择变化
    /// </summary>
    private void HandleSelectionChanged()
    {
        if (SelectedDataItem != null)
        {
            Console.WriteLine($"选择了数据项: {SelectedDataItem.Name}");
            
            // 如果是多选模式，添加到选中集合
            if (IsMultiSelectEnabled && !SelectedItems.Contains(SelectedDataItem))
            {
                SelectedItems.Add(SelectedDataItem);
            }
        }
    }

    /// <summary>
    /// 处理多选模式变化
    /// </summary>
    private void HandleMultiSelectModeChanged()
    {
        Console.WriteLine($"多选模式: {(IsMultiSelectEnabled ? "启用" : "禁用")}");
    }
}

/// <summary>
/// 数据项模型
/// </summary>
public partial class DataItem : ObservableObject
{
    [ObservableProperty]
    private int id;

    [ObservableProperty]
    private string name = string.Empty;

    [ObservableProperty]
    private string description = string.Empty;

    [ObservableProperty]
    private string icon = "📄";

    [ObservableProperty]
    private string category = "默认";

    [ObservableProperty]
    private bool isEnabled = true;

    [ObservableProperty]
    private DateTime createdDate = DateTime.Now;

    [ObservableProperty]
    private bool isSelected = false;
}

/*
高级概念：
1. 复杂数据模型设计
2. 多选模式实现
3. 动态数据操作
4. 属性变化监听
5. 命令模式应用
6. 数据验证和处理
*/
