<UserControl x:Class="WPFTest.Views.HomePageView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:zylo="http://schemas.zylo.com/wpf/2024/xaml"
             xmlns:viewModels="clr-namespace:WPFTest.ViewModels"
             xmlns:mvvm="http://prismlibrary.com/"
             mc:Ignorable="d"
             
             d:DataContext="{d:DesignInstance viewModels:HomePageViewModel}"
             mvvm:ViewModelLocator.AutoWireViewModel="True"
             d:DesignHeight="800" d:DesignWidth="1200">

    <UserControl.Resources>
        <!-- 转换器 -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />


    </UserControl.Resources>

    <Border Background="{DynamicResource ControlFillColorDefaultBrush}"
            BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
            BorderThickness="1"
            CornerRadius="8"
            Margin="0">



        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="200" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <!-- 顶部横幅 - 仿照WPF-UI官方设计 -->
            <Border x:Name="TopBanner"
                    Grid.Row="0"
                    Background="{DynamicResource ControlFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Margin="15,15,15,5"
                    Opacity="1">

                <Grid>
                    <!-- 装饰性圆点 - 带动画效果 -->
                    <Canvas>
                        <Ellipse x:Name="Dot1" Width="8" Height="8" Fill="{DynamicResource SystemAccentColorSecondaryBrush}" Opacity="0.3"
                                 Canvas.Left="50" Canvas.Top="30" />
                        <Ellipse x:Name="Dot2" Width="12" Height="12" Fill="{DynamicResource SystemAccentColorSecondaryBrush}" Opacity="0.2"
                                 Canvas.Left="150" Canvas.Top="50" />
                        <Ellipse x:Name="Dot3" Width="6" Height="6" Fill="{DynamicResource SystemAccentColorSecondaryBrush}" Opacity="0.4"
                                 Canvas.Left="250" Canvas.Top="25" />
                        <Ellipse x:Name="Dot4" Width="10" Height="10" Fill="{DynamicResource SystemAccentColorSecondaryBrush}" Opacity="0.25"
                                 Canvas.Left="350" Canvas.Top="40" />
                        <Ellipse Width="14" Height="14" Fill="{DynamicResource SystemAccentColorSecondaryBrush}" Opacity="0.15"
                                 Canvas.Left="450" Canvas.Top="35" />
                        <Ellipse Width="8" Height="8" Fill="{DynamicResource SystemAccentColorSecondaryBrush}" Opacity="0.3"
                                 Canvas.Left="550" Canvas.Top="45" />
                        <Ellipse Width="6" Height="6" Fill="{DynamicResource SystemAccentColorSecondaryBrush}" Opacity="0.4"
                                 Canvas.Left="650" Canvas.Top="30" />
                        <Ellipse Width="10" Height="10" Fill="{DynamicResource SystemAccentColorSecondaryBrush}" Opacity="0.2"
                                 Canvas.Left="750" Canvas.Top="50" />
                    </Canvas>

                    <!-- 内容区域 -->
                    <Grid Margin="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <!-- 大图标 - 只使用强调色字体 -->
                        <Border Grid.Column="0"
                                Width="100"
                                Height="100"
                                BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                CornerRadius="20"
                                BorderThickness="2"
                                Margin="5"
                                VerticalAlignment="Center">
                            <ui:SymbolIcon Symbol="Apps24"
                                          FontSize="56"
                                          Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                          HorizontalAlignment="Center"
                                          VerticalAlignment="Center"/>
                        </Border>

                        <!-- 标题和描述 -->
                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                            <TextBlock Text="🎯 Zylo.WPF 控件示例库"
                                       FontSize="32"
                                       FontWeight="Bold"
                                       
                                       Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                            <TextBlock Text="基于 WPF-UI 的现代化控件演示平台，提供完整的控件示例和最佳实践指南"
                                       FontSize="14"
                                       Margin="0,8,0,0"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Opacity="0.9"
                                       TextWrapping="Wrap"
                                       LineHeight="20" />
                        </StackPanel>
                    </Grid>
                </Grid>
            </Border>

            <!-- 🍞 面包屑导航 -->
            <ui:BreadcrumbBar Grid.Row="1" Margin="20,5,20,5" VerticalAlignment="Top">
                <ui:BreadcrumbBarItem Content="首页" Icon="{ui:SymbolIcon Home24}"/>
                <ui:BreadcrumbBarItem Content="控件示例库" Icon="{ui:SymbolIcon Apps24}"/>
            </ui:BreadcrumbBar>

            <!-- 移除了最近使用导航区域 -->

            <!-- 🎨 控件类别展示 - 竖向排列，参考 SubMenuView -->
            <ScrollViewer x:Name="ContentSection" Grid.Row="3" VerticalScrollBarVisibility="Auto" Margin="20,0,20,20" Opacity="1">

                <ItemsControl ItemsSource="{Binding ControlCategories}">
                    <!-- 网格布局：一行三个卡片 -->
                    <ItemsControl.ItemsPanel>
                        <ItemsPanelTemplate>
                            <WrapPanel Orientation="Horizontal"/>
                        </ItemsPanelTemplate>
                    </ItemsControl.ItemsPanel>

                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <!-- 现代化卡片设计：紧凑尺寸 -->
                            <Border Background="{DynamicResource ControlFillColorDefaultBrush}"
                                    BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                    BorderThickness="1"
                                    CornerRadius="8"
                                    Margin="6"
                                    Width="220"
                                    MinHeight="120"
                                    Cursor="Hand">

                                <!-- 卡片变换组 -->
                                <Border.RenderTransform>
                                    <TransformGroup>
                                        <ScaleTransform ScaleX="1" ScaleY="1"/>
                                        <TranslateTransform Y="0"/>
                                    </TransformGroup>
                                </Border.RenderTransform>
                                <Border.RenderTransformOrigin>
                                    <Point X="0.5" Y="0.5"/>
                                </Border.RenderTransformOrigin>

                                <!-- 卡片样式和动画触发器 -->
                                <Border.Style>
                                    <Style TargetType="Border">
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background"
                                                        Value="{DynamicResource ControlFillColorSecondaryBrush}" />
                                                <Setter Property="BorderBrush"
                                                        Value="{DynamicResource SystemAccentColorSecondaryBrush}" />
                                                <Trigger.EnterActions>
                                                    <BeginStoryboard Storyboard="{StaticResource ZyloCardHoverEnterAnimation}"/>
                                                </Trigger.EnterActions>
                                                <Trigger.ExitActions>
                                                    <BeginStoryboard Storyboard="{StaticResource ZyloCardHoverExitAnimation}"/>
                                                </Trigger.ExitActions>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Border.Style>

                                <Border.InputBindings>
                                    <MouseBinding MouseAction="LeftClick"
                                                  Command="{Binding DataContext.NavigateToItemCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                  CommandParameter="{Binding}" />
                                </Border.InputBindings>

                                <!-- 紧凑卡片内容：居中布局 -->
                                <Grid Margin="12">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- 顶部图标区域 -->
                                    <StackPanel Grid.Row="0"
                                                HorizontalAlignment="Center"
                                                Margin="0,0,0,8">
                                        <!-- 紧凑图标 -->
                                        <Border Width="48" Height="48"
                                                Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                                CornerRadius="12"
                                                HorizontalAlignment="Center">
                                            <ui:SymbolIcon Symbol="{Binding WpfUiSymbol}"
                                                          FontSize="24"
                                                          Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                                          HorizontalAlignment="Center"
                                                          VerticalAlignment="Center"/>
                                        </Border>

                                        <!-- 子项目数量指示器 - 更紧凑 -->
                                        <Border Background="{DynamicResource ControlFillColorTertiaryBrush}"
                                                BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                                BorderThickness="1"
                                                CornerRadius="8"
                                                Padding="4,2"
                                                Margin="0,6,0,0"
                                                HorizontalAlignment="Center"
                                                Visibility="{Binding HasChildren, Converter={StaticResource BooleanToVisibilityConverter}}">
                                            <TextBlock Text="{Binding Children.Count, StringFormat={}{0} 个示例}"
                                                       FontSize="12"
                                                       FontWeight="Medium"
                                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}" />
                                        </Border>
                                    </StackPanel>

                                    <!-- 中间标题 -->
                                    <TextBlock Grid.Row="1"
                                               Text="{Binding Name}"
                                               FontSize="15"
                                               FontWeight="SemiBold"
                                               Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                               TextWrapping="Wrap"
                                               HorizontalAlignment="Center"
                                               TextAlignment="Center"
                                               Margin="0,0,0,6" />

                                    <!-- 底部描述 -->
                                    <TextBlock Grid.Row="2"
                                               Text="{Binding Description}"
                                               FontSize="11"
                                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                               TextWrapping="Wrap"
                                               HorizontalAlignment="Center"
                                               TextAlignment="Center"
                                               LineHeight="14"
                                               Visibility="{Binding Description, Converter={StaticResource StringToVisibilityConverter}}" />
                                </Grid>
                            </Border>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </ScrollViewer>
        </Grid>
    </Border>
</UserControl>
