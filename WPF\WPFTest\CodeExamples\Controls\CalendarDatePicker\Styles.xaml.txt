<!-- CalendarDatePicker 样式展示示例 -->
<StackPanel Margin="20" Spacing="15">

    <!-- 基础样式系列 -->
    <GroupBox Header="基础样式系列" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <!-- 紧凑样式 -->
            <StackPanel Margin="8">
                <TextBlock Text="紧凑样式" FontSize="11" Margin="0,0,0,4" HorizontalAlignment="Center"/>
                <ui:CalendarDatePicker Date="{Binding SelectedDate, Mode=TwoWay}"
                                       Style="{StaticResource CompactCalendarDatePickerStyle}"
                                       Icon="{ui:SymbolIcon Calendar24}"/>
            </StackPanel>
            
            <!-- 小型样式 -->
            <StackPanel Margin="8">
                <TextBlock Text="小型样式" FontSize="11" Margin="0,0,0,4" HorizontalAlignment="Center"/>
                <ui:CalendarDatePicker Date="{Binding SelectedDate, Mode=TwoWay}"
                                       Style="{StaticResource SmallCalendarDatePickerStyle}"
                                       Icon="{ui:SymbolIcon Calendar24}"/>
            </StackPanel>
            
            <!-- 标准样式 -->
            <StackPanel Margin="8">
                <TextBlock Text="标准样式" FontSize="11" Margin="0,0,0,4" HorizontalAlignment="Center"/>
                <ui:CalendarDatePicker Date="{Binding SelectedDate, Mode=TwoWay}"
                                       Style="{StaticResource CalendarDatePickerStyle}"
                                       Icon="{ui:SymbolIcon Calendar24}"/>
            </StackPanel>
            
            <!-- 大型样式 -->
            <StackPanel Margin="8">
                <TextBlock Text="大型样式" FontSize="11" Margin="0,0,0,4" HorizontalAlignment="Center"/>
                <ui:CalendarDatePicker Date="{Binding SelectedDate, Mode=TwoWay}"
                                       Style="{StaticResource LargeCalendarDatePickerStyle}"
                                       Icon="{ui:SymbolIcon Calendar24}"/>
            </StackPanel>
        </WrapPanel>
    </GroupBox>

    <!-- 特殊样式系列 -->
    <GroupBox Header="特殊样式系列" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <!-- 透明样式 -->
            <StackPanel Margin="8">
                <TextBlock Text="透明样式" FontSize="11" Margin="0,0,0,4" HorizontalAlignment="Center"/>
                <ui:CalendarDatePicker Date="{Binding SelectedDate, Mode=TwoWay}"
                                       Style="{StaticResource TransparentCalendarDatePickerStyle}"
                                       Icon="{ui:SymbolIcon Calendar24}"/>
            </StackPanel>
            
            <!-- 强调样式 -->
            <StackPanel Margin="8">
                <TextBlock Text="强调样式" FontSize="11" Margin="0,0,0,4" HorizontalAlignment="Center"/>
                <ui:CalendarDatePicker Date="{Binding SelectedDate, Mode=TwoWay}"
                                       Style="{StaticResource AccentCalendarDatePickerStyle}"
                                       Icon="{ui:SymbolIcon Calendar24}"/>
            </StackPanel>
            
            <!-- 圆角样式 -->
            <StackPanel Margin="8">
                <TextBlock Text="圆角样式" FontSize="11" Margin="0,0,0,4" HorizontalAlignment="Center"/>
                <ui:CalendarDatePicker Date="{Binding SelectedDate, Mode=TwoWay}"
                                       Style="{StaticResource RoundedCalendarDatePickerStyle}"
                                       Icon="{ui:SymbolIcon Calendar24}"/>
            </StackPanel>
        </WrapPanel>
    </GroupBox>

    <!-- 不同图标展示 -->
    <GroupBox Header="不同图标展示" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <ui:CalendarDatePicker Date="{Binding SelectedDate, Mode=TwoWay}"
                                   Style="{StaticResource CalendarDatePickerStyle}"
                                   Icon="{ui:SymbolIcon Calendar24}"
                                   Margin="4"/>
            <ui:CalendarDatePicker Date="{Binding SelectedDate, Mode=TwoWay}"
                                   Style="{StaticResource CalendarDatePickerStyle}"
                                   Icon="{ui:SymbolIcon Timer24}"
                                   Margin="4"/>
            <ui:CalendarDatePicker Date="{Binding SelectedDate, Mode=TwoWay}"
                                   Style="{StaticResource CalendarDatePickerStyle}"
                                   Icon="{ui:SymbolIcon Settings24}"
                                   Margin="4"/>
            <ui:CalendarDatePicker Date="{Binding SelectedDate, Mode=TwoWay}"
                                   Style="{StaticResource CalendarDatePickerStyle}"
                                   Icon="{ui:SymbolIcon Star24}"
                                   Margin="4"/>
            <ui:CalendarDatePicker Date="{Binding SelectedDate, Mode=TwoWay}"
                                   Style="{StaticResource CalendarDatePickerStyle}"
                                   Icon="{ui:SymbolIcon Info24}"
                                   Margin="4"/>
        </WrapPanel>
    </GroupBox>

    <!-- 状态展示 -->
    <GroupBox Header="状态展示" Padding="15">
        <StackPanel>
            <!-- 正常状态 -->
            <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                <TextBlock Text="正常状态：" Width="80" VerticalAlignment="Center"/>
                <ui:CalendarDatePicker Date="{Binding SelectedDate, Mode=TwoWay}"
                                       Style="{StaticResource CalendarDatePickerStyle}"
                                       Icon="{ui:SymbolIcon Calendar24}"/>
            </StackPanel>
            
            <!-- 禁用状态 -->
            <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                <TextBlock Text="禁用状态：" Width="80" VerticalAlignment="Center"/>
                <ui:CalendarDatePicker Date="{Binding SelectedDate, Mode=TwoWay}"
                                       Style="{StaticResource CalendarDatePickerStyle}"
                                       Icon="{ui:SymbolIcon Calendar24}"
                                       IsEnabled="False"/>
            </StackPanel>
            
            <!-- 只读状态 -->
            <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                <TextBlock Text="只读状态：" Width="80" VerticalAlignment="Center"/>
                <ui:CalendarDatePicker Date="{Binding SelectedDate, Mode=TwoWay}"
                                       Style="{StaticResource CalendarDatePickerStyle}"
                                       Icon="{ui:SymbolIcon Calendar24}"
                                       IsReadOnly="True"/>
            </StackPanel>
        </StackPanel>
    </GroupBox>

    <!-- 自定义样式示例 -->
    <GroupBox Header="自定义样式示例" Padding="15">
        <StackPanel>
            <TextBlock Text="以下是如何创建自定义样式的示例：" Margin="0,0,0,8"/>
            
            <!-- 自定义样式1：渐变背景 -->
            <ui:CalendarDatePicker Date="{Binding SelectedDate, Mode=TwoWay}"
                                   Icon="{ui:SymbolIcon Calendar24}"
                                   Margin="0,0,0,8">
                <ui:CalendarDatePicker.Style>
                    <Style TargetType="ui:CalendarDatePicker" BasedOn="{StaticResource CalendarDatePickerBaseStyle}">
                        <Setter Property="Background">
                            <Setter.Value>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                    <GradientStop Color="{DynamicResource SystemAccentColorLight1}" Offset="0"/>
                                    <GradientStop Color="{DynamicResource SystemAccentColorLight3}" Offset="1"/>
                                </LinearGradientBrush>
                            </Setter.Value>
                        </Setter>
                        <Setter Property="Foreground" Value="{DynamicResource SystemAccentColorDark1}"/>
                        <Setter Property="BorderBrush" Value="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                        <Setter Property="BorderThickness" Value="2"/>
                    </Style>
                </ui:CalendarDatePicker.Style>
            </ui:CalendarDatePicker>
            
            <!-- 自定义样式2：阴影效果 -->
            <ui:CalendarDatePicker Date="{Binding SelectedDate, Mode=TwoWay}"
                                   Icon="{ui:SymbolIcon Calendar24}"
                                   Margin="0,0,0,8">
                <ui:CalendarDatePicker.Style>
                    <Style TargetType="ui:CalendarDatePicker" BasedOn="{StaticResource CalendarDatePickerBaseStyle}">
                        <Setter Property="Effect">
                            <Setter.Value>
                                <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="3" Opacity="0.3"/>
                            </Setter.Value>
                        </Setter>
                        <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}"/>
                        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
                    </Style>
                </ui:CalendarDatePicker.Style>
            </ui:CalendarDatePicker>
        </StackPanel>
    </GroupBox>

</StackPanel>
