using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using System.Windows;
using System.IO;
using System.Reflection;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels.ButtonControls
{
    /// <summary>
    /// SplitButton 控件示例页面 ViewModel
    /// 展示 SplitButton 控件的各种用法和功能
    /// </summary>
    public partial class SplitButtonPageViewModel : ObservableObject
    {
        #region 私有字段

        private readonly YLoggerInstance _logger = YLogger.For<DropDownButtonPageViewModel>();

        #endregion

        #region 状态属性

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        private string statusMessage = "SplitButton 示例库已加载，开始体验各种功能！";

        /// <summary>
        /// 最后执行的操作
        /// </summary>
        [ObservableProperty]
        private string lastAction = "无操作";

        /// <summary>
        /// 选择的选项
        /// </summary>
        [ObservableProperty]
        private string selectedOption = "默认选项";

        /// <summary>
        /// 交互计数
        /// </summary>
        [ObservableProperty]
        private int interactionCount = 0;

        /// <summary>
        /// SplitButton 是否启用
        /// </summary>
        [ObservableProperty]
        private bool isSplitButtonEnabled = true;

        #endregion

        #region 代码示例属性

        /// <summary>
        /// 基础 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 基础 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 样式 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string StylesXamlExample { get; set; } = string.Empty;

        #endregion

        #region 构造函数

        public SplitButtonPageViewModel()
        {
          
            StatusMessage = "SplitButton 示例库已加载，开始体验各种功能！";
            InitializeCodeExamples();
        }

        #endregion

        #region 命令

        /// <summary>
        /// 处理主要操作命令
        /// </summary>
        [RelayCommand]
        private void HandlePrimaryAction(string? parameter)
        {
            var action = parameter ?? "主要操作";
            LastAction = action;
            StatusMessage = $"🎯 执行了主要操作: {action}";
            InteractionCount++;
            _logger.Info($"执行了主要操作: {action}");
        }

        /// <summary>
        /// 处理下拉菜单交互命令
        /// </summary>
        [RelayCommand]
        private void HandleDropdownInteraction(string? parameter)
        {
            var option = parameter ?? "未知选项";
            SelectedOption = option;
            LastAction = $"下拉选择: {option}";
            StatusMessage = $"📋 从下拉菜单选择了: {option}";
            InteractionCount++;
            _logger.Info($"从下拉菜单选择了: {option}");
        }

        /// <summary>
        /// 切换启用状态命令
        /// </summary>
        [RelayCommand]
        private void ToggleEnabled()
        {
            IsSplitButtonEnabled = !IsSplitButtonEnabled;
            StatusMessage = $"🔄 SplitButton 已{(IsSplitButtonEnabled ? "启用" : "禁用")}";
            InteractionCount++;
            _logger.Info($"SplitButton 状态切换为: {(IsSplitButtonEnabled ? "启用" : "禁用")}");
        }

        /// <summary>
        /// 清除状态命令
        /// </summary>
        [RelayCommand]
        private void ClearStatus()
        {
            LastAction = "无操作";
            SelectedOption = "默认选项";
            StatusMessage = "状态已清除";
            _logger.Info("清除了状态信息");
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化代码示例
        /// </summary>
        private void InitializeCodeExamples()
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                var assemblyLocation = assembly.Location;
                var assemblyDirectory = Path.GetDirectoryName(assemblyLocation);
                var codeExamplesPath = Path.Combine(assemblyDirectory, "CodeExamples", "Controls", "SplitButton");

                _logger.Debug($"代码示例路径: {codeExamplesPath}");

                // 从文件加载代码示例
                BasicXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.xaml.txt"));
                BasicCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.cs.txt"));
                AdvancedXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Advanced.xaml.txt"));
                AdvancedCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Advanced.cs.txt"));
                StylesXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Styles.xaml.txt"));

                _logger.Info("SplitButton 代码示例加载完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"加载代码示例时出错: {ex.Message}");
                LoadDefaultCodeExamples();
            }
        }

        /// <summary>
        /// 读取代码示例文件
        /// </summary>
        private string ReadCodeExampleFile(string filePath)
        {
            try
            {
                if (File.Exists(filePath))
                {
                    return File.ReadAllText(filePath);
                }
                else
                {
                    _logger.Warning($"代码示例文件不存在: {filePath}");
                    return $"<!-- 代码示例文件不存在: {Path.GetFileName(filePath)} -->";
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"读取代码示例文件失败 {filePath}: {ex.Message}");
                return $"<!-- 读取代码示例文件失败: {ex.Message} -->";
            }
        }

        /// <summary>
        /// 加载默认代码示例
        /// </summary>
        private void LoadDefaultCodeExamples()
        {
            BasicXamlExample = "<!-- SplitButton 基础示例 -->\n<ui:SplitButton Content=\"保存\" Command=\"{Binding HandlePrimaryActionCommand}\" CommandParameter=\"保存\">\n    <ui:SplitButton.Flyout>\n        <ContextMenu>\n            <MenuItem Header=\"保存\" Command=\"{Binding HandleDropdownInteractionCommand}\" CommandParameter=\"保存\"/>\n            <MenuItem Header=\"另存为\" Command=\"{Binding HandleDropdownInteractionCommand}\" CommandParameter=\"另存为\"/>\n        </ContextMenu>\n    </ui:SplitButton.Flyout>\n</ui:SplitButton>";
            BasicCSharpExample = "// SplitButton C# 基础示例\n[RelayCommand]\nprivate void HandlePrimaryAction(string parameter)\n{\n    StatusMessage = $\"执行了主要操作: {parameter}\";\n    InteractionCount++;\n}\n\n[RelayCommand]\nprivate void HandleDropdownInteraction(string parameter)\n{\n    StatusMessage = $\"从下拉菜单选择了: {parameter}\";\n    InteractionCount++;\n}";
            AdvancedXamlExample = "<!-- SplitButton 高级示例 -->\n<!-- 在这里添加高级用法示例 -->";
            AdvancedCSharpExample = "// SplitButton C# 高级示例\n// 在这里添加高级 C# 代码示例";
            StylesXamlExample = "<!-- SplitButton 样式示例 -->\n<!-- 在这里添加样式使用示例 -->";
        }

        #endregion
    }
}
