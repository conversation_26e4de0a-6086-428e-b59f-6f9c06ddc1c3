<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml">

    <!-- 🎨 Zylo.WPF 矩形按钮样式库 -->
    <!-- 提供现代化的矩形按钮样式，支持多种尺寸和透明度 -->

    <!-- 矩形按钮样式 -->
    <Style x:Key="RectangleButtonStyle" TargetType="ui:Button">
        <Setter Property="Background" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextOnAccentFillColorPrimaryBrush}"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Width" Value="80"/>
        <Setter Property="Height" Value="48"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="{DynamicResource SystemAccentColor}"
                                  BlurRadius="4"
                                  ShadowDepth="1"
                                  Opacity="0.3"/>
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ui:Button">
                    <Border x:Name="ButtonBorder"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{TemplateBinding CornerRadius}"
                            Width="{TemplateBinding Width}"
                            Height="{TemplateBinding Height}"
                            Effect="{TemplateBinding Effect}">
                        <ContentPresenter x:Name="ContentPresenter"
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          Margin="{TemplateBinding Padding}"
                                          Content="{TemplateBinding Content}"
                                          ContentTemplate="{TemplateBinding ContentTemplate}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="ButtonBorder" Property="Background" Value="{DynamicResource AccentFillColorSecondaryBrush}"/>
                            <Setter Property="Effect">
                                <Setter.Value>
                                    <DropShadowEffect Color="{DynamicResource SystemAccentColor}"
                                                      BlurRadius="6"
                                                      ShadowDepth="2"
                                                      Opacity="0.4"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="ButtonBorder" Property="Background" Value="{DynamicResource AccentFillColorTertiaryBrush}"/>
                            <Setter Property="Effect">
                                <Setter.Value>
                                    <DropShadowEffect Color="{DynamicResource SystemAccentColor}"
                                                      BlurRadius="2"
                                                      ShadowDepth="0"
                                                      Opacity="0.2"/>
                                </Setter.Value>
                            </Setter>
                            <Setter Property="RenderTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="ButtonBorder" Property="Background" Value="{DynamicResource ControlFillColorDisabledBrush}"/>
                            <Setter Property="Foreground" Value="{DynamicResource TextFillColorDisabledBrush}"/>
                            <Setter Property="Effect" Value="{x:Null}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 小型矩形按钮样式 -->
    <Style x:Key="SmallRectangleButtonStyle" TargetType="ui:Button" BasedOn="{StaticResource RectangleButtonStyle}">
        <Setter Property="Width" Value="64"/>
        <Setter Property="Height" Value="36"/>
        <Setter Property="CornerRadius" Value="6"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Padding" Value="8,6"/>
    </Style>

    <!-- 大型矩形按钮样式 -->
    <Style x:Key="LargeRectangleButtonStyle" TargetType="ui:Button" BasedOn="{StaticResource RectangleButtonStyle}">
        <Setter Property="Width" Value="100"/>
        <Setter Property="Height" Value="56"/>
        <Setter Property="CornerRadius" Value="12"/>
        <Setter Property="FontSize" Value="18"/>
        <Setter Property="Padding" Value="16,12"/>
    </Style>

    <!-- 透明矩形按钮样式 (带边框) -->
    <Style x:Key="TransparentRectangleButtonStyle" TargetType="ui:Button" BasedOn="{StaticResource RectangleButtonStyle}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="Foreground" Value="{DynamicResource AccentTextFillColorPrimaryBrush}"/>
        <Setter Property="Effect" Value="{x:Null}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ui:Button">
                    <Border x:Name="ButtonBorder"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{TemplateBinding CornerRadius}"
                            Width="{TemplateBinding Width}"
                            Height="{TemplateBinding Height}">
                        <ContentPresenter x:Name="ContentPresenter"
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          Margin="{TemplateBinding Padding}"
                                          Content="{TemplateBinding Content}"
                                          ContentTemplate="{TemplateBinding ContentTemplate}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="ButtonBorder" Property="Background" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
                            <Setter Property="Foreground" Value="{DynamicResource TextOnAccentFillColorPrimaryBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="ButtonBorder" Property="Background" Value="{DynamicResource AccentFillColorSecondaryBrush}"/>
                            <Setter Property="Foreground" Value="{DynamicResource TextOnAccentFillColorPrimaryBrush}"/>
                            <Setter Property="RenderTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="ButtonBorder" Property="Background" Value="Transparent"/>
                            <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
                            <Setter Property="Foreground" Value="{DynamicResource TextFillColorDisabledBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 无边框矩形按钮样式 -->
    <Style x:Key="BorderlessRectangleButtonStyle" TargetType="ui:Button" BasedOn="{StaticResource RectangleButtonStyle}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="Effect" Value="{x:Null}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ui:Button">
                    <Border x:Name="ButtonBorder"
                            Background="{TemplateBinding Background}"
                            BorderThickness="0"
                            CornerRadius="{TemplateBinding CornerRadius}"
                            Width="{TemplateBinding Width}"
                            Height="{TemplateBinding Height}">
                        <ContentPresenter x:Name="ContentPresenter"
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          Margin="{TemplateBinding Padding}"
                                          Content="{TemplateBinding Content}"
                                          ContentTemplate="{TemplateBinding ContentTemplate}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="ButtonBorder" Property="Background" Value="{DynamicResource SubtleFillColorSecondaryBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="ButtonBorder" Property="Background" Value="{DynamicResource SubtleFillColorTertiaryBrush}"/>
                            <Setter Property="RenderTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Foreground" Value="{DynamicResource TextFillColorDisabledBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
