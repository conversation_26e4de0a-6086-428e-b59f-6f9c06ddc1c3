// WPF-UI Flyout 高级功能 C# 示例

// 1. 创建菜单类型的 Flyout
private Wpf.Ui.Controls.Button CreateMenuFlyoutButton()
{
    var button = new Wpf.Ui.Controls.Button
    {
        Content = "操作菜单"
    };

    var flyout = new Wpf.Ui.Controls.Flyout
    {
        Placement = PlacementMode.Bottom
    };

    var menuPanel = new StackPanel
    {
        Margin = new Thickness(8),
        MinWidth = 150
    };

    // 添加菜单项
    var menuItems = new[]
    {
        ("新建", "New"),
        ("编辑", "Edit"),
        ("删除", "Delete"),
        ("设置", "Settings")
    };

    foreach (var (text, command) in menuItems)
    {
        var menuButton = new Wpf.Ui.Controls.Button
        {
            Content = text,
            HorizontalAlignment = HorizontalAlignment.Stretch,
            HorizontalContentAlignment = HorizontalAlignment.Left,
            Appearance = ControlAppearance.Transparent,
            Margin = new Thickness(0, 0, 0, 4)
        };

        menuButton.Click += (s, e) =>
        {
            Console.WriteLine($"执行命令: {command}");
            flyout.IsOpen = false;
        };

        menuPanel.Children.Add(menuButton);

        // 在删除后添加分隔符
        if (text == "删除")
        {
            menuPanel.Children.Add(new Separator
            {
                Margin = new Thickness(0, 4, 0, 4)
            });
        }
    }

    flyout.Content = menuPanel;
    button.Click += (s, e) => flyout.IsOpen = !flyout.IsOpen;
    
    return button;
}

// 2. 创建用户信息 Flyout
private Wpf.Ui.Controls.Button CreateUserFlyoutButton()
{
    var button = new Wpf.Ui.Controls.Button
    {
        Content = "用户菜单"
    };

    var flyout = new Wpf.Ui.Controls.Flyout
    {
        Placement = PlacementMode.Bottom
    };

    var mainPanel = new StackPanel
    {
        Margin = new Thickness(16),
        MinWidth = 200
    };

    // 用户信息区域
    var userInfoGrid = new Grid
    {
        Margin = new Thickness(0, 0, 0, 12)
    };
    userInfoGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
    userInfoGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

    // 用户头像
    var avatar = new Border
    {
        Background = new SolidColorBrush(Colors.Blue),
        Width = 40,
        Height = 40,
        CornerRadius = new CornerRadius(20),
        Margin = new Thickness(0, 0, 12, 0),
        Child = new TextBlock
        {
            Text = "U",
            Foreground = Brushes.White,
            FontWeight = FontWeights.Bold,
            HorizontalAlignment = HorizontalAlignment.Center,
            VerticalAlignment = VerticalAlignment.Center
        }
    };
    Grid.SetColumn(avatar, 0);

    // 用户信息
    var userInfoPanel = new StackPanel
    {
        VerticalAlignment = VerticalAlignment.Center
    };
    userInfoPanel.Children.Add(new TextBlock
    {
        Text = "用户名",
        FontWeight = FontWeights.Bold
    });
    userInfoPanel.Children.Add(new TextBlock
    {
        Text = "<EMAIL>",
        FontSize = 12
    });
    Grid.SetColumn(userInfoPanel, 1);

    userInfoGrid.Children.Add(avatar);
    userInfoGrid.Children.Add(userInfoPanel);
    mainPanel.Children.Add(userInfoGrid);

    // 分隔符
    mainPanel.Children.Add(new Separator
    {
        Margin = new Thickness(0, 0, 0, 8)
    });

    // 菜单项
    var menuItems = new[]
    {
        "个人资料",
        "账户设置",
        "帮助",
        null, // 分隔符
        "退出登录"
    };

    foreach (var item in menuItems)
    {
        if (item == null)
        {
            mainPanel.Children.Add(new Separator
            {
                Margin = new Thickness(0, 0, 0, 8)
            });
        }
        else
        {
            var menuButton = new Wpf.Ui.Controls.Button
            {
                Content = item,
                HorizontalAlignment = HorizontalAlignment.Stretch,
                HorizontalContentAlignment = HorizontalAlignment.Left,
                Appearance = ControlAppearance.Transparent,
                Margin = new Thickness(0, 0, 0, 4)
            };

            menuButton.Click += (s, e) =>
            {
                Console.WriteLine($"点击了: {item}");
                flyout.IsOpen = false;
            };

            mainPanel.Children.Add(menuButton);
        }
    }

    flyout.Content = mainPanel;
    button.Click += (s, e) => flyout.IsOpen = !flyout.IsOpen;
    
    return button;
}

// 3. 创建表单类型的 Flyout
private Wpf.Ui.Controls.Button CreateSettingsFlyoutButton()
{
    var button = new Wpf.Ui.Controls.Button
    {
        Content = "快速设置"
    };

    var flyout = new Wpf.Ui.Controls.Flyout
    {
        Placement = PlacementMode.Bottom
    };

    var mainPanel = new StackPanel
    {
        Margin = new Thickness(16),
        MinWidth = 250
    };

    // 标题
    mainPanel.Children.Add(new TextBlock
    {
        Text = "快速设置",
        FontWeight = FontWeights.Bold,
        FontSize = 16,
        Margin = new Thickness(0, 0, 0, 12)
    });

    // 设置项
    var settingsGrid = new Grid();
    settingsGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
    settingsGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

    // 主题设置
    var themeLabel = new TextBlock
    {
        Text = "主题:",
        VerticalAlignment = VerticalAlignment.Center,
        Margin = new Thickness(0, 0, 8, 8)
    };
    Grid.SetRow(themeLabel, 0);
    Grid.SetColumn(themeLabel, 0);

    var themeCombo = new ComboBox
    {
        Margin = new Thickness(0, 0, 0, 8)
    };
    themeCombo.Items.Add("浅色");
    themeCombo.Items.Add("深色");
    themeCombo.Items.Add("自动");
    themeCombo.SelectedIndex = 0;
    Grid.SetRow(themeCombo, 0);
    Grid.SetColumn(themeCombo, 1);

    settingsGrid.Children.Add(themeLabel);
    settingsGrid.Children.Add(themeCombo);

    mainPanel.Children.Add(settingsGrid);

    // 复选框设置
    var notificationCheck = new CheckBox
    {
        Content = "启用通知",
        IsChecked = true,
        Margin = new Thickness(0, 0, 0, 8)
    };
    mainPanel.Children.Add(notificationCheck);

    var autoSaveCheck = new CheckBox
    {
        Content = "自动保存",
        IsChecked = false,
        Margin = new Thickness(0, 0, 0, 12)
    };
    mainPanel.Children.Add(autoSaveCheck);

    // 按钮区域
    var buttonPanel = new StackPanel
    {
        Orientation = Orientation.Horizontal,
        HorizontalAlignment = HorizontalAlignment.Right
    };

    var cancelButton = new Button
    {
        Content = "取消",
        Margin = new Thickness(0, 0, 8, 0)
    };
    cancelButton.Click += (s, e) => flyout.IsOpen = false;

    var saveButton = new Button
    {
        Content = "保存"
    };
    saveButton.Click += (s, e) =>
    {
        Console.WriteLine("保存设置");
        flyout.IsOpen = false;
    };

    buttonPanel.Children.Add(cancelButton);
    buttonPanel.Children.Add(saveButton);
    mainPanel.Children.Add(buttonPanel);

    flyout.Content = mainPanel;
    button.Click += (s, e) => flyout.IsOpen = !flyout.IsOpen;
    
    return button;
}

// 4. 高级 Flyout 管理器
public class FlyoutManager
{
    private readonly Dictionary<string, Wpf.Ui.Controls.Flyout> _flyouts = new();

    public void RegisterFlyout(string name, Wpf.Ui.Controls.Flyout flyout)
    {
        _flyouts[name] = flyout;
    }

    public void ShowFlyout(string name)
    {
        if (_flyouts.TryGetValue(name, out var flyout))
        {
            // 关闭其他 Flyout
            foreach (var other in _flyouts.Values.Where(f => f != flyout))
            {
                other.IsOpen = false;
            }
            
            flyout.IsOpen = true;
        }
    }

    public void HideFlyout(string name)
    {
        if (_flyouts.TryGetValue(name, out var flyout))
        {
            flyout.IsOpen = false;
        }
    }

    public void HideAllFlyouts()
    {
        foreach (var flyout in _flyouts.Values)
        {
            flyout.IsOpen = false;
        }
    }
}
