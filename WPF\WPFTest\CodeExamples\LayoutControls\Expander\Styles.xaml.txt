<!-- Expander 样式示例 -->
<!-- 在 Zylo.WPF 中定义的 Expander 样式 -->

<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml">

    <!-- Expander 基础样式 -->
    <Style x:Key="ExpanderBaseStyle" TargetType="Expander">
        <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="Margin" Value="4"/>
        
        <!-- 添加鼠标悬停效果 -->
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Expander 标准样式 -->
    <Style x:Key="ExpanderStyle" TargetType="Expander" BasedOn="{StaticResource ExpanderBaseStyle}">
        <!-- 标准样式的特定设置 -->
        <Setter Property="Padding" Value="12"/>
    </Style>

    <!-- Expander 小型样式 -->
    <Style x:Key="SmallExpanderStyle" TargetType="Expander" BasedOn="{StaticResource ExpanderBaseStyle}">
        <Setter Property="Padding" Value="6"/>
        <Setter Property="BorderThickness" Value="1"/>
    </Style>

    <!-- Expander 大型样式 -->
    <Style x:Key="LargeExpanderStyle" TargetType="Expander" BasedOn="{StaticResource ExpanderBaseStyle}">
        <Setter Property="Padding" Value="16"/>
        <Setter Property="BorderThickness" Value="2"/>
    </Style>

    <!-- Expander 现代化样式 -->
    <Style x:Key="ModernExpanderStyle" TargetType="Expander" BasedOn="{StaticResource ExpanderBaseStyle}">
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Background" Value="{DynamicResource CardBackgroundFillColorDefaultBrush}"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" 
                                  Opacity="0.1" 
                                  ShadowDepth="4" 
                                  BlurRadius="8"/>
            </Setter.Value>
        </Setter>
        
        <!-- 悬停效果 -->
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="Black" 
                                          Opacity="0.15" 
                                          ShadowDepth="6" 
                                          BlurRadius="12"/>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Expander 强调样式 -->
    <Style x:Key="AccentExpanderStyle" TargetType="Expander" BasedOn="{StaticResource ExpanderBaseStyle}">
        <Setter Property="Background" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource AccentStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="Foreground" Value="White"/>
    </Style>

    <!-- Expander 卡片样式 -->
    <Style x:Key="CardExpanderStyle" TargetType="Expander">
        <Setter Property="Background" Value="{DynamicResource CardBackgroundFillColorDefaultBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource CardStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" 
                                  Opacity="0.08" 
                                  ShadowDepth="2" 
                                  BlurRadius="8"/>
            </Setter.Value>
        </Setter>
        
        <!-- 悬停和展开效果 -->
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="Black" 
                                          Opacity="0.15" 
                                          ShadowDepth="4" 
                                          BlurRadius="12"/>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger Property="IsExpanded" Value="True">
                <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Expander 危险样式 -->
    <Style x:Key="DangerExpanderStyle" TargetType="Expander" BasedOn="{StaticResource ExpanderBaseStyle}">
        <Setter Property="Background" Value="#FFFFE6E6"/>
        <Setter Property="BorderBrush" Value="#FFFF4444"/>
        <Setter Property="BorderThickness" Value="2"/>
    </Style>

    <!-- Expander 成功样式 -->
    <Style x:Key="SuccessExpanderStyle" TargetType="Expander" BasedOn="{StaticResource ExpanderBaseStyle}">
        <Setter Property="Background" Value="#FFE6FFE6"/>
        <Setter Property="BorderBrush" Value="#FF44AA44"/>
        <Setter Property="BorderThickness" Value="2"/>
    </Style>

    <!-- Expander 警告样式 -->
    <Style x:Key="WarningExpanderStyle" TargetType="Expander" BasedOn="{StaticResource ExpanderBaseStyle}">
        <Setter Property="Background" Value="#FFFFF0E6"/>
        <Setter Property="BorderBrush" Value="#FFFF8800"/>
        <Setter Property="BorderThickness" Value="2"/>
    </Style>

    <!-- Expander 信息样式 -->
    <Style x:Key="InfoExpanderStyle" TargetType="Expander" BasedOn="{StaticResource ExpanderBaseStyle}">
        <Setter Property="Background" Value="#FFE6F3FF"/>
        <Setter Property="BorderBrush" Value="#FF0088FF"/>
        <Setter Property="BorderThickness" Value="2"/>
    </Style>

    <!-- Expander 动画样式 -->
    <Style x:Key="AnimatedExpanderStyle" TargetType="Expander" BasedOn="{StaticResource ExpanderBaseStyle}">
        <Style.Triggers>
            <Trigger Property="IsExpanded" Value="True">
                <Trigger.EnterActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <ColorAnimation Storyboard.TargetProperty="(Expander.Background).(SolidColorBrush.Color)"
                                            To="LightGreen" Duration="0:0:0.3"/>
                        </Storyboard>
                    </BeginStoryboard>
                </Trigger.EnterActions>
                <Trigger.ExitActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <ColorAnimation Storyboard.TargetProperty="(Expander.Background).(SolidColorBrush.Color)"
                                            To="LightGray" Duration="0:0:0.3"/>
                        </Storyboard>
                    </BeginStoryboard>
                </Trigger.ExitActions>
            </Trigger>
        </Style.Triggers>
    </Style>

</ResourceDictionary>

<!-- 使用示例 -->
<!--
<Expander Style="{StaticResource ModernExpanderStyle}" Header="现代化 Expander">
    <TextBlock Text="现代化样式的内容"/>
</Expander>

<Expander Style="{StaticResource CardExpanderStyle}" Header="卡片式 Expander">
    <TextBlock Text="卡片式样式的内容"/>
</Expander>

<Expander Style="{StaticResource AccentExpanderStyle}" Header="强调 Expander">
    <TextBlock Text="强调样式的内容"/>
</Expander>

<Expander Style="{StaticResource AnimatedExpanderStyle}" Header="动画 Expander">
    <TextBlock Text="带动画效果的内容"/>
</Expander>
-->
