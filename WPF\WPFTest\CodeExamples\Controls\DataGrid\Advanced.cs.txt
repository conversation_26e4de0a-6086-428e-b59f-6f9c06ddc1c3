// DataGrid C# 高级用法示例
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;

namespace WPFTest.ViewModels.List
{
    public partial class DataGridPageViewModel : ObservableObject
    {
        /// <summary>
        /// 产品数据集合（用于高级功能展示）
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<ProductData> productData = new();

        /// <summary>
        /// 选中的产品
        /// </summary>
        [ObservableProperty]
        private ProductData? selectedProduct;

        /// <summary>
        /// 是否启用编辑模式
        /// </summary>
        [ObservableProperty]
        private bool isEditModeEnabled = false;

        /// <summary>
        /// 添加员工命令
        /// </summary>
        [RelayCommand]
        private void AddEmployee()
        {
            try
            {
                var newEmployee = new EmployeeData
                {
                    Id = EmployeeData.Count + 1,
                    Name = $"新员工 {EmployeeData.Count + 1}",
                    Department = "新部门",
                    Position = "新职位",
                    Salary = 5000,
                    HireDate = DateTime.Now
                };

                EmployeeData.Insert(0, newEmployee);
                SelectedEmployee = newEmployee;
                
                HandleInteraction("添加员工");
            }
            catch (Exception ex)
            {
                // 错误处理
                StatusMessage = $"添加员工失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 删除选中员工命令
        /// </summary>
        [RelayCommand]
        private void DeleteSelectedEmployee()
        {
            try
            {
                if (SelectedEmployee != null)
                {
                    var employeeName = SelectedEmployee.Name;
                    EmployeeData.Remove(SelectedEmployee);
                    SelectedEmployee = null;
                    
                    HandleInteraction("删除员工");
                    StatusMessage = $"已删除员工: {employeeName}";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"删除员工失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 切换编辑模式命令
        /// </summary>
        [RelayCommand]
        private void ToggleEditMode()
        {
            IsEditModeEnabled = !IsEditModeEnabled;
            StatusMessage = $"编辑模式: {(IsEditModeEnabled ? "已启用" : "已禁用")}";
            HandleInteraction("编辑模式");
        }

        /// <summary>
        /// 编辑员工命令
        /// </summary>
        [RelayCommand]
        private void EditEmployee(EmployeeData employee)
        {
            if (employee != null)
            {
                SelectedEmployee = employee;
                StatusMessage = $"正在编辑员工: {employee.Name}";
                HandleInteraction("编辑员工");
            }
        }

        /// <summary>
        /// 初始化产品数据
        /// </summary>
        private void InitializeProductData()
        {
            var products = new[]
            {
                new ProductData { Id = 1, Name = "笔记本电脑", Category = "电子产品", Price = 5999, Stock = 50, IsActive = true },
                new ProductData { Id = 2, Name = "无线鼠标", Category = "电子产品", Price = 199, Stock = 200, IsActive = true },
                new ProductData { Id = 3, Name = "机械键盘", Category = "电子产品", Price = 599, Stock = 80, IsActive = true },
                new ProductData { Id = 4, Name = "显示器", Category = "电子产品", Price = 1299, Stock = 30, IsActive = false },
                new ProductData { Id = 5, Name = "办公椅", Category = "办公用品", Price = 899, Stock = 25, IsActive = true }
            };

            ProductData = new ObservableCollection<ProductData>(products);
        }
    }

    /// <summary>
    /// 产品数据模型
    /// </summary>
    public partial class ProductData : ObservableObject
    {
        [ObservableProperty]
        private int id;

        [ObservableProperty]
        private string name = string.Empty;

        [ObservableProperty]
        private string category = string.Empty;

        [ObservableProperty]
        private decimal price;

        [ObservableProperty]
        private int stock;

        [ObservableProperty]
        private bool isActive;

        public override string ToString() => $"{Name} ({Category})";
    }
}
