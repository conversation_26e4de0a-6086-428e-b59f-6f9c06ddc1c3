<!-- 基础面包屑导航示例 -->
<UserControl x:Class="WPFTest.Views.Navigation.BasicBreadcrumbExample"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 基础面包屑导航 -->
        <ui:BreadcrumbBar Grid.Row="0"
                         ItemsSource="{Binding BreadcrumbItems}"
                         Margin="20,10"
                         Background="{DynamicResource LayerFillColorDefaultBrush}"
                         BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                         BorderThickness="1"
                         CornerRadius="4"
                         Padding="10,8">
            
            <!-- 自定义项模板 -->
            <ui:BreadcrumbBar.ItemTemplate>
                <DataTemplate>
                    <StackPanel Orientation="Horizontal">
                        <!-- 图标 -->
                        <TextBlock Text="{Binding Icon}"
                                  FontSize="14"
                                  Margin="0,0,5,0"
                                  Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                        
                        <!-- 标题 -->
                        <TextBlock Text="{Binding Name}"
                                  FontSize="14"
                                  VerticalAlignment="Center"
                                  Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                    </StackPanel>
                </DataTemplate>
            </ui:BreadcrumbBar.ItemTemplate>
        </ui:BreadcrumbBar>

        <!-- 内容区域 -->
        <ScrollViewer Grid.Row="1" Margin="20">
            <StackPanel>
                <!-- 当前路径显示 -->
                <ui:Card Margin="0,0,0,16" Padding="16">
                    <StackPanel>
                        <TextBlock Text="当前路径" FontWeight="Bold" Margin="0,0,0,8"/>
                        <TextBlock Text="{Binding CurrentPath}" 
                                  FontFamily="Consolas"
                                  Background="{DynamicResource ControlFillColorTertiaryBrush}"
                                  Padding="8,4"
                                  BorderRadius="4"/>
                    </StackPanel>
                </ui:Card>

                <!-- 快速导航按钮 -->
                <ui:Card Margin="0,0,0,16" Padding="16">
                    <StackPanel>
                        <TextBlock Text="快速导航" FontWeight="Bold" Margin="0,0,0,8"/>
                        <WrapPanel>
                            <ui:Button Content="🏠 首页" 
                                      Command="{Binding NavigateToCommand}" 
                                      CommandParameter="home"
                                      Margin="0,0,8,8"/>
                            <ui:Button Content="📁 项目" 
                                      Command="{Binding NavigateToCommand}" 
                                      CommandParameter="projects"
                                      Margin="0,0,8,8"/>
                            <ui:Button Content="📄 文档" 
                                      Command="{Binding NavigateToCommand}" 
                                      CommandParameter="docs"
                                      Margin="0,0,8,8"/>
                            <ui:Button Content="⚙️ 设置" 
                                      Command="{Binding NavigateToCommand}" 
                                      CommandParameter="settings"
                                      Margin="0,0,8,8"/>
                        </WrapPanel>
                    </StackPanel>
                </ui:Card>

                <!-- 面包屑操作 -->
                <ui:Card Padding="16">
                    <StackPanel>
                        <TextBlock Text="面包屑操作" FontWeight="Bold" Margin="0,0,0,8"/>
                        <WrapPanel>
                            <ui:Button Content="🔄 重置" 
                                      Command="{Binding ResetBreadcrumbCommand}"
                                      Appearance="Secondary"
                                      Margin="0,0,8,8"/>
                            <ui:Button Content="⬆️ 向上" 
                                      Command="{Binding NavigateUpCommand}"
                                      Appearance="Secondary"
                                      Margin="0,0,8,8"/>
                            <ui:Button Content="📊 统计" 
                                      Command="{Binding ShowStatsCommand}"
                                      Appearance="Secondary"
                                      Margin="0,0,8,8"/>
                        </WrapPanel>
                    </StackPanel>
                </ui:Card>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
