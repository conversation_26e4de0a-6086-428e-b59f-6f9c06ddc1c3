<UserControl x:Class="WPFTest.Views.DragDrop.FileDragExample"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:dd="urn:gong-wpf-dragdrop"
             xmlns:local="clr-namespace:WPFTest.Views.DragDrop"
             xmlns:converters="clr-namespace:Zylo.WPF.Converters;assembly=Zylo.WPF"
             mc:Ignorable="d"
             d:DesignHeight="800" d:DesignWidth="1200">

    <UserControl.Resources>
        <!-- 转换器资源 -->
        <converters:CountToVisibilityConverter x:Key="CountToVisibilityConverter"/>
        <converters:StringToVisibilityConverter x:Key="NullToVisibilityConverter"/>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>

        <!-- 自定义拖拽装饰器模板 -->
        <DataTemplate x:Key="CustomDragAdornerTemplate">
            <Border Background="{DynamicResource SystemFillColorAttentionBrush}"
                    BorderBrush="{DynamicResource SystemFillColorAttentionBrush}"
                    BorderThickness="2"
                    CornerRadius="8"
                    Padding="12,8"
                    Opacity="0.9">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="📐" FontSize="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
                    <StackPanel>
                        <TextBlock Text="{Binding Name}"
                                   FontWeight="Medium"
                                   Foreground="White"
                                   MaxWidth="200"
                                   TextTrimming="CharacterEllipsis"/>
                        <TextBlock Text="{Binding SizeText}"
                                   FontSize="11"
                                   Foreground="White"
                                   Opacity="0.8"/>
                    </StackPanel>
                    <TextBlock Text="🚀" FontSize="14" VerticalAlignment="Center" Margin="8,0,0,0"/>
                </StackPanel>
            </Border>
        </DataTemplate>
    </UserControl.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题区域 -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="🗂️ 文件拖拽示例" 
                       FontSize="24" 
                       FontWeight="Bold" 
                       Margin="0,0,0,10"/>
            
            <TextBlock Text="仿 Windows 文件管理器的拖拽功能，支持将 DWG 文件拖拽到外部软件（如 AutoCAD）中打开。" 
                       FontSize="14" 
                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                       TextWrapping="Wrap"
                       Margin="0,0,0,15"/>
        </StackPanel>

        <!-- 工具栏区域 -->
        <Border Grid.Row="1"
                Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                BorderThickness="1"
                CornerRadius="8"
                Padding="15"
                Margin="0,0,0,15">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 第一行：目录选择和操作按钮 -->
                <Grid Grid.Row="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" 
                               Text="📁 当前目录:" 
                               VerticalAlignment="Center"
                               FontWeight="Medium"
                               Margin="0,0,10,0"/>

                    <TextBox Grid.Column="1" 
                             Text="{Binding CurrentDirectory}"
                             IsReadOnly="True"
                             Background="{DynamicResource ControlFillColorDefaultBrush}"
                             Margin="0,0,10,0"/>

                    <StackPanel Grid.Column="2" Orientation="Horizontal">
                        <ui:Button Content="选择目录"
                                   Command="{Binding SelectDirectoryCommand}"
                                   Icon="{ui:SymbolIcon FolderOpen24}"
                                   Appearance="Primary"
                                   Margin="0,0,10,0"/>

                        <ui:Button Content="刷新"
                                   Command="{Binding RefreshFilesCommand}"
                                   Icon="{ui:SymbolIcon ArrowClockwise24}"
                                   Appearance="Secondary"
                                   Margin="0,0,10,0"/>

                        <!-- 拖拽策略切换按钮 -->
                        <ui:Button Command="{Binding SwitchDragStrategyCommand}"
                                   Icon="{ui:SymbolIcon ArrowSwap24}"
                                   Appearance="Info"
                                   ToolTip="切换拖拽策略：智能检测 → 原生拖拽 → 界面拖拽">
                            <ui:Button.Content>
                                <TextBlock>
                                    <Run Text="拖拽:"/>
                                    <Run Text="{Binding CurrentDragStrategyName, Mode=OneWay}"/>
                                </TextBlock>
                            </ui:Button.Content>
                        </ui:Button>
                    </StackPanel>
                </Grid>

                <!-- 第二行：搜索和状态信息 -->
                <Grid Grid.Row="1" Margin="0,15,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="300"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 搜索框 -->
                    <Grid Grid.Column="0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <ui:TextBox Grid.Column="0"
                                    Text="{Binding SearchKeyword, UpdateSourceTrigger=PropertyChanged}"
                                    PlaceholderText="搜索文件名..."
                                    Icon="{ui:SymbolIcon Search24}"
                                    Margin="0,0,5,0"/>
                        
                        <ui:Button Grid.Column="1"
                                   Content="清空"
                                   Command="{Binding ClearSearchCommand}"
                                   Icon="{ui:SymbolIcon Dismiss24}"
                                   Appearance="Secondary"/>
                    </Grid>

                    <!-- 状态信息 -->
                    <TextBlock Grid.Column="1" 
                               Text="{Binding StatusMessage}" 
                               VerticalAlignment="Center"
                               FontWeight="Medium"
                               Margin="20,0"/>

                    <!-- 统计信息 -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal">
                        <ui:SymbolIcon Symbol="Document24" FontSize="16" Margin="0,0,5,0"/>
                        <TextBlock Text="{Binding TotalFileCount, StringFormat={}{0} 个文件}"
                                   VerticalAlignment="Center" Margin="0,0,15,0"/>

                        <ui:SymbolIcon Symbol="Storage24" FontSize="16" Margin="0,0,5,0"/>
                        <TextBlock Text="{Binding TotalFileSize}"
                                   VerticalAlignment="Center" Margin="0,0,15,0"/>

                        <ui:SymbolIcon Symbol="ArrowMove24" FontSize="16" Margin="0,0,5,0"/>
                        <TextBlock Text="{Binding DragOperationCount, StringFormat={}拖拽: {0} 次}"
                                   VerticalAlignment="Center" Margin="0,0,15,0"/>

                        <!-- 性能监控信息 -->
                        <ui:SymbolIcon Symbol="Timer24" FontSize="16" Margin="0,0,5,0"/>
                        <TextBlock Text="{Binding LastLoadTime, StringFormat={}加载: {0}}"
                                   VerticalAlignment="Center" Margin="0,0,10,0"
                                   FontSize="11"/>
                        <TextBlock Text="{Binding LastSearchTime, StringFormat={}搜索: {0}}"
                                   VerticalAlignment="Center"
                                   FontSize="11"/>
                    </StackPanel>
                </Grid>
            </Grid>
        </Border>

        <!-- 主内容区域 -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="300"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧：文件列表 -->
            <Border Grid.Column="0"
                    Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Margin="0,0,15,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 标题 -->
                    <Border Grid.Row="0"
                            Background="{DynamicResource AccentFillColorSecondaryBrush}"
                            CornerRadius="8,8,0,0"
                            Padding="15,10">
                        <StackPanel Orientation="Horizontal">
                            <ui:SymbolIcon Symbol="Document24" FontSize="16" Margin="0,0,8,0" Foreground="{DynamicResource TextFillColorInverseBrush}"/>
                            <TextBlock Text="DWG 文件列表" FontWeight="Bold" Foreground="{DynamicResource TextFillColorInverseBrush}"/>
                            <TextBlock Text="{Binding FilteredFiles.Count, StringFormat='({0} 项)'}"
                                       FontWeight="Bold" Foreground="{DynamicResource TextFillColorInverseBrush}" Margin="8,0,0,0"/>
                        </StackPanel>
                    </Border>

                    <!-- 文件列表 -->
                    <ListBox x:Name="FileListBox"
                             Grid.Row="1"
                             ItemsSource="{Binding FilteredFiles}"
                             SelectedItem="{Binding SelectedFile}"
                             SelectionMode="Extended"
                             dd:DragDrop.IsDragSource="True"
                             dd:DragDrop.DragHandler="{Binding}"
                             dd:DragDrop.UseDefaultDragAdorner="False"
                             dd:DragDrop.DragAdornerTemplate="{StaticResource CustomDragAdornerTemplate}"
                             ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                             ScrollViewer.CanContentScroll="True"
                             VirtualizingPanel.IsVirtualizing="True"
                             VirtualizingPanel.VirtualizationMode="Recycling"
                             VirtualizingPanel.IsContainerVirtualizable="True"
                             VirtualizingPanel.ScrollUnit="Item"
                             Background="{DynamicResource ControlFillColorDefaultBrush}"
                             BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                             MouseDoubleClick="ListBox_MouseDoubleClick"
                             MouseDown="ListBox_MouseDown"
                             MouseMove="ListBox_MouseMove"
                             MouseUp="ListBox_MouseUp"
                             Margin="8">

                        <!-- 右键菜单 -->
                        <ListBox.ContextMenu>
                            <ContextMenu>
                                <MenuItem Header="📂 打开文件"
                                          Command="{Binding OpenFileCommand}"
                                          CommandParameter="{Binding SelectedFile}"/>
                                <MenuItem Header="📋 复制到剪切板"
                                          Command="{Binding CopyToClipboardCommand}"/>
                                <Separator/>
                                <MenuItem Header="🔍 在文件夹中显示"
                                          Command="{Binding ShowInExplorerCommand}"/>
                                <MenuItem Header="📊 文件属性"
                                          Command="{Binding ShowFilePropertiesCommand}"/>
                            </ContextMenu>
                        </ListBox.ContextMenu>

                        <!-- 加载指示器 -->
                        <ListBox.Style>
                            <Style TargetType="ListBox" BasedOn="{StaticResource {x:Type ListBox}}">
                                <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}"/>
                                <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
                                <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsLoading}" Value="True">
                                        <Setter Property="Cursor" Value="Wait"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </ListBox.Style>

                        <!-- 优化的 ListBox 项目容器样式 -->
                        <ListBox.ItemContainerStyle>
                            <Style TargetType="ListBoxItem">
                                <Setter Property="Background" Value="Transparent"/>
                                <Setter Property="BorderBrush" Value="Transparent"/>
                                <Setter Property="Padding" Value="2"/>
                                <Setter Property="Margin" Value="1"/>
                                <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                <!-- 简化模板，减少触发器 -->
                                <Style.Triggers>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter Property="Background" Value="{DynamicResource AccentFillColorSecondaryBrush}"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </ListBox.ItemContainerStyle>

                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <!-- 简化的单行显示模板 -->
                                <Grid Background="Transparent"
                                      Margin="2,1"
                                      Cursor="Hand"
                                      Height="32">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- 右键菜单 -->
                                    <Grid.ContextMenu>
                                        <ContextMenu>
                                            <MenuItem Header="打开文件"
                                                      Command="{Binding DataContext.OpenFileCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                      CommandParameter="{Binding}"/>
                                            <MenuItem Header="复制路径"
                                                      Command="{Binding DataContext.CopyFilePathCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                      CommandParameter="{Binding}"/>
                                            <MenuItem Header="在资源管理器中显示"
                                                      Command="{Binding DataContext.ShowInExplorerCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                      CommandParameter="{Binding}"/>
                                        </ContextMenu>
                                    </Grid.ContextMenu>

                                    <!-- DWG 图标 -->
                                    <TextBlock Grid.Column="0"
                                               Text="📐"
                                               FontSize="16"
                                               VerticalAlignment="Center"
                                               Margin="6,0,8,0"/>

                                    <!-- 文件名 -->
                                    <TextBlock Grid.Column="1"
                                               Text="{Binding Name, Mode=OneTime}"
                                               FontWeight="Medium"
                                               TextTrimming="CharacterEllipsis"
                                               VerticalAlignment="Center"
                                               Margin="0,0,10,0"/>

                                    <!-- 修改日期 -->
                                    <TextBlock Grid.Column="2"
                                               Text="{Binding ModifiedDate, StringFormat=yyyy-MM-dd, Mode=OneTime}"
                                               FontSize="11"
                                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                               VerticalAlignment="Center"
                                               Margin="0,0,10,0"/>

                                    <!-- 文件大小 -->
                                    <TextBlock Grid.Column="3"
                                               Text="{Binding SizeText, Mode=OneTime}"
                                               FontSize="11"
                                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                               VerticalAlignment="Center"
                                               Margin="0,0,10,0"/>

                                    <!-- 拖拽提示 -->
                                    <TextBlock Grid.Column="4"
                                               Text="⋮⋮"
                                               FontSize="10"
                                               Foreground="{DynamicResource TextFillColorTertiaryBrush}"
                                               VerticalAlignment="Center"
                                               Margin="0,0,6,0"/>
                                </Grid>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>

                    <!-- 空状态提示 -->
                    <StackPanel Grid.Row="1" 
                                VerticalAlignment="Center" 
                                HorizontalAlignment="Center"
                                Visibility="{Binding FilteredFiles.Count, Converter={StaticResource CountToVisibilityConverter}}">
                        <ui:SymbolIcon Symbol="DocumentSearch24" FontSize="48" 
                                       Foreground="{DynamicResource TextFillColorTertiaryBrush}"
                                       HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="未找到 DWG 文件" 
                                   FontSize="16" FontWeight="Medium"
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   HorizontalAlignment="Center" Margin="0,0,0,5"/>
                        <TextBlock Text="请选择包含 DWG 文件的目录" 
                                   FontSize="12"
                                   Foreground="{DynamicResource TextFillColorTertiaryBrush}"
                                   HorizontalAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- 右侧：文件详情和操作面板 -->
            <Border Grid.Column="1"
                    Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 标题 -->
                    <Border Grid.Row="0"
                            Background="{DynamicResource SystemFillColorSuccessBrush}"
                            CornerRadius="8,8,0,0"
                            Padding="15,10">
                        <StackPanel Orientation="Horizontal">
                            <ui:SymbolIcon Symbol="Info24" FontSize="16" Margin="0,0,8,0" Foreground="{DynamicResource TextFillColorInverseBrush}"/>
                            <TextBlock Text="文件详情" FontWeight="Bold" Foreground="{DynamicResource TextFillColorInverseBrush}"/>
                        </StackPanel>
                    </Border>

                    <!-- 详情内容 -->
                    <ScrollViewer Grid.Row="1" Padding="15" VerticalScrollBarVisibility="Auto">
                        <StackPanel Visibility="{Binding SelectedFile, Converter={StaticResource NullToVisibilityConverter}}">
                            <!-- 文件基本信息 -->
                            <TextBlock Text="📄 基本信息" FontWeight="Bold" Margin="0,0,0,10"/>
                            
                            <Grid Margin="0,0,0,15">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="文件名:" FontWeight="Medium" Margin="0,0,10,5"/>
                                <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding SelectedFile.Name}" 
                                           TextWrapping="Wrap" Margin="0,0,0,5"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="大小:" FontWeight="Medium" Margin="0,0,10,5"/>
                                <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding SelectedFile.SizeText}" Margin="0,0,0,5"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="类型:" FontWeight="Medium" Margin="0,0,10,5"/>
                                <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding SelectedFile.Extension}" Margin="0,0,0,5"/>

                                <TextBlock Grid.Row="3" Grid.Column="0" Text="创建时间:" FontWeight="Medium" Margin="0,0,10,5"/>
                                <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding SelectedFile.CreatedDate, StringFormat={}yyyy-MM-dd HH:mm:ss}" Margin="0,0,0,5"/>

                                <TextBlock Grid.Row="4" Grid.Column="0" Text="修改时间:" FontWeight="Medium" Margin="0,0,10,5"/>
                                <TextBlock Grid.Row="4" Grid.Column="1" Text="{Binding SelectedFile.ModifiedDate, StringFormat={}yyyy-MM-dd HH:mm:ss}" Margin="0,0,0,5"/>
                            </Grid>

                            <!-- 文件路径 -->
                            <TextBlock Text="📁 完整路径" FontWeight="Bold" Margin="0,0,0,10"/>
                            <TextBox Text="{Binding SelectedFile.FullPath}" 
                                     IsReadOnly="True"
                                     TextWrapping="Wrap"
                                     Background="{DynamicResource ControlFillColorDefaultBrush}"
                                     Margin="0,0,0,15"/>

                            <!-- 操作按钮 -->
                            <TextBlock Text="🔧 文件操作" FontWeight="Bold" Margin="0,0,0,10"/>
                            <StackPanel>
                                <ui:Button Content="打开文件" 
                                           Command="{Binding OpenFileCommand}"
                                           CommandParameter="{Binding SelectedFile}"
                                           Icon="{ui:SymbolIcon Open24}"
                                           Appearance="Primary"
                                           HorizontalAlignment="Stretch"
                                           Margin="0,0,0,8"/>
                                
                                <ui:Button Content="复制路径" 
                                           Command="{Binding CopyFilePathCommand}"
                                           CommandParameter="{Binding SelectedFile}"
                                           Icon="{ui:SymbolIcon Copy24}"
                                           Appearance="Secondary"
                                           HorizontalAlignment="Stretch"
                                           Margin="0,0,0,8"/>
                                
                                <ui:Button Content="在资源管理器中显示"
                                           Command="{Binding ShowInExplorerCommand}"
                                           CommandParameter="{Binding SelectedFile}"
                                           Icon="{ui:SymbolIcon FolderOpen24}"
                                           Appearance="Secondary"
                                           HorizontalAlignment="Stretch"
                                           Margin="0,0,0,8"/>

                                <ui:Button Content="测试拖拽功能"
                                           Command="{Binding TestDragDropCommand}"
                                           Icon="{ui:SymbolIcon Bug24}"
                                           Appearance="Info"
                                           HorizontalAlignment="Stretch"
                                           Margin="0,0,0,8"/>

                                <ui:Button Content="验证拖拽格式"
                                           Command="{Binding ValidateDragFormatCommand}"
                                           Icon="{ui:SymbolIcon CheckmarkCircle24}"
                                           Appearance="Success"
                                           HorizontalAlignment="Stretch"
                                           Margin="0,0,0,8"/>

                                <ui:Button Content="复制到剪切板"
                                           Command="{Binding CopyToClipboardCommand}"
                                           Icon="{ui:SymbolIcon Copy24}"
                                           Appearance="Primary"
                                           HorizontalAlignment="Stretch"/>
                            </StackPanel>

                            <!-- 界面内拖拽测试区域 -->
                            <Border Background="{DynamicResource SystemFillColorCautionBrush}"
                                    CornerRadius="6"
                                    Padding="12"
                                    Margin="0,20,0,10"
                                    dd:DragDrop.IsDropTarget="True"
                                    dd:DragDrop.DropHandler="{Binding}"
                                    MinHeight="80">
                                <StackPanel>
                                    <TextBlock Text="🎯 界面内拖拽测试区域" FontWeight="Bold" Foreground="{DynamicResource TextFillColorInverseBrush}" Margin="0,0,0,8"/>
                                    <TextBlock Text="将文件拖拽到此区域测试界面内拖拽功能"
                                               FontSize="11" Foreground="{DynamicResource TextFillColorInverseBrush}" Margin="0,0,0,3"/>
                                    <TextBlock Text="• 智能检测模式会自动使用 Gong 拖拽"
                                               FontSize="10" Foreground="{DynamicResource TextFillColorInverseBrush}" Margin="0,0,0,3"/>
                                    <TextBlock Text="• 成功时会在状态栏显示详细信息"
                                               FontSize="10" Foreground="{DynamicResource TextFillColorInverseBrush}"/>
                                </StackPanel>
                            </Border>

                            <!-- 拖拽说明 -->
                            <Border Background="{DynamicResource AccentFillColorSecondaryBrush}"
                                    CornerRadius="6"
                                    Padding="12"
                                    Margin="0,0,0,0">
                                <StackPanel>
                                    <TextBlock Text="📖 拖拽说明" FontWeight="Bold" Foreground="{DynamicResource TextFillColorInverseBrush}" Margin="0,0,0,8"/>
                                    <TextBlock Text="• 智能检测：自动选择最佳拖拽方式"
                                               FontSize="11" Foreground="{DynamicResource TextFillColorInverseBrush}" Margin="0,0,0,3"/>
                                    <TextBlock Text="• 原生拖拽：适用于外部软件（如 CAD）"
                                               FontSize="11" Foreground="{DynamicResource TextFillColorInverseBrush}" Margin="0,0,0,3"/>
                                    <TextBlock Text="• 界面拖拽：适用于界面内操作"
                                               FontSize="11" Foreground="{DynamicResource TextFillColorInverseBrush}" Margin="0,0,0,3"/>
                                    <TextBlock Text="• 支持多选文件批量拖拽"
                                               FontSize="11" Foreground="{DynamicResource TextFillColorInverseBrush}"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </ScrollViewer>
                </Grid>
            </Border>
        </Grid>

        <!-- 底部：使用说明 -->
        <Border Grid.Row="3"
                Background="{DynamicResource CardBackgroundFillColorSecondaryBrush}"
                BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                BorderThickness="1"
                CornerRadius="8"
                Padding="15"
                Margin="0,15,0,0">
            <StackPanel>
                <TextBlock Text="💡 使用说明" 
                           FontWeight="Bold" 
                           FontSize="14"
                           Margin="0,0,0,8"/>
                
                <TextBlock TextWrapping="Wrap" 
                           FontSize="12"
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}">
                    <Run Text="• 文件拖拽：直接将 DWG 文件从列表拖拽到 AutoCAD、浏览器或其他支持文件拖拽的应用程序"/>
                    <LineBreak/>
                    <Run Text="• 多选支持：按住 Ctrl 键选择多个文件，然后拖拽可以批量操作"/>
                    <LineBreak/>
                    <Run Text="• 右键菜单：右键点击文件可以快速打开、复制路径或在资源管理器中显示"/>
                    <LineBreak/>
                    <Run Text="• 搜索过滤：使用搜索框可以快速找到特定的文件"/>
                    <LineBreak/>
                    <Run Text="• 实时更新：文件列表会实时显示拖拽操作的状态和统计信息"/>
                </TextBlock>
            </StackPanel>
        </Border>

        <!-- 拖拽进度指示器 -->
        <Border Grid.Row="3"
                Background="{DynamicResource SystemFillColorAttentionBrush}"
                Padding="12,6"
                Visibility="{Binding IsDragInProgress, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="🚀" FontSize="14" VerticalAlignment="Center" Margin="0,0,8,0"/>
                <TextBlock Text="{Binding DragProgressStatus}"
                           FontWeight="Medium"
                           Foreground="White"
                           VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- 状态栏 -->
        <Border Grid.Row="3"
                Background="{DynamicResource ControlFillColorDefaultBrush}"
                BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                BorderThickness="0,1,0,0"
                Padding="12,8"
                Visibility="{Binding IsDragInProgress, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 状态信息 -->
                <TextBlock Grid.Column="0"
                           Text="{Binding StatusMessage}"
                           VerticalAlignment="Center"
                           FontSize="12"
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>

                <!-- 搜索时间 -->
                <TextBlock Grid.Column="1"
                           Text="{Binding LastSearchTime, StringFormat='搜索: {0}'}"
                           VerticalAlignment="Center"
                           FontSize="11"
                           Foreground="{DynamicResource TextFillColorTertiaryBrush}"
                           Margin="0,0,12,0"
                           Visibility="{Binding LastSearchTime, Converter={StaticResource NullToVisibilityConverter}}"/>

                <!-- 文件统计 -->
                <TextBlock Grid.Column="2"
                           VerticalAlignment="Center"
                           FontSize="11"
                           Foreground="{DynamicResource TextFillColorTertiaryBrush}">
                    <Run Text="{Binding FilteredFiles.Count, Mode=OneWay}"/>
                    <Run Text="/"/>
                    <Run Text="{Binding DwgFiles.Count, Mode=OneWay}"/>
                    <Run Text="个文件"/>
                </TextBlock>
            </Grid>
        </Border>
    </Grid>
</UserControl>
