using System;
using System.ComponentModel.DataAnnotations;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Media;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.Messaging;
using Wpf.Ui.Controls;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels.NotificationControls;

/// <summary>
/// MessageBox 页面 ViewModel - 展示 WPFUI MessageBox 组件的各种功能
/// </summary>
public partial class MessageBoxPageViewModel : ObservableValidator, IRecipient<string>
{
    #region 私有字段

    private readonly YLoggerInstance _logger = YLogger.ForDebug<MessageBoxPageViewModel>();
    private int _operationCounter = 0;

    #endregion

    #region 基础MessageBox属性

    [ObservableProperty] private string messageBoxTitle = "消息提示";

    [ObservableProperty] private string messageBoxContent = "这是一个消息框内容示例";

    [ObservableProperty] private string primaryButtonText = "确定";

    [ObservableProperty] private string secondaryButtonText = "取消";

    [ObservableProperty] private string closeButtonText = "关闭";

    [ObservableProperty] private bool showTitle = true;

    [ObservableProperty] private bool isPrimaryButtonEnabled = true;

    [ObservableProperty] private bool isSecondaryButtonEnabled = true;

    [ObservableProperty] private bool isCloseButtonEnabled = true;

    [ObservableProperty] private ControlAppearance primaryButtonAppearance = ControlAppearance.Primary;

    [ObservableProperty] private ControlAppearance secondaryButtonAppearance = ControlAppearance.Secondary;

    [ObservableProperty] private ControlAppearance closeButtonAppearance = ControlAppearance.Secondary;

    #endregion

    #region 状态属性

    [ObservableProperty] private string lastResult = "无操作";

    [ObservableProperty] private Brush lastResultColor = Brushes.Gray;

    [ObservableProperty] private string operationStatus = "MessageBox 示例库已加载，开始体验各种功能！";

    [ObservableProperty] private int interactionCount = 0;

    [ObservableProperty] private string lastReceivedMessage = "无消息";

    #endregion

    #region 代码示例属性

    [ObservableProperty] private string basicXamlExample = string.Empty;

    [ObservableProperty] private string basicCSharpExample = string.Empty;

    [ObservableProperty] private string advancedXamlExample = string.Empty;

    [ObservableProperty] private string advancedCSharpExample = string.Empty;

    [ObservableProperty] private string customXamlExample = string.Empty;

    [ObservableProperty] private string customCSharpExample = string.Empty;

    #endregion

    #region 构造函数

    /// <summary>
    /// 初始化 MessageBoxPageViewModel
    /// </summary>
    public MessageBoxPageViewModel()
    {
        InitializeCodeExamples();

        // 注册全局消息接收
        WeakReferenceMessenger.Default.Register<string>(this);

        _logger.Info("📦 MessageBoxPageViewModel 初始化完成");
    }

    #endregion

    #region 基础MessageBox命令

    [RelayCommand]
    private async Task ShowInfoMessageBox()
    {
        try
        {
            var messageBox = new Wpf.Ui.Controls.MessageBox
            {
                Title = "信息提示",
                Content = "这是一个信息类型的 MessageBox，用于显示重要信息。",
                PrimaryButtonText = "知道了",
                ShowTitle = true,
                PrimaryButtonAppearance = ControlAppearance.Info
            };

            var result = await messageBox.ShowDialogAsync();
            UpdateResult("信息MessageBox", result, Brushes.Blue);
            UpdateOperationStatus("显示信息MessageBox");
            _logger.Info("📢 显示信息MessageBox");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 显示信息MessageBox失败: {ex}");
            UpdateOperationStatus($"显示信息MessageBox失败: {ex.Message}");
        }
    }

    [RelayCommand]
    private async Task ShowConfirmMessageBox()
    {
        try
        {
            var messageBox = new Wpf.Ui.Controls.MessageBox
            {
                Title = "确认操作",
                Content = "您确定要执行此操作吗？此操作无法撤销。",
                PrimaryButtonText = "确定",
                SecondaryButtonText = "取消",
                ShowTitle = true,
                PrimaryButtonAppearance = ControlAppearance.Primary,
                SecondaryButtonAppearance = ControlAppearance.Secondary
            };

            var result = await messageBox.ShowDialogAsync();
            UpdateResult("确认MessageBox", result, result == MessageBoxResult.Primary ? Brushes.Green : Brushes.Orange);
            UpdateOperationStatus("显示确认MessageBox");
            _logger.Info("❓ 显示确认MessageBox");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 显示确认MessageBox失败: {ex}");
            UpdateOperationStatus($"显示确认MessageBox失败: {ex.Message}");
        }
    }

    [RelayCommand]
    private async Task ShowWarningMessageBox()
    {
        try
        {
            var messageBox = new Wpf.Ui.Controls.MessageBox
            {
                Title = "警告提示",
                Content = "检测到潜在风险，建议您先备份数据再继续操作。",
                PrimaryButtonText = "继续",
                SecondaryButtonText = "取消",
                CloseButtonText = "稍后处理",
                ShowTitle = true,
                PrimaryButtonAppearance = ControlAppearance.Caution,
                SecondaryButtonAppearance = ControlAppearance.Secondary
            };

            var result = await messageBox.ShowDialogAsync();
            UpdateResult("警告MessageBox", result, Brushes.Orange);
            UpdateOperationStatus("显示警告MessageBox");
            _logger.Info("⚠️ 显示警告MessageBox");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 显示警告MessageBox失败: {ex}");
            UpdateOperationStatus($"显示警告MessageBox失败: {ex.Message}");
        }
    }

    [RelayCommand]
    private async Task ShowErrorMessageBox()
    {
        try
        {
            var messageBox = new Wpf.Ui.Controls.MessageBox
            {
                Title = "错误信息",
                Content = "操作失败：网络连接超时。请检查网络设置后重试。",
                PrimaryButtonText = "重试",
                SecondaryButtonText = "取消",
                ShowTitle = true,
                PrimaryButtonAppearance = ControlAppearance.Danger,
                SecondaryButtonAppearance = ControlAppearance.Secondary
            };

            var result = await messageBox.ShowDialogAsync();
            UpdateResult("错误MessageBox", result, Brushes.Red);
            UpdateOperationStatus("显示错误MessageBox");
            _logger.Info("❌ 显示错误MessageBox");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 显示错误MessageBox失败: {ex}");
            UpdateOperationStatus($"显示错误MessageBox失败: {ex.Message}");
        }
    }

    [RelayCommand]
    private async Task ShowCustomMessageBox()
    {
        try
        {
            var messageBox = new Wpf.Ui.Controls.MessageBox
            {
                Title = string.IsNullOrWhiteSpace(MessageBoxTitle) ? "自定义MessageBox" : MessageBoxTitle,
                Content = string.IsNullOrWhiteSpace(MessageBoxContent) ? "这是自定义内容" : MessageBoxContent,
                PrimaryButtonText = string.IsNullOrWhiteSpace(PrimaryButtonText) ? "确定" : PrimaryButtonText,
                SecondaryButtonText = string.IsNullOrWhiteSpace(SecondaryButtonText) ? "取消" : SecondaryButtonText,
                CloseButtonText = string.IsNullOrWhiteSpace(CloseButtonText) ? "关闭" : CloseButtonText,
                ShowTitle = ShowTitle,
                IsPrimaryButtonEnabled = IsPrimaryButtonEnabled,
                IsSecondaryButtonEnabled = IsSecondaryButtonEnabled,
                IsCloseButtonEnabled = IsCloseButtonEnabled,
                PrimaryButtonAppearance = PrimaryButtonAppearance,
                SecondaryButtonAppearance = SecondaryButtonAppearance,
                CloseButtonAppearance = CloseButtonAppearance
            };

            var result = await messageBox.ShowDialogAsync();
            UpdateResult("自定义MessageBox", result, Brushes.Purple);
            UpdateOperationStatus("显示自定义MessageBox");
            _logger.Info($"🔧 显示自定义MessageBox: {MessageBoxTitle}");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 显示自定义MessageBox失败: {ex}");
            UpdateOperationStatus($"显示自定义MessageBox失败: {ex.Message}");
        }
    }

    #endregion

    #region 高级功能命令

    [RelayCommand]
    private async Task ShowSuccessMessageBox()
    {
        try
        {
            var messageBox = new Wpf.Ui.Controls.MessageBox
            {
                Title = "操作成功",
                Content = "✅ 操作已成功完成！数据已保存到服务器。",
                PrimaryButtonText = "继续",
                ShowTitle = true,
                PrimaryButtonAppearance = ControlAppearance.Success
            };

            var result = await messageBox.ShowDialogAsync();
            UpdateResult("成功MessageBox", result, Brushes.Green);
            UpdateOperationStatus("显示成功MessageBox");
            _logger.Info("✅ 显示成功MessageBox");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 显示成功MessageBox失败: {ex}");
            UpdateOperationStatus($"显示成功MessageBox失败: {ex.Message}");
        }
    }

    [RelayCommand]
    private async Task ShowLongContentMessageBox()
    {
        try
        {
            var longContent = @"这是一个包含长文本内容的 MessageBox 示例。

在实际应用中，您可能需要显示：
• 详细的错误信息和解决方案
• 用户协议或隐私政策内容
• 操作步骤说明
• 系统状态报告

MessageBox 会自动调整大小以适应内容，
同时保持良好的用户体验。

您可以使用换行符来格式化文本，
使内容更易于阅读和理解。";

            var messageBox = new Wpf.Ui.Controls.MessageBox
            {
                Title = "长内容示例",
                Content = longContent,
                PrimaryButtonText = "我已阅读",
                SecondaryButtonText = "稍后查看",
                ShowTitle = true,
                PrimaryButtonAppearance = ControlAppearance.Primary,
                SecondaryButtonAppearance = ControlAppearance.Secondary
            };

            var result = await messageBox.ShowDialogAsync();
            UpdateResult("长内容MessageBox", result, Brushes.Teal);
            UpdateOperationStatus("显示长内容MessageBox");
            _logger.Info("📄 显示长内容MessageBox");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 显示长内容MessageBox失败: {ex}");
            UpdateOperationStatus($"显示长内容MessageBox失败: {ex.Message}");
        }
    }

    [RelayCommand]
    private async Task ShowMessageWithGlobalMessage()
    {
        try
        {
            // 发送全局消息
            var message = $"MessageBox操作 #{++_operationCounter} - {DateTime.Now:HH:mm:ss}";
            WeakReferenceMessenger.Default.Send(message);

            var messageBox = new Wpf.Ui.Controls.MessageBox
            {
                Title = "消息传递演示",
                Content = $"已发送全局消息：{message}\n\n这演示了 CommunityToolkit.Mvvm 的消息传递功能。",
                PrimaryButtonText = "继续发送",
                SecondaryButtonText = "停止发送",
                ShowTitle = true,
                PrimaryButtonAppearance = ControlAppearance.Info,
                SecondaryButtonAppearance = ControlAppearance.Secondary
            };

            var result = await messageBox.ShowDialogAsync();
            UpdateResult("消息传递MessageBox", result, Brushes.Cyan);
            UpdateOperationStatus("显示消息传递MessageBox");
            _logger.Info("📡 显示消息传递MessageBox");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 显示消息传递MessageBox失败: {ex}");
            UpdateOperationStatus($"显示消息传递MessageBox失败: {ex.Message}");
        }
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 更新操作结果
    /// </summary>
    private void UpdateResult(string operation, MessageBoxResult result, Brush color)
    {
        var resultText = result switch
        {
            MessageBoxResult.Primary => "用户点击了主要按钮",
            MessageBoxResult.Secondary => "用户点击了次要按钮",
            MessageBoxResult.None => "用户点击了关闭按钮或按ESC键",
            _ => "未知结果"
        };

        LastResult = $"{operation}: {resultText}";
        LastResultColor = color;
        InteractionCount++;
    }

    /// <summary>
    /// 更新操作状态
    /// </summary>
    private void UpdateOperationStatus(string status)
    {
        OperationStatus = $"🎯 {status} - {DateTime.Now:HH:mm:ss}";
    }

    /// <summary>
    /// 实现 IRecipient<string> 接口
    /// </summary>
    public void Receive(string message)
    {
        LastReceivedMessage = message;
    }

    /// <summary>
    /// 初始化代码示例
    /// </summary>
    private void InitializeCodeExamples()
    {
        // 基础用法示例
        BasicXamlExample = @"<!-- MessageBox 基础用法 -->
<ui:Button Content=""显示信息MessageBox""
           Command=""{Binding ShowInfoMessageBoxCommand}""
           Appearance=""Info""
           Icon=""{ui:SymbolIcon Info24}"" />";

        BasicCSharpExample = @"// MessageBox 基础用法 - 使用 CommunityToolkit.Mvvm
[RelayCommand]
private async Task ShowInfoMessageBox()
{
    var messageBox = new Wpf.Ui.Controls.MessageBox
    {
        Title = ""信息提示"",
        Content = ""这是一个信息类型的 MessageBox"",
        PrimaryButtonText = ""知道了"",
        ShowTitle = true,
        PrimaryButtonAppearance = ControlAppearance.Info
    };

    var result = await messageBox.ShowDialogAsync();

    // 处理结果
    if (result == MessageBoxResult.Primary)
    {
        // 用户点击了主要按钮
    }
}";

        // 高级用法示例
        AdvancedXamlExample = @"<!-- MessageBox 高级配置 -->
<StackPanel>
    <ui:TextBox Text=""{Binding MessageBoxTitle, Mode=TwoWay}""
                PlaceholderText=""MessageBox标题"" />
    <ui:TextBox Text=""{Binding MessageBoxContent, Mode=TwoWay}""
                PlaceholderText=""MessageBox内容""
                AcceptsReturn=""True""
                TextWrapping=""Wrap"" />

    <ui:Button Content=""显示自定义MessageBox""
               Command=""{Binding ShowCustomMessageBoxCommand}""
               Appearance=""Primary"" />
</StackPanel>";

        AdvancedCSharpExample = @"// MessageBox 高级配置 - 完全自定义
[RelayCommand]
private async Task ShowCustomMessageBox()
{
    var messageBox = new Wpf.Ui.Controls.MessageBox
    {
        Title = MessageBoxTitle,
        Content = MessageBoxContent,
        PrimaryButtonText = PrimaryButtonText,
        SecondaryButtonText = SecondaryButtonText,
        CloseButtonText = CloseButtonText,
        ShowTitle = ShowTitle,
        IsPrimaryButtonEnabled = IsPrimaryButtonEnabled,
        IsSecondaryButtonEnabled = IsSecondaryButtonEnabled,
        IsCloseButtonEnabled = IsCloseButtonEnabled,
        PrimaryButtonAppearance = PrimaryButtonAppearance,
        SecondaryButtonAppearance = SecondaryButtonAppearance,
        CloseButtonAppearance = CloseButtonAppearance
    };

    var result = await messageBox.ShowDialogAsync();
    UpdateResult(""自定义MessageBox"", result, Brushes.Purple);
}";

        // 自定义样式示例
        CustomXamlExample = @"<!-- MessageBox 样式自定义 -->
<ui:Button.Resources>
    <Style TargetType=""ui:MessageBox"">
        <Setter Property=""Background"" Value=""{DynamicResource ControlFillColorDefaultBrush}"" />
        <Setter Property=""Foreground"" Value=""{DynamicResource TextFillColorPrimaryBrush}"" />
        <Setter Property=""BorderBrush"" Value=""{DynamicResource ControlStrokeColorDefaultBrush}"" />
    </Style>
</ui:Button.Resources>";

        CustomCSharpExample = @"// MessageBox 与 CommunityToolkit.Mvvm 消息传递集成
public partial class MessageBoxPageViewModel : ObservableValidator, IRecipient<string>
{
    [RelayCommand]
    private async Task ShowMessageWithGlobalMessage()
    {
        // 发送全局消息
        var message = $""MessageBox操作 #{++_operationCounter}"";
        WeakReferenceMessenger.Default.Send(message);

        var messageBox = new Wpf.Ui.Controls.MessageBox
        {
            Title = ""消息传递演示"",
            Content = $""已发送全局消息：{message}"",
            PrimaryButtonText = ""继续"",
            PrimaryButtonAppearance = ControlAppearance.Info
        };

        var result = await messageBox.ShowDialogAsync();
    }

    // 实现消息接收
    public void Receive(string message)
    {
        LastReceivedMessage = message;
    }
}";
    }
}

#endregion
