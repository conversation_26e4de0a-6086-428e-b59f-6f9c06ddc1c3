﻿using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using Wpf.Ui.Controls;
using WPFTest.Models;
using Zylo.YLog.Runtime;

namespace WPFTest.Views;

/// <summary>
/// WPF-UI 现代化主窗口
/// </summary>
public partial class MainView : FluentWindow
{
    public MainView()
    {
        InitializeComponent();
    }

    /// <summary>
    /// 🖱️ 四个角拖拽事件 - 简单安全的拖拽实现
    /// </summary>
    private void CornerDrag_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
    {
        try
        {
            this.DragMove();
        }
        catch (InvalidOperationException)
        {
            // 忽略拖拽异常
        }
    }
}