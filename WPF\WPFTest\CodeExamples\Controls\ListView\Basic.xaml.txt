<!-- ListView 基础功能示例 -->
<!-- 展示 ListView 的基本用法和数据绑定 -->

<StackPanel>
    <!-- 简单字符串列表 -->
    <TextBlock Text="简单字符串列表：" FontWeight="Medium" Margin="0,0,0,8"/>
    <ListView ItemsSource="{Binding SimpleItems}"
              SelectedItem="{Binding SelectedSimpleItem, Mode=TwoWay}"
              Height="150"
              Margin="0,0,0,16">
        <!-- 使用默认的字符串显示 -->
    </ListView>

    <!-- 带样式的列表 -->
    <TextBlock Text="带样式的列表：" FontWeight="Medium" Margin="0,0,0,8"/>
    <ListView ItemsSource="{Binding SimpleItems}"
              Style="{StaticResource ListViewStyle}"
              Height="150"
              Margin="0,0,0,16">
        <!-- 应用自定义样式 -->
    </ListView>

    <!-- 选择状态显示 -->
    <Border Background="LightGray" Padding="8" CornerRadius="4">
        <StackPanel>
            <TextBlock Text="选择状态：" FontWeight="Medium"/>
            <TextBlock Text="{Binding SelectedSimpleItem, StringFormat='当前选中: {0}', TargetNullValue='未选择任何项'}"
                       Foreground="Blue"/>
        </StackPanel>
    </Border>
</StackPanel>

<!-- 
关键特性：
1. ItemsSource 绑定到数据源
2. SelectedItem 双向绑定选中项
3. 支持自定义样式
4. 自动处理选择状态变化
5. 支持键盘和鼠标交互
-->
