using System.Windows.Input;
using Microsoft.Xaml.Behaviors;

namespace Zylo.WPF.Behaviors.Navigation;

/// <summary>
/// ListBox/ListView选中项行为 - 支持双向绑定SelectedItem
/// 兼容原生ListBox和WPF-UI ListView
/// </summary>
public class ListBoxSelectionBehavior : Behavior<ListBox>
{
    #region SelectedItem 依赖属性

    /// <summary>
    /// 选中项依赖属性
    /// </summary>
    public static readonly DependencyProperty SelectedItemProperty =
        DependencyProperty.Register(
            nameof(SelectedItem),
            typeof(object),
            typeof(ListBoxSelectionBehavior),
            new FrameworkPropertyMetadata(null, FrameworkPropertyMetadataOptions.BindsTwoWayByDefault, OnSelectedItemChanged));

    /// <summary>
    /// 选中项
    /// </summary>
    public object SelectedItem
    {
        get => GetValue(SelectedItemProperty);
        set => SetValue(SelectedItemProperty, value);
    }

    /// <summary>
    /// 选中项变化处理
    /// </summary>
    private static void OnSelectedItemChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is ListBoxSelectionBehavior behavior && behavior.AssociatedObject != null)
        {
            // 避免循环触发
            behavior._isUpdatingFromProperty = true;
            behavior.AssociatedObject.SelectedItem = e.NewValue;
            behavior._isUpdatingFromProperty = false;
        }
    }

    #endregion

    #region 选中命令相关属性

    /// <summary>
    /// 选择命令 - 当ListBox项被选中时执行的命令
    /// </summary>
    public static readonly DependencyProperty SelectionCommandProperty =
        DependencyProperty.Register(
            nameof(SelectionCommand),
            typeof(ICommand),
            typeof(ListBoxSelectionBehavior),
            new PropertyMetadata(null));

    /// <summary>
    /// 获取或设置选择命令
    /// </summary>
    public ICommand SelectionCommand
    {
        get => (ICommand)GetValue(SelectionCommandProperty);
        set => SetValue(SelectionCommandProperty, value);
    }

    /// <summary>
    /// 选择命令参数 - 传递给选择命令的参数
    /// </summary>
    public static readonly DependencyProperty SelectionCommandParameterProperty =
        DependencyProperty.Register(
            nameof(SelectionCommandParameter),
            typeof(object),
            typeof(ListBoxSelectionBehavior),
            new PropertyMetadata(null));

    /// <summary>
    /// 获取或设置选择命令参数
    /// </summary>
    public object SelectionCommandParameter
    {
        get => GetValue(SelectionCommandParameterProperty);
        set => SetValue(SelectionCommandParameterProperty, value);
    }

    /// <summary>
    /// 是否传递选中项作为命令参数
    /// </summary>
    public static readonly DependencyProperty PassSelectedItemAsParameterProperty =
        DependencyProperty.Register(
            nameof(PassSelectedItemAsParameter),
            typeof(bool),
            typeof(ListBoxSelectionBehavior),
            new PropertyMetadata(true));

    /// <summary>
    /// 获取或设置是否传递选中项作为命令参数
    /// </summary>
    public bool PassSelectedItemAsParameter
    {
        get => (bool)GetValue(PassSelectedItemAsParameterProperty);
        set => SetValue(PassSelectedItemAsParameterProperty, value);
    }

    #endregion

    #region 私有字段

    private bool _isUpdatingFromProperty = false;

    #endregion

    #region 行为生命周期

    /// <summary>
    /// 附加到ListBox时
    /// </summary>
    protected override void OnAttached()
    {
        base.OnAttached();
        AssociatedObject.SelectionChanged += OnListBoxSelectionChanged;
        
        // 初始化选中项
        if (SelectedItem != null)
        {
            AssociatedObject.SelectedItem = SelectedItem;
        }
    }

    /// <summary>
    /// 从ListBox分离时
    /// </summary>
    protected override void OnDetaching()
    {
        if (AssociatedObject != null)
        {
            AssociatedObject.SelectionChanged -= OnListBoxSelectionChanged;
        }
        base.OnDetaching();
    }

    #endregion

    #region 事件处理

    /// <summary>
    /// ListBox选中项变化处理
    /// </summary>
    private void OnListBoxSelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        // 避免循环触发
        if (_isUpdatingFromProperty) return;

        object selectedItem = null;

        // 更新绑定的属性
        if (e.AddedItems.Count > 0)
        {
            selectedItem = e.AddedItems[0];
            SelectedItem = selectedItem;
        }
        else if (e.RemovedItems.Count > 0)
        {
            SelectedItem = null;
        }

        // 执行选中命令
        ExecuteSelectionCommand(selectedItem);
    }

    /// <summary>
    /// 执行选中命令
    /// </summary>
    private void ExecuteSelectionCommand(object selectedItem)
    {
        if (SelectionCommand == null) return;

        // 确定命令参数
        object parameter = PassSelectedItemAsParameter ? selectedItem : SelectionCommandParameter;

        // 执行命令
        if (SelectionCommand.CanExecute(parameter))
        {
            SelectionCommand.Execute(parameter);
        }
    }

    #endregion
}
