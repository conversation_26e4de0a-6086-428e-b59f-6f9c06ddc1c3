<!-- Border 基础用法示例 -->
<StackPanel Margin="20" Spacing="15">

    <!-- 标准 Border -->
    <GroupBox Header="标准 Border" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <Border Background="{DynamicResource ControlFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="4"
                    Padding="16"
                    Margin="4">
                <TextBlock Text="标准 Border" 
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"/>
            </Border>
            
            <Border Background="{DynamicResource ControlFillColorSecondaryBrush}"
                    BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                    BorderThickness="2"
                    CornerRadius="4"
                    Padding="16"
                    Margin="4">
                <TextBlock Text="加粗边框" 
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"/>
            </Border>
        </WrapPanel>
    </GroupBox>

    <!-- 不同圆角 -->
    <GroupBox Header="不同圆角" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <Border Background="{DynamicResource AccentFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource AccentStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="0"
                    Padding="16"
                    Margin="4">
                <TextBlock Text="无圆角" 
                           Foreground="White"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"/>
            </Border>
            
            <Border Background="{DynamicResource AccentFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource AccentStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Padding="16"
                    Margin="4">
                <TextBlock Text="小圆角" 
                           Foreground="White"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"/>
            </Border>
            
            <Border Background="{DynamicResource AccentFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource AccentStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="16"
                    Padding="16"
                    Margin="4">
                <TextBlock Text="大圆角" 
                           Foreground="White"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"/>
            </Border>
        </WrapPanel>
    </GroupBox>

    <!-- 不同边框厚度 -->
    <GroupBox Header="不同边框厚度" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <Border Background="{DynamicResource ControlFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource AccentFillColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="4"
                    Padding="16"
                    Margin="4">
                <TextBlock Text="细边框 (1px)" 
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"/>
            </Border>
            
            <Border Background="{DynamicResource ControlFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource AccentFillColorDefaultBrush}"
                    BorderThickness="3"
                    CornerRadius="4"
                    Padding="16"
                    Margin="4">
                <TextBlock Text="中等边框 (3px)" 
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"/>
            </Border>
            
            <Border Background="{DynamicResource ControlFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource AccentFillColorDefaultBrush}"
                    BorderThickness="5"
                    CornerRadius="4"
                    Padding="16"
                    Margin="4">
                <TextBlock Text="粗边框 (5px)" 
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"/>
            </Border>
        </WrapPanel>
    </GroupBox>

</StackPanel>
