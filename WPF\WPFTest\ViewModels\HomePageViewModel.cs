using System.Collections.ObjectModel;
using System.Windows.Input;
using System.IO;
using System.Text.Json;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Prism.Regions;
using Wpf.Ui.Controls;
using Zylo.WPF.Enums;
using Zylo.WPF.Helpers;
using WPFTest.Services;
using Zylo.WPF.YPrism;
using WPFTest.Models;
using Zylo.WPF.Models.Navigation;

namespace WPFTest.ViewModels;

/// <summary>
/// 🏠 首页 ViewModel - 展示所有控件类别和示例
/// </summary>
/// <remarks>
/// <para>🎯 <strong>主要功能</strong></para>
/// <list type="bullet">
///   <item><description>📊 展示控件统计信息</description></item>
///   <item><description>🎨 显示所有控件类别</description></item>
///   <item><description>🔗 提供快速导航功能</description></item>
///   <item><description>📱 响应式布局展示</description></item>
/// </list>
/// 
/// <para>🔧 <strong>技术特点</strong></para>
/// <list type="bullet">
///   <item><description>🎯 基于 MVVM 模式</description></item>
///   <item><description>📊 数据绑定展示</description></item>
///   <item><description>🚀 命令模式导航</description></item>
///   <item><description>🎨 现代化 UI 设计</description></item>
/// </list>
/// </remarks>
public partial class HomePageViewModel : ObservableObject
{
    #region 🔧 私有字段

    private readonly IRegionManager _regionManager;
    private readonly IConfigureService _configureService;

    #endregion

    #region 📊 公共属性

    /// <summary>
    /// 🎨 控件类别集合 - 用于首页展示
    /// </summary>
    [ObservableProperty]
    private ObservableCollection<ZyloNavigationItemModel> controlCategories = new();

    /// <summary>
    /// 📊 控件类别总数
    /// </summary>
    [ObservableProperty]
    private int totalCategories;

    /// <summary>
    /// 📄 示例页面总数
    /// </summary>
    [ObservableProperty]
    private int totalExamples;



    #endregion

    #region 🎯 构造函数

    /// <summary>
    /// 🏗️ 初始化 HomePageViewModel
    /// </summary>
    /// <param name="regionManager">区域管理器</param>
    /// <param name="configureService">配置服务（实际是 MainViewModel）</param>
    public HomePageViewModel(IRegionManager regionManager, IConfigureService configureService)
    {
        _regionManager = regionManager;
        _configureService = configureService;
        InitializeControlCategories();
        CalculateStatistics();
    }

    #endregion

    #region 🚀 命令

    /// <summary>
    /// 🔗 导航命令 - 跳转到指定的示例页面（参考 MainViewModel 的实现）
    /// </summary>
    [RelayCommand]
    private void NavigateToItem(ZyloNavigationItemModel item)
    {
        if (item == null)
        {
            System.Diagnostics.Debug.WriteLine("⚠️ 导航项参数为空，忽略导航请求");
            return;
        }

        // 🔄 检查是否有子项目
        if (item.Children?.Count > 0)
        {
            System.Diagnostics.Debug.WriteLine($"🔄 导航项有子项目，导航到子菜单页面: {item.Name}");

            // 📋 创建导航参数，传递父项目信息
            NavigationParameters navigationParams = new NavigationParameters();
            navigationParams.Add("ParentItem", item);
            navigationParams.Add("Title", $"{item.Name} - 子菜单");

            // 🚀 导航到子菜单页面
            _regionManager.Regions[PrismManager.MainViewRegionName].RequestNavigate(
                "SubMenuView",
                navigationResult =>
                {
                    if (navigationResult.Result == true)
                    {
                        System.Diagnostics.Debug.WriteLine($"✅ 导航到子菜单成功: {item.Name}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ 导航到子菜单失败: {item.Name} - {navigationResult.Error?.Message}");
                    }
                },
                navigationParams);
        }
        // 🔄 执行页面导航（如果有导航目标且没有子项目）
        else if (!string.IsNullOrEmpty(item.NavigationTarget))
        {
            System.Diagnostics.Debug.WriteLine($"🔄 准备导航到视图: {item.NavigationTarget}");

            // 📋 创建导航参数
            NavigationParameters navigationParams = new NavigationParameters();
            navigationParams.Add("NavigationItem", item);
            navigationParams.Add("Title", item.Name);

            // 🚀 请求导航到目标页面
            _regionManager.Regions[PrismManager.MainViewRegionName].RequestNavigate(
                item.NavigationTarget,
                navigationResult =>
                {
                    if (navigationResult.Result == true)
                    {
                        System.Diagnostics.Debug.WriteLine($"✅ 导航成功: {item.NavigationTarget}");
                        // 📋 最近使用导航记录现在在 MainViewModel 中处理
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ 导航失败: {item.NavigationTarget} - {navigationResult.Error?.Message}");
                    }
                },
                navigationParams);
        }
        else
        {
            System.Diagnostics.Debug.WriteLine($"⚠️ 导航项没有有效的导航目标: {item.Name}");
        }
    }

    /// <summary>
    /// 🚀 快速导航命令 - 直接导航到指定页面
    /// </summary>
    [RelayCommand]
    private void NavigateToPage(string pageName)
    {
        if (string.IsNullOrEmpty(pageName))
        {
            System.Diagnostics.Debug.WriteLine("⚠️ 页面名称为空，忽略导航请求");
            return;
        }

        try
        {
            // 🚀 直接导航到指定页面
            _regionManager.Regions[PrismManager.MainViewRegionName].RequestNavigate(
                pageName,
                navigationResult =>
                {
                    if (navigationResult.Result == true)
                    {
                        System.Diagnostics.Debug.WriteLine($"✅ 成功导航到页面: {pageName}");
                        // 📋 最近使用导航记录现在在 MainViewModel 中处理
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ 导航失败: {pageName}, 错误: {navigationResult.Error?.Message}");
                    }
                });
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ 导航异常: {pageName}, 异常: {ex.Message}");
        }
    }

    #endregion

    #region 🔧 私有方法

    /// <summary>
    /// 🎨 初始化控件类别数据 - 直接使用 MainViewModel 的数据
    /// </summary>
    private void InitializeControlCategories()
    {
        // 🎯 从 MainViewModel 获取导航数据结构
        if (_configureService is MainViewModel mainViewModel)
        {
            // 🔄 获取顶部导航数据，排除首页
            var categories = mainViewModel.TnavigationItems
                .Where(item => !string.IsNullOrEmpty(item.Name) && item.Name != "首页")
                .ToList();

            ControlCategories = new ObservableCollection<ZyloNavigationItemModel>(categories);
        }
        else
        {
            System.Diagnostics.Debug.WriteLine("⚠️ 无法获取 MainViewModel，使用空数据");
            ControlCategories = new ObservableCollection<ZyloNavigationItemModel>();
        }
    }

    /// <summary>
    /// 📊 计算统计信息
    /// </summary>
    private void CalculateStatistics()
    {
        TotalCategories = ControlCategories.Count;
        TotalExamples = ControlCategories.Sum(c => c.Children?.Count ?? 0);
    }

    // 最近使用导航功能已移至 RecentNavigationService
    
    #endregion
}

// RecentNavigationItem 类已移至 RecentNavigationService
