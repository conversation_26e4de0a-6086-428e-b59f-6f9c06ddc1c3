<UserControl x:Class="WPFTest.Views.LayoutControls.CardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:codeExample="clr-namespace:Zylo.WPF.Controls.CodeExample;assembly=Zylo.WPF"
             xmlns:mvvm="http://prismlibrary.com/"
             xmlns:layoutControls="clr-namespace:WPFTest.ViewModels.LayoutControls"
             mc:Ignorable="d"
             d:DesignHeight="800" d:DesignWidth="1200"
             mvvm:ViewModelLocator.AutoWireViewModel="True"
             d:DataContext="{d:DesignInstance layoutControls:CardViewModel}">

    <UserControl.Resources>
        <!-- 🎨 所有样式来自 WPF-UI 官方库 -->
        <!-- Card 是 WPF-UI 的官方控件，具有现代化的卡片设计 -->
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" 
                  HorizontalScrollBarVisibility="Disabled"
                  Padding="24">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>    <!-- 标题区域 -->
                <RowDefinition Height="*"/>       <!-- 主要内容 -->
                <RowDefinition Height="Auto"/>    <!-- 底部操作栏 -->
            </Grid.RowDefinitions>

            <!-- 标题区域 -->
            <StackPanel Grid.Row="0" Margin="0,0,0,20">
                <TextBlock Text="🎴 Card 控件展示"
                           FontSize="32"
                           FontWeight="Bold"
                           Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                           Margin="0,0,0,8"/>
                <TextBlock Text="展示 WPF-UI 官方的 Card 控件的各种样式和功能" 
                           FontSize="16" 
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                           TextWrapping="Wrap"/>
                
                <!-- 状态显示 -->
                <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                        BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Padding="16,12"
                        Margin="0,16,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <ui:SymbolIcon Symbol="Info24" 
                                       FontSize="20" 
                                       Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                       VerticalAlignment="Center"
                                       Margin="0,0,12,0"/>
                        <TextBlock Grid.Column="1" 
                                   Text="{Binding StatusMessage}" 
                                   FontSize="14"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                   VerticalAlignment="Center"
                                   TextWrapping="Wrap"/>
                    </Grid>
                </Border>
            </StackPanel>

            <!-- 主要内容区域 -->
            <Border Grid.Row="1" Margin="0,0,0,20">
                <StackPanel>

                    <!-- 基础功能展示 -->
                    <ui:CardExpander Header="🎯 基础功能" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 WPF-UI Card 控件的基础功能和用法"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>
                            
                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="基础 Card 示例：" FontWeight="Medium" Margin="0,0,0,16"/>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <!-- 左侧示例 -->
                                        <StackPanel Grid.Column="0" Margin="0,0,12,0">
                                            <!-- 简单卡片 -->
                                            <TextBlock Text="简单卡片" FontWeight="Bold" Margin="0,0,0,8"/>
                                            <ui:Card Padding="16" Margin="0,0,0,16">
                                                <StackPanel>
                                                    <TextBlock Text="这是一个简单的卡片" 
                                                               FontWeight="Medium" 
                                                               Margin="0,0,0,8"/>
                                                    <TextBlock Text="卡片提供了现代化的容器样式，具有阴影效果和圆角边框。" 
                                                               TextWrapping="Wrap"
                                                               Margin="0,0,0,12"/>
                                                    <Button Content="卡片按钮" 
                                                            Command="{Binding HandleInteractionCommand}"
                                                            CommandParameter="简单卡片按钮"
                                                            HorizontalAlignment="Left"/>
                                                </StackPanel>
                                            </ui:Card>

                                            <!-- 带图标的卡片 -->
                                            <TextBlock Text="带图标的卡片" FontWeight="Bold" Margin="0,0,0,8"/>
                                            <ui:Card Padding="16" Margin="0,0,0,16">
                                                <StackPanel>
                                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                                        <ui:SymbolIcon Symbol="Star24" 
                                                                       FontSize="24" 
                                                                       Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                                                       Margin="0,0,12,0"/>
                                                        <TextBlock Text="特色功能" 
                                                                   FontWeight="Medium" 
                                                                   FontSize="16"
                                                                   VerticalAlignment="Center"/>
                                                    </StackPanel>
                                                    <TextBlock Text="这个卡片包含了图标和文字的组合，提供更丰富的视觉效果。" 
                                                               TextWrapping="Wrap"
                                                               Margin="0,0,0,12"/>
                                                    <StackPanel Orientation="Horizontal">
                                                        <Button Content="主要操作" 
                                                                Command="{Binding HandleInteractionCommand}"
                                                                CommandParameter="主要操作"
                                                                Margin="0,0,8,0"/>
                                                        <Button Content="次要操作" 
                                                                Command="{Binding HandleInteractionCommand}"
                                                                CommandParameter="次要操作"
                                                                Style="{StaticResource DefaultButtonStyle}"/>
                                                    </StackPanel>
                                                </StackPanel>
                                            </ui:Card>

                                            <!-- 信息卡片 -->
                                            <TextBlock Text="信息卡片" FontWeight="Bold" Margin="0,0,0,8"/>
                                            <ui:Card Padding="16">
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    
                                                    <ui:SymbolIcon Grid.Column="0"
                                                                   Symbol="Info24" 
                                                                   FontSize="32" 
                                                                   Foreground="{DynamicResource SystemFillColorCautionBrush}"
                                                                   Margin="0,0,16,0"
                                                                   VerticalAlignment="Top"/>
                                                    
                                                    <StackPanel Grid.Column="1">
                                                        <TextBlock Text="重要提示" 
                                                                   FontWeight="Bold" 
                                                                   FontSize="16"
                                                                   Margin="0,0,0,8"/>
                                                        <TextBlock Text="这是一个信息提示卡片，用于显示重要的系统消息或用户通知。" 
                                                                   TextWrapping="Wrap"
                                                                   Margin="0,0,0,8"/>
                                                        <TextBlock Text="• 支持多行文本显示" 
                                                                   Margin="0,0,0,2"/>
                                                        <TextBlock Text="• 可以包含图标和按钮" 
                                                                   Margin="0,0,0,2"/>
                                                        <TextBlock Text="• 响应式布局设计" 
                                                                   Margin="0,0,0,8"/>
                                                        <Button Content="了解更多" 
                                                                Command="{Binding HandleInteractionCommand}"
                                                                CommandParameter="了解更多"
                                                                HorizontalAlignment="Left"/>
                                                    </StackPanel>
                                                </Grid>
                                            </ui:Card>
                                        </StackPanel>
                                        
                                        <!-- 右侧示例 -->
                                        <StackPanel Grid.Column="1" Margin="12,0,0,0">
                                            <!-- 统计卡片 -->
                                            <TextBlock Text="统计卡片" FontWeight="Bold" Margin="0,0,0,8"/>
                                            <ui:Card Padding="20" Margin="0,0,0,16">
                                                <StackPanel>
                                                    <TextBlock Text="数据统计" 
                                                               FontWeight="Bold" 
                                                               FontSize="18"
                                                               Margin="0,0,0,16"/>
                                                    <Grid>
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="*"/>
                                                            <ColumnDefinition Width="*"/>
                                                        </Grid.ColumnDefinitions>
                                                        <Grid.RowDefinitions>
                                                            <RowDefinition Height="Auto"/>
                                                            <RowDefinition Height="Auto"/>
                                                            <RowDefinition Height="Auto"/>
                                                        </Grid.RowDefinitions>
                                                        
                                                        <TextBlock Grid.Row="0" Grid.Column="0" Text="总用户:" FontWeight="Medium"/>
                                                        <TextBlock Grid.Row="0" Grid.Column="1" Text="1,234" HorizontalAlignment="Right" FontWeight="Bold"/>
                                                        
                                                        <TextBlock Grid.Row="1" Grid.Column="0" Text="活跃用户:" FontWeight="Medium"/>
                                                        <TextBlock Grid.Row="1" Grid.Column="1" Text="987" HorizontalAlignment="Right" FontWeight="Bold"/>
                                                        
                                                        <TextBlock Grid.Row="2" Grid.Column="0" Text="新增用户:" FontWeight="Medium"/>
                                                        <TextBlock Grid.Row="2" Grid.Column="1" Text="45" HorizontalAlignment="Right" FontWeight="Bold"/>
                                                    </Grid>
                                                    <ProgressBar Value="75" 
                                                                 Height="8" 
                                                                 Margin="0,16,0,8"/>
                                                    <TextBlock Text="完成度: 75%" 
                                                               FontSize="12" 
                                                               HorizontalAlignment="Center"/>
                                                </StackPanel>
                                            </ui:Card>

                                            <!-- 操作卡片 -->
                                            <TextBlock Text="操作卡片" FontWeight="Bold" Margin="0,0,0,8"/>
                                            <ui:Card Padding="16" Margin="0,0,0,16">
                                                <StackPanel>
                                                    <TextBlock Text="快速操作" 
                                                               FontWeight="Medium" 
                                                               FontSize="16"
                                                               Margin="0,0,0,12"/>
                                                    <Grid>
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="*"/>
                                                            <ColumnDefinition Width="*"/>
                                                        </Grid.ColumnDefinitions>
                                                        <Grid.RowDefinitions>
                                                            <RowDefinition Height="Auto"/>
                                                            <RowDefinition Height="Auto"/>
                                                        </Grid.RowDefinitions>
                                                        
                                                        <Button Grid.Row="0" Grid.Column="0" 
                                                                Content="新建" 
                                                                Command="{Binding HandleInteractionCommand}"
                                                                CommandParameter="新建操作"
                                                                Margin="0,0,4,4"/>
                                                        <Button Grid.Row="0" Grid.Column="1" 
                                                                Content="编辑" 
                                                                Command="{Binding HandleInteractionCommand}"
                                                                CommandParameter="编辑操作"
                                                                Margin="4,0,0,4"/>
                                                        <Button Grid.Row="1" Grid.Column="0" 
                                                                Content="删除" 
                                                                Command="{Binding HandleInteractionCommand}"
                                                                CommandParameter="删除操作"
                                                                Margin="0,4,4,0"/>
                                                        <Button Grid.Row="1" Grid.Column="1" 
                                                                Content="设置" 
                                                                Command="{Binding HandleInteractionCommand}"
                                                                CommandParameter="设置操作"
                                                                Margin="4,4,0,0"/>
                                                    </Grid>
                                                </StackPanel>
                                            </ui:Card>

                                            <!-- 媒体卡片 -->
                                            <TextBlock Text="媒体卡片" FontWeight="Bold" Margin="0,0,0,8"/>
                                            <ui:Card Padding="0">
                                                <StackPanel>
                                                    <!-- 模拟图片区域 -->
                                                    <Border Background="{DynamicResource AccentFillColorDefaultBrush}"
                                                            Height="120"
                                                            CornerRadius="8,8,0,0">
                                                        <StackPanel HorizontalAlignment="Center" 
                                                                    VerticalAlignment="Center">
                                                            <ui:SymbolIcon Symbol="Image24" 
                                                                           FontSize="32" 
                                                                           Foreground="White"/>
                                                            <TextBlock Text="图片区域" 
                                                                       Foreground="White" 
                                                                       FontSize="12"
                                                                       Margin="0,4,0,0"/>
                                                        </StackPanel>
                                                    </Border>
                                                    
                                                    <!-- 内容区域 -->
                                                    <StackPanel Margin="16">
                                                        <TextBlock Text="媒体标题" 
                                                                   FontWeight="Bold" 
                                                                   FontSize="16"
                                                                   Margin="0,0,0,8"/>
                                                        <TextBlock Text="这是一个媒体卡片的描述文本，展示了如何在卡片中组合图片和文字内容。" 
                                                                   TextWrapping="Wrap"
                                                                   FontSize="14"
                                                                   Margin="0,0,0,12"/>
                                                        <Button Content="查看详情" 
                                                                Command="{Binding HandleInteractionCommand}"
                                                                CommandParameter="查看详情"
                                                                HorizontalAlignment="Left"/>
                                                    </StackPanel>
                                                </StackPanel>
                                            </ui:Card>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="基础功能代码示例"
                                Language="XAML"
                                Description="展示 WPF-UI Card 控件的基础用法"
                                ShowTabs="True"
                                XamlCode="{Binding BasicXamlExample}"
                                CSharpCode="{Binding BasicCSharpExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 高级功能展示 -->
                    <ui:CardExpander Header="⚡ 高级功能" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 WPF-UI Card 控件的高级功能和组合用法"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="高级 Card 示例：" FontWeight="Medium" Margin="0,0,0,16"/>

                                    <!-- 卡片网格布局 -->
                                    <TextBlock Text="卡片网格布局" FontWeight="Bold" Margin="0,0,0,12"/>
                                    <Grid Margin="0,0,0,24">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- 卡片 1 -->
                                        <ui:Card Grid.Column="0" Padding="16" Margin="0,0,8,0">
                                            <StackPanel HorizontalAlignment="Center">
                                                <ui:SymbolIcon Symbol="Home24"
                                                               FontSize="32"
                                                               Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                                               HorizontalAlignment="Center"
                                                               Margin="0,0,0,8"/>
                                                <TextBlock Text="主页"
                                                           FontWeight="Bold"
                                                           HorizontalAlignment="Center"
                                                           Margin="0,0,0,4"/>
                                                <TextBlock Text="返回主页面"
                                                           FontSize="12"
                                                           HorizontalAlignment="Center"
                                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                                            </StackPanel>
                                        </ui:Card>

                                        <!-- 卡片 2 -->
                                        <ui:Card Grid.Column="1" Padding="16" Margin="4,0,4,0">
                                            <StackPanel HorizontalAlignment="Center">
                                                <ui:SymbolIcon Symbol="Settings24"
                                                               FontSize="32"
                                                               Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                                               HorizontalAlignment="Center"
                                                               Margin="0,0,0,8"/>
                                                <TextBlock Text="设置"
                                                           FontWeight="Bold"
                                                           HorizontalAlignment="Center"
                                                           Margin="0,0,0,4"/>
                                                <TextBlock Text="系统配置"
                                                           FontSize="12"
                                                           HorizontalAlignment="Center"
                                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                                            </StackPanel>
                                        </ui:Card>

                                        <!-- 卡片 3 -->
                                        <ui:Card Grid.Column="2" Padding="16" Margin="8,0,0,0">
                                            <StackPanel HorizontalAlignment="Center">
                                                <ui:SymbolIcon Symbol="Person24"
                                                               FontSize="32"
                                                               Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                                               HorizontalAlignment="Center"
                                                               Margin="0,0,0,8"/>
                                                <TextBlock Text="用户"
                                                           FontWeight="Bold"
                                                           HorizontalAlignment="Center"
                                                           Margin="0,0,0,4"/>
                                                <TextBlock Text="用户管理"
                                                           FontSize="12"
                                                           HorizontalAlignment="Center"
                                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                                            </StackPanel>
                                        </ui:Card>
                                    </Grid>

                                    <!-- 嵌套卡片 -->
                                    <TextBlock Text="嵌套卡片布局" FontWeight="Bold" Margin="0,0,0,12"/>
                                    <ui:Card Padding="20" Margin="0,0,0,24">
                                        <StackPanel>
                                            <TextBlock Text="父级卡片"
                                                       FontWeight="Bold"
                                                       FontSize="18"
                                                       Margin="0,0,0,16"/>
                                            <TextBlock Text="这是一个包含子卡片的父级卡片容器。"
                                                       Margin="0,0,0,16"/>

                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>

                                                <!-- 子卡片 1 -->
                                                <ui:Card Grid.Column="0"
                                                         Padding="12"
                                                         Margin="0,0,8,0"
                                                         Background="{DynamicResource ControlFillColorSecondaryBrush}">
                                                    <StackPanel>
                                                        <TextBlock Text="子卡片 A"
                                                                   FontWeight="Medium"
                                                                   Margin="0,0,0,8"/>
                                                        <TextBlock Text="这是第一个子卡片的内容。"
                                                                   FontSize="12"
                                                                   Margin="0,0,0,8"/>
                                                        <Button Content="操作 A"
                                                                Command="{Binding HandleInteractionCommand}"
                                                                CommandParameter="子卡片A操作"
                                                                HorizontalAlignment="Left"/>
                                                    </StackPanel>
                                                </ui:Card>

                                                <!-- 子卡片 2 -->
                                                <ui:Card Grid.Column="1"
                                                         Padding="12"
                                                         Margin="8,0,0,0"
                                                         Background="{DynamicResource ControlFillColorSecondaryBrush}">
                                                    <StackPanel>
                                                        <TextBlock Text="子卡片 B"
                                                                   FontWeight="Medium"
                                                                   Margin="0,0,0,8"/>
                                                        <TextBlock Text="这是第二个子卡片的内容。"
                                                                   FontSize="12"
                                                                   Margin="0,0,0,8"/>
                                                        <Button Content="操作 B"
                                                                Command="{Binding HandleInteractionCommand}"
                                                                CommandParameter="子卡片B操作"
                                                                HorizontalAlignment="Left"/>
                                                    </StackPanel>
                                                </ui:Card>
                                            </Grid>
                                        </StackPanel>
                                    </ui:Card>

                                    <!-- 交互式卡片 -->
                                    <TextBlock Text="交互式卡片" FontWeight="Bold" Margin="0,0,0,12"/>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- 可点击卡片 -->
                                        <ui:Card Grid.Column="0"
                                                 Padding="16"
                                                 Margin="0,0,8,0"
                                                 Cursor="Hand">
                                            <ui:Card.Style>
                                                <Style TargetType="ui:Card">
                                                    <Style.Triggers>
                                                        <Trigger Property="IsMouseOver" Value="True">
                                                            <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}"/>
                                                        </Trigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </ui:Card.Style>
                                            <StackPanel>
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                                    <ui:SymbolIcon Symbol="ChevronRight24"
                                                                   FontSize="16"
                                                                   Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                                                   Margin="0,0,8,0"/>
                                                    <TextBlock Text="可点击卡片" FontWeight="Medium"/>
                                                </StackPanel>
                                                <TextBlock Text="鼠标悬停时会有视觉反馈"
                                                           FontSize="12"
                                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                                            </StackPanel>
                                        </ui:Card>

                                        <!-- 状态卡片 -->
                                        <ui:Card Grid.Column="1"
                                                 Padding="16"
                                                 Margin="8,0,0,0">
                                            <StackPanel>
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                                    <Ellipse Width="12"
                                                             Height="12"
                                                             Fill="{DynamicResource SystemFillColorSuccessBrush}"
                                                             Margin="0,0,8,0"/>
                                                    <TextBlock Text="在线状态" FontWeight="Medium"/>
                                                </StackPanel>
                                                <TextBlock Text="显示实时状态信息"
                                                           FontSize="12"
                                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                                            </StackPanel>
                                        </ui:Card>
                                    </Grid>
                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="高级功能代码示例"
                                Language="XAML"
                                Description="展示 WPF-UI Card 控件的高级用法和组合布局"
                                ShowTabs="True"
                                XamlCode="{Binding AdvancedXamlExample}"
                                CSharpCode="{Binding AdvancedCSharpExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 样式展示 -->
                    <ui:CardExpander Header="🎨 样式展示" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 WPF-UI Card 控件的各种样式和主题适配"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="样式展示示例：" FontWeight="Medium" Margin="0,0,0,16"/>

                                    <!-- 不同样式的卡片 -->
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <!-- 默认样式 -->
                                        <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,8,16">
                                            <TextBlock Text="默认样式" FontWeight="Bold" Margin="0,0,0,8"/>
                                            <ui:Card Padding="16" MinHeight="100">
                                                <StackPanel VerticalAlignment="Center">
                                                    <TextBlock Text="默认卡片"
                                                               FontWeight="Medium"
                                                               HorizontalAlignment="Center"
                                                               Margin="0,0,0,4"/>
                                                    <TextBlock Text="标准样式"
                                                               FontSize="12"
                                                               HorizontalAlignment="Center"
                                                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                                                </StackPanel>
                                            </ui:Card>
                                        </StackPanel>

                                        <!-- 强调样式 -->
                                        <StackPanel Grid.Row="0" Grid.Column="1" Margin="4,0,4,16">
                                            <TextBlock Text="强调样式" FontWeight="Bold" Margin="0,0,0,8"/>
                                            <ui:Card Padding="16"
                                                     MinHeight="100"
                                                     Background="{DynamicResource AccentFillColorDefaultBrush}">
                                                <StackPanel VerticalAlignment="Center">
                                                    <TextBlock Text="强调卡片"
                                                               FontWeight="Medium"
                                                               HorizontalAlignment="Center"
                                                               Foreground="White"
                                                               Margin="0,0,0,4"/>
                                                    <TextBlock Text="突出显示"
                                                               FontSize="12"
                                                               HorizontalAlignment="Center"
                                                               Foreground="White"/>
                                                </StackPanel>
                                            </ui:Card>
                                        </StackPanel>

                                        <!-- 透明样式 -->
                                        <StackPanel Grid.Row="0" Grid.Column="2" Margin="8,0,0,16">
                                            <TextBlock Text="透明样式" FontWeight="Bold" Margin="0,0,0,8"/>
                                            <ui:Card Padding="16"
                                                     MinHeight="100"
                                                     Background="Transparent"
                                                     BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                                     BorderThickness="1">
                                                <StackPanel VerticalAlignment="Center">
                                                    <TextBlock Text="透明卡片"
                                                               FontWeight="Medium"
                                                               HorizontalAlignment="Center"
                                                               Margin="0,0,0,4"/>
                                                    <TextBlock Text="边框样式"
                                                               FontSize="12"
                                                               HorizontalAlignment="Center"
                                                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                                                </StackPanel>
                                            </ui:Card>
                                        </StackPanel>

                                        <!-- 成功样式 -->
                                        <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,8,0">
                                            <TextBlock Text="成功样式" FontWeight="Bold" Margin="0,0,0,8"/>
                                            <ui:Card Padding="16"
                                                     MinHeight="100"
                                                     Background="{DynamicResource SystemFillColorSuccessBrush}">
                                                <StackPanel VerticalAlignment="Center">
                                                    <TextBlock Text="成功卡片"
                                                               FontWeight="Medium"
                                                               HorizontalAlignment="Center"
                                                               Foreground="White"
                                                               Margin="0,0,0,4"/>
                                                    <TextBlock Text="操作成功"
                                                               FontSize="12"
                                                               HorizontalAlignment="Center"
                                                               Foreground="White"/>
                                                </StackPanel>
                                            </ui:Card>
                                        </StackPanel>

                                        <!-- 警告样式 -->
                                        <StackPanel Grid.Row="1" Grid.Column="1" Margin="4,0,4,0">
                                            <TextBlock Text="警告样式" FontWeight="Bold" Margin="0,0,0,8"/>
                                            <ui:Card Padding="16"
                                                     MinHeight="100"
                                                     Background="{DynamicResource SystemFillColorCautionBrush}">
                                                <StackPanel VerticalAlignment="Center">
                                                    <TextBlock Text="警告卡片"
                                                               FontWeight="Medium"
                                                               HorizontalAlignment="Center"
                                                               Foreground="White"
                                                               Margin="0,0,0,4"/>
                                                    <TextBlock Text="注意事项"
                                                               FontSize="12"
                                                               HorizontalAlignment="Center"
                                                               Foreground="White"/>
                                                </StackPanel>
                                            </ui:Card>
                                        </StackPanel>

                                        <!-- 错误样式 -->
                                        <StackPanel Grid.Row="1" Grid.Column="2" Margin="8,0,0,0">
                                            <TextBlock Text="错误样式" FontWeight="Bold" Margin="0,0,0,8"/>
                                            <ui:Card Padding="16"
                                                     MinHeight="100"
                                                     Background="{DynamicResource SystemFillColorCriticalBrush}">
                                                <StackPanel VerticalAlignment="Center">
                                                    <TextBlock Text="错误卡片"
                                                               FontWeight="Medium"
                                                               HorizontalAlignment="Center"
                                                               Foreground="White"
                                                               Margin="0,0,0,4"/>
                                                    <TextBlock Text="操作失败"
                                                               FontSize="12"
                                                               HorizontalAlignment="Center"
                                                               Foreground="White"/>
                                                </StackPanel>
                                            </ui:Card>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="样式代码示例"
                                Language="XAML"
                                Description="展示如何使用不同的 Card 样式和主题"
                                ShowTabs="True"
                                XamlCode="{Binding StylesXamlExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                </StackPanel>
            </Border>

            <!-- 底部操作栏 -->
            <Border Grid.Row="2" 
                    Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Padding="16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 统计信息 -->
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock Text="{Binding InteractionCount, StringFormat='交互次数: {0}'}" 
                                   FontSize="14" 
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   Margin="0,0,20,0"/>
                        <TextBlock Text="{Binding LastAction, StringFormat='最后操作: {0}'}" 
                                   FontSize="14" 
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                    </StackPanel>

                    <!-- 操作按钮 -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <ui:Button Content="重置计数" 
                                   Command="{Binding ResetCountCommand}"
                                   Appearance="Secondary"
                                   Margin="0,0,8,0"/>
                        <ui:Button Content="清除状态" 
                                   Command="{Binding ClearStatusCommand}"
                                   Appearance="Secondary"/>
                    </StackPanel>
                </Grid>
            </Border>

        </Grid>
    </ScrollViewer>
</UserControl>
