<!-- 自定义样式面包屑导航示例 -->
<Grid>
    <Grid.Resources>
        <!-- 自定义面包屑按钮样式 -->
        <Style x:Key="BreadcrumbButtonStyle" TargetType="ui:Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="CornerRadius" Value="4"/>
            <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}"/>
                </Trigger>
                <DataTrigger Binding="{Binding IsActive}" Value="True">
                    <Setter Property="Background" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
                    <Setter Property="Foreground" Value="{DynamicResource TextOnAccentFillColorPrimaryBrush}"/>
                    <Setter Property="FontWeight" Value="SemiBold"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- 紧凑型面包屑按钮样式 -->
        <Style x:Key="CompactBreadcrumbButtonStyle" TargetType="ui:Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="Margin" Value="1"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="CornerRadius" Value="2"/>
            <Setter Property="Foreground" Value="{DynamicResource TextFillColorSecondaryBrush}"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}"/>
                    <Setter Property="BorderBrush" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
                </Trigger>
                <DataTrigger Binding="{Binding IsActive}" Value="True">
                    <Setter Property="Background" Value="{DynamicResource AccentFillColorSecondaryBrush}"/>
                    <Setter Property="BorderBrush" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
                    <Setter Property="Foreground" Value="{DynamicResource AccentTextFillColorPrimaryBrush}"/>
                    <Setter Property="FontWeight" Value="SemiBold"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- 卡片式面包屑样式 -->
        <Style x:Key="CardBreadcrumbStyle" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}"/>
            <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="6"/>
            <Setter Property="Padding" Value="10,6"/>
            <Setter Property="Margin" Value="2"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}"/>
                    <Setter Property="BorderBrush" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Grid.Resources>

    <Grid.RowDefinitions>
        <RowDefinition Height="Auto"/>
        <RowDefinition Height="Auto"/>
        <RowDefinition Height="Auto"/>
        <RowDefinition Height="*"/>
    </Grid.RowDefinitions>

    <!-- 默认样式面包屑 -->
    <GroupBox Grid.Row="0" Header="默认样式" Margin="20,20,20,10" Padding="15">
        <ItemsControl ItemsSource="{Binding BreadcrumbItems}">
            <ItemsControl.ItemsPanel>
                <ItemsPanelTemplate>
                    <StackPanel Orientation="Horizontal"/>
                </ItemsPanelTemplate>
            </ItemsControl.ItemsPanel>
            
            <ItemsControl.ItemTemplate>
                <DataTemplate>
                    <StackPanel Orientation="Horizontal">
                        <ui:Button Style="{StaticResource BreadcrumbButtonStyle}"
                                  Command="{Binding DataContext.NavigateToItemCommand, RelativeSource={RelativeSource AncestorType=ItemsControl}}"
                                  CommandParameter="{Binding}">
                            <StackPanel Orientation="Horizontal">
                                <zylo:ZyloIcon Icon="{Binding Icon}" FontSize="14" Margin="0,0,5,0"/>
                                <TextBlock Text="{Binding Title}"/>
                            </StackPanel>
                        </ui:Button>
                        
                        <zylo:ZyloIcon Icon="ChevronRight"
                                      FontSize="12"
                                      Margin="5,0"
                                      VerticalAlignment="Center"
                                      Foreground="{DynamicResource TextFillColorTertiaryBrush}"
                                      Visibility="{Binding IsLast, Converter={StaticResource InverseBoolToVisibilityConverter}}"/>
                    </StackPanel>
                </DataTemplate>
            </ItemsControl.ItemTemplate>
        </ItemsControl>
    </GroupBox>

    <!-- 紧凑样式面包屑 -->
    <GroupBox Grid.Row="1" Header="紧凑样式" Margin="20,10" Padding="15">
        <ItemsControl ItemsSource="{Binding BreadcrumbItems}">
            <ItemsControl.ItemsPanel>
                <ItemsPanelTemplate>
                    <StackPanel Orientation="Horizontal"/>
                </ItemsPanelTemplate>
            </ItemsControl.ItemsPanel>
            
            <ItemsControl.ItemTemplate>
                <DataTemplate>
                    <StackPanel Orientation="Horizontal">
                        <ui:Button Style="{StaticResource CompactBreadcrumbButtonStyle}"
                                  Command="{Binding DataContext.NavigateToItemCommand, RelativeSource={RelativeSource AncestorType=ItemsControl}}"
                                  CommandParameter="{Binding}">
                            <TextBlock Text="{Binding Title}"/>
                        </ui:Button>
                        
                        <TextBlock Text="/"
                                  FontSize="12"
                                  Margin="3,0"
                                  VerticalAlignment="Center"
                                  Foreground="{DynamicResource TextFillColorTertiaryBrush}"
                                  Visibility="{Binding IsLast, Converter={StaticResource InverseBoolToVisibilityConverter}}"/>
                    </StackPanel>
                </DataTemplate>
            </ItemsControl.ItemTemplate>
        </ItemsControl>
    </GroupBox>

    <!-- 卡片样式面包屑 -->
    <GroupBox Grid.Row="2" Header="卡片样式" Margin="20,10" Padding="15">
        <ItemsControl ItemsSource="{Binding BreadcrumbItems}">
            <ItemsControl.ItemsPanel>
                <ItemsPanelTemplate>
                    <StackPanel Orientation="Horizontal"/>
                </ItemsPanelTemplate>
            </ItemsControl.ItemsPanel>
            
            <ItemsControl.ItemTemplate>
                <DataTemplate>
                    <StackPanel Orientation="Horizontal">
                        <Border Style="{StaticResource CardBreadcrumbStyle}">
                            <ui:Button Background="Transparent"
                                      BorderThickness="0"
                                      Padding="0"
                                      Command="{Binding DataContext.NavigateToItemCommand, RelativeSource={RelativeSource AncestorType=ItemsControl}}"
                                      CommandParameter="{Binding}">
                                <StackPanel Orientation="Horizontal">
                                    <zylo:ZyloIcon Icon="{Binding Icon}" 
                                                  FontSize="16" 
                                                  Margin="0,0,8,0"
                                                  Foreground="{Binding IsActive, Converter={StaticResource BoolToAccentBrushConverter}}"/>
                                    
                                    <StackPanel>
                                        <TextBlock Text="{Binding Title}"
                                                  FontSize="13"
                                                  FontWeight="{Binding IsActive, Converter={StaticResource BoolToFontWeightConverter}}"/>
                                        
                                        <TextBlock Text="{Binding Description}"
                                                  FontSize="10"
                                                  Foreground="{DynamicResource TextFillColorTertiaryBrush}"
                                                  Visibility="{Binding Description, Converter={StaticResource StringToVisibilityConverter}}"/>
                                    </StackPanel>
                                </StackPanel>
                            </ui:Button>
                        </Border>
                        
                        <zylo:ZyloIcon Icon="ArrowForward"
                                      FontSize="14"
                                      Margin="8,0"
                                      VerticalAlignment="Center"
                                      Foreground="{DynamicResource AccentFillColorDefaultBrush}"
                                      Visibility="{Binding IsLast, Converter={StaticResource InverseBoolToVisibilityConverter}}"/>
                    </StackPanel>
                </DataTemplate>
            </ItemsControl.ItemTemplate>
        </ItemsControl>
    </GroupBox>

    <!-- 主要内容区域 -->
    <ui:Card Grid.Row="3" Margin="20,10,20,20" Padding="20">
        <ScrollViewer>
            <StackPanel>
                <TextBlock Text="自定义样式面包屑导航示例"
                          FontSize="18"
                          FontWeight="SemiBold"
                          Margin="0,0,0,15"
                          Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>

                <TextBlock TextWrapping="Wrap" Margin="0,0,0,15"
                          Foreground="{DynamicResource TextFillColorSecondaryBrush}">
                    这个示例展示了如何自定义面包屑导航的样式：
                    <LineBreak/>• 默认样式：标准的按钮样式，带图标和悬停效果
                    <LineBreak/>• 紧凑样式：更小的按钮，使用斜杠分隔符
                    <LineBreak/>• 卡片样式：卡片式设计，包含描述信息
                    <LineBreak/>• 响应式设计：自动适应主题变化
                    <LineBreak/>• 可扩展性：易于添加新的样式变体
                </TextBlock>

                <!-- 样式特点对比 -->
                <Border Background="{DynamicResource ControlFillColorSecondaryBrush}"
                       CornerRadius="4"
                       Padding="15"
                       Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="样式特点对比"
                                  FontWeight="SemiBold"
                                  Margin="0,0,0,10"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- 表头 -->
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="默认样式" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Text="紧凑样式" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <TextBlock Grid.Row="0" Grid.Column="2" Text="卡片样式" FontWeight="SemiBold" Margin="0,0,0,5"/>

                            <!-- 特点对比 -->
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="• 标准按钮大小" FontSize="12" Margin="0,0,0,3"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" Text="• 紧凑按钮大小" FontSize="12" Margin="0,0,0,3"/>
                            <TextBlock Grid.Row="1" Grid.Column="2" Text="• 卡片式布局" FontSize="12" Margin="0,0,0,3"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="• 箭头分隔符" FontSize="12" Margin="0,0,0,3"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" Text="• 斜杠分隔符" FontSize="12" Margin="0,0,0,3"/>
                            <TextBlock Grid.Row="2" Grid.Column="2" Text="• 箭头分隔符" FontSize="12" Margin="0,0,0,3"/>

                            <TextBlock Grid.Row="3" Grid.Column="0" Text="• 图标 + 文字" FontSize="12" Margin="0,0,0,3"/>
                            <TextBlock Grid.Row="3" Grid.Column="1" Text="• 仅文字" FontSize="12" Margin="0,0,0,3"/>
                            <TextBlock Grid.Row="3" Grid.Column="2" Text="• 图标 + 文字 + 描述" FontSize="12" Margin="0,0,0,3"/>

                            <TextBlock Grid.Row="4" Grid.Column="0" Text="• 适合一般用途" FontSize="12"/>
                            <TextBlock Grid.Row="4" Grid.Column="1" Text="• 适合空间受限" FontSize="12"/>
                            <TextBlock Grid.Row="4" Grid.Column="2" Text="• 适合详细信息" FontSize="12"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- 样式切换控制 -->
                <GroupBox Header="样式控制" Padding="15" Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="当前样式设置："
                                  FontWeight="SemiBold"
                                  Margin="0,0,0,10"/>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <CheckBox Content="显示图标"
                                     IsChecked="{Binding ShowIcons}"
                                     Margin="0,0,15,0"/>
                            
                            <CheckBox Content="显示描述"
                                     IsChecked="{Binding ShowDescriptions}"
                                     Margin="0,0,15,0"/>
                            
                            <CheckBox Content="紧凑模式"
                                     IsChecked="{Binding CompactMode}"
                                     Margin="0,0,15,0"/>
                        </StackPanel>
                        
                        <StackPanel Orientation="Horizontal">
                            <ui:Button Content="🎨 应用默认样式"
                                      Command="{Binding ApplyDefaultStyleCommand}"
                                      Margin="0,0,10,0"/>
                            
                            <ui:Button Content="📏 应用紧凑样式"
                                      Command="{Binding ApplyCompactStyleCommand}"
                                      Margin="0,0,10,0"/>
                            
                            <ui:Button Content="🃏 应用卡片样式"
                                      Command="{Binding ApplyCardStyleCommand}"/>
                        </StackPanel>
                    </StackPanel>
                </GroupBox>

                <!-- 自定义样式说明 -->
                <Expander Header="自定义样式指南" IsExpanded="False">
                    <StackPanel Margin="0,10,0,0">
                        <TextBlock Text="创建自定义面包屑样式的步骤："
                                  FontWeight="SemiBold"
                                  Margin="0,0,0,10"/>
                        
                        <TextBlock TextWrapping="Wrap" Margin="0,0,0,10">
                            1. 定义按钮样式：设置 Background、Padding、FontSize 等属性
                            <LineBreak/>2. 添加触发器：IsMouseOver 和 DataTrigger 用于状态变化
                            <LineBreak/>3. 自定义分隔符：使用不同的图标或文字作为分隔符
                            <LineBreak/>4. 布局调整：通过 Margin 和 Padding 控制间距
                            <LineBreak/>5. 主题适配：使用动态资源确保主题兼容性
                        </TextBlock>
                        
                        <TextBlock Text="样式定制建议："
                                  FontWeight="SemiBold"
                                  Margin="0,0,0,5"/>
                        
                        <TextBlock TextWrapping="Wrap">
                            • 保持一致性：确保所有面包屑项使用相同的样式规则
                            <LineBreak/>• 考虑可访问性：确保足够的颜色对比度和点击区域
                            <LineBreak/>• 响应式设计：在不同屏幕尺寸下都能正常显示
                            <LineBreak/>• 性能优化：避免过于复杂的样式和动画
                        </TextBlock>
                    </StackPanel>
                </Expander>
            </StackPanel>
        </ScrollViewer>
    </ui:Card>
</Grid>
