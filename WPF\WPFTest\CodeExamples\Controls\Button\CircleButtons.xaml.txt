<!-- 圆形按钮样式示例 -->
<StackPanel Margin="20" Spacing="15">
    
    <!-- 标准圆形按钮 -->
    <GroupBox Header="标准圆形按钮 (48x48)" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <ui:Button Style="{StaticResource CircleButtonStyle}"
                       Command="{Binding ButtonClickCommand}"
                       CommandParameter="添加"
                       ToolTip="添加新项目"
                       Margin="4">
                <ui:SymbolIcon Symbol="Add24" FontSize="20"/>
            </ui:Button>
            
            <ui:Button Style="{StaticResource CircleButtonStyle}"
                       Command="{Binding ButtonClickCommand}"
                       CommandParameter="编辑"
                       ToolTip="编辑项目"
                       Margin="4">
                <ui:SymbolIcon Symbol="Edit24" FontSize="20"/>
            </ui:Button>
            
            <ui:Button Style="{StaticResource CircleButtonStyle}"
                       Command="{Binding ButtonClickCommand}"
                       CommandParameter="删除"
                       ToolTip="删除项目"
                       Margin="4">
                <ui:SymbolIcon Symbol="Delete24" FontSize="20"/>
            </ui:Button>
        </WrapPanel>
    </GroupBox>

    <!-- 小型圆形按钮 -->
    <GroupBox Header="小型圆形按钮 (36x36)" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <ui:Button Style="{StaticResource SmallCircleButtonStyle}"
                       Command="{Binding ButtonClickCommand}"
                       CommandParameter="小型添加"
                       ToolTip="小型添加按钮"
                       Margin="4">
                <ui:SymbolIcon Symbol="Add24" FontSize="16"/>
            </ui:Button>
            
            <ui:Button Style="{StaticResource SmallCircleButtonStyle}"
                       Command="{Binding ButtonClickCommand}"
                       CommandParameter="小型编辑"
                       ToolTip="小型编辑按钮"
                       Margin="4">
                <ui:SymbolIcon Symbol="Edit24" FontSize="16"/>
            </ui:Button>
        </WrapPanel>
    </GroupBox>

    <!-- 大型圆形按钮 -->
    <GroupBox Header="大型圆形按钮 (56x56)" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <ui:Button Style="{StaticResource LargeCircleButtonStyle}"
                       Command="{Binding ButtonClickCommand}"
                       CommandParameter="大型添加"
                       ToolTip="大型添加按钮"
                       Margin="4">
                <ui:SymbolIcon Symbol="Add24" FontSize="24"/>
            </ui:Button>
            
            <ui:Button Style="{StaticResource LargeCircleButtonStyle}"
                       Command="{Binding ButtonClickCommand}"
                       CommandParameter="大型播放"
                       ToolTip="大型播放按钮"
                       Margin="4">
                <ui:SymbolIcon Symbol="Play24" FontSize="24"/>
            </ui:Button>
        </WrapPanel>
    </GroupBox>

    <!-- 透明圆形按钮 -->
    <GroupBox Header="透明样式圆形按钮" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <!-- 带边框透明按钮 -->
            <ui:Button Style="{StaticResource TransparentCircleButtonStyle}"
                       Command="{Binding ButtonClickCommand}"
                       CommandParameter="透明添加"
                       ToolTip="透明添加按钮"
                       Margin="4">
                <ui:SymbolIcon Symbol="Add24" FontSize="20"/>
            </ui:Button>
            
            <!-- 无边框透明按钮 -->
            <ui:Button Style="{StaticResource BorderlessCircleButtonStyle}"
                       Command="{Binding ButtonClickCommand}"
                       CommandParameter="无边框更多"
                       ToolTip="更多选项"
                       Margin="4">
                <ui:SymbolIcon Symbol="MoreHorizontal24" FontSize="20"/>
            </ui:Button>
        </WrapPanel>
    </GroupBox>

</StackPanel>
