using System;
using System.IO;
using System.Threading.Tasks;
using WPFTest.Models.DWG;
using WPFTest.ViewModels.DragDrop;
using WPFTest.ViewModels.DWG;

namespace WPFTest.Examples;

/// <summary>
/// DWG对话框功能示例
/// </summary>
/// <remarks>
/// 演示如何使用DWG管理系统中的各种WPF-UI对话框功能
/// </remarks>
public class DwgDialogExample
{
    private readonly DwgManagerTabViewModel _viewModel;

    public DwgDialogExample(DwgManagerTabViewModel viewModel)
    {
        _viewModel = viewModel ?? throw new ArgumentNullException(nameof(viewModel));
    }

    /// <summary>
    /// 演示删除确认对话框
    /// </summary>
    public async Task DemoDeleteConfirmationAsync()
    {
        Console.WriteLine("🗑️ 演示删除确认对话框...");

        try
        {
            // 创建测试文件模型
            var testFile = CreateTestFileModel("要删除的文件.dwg");

            // 调用删除命令
            await _viewModel.DeleteFileCommand.ExecuteAsync(testFile);

            Console.WriteLine("✅ 删除确认对话框演示完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 删除确认对话框演示失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 演示重命名对话框
    /// </summary>
    public async Task DemoRenameDialogAsync()
    {
        Console.WriteLine("📝 演示重命名对话框...");

        try
        {
            // 创建测试文件
            var testFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "重命名测试.dwg");
            File.WriteAllText(testFilePath, "测试内容");

            var testFile = DwgFileModel.FromFilePath(testFilePath);

            // 调用重命名命令
            await _viewModel.RenameFileCommand.ExecuteAsync(testFile);

            Console.WriteLine("✅ 重命名对话框演示完成");

            // 清理测试文件
            if (File.Exists(testFilePath))
            {
                File.Delete(testFilePath);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 重命名对话框演示失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 演示文件属性对话框
    /// </summary>
    public void DemoFilePropertiesDialog()
    {
        Console.WriteLine("ℹ️ 演示文件属性对话框...");

        try
        {
            // 创建测试文件模型
            var testFile = CreateTestFileModel("属性查看测试.dwg");

            // 调用属性显示命令
            _viewModel.ShowFilePropertiesCommand.Execute(testFile);

            Console.WriteLine("✅ 文件属性对话框演示完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 文件属性对话框演示失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 演示复制到桌面功能
    /// </summary>
    public async Task DemoCopyToDesktopAsync()
    {
        Console.WriteLine("🖥️ 演示复制到桌面功能...");

        try
        {
            // 创建测试文件
            var testFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "复制测试源文件.dwg");
            File.WriteAllText(testFilePath, "测试内容");

            var testFile = DwgFileModel.FromFilePath(testFilePath);

            // 调用复制到桌面命令
            await _viewModel.CopyToDesktopCommand.ExecuteAsync(testFile);

            Console.WriteLine("✅ 复制到桌面功能演示完成");

            // 清理测试文件
            if (File.Exists(testFilePath))
            {
                File.Delete(testFilePath);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 复制到桌面功能演示失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 演示新建DWG文件功能
    /// </summary>
    public async Task DemoCreateNewDwgAsync()
    {
        Console.WriteLine("➕ 演示新建DWG文件功能...");

        try
        {
            // 确保有选中的专业文件夹
            if (_viewModel.LDwgFolderModels.Count > 0)
            {
                _viewModel.TDwgFolderModel = _viewModel.LDwgFolderModels.First();
            }

            // 调用新建DWG命令
            await _viewModel.CreateNewDwgCommand.ExecuteAsync(null);

            Console.WriteLine("✅ 新建DWG文件功能演示完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 新建DWG文件功能演示失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 运行所有对话框演示
    /// </summary>
    public async Task RunAllDemosAsync()
    {
        Console.WriteLine("🚀 开始DWG对话框功能演示...");
        Console.WriteLine(new string('=', 60));

        await DemoDeleteConfirmationAsync();
        Console.WriteLine();

        await DemoRenameDialogAsync();
        Console.WriteLine();

        DemoFilePropertiesDialog();
        Console.WriteLine();

        await DemoCopyToDesktopAsync();
        Console.WriteLine();

        await DemoCreateNewDwgAsync();

        Console.WriteLine(new string('=', 60));
        Console.WriteLine("🏁 所有对话框功能演示完成");
    }

    /// <summary>
    /// 创建测试文件模型
    /// </summary>
    /// <param name="fileName">文件名</param>
    /// <returns>测试用的DwgFileModel</returns>
    private DwgFileModel CreateTestFileModel(string fileName)
    {
        // 创建一个虚拟的文件路径用于演示
        var testPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), fileName);
        
        // 如果文件不存在，创建一个临时文件
        if (!File.Exists(testPath))
        {
            File.WriteAllText(testPath, "演示用测试内容");
        }

        return DwgFileModel.FromFilePath(testPath);
    }
}

/// <summary>
/// DWG对话框演示运行器
/// </summary>
public static class DwgDialogDemoRunner
{
    /// <summary>
    /// 运行对话框演示
    /// </summary>
    /// <param name="viewModel">DwgManagerTabViewModel实例</param>
    public static async Task RunDemoAsync(DwgManagerTabViewModel viewModel)
    {
        var demo = new DwgDialogExample(viewModel);
        await demo.RunAllDemosAsync();
    }

    /// <summary>
    /// 快速演示 - 只演示一个功能
    /// </summary>
    /// <param name="viewModel">DwgManagerTabViewModel实例</param>
    public static async Task QuickDemoAsync(DwgManagerTabViewModel viewModel)
    {
        var demo = new DwgDialogExample(viewModel);
        
        Console.WriteLine("⚡ 快速演示模式 - 文件属性对话框");
        demo.DemoFilePropertiesDialog();
        Console.WriteLine("⚡ 快速演示完成");
    }

    /// <summary>
    /// 演示对话框的键盘快捷键功能
    /// </summary>
    public static void DemoKeyboardShortcuts()
    {
        Console.WriteLine("⌨️ 对话框键盘快捷键说明:");
        Console.WriteLine("📝 重命名对话框:");
        Console.WriteLine("   - Enter: 确认重命名");
        Console.WriteLine("   - Escape: 取消重命名");
        Console.WriteLine("   - 自动选中文件名部分（不包括扩展名）");
        Console.WriteLine();
        Console.WriteLine("🗑️ 删除确认对话框:");
        Console.WriteLine("   - Tab: 在按钮间切换");
        Console.WriteLine("   - Enter: 确认当前选中的按钮");
        Console.WriteLine("   - Escape: 取消删除");
        Console.WriteLine();
        Console.WriteLine("ℹ️ 文件属性对话框:");
        Console.WriteLine("   - Enter: 关闭对话框");
        Console.WriteLine("   - Escape: 关闭对话框");
    }

    /// <summary>
    /// 演示对话框的验证功能
    /// </summary>
    public static void DemoValidationFeatures()
    {
        Console.WriteLine("🛡️ 对话框验证功能说明:");
        Console.WriteLine("📝 重命名验证:");
        Console.WriteLine("   - 检查文件名是否为空");
        Console.WriteLine("   - 检查是否包含非法字符");
        Console.WriteLine("   - 自动添加原始扩展名");
        Console.WriteLine("   - 防止与原文件名相同");
        Console.WriteLine();
        Console.WriteLine("🗑️ 删除确认:");
        Console.WriteLine("   - 显示警告图标和文字");
        Console.WriteLine("   - 明确标注操作不可撤销");
        Console.WriteLine("   - 需要明确点击确认按钮");
        Console.WriteLine();
        Console.WriteLine("📁 文件操作:");
        Console.WriteLine("   - 自动处理文件名冲突");
        Console.WriteLine("   - 智能生成副本文件名");
        Console.WriteLine("   - 完整的错误处理和用户反馈");
    }
}
