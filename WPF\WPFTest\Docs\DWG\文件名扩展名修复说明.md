# 文件名扩展名修复说明

## 🎯 问题描述
在复制DWG文件时，发现复制后的文件名丢失了`.dwg`扩展名。

### 问题示例
```
原始文件名: GS_2025.6_副本.13-誉峰四期地下室与二期地下室交界局部梁板修改(1).dwg
复制后文件名: GS_2025.6_副本.13-誉峰四期地下室与二期地下室交界局部梁板修改(1)_副本
```

**问题原因：** 当文件名包含多个点号时，`Path.GetExtension()`只返回最后一个点号后的部分，导致扩展名处理不正确。

## 🔍 根本原因分析

### .NET Path类的行为
```csharp
var fileName = "GS_2025.6_副本.13-誉峰四期地下室与二期地下室交界局部梁板修改(1).dwg";

// 标准方法的结果
var nameWithoutExt = Path.GetFileNameWithoutExtension(fileName);
// 结果: "GS_2025.6_副本.13-誉峰四期地下室与二期地下室交界局部梁板修改(1)"

var extension = Path.GetExtension(fileName);
// 结果: ".dwg"
```

虽然`Path.GetExtension()`正确返回了`.dwg`，但在某些复杂情况下可能出现问题。

## ✅ 修复方案

### 改进的文件名处理逻辑
```csharp
// 改进的文件名处理：确保正确处理.dwg扩展名
string fileName, extension;
if (fileModel.FileName.EndsWith(".dwg", StringComparison.OrdinalIgnoreCase))
{
    // 对于.dwg文件，手动处理扩展名以避免多点号问题
    fileName = fileModel.FileName.Substring(0, fileModel.FileName.Length - 4);
    extension = ".dwg";
}
else
{
    // 对于其他文件，使用标准方法
    fileName = Path.GetFileNameWithoutExtension(fileModel.FileName);
    extension = Path.GetExtension(fileModel.FileName);
}

var copyName = $"{fileName}_副本{extension}";
```

### 修复的优势
1. **明确处理DWG文件** - 专门针对`.dwg`扩展名进行处理
2. **避免多点号问题** - 不依赖`Path.GetExtension()`的复杂逻辑
3. **保持向后兼容** - 对非DWG文件仍使用标准方法
4. **大小写不敏感** - 支持`.DWG`、`.dwg`等不同大小写

## 🔧 修复的文件和方法

### 1. 复制文件到当前目录
**文件:** `DwgManagerTabViewModel.cs`
**方法:** `CopyFileAsync()`

```csharp
// 修复前
var fileName = Path.GetFileNameWithoutExtension(fileModel.FileName);
var extension = Path.GetExtension(fileModel.FileName);

// 修复后
string fileName, extension;
if (fileModel.FileName.EndsWith(".dwg", StringComparison.OrdinalIgnoreCase))
{
    fileName = fileModel.FileName.Substring(0, fileModel.FileName.Length - 4);
    extension = ".dwg";
}
else
{
    fileName = Path.GetFileNameWithoutExtension(fileModel.FileName);
    extension = Path.GetExtension(fileModel.FileName);
}
```

### 2. 复制文件到桌面
**文件:** `DwgManagerTabViewModel.cs`
**方法:** `CopyToDesktopAsync()`

应用了相同的修复逻辑，确保复制到桌面的文件也保持正确的扩展名。

## 📋 测试用例

### 复杂文件名测试
```
✅ GS_2025.6_副本.13-誉峰四期地下室与二期地下室交界局部梁板修改(1).dwg
   → GS_2025.6_副本.13-誉峰四期地下室与二期地下室交界局部梁板修改(1)_副本.dwg

✅ A1.2-建筑平面图.dwg
   → A1.2-建筑平面图_副本.dwg

✅ 包含.多个.点号.的文件.dwg
   → 包含.多个.点号.的文件_副本.dwg

✅ 简单文件.dwg
   → 简单文件_副本.dwg
```

### 数字后缀测试
```
第一次复制: 原始文件.dwg → 原始文件_副本.dwg
第二次复制: 原始文件.dwg → 原始文件_副本1.dwg
第三次复制: 原始文件.dwg → 原始文件_副本2.dwg
```

## 🎯 修复效果

### 修复前
```
原始: GS_2025.6_副本.13-誉峰四期地下室与二期地下室交界局部梁板修改(1).dwg
复制: GS_2025.6_副本.13-誉峰四期地下室与二期地下室交界局部梁板修改(1)_副本  ❌
```

### 修复后
```
原始: GS_2025.6_副本.13-誉峰四期地下室与二期地下室交界局部梁板修改(1).dwg
复制: GS_2025.6_副本.13-誉峰四期地下室与二期地下室交界局部梁板修改(1)_副本.dwg  ✅
```

## 🔍 其他相关功能

### 不受影响的功能
以下功能使用不同的逻辑，不受此问题影响：
- **重命名文件** - 用户手动输入新文件名
- **新建DWG文件** - 使用`GenerateNewFileNameAsync`方法
- **基于模板创建** - 使用模板文件名生成

### 可能需要类似修复的功能
如果将来添加其他文件复制相关功能，建议使用相同的改进逻辑：
```csharp
// 推荐的DWG文件名处理模式
private static (string nameWithoutExt, string extension) SplitDwgFileName(string fileName)
{
    if (fileName.EndsWith(".dwg", StringComparison.OrdinalIgnoreCase))
    {
        return (fileName.Substring(0, fileName.Length - 4), ".dwg");
    }
    else
    {
        return (Path.GetFileNameWithoutExtension(fileName), Path.GetExtension(fileName));
    }
}
```

## 📝 更新日志

- **2025-01-27**: 修复文件复制时扩展名丢失问题
  - 改进`CopyFileAsync()`方法的文件名处理逻辑
  - 改进`CopyToDesktopAsync()`方法的文件名处理逻辑
  - 专门处理`.dwg`文件的扩展名，避免多点号问题
  - 保持对其他文件类型的向后兼容性

通过这次修复，确保了所有复制操作都能正确保留`.dwg`扩展名，无论原始文件名多么复杂。
