using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Windows;
using System.IO;
using System.Reflection;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels.InputControls
{
    /// <summary>
    /// TextBox 页面的 ViewModel，演示 TextBox 控件的各种功能
    /// </summary>
    public partial class TextBoxPageViewModel : ObservableObject
    {
        private readonly YLoggerInstance _logger = YLogger.For<TextBoxPageViewModel>();

        #region 属性

        /// <summary>
        /// 交互次数
        /// </summary>
        [ObservableProperty]
        private int interactionCount = 0;

        /// <summary>
        /// 最后执行的操作
        /// </summary>
        [ObservableProperty]
        private string lastAction = "无操作";

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        private string statusMessage = "欢迎使用 TextBox 示例库！";

        #region TextBox 值属性

        /// <summary>
        /// 标准 TextBox 的值
        /// </summary>
        [ObservableProperty]
        private string standardTextValue = "";

        /// <summary>
        /// 小型 TextBox 的值
        /// </summary>
        [ObservableProperty]
        private string smallTextValue = "";

        /// <summary>
        /// 大型 TextBox 的值
        /// </summary>
        [ObservableProperty]
        private string largeTextValue = "";

        /// <summary>
        /// 多行文本值
        /// </summary>
        [ObservableProperty]
        private string multilineTextValue = "";

        /// <summary>
        /// 透明样式文本值
        /// </summary>
        [ObservableProperty]
        private string transparentTextValue = "";

        /// <summary>
        /// 圆角样式文本值
        /// </summary>
        [ObservableProperty]
        private string roundedTextValue = "";

        /// <summary>
        /// 搜索文本值
        /// </summary>
        [ObservableProperty]
        private string searchTextValue = "";

        /// <summary>
        /// 错误状态文本值
        /// </summary>
        [ObservableProperty]
        private string errorTextValue = "";

        /// <summary>
        /// 成功状态文本值
        /// </summary>
        [ObservableProperty]
        private string successTextValue = "";

        #region 组合控件属性

        /// <summary>
        /// 用户名值
        /// </summary>
        [ObservableProperty]
        private string usernameValue = "";

        /// <summary>
        /// 邮箱值
        /// </summary>
        [ObservableProperty]
        private string emailValue = "";

        /// <summary>
        /// 描述值
        /// </summary>
        [ObservableProperty]
        private string descriptionValue = "";

        /// <summary>
        /// 全名值
        /// </summary>
        [ObservableProperty]
        private string fullNameValue = "";

        /// <summary>
        /// 公司值
        /// </summary>
        [ObservableProperty]
        private string companyValue = "";

        /// <summary>
        /// 电话值
        /// </summary>
        [ObservableProperty]
        private string phoneValue = "";

        /// <summary>
        /// 地址值
        /// </summary>
        [ObservableProperty]
        private string addressValue = "";

        #region 自定义控件属性

        /// <summary>
        /// 自定义用户名值
        /// </summary>
        [ObservableProperty]
        private string customUsernameValue = "";

        /// <summary>
        /// 自定义邮箱值
        /// </summary>
        [ObservableProperty]
        private string customEmailValue = "";

        /// <summary>
        /// 自定义描述值
        /// </summary>
        [ObservableProperty]
        private string customDescriptionValue = "";

        /// <summary>
        /// 自定义全名值
        /// </summary>
        [ObservableProperty]
        private string customFullNameValue = "";

        /// <summary>
        /// 自定义公司值
        /// </summary>
        [ObservableProperty]
        private string customCompanyValue = "";

        /// <summary>
        /// 自定义地址值
        /// </summary>
        [ObservableProperty]
        private string customAddressValue = "";

        /// <summary>
        /// 自定义ID值
        /// </summary>
        [ObservableProperty]
        private string customIdValue = "";

        /// <summary>
        /// 自定义电话值
        /// </summary>
        [ObservableProperty]
        private string customPhoneValue = "";

        /// <summary>
        /// 自定义短标签值
        /// </summary>
        [ObservableProperty]
        private string customShortLabelValue = "";

        /// <summary>
        /// 自定义中等标签值
        /// </summary>
        [ObservableProperty]
        private string customMediumLabelValue = "";

        /// <summary>
        /// 自定义长标签值
        /// </summary>
        [ObservableProperty]
        private string customLongLabelValue = "";

        #endregion

        #endregion

        #endregion

        #region 代码示例属性

        /// <summary>
        /// 基础功能 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 基础功能 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级功能 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级功能 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 样式 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string StylesXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 组合控件 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string CombinedXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 组合控件 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string CombinedCSharpExample { get; set; } = string.Empty;

        #endregion

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化 TextBoxPageViewModel
        /// </summary>
        public TextBoxPageViewModel()
        {
            StatusMessage = "TextBox 示例库已加载，开始体验各种功能！";
            InitializeCodeExamples();
            
            // 监听文本变化
            PropertyChanged += OnPropertyChanged;
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 属性变化事件处理
        /// </summary>
        private void OnPropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName?.EndsWith("TextValue") == true)
            {
                InteractionCount++;
                LastAction = $"文本输入: {e.PropertyName}";
                StatusMessage = $"🎯 {e.PropertyName} 文本已更新";
                _logger.Info($"文本框值变化: {e.PropertyName}");
            }
        }

        #endregion

        #region 命令

        /// <summary>
        /// 通用交互命令
        /// </summary>
        /// <param name="parameter">操作参数</param>
        [RelayCommand]
        private void HandleInteraction(string? parameter)
        {
            InteractionCount++;
            LastAction = parameter ?? "未知操作";
            
            // 根据不同的操作显示不同的状态消息
            StatusMessage = parameter switch
            {
                "标准输入" => "🎯 标准 TextBox 交互",
                "小型输入" => "🎯 小型 TextBox 交互",
                "大型输入" => "🎯 大型 TextBox 交互",
                _ => $"🎯 执行了操作: {parameter}"
            };
        }

        /// <summary>
        /// 重置计数命令
        /// </summary>
        [RelayCommand]
        private void ResetCount()
        {
            InteractionCount = 0;
            StatusMessage = "计数已重置";
            _logger.Info("重置了交互计数");
        }

        /// <summary>
        /// 清除状态命令
        /// </summary>
        [RelayCommand]
        private void ClearStatus()
        {
            LastAction = "无操作";
            StatusMessage = "状态已清除";
            StandardTextValue = "";
            SmallTextValue = "";
            LargeTextValue = "";
            MultilineTextValue = "";
            TransparentTextValue = "";
            RoundedTextValue = "";
            SearchTextValue = "";
            ErrorTextValue = "";
            SuccessTextValue = "";

            // 清除组合控件值
            UsernameValue = "";
            EmailValue = "";
            DescriptionValue = "";
            FullNameValue = "";
            CompanyValue = "";
            PhoneValue = "";
            AddressValue = "";

            // 清除自定义控件值
            CustomUsernameValue = "";
            CustomEmailValue = "";
            CustomDescriptionValue = "";
            CustomFullNameValue = "";
            CustomCompanyValue = "";
            CustomAddressValue = "";
            CustomIdValue = "";
            CustomPhoneValue = "";
            CustomShortLabelValue = "";
            CustomMediumLabelValue = "";
            CustomLongLabelValue = "";

            _logger.Info("清除了状态信息");
        }

        /// <summary>
        /// 清空多行文本命令
        /// </summary>
        [RelayCommand]
        private void ClearMultilineText()
        {
            MultilineTextValue = "";
            StatusMessage = "多行文本已清空";
            InteractionCount++;
            LastAction = "清空多行文本";
            _logger.Info("清空了多行文本内容");
        }

        /// <summary>
        /// 添加示例文本命令
        /// </summary>
        [RelayCommand]
        private void AddSampleText()
        {
            var sampleText = @"这是一个多行文本框示例。

您可以在这里输入多行文本，文本会自动换行。

支持的功能包括：
• 自动换行
• 垂直滚动条
• 回车键换行
• 文本选择和复制
• 实时数据绑定

试试输入更多内容来测试滚动功能！

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.

Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.";

            MultilineTextValue = sampleText;
            StatusMessage = "已添加示例文本到多行文本框";
            InteractionCount++;
            LastAction = "添加示例文本";
            _logger.Info("添加了示例文本到多行文本框");
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化代码示例
        /// </summary>
        private void InitializeCodeExamples()
        {
            try
            {
                // 获取代码示例文件的基础路径
                var baseDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
                var codeExamplesPath = Path.Combine(baseDirectory, "CodeExamples", "Controls", "TextBox");

                _logger.Info($"基础目录: {baseDirectory}");
                _logger.Info($"代码示例路径: {codeExamplesPath}");
                _logger.Info($"目录是否存在: {Directory.Exists(codeExamplesPath)}");

                // 读取各种示例文件
                BasicXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.xaml.txt"));
                BasicCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.cs.txt"));
                AdvancedXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Advanced.xaml.txt"));
                AdvancedCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Advanced.cs.txt"));
                StylesXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Styles.xaml.txt"));
                CombinedXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Combined.xaml.txt"));
                CombinedCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Combined.cs.txt"));

                _logger.Info("TextBox 代码示例加载完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"加载 TextBox 代码示例失败: {ex.Message}");
                SetDefaultCodeExamples();
            }
        }

        /// <summary>
        /// 读取代码示例文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件内容</returns>
        private string ReadCodeExampleFile(string filePath)
        {
            _logger.Info($"尝试读取文件: {filePath}");
            
            if (File.Exists(filePath))
            {
                var content = File.ReadAllText(filePath);
                _logger.Info($"成功读取文件，内容长度: {content.Length}");
                return content;
            }
            else
            {
                _logger.Warning($"代码示例文件不存在: {filePath}");
                return $"// 代码示例文件不存在: {Path.GetFileName(filePath)}\n// 完整路径: {filePath}";
            }
        }

        /// <summary>
        /// 设置默认代码示例（当文件读取失败时使用）
        /// </summary>
        private void SetDefaultCodeExamples()
        {
            BasicXamlExample = "<!-- TextBox 基础示例 -->\n<!-- 在这里添加基础用法示例 -->";
            BasicCSharpExample = "// TextBox C# 基础示例\n// 在这里添加 C# 代码示例";
            AdvancedXamlExample = "<!-- TextBox 高级示例 -->\n<!-- 在这里添加高级用法示例 -->";
            AdvancedCSharpExample = "// TextBox C# 高级示例\n// 在这里添加高级 C# 代码示例";
            StylesXamlExample = "<!-- TextBox 样式示例 -->\n<!-- 在这里添加样式使用示例 -->";
            CombinedXamlExample = "<!-- TextBlock + TextBox 组合控件示例 -->\n<!-- 在这里添加组合控件示例 -->";
            CombinedCSharpExample = "// TextBlock + TextBox 组合控件 C# 示例\n// 在这里添加组合控件 C# 代码示例";
        }

        #endregion
    }
}
