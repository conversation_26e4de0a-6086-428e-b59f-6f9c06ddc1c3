using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.ComponentModel;
using System.IO;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Media;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels.InputControls
{
    /// <summary>
    /// PasswordBox 页面的 ViewModel，演示 PasswordBox 控件的各种功能
    /// </summary>
    public partial class PasswordBoxPageViewModel : ObservableObject
    {
        private readonly YLoggerInstance _logger = YLogger.For<PasswordBoxPageViewModel>();

        #region 属性

        /// <summary>
        /// 交互次数
        /// </summary>
        [ObservableProperty]
        private int interactionCount = 0;

        /// <summary>
        /// 最后执行的操作
        /// </summary>
        [ObservableProperty]
        private string lastAction = "无操作";

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        private string statusMessage = "欢迎使用 PasswordBox 示例库！";

        /// <summary>
        /// 验证消息
        /// </summary>
        [ObservableProperty]
        private string validationMessage = string.Empty;

        /// <summary>
        /// 验证消息颜色
        /// </summary>
        [ObservableProperty]
        private Brush validationMessageColor = Brushes.Gray;

        /// <summary>
        /// 是否显示密码详情
        /// </summary>
        [ObservableProperty]
        private bool showPasswordDetails = false;

        /// <summary>
        /// 演示密码
        /// </summary>
        [ObservableProperty]
        private string demoPassword = string.Empty;

        /// <summary>
        /// 演示验证结果
        /// </summary>
        [ObservableProperty]
        private string demoVerificationResult = string.Empty;

        /// <summary>
        /// 演示验证结果颜色
        /// </summary>
        [ObservableProperty]
        private Brush demoVerificationColor = Brushes.Gray;

        /// <summary>
        /// 存储的测试密码哈希
        /// </summary>
        private string storedTestPasswordHash = string.Empty;

        #region 密码相关属性

        /// <summary>
        /// 基础密码
        /// </summary>
        [ObservableProperty]
        private string basicPassword = string.Empty;

        /// <summary>
        /// 确认密码
        /// </summary>
        [ObservableProperty]
        private string confirmPassword = string.Empty;

        /// <summary>
        /// 可切换显示的密码
        /// </summary>
        [ObservableProperty]
        private string togglePassword = string.Empty;

        /// <summary>
        /// 密码是否可见
        /// </summary>
        [ObservableProperty]
        private bool isPasswordVisible = false;

        /// <summary>
        /// 强度检测密码
        /// </summary>
        [ObservableProperty]
        private string strengthPassword = string.Empty;

        /// <summary>
        /// 密码强度值 (0-100)
        /// </summary>
        [ObservableProperty]
        private double passwordStrength = 0;

        /// <summary>
        /// 密码强度文本
        /// </summary>
        [ObservableProperty]
        private string passwordStrengthText = "请输入密码";

        /// <summary>
        /// 密码强度颜色
        /// </summary>
        [ObservableProperty]
        private Brush passwordStrengthColor = Brushes.Gray;

        /// <summary>
        /// 密码验证结果
        /// </summary>
        [ObservableProperty]
        private PasswordValidationResult validationResult = new();

        /// <summary>
        /// 密码匹配状态
        /// </summary>
        [ObservableProperty]
        private bool passwordsMatch = false;

        /// <summary>
        /// 密码匹配消息
        /// </summary>
        [ObservableProperty]
        private string passwordMatchMessage = string.Empty;

        /// <summary>
        /// 密码匹配颜色
        /// </summary>
        [ObservableProperty]
        private Brush passwordMatchColor = Brushes.Gray;

        /// <summary>
        /// 生成的密码
        /// </summary>
        [ObservableProperty]
        private string generatedPassword = string.Empty;

        /// <summary>
        /// 密码生成长度
        /// </summary>
        [ObservableProperty]
        private int passwordLength = 12;

        /// <summary>
        /// 是否包含大写字母
        /// </summary>
        [ObservableProperty]
        private bool includeUppercase = true;

        /// <summary>
        /// 是否包含小写字母
        /// </summary>
        [ObservableProperty]
        private bool includeLowercase = true;

        /// <summary>
        /// 是否包含数字
        /// </summary>
        [ObservableProperty]
        private bool includeNumbers = true;

        /// <summary>
        /// 是否包含特殊字符
        /// </summary>
        [ObservableProperty]
        private bool includeSymbols = true;

        /// <summary>
        /// 是否避免相似字符
        /// </summary>
        [ObservableProperty]
        private bool avoidSimilarChars = false;

        #endregion

        #region 代码示例属性

        /// <summary>
        /// 基础功能 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 基础功能 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级功能 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级功能 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 样式 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string StylesXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 安全特性 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string SecurityXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 安全特性 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string SecurityCSharpExample { get; set; } = string.Empty;

        #endregion

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化 PasswordBoxPageViewModel
        /// </summary>
        public PasswordBoxPageViewModel()
        {
            StatusMessage = "PasswordBox 示例库已加载，开始体验各种安全功能！";
            InitializeCodeExamples();

            // 监听密码变化
            PropertyChanged += (s, e) =>
            {
                switch (e.PropertyName)
                {
                    case nameof(StrengthPassword):
                        UpdatePasswordStrength();
                        ValidatePasswordRules(StrengthPassword);
                        break;
                    case nameof(BasicPassword):
                    case nameof(ConfirmPassword):
                        ValidatePasswordMatch();
                        // 同时验证基础密码的详细要求
                        if (e.PropertyName == nameof(BasicPassword))
                        {
                            ValidatePasswordRules(BasicPassword);
                        }
                        break;
                    case nameof(PasswordLength):
                    case nameof(IncludeUppercase):
                    case nameof(IncludeLowercase):
                    case nameof(IncludeNumbers):
                    case nameof(IncludeSymbols):
                    case nameof(AvoidSimilarChars):
                        // 密码生成参数变化时自动重新生成
                        if (!string.IsNullOrEmpty(GeneratedPassword))
                        {
                            GeneratePassword();
                        }
                        break;
                }
            };
        }

        #endregion

        #region 命令

        /// <summary>
        /// 通用交互命令
        /// </summary>
        /// <param name="parameter">操作参数</param>
        [RelayCommand]
        private void HandleInteraction(string? parameter)
        {
            InteractionCount++;
            LastAction = parameter ?? "未知操作";
            
            // 根据不同的操作显示不同的状态消息
            StatusMessage = parameter switch
            {
                "基础密码输入" => "🔒 输入了基础密码",
                "切换密码显示" => "👁️ 切换了密码显示状态",
                "密码强度检测" => "💪 检测了密码强度",
                _ => $"🎯 执行了操作: {parameter}"
            };
        }

        /// <summary>
        /// 切换密码可见性命令
        /// </summary>
        [RelayCommand]
        private void TogglePasswordVisibility()
        {
            IsPasswordVisible = !IsPasswordVisible;
            HandleInteraction("切换密码显示");
        }

        /// <summary>
        /// 重置计数命令
        /// </summary>
        [RelayCommand]
        private void ResetCount()
        {
            InteractionCount = 0;
            StatusMessage = "计数已重置";
            _logger.Info("重置了交互计数");
        }

        /// <summary>
        /// 清除密码命令
        /// </summary>
        [RelayCommand]
        private void ClearPasswords()
        {
            BasicPassword = string.Empty;
            ConfirmPassword = string.Empty;
            TogglePassword = string.Empty;
            StrengthPassword = string.Empty;
            GeneratedPassword = string.Empty;
            LastAction = "清除密码";
            StatusMessage = "所有密码已清除";
            _logger.Info("清除了所有密码");
        }

        /// <summary>
        /// 生成密码命令
        /// </summary>
        [RelayCommand]
        private void GeneratePassword()
        {
            try
            {
                GeneratedPassword = CreateSecurePassword(
                    PasswordLength,
                    IncludeUppercase,
                    IncludeLowercase,
                    IncludeNumbers,
                    IncludeSymbols,
                    AvoidSimilarChars);

                StatusMessage = $"已生成 {PasswordLength} 位安全密码";
                HandleInteraction("生成密码");
            }
            catch (Exception ex)
            {
                StatusMessage = $"密码生成失败: {ex.Message}";
                _logger.Error($"密码生成失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 复制密码命令
        /// </summary>
        [RelayCommand]
        private void CopyPassword()
        {
            try
            {
                if (!string.IsNullOrEmpty(GeneratedPassword))
                {
                    Clipboard.SetText(GeneratedPassword);
                    StatusMessage = "密码已复制到剪贴板";
                    HandleInteraction("复制密码");
                }
                else
                {
                    StatusMessage = "没有可复制的密码";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"复制失败: {ex.Message}";
                _logger.Error($"复制密码失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证密码命令
        /// </summary>
        [RelayCommand]
        private void ValidatePassword()
        {
            try
            {
                // 清除之前的验证消息
                ValidationMessage = string.Empty;

                if (string.IsNullOrEmpty(BasicPassword))
                {
                    ShowValidationResult(false, "请输入密码");
                    return;
                }

                if (string.IsNullOrEmpty(ConfirmPassword))
                {
                    ShowValidationResult(false, "请输入确认密码");
                    return;
                }

                if (!PasswordsMatch)
                {
                    ShowValidationResult(false, "两次输入的密码不一致");
                    return;
                }

                // 执行详细的密码安全验证
                var result = ValidatePasswordSecurity(BasicPassword);
                if (result.IsValid)
                {
                    // 生成密码哈希（模拟加密存储）
                    var passwordHash = HashPassword(BasicPassword);
                    var strength = CalculatePasswordStrength(BasicPassword);

                    var successMessage = $"✅ 密码验证通过！\n" +
                                       $"🔒 密码强度: {GetStrengthLevel(strength)} ({strength:F0}分)\n" +
                                       $"🛡️ 安全哈希: {passwordHash[..16]}...\n" +
                                       $"📅 验证时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}";

                    ShowValidationResult(true, successMessage);
                    StatusMessage = "✅ 密码验证通过！";
                }
                else
                {
                    var failureMessage = $"❌ 密码验证失败\n" +
                                       $"📋 失败原因: {result.Message}\n" +
                                       $"💡 建议: 请确保密码包含大小写字母、数字和特殊字符";

                    ShowValidationResult(false, failureMessage);
                    StatusMessage = $"❌ 密码验证失败: {result.Message}";
                }

                HandleInteraction("密码验证");
            }
            catch (Exception ex)
            {
                ShowValidationResult(false, $"验证过程出错: {ex.Message}");
                StatusMessage = "验证过程出错";
                _logger.Error($"密码验证失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检测密码泄露命令
        /// </summary>
        [RelayCommand]
        private async Task CheckPasswordBreach()
        {
            if (string.IsNullOrEmpty(BasicPassword))
            {
                StatusMessage = "请输入密码进行检测";
                return;
            }

            StatusMessage = "正在检测密码安全性...";

            try
            {
                // 模拟异步检测过程
                await Task.Delay(1000);

                // 模拟检测逻辑：常见弱密码被标记为已泄露
                var commonPasswords = new[] { "123456", "password", "123456789", "qwerty", "abc123", "password123" };
                var isCompromised = commonPasswords.Any(p => BasicPassword.ToLower().Contains(p.ToLower()));

                if (isCompromised)
                {
                    StatusMessage = "⚠️ 此密码已在数据泄露中发现，建议立即更换";
                }
                else
                {
                    StatusMessage = "✅ 此密码未在已知泄露数据库中发现";
                }

                HandleInteraction("泄露检测");
            }
            catch (Exception ex)
            {
                StatusMessage = $"检测失败: {ex.Message}";
                _logger.Error($"密码泄露检测失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 双因素认证命令
        /// </summary>
        [RelayCommand]
        private void VerifyTwoFactor()
        {
            if (string.IsNullOrEmpty(BasicPassword))
            {
                StatusMessage = "请输入密码";
                return;
            }

            // 这里可以添加验证码输入，暂时模拟
            StatusMessage = "🔐 双因素认证模拟成功";
            HandleInteraction("双因素认证");
        }

        /// <summary>
        /// 显示密码详情命令
        /// </summary>
        [RelayCommand]
        private void ShowPassword()
        {
            ShowPasswordDetails = !ShowPasswordDetails;

            var action = ShowPasswordDetails ? "显示" : "隐藏";
            StatusMessage = $"👁️ {action}了密码详情";
            HandleInteraction($"{action}密码详情");
        }

        /// <summary>
        /// 生成测试密码命令
        /// </summary>
        [RelayCommand]
        private void GenerateTestPassword()
        {
            try
            {
                // 生成一个测试密码
                var testPassword = "Test123!@#";
                DemoPassword = testPassword;

                // 存储密码哈希用于验证
                storedTestPasswordHash = HashPassword(testPassword);

                DemoVerificationResult = $"✅ 已生成测试密码: {testPassword}\n🔒 密码哈希已存储用于验证";
                DemoVerificationColor = new SolidColorBrush(Colors.Green);

                StatusMessage = "生成了测试密码";
                HandleInteraction("生成测试密码");
            }
            catch (Exception ex)
            {
                DemoVerificationResult = $"❌ 生成测试密码失败: {ex.Message}";
                DemoVerificationColor = new SolidColorBrush(Colors.Red);
                _logger.Error($"生成测试密码失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证演示密码命令
        /// </summary>
        [RelayCommand]
        private void VerifyDemoPassword()
        {
            try
            {
                if (string.IsNullOrEmpty(DemoPassword))
                {
                    DemoVerificationResult = "❌ 请输入要验证的密码";
                    DemoVerificationColor = new SolidColorBrush(Colors.Red);
                    return;
                }

                if (string.IsNullOrEmpty(storedTestPasswordHash))
                {
                    DemoVerificationResult = "❌ 没有存储的密码哈希，请先生成测试密码";
                    DemoVerificationColor = new SolidColorBrush(Colors.Red);
                    return;
                }

                // 验证密码
                var isValid = VerifyPassword(DemoPassword, storedTestPasswordHash);
                var inputHash = HashPassword(DemoPassword);

                if (isValid)
                {
                    DemoVerificationResult = $"✅ 密码验证成功！\n" +
                                           $"🔍 输入密码: {DemoPassword}\n" +
                                           $"🔒 输入哈希: {inputHash[..16]}...\n" +
                                           $"🛡️ 存储哈希: {storedTestPasswordHash[..16]}...\n" +
                                           $"✅ 哈希匹配: 是\n" +
                                           $"📅 验证时间: {DateTime.Now:HH:mm:ss}";
                    DemoVerificationColor = new SolidColorBrush(Colors.Green);
                    StatusMessage = "✅ 密码核对成功";
                }
                else
                {
                    DemoVerificationResult = $"❌ 密码验证失败！\n" +
                                           $"🔍 输入密码: {DemoPassword}\n" +
                                           $"🔒 输入哈希: {inputHash[..16]}...\n" +
                                           $"🛡️ 存储哈希: {storedTestPasswordHash[..16]}...\n" +
                                           $"❌ 哈希匹配: 否\n" +
                                           $"📅 验证时间: {DateTime.Now:HH:mm:ss}";
                    DemoVerificationColor = new SolidColorBrush(Colors.Red);
                    StatusMessage = "❌ 密码核对失败";
                }

                HandleInteraction("密码核对");
            }
            catch (Exception ex)
            {
                DemoVerificationResult = $"❌ 验证过程出错: {ex.Message}";
                DemoVerificationColor = new SolidColorBrush(Colors.Red);
                StatusMessage = "验证过程出错";
                _logger.Error($"密码核对失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示验证结果
        /// </summary>
        /// <param name="isSuccess">是否成功</param>
        /// <param name="message">消息内容</param>
        private void ShowValidationResult(bool isSuccess, string message)
        {
            ValidationMessage = message;
            ValidationMessageColor = isSuccess
                ? new SolidColorBrush(Colors.Green)
                : new SolidColorBrush(Colors.Red);
        }

        /// <summary>
        /// 密码哈希加密（使用 SHA-256）
        /// </summary>
        /// <param name="password">原始密码</param>
        /// <returns>哈希值</returns>
        private string HashPassword(string password)
        {
            using var sha256 = SHA256.Create();
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password + "ZyloSalt2024")); // 添加盐值
            return Convert.ToBase64String(hashedBytes);
        }

        /// <summary>
        /// 验证密码哈希
        /// </summary>
        /// <param name="password">原始密码</param>
        /// <param name="hash">存储的哈希值</param>
        /// <returns>是否匹配</returns>
        private bool VerifyPassword(string password, string hash)
        {
            var computedHash = HashPassword(password);
            return computedHash == hash;
        }

        /// <summary>
        /// 获取密码强度等级描述
        /// </summary>
        /// <param name="strength">强度分数</param>
        /// <returns>等级描述</returns>
        private string GetStrengthLevel(double strength)
        {
            return strength switch
            {
                >= 80 => "强",
                >= 60 => "良好",
                >= 40 => "一般",
                _ => "弱"
            };
        }



        #endregion

        #region 私有方法

        /// <summary>
        /// 更新密码强度
        /// </summary>
        private void UpdatePasswordStrength()
        {
            if (string.IsNullOrEmpty(StrengthPassword))
            {
                PasswordStrength = 0;
                PasswordStrengthText = "请输入密码";
                PasswordStrengthColor = Brushes.Gray;
                return;
            }

            var strength = CalculatePasswordStrength(StrengthPassword);
            PasswordStrength = strength;

            (PasswordStrengthText, PasswordStrengthColor) = strength switch
            {
                < 25 => ("弱", Brushes.Red),
                < 50 => ("一般", Brushes.Orange),
                < 75 => ("良好", Brushes.Yellow),
                _ => ("强", Brushes.Green)
            };

            HandleInteraction("密码强度检测");
        }

        /// <summary>
        /// 计算密码强度
        /// </summary>
        /// <param name="password">密码</param>
        /// <returns>强度值 (0-100)</returns>
        private static double CalculatePasswordStrength(string password)
        {
            if (string.IsNullOrEmpty(password)) return 0;

            double strength = 0;

            // 长度评分 (最多40分)
            strength += Math.Min(password.Length * 4, 40);

            // 包含小写字母 (10分)
            if (Regex.IsMatch(password, @"[a-z]")) strength += 10;

            // 包含大写字母 (10分)
            if (Regex.IsMatch(password, @"[A-Z]")) strength += 10;

            // 包含数字 (10分)
            if (Regex.IsMatch(password, @"\d")) strength += 10;

            // 包含特殊字符 (15分)
            if (Regex.IsMatch(password, @"[!@#$%^&*(),.?""':;{}|<>]")) strength += 15;

            // 字符种类多样性 (5分)
            var uniqueChars = password.Distinct().Count();
            if (uniqueChars > password.Length * 0.7) strength += 5;

            return Math.Min(strength, 100);
        }

        /// <summary>
        /// 验证密码匹配
        /// </summary>
        private void ValidatePasswordMatch()
        {
            if (string.IsNullOrEmpty(BasicPassword) && string.IsNullOrEmpty(ConfirmPassword))
            {
                PasswordsMatch = false;
                PasswordMatchMessage = string.Empty;
                PasswordMatchColor = Brushes.Gray;
                return;
            }

            if (string.IsNullOrEmpty(BasicPassword) || string.IsNullOrEmpty(ConfirmPassword))
            {
                PasswordsMatch = false;
                PasswordMatchMessage = "请输入完整的密码";
                PasswordMatchColor = Brushes.Orange;
                return;
            }

            if (BasicPassword == ConfirmPassword)
            {
                PasswordsMatch = true;
                PasswordMatchMessage = "✅ 密码匹配";
                PasswordMatchColor = Brushes.Green;
            }
            else
            {
                PasswordsMatch = false;
                PasswordMatchMessage = "❌ 密码不匹配";
                PasswordMatchColor = Brushes.Red;
            }
        }

        /// <summary>
        /// 验证密码安全性
        /// </summary>
        /// <param name="password">密码</param>
        /// <returns>验证结果</returns>
        private (bool IsValid, string Message) ValidatePasswordSecurity(string password)
        {
            if (string.IsNullOrEmpty(password))
                return (false, "密码不能为空");

            if (password.Length < 8)
                return (false, "密码长度至少8位");

            if (password.Length > 128)
                return (false, "密码长度不能超过128位");

            if (!Regex.IsMatch(password, @"[a-z]"))
                return (false, "密码必须包含小写字母");

            if (!Regex.IsMatch(password, @"[A-Z]"))
                return (false, "密码必须包含大写字母");

            if (!Regex.IsMatch(password, @"\d"))
                return (false, "密码必须包含数字");

            if (!Regex.IsMatch(password, @"[!@#$%^&*(),.?""':;{}|<>]"))
                return (false, "密码必须包含特殊字符");

            // 检查常见弱密码
            var commonPasswords = new[] { "password", "123456", "123456789", "qwerty", "abc123", "password123" };
            if (commonPasswords.Any(p => password.ToLower().Contains(p.ToLower())))
                return (false, "密码不能包含常见弱密码模式");

            // 检查重复字符
            if (HasRepeatingChars(password, 3))
                return (false, "密码不能包含连续3个相同字符");

            // 检查键盘序列
            if (HasKeyboardSequence(password))
                return (false, "密码不能包含键盘序列");

            return (true, "密码符合安全要求");
        }

        /// <summary>
        /// 验证密码详细要求
        /// </summary>
        /// <param name="password">密码</param>
        private void ValidatePasswordRules(string password)
        {
            var result = new PasswordValidationResult();

            if (!string.IsNullOrEmpty(password))
            {
                result.HasMinLength = password.Length >= 8;
                result.HasMaxLength = password.Length <= 128;
                result.HasUppercase = Regex.IsMatch(password, @"[A-Z]");
                result.HasLowercase = Regex.IsMatch(password, @"[a-z]");
                result.HasNumbers = Regex.IsMatch(password, @"\d");
                result.HasSymbols = Regex.IsMatch(password, @"[!@#$%^&*(),.?""':;{}|<>]");
                result.NoRepeatingChars = !HasRepeatingChars(password, 3);
                result.NoKeyboardSequence = !HasKeyboardSequence(password);
                result.NoCommonPatterns = !HasCommonPatterns(password);
            }

            ValidationResult = result;
        }

        /// <summary>
        /// 检查重复字符
        /// </summary>
        private static bool HasRepeatingChars(string password, int maxRepeats)
        {
            for (int i = 0; i <= password.Length - maxRepeats; i++)
            {
                var currentChar = password[i];
                var count = 1;

                for (int j = i + 1; j < password.Length && password[j] == currentChar; j++)
                {
                    count++;
                    if (count >= maxRepeats) return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 检查键盘序列
        /// </summary>
        private static bool HasKeyboardSequence(string password)
        {
            var sequences = new[]
            {
                "qwerty", "asdf", "zxcv", "123456", "abcdef",
                "qwertyuiop", "asdfghjkl", "zxcvbnm"
            };

            return sequences.Any(seq =>
                password.ToLower().Contains(seq) ||
                password.ToLower().Contains(new string(seq.Reverse().ToArray())));
        }

        /// <summary>
        /// 检查常见模式
        /// </summary>
        private static bool HasCommonPatterns(string password)
        {
            var patterns = new[]
            {
                "password", "admin", "user", "login", "welcome",
                "123456", "qwerty", "abc123", "password123"
            };

            return patterns.Any(pattern => password.ToLower().Contains(pattern.ToLower()));
        }

        /// <summary>
        /// 初始化代码示例
        /// </summary>
        private void InitializeCodeExamples()
        {
            try
            {
                // 获取代码示例文件的基础路径
                var baseDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
                var codeExamplesPath = Path.Combine(baseDirectory, "CodeExamples", "Controls", "PasswordBox");

                _logger.Info($"基础目录: {baseDirectory}");
                _logger.Info($"代码示例路径: {codeExamplesPath}");
                _logger.Info($"目录是否存在: {Directory.Exists(codeExamplesPath)}");

                // 读取各种示例文件
                BasicXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.xaml.txt"));
                BasicCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.cs.txt"));
                AdvancedXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Advanced.xaml.txt"));
                AdvancedCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Advanced.cs.txt"));
                StylesXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Styles.xaml.txt"));
                SecurityXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Security.xaml.txt"));
                SecurityCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Security.cs.txt"));

                _logger.Info("PasswordBox 代码示例加载完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"加载 PasswordBox 代码示例失败: {ex.Message}");
                SetDefaultCodeExamples();
            }
        }

        /// <summary>
        /// 读取代码示例文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件内容</returns>
        private string ReadCodeExampleFile(string filePath)
        {
            _logger.Info($"尝试读取文件: {filePath}");
            
            if (File.Exists(filePath))
            {
                var content = File.ReadAllText(filePath);
                _logger.Info($"成功读取文件，内容长度: {content.Length}");
                return content;
            }
            else
            {
                _logger.Warning($"代码示例文件不存在: {filePath}");
                return $"// 代码示例文件不存在: {Path.GetFileName(filePath)}\n// 完整路径: {filePath}";
            }
        }

        /// <summary>
        /// 创建安全密码
        /// </summary>
        private static string CreateSecurePassword(int length, bool includeUppercase, bool includeLowercase,
            bool includeNumbers, bool includeSymbols, bool avoidSimilarChars)
        {
            if (length < 4) throw new ArgumentException("密码长度至少为4位");
            if (length > 128) throw new ArgumentException("密码长度不能超过128位");

            const string lowercase = "abcdefghijklmnopqrstuvwxyz";
            const string uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            const string numbers = "0123456789";
            const string symbols = "!@#$%^&*()_+-=[]{}|;:,.<>?";
            const string similarChars = "il1Lo0O";

            var charSet = string.Empty;
            var requiredChars = new List<char>();

            if (includeLowercase)
            {
                charSet += lowercase;
                requiredChars.Add(lowercase[Random.Shared.Next(lowercase.Length)]);
            }

            if (includeUppercase)
            {
                charSet += uppercase;
                requiredChars.Add(uppercase[Random.Shared.Next(uppercase.Length)]);
            }

            if (includeNumbers)
            {
                charSet += numbers;
                requiredChars.Add(numbers[Random.Shared.Next(numbers.Length)]);
            }

            if (includeSymbols)
            {
                charSet += symbols;
                requiredChars.Add(symbols[Random.Shared.Next(symbols.Length)]);
            }

            if (string.IsNullOrEmpty(charSet))
                throw new ArgumentException("至少需要选择一种字符类型");

            if (avoidSimilarChars)
            {
                charSet = new string(charSet.Where(c => !similarChars.Contains(c)).ToArray());
            }

            var password = new List<char>(requiredChars);

            // 填充剩余长度
            for (int i = requiredChars.Count; i < length; i++)
            {
                password.Add(charSet[Random.Shared.Next(charSet.Length)]);
            }

            // 打乱顺序
            for (int i = password.Count - 1; i > 0; i--)
            {
                int j = Random.Shared.Next(i + 1);
                (password[i], password[j]) = (password[j], password[i]);
            }

            return new string(password.ToArray());
        }

        /// <summary>
        /// 设置默认代码示例（当文件读取失败时使用）
        /// </summary>
        private void SetDefaultCodeExamples()
        {
            BasicXamlExample = "<!-- PasswordBox 基础示例 -->\n<!-- 在这里添加基础用法示例 -->";
            BasicCSharpExample = "// PasswordBox C# 基础示例\n// 在这里添加 C# 代码示例";
            AdvancedXamlExample = "<!-- PasswordBox 高级示例 -->\n<!-- 在这里添加高级用法示例 -->";
            AdvancedCSharpExample = "// PasswordBox C# 高级示例\n// 在这里添加高级 C# 代码示例";
            StylesXamlExample = "<!-- PasswordBox 样式示例 -->\n<!-- 在这里添加样式使用示例 -->";
            SecurityXamlExample = "<!-- PasswordBox 安全特性示例 -->\n<!-- 在这里添加安全特性示例 -->";
            SecurityCSharpExample = "// PasswordBox 安全特性 C# 示例\n// 在这里添加安全特性 C# 代码示例";
        }

        #endregion
    }

    /// <summary>
    /// 密码验证结果
    /// </summary>
    public partial class PasswordValidationResult : ObservableObject
    {
        /// <summary>
        /// 是否满足最小长度要求
        /// </summary>
        [ObservableProperty]
        private bool hasMinLength;

        /// <summary>
        /// 是否满足最大长度要求
        /// </summary>
        [ObservableProperty]
        private bool hasMaxLength = true;

        /// <summary>
        /// 是否包含大写字母
        /// </summary>
        [ObservableProperty]
        private bool hasUppercase;

        /// <summary>
        /// 是否包含小写字母
        /// </summary>
        [ObservableProperty]
        private bool hasLowercase;

        /// <summary>
        /// 是否包含数字
        /// </summary>
        [ObservableProperty]
        private bool hasNumbers;

        /// <summary>
        /// 是否包含特殊字符
        /// </summary>
        [ObservableProperty]
        private bool hasSymbols;

        /// <summary>
        /// 没有重复字符
        /// </summary>
        [ObservableProperty]
        private bool noRepeatingChars = true;

        /// <summary>
        /// 没有键盘序列
        /// </summary>
        [ObservableProperty]
        private bool noKeyboardSequence = true;

        /// <summary>
        /// 没有常见模式
        /// </summary>
        [ObservableProperty]
        private bool noCommonPatterns = true;

        /// <summary>
        /// 是否全部验证通过
        /// </summary>
        public bool IsValid => HasMinLength && HasMaxLength && HasUppercase && HasLowercase &&
                              HasNumbers && HasSymbols && NoRepeatingChars && NoKeyboardSequence && NoCommonPatterns;

        /// <summary>
        /// 验证通过的项目数量
        /// </summary>
        public int ValidCount
        {
            get
            {
                var count = 0;
                if (HasMinLength) count++;
                if (HasMaxLength) count++;
                if (HasUppercase) count++;
                if (HasLowercase) count++;
                if (HasNumbers) count++;
                if (HasSymbols) count++;
                if (NoRepeatingChars) count++;
                if (NoKeyboardSequence) count++;
                if (NoCommonPatterns) count++;
                return count;
            }
        }

        /// <summary>
        /// 总验证项目数量
        /// </summary>
        public int TotalCount => 9;

        /// <summary>
        /// 验证完成百分比
        /// </summary>
        public double CompletionPercentage => (double)ValidCount / TotalCount * 100;
    }
}
