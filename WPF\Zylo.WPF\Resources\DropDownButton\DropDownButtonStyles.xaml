<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml">

    <!-- DropDownButton 基础样式 -->
    <Style x:Key="DropDownButtonBaseStyle" TargetType="ui:DropDownButton">
        <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="4"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontFamily" Value="{DynamicResource ContentControlThemeFontFamily}"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="HorizontalAlignment" Value="Left"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        
        <!-- 添加鼠标悬停和按下效果 -->
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorSecondaryBrush}"/>
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="{DynamicResource ControlFillColorTertiaryBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorTertiaryBrush}"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Opacity" Value="0.5"/>
                <Setter Property="Cursor" Value="Arrow"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- DropDownButton 标准样式 -->
    <Style x:Key="DropDownButtonStyle" TargetType="ui:DropDownButton" BasedOn="{StaticResource DropDownButtonBaseStyle}">
        <!-- 标准样式的特定设置 -->
        <Setter Property="MinWidth" Value="80"/>
        <Setter Property="MinHeight" Value="32"/>
    </Style>

    <!-- DropDownButton 小型样式 -->
    <Style x:Key="SmallDropDownButtonStyle" TargetType="ui:DropDownButton" BasedOn="{StaticResource DropDownButtonBaseStyle}">
        <Setter Property="Padding" Value="8,4"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="MinWidth" Value="60"/>
        <Setter Property="MinHeight" Value="24"/>
    </Style>

    <!-- DropDownButton 大型样式 -->
    <Style x:Key="LargeDropDownButtonStyle" TargetType="ui:DropDownButton" BasedOn="{StaticResource DropDownButtonBaseStyle}">
        <Setter Property="Padding" Value="16,12"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="MinWidth" Value="100"/>
        <Setter Property="MinHeight" Value="40"/>
    </Style>

    <!-- DropDownButton Primary 样式 -->
    <Style x:Key="PrimaryDropDownButtonStyle" TargetType="ui:DropDownButton" BasedOn="{StaticResource DropDownButtonBaseStyle}">
        <Setter Property="Background" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextOnAccentFillColorPrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
        <Setter Property="FontWeight" Value="Medium"/>
        
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{DynamicResource AccentFillColorSecondaryBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource AccentFillColorSecondaryBrush}"/>
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="{DynamicResource AccentFillColorTertiaryBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource AccentFillColorTertiaryBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- DropDownButton 透明样式 -->
    <Style x:Key="TransparentDropDownButtonStyle" TargetType="ui:DropDownButton" BasedOn="{StaticResource DropDownButtonBaseStyle}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorSecondaryBrush}"/>
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="{DynamicResource ControlFillColorTertiaryBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- DropDownButton 现代化样式 -->
    <Style x:Key="ModernDropDownButtonStyle" TargetType="ui:DropDownButton" BasedOn="{StaticResource DropDownButtonBaseStyle}">
        <Setter Property="Background" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextOnAccentFillColorPrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="MinHeight" Value="36"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="{DynamicResource SystemAccentColor}"
                                  BlurRadius="8"
                                  ShadowDepth="2"
                                  Opacity="0.3"/>
            </Setter.Value>
        </Setter>
        
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{DynamicResource AccentFillColorSecondaryBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource AccentFillColorSecondaryBrush}"/>
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="{DynamicResource AccentFillColorTertiaryBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource AccentFillColorTertiaryBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- DropDownButton 渐变样式 -->
    <Style x:Key="GradientDropDownButtonStyle" TargetType="ui:DropDownButton" BasedOn="{StaticResource DropDownButtonBaseStyle}">
        <Setter Property="Background">
            <Setter.Value>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#667eea" Offset="0"/>
                    <GradientStop Color="#764ba2" Offset="1"/>
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="CornerRadius" Value="12"/>
        <Setter Property="Padding" Value="20,10"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="MinHeight" Value="40"/>
        <Setter Property="Cursor" Value="Hand"/>
        
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background">
                    <Setter.Value>
                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                            <GradientStop Color="#764ba2" Offset="0"/>
                            <GradientStop Color="#667eea" Offset="1"/>
                        </LinearGradientBrush>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background">
                    <Setter.Value>
                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                            <GradientStop Color="#5a6fd8" Offset="0"/>
                            <GradientStop Color="#6a4190" Offset="1"/>
                        </LinearGradientBrush>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

</ResourceDictionary>
