using System.Windows.Input;
using System.Windows.Threading;
using Microsoft.Xaml.Behaviors;

namespace Zylo.WPF.Behaviors.Navigation;

/// <summary>
/// 搜索框行为 - 为搜索框提供防抖动搜索功能
/// 当用户在搜索框中输入时，会延迟执行搜索命令，避免频繁触发搜索
/// </summary>
public class SearchBoxBehavior : Behavior<TextBox>
{
    #region 依赖属性

    /// <summary>
    /// 搜索命令 - 执行搜索的命令
    /// </summary>
    public static readonly DependencyProperty SearchCommandProperty =
        DependencyProperty.Register(
            nameof(SearchCommand),
            typeof(ICommand),
            typeof(SearchBoxBehavior),
            new PropertyMetadata(null));

    /// <summary>
    /// 搜索延迟 - 用户停止输入后多长时间执行搜索（毫秒）
    /// </summary>
    public static readonly DependencyProperty SearchDelayProperty =
        DependencyProperty.Register(
            nameof(SearchDelay),
            typeof(int),
            typeof(SearchBoxBehavior),
            new PropertyMetadata(300)); // 默认300毫秒

    /// <summary>
    /// 最小搜索长度 - 触发搜索的最小字符数
    /// </summary>
    public static readonly DependencyProperty MinSearchLengthProperty =
        DependencyProperty.Register(
            nameof(MinSearchLength),
            typeof(int),
            typeof(SearchBoxBehavior),
            new PropertyMetadata(0)); // 默认0，即任何输入都会触发搜索

    /// <summary>
    /// 清空命令 - 清空搜索框时执行的命令
    /// </summary>
    public static readonly DependencyProperty ClearCommandProperty =
        DependencyProperty.Register(
            nameof(ClearCommand),
            typeof(ICommand),
            typeof(SearchBoxBehavior),
            new PropertyMetadata(null));

    #endregion

    #region 私有字段

    private DispatcherTimer? _searchTimer;
    private string _lastSearchText = string.Empty;

    #endregion

    #region 公共属性

    /// <summary>
    /// 获取或设置搜索命令
    /// </summary>
    public ICommand? SearchCommand
    {
        get => (ICommand?)GetValue(SearchCommandProperty);
        set => SetValue(SearchCommandProperty, value);
    }

    /// <summary>
    /// 获取或设置搜索延迟（毫秒）
    /// </summary>
    public int SearchDelay
    {
        get => (int)GetValue(SearchDelayProperty);
        set => SetValue(SearchDelayProperty, value);
    }

    /// <summary>
    /// 获取或设置最小搜索长度
    /// </summary>
    public int MinSearchLength
    {
        get => (int)GetValue(MinSearchLengthProperty);
        set => SetValue(MinSearchLengthProperty, value);
    }

    /// <summary>
    /// 获取或设置清空命令
    /// </summary>
    public ICommand? ClearCommand
    {
        get => (ICommand?)GetValue(ClearCommandProperty);
        set => SetValue(ClearCommandProperty, value);
    }

    #endregion

    #region 行为生命周期

    /// <summary>
    /// 当行为附加到TextBox时调用
    /// </summary>
    protected override void OnAttached()
    {
        base.OnAttached();

        if (AssociatedObject != null)
        {
            // 初始化搜索计时器
            InitializeSearchTimer();

            // 订阅文本变化事件
            AssociatedObject.TextChanged += OnTextChanged;
            
            // 订阅键盘事件（用于处理Escape键清空等）
            AssociatedObject.KeyDown += OnKeyDown;
        }
    }

    /// <summary>
    /// 当行为从TextBox分离时调用
    /// </summary>
    protected override void OnDetaching()
    {
        if (AssociatedObject != null)
        {
            // 取消订阅事件
            AssociatedObject.TextChanged -= OnTextChanged;
            AssociatedObject.KeyDown -= OnKeyDown;
        }

        // 清理计时器
        _searchTimer?.Stop();
        _searchTimer = null;

        base.OnDetaching();
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 初始化搜索计时器
    /// </summary>
    private void InitializeSearchTimer()
    {
        _searchTimer = new DispatcherTimer
        {
            Interval = TimeSpan.FromMilliseconds(SearchDelay)
        };
        _searchTimer.Tick += OnSearchTimerTick;
    }

    /// <summary>
    /// 处理文本变化事件
    /// </summary>
    private void OnTextChanged(object sender, TextChangedEventArgs e)
    {
        if (AssociatedObject == null)
            return;

        // 重启搜索计时器
        _searchTimer?.Stop();
        _searchTimer?.Start();
    }

    /// <summary>
    /// 处理键盘按键事件
    /// </summary>
    private void OnKeyDown(object sender, KeyEventArgs e)
    {
        switch (e.Key)
        {
            case Key.Escape:
                // Escape键清空搜索框
                if (AssociatedObject != null)
                {
                    AssociatedObject.Clear();
                    ExecuteClearCommand();
                }
                e.Handled = true;
                break;

            case Key.Enter:
                // Enter键立即执行搜索
                _searchTimer?.Stop();
                ExecuteSearch();
                e.Handled = true;
                break;
        }
    }

    /// <summary>
    /// 搜索计时器触发事件
    /// </summary>
    private void OnSearchTimerTick(object? sender, EventArgs e)
    {
        _searchTimer?.Stop();
        ExecuteSearch();
    }

    /// <summary>
    /// 执行搜索
    /// </summary>
    private void ExecuteSearch()
    {
        if (AssociatedObject == null || SearchCommand == null)
            return;

        string currentText = AssociatedObject.Text ?? string.Empty;

        // 检查是否满足最小搜索长度要求
        if (currentText.Length < MinSearchLength)
        {
            // 如果之前有搜索结果，需要清空
            if (!string.IsNullOrEmpty(_lastSearchText))
            {
                ExecuteClearCommand();
            }
            return;
        }

        // 避免重复搜索相同的文本
        if (currentText == _lastSearchText)
            return;

        _lastSearchText = currentText;

        // 执行搜索命令
        if (SearchCommand.CanExecute(currentText))
        {
            SearchCommand.Execute(currentText);
        }
    }

    /// <summary>
    /// 执行清空命令
    /// </summary>
    private void ExecuteClearCommand()
    {
        _lastSearchText = string.Empty;

        if (ClearCommand?.CanExecute(null) == true)
        {
            ClearCommand.Execute(null);
        }
    }

    #endregion
}
