using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Windows;
using System.IO;
using System.Reflection;
using System.Collections.ObjectModel;
using System.ComponentModel;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels.LayoutControls
{
    /// <summary>
    /// Card 页面的 ViewModel，演示 WPF-UI Card 控件的各种功能
    /// </summary>
    public partial class CardViewModel : ObservableObject
    {
        private readonly YLoggerInstance _logger = YLogger.For<CardViewModel>();

        #region 属性

        /// <summary>
        /// 交互次数
        /// </summary>
        [ObservableProperty]
        private int interactionCount = 0;

        /// <summary>
        /// 最后执行的操作
        /// </summary>
        [ObservableProperty]
        private string lastAction = "无操作";

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        private string statusMessage = "欢迎使用 WPF-UI Card 示例库！";

        #region 代码示例属性

        /// <summary>
        /// 基础功能 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 基础功能 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级功能 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级功能 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 样式 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string StylesXamlExample { get; set; } = string.Empty;

        #endregion

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化 CardViewModel
        /// </summary>
        public CardViewModel()
        {
            try
            {
                _logger.Info("🚀 Card 页面 ViewModel 开始初始化");

                StatusMessage = "WPF-UI Card 示例库已加载，开始体验现代化卡片设计！";
                InitializeCodeExamples();

                _logger.Info("✅ Card 页面 ViewModel 初始化完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ Card 页面 ViewModel 初始化失败: {ex.Message}");
                StatusMessage = "初始化失败，请检查日志";
            }
        }

        #endregion

        #region 命令

        /// <summary>
        /// 通用交互命令
        /// </summary>
        /// <param name="parameter">操作参数</param>
        [RelayCommand]
        private void HandleInteraction(string? parameter)
        {
            try
            {
                InteractionCount++;
                LastAction = parameter ?? "未知操作";
                
                _logger.Info($"🎯 用户交互操作: {parameter}");
                _logger.Info($"🔢 当前交互次数: {InteractionCount}");

                // 根据不同的操作显示不同的状态消息
                StatusMessage = parameter switch
                {
                    "简单卡片按钮" => "🎯 点击了简单卡片中的按钮",
                    "主要操作" => "🎯 执行了主要操作",
                    "次要操作" => "🎯 执行了次要操作",
                    "了解更多" => "🎯 查看了更多信息",
                    "新建操作" => "🎯 执行了新建操作",
                    "编辑操作" => "🎯 执行了编辑操作",
                    "删除操作" => "🎯 执行了删除操作",
                    "设置操作" => "🎯 执行了设置操作",
                    "查看详情" => "🎯 查看了媒体详情",
                    "子卡片A操作" => "🎯 操作了子卡片A",
                    "子卡片B操作" => "🎯 操作了子卡片B",
                    _ => $"🎯 执行了操作: {parameter}"
                };
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 处理用户交互失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 重置计数命令
        /// </summary>
        [RelayCommand]
        private void ResetCount()
        {
            InteractionCount = 0;
            StatusMessage = "计数已重置";
            _logger.Info("重置了交互计数");
        }

        /// <summary>
        /// 清除状态命令
        /// </summary>
        [RelayCommand]
        private void ClearStatus()
        {
            LastAction = "无操作";
            StatusMessage = "状态已清除";
            _logger.Info("清除了状态信息");
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化代码示例
        /// </summary>
        private void InitializeCodeExamples()
        {
            try
            {
                // 获取代码示例文件的基础路径
                var baseDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
                var codeExamplesPath = Path.Combine(baseDirectory, "CodeExamples", "LayoutControls", "Card");

                _logger.Info($"基础目录: {baseDirectory}");
                _logger.Info($"代码示例路径: {codeExamplesPath}");
                _logger.Info($"目录是否存在: {Directory.Exists(codeExamplesPath)}");

                // 读取各种示例文件
                BasicXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.xaml.txt"));
                BasicCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.cs.txt"));
                AdvancedXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Advanced.xaml.txt"));
                AdvancedCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Advanced.cs.txt"));
                StylesXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Styles.xaml.txt"));

                _logger.Info("Card 代码示例加载完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"加载 Card 代码示例失败: {ex.Message}");
                SetDefaultCodeExamples();
            }
        }

        /// <summary>
        /// 读取代码示例文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件内容</returns>
        private string ReadCodeExampleFile(string filePath)
        {
            _logger.Info($"尝试读取文件: {filePath}");
            
            if (File.Exists(filePath))
            {
                var content = File.ReadAllText(filePath);
                _logger.Info($"成功读取文件，内容长度: {content.Length}");
                return content;
            }
            else
            {
                _logger.Warning($"代码示例文件不存在: {filePath}");
                return $"// 代码示例文件不存在: {Path.GetFileName(filePath)}\n// 完整路径: {filePath}";
            }
        }

        /// <summary>
        /// 设置默认代码示例（当文件读取失败时使用）
        /// </summary>
        private void SetDefaultCodeExamples()
        {
            BasicXamlExample = @"<!-- WPF-UI Card 基础示例 -->
<ui:Card Padding=""16"">
    <StackPanel>
        <TextBlock Text=""这是一个简单的卡片"" 
                   FontWeight=""Medium"" 
                   Margin=""0,0,0,8""/>
        <TextBlock Text=""卡片提供了现代化的容器样式，具有阴影效果和圆角边框。"" 
                   TextWrapping=""Wrap""
                   Margin=""0,0,0,12""/>
        <Button Content=""卡片按钮"" 
                HorizontalAlignment=""Left""/>
    </StackPanel>
</ui:Card>";

            BasicCSharpExample = @"// WPF-UI Card C# 基础示例
using Wpf.Ui.Controls;

// 创建基础 Card
var card = new Card
{
    Padding = new Thickness(16),
    Margin = new Thickness(8)
};

var content = new StackPanel();
content.Children.Add(new TextBlock 
{ 
    Text = ""这是一个简单的卡片"",
    FontWeight = FontWeights.Medium,
    Margin = new Thickness(0, 0, 0, 8)
});

content.Children.Add(new TextBlock 
{ 
    Text = ""卡片提供了现代化的容器样式"",
    TextWrapping = TextWrapping.Wrap,
    Margin = new Thickness(0, 0, 0, 12)
});

content.Children.Add(new Button 
{ 
    Content = ""卡片按钮"",
    HorizontalAlignment = HorizontalAlignment.Left
});

card.Content = content;";

            AdvancedXamlExample = @"<!-- WPF-UI Card 高级示例 -->
<!-- 卡片网格布局 -->
<Grid>
    <Grid.ColumnDefinitions>
        <ColumnDefinition Width=""*""/>
        <ColumnDefinition Width=""*""/>
        <ColumnDefinition Width=""*""/>
    </Grid.ColumnDefinitions>
    
    <ui:Card Grid.Column=""0"" Padding=""16"" Margin=""0,0,8,0"">
        <StackPanel HorizontalAlignment=""Center"">
            <ui:SymbolIcon Symbol=""Home24"" FontSize=""32"" HorizontalAlignment=""Center""/>
            <TextBlock Text=""主页"" FontWeight=""Bold"" HorizontalAlignment=""Center""/>
        </StackPanel>
    </ui:Card>
    
    <ui:Card Grid.Column=""1"" Padding=""16"" Margin=""4,0,4,0"">
        <StackPanel HorizontalAlignment=""Center"">
            <ui:SymbolIcon Symbol=""Settings24"" FontSize=""32"" HorizontalAlignment=""Center""/>
            <TextBlock Text=""设置"" FontWeight=""Bold"" HorizontalAlignment=""Center""/>
        </StackPanel>
    </ui:Card>
    
    <ui:Card Grid.Column=""2"" Padding=""16"" Margin=""8,0,0,0"">
        <StackPanel HorizontalAlignment=""Center"">
            <ui:SymbolIcon Symbol=""Person24"" FontSize=""32"" HorizontalAlignment=""Center""/>
            <TextBlock Text=""用户"" FontWeight=""Bold"" HorizontalAlignment=""Center""/>
        </StackPanel>
    </ui:Card>
</Grid>";

            AdvancedCSharpExample = @"// WPF-UI Card 高级 C# 示例
// 创建卡片网格布局
public static Grid CreateCardGrid()
{
    var grid = new Grid();
    
    // 设置列定义
    for (int i = 0; i < 3; i++)
    {
        grid.ColumnDefinitions.Add(new ColumnDefinition 
        { 
            Width = new GridLength(1, GridUnitType.Star) 
        });
    }
    
    // 创建卡片
    var cards = new[]
    {
        (""Home24"", ""主页""),
        (""Settings24"", ""设置""),
        (""Person24"", ""用户"")
    };
    
    for (int i = 0; i < cards.Length; i++)
    {
        var card = new Card
        {
            Padding = new Thickness(16),
            Margin = new Thickness(i == 0 ? 0 : 4, 0, i == 2 ? 0 : 4, 0)
        };
        
        var content = new StackPanel
        {
            HorizontalAlignment = HorizontalAlignment.Center
        };
        
        content.Children.Add(new SymbolIcon
        {
            Symbol = Enum.Parse<SymbolRegular>(cards[i].Item1),
            FontSize = 32,
            HorizontalAlignment = HorizontalAlignment.Center
        });
        
        content.Children.Add(new TextBlock
        {
            Text = cards[i].Item2,
            FontWeight = FontWeights.Bold,
            HorizontalAlignment = HorizontalAlignment.Center
        });
        
        card.Content = content;
        Grid.SetColumn(card, i);
        grid.Children.Add(card);
    }
    
    return grid;
}";

            StylesXamlExample = @"<!-- WPF-UI Card 样式示例 -->
<!-- 默认样式 -->
<ui:Card Padding=""16"">
    <TextBlock Text=""默认卡片样式""/>
</ui:Card>

<!-- 强调样式 -->
<ui:Card Padding=""16"" 
         Background=""{DynamicResource AccentFillColorDefaultBrush}"">
    <TextBlock Text=""强调卡片样式"" Foreground=""White""/>
</ui:Card>

<!-- 透明样式 -->
<ui:Card Padding=""16"" 
         Background=""Transparent""
         BorderBrush=""{DynamicResource ControlStrokeColorDefaultBrush}""
         BorderThickness=""1"">
    <TextBlock Text=""透明卡片样式""/>
</ui:Card>

<!-- 成功样式 -->
<ui:Card Padding=""16"" 
         Background=""{DynamicResource SystemFillColorSuccessBrush}"">
    <TextBlock Text=""成功卡片样式"" Foreground=""White""/>
</ui:Card>

<!-- 警告样式 -->
<ui:Card Padding=""16"" 
         Background=""{DynamicResource SystemFillColorCautionBrush}"">
    <TextBlock Text=""警告卡片样式"" Foreground=""White""/>
</ui:Card>

<!-- 错误样式 -->
<ui:Card Padding=""16"" 
         Background=""{DynamicResource SystemFillColorCriticalBrush}"">
    <TextBlock Text=""错误卡片样式"" Foreground=""White""/>
</ui:Card>";
        }

        #endregion
    }
}
