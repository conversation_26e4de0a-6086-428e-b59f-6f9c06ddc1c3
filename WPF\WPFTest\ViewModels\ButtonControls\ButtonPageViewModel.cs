using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Windows;
using System.IO;
using System.Reflection;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels.ButtonControls
{
    /// <summary>
    /// 按钮页面的 ViewModel，演示各种按钮功能
    /// </summary>
    public partial class ButtonPageViewModel : ObservableObject
    {
        private readonly YLoggerInstance _logger = YLogger.For<ButtonPageViewModel>();

        #region 属性

        /// <summary>
        /// 按钮点击次数
        /// </summary>
        [ObservableProperty]
        private int clickCount = 0;

        /// <summary>
        /// 最后执行的操作
        /// </summary>
        [ObservableProperty]
        private string lastAction = "无操作";

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        private string statusMessage = "欢迎使用按钮示例库！点击任意按钮开始体验。";

        #region 代码示例属性

        /// <summary>
        /// 圆形按钮 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string CircleButtonXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 圆形按钮 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string CircleButtonCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 矩形按钮 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string RectangleButtonXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 矩形按钮 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string RectangleButtonCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 现代化按钮 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string ModernButtonXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 现代化按钮 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string ModernButtonCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 基础按钮 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicButtonXamlExample { get; set; } = string.Empty;

        #endregion

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化 ButtonPageViewModel
        /// </summary>
        public ButtonPageViewModel()
        {
            StatusMessage = "按钮示例库已加载，点击任意按钮开始体验！";
            InitializeCodeExamples();
        }

        #endregion

        #region 命令

        /// <summary>
        /// 通用按钮点击命令
        /// </summary>
        /// <param name="parameter">按钮参数</param>
        [RelayCommand]
        private void ButtonClick(string? parameter)
        {
            ClickCount++;
            LastAction = parameter ?? "未知操作";
            
            // 根据不同的按钮类型显示不同的状态消息
            StatusMessage = parameter switch
            {
                "Primary" => "🔵 点击了主要按钮",
                "Secondary" => "⚪ 点击了次要按钮", 
                "Success" => "✅ 点击了成功按钮",
                "Danger" => "❌ 点击了危险按钮",
                "Info" => "ℹ️ 点击了信息按钮",
                "Caution" => "⚠️ 点击了警告按钮",
                "Transparent" => "👻 点击了透明按钮",
                "小型按钮" => "🔸 体验了小型按钮",
                "中型按钮" => "🔹 体验了中型按钮",
                "大型按钮" => "🔶 体验了大型按钮",
                "超大按钮" => "🔷 体验了超大按钮",
                "现代化样式" => "✨ 体验了现代化按钮样式",
                "渐变样式" => "🌈 体验了渐变按钮样式",
                "添加" => "➕ 执行了添加操作",
                "编辑" => "✏️ 执行了编辑操作",
                "删除" => "🗑️ 执行了删除操作",
                "保存" => "💾 执行了保存操作",
                "搜索" => "🔍 执行了搜索操作",
                "设置" => "⚙️ 打开了设置",
                "关于" => "ℹ️ 查看了关于信息",
                "小型添加" => "🔸 点击了小型添加按钮",
                "小型编辑" => "🔸 点击了小型编辑按钮",
                "小型删除" => "🔸 点击了小型删除按钮",
                "大型添加" => "🔷 点击了大型添加按钮",
                "大型播放" => "🔷 点击了大型播放按钮",
                "透明添加" => "👻 点击了透明添加按钮",
                "透明收藏" => "👻 点击了透明收藏按钮",
                "透明分享" => "👻 点击了透明分享按钮",
                "无边框更多" => "⚪ 点击了无边框更多按钮",
                "无边框关闭" => "⚪ 点击了无边框关闭按钮",
                "无边框刷新" => "⚪ 点击了无边框刷新按钮",
                "无边框帮助" => "⚪ 点击了无边框帮助按钮",
                // 矩形按钮
                "矩形新建" => "📄 点击了矩形新建按钮",
                "矩形打开" => "📂 点击了矩形打开按钮",
                "矩形保存" => "💾 点击了矩形保存按钮",
                "矩形导出" => "📤 点击了矩形导出按钮",
                "小型复制" => "📋 点击了小型复制按钮",
                "小型粘贴" => "📋 点击了小型粘贴按钮",
                "小型撤销" => "↩️ 点击了小型撤销按钮",
                "大型开始" => "▶️ 点击了大型开始按钮",
                "大型停止" => "⏹️ 点击了大型停止按钮",
                "透明上传" => "⬆️ 点击了透明上传按钮",
                "透明下载" => "⬇️ 点击了透明下载按钮",
                "透明同步" => "🔄 点击了透明同步按钮",
                "无边框菜单" => "📋 点击了无边框菜单按钮",
                "无边框工具" => "🔧 点击了无边框工具按钮",
                "无边框视图" => "👁️ 点击了无边框视图按钮",
                "无边框选项" => "⚙️ 点击了无边框选项按钮",
                _ => $"✅ 执行了 {LastAction} 操作"
            };
        }

        /// <summary>
        /// 重置计数命令
        /// </summary>
        [RelayCommand]
        private void Reset()
        {
            var result = MessageBox.Show("确定要重置所有计数吗？", "确认重置", 
                MessageBoxButton.YesNo, MessageBoxImage.Question);
                
            if (result == MessageBoxResult.Yes)
            {
                ClickCount = 0;
                LastAction = "重置操作";
                StatusMessage = "🔄 所有数据已重置";
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化代码示例
        /// </summary>
        private void InitializeCodeExamples()
        {
            try
            {
                // 获取代码示例文件的基础路径
                var baseDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
                var codeExamplesPath = Path.Combine(baseDirectory, "CodeExamples", "Controls", "Button");

                _logger.Info($"基础目录: {baseDirectory}");
                _logger.Info($"代码示例路径: {codeExamplesPath}");
                _logger.Info($"目录是否存在: {Directory.Exists(codeExamplesPath)}");

                // 读取圆形按钮示例
                CircleButtonXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "CircleButtons.xaml.txt"));
                CircleButtonCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "CircleButtons.cs.txt"));

                // 读取矩形按钮示例
                RectangleButtonXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "RectangleButtons.xaml.txt"));
                RectangleButtonCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "RectangleButtons.cs.txt"));

                // 读取现代化按钮示例
                ModernButtonXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "ModernButtons.xaml.txt"));
                ModernButtonCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "ModernButtons.cs.txt"));

                // 读取基础按钮示例
                BasicButtonXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.xaml.txt"));

                _logger.Info("按钮代码示例加载完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"加载按钮代码示例失败: {ex.Message}");
                SetDefaultCodeExamples();
            }
        }

        /// <summary>
        /// 读取代码示例文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件内容</returns>
        private string ReadCodeExampleFile(string filePath)
        {
            _logger.Info($"尝试读取文件: {filePath}");

            if (File.Exists(filePath))
            {
                var content = File.ReadAllText(filePath);
                _logger.Info($"成功读取文件，内容长度: {content.Length}");
                return content;
            }
            else
            {
                _logger.Warning($"代码示例文件不存在: {filePath}");
                return $"// 代码示例文件不存在: {Path.GetFileName(filePath)}\n// 完整路径: {filePath}";
            }
        }

        /// <summary>
        /// 设置默认代码示例（当文件读取失败时使用）
        /// </summary>
        private void SetDefaultCodeExamples()
        {
            CircleButtonXamlExample = "<!-- 圆形按钮示例 -->\n<ui:Button Style=\"{StaticResource CircleButtonStyle}\">\n    <ui:SymbolIcon Symbol=\"Add24\" FontSize=\"20\"/>\n</ui:Button>";
            CircleButtonCSharpExample = "// 圆形按钮 C# 示例\n[RelayCommand]\nprivate void ButtonClick(string parameter)\n{\n    StatusMessage = $\"点击了按钮: {parameter}\";\n}";

            RectangleButtonXamlExample = "<!-- 矩形按钮示例 -->\n<ui:Button Style=\"{StaticResource RectangleButtonStyle}\">\n    <StackPanel Orientation=\"Horizontal\">\n        <ui:SymbolIcon Symbol=\"Save24\" FontSize=\"16\"/>\n        <TextBlock Text=\"保存\"/>\n    </StackPanel>\n</ui:Button>";
            RectangleButtonCSharpExample = "// 矩形按钮 C# 示例\n[RelayCommand]\nprivate void RectangleButtonClick(string parameter)\n{\n    StatusMessage = $\"矩形按钮: {parameter}\";\n}";

            ModernButtonXamlExample = "<!-- 现代化按钮示例 -->\n<ui:Button Style=\"{StaticResource ModernButtonStyle}\" Content=\"现代化按钮\"/>";
            ModernButtonCSharpExample = "// 现代化按钮 C# 示例\n[RelayCommand]\nprivate void ModernButtonClick()\n{\n    StatusMessage = \"点击了现代化按钮\";\n}";

            BasicButtonXamlExample = "<!-- 基础按钮示例 -->\n<ui:Button Content=\"基础按钮\" Appearance=\"Primary\"/>";
        }

        #endregion
    }
}
