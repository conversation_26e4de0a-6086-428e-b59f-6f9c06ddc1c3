// Expander 高级 C# 示例
// 展示如何通过代码创建复杂的 Expander 结构和功能

using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;

namespace WPFTest.Examples.LayoutControls
{
    public class ExpanderAdvancedExample
    {
        /// <summary>
        /// 创建嵌套 Expander
        /// </summary>
        public static Expander CreateNestedExpander()
        {
            var parentExpander = new Expander
            {
                Header = "嵌套 Expander 示例",
                IsExpanded = false,
                Background = new SolidColorBrush(Colors.LightBlue),
                BorderBrush = new SolidColorBrush(Colors.Blue),
                BorderThickness = new Thickness(1),
                Margin = new Thickness(8)
            };

            var parentContent = new StackPanel
            {
                Margin = new Thickness(16)
            };

            parentContent.Children.Add(new TextBlock
            {
                Text = "这是父级 Expander 的内容。",
                Margin = new Thickness(0, 0, 0, 12)
            });

            // 创建第一个子级 Expander
            var childExpander1 = new Expander
            {
                Header = "子级 Expander 1",
                IsExpanded = false,
                Background = new SolidColorBrush(Colors.LightGreen),
                Margin = new Thickness(0, 0, 0, 8)
            };

            var childContent1 = new StackPanel
            {
                Margin = new Thickness(12)
            };

            childContent1.Children.Add(new TextBlock
            {
                Text = "这是第一个子级 Expander。"
            });

            childContent1.Children.Add(new Button
            {
                Content = "子级按钮 1",
                HorizontalAlignment = HorizontalAlignment.Left,
                Margin = new Thickness(0, 8, 0, 0)
            });

            childExpander1.Content = childContent1;

            // 创建第二个子级 Expander
            var childExpander2 = new Expander
            {
                Header = "子级 Expander 2",
                IsExpanded = false,
                Background = new SolidColorBrush(Colors.LightYellow)
            };

            var childContent2 = new StackPanel
            {
                Margin = new Thickness(12)
            };

            childContent2.Children.Add(new TextBlock
            {
                Text = "这是第二个子级 Expander。"
            });

            var comboBox = new ComboBox
            {
                Margin = new Thickness(0, 8, 0, 0)
            };
            comboBox.Items.Add(new ComboBoxItem { Content = "选项 1" });
            comboBox.Items.Add(new ComboBoxItem { Content = "选项 2" });
            comboBox.Items.Add(new ComboBoxItem { Content = "选项 3" });

            childContent2.Children.Add(comboBox);
            childExpander2.Content = childContent2;

            // 将子级 Expander 添加到父级
            parentContent.Children.Add(childExpander1);
            parentContent.Children.Add(childExpander2);
            parentExpander.Content = parentContent;

            return parentExpander;
        }

        /// <summary>
        /// 创建自定义样式的 Expander
        /// </summary>
        public static Expander CreateCustomStyledExpander()
        {
            var expander = new Expander
            {
                Header = "自定义样式 Expander",
                IsExpanded = false,
                Background = new SolidColorBrush(Color.FromRgb(45, 55, 72)), // #2D3748
                Foreground = Brushes.White,
                BorderBrush = new SolidColorBrush(Color.FromRgb(74, 85, 104)), // #4A5568
                BorderThickness = new Thickness(2),
                Padding = new Thickness(12),
                Margin = new Thickness(8)
            };

            var content = new StackPanel
            {
                Margin = new Thickness(16)
            };

            content.Children.Add(new TextBlock
            {
                Text = "这是一个自定义样式的 Expander。",
                Foreground = Brushes.White,
                Margin = new Thickness(0, 0, 0, 12)
            });

            // 创建两列布局
            var grid = new Grid();
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            // 左侧内容
            var leftPanel = new StackPanel
            {
                Margin = new Thickness(0, 0, 8, 0)
            };

            leftPanel.Children.Add(new TextBlock
            {
                Text = "左侧内容",
                Foreground = Brushes.White,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 8)
            });

            leftPanel.Children.Add(new TextBox
            {
                Text = "输入框示例",
                Margin = new Thickness(0, 0, 0, 8)
            });

            leftPanel.Children.Add(new Button
            {
                Content = "左侧按钮"
            });

            Grid.SetColumn(leftPanel, 0);
            grid.Children.Add(leftPanel);

            // 右侧内容
            var rightPanel = new StackPanel
            {
                Margin = new Thickness(8, 0, 0, 0)
            };

            rightPanel.Children.Add(new TextBlock
            {
                Text = "右侧内容",
                Foreground = Brushes.White,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 8)
            });

            var listBox = new ListBox
            {
                Height = 80,
                Margin = new Thickness(0, 0, 0, 8)
            };
            listBox.Items.Add(new ListBoxItem { Content = "项目 1" });
            listBox.Items.Add(new ListBoxItem { Content = "项目 2" });
            listBox.Items.Add(new ListBoxItem { Content = "项目 3" });

            rightPanel.Children.Add(listBox);
            rightPanel.Children.Add(new Button
            {
                Content = "右侧按钮"
            });

            Grid.SetColumn(rightPanel, 1);
            grid.Children.Add(rightPanel);

            content.Children.Add(grid);
            expander.Content = content;

            return expander;
        }

        /// <summary>
        /// 创建带动画效果的 Expander
        /// </summary>
        public static Expander CreateAnimatedExpander()
        {
            var expander = new Expander
            {
                Header = "动画效果 Expander",
                IsExpanded = false,
                Background = new SolidColorBrush(Colors.LightGray),
                BorderBrush = new SolidColorBrush(Colors.Blue),
                BorderThickness = new Thickness(2),
                Margin = new Thickness(8)
            };

            // 添加展开/折叠事件处理动画
            expander.Expanded += (sender, e) =>
            {
                var colorAnimation = new ColorAnimation
                {
                    To = Colors.LightGreen,
                    Duration = TimeSpan.FromMilliseconds(300)
                };

                var brush = expander.Background as SolidColorBrush;
                if (brush != null)
                {
                    brush.BeginAnimation(SolidColorBrush.ColorProperty, colorAnimation);
                }
            };

            expander.Collapsed += (sender, e) =>
            {
                var colorAnimation = new ColorAnimation
                {
                    To = Colors.LightGray,
                    Duration = TimeSpan.FromMilliseconds(300)
                };

                var brush = expander.Background as SolidColorBrush;
                if (brush != null)
                {
                    brush.BeginAnimation(SolidColorBrush.ColorProperty, colorAnimation);
                }
            };

            var content = new StackPanel
            {
                Margin = new Thickness(16)
            };

            content.Children.Add(new TextBlock
            {
                Text = "这个 Expander 在展开/折叠时有颜色动画效果。",
                Margin = new Thickness(0, 0, 0, 12)
            });

            content.Children.Add(new ProgressBar
            {
                Value = 60,
                Height = 20,
                Margin = new Thickness(0, 0, 0, 8)
            });

            content.Children.Add(new TextBlock
            {
                Text = "展开时背景变为绿色，折叠时恢复原色。"
            });

            expander.Content = content;
            return expander;
        }

        /// <summary>
        /// 创建复杂内容的 Expander（表单示例）
        /// </summary>
        public static Expander CreateComplexContentExpander()
        {
            var expander = new Expander
            {
                Header = "复杂内容展示",
                IsExpanded = false,
                Margin = new Thickness(8)
            };

            var border = new Border
            {
                Background = Brushes.White,
                BorderBrush = Brushes.LightGray,
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(8),
                Padding = new Thickness(16)
            };

            var mainGrid = new Grid();
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            // 标题区域
            var titlePanel = new StackPanel
            {
                Margin = new Thickness(0, 0, 0, 16)
            };

            titlePanel.Children.Add(new TextBlock
            {
                Text = "用户信息表单",
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 8)
            });

            titlePanel.Children.Add(new Separator());

            Grid.SetRow(titlePanel, 0);
            mainGrid.Children.Add(titlePanel);

            // 表单内容
            var formGrid = new Grid
            {
                Margin = new Thickness(0, 0, 0, 16)
            };

            // 设置列定义
            formGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
            formGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            formGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
            formGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            // 设置行定义
            formGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            formGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            formGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            // 添加表单控件
            AddFormField(formGrid, "姓名:", "张三", 0, 0);
            AddFormField(formGrid, "年龄:", "25", 0, 2);
            AddFormField(formGrid, "邮箱:", "<EMAIL>", 1, 0);
            AddFormField(formGrid, "电话:", "138****8888", 1, 2);

            // 备注字段（跨列）
            var remarkLabel = new TextBlock
            {
                Text = "备注:",
                VerticalAlignment = VerticalAlignment.Top,
                Margin = new Thickness(0, 0, 8, 0)
            };
            Grid.SetRow(remarkLabel, 2);
            Grid.SetColumn(remarkLabel, 0);
            formGrid.Children.Add(remarkLabel);

            var remarkTextBox = new TextBox
            {
                Text = "这是一个示例用户的备注信息。",
                TextWrapping = TextWrapping.Wrap,
                Height = 60
            };
            Grid.SetRow(remarkTextBox, 2);
            Grid.SetColumn(remarkTextBox, 1);
            Grid.SetColumnSpan(remarkTextBox, 3);
            formGrid.Children.Add(remarkTextBox);

            Grid.SetRow(formGrid, 1);
            mainGrid.Children.Add(formGrid);

            // 操作按钮
            var buttonPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Right
            };

            buttonPanel.Children.Add(new Button
            {
                Content = "重置",
                Margin = new Thickness(0, 0, 8, 0)
            });

            buttonPanel.Children.Add(new Button
            {
                Content = "保存"
            });

            Grid.SetRow(buttonPanel, 2);
            mainGrid.Children.Add(buttonPanel);

            border.Child = mainGrid;
            expander.Content = border;

            return expander;
        }

        /// <summary>
        /// 辅助方法：添加表单字段
        /// </summary>
        private static void AddFormField(Grid grid, string labelText, string valueText, int row, int column)
        {
            var label = new TextBlock
            {
                Text = labelText,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(0, 0, 8, 8)
            };
            Grid.SetRow(label, row);
            Grid.SetColumn(label, column);
            grid.Children.Add(label);

            var textBox = new TextBox
            {
                Text = valueText,
                Margin = new Thickness(0, 0, column == 0 ? 16 : 0, 8)
            };
            Grid.SetRow(textBox, row);
            Grid.SetColumn(textBox, column + 1);
            grid.Children.Add(textBox);
        }

        /// <summary>
        /// 为 Expander 添加状态监控
        /// </summary>
        public static void AddExpanderStateMonitoring(Expander expander)
        {
            expander.Expanded += (sender, e) =>
            {
                System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss}] Expander '{expander.Header}' 已展开");
            };

            expander.Collapsed += (sender, e) =>
            {
                System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss}] Expander '{expander.Header}' 已折叠");
            };
        }
    }
}
