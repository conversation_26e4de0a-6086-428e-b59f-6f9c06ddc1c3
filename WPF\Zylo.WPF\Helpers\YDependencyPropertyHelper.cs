namespace Zylo.WPF.Helpers;

/// <summary>
/// 依赖属性帮助类 - 简化依赖属性的创建
/// </summary>
public static class YDependencyPropertyHelper
{
    /// <summary>
    /// 创建依赖属性
    /// </summary>
    /// <typeparam name="TProperty">属性类型</typeparam>
    /// <typeparam name="TOwner">拥有者类型</typeparam>
    /// <param name="propertyName">属性名称</param>
    /// <param name="defaultValue">默认值</param>
    /// <param name="propertyChangedCallback">属性变化回调</param>
    /// <returns>依赖属性</returns>
    public static DependencyProperty Register<TProperty, TOwner>(
        string propertyName,
        TProperty defaultValue = default!,
        PropertyChangedCallback? propertyChangedCallback = null)
        where TOwner : DependencyObject
    {
        return DependencyProperty.Register(
            propertyName,
            typeof(TProperty),
            typeof(TOwner),
            new PropertyMetadata(defaultValue, propertyChangedCallback));
    }

    /// <summary>
    /// 创建只读依赖属性
    /// </summary>
    /// <typeparam name="TProperty">属性类型</typeparam>
    /// <typeparam name="TOwner">拥有者类型</typeparam>
    /// <param name="propertyName">属性名称</param>
    /// <param name="defaultValue">默认值</param>
    /// <param name="propertyChangedCallback">属性变化回调</param>
    /// <returns>只读依赖属性键</returns>
    public static DependencyPropertyKey RegisterReadOnly<TProperty, TOwner>(
        string propertyName,
        TProperty defaultValue = default!,
        PropertyChangedCallback? propertyChangedCallback = null)
        where TOwner : DependencyObject
    {
        return DependencyProperty.RegisterReadOnly(
            propertyName,
            typeof(TProperty),
            typeof(TOwner),
            new PropertyMetadata(defaultValue, propertyChangedCallback));
    }

    /// <summary>
    /// 创建附加属性
    /// </summary>
    /// <typeparam name="TProperty">属性类型</typeparam>
    /// <typeparam name="TOwner">拥有者类型</typeparam>
    /// <param name="propertyName">属性名称</param>
    /// <param name="defaultValue">默认值</param>
    /// <param name="propertyChangedCallback">属性变化回调</param>
    /// <returns>依赖属性</returns>
    public static DependencyProperty RegisterAttached<TProperty, TOwner>(
        string propertyName,
        TProperty defaultValue = default!,
        PropertyChangedCallback? propertyChangedCallback = null)
        where TOwner : DependencyObject
    {
        return DependencyProperty.RegisterAttached(
            propertyName,
            typeof(TProperty),
            typeof(TOwner),
            new PropertyMetadata(defaultValue, propertyChangedCallback));
    }

    /// <summary>
    /// 创建带验证的依赖属性
    /// </summary>
    /// <typeparam name="TProperty">属性类型</typeparam>
    /// <typeparam name="TOwner">拥有者类型</typeparam>
    /// <param name="propertyName">属性名称</param>
    /// <param name="defaultValue">默认值</param>
    /// <param name="validateValueCallback">值验证回调</param>
    /// <param name="propertyChangedCallback">属性变化回调</param>
    /// <returns>依赖属性</returns>
    public static DependencyProperty RegisterWithValidation<TProperty, TOwner>(
        string propertyName,
        TProperty defaultValue,
        ValidateValueCallback validateValueCallback,
        PropertyChangedCallback? propertyChangedCallback = null)
        where TOwner : DependencyObject
    {
        return DependencyProperty.Register(
            propertyName,
            typeof(TProperty),
            typeof(TOwner),
            new PropertyMetadata(defaultValue, propertyChangedCallback),
            validateValueCallback);
    }

    /// <summary>
    /// 创建带强制值的依赖属性
    /// </summary>
    /// <typeparam name="TProperty">属性类型</typeparam>
    /// <typeparam name="TOwner">拥有者类型</typeparam>
    /// <param name="propertyName">属性名称</param>
    /// <param name="defaultValue">默认值</param>
    /// <param name="propertyChangedCallback">属性变化回调</param>
    /// <param name="coerceValueCallback">强制值回调</param>
    /// <returns>依赖属性</returns>
    public static DependencyProperty RegisterWithCoercion<TProperty, TOwner>(
        string propertyName,
        TProperty defaultValue,
        PropertyChangedCallback? propertyChangedCallback,
        CoerceValueCallback coerceValueCallback)
        where TOwner : DependencyObject
    {
        return DependencyProperty.Register(
            propertyName,
            typeof(TProperty),
            typeof(TOwner),
            new PropertyMetadata(defaultValue, propertyChangedCallback, coerceValueCallback));
    }
}

/// <summary>
/// 依赖属性构建器 - 流式API
/// </summary>
/// <typeparam name="TProperty">属性类型</typeparam>
/// <typeparam name="TOwner">拥有者类型</typeparam>
public class DependencyPropertyBuilder<TProperty, TOwner>
    where TOwner : DependencyObject
{
    private readonly string _propertyName;
    private TProperty _defaultValue = default!;
    private PropertyChangedCallback? _propertyChangedCallback;
    private ValidateValueCallback? _validateValueCallback;
    private CoerceValueCallback? _coerceValueCallback;
    private bool _isReadOnly;
    private bool _isAttached;

    internal DependencyPropertyBuilder(string propertyName)
    {
        _propertyName = propertyName;
    }

    /// <summary>
    /// 设置默认值
    /// </summary>
    public DependencyPropertyBuilder<TProperty, TOwner> WithDefaultValue(TProperty defaultValue)
    {
        _defaultValue = defaultValue;
        return this;
    }

    /// <summary>
    /// 设置属性变化回调
    /// </summary>
    public DependencyPropertyBuilder<TProperty, TOwner> OnChanged(PropertyChangedCallback callback)
    {
        _propertyChangedCallback = callback;
        return this;
    }

    /// <summary>
    /// 设置值验证回调
    /// </summary>
    public DependencyPropertyBuilder<TProperty, TOwner> WithValidation(ValidateValueCallback callback)
    {
        _validateValueCallback = callback;
        return this;
    }

    /// <summary>
    /// 设置强制值回调
    /// </summary>
    public DependencyPropertyBuilder<TProperty, TOwner> WithCoercion(CoerceValueCallback callback)
    {
        _coerceValueCallback = callback;
        return this;
    }

    /// <summary>
    /// 设置为只读属性
    /// </summary>
    public DependencyPropertyBuilder<TProperty, TOwner> AsReadOnly()
    {
        _isReadOnly = true;
        return this;
    }

    /// <summary>
    /// 设置为附加属性
    /// </summary>
    public DependencyPropertyBuilder<TProperty, TOwner> AsAttached()
    {
        _isAttached = true;
        return this;
    }

    /// <summary>
    /// 构建依赖属性
    /// </summary>
    public DependencyProperty Build()
    {
        var metadata = new PropertyMetadata(_defaultValue, _propertyChangedCallback, _coerceValueCallback);

        if (_isReadOnly)
        {
            var key = DependencyProperty.RegisterReadOnly(
                _propertyName,
                typeof(TProperty),
                typeof(TOwner),
                metadata,
                _validateValueCallback);
            return key.DependencyProperty;
        }

        if (_isAttached)
        {
            return DependencyProperty.RegisterAttached(
                _propertyName,
                typeof(TProperty),
                typeof(TOwner),
                metadata,
                _validateValueCallback);
        }

        return DependencyProperty.Register(
            _propertyName,
            typeof(TProperty),
            typeof(TOwner),
            metadata,
            _validateValueCallback);
    }

    /// <summary>
    /// 构建只读依赖属性键
    /// </summary>
    public DependencyPropertyKey BuildReadOnlyKey()
    {
        var metadata = new PropertyMetadata(_defaultValue, _propertyChangedCallback, _coerceValueCallback);

        return DependencyProperty.RegisterReadOnly(
            _propertyName,
            typeof(TProperty),
            typeof(TOwner),
            metadata,
            _validateValueCallback);
    }
}

/// <summary>
/// 依赖属性构建器工厂
/// </summary>
public static class DependencyPropertyBuilder
{
    /// <summary>
    /// 创建依赖属性构建器
    /// </summary>
    /// <typeparam name="TProperty">属性类型</typeparam>
    /// <typeparam name="TOwner">拥有者类型</typeparam>
    /// <param name="propertyName">属性名称</param>
    /// <returns>依赖属性构建器</returns>
    public static DependencyPropertyBuilder<TProperty, TOwner> Create<TProperty, TOwner>(string propertyName)
        where TOwner : DependencyObject
    {
        return new DependencyPropertyBuilder<TProperty, TOwner>(propertyName);
    }
}
