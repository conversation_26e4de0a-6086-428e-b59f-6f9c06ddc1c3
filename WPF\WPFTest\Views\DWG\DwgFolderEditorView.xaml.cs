using System.Windows.Controls;

namespace WPFTest.Views.DWG;

/// <summary>
/// DwgFolderEditorView.xaml 的交互逻辑
/// </summary>
/// <remarks>
/// 纯 MVVM 模式：无代码后置逻辑，所有交互通过数据绑定和命令实现
/// 遵循 CommunityToolkit.Mvvm 8.4.0 的最佳实践
/// </remarks>
public partial class DwgFolderEditorView : UserControl
{
    /// <summary>
    /// 构造函数
    /// </summary>
    public DwgFolderEditorView()
    {
        InitializeComponent();
    }
}
