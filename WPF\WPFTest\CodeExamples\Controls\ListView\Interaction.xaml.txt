<!-- ListView 交互功能示例 -->
<!-- 展示 ListView 的各种交互功能和事件处理 -->

<StackPanel>
    <!-- 基础交互 -->
    <TextBlock Text="基础交互功能：" FontWeight="Medium" Margin="0,0,0,8"/>
    
    <!-- 选择和点击 -->
    <ListView ItemsSource="{Binding DataItems}"
              SelectedItem="{Binding SelectedDataItem, Mode=TwoWay}"
              Height="150"
              Margin="0,0,0,16">
        <ListView.ItemTemplate>
            <DataTemplate>
                <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                        BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                        BorderThickness="1"
                        CornerRadius="6"
                        Padding="12"
                        Margin="4">
                    <StackPanel Orientation="Horizontal">
                        <ui:SymbolIcon Symbol="{Binding Icon}" FontSize="20" Margin="0,0,12,0"/>
                        <StackPanel>
                            <TextBlock Text="{Binding Name}" FontWeight="Medium"/>
                            <TextBlock Text="{Binding Description}" FontSize="12" 
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                        </StackPanel>
                    </StackPanel>
                    
                    <!-- 交互触发器 -->
                    <Border.InputBindings>
                        <MouseBinding MouseAction="LeftDoubleClick" 
                                      Command="{Binding DataContext.ItemDoubleClickCommand, 
                                                RelativeSource={RelativeSource AncestorType=ListView}}"
                                      CommandParameter="{Binding}"/>
                    </Border.InputBindings>
                </Border>
            </DataTemplate>
        </ListView.ItemTemplate>
        
        <!-- 右键菜单 -->
        <ListView.ContextMenu>
            <ContextMenu>
                <MenuItem Header="编辑" 
                          Command="{Binding EditItemCommand}"
                          CommandParameter="{Binding SelectedDataItem}">
                    <MenuItem.Icon>
                        <ui:SymbolIcon Symbol="Edit24"/>
                    </MenuItem.Icon>
                </MenuItem>
                <MenuItem Header="删除" 
                          Command="{Binding DeleteItemCommand}"
                          CommandParameter="{Binding SelectedDataItem}">
                    <MenuItem.Icon>
                        <ui:SymbolIcon Symbol="Delete24"/>
                    </MenuItem.Icon>
                </MenuItem>
                <Separator/>
                <MenuItem Header="复制" 
                          Command="{Binding CopyItemCommand}"
                          CommandParameter="{Binding SelectedDataItem}">
                    <MenuItem.Icon>
                        <ui:SymbolIcon Symbol="Copy24"/>
                    </MenuItem.Icon>
                </MenuItem>
                <MenuItem Header="属性" 
                          Command="{Binding ShowPropertiesCommand}"
                          CommandParameter="{Binding SelectedDataItem}">
                    <MenuItem.Icon>
                        <ui:SymbolIcon Symbol="Settings24"/>
                    </MenuItem.Icon>
                </MenuItem>
            </ContextMenu>
        </ListView.ContextMenu>
    </ListView>

    <!-- 多选功能 -->
    <TextBlock Text="多选功能：" FontWeight="Medium" Margin="0,0,0,8"/>
    
    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
        <CheckBox Content="启用多选模式" 
                  IsChecked="{Binding IsMultiSelectEnabled, Mode=TwoWay}"
                  Margin="0,0,16,0"/>
        <TextBlock Text="{Binding SelectedItems.Count, StringFormat='已选择 {0} 项'}"
                   VerticalAlignment="Center"/>
    </StackPanel>
    
    <ListView ItemsSource="{Binding DataItems}"
              SelectionMode="{Binding IsMultiSelectEnabled, 
                              Converter={StaticResource BoolToSelectionModeConverter}}"
              Height="120"
              Margin="0,0,0,16">
        <ListView.ItemTemplate>
            <DataTemplate>
                <CheckBox Content="{Binding Name}"
                          IsChecked="{Binding IsSelected, Mode=TwoWay}"
                          Command="{Binding DataContext.ItemSelectionChangedCommand, 
                                    RelativeSource={RelativeSource AncestorType=ListView}}"
                          CommandParameter="{Binding}"
                          Margin="4"/>
            </DataTemplate>
        </ListView.ItemTemplate>
    </ListView>

    <!-- 拖拽功能 -->
    <TextBlock Text="拖拽功能：" FontWeight="Medium" Margin="0,0,0,8"/>
    
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="20"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>
        
        <!-- 源列表 -->
        <StackPanel Grid.Column="0">
            <TextBlock Text="源列表 (可拖拽)" FontSize="12" Margin="0,0,0,4"/>
            <ListView ItemsSource="{Binding SourceItems}"
                      AllowDrop="True"
                      Height="100">
                <ListView.ItemTemplate>
                    <DataTemplate>
                        <Border Background="LightBlue" 
                                Padding="8" 
                                Margin="2"
                                CornerRadius="4">
                            <TextBlock Text="{Binding}" 
                                       Cursor="Hand"/>
                            <!-- 拖拽行为 -->
                            <Border.InputBindings>
                                <MouseBinding MouseAction="LeftClick" 
                                              Command="{Binding DataContext.StartDragCommand, 
                                                        RelativeSource={RelativeSource AncestorType=ListView}}"
                                              CommandParameter="{Binding}"/>
                            </Border.InputBindings>
                        </Border>
                    </DataTemplate>
                </ListView.ItemTemplate>
            </ListView>
        </StackPanel>
        
        <!-- 箭头指示 -->
        <ui:SymbolIcon Grid.Column="1" 
                       Symbol="ArrowRight24" 
                       FontSize="16"
                       HorizontalAlignment="Center"
                       VerticalAlignment="Center"/>
        
        <!-- 目标列表 -->
        <StackPanel Grid.Column="2">
            <TextBlock Text="目标列表 (可放置)" FontSize="12" Margin="0,0,0,4"/>
            <ListView ItemsSource="{Binding TargetItems}"
                      AllowDrop="True"
                      Height="100"
                      Background="LightGreen">
                <ListView.ItemTemplate>
                    <DataTemplate>
                        <Border Background="LightGreen" 
                                Padding="8" 
                                Margin="2"
                                CornerRadius="4">
                            <TextBlock Text="{Binding}"/>
                        </Border>
                    </DataTemplate>
                </ListView.ItemTemplate>
                
                <!-- 拖放事件 -->
                <ListView.InputBindings>
                    <MouseBinding MouseAction="LeftClick" 
                                  Command="{Binding DropItemCommand}"/>
                </ListView.InputBindings>
            </ListView>
        </StackPanel>
    </Grid>

    <!-- 搜索和筛选 -->
    <TextBlock Text="搜索和筛选：" FontWeight="Medium" Margin="0,16,0,8"/>
    
    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
        <ui:TextBox Text="{Binding SearchText, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                    PlaceholderText="搜索项目..."
                    Width="200"
                    Margin="0,0,8,0"/>
        <ComboBox ItemsSource="{Binding Categories}"
                  SelectedItem="{Binding SelectedCategory, Mode=TwoWay}"
                  Width="120"
                  Margin="0,0,8,0"/>
        <ui:Button Content="清除筛选" 
                   Command="{Binding ClearFilterCommand}"/>
    </StackPanel>
    
    <ListView ItemsSource="{Binding FilteredItems}"
              Height="100">
        <ListView.ItemTemplate>
            <DataTemplate>
                <StackPanel Orientation="Horizontal" Margin="4">
                    <ui:SymbolIcon Symbol="{Binding Icon}" FontSize="16" Margin="0,0,8,0"/>
                    <TextBlock Text="{Binding Name}" FontWeight="Medium" Margin="0,0,8,0"/>
                    <TextBlock Text="{Binding Category}" FontSize="10" 
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                </StackPanel>
            </DataTemplate>
        </ListView.ItemTemplate>
    </ListView>

    <!-- 键盘导航 -->
    <TextBlock Text="键盘导航支持：" FontWeight="Medium" Margin="0,16,0,8"/>
    <TextBlock Text="• 上下箭头键：选择项目" FontSize="12" Margin="0,0,0,2"/>
    <TextBlock Text="• Enter键：激活选中项目" FontSize="12" Margin="0,0,0,2"/>
    <TextBlock Text="• Ctrl+A：全选（多选模式）" FontSize="12" Margin="0,0,0,2"/>
    <TextBlock Text="• Delete键：删除选中项目" FontSize="12" Margin="0,0,0,2"/>
    <TextBlock Text="• F2键：编辑选中项目" FontSize="12"/>
</StackPanel>

<!-- 
交互功能说明：

1. 基础交互：
   - 单击选择
   - 双击激活
   - 右键菜单

2. 多选功能：
   - 切换多选模式
   - 批量操作
   - 选择状态管理

3. 拖拽功能：
   - 项目拖拽
   - 拖放处理
   - 视觉反馈

4. 搜索筛选：
   - 实时搜索
   - 分类筛选
   - 组合条件

5. 键盘导航：
   - 方向键导航
   - 快捷键支持
   - 可访问性

实现要点：
- 使用命令模式处理交互
- 实现适当的视觉反馈
- 考虑用户体验和可访问性
- 提供丰富的交互选项
-->
