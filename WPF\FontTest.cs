using System;
using System.IO;
using System.Windows;
using System.Windows.Media;

namespace FontTest
{
    class Program
    {
        [STAThread]
        static void Main(string[] args)
        {
            Console.WriteLine("=== iconfont.ttf 字体测试工具 ===\n");
            
            // 测试字体路径
            var fontPaths = new[]
            {
                "/Zylo.WPF;component/Assets/Font/#iconfont",
                "/Zylo.WPF;component/Assets/Font/iconfont.ttf#iconfont", 
                "/Zylo.WPF;component/Assets/Font/iconfont.ttf#IconFont",
                "/Zylo.WPF;component/Assets/Font/iconfont.ttf#icon-font",
                "/Zylo.WPF;component/Assets/Font/iconfont.ttf#Iconfont",
                "pack://application:,,,/Zylo.WPF;component/Assets/Font/#iconfont",
                "pack://application:,,,/Zylo.WPF;component/Assets/Font/iconfont.ttf#iconfont",
                "pack://application:,,,/Zylo.WPF;component/Assets/Font/iconfont.ttf#IconFont"
            };

            Console.WriteLine("1. 测试字体路径可用性:");
            foreach (var fontPath in fontPaths)
            {
                try
                {
                    var fontFamily = new FontFamily(fontPath);
                    var typefaces = fontFamily.GetTypefaces();
                    var isAvailable = typefaces.Any();
                    
                    Console.WriteLine($"   {(isAvailable ? "✅" : "❌")} {fontPath}");
                    
                    if (isAvailable)
                    {
                        foreach (var typeface in typefaces.Take(1))
                        {
                            if (typeface.TryGetGlyphTypeface(out var glyphTypeface))
                            {
                                var familyName = glyphTypeface.FamilyNames.Values.FirstOrDefault();
                                Console.WriteLine($"      字体名称: {familyName}");
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   ❌ {fontPath} - 错误: {ex.Message}");
                }
            }

            Console.WriteLine("\n2. 检查字体文件是否存在:");
            var fontFilePath = Path.Combine(Directory.GetCurrentDirectory(), "Zylo.WPF", "Assets", "Font", "iconfont.ttf");
            Console.WriteLine($"   文件路径: {fontFilePath}");
            Console.WriteLine($"   文件存在: {(File.Exists(fontFilePath) ? "✅" : "❌")}");
            
            if (File.Exists(fontFilePath))
            {
                var fileInfo = new FileInfo(fontFilePath);
                Console.WriteLine($"   文件大小: {fileInfo.Length} 字节");
                Console.WriteLine($"   修改时间: {fileInfo.LastWriteTime}");
            }

            Console.WriteLine("\n3. 系统字体信息 (前10个):");
            var systemFonts = Fonts.SystemFontFamilies.Take(10);
            foreach (var font in systemFonts)
            {
                Console.WriteLine($"   - {font.Source}");
            }

            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
    }
}
