// SplitButton 基础 C# 示例
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace WPFTest.ViewModels.ButtonControls
{
    public partial class SplitButtonPageViewModel : ObservableObject
    {
        #region 状态属性

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        private string statusMessage = "SplitButton 示例库已加载，开始体验各种功能！";

        /// <summary>
        /// 最后执行的操作
        /// </summary>
        [ObservableProperty]
        private string lastAction = "无操作";

        /// <summary>
        /// 选择的选项
        /// </summary>
        [ObservableProperty]
        private string selectedOption = "默认选项";

        /// <summary>
        /// 交互计数
        /// </summary>
        [ObservableProperty]
        private int interactionCount = 0;

        /// <summary>
        /// SplitButton 是否启用
        /// </summary>
        [ObservableProperty]
        private bool isSplitButtonEnabled = true;

        #endregion

        #region 命令

        /// <summary>
        /// 处理主要操作命令
        /// SplitButton 的主按钮点击时执行
        /// </summary>
        [RelayCommand]
        private void HandlePrimaryAction(string? parameter)
        {
            var action = parameter ?? "主要操作";
            LastAction = action;
            StatusMessage = $"🎯 执行了主要操作: {action}";
            InteractionCount++;
            
            // 这里可以添加具体的业务逻辑
            // 例如：保存文件、发送邮件、下载文件等
        }

        /// <summary>
        /// 处理下拉菜单交互命令
        /// SplitButton 的下拉菜单项点击时执行
        /// </summary>
        [RelayCommand]
        private void HandleDropdownInteraction(string? parameter)
        {
            var option = parameter ?? "未知选项";
            SelectedOption = option;
            LastAction = $"下拉选择: {option}";
            StatusMessage = $"📋 从下拉菜单选择了: {option}";
            InteractionCount++;
            
            // 根据选择的选项执行不同的操作
            switch (option)
            {
                case "保存":
                    // 执行保存操作
                    break;
                case "另存为":
                    // 执行另存为操作
                    break;
                case "保存所有":
                    // 执行保存所有操作
                    break;
                default:
                    // 处理其他选项
                    break;
            }
        }

        /// <summary>
        /// 切换启用状态命令
        /// </summary>
        [RelayCommand]
        private void ToggleEnabled()
        {
            IsSplitButtonEnabled = !IsSplitButtonEnabled;
            StatusMessage = $"🔄 SplitButton 已{(IsSplitButtonEnabled ? "启用" : "禁用")}";
            InteractionCount++;
        }

        /// <summary>
        /// 清除状态命令
        /// </summary>
        [RelayCommand]
        private void ClearStatus()
        {
            LastAction = "无操作";
            SelectedOption = "默认选项";
            StatusMessage = "状态已清除";
        }

        #endregion

        #region 使用技巧

        /*
         * SplitButton 使用技巧：
         * 
         * 1. 主要操作设计
         *    - 主按钮应该是最常用的操作
         *    - 下拉菜单提供相关的替代选项
         * 
         * 2. 菜单项组织
         *    - 使用 Separator 分组相关选项
         *    - 为菜单项添加图标提高识别度
         * 
         * 3. 外观选择
         *    - Primary: 用于主要操作（如保存、发送）
         *    - Danger: 用于危险操作（如删除）
         *    - 默认: 用于一般操作
         * 
         * 4. 状态管理
         *    - 使用 IsEnabled 控制整体启用状态
         *    - 可以单独控制菜单项的启用状态
         * 
         * 5. 命令绑定
         *    - 主按钮和菜单项可以绑定不同的命令
         *    - 使用 CommandParameter 传递参数
         */

        #endregion
    }
}
