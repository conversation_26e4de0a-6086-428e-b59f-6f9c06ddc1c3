// AutoSuggestBox 基础功能 C# 实现
using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace WPFTest.ViewModels.Controls;

/// <summary>
/// AutoSuggestBox 基础功能示例 ViewModel
/// 使用 CommunityToolkit.MVVM 8.4.0 的 Partial Properties 语法
/// </summary>
public partial class BasicAutoSuggestBoxViewModel : ObservableObject
{
    #region 搜索文本属性

    [ObservableProperty]
    public partial string CitySearchText { get; set; } = string.Empty;

    [ObservableProperty]
    public partial string LanguageSearchText { get; set; } = string.Empty;

    [ObservableProperty]
    public partial string CountrySearchText { get; set; } = string.Empty;

    [ObservableProperty]
    public partial string ProductSearchText { get; set; } = string.Empty;

    [ObservableProperty]
    public partial string UserSearchText { get; set; } = string.Empty;

    #endregion

    #region 建议列表属性

    [ObservableProperty]
    public partial ObservableCollection<string> CitySuggestions { get; set; } = new();

    [ObservableProperty]
    public partial ObservableCollection<string> LanguageSuggestions { get; set; } = new();

    [ObservableProperty]
    public partial ObservableCollection<string> CountrySuggestions { get; set; } = new();

    [ObservableProperty]
    public partial ObservableCollection<string> ProductSuggestions { get; set; } = new();

    [ObservableProperty]
    public partial ObservableCollection<string> UserSuggestions { get; set; } = new();

    #endregion

    #region 数据源

    private readonly List<string> _cities = new()
    {
        "北京", "上海", "广州", "深圳", "杭州", "南京", "苏州", "成都", "武汉", "西安",
        "重庆", "天津", "青岛", "大连", "厦门", "宁波", "无锡", "佛山", "温州", "烟台"
    };

    private readonly List<string> _languages = new()
    {
        "C#", "Java", "Python", "JavaScript", "TypeScript", "C++", "C", "Go", "Rust", "Swift",
        "Kotlin", "Scala", "Ruby", "PHP", "Dart", "F#", "VB.NET", "Objective-C", "R", "MATLAB"
    };

    private readonly List<string> _countries = new()
    {
        "中国", "美国", "日本", "德国", "英国", "法国", "意大利", "加拿大", "澳大利亚", "韩国",
        "俄罗斯", "印度", "巴西", "墨西哥", "西班牙", "荷兰", "瑞士", "瑞典", "挪威", "丹麦"
    };

    private readonly List<string> _products = new()
    {
        "笔记本电脑", "台式电脑", "平板电脑", "智能手机", "智能手表", "耳机", "音响", "键盘", "鼠标", "显示器",
        "打印机", "扫描仪", "路由器", "交换机", "硬盘", "内存条", "显卡", "主板", "电源", "机箱"
    };

    private readonly List<string> _users = new()
    {
        "张三", "李四", "王五", "赵六", "钱七", "孙八", "周九", "吴十", "郑十一", "王十二",
        "冯十三", "陈十四", "褚十五", "卫十六", "蒋十七", "沈十八", "韩十九", "杨二十", "朱二十一", "秦二十二"
    };

    #endregion

    #region 构造函数

    public BasicAutoSuggestBoxViewModel()
    {
        // 监听属性变化以实现实时搜索
        PropertyChanged += OnPropertyChanged;
    }

    #endregion

    #region 搜索命令

    [RelayCommand]
    private void SearchCities(string? query)
    {
        UpdateSuggestions(query, _cities, CitySuggestions);
    }

    [RelayCommand]
    private void SearchLanguages(string? query)
    {
        UpdateSuggestions(query, _languages, LanguageSuggestions);
    }

    [RelayCommand]
    private void SearchCountries(string? query)
    {
        UpdateSuggestions(query, _countries, CountrySuggestions);
    }

    [RelayCommand]
    private void SearchProducts(string? query)
    {
        UpdateSuggestions(query, _products, ProductSuggestions);
    }

    [RelayCommand]
    private void SearchUsers(string? query)
    {
        UpdateSuggestions(query, _users, UserSuggestions);
    }

    [RelayCommand]
    private void ClearAllSearches()
    {
        CitySearchText = string.Empty;
        LanguageSearchText = string.Empty;
        CountrySearchText = string.Empty;
        ProductSearchText = string.Empty;
        UserSearchText = string.Empty;

        CitySuggestions.Clear();
        LanguageSuggestions.Clear();
        CountrySuggestions.Clear();
        ProductSuggestions.Clear();
        UserSuggestions.Clear();
    }

    #endregion

    #region 私有方法

    private void OnPropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        switch (e.PropertyName)
        {
            case nameof(CitySearchText):
                SearchCitiesCommand.Execute(CitySearchText);
                break;
            case nameof(LanguageSearchText):
                SearchLanguagesCommand.Execute(LanguageSearchText);
                break;
            case nameof(CountrySearchText):
                SearchCountriesCommand.Execute(CountrySearchText);
                break;
            case nameof(ProductSearchText):
                SearchProductsCommand.Execute(ProductSearchText);
                break;
            case nameof(UserSearchText):
                SearchUsersCommand.Execute(UserSearchText);
                break;
        }
    }

    private void UpdateSuggestions(string? query, List<string> source, ObservableCollection<string> target)
    {
        target.Clear();

        if (string.IsNullOrWhiteSpace(query))
            return;

        var suggestions = source
            .Where(item => item.Contains(query, StringComparison.OrdinalIgnoreCase))
            .Take(10)
            .ToList();

        foreach (var suggestion in suggestions)
        {
            target.Add(suggestion);
        }
    }

    #endregion
}

/*
AutoSuggestBox 基础功能实现要点：

1. 使用 Partial Properties 语法定义属性
2. 通过 PropertyChanged 事件监听文本变化
3. 实时搜索：UpdateSourceTrigger=PropertyChanged
4. 建议列表动态更新：根据输入内容过滤数据源
5. 命令模式：使用 RelayCommand 处理搜索逻辑
6. 数据绑定：Text 和 ItemsSource 双向绑定

关键特性：
- 实时搜索响应
- 多种数据类型支持
- 清除功能
- 图标显示
- 建议列表高度控制
*/
