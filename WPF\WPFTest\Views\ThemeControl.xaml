<UserControl
    d:DataContext="{d:DesignInstance viewModels1:ThemeControlViewModel}"
    d:DesignHeight="600"
    d:DesignWidth="500"
    mc:Ignorable="d"
    x:Class="WPFTest.Views.ThemeControl"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:viewModels="clr-namespace:Zylo.WPF.ViewModels"
    xmlns:viewModels1="clr-namespace:WPFTest.ViewModels"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <UserControl.Resources>
        <!--  颜色块样式  -->
        <Style TargetType="Border" x:Key="ColorBlockStyle">
            <Setter Property="Width" Value="40" />
            <Setter Property="Height" Value="40" />
            <Setter Property="CornerRadius" Value="8" />
            <Setter Property="BorderThickness" Value="2" />
            <Setter Property="BorderBrush" Value="Transparent" />
            <Setter Property="Margin" Value="4" />
            <Setter Property="Cursor" Value="Hand" />
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="BorderBrush" Value="{DynamicResource SystemAccentColorPrimaryBrush}" />
                    <Setter Property="BorderThickness" Value="3" />
                </Trigger>
            </Style.Triggers>
        </Style>

        <!--  主题模式按钮样式  -->
        <Style TargetType="ui:Button" x:Key="ThemeModeButtonStyle">
            <Setter Property="Margin" Value="4" />
            <Setter Property="Padding" Value="16,8" />
            <Setter Property="MinWidth" Value="120" />
            <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}" />
            <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}" />
        </Style>
    </UserControl.Resources>

    <ScrollViewer Padding="20" VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="20">

            <!--  标题  -->
            <StackPanel Margin="12" Orientation="Horizontal">
                <ui:SymbolIcon
                    FontSize="28"
                    Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                    Symbol="ColorBackground24" />
                <TextBlock
                    FontSize="24"
                    FontWeight="Bold"
                    Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                    Text="🎨 主题设置"
                    VerticalAlignment="Center" />
            </StackPanel>

            <!--  状态消息  -->
            <ui:InfoBar
                IsClosable="True"
                Margin="0,0,0,10"
                Severity="Success"
                Title="{Binding StatusMessage}"
                x:Name="StatusInfoBar">
                <ui:InfoBar.Style>
                    <Style TargetType="ui:InfoBar">
                        <Setter Property="Visibility" Value="Visible" />
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding StatusMessage}" Value="">
                                <Setter Property="Visibility" Value="Collapsed" />
                            </DataTrigger>
                            <DataTrigger Binding="{Binding StatusMessage}" Value="{x:Null}">
                                <Setter Property="Visibility" Value="Collapsed" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </ui:InfoBar.Style>
            </ui:InfoBar>

            <!--  主题模式设置  -->
            <ui:CardExpander Header="🌙 主题模式" IsExpanded="True">
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>

                    <TextBlock
                        FontSize="14"
                        Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                        Grid.Row="0"
                        Margin="0,0,0,12"
                        Text="选择应用的主题模式" />

                    <StackPanel
                        Grid.Row="1"
                        HorizontalAlignment="Left"
                        Orientation="Horizontal">
                        <ui:Button
                            Background="{DynamicResource ControlFillColorDefaultBrush}"
                            Command="{Binding UpdateThemeModeCommand}"
                            CommandParameter="Light"
                            Content="浅色"
                            Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                            Margin="0,4,8,4"
                            MinWidth="120"
                            Padding="16,8" />

                        <ui:Button
                            Background="{DynamicResource ControlFillColorDefaultBrush}"
                            Command="{Binding UpdateThemeModeCommand}"
                            CommandParameter="Dark"
                            Content="深色"
                            Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                            Margin="0,4,8,4"
                            MinWidth="120"
                            Padding="16,8" />

                        <ui:Button
                            Background="{DynamicResource ControlFillColorDefaultBrush}"
                            Command="{Binding UpdateThemeModeCommand}"
                            CommandParameter="Auto"
                            Content="跟随系统"
                            Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                            Margin="0,4,8,4"
                            MinWidth="120"
                            Padding="16,8" />
                    </StackPanel>
                </Grid>
            </ui:CardExpander>

            <!--  强调色设置  -->
            <ui:CardExpander Header="🌈 强调色设置" IsExpanded="True">
                <StackPanel Margin="16">

                    <!--  启用自定义强调色  -->
                    <ui:ToggleSwitch
                        Content="启用自定义强调色"
                        IsChecked="{Binding UseCustomAccentColor}"
                        ToolTip="启用后将使用自定义强调色，否则使用系统默认强调色" />

                    <!--  当前选中的强调色预览  -->
                    <Border
                        Background="{DynamicResource LayerFillColorDefaultBrush}"
                        BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Padding="16">
                        <StackPanel Margin="12" Orientation="Horizontal">
                            <Border
                                Background="{Binding SelectedAccentColor}"
                                CornerRadius="6"
                                Height="32"
                                Width="32" />
                            <StackPanel VerticalAlignment="Center">
                                <TextBlock
                                    FontWeight="SemiBold"
                                    Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                    Text="当前强调色" />
                                <TextBlock
                                    FontSize="12"
                                    Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                    Text="{Binding SelectedAccentColor}" />
                            </StackPanel>
                        </StackPanel>
                    </Border>

                    <!--  预定义强调色选择  -->
                    <StackPanel>
                        <TextBlock
                            FontWeight="SemiBold"
                            Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                            Margin="0,0,0,8"
                            Text="预定义颜色" />

                        <ItemsControl ItemsSource="{Binding AccentColorOptions}">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <WrapPanel Orientation="Horizontal" />
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border
                                        Background="{Binding ColorBrush}"
                                        Style="{StaticResource ColorBlockStyle}"
                                        ToolTip="{Binding Name}">
                                        <Border.InputBindings>
                                            <MouseBinding
                                                Command="{Binding DataContext.SelectAccentColorCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                CommandParameter="{Binding}"
                                                MouseAction="LeftClick" />
                                        </Border.InputBindings>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>

                    <!--  自定义颜色选择器  -->
                    <StackPanel Margin="0,16,0,0">
                        <TextBlock
                            FontWeight="SemiBold"
                            Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                            Margin="0,0,0,12"
                            Text="🎨 自定义颜色" />

                        <!--  现代化颜色调色板  -->
                        <ui:Card Margin="0,0,0,12" Padding="16">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>

                                <!--  流行色彩调色板  -->
                                <StackPanel Grid.Row="0" Margin="0,0,0,16">
                                    <TextBlock
                                        FontSize="13"
                                        FontWeight="Medium"
                                        Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                        Margin="0,0,0,8"
                                        Text="流行色彩" />

                                    <UniformGrid Columns="8" Rows="3">
                                        <!--  第一行：蓝色系  -->
                                        <Border
                                            Background="#0078D4"
                                            Style="{StaticResource ColorBlockStyle}"
                                            Tag="#0078D4"
                                            ToolTip="Azure Blue">
                                            <Border.InputBindings>
                                                <MouseBinding
                                                    Command="{Binding SelectCustomColorCommand}"
                                                    CommandParameter="#0078D4"
                                                    MouseAction="LeftClick" />
                                            </Border.InputBindings>
                                        </Border>
                                        <Border
                                            Background="#106EBE"
                                            Style="{StaticResource ColorBlockStyle}"
                                            Tag="#106EBE"
                                            ToolTip="Deep Blue">
                                            <Border.InputBindings>
                                                <MouseBinding
                                                    Command="{Binding SelectCustomColorCommand}"
                                                    CommandParameter="#106EBE"
                                                    MouseAction="LeftClick" />
                                            </Border.InputBindings>
                                        </Border>
                                        <Border
                                            Background="#005A9E"
                                            Style="{StaticResource ColorBlockStyle}"
                                            Tag="#005A9E"
                                            ToolTip="Navy Blue">
                                            <Border.InputBindings>
                                                <MouseBinding
                                                    Command="{Binding SelectCustomColorCommand}"
                                                    CommandParameter="#005A9E"
                                                    MouseAction="LeftClick" />
                                            </Border.InputBindings>
                                        </Border>
                                        <Border
                                            Background="#004578"
                                            Style="{StaticResource ColorBlockStyle}"
                                            Tag="#004578"
                                            ToolTip="Dark Navy">
                                            <Border.InputBindings>
                                                <MouseBinding
                                                    Command="{Binding SelectCustomColorCommand}"
                                                    CommandParameter="#004578"
                                                    MouseAction="LeftClick" />
                                            </Border.InputBindings>
                                        </Border>
                                        <Border
                                            Background="#40E0D0"
                                            Style="{StaticResource ColorBlockStyle}"
                                            Tag="#40E0D0"
                                            ToolTip="Turquoise">
                                            <Border.InputBindings>
                                                <MouseBinding
                                                    Command="{Binding SelectCustomColorCommand}"
                                                    CommandParameter="#40E0D0"
                                                    MouseAction="LeftClick" />
                                            </Border.InputBindings>
                                        </Border>
                                        <Border
                                            Background="#00CED1"
                                            Style="{StaticResource ColorBlockStyle}"
                                            Tag="#00CED1"
                                            ToolTip="Dark Turquoise">
                                            <Border.InputBindings>
                                                <MouseBinding
                                                    Command="{Binding SelectCustomColorCommand}"
                                                    CommandParameter="#00CED1"
                                                    MouseAction="LeftClick" />
                                            </Border.InputBindings>
                                        </Border>
                                        <Border
                                            Background="#5F9EA0"
                                            Style="{StaticResource ColorBlockStyle}"
                                            Tag="#5F9EA0"
                                            ToolTip="Cadet Blue">
                                            <Border.InputBindings>
                                                <MouseBinding
                                                    Command="{Binding SelectCustomColorCommand}"
                                                    CommandParameter="#5F9EA0"
                                                    MouseAction="LeftClick" />
                                            </Border.InputBindings>
                                        </Border>
                                        <Border
                                            Background="#4682B4"
                                            Style="{StaticResource ColorBlockStyle}"
                                            Tag="#4682B4"
                                            ToolTip="Steel Blue">
                                            <Border.InputBindings>
                                                <MouseBinding
                                                    Command="{Binding SelectCustomColorCommand}"
                                                    CommandParameter="#4682B4"
                                                    MouseAction="LeftClick" />
                                            </Border.InputBindings>
                                        </Border>

                                        <!--  第二行：绿色系  -->
                                        <Border
                                            Background="#107C10"
                                            Style="{StaticResource ColorBlockStyle}"
                                            Tag="#107C10"
                                            ToolTip="Forest Green">
                                            <Border.InputBindings>
                                                <MouseBinding
                                                    Command="{Binding SelectCustomColorCommand}"
                                                    CommandParameter="#107C10"
                                                    MouseAction="LeftClick" />
                                            </Border.InputBindings>
                                        </Border>
                                        <Border
                                            Background="#0E700E"
                                            Style="{StaticResource ColorBlockStyle}"
                                            Tag="#0E700E"
                                            ToolTip="Dark Green">
                                            <Border.InputBindings>
                                                <MouseBinding
                                                    Command="{Binding SelectCustomColorCommand}"
                                                    CommandParameter="#0E700E"
                                                    MouseAction="LeftClick" />
                                            </Border.InputBindings>
                                        </Border>
                                        <Border
                                            Background="#0C5F0C"
                                            Style="{StaticResource ColorBlockStyle}"
                                            Tag="#0C5F0C"
                                            ToolTip="Deep Green">
                                            <Border.InputBindings>
                                                <MouseBinding
                                                    Command="{Binding SelectCustomColorCommand}"
                                                    CommandParameter="#0C5F0C"
                                                    MouseAction="LeftClick" />
                                            </Border.InputBindings>
                                        </Border>
                                        <Border
                                            Background="#0A4F0A"
                                            Style="{StaticResource ColorBlockStyle}"
                                            Tag="#0A4F0A"
                                            ToolTip="Pine Green">
                                            <Border.InputBindings>
                                                <MouseBinding
                                                    Command="{Binding SelectCustomColorCommand}"
                                                    CommandParameter="#0A4F0A"
                                                    MouseAction="LeftClick" />
                                            </Border.InputBindings>
                                        </Border>
                                        <Border
                                            Background="#32CD32"
                                            Style="{StaticResource ColorBlockStyle}"
                                            Tag="#32CD32"
                                            ToolTip="Lime Green">
                                            <Border.InputBindings>
                                                <MouseBinding
                                                    Command="{Binding SelectCustomColorCommand}"
                                                    CommandParameter="#32CD32"
                                                    MouseAction="LeftClick" />
                                            </Border.InputBindings>
                                        </Border>
                                        <Border
                                            Background="#00FF7F"
                                            Style="{StaticResource ColorBlockStyle}"
                                            Tag="#00FF7F"
                                            ToolTip="Spring Green">
                                            <Border.InputBindings>
                                                <MouseBinding
                                                    Command="{Binding SelectCustomColorCommand}"
                                                    CommandParameter="#00FF7F"
                                                    MouseAction="LeftClick" />
                                            </Border.InputBindings>
                                        </Border>
                                        <Border
                                            Background="#98FB98"
                                            Style="{StaticResource ColorBlockStyle}"
                                            Tag="#98FB98"
                                            ToolTip="Pale Green">
                                            <Border.InputBindings>
                                                <MouseBinding
                                                    Command="{Binding SelectCustomColorCommand}"
                                                    CommandParameter="#98FB98"
                                                    MouseAction="LeftClick" />
                                            </Border.InputBindings>
                                        </Border>
                                        <Border
                                            Background="#90EE90"
                                            Style="{StaticResource ColorBlockStyle}"
                                            Tag="#90EE90"
                                            ToolTip="Light Green">
                                            <Border.InputBindings>
                                                <MouseBinding
                                                    Command="{Binding SelectCustomColorCommand}"
                                                    CommandParameter="#90EE90"
                                                    MouseAction="LeftClick" />
                                            </Border.InputBindings>
                                        </Border>

                                        <!--  第三行：暖色系  -->
                                        <Border
                                            Background="#D13438"
                                            Style="{StaticResource ColorBlockStyle}"
                                            Tag="#D13438"
                                            ToolTip="Crimson Red">
                                            <Border.InputBindings>
                                                <MouseBinding
                                                    Command="{Binding SelectCustomColorCommand}"
                                                    CommandParameter="#D13438"
                                                    MouseAction="LeftClick" />
                                            </Border.InputBindings>
                                        </Border>
                                        <Border
                                            Background="#FF4500"
                                            Style="{StaticResource ColorBlockStyle}"
                                            Tag="#FF4500"
                                            ToolTip="Orange Red">
                                            <Border.InputBindings>
                                                <MouseBinding
                                                    Command="{Binding SelectCustomColorCommand}"
                                                    CommandParameter="#FF4500"
                                                    MouseAction="LeftClick" />
                                            </Border.InputBindings>
                                        </Border>
                                        <Border
                                            Background="#FF8C00"
                                            Style="{StaticResource ColorBlockStyle}"
                                            Tag="#FF8C00"
                                            ToolTip="Dark Orange">
                                            <Border.InputBindings>
                                                <MouseBinding
                                                    Command="{Binding SelectCustomColorCommand}"
                                                    CommandParameter="#FF8C00"
                                                    MouseAction="LeftClick" />
                                            </Border.InputBindings>
                                        </Border>
                                        <Border
                                            Background="#FFD700"
                                            Style="{StaticResource ColorBlockStyle}"
                                            Tag="#FFD700"
                                            ToolTip="Gold">
                                            <Border.InputBindings>
                                                <MouseBinding
                                                    Command="{Binding SelectCustomColorCommand}"
                                                    CommandParameter="#FFD700"
                                                    MouseAction="LeftClick" />
                                            </Border.InputBindings>
                                        </Border>
                                        <Border
                                            Background="#9A0089"
                                            Style="{StaticResource ColorBlockStyle}"
                                            Tag="#9A0089"
                                            ToolTip="Purple">
                                            <Border.InputBindings>
                                                <MouseBinding
                                                    Command="{Binding SelectCustomColorCommand}"
                                                    CommandParameter="#9A0089"
                                                    MouseAction="LeftClick" />
                                            </Border.InputBindings>
                                        </Border>
                                        <Border
                                            Background="#8B008B"
                                            Style="{StaticResource ColorBlockStyle}"
                                            Tag="#8B008B"
                                            ToolTip="Dark Magenta">
                                            <Border.InputBindings>
                                                <MouseBinding
                                                    Command="{Binding SelectCustomColorCommand}"
                                                    CommandParameter="#8B008B"
                                                    MouseAction="LeftClick" />
                                            </Border.InputBindings>
                                        </Border>
                                        <Border
                                            Background="#DA70D6"
                                            Style="{StaticResource ColorBlockStyle}"
                                            Tag="#DA70D6"
                                            ToolTip="Orchid">
                                            <Border.InputBindings>
                                                <MouseBinding
                                                    Command="{Binding SelectCustomColorCommand}"
                                                    CommandParameter="#DA70D6"
                                                    MouseAction="LeftClick" />
                                            </Border.InputBindings>
                                        </Border>
                                        <Border
                                            Background="#DDA0DD"
                                            Style="{StaticResource ColorBlockStyle}"
                                            Tag="#DDA0DD"
                                            ToolTip="Plum">
                                            <Border.InputBindings>
                                                <MouseBinding
                                                    Command="{Binding SelectCustomColorCommand}"
                                                    CommandParameter="#DDA0DD"
                                                    MouseAction="LeftClick" />
                                            </Border.InputBindings>
                                        </Border>
                                    </UniformGrid>
                                </StackPanel>

                                <!--  中性色调色板  -->
                                <StackPanel Grid.Row="1" Margin="0,0,0,16">
                                    <TextBlock
                                        FontSize="13"
                                        FontWeight="Medium"
                                        Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                        Margin="0,0,0,8"
                                        Text="中性色调" />

                                    <UniformGrid Columns="8" Rows="1">
                                        <Border
                                            Background="#000000"
                                            Style="{StaticResource ColorBlockStyle}"
                                            Tag="#000000"
                                            ToolTip="Black">
                                            <Border.InputBindings>
                                                <MouseBinding
                                                    Command="{Binding SelectCustomColorCommand}"
                                                    CommandParameter="#000000"
                                                    MouseAction="LeftClick" />
                                            </Border.InputBindings>
                                        </Border>
                                        <Border
                                            Background="#2D2D30"
                                            Style="{StaticResource ColorBlockStyle}"
                                            Tag="#2D2D30"
                                            ToolTip="Dark Gray">
                                            <Border.InputBindings>
                                                <MouseBinding
                                                    Command="{Binding SelectCustomColorCommand}"
                                                    CommandParameter="#2D2D30"
                                                    MouseAction="LeftClick" />
                                            </Border.InputBindings>
                                        </Border>
                                        <Border
                                            Background="#3E3E42"
                                            Style="{StaticResource ColorBlockStyle}"
                                            Tag="#3E3E42"
                                            ToolTip="Charcoal">
                                            <Border.InputBindings>
                                                <MouseBinding
                                                    Command="{Binding SelectCustomColorCommand}"
                                                    CommandParameter="#3E3E42"
                                                    MouseAction="LeftClick" />
                                            </Border.InputBindings>
                                        </Border>
                                        <Border
                                            Background="#68768A"
                                            Style="{StaticResource ColorBlockStyle}"
                                            Tag="#68768A"
                                            ToolTip="Slate Gray">
                                            <Border.InputBindings>
                                                <MouseBinding
                                                    Command="{Binding SelectCustomColorCommand}"
                                                    CommandParameter="#68768A"
                                                    MouseAction="LeftClick" />
                                            </Border.InputBindings>
                                        </Border>
                                        <Border
                                            Background="#8A8886"
                                            Style="{StaticResource ColorBlockStyle}"
                                            Tag="#8A8886"
                                            ToolTip="Gray">
                                            <Border.InputBindings>
                                                <MouseBinding
                                                    Command="{Binding SelectCustomColorCommand}"
                                                    CommandParameter="#8A8886"
                                                    MouseAction="LeftClick" />
                                            </Border.InputBindings>
                                        </Border>
                                        <Border
                                            Background="#C8C6C4"
                                            Style="{StaticResource ColorBlockStyle}"
                                            Tag="#C8C6C4"
                                            ToolTip="Light Gray">
                                            <Border.InputBindings>
                                                <MouseBinding
                                                    Command="{Binding SelectCustomColorCommand}"
                                                    CommandParameter="#C8C6C4"
                                                    MouseAction="LeftClick" />
                                            </Border.InputBindings>
                                        </Border>
                                        <Border
                                            Background="#EDEBE9"
                                            Style="{StaticResource ColorBlockStyle}"
                                            Tag="#EDEBE9"
                                            ToolTip="Off White">
                                            <Border.InputBindings>
                                                <MouseBinding
                                                    Command="{Binding SelectCustomColorCommand}"
                                                    CommandParameter="#EDEBE9"
                                                    MouseAction="LeftClick" />
                                            </Border.InputBindings>
                                        </Border>
                                        <Border
                                            Background="#FFFFFF"
                                            Style="{StaticResource ColorBlockStyle}"
                                            Tag="#FFFFFF"
                                            ToolTip="White">
                                            <Border.InputBindings>
                                                <MouseBinding
                                                    Command="{Binding SelectCustomColorCommand}"
                                                    CommandParameter="#FFFFFF"
                                                    MouseAction="LeftClick" />
                                            </Border.InputBindings>
                                        </Border>
                                    </UniformGrid>
                                </StackPanel>
                            </Grid>
                        </ui:Card>
                    </StackPanel>

                    <!--  提示信息  -->
                    <StackPanel>
                        <TextBlock
                            FontWeight="SemiBold"
                            Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                            Margin="0,0,0,8"
                            Text="💡 提示" />

                        <Border
                            Background="{DynamicResource LayerFillColorDefaultBrush}"
                            BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                            BorderThickness="1"
                            CornerRadius="8"
                            Padding="16">
                            <TextBlock
                                FontSize="14"
                                Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                Text="🎨 点击上方颜色块可快速选择流行色彩，或使用预定义颜色进行精确调节。所有颜色变更都会实时预览效果！"
                                TextWrapping="Wrap" />
                        </Border>
                    </StackPanel>
                </StackPanel>
            </ui:CardExpander>

            <!--  操作按钮  -->
            <ui:Card Padding="16">
                <StackPanel
                    HorizontalAlignment="Center"
                    Margin="12"
                    Orientation="Horizontal">
                    <ui:Button
                        Background="{DynamicResource SystemAccentColorPrimaryBrush}"
                        Command="{Binding ApplyThemeCommand}"
                        Content="✅ 应用主题"
                        FontWeight="SemiBold"
                        Foreground="{DynamicResource TextFillColorInverseBrush}"
                        Padding="20,10" />

                    <ui:Button
                        Background="{DynamicResource ControlFillColorDefaultBrush}"
                        Command="{Binding ResetThemeCommand}"
                        Content="🔄 重置默认"
                        Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                        Padding="20,10" />
                </StackPanel>
            </ui:Card>

            <!--  配置信息  -->
            <ui:CardExpander Header="ℹ️ 配置信息" IsExpanded="False">
                <StackPanel Margin="8">
                    <TextBlock
                        FontWeight="SemiBold"
                        Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                        Text="配置文件位置:" />
                    <TextBlock
                        FontSize="12"
                        Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                        Text="配置文件将保存在应用数据目录中"
                        TextWrapping="Wrap" />

                    <TextBlock
                        FontWeight="SemiBold"
                        Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                        Margin="0,8,0,0"
                        Text="说明:" />
                    <TextBlock
                        FontSize="12"
                        Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                        Text="• 主题设置会自动保存到配置文件&#x0a;• 应用重启后会自动加载上次的设置&#x0a;• 强调色会影响整个应用的UI风格"
                        TextWrapping="Wrap" />
                </StackPanel>
            </ui:CardExpander>

        </StackPanel>
    </ScrollViewer>
</UserControl>
