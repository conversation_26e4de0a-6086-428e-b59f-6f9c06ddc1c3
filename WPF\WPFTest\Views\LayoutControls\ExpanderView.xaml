<UserControl x:Class="WPFTest.Views.LayoutControls.ExpanderView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:codeExample="clr-namespace:Zylo.WPF.Controls.CodeExample;assembly=Zylo.WPF"
             xmlns:mvvm="http://prismlibrary.com/"
             xmlns:layoutControls="clr-namespace:WPFTest.ViewModels.LayoutControls"
             mc:Ignorable="d"
             d:DesignHeight="800" d:DesignWidth="1200"
             mvvm:ViewModelLocator.AutoWireViewModel="True"
             d:DataContext="{d:DesignInstance layoutControls:ExpanderViewModel}">

    <UserControl.Resources>
        <!-- 🎨 所有样式来自 Zylo.WPF 项目 -->
        <!-- Expander 样式已在 Zylo.WPF/Resources/Expander/ 目录下定义 -->
        <!-- 可用样式：ExpanderBaseStyle, ModernExpanderStyle, CardExpanderStyle -->
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" 
                  HorizontalScrollBarVisibility="Disabled"
                  Padding="24">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>    <!-- 标题区域 -->
                <RowDefinition Height="*"/>       <!-- 主要内容 -->
                <RowDefinition Height="Auto"/>    <!-- 底部操作栏 -->
            </Grid.RowDefinitions>

            <!-- 标题区域 -->
            <StackPanel Grid.Row="0" Margin="0,0,0,20">
                <TextBlock Text="🎨 Expander 控件展示"
                           FontSize="32"
                           FontWeight="Bold"
                           Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                           Margin="0,0,0,8"/>
                <TextBlock Text="展示 WPF-UI 增强的 Expander 控件的各种样式和交互效果" 
                           FontSize="16" 
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                           TextWrapping="Wrap"/>
                
                <!-- 状态显示 -->
                <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                        BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Padding="16,12"
                        Margin="0,16,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <ui:SymbolIcon Symbol="Info24" 
                                       FontSize="20" 
                                       Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                       VerticalAlignment="Center"
                                       Margin="0,0,12,0"/>
                        <TextBlock Grid.Column="1" 
                                   Text="{Binding StatusMessage}" 
                                   FontSize="14"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                   VerticalAlignment="Center"
                                   TextWrapping="Wrap"/>
                    </Grid>
                </Border>
            </StackPanel>

            <!-- 主要内容区域 -->
            <Border Grid.Row="1" Margin="0,0,0,20">
                <StackPanel>

                    <!-- 基础功能展示 -->
                    <ui:CardExpander Header="🎯 基础功能" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 Expander 的基础功能和用法"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>
                            
                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="基础 Expander 示例：" FontWeight="Medium" Margin="0,0,0,16"/>
                                    <StackPanel>
                                        <!-- 标准 Expander -->
                                        <Expander Header="标准 Expander"
                                                  IsExpanded="False"
                                                  Background="{DynamicResource ControlFillColorDefaultBrush}"
                                                  BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                                  BorderThickness="1"
                                                  Margin="4,4,4,12">
                                            <StackPanel Margin="16">
                                                <TextBlock Text="这是 Expander 的内容区域。" Margin="0,0,0,8"/>
                                                <TextBlock Text="可以包含任何 WPF 控件和布局。" Margin="0,0,0,8"/>
                                                <Button Content="示例按钮" 
                                                        Command="{Binding HandleInteractionCommand}"
                                                        CommandParameter="标准Expander按钮"
                                                        HorizontalAlignment="Left"/>
                                            </StackPanel>
                                        </Expander>

                                        <!-- 默认展开的 Expander -->
                                        <Expander Header="默认展开 Expander"
                                                  IsExpanded="True"
                                                  Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                                  BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                                  BorderThickness="1"
                                                  Margin="4,4,4,12">
                                            <StackPanel Margin="16">
                                                <TextBlock Text="这个 Expander 默认是展开状态。" Margin="0,0,0,8"/>
                                                <CheckBox Content="示例复选框" Margin="0,0,0,8"/>
                                                <Slider Minimum="0" Maximum="100" Value="50" Margin="0,0,0,8"/>
                                            </StackPanel>
                                        </Expander>

                                        <!-- 带图标的 Expander -->
                                        <Expander IsExpanded="False"
                                                  Background="{DynamicResource AccentFillColorDefaultBrush}"
                                                  BorderBrush="{DynamicResource AccentStrokeColorDefaultBrush}"
                                                  BorderThickness="1"
                                                  Margin="4,4,4,12">
                                            <Expander.Header>
                                                <StackPanel Orientation="Horizontal">
                                                    <ui:SymbolIcon Symbol="Settings24" 
                                                                   FontSize="16" 
                                                                   Foreground="White"
                                                                   Margin="0,0,8,0"/>
                                                    <TextBlock Text="带图标的 Expander" 
                                                               Foreground="White"
                                                               VerticalAlignment="Center"/>
                                                </StackPanel>
                                            </Expander.Header>
                                            <StackPanel Margin="16">
                                                <TextBlock Text="这个 Expander 的标题包含图标。"
                                                           Foreground="White"
                                                           Margin="0,0,0,8"/>
                                                <ProgressBar Value="75" Height="20" Margin="0,0,0,8"/>
                                                <TextBlock Text="进度: 75%"
                                                           Foreground="White"
                                                           FontSize="12"/>
                                            </StackPanel>
                                        </Expander>
                                    </StackPanel>
                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="基础功能代码示例"
                                Language="XAML"
                                Description="展示 Expander 的基础用法"
                                ShowTabs="True"
                                XamlCode="{Binding BasicXamlExample}"
                                CSharpCode="{Binding BasicCSharpExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 高级功能展示 -->
                    <ui:CardExpander Header="⚡ 高级功能" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 Expander 的高级功能和自定义样式"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>
                            
                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="高级 Expander 示例：" FontWeight="Medium" Margin="0,0,0,16"/>
                                    <StackPanel>
                                        <!-- 嵌套 Expander -->
                                        <Expander Header="嵌套 Expander 示例"
                                                  IsExpanded="False"
                                                  Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                                                  BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                                  BorderThickness="1"
                                                  Margin="4,4,4,12">
                                            <StackPanel Margin="16">
                                                <TextBlock Text="这是父级 Expander 的内容。" Margin="0,0,0,12"/>

                                                <Expander Header="子级 Expander 1"
                                                          IsExpanded="False"
                                                          Background="{DynamicResource ControlFillColorDefaultBrush}"
                                                          Margin="0,0,0,8">
                                                    <StackPanel Margin="12">
                                                        <TextBlock Text="这是第一个子级 Expander。"/>
                                                        <Button Content="子级按钮 1" 
                                                                Command="{Binding HandleInteractionCommand}"
                                                                CommandParameter="子级Expander1"
                                                                HorizontalAlignment="Left"
                                                                Margin="0,8,0,0"/>
                                                    </StackPanel>
                                                </Expander>
                                                
                                                <Expander Header="子级 Expander 2"
                                                          IsExpanded="False"
                                                          Background="{DynamicResource ControlFillColorDefaultBrush}">
                                                    <StackPanel Margin="12">
                                                        <TextBlock Text="这是第二个子级 Expander。"/>
                                                        <ComboBox Margin="0,8,0,0">
                                                            <ComboBoxItem Content="选项 1"/>
                                                            <ComboBoxItem Content="选项 2"/>
                                                            <ComboBoxItem Content="选项 3"/>
                                                        </ComboBox>
                                                    </StackPanel>
                                                </Expander>
                                            </StackPanel>
                                        </Expander>

                                        <!-- 自定义样式 Expander -->
                                        <Expander Header="自定义样式 Expander"
                                                  IsExpanded="False"
                                                  Margin="4,4,4,12">
                                            <Expander.Style>
                                                <Style TargetType="Expander">
                                                    <Setter Property="Background" Value="#FF2D3748"/>
                                                    <Setter Property="Foreground" Value="White"/>
                                                    <Setter Property="BorderBrush" Value="#FF4A5568"/>
                                                    <Setter Property="BorderThickness" Value="2"/>
                                                    <Setter Property="Padding" Value="12"/>
                                                </Style>
                                            </Expander.Style>
                                            <StackPanel Margin="16">
                                                <TextBlock Text="这是一个自定义样式的 Expander。" 
                                                           Foreground="White" 
                                                           Margin="0,0,0,12"/>
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                                        <TextBlock Text="左侧内容" 
                                                                   Foreground="White" 
                                                                   FontWeight="Bold" 
                                                                   Margin="0,0,0,8"/>
                                                        <TextBox Text="输入框示例" Margin="0,0,0,8"/>
                                                        <Button Content="左侧按钮" 
                                                                Command="{Binding HandleInteractionCommand}"
                                                                CommandParameter="自定义样式左侧"/>
                                                    </StackPanel>
                                                    <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                                        <TextBlock Text="右侧内容" 
                                                                   Foreground="White" 
                                                                   FontWeight="Bold" 
                                                                   Margin="0,0,0,8"/>
                                                        <ListBox Height="80" Margin="0,0,0,8">
                                                            <ListBoxItem Content="项目 1"/>
                                                            <ListBoxItem Content="项目 2"/>
                                                            <ListBoxItem Content="项目 3"/>
                                                        </ListBox>
                                                        <Button Content="右侧按钮" 
                                                                Command="{Binding HandleInteractionCommand}"
                                                                CommandParameter="自定义样式右侧"/>
                                                    </StackPanel>
                                                </Grid>
                                            </StackPanel>
                                        </Expander>
                                    </StackPanel>
                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="高级功能代码示例"
                                Language="XAML"
                                Description="展示 Expander 的高级用法和自定义样式"
                                ShowTabs="True"
                                XamlCode="{Binding AdvancedXamlExample}"
                                CSharpCode="{Binding AdvancedCSharpExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 样式展示 -->
                    <ui:CardExpander Header="🎨 样式展示" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示来自 Zylo.WPF 的各种 Expander 样式"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>
                            
                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="样式展示示例：" FontWeight="Medium" Margin="0,0,0,16"/>
                                    <StackPanel>
                                        <!-- 这里将添加不同样式的 Expander 示例 -->
                                        <Expander Header="默认样式" 
                                                  IsExpanded="False"
                                                  Background="{DynamicResource ControlFillColorDefaultBrush}"
                                                  BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                                  BorderThickness="1"
                                                  Margin="4">
                                            <StackPanel Margin="16">
                                                <TextBlock Text="这是默认样式的 Expander。"/>
                                            </StackPanel>
                                        </Expander>
                                    </StackPanel>
                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="样式代码示例"
                                Language="XAML"
                                Description="展示如何使用 Zylo.WPF 中的 Expander 样式"
                                ShowTabs="True"
                                XamlCode="{Binding StylesXamlExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                </StackPanel>
            </Border>

            <!-- 底部操作栏 -->
            <Border Grid.Row="2" 
                    Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Padding="16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 统计信息 -->
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock Text="{Binding InteractionCount, StringFormat='交互次数: {0}'}" 
                                   FontSize="14" 
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   Margin="0,0,20,0"/>
                        <TextBlock Text="{Binding LastAction, StringFormat='最后操作: {0}'}" 
                                   FontSize="14" 
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                    </StackPanel>

                    <!-- 操作按钮 -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <ui:Button Content="重置计数" 
                                   Command="{Binding ResetCountCommand}"
                                   Appearance="Secondary"
                                   Margin="0,0,8,0"/>
                        <ui:Button Content="清除状态" 
                                   Command="{Binding ClearStatusCommand}"
                                   Appearance="Secondary"/>
                    </StackPanel>
                </Grid>
            </Border>

        </Grid>
    </ScrollViewer>
</UserControl>
