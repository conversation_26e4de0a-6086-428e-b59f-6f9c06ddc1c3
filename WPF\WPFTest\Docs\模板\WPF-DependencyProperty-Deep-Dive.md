# 🏗️ WPF 依赖属性深度解析

## 📚 目录

1. [依赖属性基础概念](#依赖属性基础概念)
2. [依赖属性的完整实现](#依赖属性的完整实现)
3. [附加属性详解](#附加属性详解)
4. [属性值优先级系统](#属性值优先级系统)
5. [元数据与回调机制](#元数据与回调机制)
6. [实际应用场景](#实际应用场景)

---

## 🎯 依赖属性基础概念

### **为什么需要依赖属性？**

普通 CLR 属性的局限性：
- ❌ 不支持数据绑定
- ❌ 不支持样式设置
- ❌ 不支持动画
- ❌ 不支持属性值继承
- ❌ 不支持属性变化通知
- ❌ 内存占用大（每个实例都存储值）

依赖属性的优势：
- ✅ 完整的数据绑定支持
- ✅ 样式和模板系统
- ✅ 动画系统支持
- ✅ 属性值继承
- ✅ 属性变化通知
- ✅ 内存优化（只存储非默认值）
- ✅ 值优先级系统
- ✅ 属性值验证和强制

### **依赖属性的核心组件**

```csharp
public class MyControl : DependencyObject
{
    // 1. 依赖属性标识符 (DependencyProperty)
    public static readonly DependencyProperty MyPropertyProperty = 
        DependencyProperty.Register(/* 参数 */);
    
    // 2. CLR 属性包装器
    public string MyProperty
    {
        get => (string)GetValue(MyPropertyProperty);
        set => SetValue(MyPropertyProperty, value);
    }
    
    // 3. 属性元数据 (PropertyMetadata)
    // 4. 属性变化回调 (PropertyChangedCallback)
    // 5. 值强制回调 (CoerceValueCallback)
    // 6. 值验证回调 (ValidateValueCallback)
}
```

---

## 🔧 依赖属性的完整实现

### **基本依赖属性**

```csharp
public class CustomTextBox : TextBox
{
    // 1. 注册依赖属性
    public static readonly DependencyProperty PlaceholderTextProperty =
        DependencyProperty.Register(
            "PlaceholderText",              // 属性名
            typeof(string),                 // 属性类型
            typeof(CustomTextBox),          // 所有者类型
            new PropertyMetadata(           // 属性元数据
                string.Empty,               // 默认值
                OnPlaceholderTextChanged    // 属性变化回调
            )
        );
    
    // 2. CLR 属性包装器
    public string PlaceholderText
    {
        get => (string)GetValue(PlaceholderTextProperty);
        set => SetValue(PlaceholderTextProperty, value);
    }
    
    // 3. 属性变化回调
    private static void OnPlaceholderTextChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is CustomTextBox textBox)
        {
            textBox.OnPlaceholderTextChanged((string)e.OldValue, (string)e.NewValue);
        }
    }
    
    // 4. 实例方法处理属性变化
    protected virtual void OnPlaceholderTextChanged(string oldValue, string newValue)
    {
        // 更新 UI 或执行其他逻辑
        UpdatePlaceholderVisibility();
    }
    
    private void UpdatePlaceholderVisibility()
    {
        // 实现占位符显示逻辑
    }
}
```

### **带验证和强制的依赖属性**

```csharp
public class NumericUpDown : Control
{
    public static readonly DependencyProperty ValueProperty =
        DependencyProperty.Register(
            "Value",
            typeof(double),
            typeof(NumericUpDown),
            new PropertyMetadata(
                0.0,                        // 默认值
                OnValueChanged,             // 属性变化回调
                CoerceValue                 // 值强制回调
            ),
            ValidateValue                   // 值验证回调
        );
    
    public double Value
    {
        get => (double)GetValue(ValueProperty);
        set => SetValue(ValueProperty, value);
    }
    
    // 最小值属性
    public static readonly DependencyProperty MinimumProperty =
        DependencyProperty.Register(
            "Minimum",
            typeof(double),
            typeof(NumericUpDown),
            new PropertyMetadata(
                double.MinValue,
                OnMinimumChanged
            )
        );
    
    public double Minimum
    {
        get => (double)GetValue(MinimumProperty);
        set => SetValue(MinimumProperty, value);
    }
    
    // 最大值属性
    public static readonly DependencyProperty MaximumProperty =
        DependencyProperty.Register(
            "Maximum",
            typeof(double),
            typeof(NumericUpDown),
            new PropertyMetadata(
                double.MaxValue,
                OnMaximumChanged
            )
        );
    
    public double Maximum
    {
        get => (double)GetValue(MaximumProperty);
        set => SetValue(MaximumProperty, value);
    }
    
    // 值验证回调
    private static bool ValidateValue(object value)
    {
        return value is double d && !double.IsNaN(d) && !double.IsInfinity(d);
    }
    
    // 值强制回调
    private static object CoerceValue(DependencyObject d, object baseValue)
    {
        var control = (NumericUpDown)d;
        var value = (double)baseValue;
        
        // 确保值在最小值和最大值之间
        value = Math.Max(control.Minimum, value);
        value = Math.Min(control.Maximum, value);
        
        return value;
    }
    
    // 值变化回调
    private static void OnValueChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        var control = (NumericUpDown)d;
        control.OnValueChanged((double)e.OldValue, (double)e.NewValue);
    }
    
    protected virtual void OnValueChanged(double oldValue, double newValue)
    {
        // 触发值变化事件
        RaiseValueChangedEvent(oldValue, newValue);
    }
    
    // 最小值变化时重新强制 Value
    private static void OnMinimumChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        var control = (NumericUpDown)d;
        control.CoerceValue(ValueProperty);
    }
    
    // 最大值变化时重新强制 Value
    private static void OnMaximumChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        var control = (NumericUpDown)d;
        control.CoerceValue(ValueProperty);
    }
    
    private void RaiseValueChangedEvent(double oldValue, double newValue)
    {
        // 实现事件触发逻辑
    }
}
```

### **只读依赖属性**

```csharp
public class ProgressControl : Control
{
    // 只读依赖属性的键
    private static readonly DependencyPropertyKey IsCompletedPropertyKey =
        DependencyProperty.RegisterReadOnly(
            "IsCompleted",
            typeof(bool),
            typeof(ProgressControl),
            new PropertyMetadata(false)
        );
    
    // 公开的只读依赖属性
    public static readonly DependencyProperty IsCompletedProperty = 
        IsCompletedPropertyKey.DependencyProperty;
    
    public bool IsCompleted
    {
        get => (bool)GetValue(IsCompletedProperty);
        private set => SetValue(IsCompletedPropertyKey, value);
    }
    
    // Progress 属性变化时更新 IsCompleted
    public static readonly DependencyProperty ProgressProperty =
        DependencyProperty.Register(
            "Progress",
            typeof(double),
            typeof(ProgressControl),
            new PropertyMetadata(0.0, OnProgressChanged),
            ValidateProgress
        );
    
    public double Progress
    {
        get => (double)GetValue(ProgressProperty);
        set => SetValue(ProgressProperty, value);
    }
    
    private static bool ValidateProgress(object value)
    {
        var progress = (double)value;
        return progress >= 0.0 && progress <= 100.0;
    }
    
    private static void OnProgressChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        var control = (ProgressControl)d;
        var newProgress = (double)e.NewValue;
        
        // 更新只读属性
        control.IsCompleted = newProgress >= 100.0;
    }
}
```

---

## 📎 附加属性详解

### **什么是附加属性？**

附加属性允许任何对象为其他对象定义属性值，主要用于：
- 🎯 布局系统 (Grid.Row, Canvas.Left)
- 🎨 样式和行为扩展
- 🔧 功能增强
- 📋 数据附加

### **创建附加属性**

```csharp
public static class GridHelper
{
    // 1. 注册附加属性
    public static readonly DependencyProperty RowSpanProperty =
        DependencyProperty.RegisterAttached(
            "RowSpan",                      // 属性名
            typeof(int),                    // 属性类型
            typeof(GridHelper),             // 所有者类型
            new PropertyMetadata(1, OnRowSpanChanged)
        );
    
    // 2. Getter 方法
    public static int GetRowSpan(DependencyObject obj)
    {
        return (int)obj.GetValue(RowSpanProperty);
    }
    
    // 3. Setter 方法
    public static void SetRowSpan(DependencyObject obj, int value)
    {
        obj.SetValue(RowSpanProperty, value);
    }
    
    // 4. 属性变化回调
    private static void OnRowSpanChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is FrameworkElement element && element.Parent is Grid grid)
        {
            // 更新 Grid 布局
            Grid.SetRowSpan(element, (int)e.NewValue);
        }
    }
}
```

### **行为附加属性示例**

```csharp
public static class TextBoxBehavior
{
    // 自动选择全部文本的附加属性
    public static readonly DependencyProperty SelectAllOnFocusProperty =
        DependencyProperty.RegisterAttached(
            "SelectAllOnFocus",
            typeof(bool),
            typeof(TextBoxBehavior),
            new PropertyMetadata(false, OnSelectAllOnFocusChanged)
        );
    
    public static bool GetSelectAllOnFocus(DependencyObject obj)
        => (bool)obj.GetValue(SelectAllOnFocusProperty);
    
    public static void SetSelectAllOnFocus(DependencyObject obj, bool value)
        => obj.SetValue(SelectAllOnFocusProperty, value);
    
    private static void OnSelectAllOnFocusChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is TextBox textBox)
        {
            if ((bool)e.NewValue)
            {
                textBox.GotFocus += OnTextBoxGotFocus;
                textBox.PreviewMouseLeftButtonDown += OnTextBoxPreviewMouseLeftButtonDown;
            }
            else
            {
                textBox.GotFocus -= OnTextBoxGotFocus;
                textBox.PreviewMouseLeftButtonDown -= OnTextBoxPreviewMouseLeftButtonDown;
            }
        }
    }
    
    private static void OnTextBoxGotFocus(object sender, RoutedEventArgs e)
    {
        if (sender is TextBox textBox)
        {
            textBox.SelectAll();
        }
    }
    
    private static void OnTextBoxPreviewMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
    {
        if (sender is TextBox textBox && !textBox.IsKeyboardFocusWithin)
        {
            textBox.Focus();
            e.Handled = true;
        }
    }
}
```

### **数据绑定附加属性**

```csharp
public static class BindingHelper
{
    // TreeView SelectedItem 绑定附加属性
    public static readonly DependencyProperty SelectedItemBindingProperty =
        DependencyProperty.RegisterAttached(
            "SelectedItemBinding",
            typeof(object),
            typeof(BindingHelper),
            new FrameworkPropertyMetadata(
                null,
                FrameworkPropertyMetadataOptions.BindsTwoWayByDefault,
                OnSelectedItemBindingChanged
            )
        );
    
    public static object GetSelectedItemBinding(DependencyObject obj)
        => obj.GetValue(SelectedItemBindingProperty);
    
    public static void SetSelectedItemBinding(DependencyObject obj, object value)
        => obj.SetValue(SelectedItemBindingProperty, value);
    
    private static void OnSelectedItemBindingChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is TreeView treeView)
        {
            // 移除旧的事件处理器
            treeView.SelectedItemChanged -= OnTreeViewSelectedItemChanged;
            
            // 添加新的事件处理器
            treeView.SelectedItemChanged += OnTreeViewSelectedItemChanged;
            
            // 如果新值不为空，尝试设置选中项
            if (e.NewValue != null)
            {
                SetTreeViewSelectedItem(treeView, e.NewValue);
            }
        }
    }
    
    private static void OnTreeViewSelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
    {
        if (sender is TreeView treeView)
        {
            SetSelectedItemBinding(treeView, e.NewValue);
        }
    }
    
    private static void SetTreeViewSelectedItem(TreeView treeView, object item)
    {
        // 实现设置 TreeView 选中项的逻辑
        // 这里需要遍历 TreeView 的项目来找到匹配的项
    }
}
```

---

## ⚖️ 属性值优先级系统

WPF 依赖属性有一个复杂的值优先级系统，按优先级从高到低：

1. **动画值** (Animation)
2. **本地值** (Local Value)
3. **模板触发器** (Template Triggers)
4. **样式触发器** (Style Triggers)
5. **模板设置器** (Template Setters)
6. **样式设置器** (Style Setters)
7. **继承值** (Inherited Value)
8. **默认值** (Default Value)

### **优先级示例**

```csharp
// 示例：Button 的 Background 属性
public partial class MainWindow : Window
{
    public MainWindow()
    {
        InitializeComponent();
        
        var button = new Button { Content = "Test" };
        
        // 8. 默认值 (最低优先级)
        // Background = null (系统默认)
        
        // 6. 样式设置器
        button.Style = new Style(typeof(Button));
        button.Style.Setters.Add(new Setter(Button.BackgroundProperty, Brushes.Blue));
        
        // 2. 本地值 (高优先级)
        button.Background = Brushes.Red;  // 这个会覆盖样式设置
        
        // 1. 动画值 (最高优先级)
        var animation = new ColorAnimation(Colors.Green, TimeSpan.FromSeconds(1));
        var storyboard = new Storyboard();
        Storyboard.SetTarget(animation, button);
        Storyboard.SetTargetProperty(animation, new PropertyPath("Background.Color"));
        storyboard.Children.Add(animation);
        storyboard.Begin();  // 动画会覆盖本地值
    }
}
```

### **清除属性值**

```csharp
// 清除本地值，回退到更低优先级的值
button.ClearValue(Button.BackgroundProperty);

// 获取属性的基础值（不包括动画）
var baseValue = button.GetValue(Button.BackgroundProperty);

// 获取属性的本地值
var localValue = button.ReadLocalValue(Button.BackgroundProperty);
if (localValue == DependencyProperty.UnsetValue)
{
    // 没有设置本地值
}
```

---

## 🔄 元数据与回调机制

### **PropertyMetadata 类型**

```csharp
// 1. 基本 PropertyMetadata
new PropertyMetadata(defaultValue, propertyChangedCallback)

// 2. FrameworkPropertyMetadata (WPF 特有)
new FrameworkPropertyMetadata(
    defaultValue,
    FrameworkPropertyMetadataOptions.AffectsRender | FrameworkPropertyMetadataOptions.Inherits,
    propertyChangedCallback,
    coerceValueCallback
)

// 3. UIPropertyMetadata (已过时，使用 FrameworkPropertyMetadata)
```

### **FrameworkPropertyMetadataOptions**

```csharp
public enum FrameworkPropertyMetadataOptions
{
    None = 0,
    AffectsMeasure = 1,          // 影响测量
    AffectsArrange = 2,          // 影响排列
    AffectsParentMeasure = 4,    // 影响父元素测量
    AffectsParentArrange = 8,    // 影响父元素排列
    AffectsRender = 16,          // 影响渲染
    Inherits = 32,               // 属性值继承
    OverridesInheritanceBehavior = 64,  // 重写继承行为
    NotDataBindable = 128,       // 不支持数据绑定
    BindsTwoWayByDefault = 256,  // 默认双向绑定
    Journal = 1024,              // 支持导航日志
    SubPropertiesDoNotAffectRender = 2048  // 子属性不影响渲染
}
```

### **完整的元数据示例**

```csharp
public class AdvancedControl : FrameworkElement
{
    public static readonly DependencyProperty SizeProperty =
        DependencyProperty.Register(
            "Size",
            typeof(double),
            typeof(AdvancedControl),
            new FrameworkPropertyMetadata(
                100.0,                                          // 默认值
                FrameworkPropertyMetadataOptions.AffectsMeasure | // 影响测量
                FrameworkPropertyMetadataOptions.AffectsRender,   // 影响渲染
                OnSizeChanged,                                  // 属性变化回调
                CoerceSize                                      // 值强制回调
            ),
            ValidateSize                                        // 值验证回调
        );
    
    public double Size
    {
        get => (double)GetValue(SizeProperty);
        set => SetValue(SizeProperty, value);
    }
    
    private static bool ValidateSize(object value)
    {
        var size = (double)value;
        return size >= 0 && size <= 1000;
    }
    
    private static object CoerceSize(DependencyObject d, object baseValue)
    {
        var size = (double)baseValue;
        return Math.Max(0, Math.Min(1000, size));
    }
    
    private static void OnSizeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        var control = (AdvancedControl)d;
        control.OnSizeChanged((double)e.OldValue, (double)e.NewValue);
    }
    
    protected virtual void OnSizeChanged(double oldSize, double newSize)
    {
        // 触发重新测量和渲染
        InvalidateMeasure();
        InvalidateVisual();
    }
}
```

---

## 🎯 Snackbar 控件依赖属性实战

### **自定义 Snackbar 控件的完整实现**

通过 Snackbar 控件的实现，我们可以看到依赖属性在实际项目中的应用：

```csharp
/// <summary>
/// 自定义 Snackbar 控件 - 展示依赖属性的完整实现
/// </summary>
public class CustomSnackbar : Control
{
    static CustomSnackbar()
    {
        DefaultStyleKeyProperty.OverrideMetadata(typeof(CustomSnackbar),
            new FrameworkPropertyMetadata(typeof(CustomSnackbar)));
    }

    #region 基础依赖属性

    // Title 属性 - 带验证和强制
    public static readonly DependencyProperty TitleProperty =
        DependencyProperty.Register(
            nameof(Title),
            typeof(string),
            typeof(CustomSnackbar),
            new FrameworkPropertyMetadata(
                string.Empty,                    // 默认值
                FrameworkPropertyMetadataOptions.AffectsRender |
                FrameworkPropertyMetadataOptions.AffectsMeasure,
                OnTitleChanged,                  // 属性变化回调
                CoerceTitleValue                 // 值强制回调
            ),
            ValidateTitleValue                   // 值验证回调
        );

    public string Title
    {
        get => (string)GetValue(TitleProperty);
        set => SetValue(TitleProperty, value);
    }

    // AutoHideDelay 属性 - 带范围限制
    public static readonly DependencyProperty AutoHideDelayProperty =
        DependencyProperty.Register(
            nameof(AutoHideDelay),
            typeof(int),
            typeof(CustomSnackbar),
            new PropertyMetadata(
                3000,                           // 默认 3 秒
                OnAutoHideDelayChanged,
                CoerceAutoHideDelay
            ),
            ValidateAutoHideDelay);

    public int AutoHideDelay
    {
        get => (int)GetValue(AutoHideDelayProperty);
        set => SetValue(AutoHideDelayProperty, value);
    }

    // IsShowing 只读属性
    private static readonly DependencyPropertyKey IsShowingPropertyKey =
        DependencyProperty.RegisterReadOnly(
            nameof(IsShowing),
            typeof(bool),
            typeof(CustomSnackbar),
            new PropertyMetadata(false, OnIsShowingChanged));

    public static readonly DependencyProperty IsShowingProperty =
        IsShowingPropertyKey.DependencyProperty;

    public bool IsShowing
    {
        get => (bool)GetValue(IsShowingProperty);
        private set => SetValue(IsShowingPropertyKey, value);
    }

    #endregion

    #region 附加属性

    // Position 附加属性 - 用于容器定位
    public static readonly DependencyProperty PositionProperty =
        DependencyProperty.RegisterAttached(
            "Position",
            typeof(SnackbarPosition),
            typeof(CustomSnackbar),
            new PropertyMetadata(SnackbarPosition.Bottom, OnPositionChanged));

    public static SnackbarPosition GetPosition(DependencyObject obj) =>
        (SnackbarPosition)obj.GetValue(PositionProperty);

    public static void SetPosition(DependencyObject obj, SnackbarPosition value) =>
        obj.SetValue(PositionProperty, value);

    #endregion

    #region 属性变化回调

    private static void OnTitleChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is CustomSnackbar snackbar)
        {
            snackbar.OnTitleChanged((string)e.OldValue, (string)e.NewValue);
        }
    }

    protected virtual void OnTitleChanged(string oldTitle, string newTitle)
    {
        // 触发标题变化事件
        TitleChanged?.Invoke(this, new PropertyChangedEventArgs<string>(oldTitle, newTitle));

        // 更新可访问性信息
        AutomationProperties.SetName(this, newTitle);
    }

    private static void OnAutoHideDelayChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is CustomSnackbar snackbar)
        {
            snackbar.RestartAutoHideTimer();
        }
    }

    #endregion

    #region 值强制和验证

    private static object CoerceTitleValue(DependencyObject d, object baseValue)
    {
        var title = (string)baseValue;

        // 限制标题长度
        if (title?.Length > 50)
        {
            return title.Substring(0, 47) + "...";
        }

        return title ?? string.Empty;
    }

    private static bool ValidateTitleValue(object value)
    {
        return value is string;
    }

    private static object CoerceAutoHideDelay(DependencyObject d, object baseValue)
    {
        var delay = (int)baseValue;
        var snackbar = (CustomSnackbar)d;

        // 根据消息长度调整延迟时间
        var messageLength = snackbar.Message?.Length ?? 0;
        var minDelay = messageLength > 50 ? 5000 : 1000;
        var maxDelay = 30000;

        return Math.Max(minDelay, Math.Min(maxDelay, delay));
    }

    private static bool ValidateAutoHideDelay(object value)
    {
        return value is int delay && delay > 0;
    }

    #endregion

    #region 事件定义

    public event EventHandler<PropertyChangedEventArgs<string>>? TitleChanged;
    public event EventHandler? AutoHideCompleted;

    #endregion

    private void RestartAutoHideTimer()
    {
        // 重启自动隐藏定时器实现
    }
}

// 辅助类和枚举
public class PropertyChangedEventArgs<T> : EventArgs
{
    public T OldValue { get; }
    public T NewValue { get; }

    public PropertyChangedEventArgs(T oldValue, T newValue)
    {
        OldValue = oldValue;
        NewValue = newValue;
    }
}

public enum SnackbarPosition
{
    Top, Bottom, Left, Right, Center
}
```

### **使用 YDependencyPropertyHelper 简化开发**

```csharp
public class ModernSnackbar : Control
{
    // 使用帮助类简化依赖属性创建
    public static readonly DependencyProperty TitleProperty =
        YDependencyPropertyHelper.Create<string, ModernSnackbar>()
            .WithName(nameof(Title))
            .WithDefaultValue(string.Empty)
            .WithPropertyChanged(OnTitleChanged)
            .WithCoerceValue(CoerceTitleValue)
            .WithValidateValue(ValidateTitleValue)
            .WithFrameworkOptions(FrameworkPropertyMetadataOptions.AffectsRender)
            .Build();

    public string Title
    {
        get => (string)GetValue(TitleProperty);
        set => SetValue(TitleProperty, value);
    }
}
```

### **Snackbar 控件依赖属性的关键要点**

1. **合理使用元数据选项** - 根据属性特性选择合适的 FrameworkPropertyMetadataOptions
2. **实现完整的回调机制** - 属性变化、值强制、值验证三位一体
3. **考虑性能影响** - 避免在回调中执行耗时操作
4. **提供丰富的事件** - 让使用者能够监听属性变化
5. **支持可访问性** - 更新 AutomationProperties 相关属性
6. **使用帮助类简化开发** - YDependencyPropertyHelper 提供流畅的 API

---

*📅 最后更新：2025年1月*
*🔗 参考资料：Microsoft Learn WPF 文档*
