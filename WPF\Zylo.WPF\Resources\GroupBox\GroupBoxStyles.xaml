<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml">

    <!-- GroupBox 基础样式 -->
    <Style x:Key="GroupBoxBaseStyle" TargetType="GroupBox">
        <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="Margin" Value="4"/>

        <!-- 添加鼠标悬停效果 -->
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- GroupBox 标准样式 -->
    <Style x:Key="GroupBoxStyle" TargetType="GroupBox" BasedOn="{StaticResource GroupBoxBaseStyle}">
        <!-- 标准样式的特定设置 -->
        <Setter Property="Padding" Value="12"/>
    </Style>

    <!-- GroupBox 小型样式 -->
    <Style x:Key="SmallGroupBoxStyle" TargetType="GroupBox" BasedOn="{StaticResource GroupBoxBaseStyle}">
        <Setter Property="Padding" Value="6"/>
        <Setter Property="BorderThickness" Value="1"/>
    </Style>

    <!-- GroupBox 大型样式 -->
    <Style x:Key="LargeGroupBoxStyle" TargetType="GroupBox" BasedOn="{StaticResource GroupBoxBaseStyle}">
        <Setter Property="Padding" Value="16"/>
        <Setter Property="BorderThickness" Value="2"/>
    </Style>

    <!-- GroupBox 现代化样式 -->
    <Style x:Key="ModernGroupBoxStyle" TargetType="GroupBox" BasedOn="{StaticResource GroupBoxBaseStyle}">
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Background" Value="{DynamicResource CardBackgroundFillColorDefaultBrush}"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" 
                                  Opacity="0.1" 
                                  ShadowDepth="4" 
                                  BlurRadius="8"/>
            </Setter.Value>
        </Setter>
        
        <!-- 悬停效果 -->
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="Black" 
                                          Opacity="0.15" 
                                          ShadowDepth="6" 
                                          BlurRadius="12"/>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- GroupBox 强调样式 -->
    <Style x:Key="AccentGroupBoxStyle" TargetType="GroupBox" BasedOn="{StaticResource GroupBoxBaseStyle}">
        <Setter Property="Background" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource AccentStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="2"/>
    </Style>

    <!-- GroupBox 卡片样式 -->
    <Style x:Key="CardGroupBoxStyle" TargetType="GroupBox">
        <Setter Property="Background" Value="{DynamicResource CardBackgroundFillColorDefaultBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource CardStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" 
                                  Opacity="0.08" 
                                  ShadowDepth="2" 
                                  BlurRadius="8"/>
            </Setter.Value>
        </Setter>
        
        <!-- 悬停效果 -->
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="Black" 
                                          Opacity="0.15" 
                                          ShadowDepth="4" 
                                          BlurRadius="12"/>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- GroupBox 危险样式 -->
    <Style x:Key="DangerGroupBoxStyle" TargetType="GroupBox" BasedOn="{StaticResource GroupBoxBaseStyle}">
        <Setter Property="Background" Value="#FFFFE6E6"/>
        <Setter Property="BorderBrush" Value="#FFFF4444"/>
        <Setter Property="BorderThickness" Value="2"/>
    </Style>

    <!-- GroupBox 成功样式 -->
    <Style x:Key="SuccessGroupBoxStyle" TargetType="GroupBox" BasedOn="{StaticResource GroupBoxBaseStyle}">
        <Setter Property="Background" Value="#FFE6FFE6"/>
        <Setter Property="BorderBrush" Value="#FF44AA44"/>
        <Setter Property="BorderThickness" Value="2"/>
    </Style>

    <!-- GroupBox 警告样式 -->
    <Style x:Key="WarningGroupBoxStyle" TargetType="GroupBox" BasedOn="{StaticResource GroupBoxBaseStyle}">
        <Setter Property="Background" Value="#FFFFF0E6"/>
        <Setter Property="BorderBrush" Value="#FFFF8800"/>
        <Setter Property="BorderThickness" Value="2"/>
    </Style>

    <!-- GroupBox 信息样式 -->
    <Style x:Key="InfoGroupBoxStyle" TargetType="GroupBox" BasedOn="{StaticResource GroupBoxBaseStyle}">
        <Setter Property="Background" Value="#FFE6F3FF"/>
        <Setter Property="BorderBrush" Value="#FF0088FF"/>
        <Setter Property="BorderThickness" Value="2"/>
    </Style>

    <!-- GroupBox 透明样式 -->
    <Style x:Key="TransparentGroupBoxStyle" TargetType="GroupBox" BasedOn="{StaticResource GroupBoxBaseStyle}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
    </Style>

</ResourceDictionary>
