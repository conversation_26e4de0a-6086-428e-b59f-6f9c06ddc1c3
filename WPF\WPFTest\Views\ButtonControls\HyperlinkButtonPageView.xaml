<UserControl x:Class="WPFTest.Views.ButtonControls.HyperlinkButtonPageView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:WPFTest.Views.ButtonControls"
             xmlns:buttonControls="clr-namespace:WPFTest.ViewModels.ButtonControls"
             xmlns:mvvm="http://prismlibrary.com/"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:codeExample="clr-namespace:Zylo.WPF.Controls.CodeExample;assembly=Zylo.WPF"
             mc:Ignorable="d"
             d:DataContext="{d:DesignInstance buttonControls:HyperlinkButtonPageViewModel}"
             mvvm:ViewModelLocator.AutoWireViewModel="True"
             d:DesignHeight="2000" d:DesignWidth="1200">

    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 标题区域 -->
            <StackPanel Grid.Row="0" Margin="0,0,0,20">
                <TextBlock Text="🔗 HyperlinkButton 超链接按钮示例"
                           FontSize="32"
                           FontWeight="Bold"
                           Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                           Margin="0,0,0,8"/>
                <TextBlock Text="展示各种 HyperlinkButton 超链接按钮的样式和功能"
                           FontSize="14"
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                           Margin="0,0,0,16"/>

                <!-- 状态信息 -->
                <ui:Card Padding="16" Margin="0,0,0,16">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="📊 当前状态" FontWeight="Bold" FontSize="14" Margin="0,0,0,8"/>
                            <TextBlock Text="{Binding StatusMessage}" 
                                       FontSize="12" 
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                            <TextBlock Text="{Binding InteractionCount, StringFormat='点击次数: {0}'}" 
                                       FontSize="11" 
                                       Foreground="{DynamicResource TextFillColorTertiaryBrush}"
                                       Margin="0,4,0,0"/>
                        </StackPanel>
                        
                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <ui:Button Content="重置计数" 
                                       Appearance="Transparent"
                                       Command="{Binding ResetCountCommand}"
                                       FontSize="11"
                                       Padding="8,4"/>
                        </StackPanel>
                    </Grid>
                </ui:Card>
            </StackPanel>

            <!-- 主要内容区域 -->
            <StackPanel Grid.Row="1">

                <!-- 基础 HyperlinkButton 示例 -->
                <ui:CardExpander Header="🎯 基础 HyperlinkButton" IsExpanded="True" Margin="0,0,0,24">
                    <StackPanel>
                        <TextBlock Text="基本的超链接按钮，支持导航和点击操作" 
                                   FontSize="12" 
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   Margin="0,0,0,16"/>
                        
                        <!-- 示例区域 -->
                        <ui:Card Padding="20" Margin="0,0,0,16">
                            <StackPanel>
                                <TextBlock Text="基础超链接按钮示例：" FontWeight="Medium" Margin="0,0,0,16"/>

                                <WrapPanel Orientation="Horizontal">
                                    <ui:HyperlinkButton Content="基础超链接"
                                                      Command="{Binding HandleInteractionCommand}"
                                                      CommandParameter="基础超链接"
                                                      Margin="0,0,16,8"/>

                                    <ui:HyperlinkButton Content="访问官网"
                                                      Command="{Binding HandleInteractionCommand}"
                                                      CommandParameter="访问官网"
                                                      Margin="0,0,16,8"/>

                                    <ui:HyperlinkButton Content="查看文档"
                                                      Command="{Binding HandleInteractionCommand}"
                                                      CommandParameter="查看文档"
                                                      Margin="0,0,16,8"/>

                                    <ui:HyperlinkButton Content="禁用状态"
                                                      IsEnabled="False"
                                                      Margin="0,0,16,8"/>
                                </WrapPanel>
                            </StackPanel>
                        </ui:Card>

                        <!-- 代码示例 -->
                        <codeExample:CodeExampleControl Title="基础 HyperlinkButton 代码示例"
                                                      Language="C#"
                                                      Description="展示如何使用基础的 HyperlinkButton 超链接按钮"
                                                      ShowTabs="True"
                                                      XamlCode="{Binding BasicHyperlinkButtonXamlExample}"
                                                      CSharpCode="{Binding BasicHyperlinkButtonCSharpExample}"/>
                    </StackPanel>
                </ui:CardExpander>

                <!-- 带图标的 HyperlinkButton -->
                <ui:CardExpander Header="🎨 带图标的 HyperlinkButton" IsExpanded="True" Margin="0,0,0,24">
                    <StackPanel>
                        <TextBlock Text="带有图标的超链接按钮，提供更好的视觉效果" 
                                   FontSize="12" 
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   Margin="0,0,0,16"/>
                        
                        <!-- 示例区域 -->
                        <ui:Card Padding="20" Margin="0,0,0,16">
                            <StackPanel>
                                <TextBlock Text="带图标超链接按钮示例：" FontWeight="Medium" Margin="0,0,0,16"/>

                                <WrapPanel Orientation="Horizontal">
                                    <ui:HyperlinkButton Command="{Binding HandleInteractionCommand}"
                                                      CommandParameter="外部链接"
                                                      Margin="0,0,16,8">
                                        <StackPanel Orientation="Horizontal">
                                            <ui:SymbolIcon Symbol="Link24" FontSize="16" Margin="0,0,4,0"/>
                                            <TextBlock Text="外部链接"/>
                                        </StackPanel>
                                    </ui:HyperlinkButton>

                                    <ui:HyperlinkButton Command="{Binding HandleInteractionCommand}"
                                                      CommandParameter="下载文件"
                                                      Margin="0,0,16,8">
                                        <StackPanel Orientation="Horizontal">
                                            <ui:SymbolIcon Symbol="ArrowDownload24" FontSize="16" Margin="0,0,4,0"/>
                                            <TextBlock Text="下载文件"/>
                                        </StackPanel>
                                    </ui:HyperlinkButton>

                                    <ui:HyperlinkButton Command="{Binding HandleInteractionCommand}"
                                                      CommandParameter="邮件联系"
                                                      Margin="0,0,16,8">
                                        <StackPanel Orientation="Horizontal">
                                            <ui:SymbolIcon Symbol="Mail24" FontSize="16" Margin="0,0,4,0"/>
                                            <TextBlock Text="邮件联系"/>
                                        </StackPanel>
                                    </ui:HyperlinkButton>

                                    <ui:HyperlinkButton Command="{Binding HandleInteractionCommand}"
                                                      CommandParameter="设置选项"
                                                      Margin="0,0,16,8">
                                        <StackPanel Orientation="Horizontal">
                                            <ui:SymbolIcon Symbol="Settings24" FontSize="16" Margin="0,0,4,0"/>
                                            <TextBlock Text="设置选项"/>
                                        </StackPanel>
                                    </ui:HyperlinkButton>
                                </WrapPanel>
                            </StackPanel>
                        </ui:Card>

                        <!-- 代码示例 -->
                        <codeExample:CodeExampleControl Title="带图标 HyperlinkButton 代码示例"
                                                      Language="C#"
                                                      Description="展示如何创建带有图标的超链接按钮"
                                                      ShowTabs="True"
                                                      XamlCode="{Binding IconHyperlinkButtonXamlExample}"
                                                      CSharpCode="{Binding IconHyperlinkButtonCSharpExample}"/>
                    </StackPanel>
                </ui:CardExpander>

                <!-- 不同样式的 HyperlinkButton -->
                <ui:CardExpander Header="⚡ 高级功能" IsExpanded="True" Margin="0,0,0,24">
                    <StackPanel>
                        <TextBlock Text="展示 HyperlinkButton 的高级功能和样式变体"
                                   FontSize="12"
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   Margin="0,0,0,16"/>

                        <!-- 示例区域 -->
                        <ui:Card Padding="20" Margin="0,0,0,16">
                            <StackPanel>
                                <TextBlock Text="不同样式的超链接按钮：" FontWeight="Medium" Margin="0,0,0,16"/>

                                <StackPanel>
                                    <!-- 不同字体大小 -->
                                    <GroupBox Header="不同字体大小" Padding="12" Margin="0,0,0,12">
                                        <WrapPanel Orientation="Horizontal">
                                            <ui:HyperlinkButton Content="小字体链接"
                                                              FontSize="10"
                                                              Command="{Binding HandleInteractionCommand}"
                                                              CommandParameter="小字体"
                                                              Margin="0,0,16,4"/>

                                            <ui:HyperlinkButton Content="标准字体链接"
                                                              FontSize="14"
                                                              Command="{Binding HandleInteractionCommand}"
                                                              CommandParameter="标准字体"
                                                              Margin="0,0,16,4"/>

                                            <ui:HyperlinkButton Content="大字体链接"
                                                              FontSize="18"
                                                              Command="{Binding HandleInteractionCommand}"
                                                              CommandParameter="大字体"
                                                              Margin="0,0,16,4"/>
                                        </WrapPanel>
                                    </GroupBox>

                                    <!-- 不同颜色 -->
                                    <GroupBox Header="不同颜色" Padding="12" Margin="0,0,0,12">
                                        <WrapPanel Orientation="Horizontal">
                                            <ui:HyperlinkButton Content="蓝色链接"
                                                              Foreground="Blue"
                                                              Command="{Binding HandleInteractionCommand}"
                                                              CommandParameter="蓝色链接"
                                                              Margin="0,0,16,4"/>

                                            <ui:HyperlinkButton Content="绿色链接"
                                                              Foreground="Green"
                                                              Command="{Binding HandleInteractionCommand}"
                                                              CommandParameter="绿色链接"
                                                              Margin="0,0,16,4"/>

                                            <ui:HyperlinkButton Content="红色链接"
                                                              Foreground="Red"
                                                              Command="{Binding HandleInteractionCommand}"
                                                              CommandParameter="红色链接"
                                                              Margin="0,0,16,4"/>
                                        </WrapPanel>
                                    </GroupBox>

                                    <!-- 带下划线 -->
                                    <GroupBox Header="文本装饰" Padding="12">
                                        <WrapPanel Orientation="Horizontal">
                                            <ui:HyperlinkButton Command="{Binding HandleInteractionCommand}"
                                                              CommandParameter="下划线链接"
                                                              Margin="0,0,16,4">
                                                <TextBlock Text="下划线链接" TextDecorations="Underline"/>
                                            </ui:HyperlinkButton>

                                            <ui:HyperlinkButton Command="{Binding HandleInteractionCommand}"
                                                              CommandParameter="粗体链接"
                                                              Margin="0,0,16,4">
                                                <TextBlock Text="粗体链接" FontWeight="Bold"/>
                                            </ui:HyperlinkButton>

                                            <ui:HyperlinkButton Command="{Binding HandleInteractionCommand}"
                                                              CommandParameter="斜体链接"
                                                              Margin="0,0,16,4">
                                                <TextBlock Text="斜体链接" FontStyle="Italic"/>
                                            </ui:HyperlinkButton>
                                        </WrapPanel>
                                    </GroupBox>
                                </StackPanel>
                            </StackPanel>
                        </ui:Card>

                        <!-- 代码示例 -->
                        <codeExample:CodeExampleControl Title="高级功能 HyperlinkButton 代码示例"
                                                      Language="C#"
                                                      Description="展示 HyperlinkButton 的高级功能和样式定制"
                                                      ShowTabs="True"
                                                      XamlCode="{Binding AdvancedHyperlinkButtonXamlExample}"
                                                      CSharpCode="{Binding AdvancedHyperlinkButtonCSharpExample}"/>
                    </StackPanel>
                </ui:CardExpander>

                <!-- 实际应用场景 -->
                <ui:CardExpander Header="💼 实际应用场景" IsExpanded="True" Margin="0,0,0,24">
                    <StackPanel>
                        <TextBlock Text="模拟真实应用中的超链接按钮使用场景"
                                   FontSize="12"
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   Margin="0,0,0,16"/>

                        <!-- 示例区域 -->
                        <ui:Card Padding="20" Margin="0,0,0,16">
                            <StackPanel>
                                <TextBlock Text="应用场景示例：" FontWeight="Medium" Margin="0,0,0,16"/>

                                <StackPanel>
                                    <!-- 导航链接 -->
                                    <GroupBox Header="🧭 导航链接" Padding="12" Margin="0,0,0,12">
                                        <WrapPanel Orientation="Horizontal">
                                            <ui:HyperlinkButton Command="{Binding HandleInteractionCommand}"
                                                              CommandParameter="首页"
                                                              Margin="0,0,16,4">
                                                <StackPanel Orientation="Horizontal">
                                                    <ui:SymbolIcon Symbol="Home24" FontSize="14" Margin="0,0,4,0"/>
                                                    <TextBlock Text="首页"/>
                                                </StackPanel>
                                            </ui:HyperlinkButton>

                                            <ui:HyperlinkButton Command="{Binding HandleInteractionCommand}"
                                                              CommandParameter="关于我们"
                                                              Margin="0,0,16,4">
                                                <StackPanel Orientation="Horizontal">
                                                    <ui:SymbolIcon Symbol="Info24" FontSize="14" Margin="0,0,4,0"/>
                                                    <TextBlock Text="关于我们"/>
                                                </StackPanel>
                                            </ui:HyperlinkButton>

                                            <ui:HyperlinkButton Command="{Binding HandleInteractionCommand}"
                                                              CommandParameter="联系我们"
                                                              Margin="0,0,16,4">
                                                <StackPanel Orientation="Horizontal">
                                                    <ui:SymbolIcon Symbol="ContactCard24" FontSize="14" Margin="0,0,4,0"/>
                                                    <TextBlock Text="联系我们"/>
                                                </StackPanel>
                                            </ui:HyperlinkButton>
                                        </WrapPanel>
                                    </GroupBox>

                                    <!-- 操作链接 -->
                                    <GroupBox Header="⚡ 操作链接" Padding="12">
                                        <WrapPanel Orientation="Horizontal">
                                            <ui:HyperlinkButton Command="{Binding HandleInteractionCommand}"
                                                              CommandParameter="编辑资料"
                                                              Margin="0,0,16,4">
                                                <StackPanel Orientation="Horizontal">
                                                    <ui:SymbolIcon Symbol="Edit24" FontSize="14" Margin="0,0,4,0"/>
                                                    <TextBlock Text="编辑资料"/>
                                                </StackPanel>
                                            </ui:HyperlinkButton>

                                            <ui:HyperlinkButton Command="{Binding HandleInteractionCommand}"
                                                              CommandParameter="查看详情"
                                                              Margin="0,0,16,4">
                                                <StackPanel Orientation="Horizontal">
                                                    <ui:SymbolIcon Symbol="Eye24" FontSize="14" Margin="0,0,4,0"/>
                                                    <TextBlock Text="查看详情"/>
                                                </StackPanel>
                                            </ui:HyperlinkButton>

                                            <ui:HyperlinkButton Command="{Binding HandleInteractionCommand}"
                                                              CommandParameter="删除项目"
                                                              Foreground="Red"
                                                              Margin="0,0,16,4">
                                                <StackPanel Orientation="Horizontal">
                                                    <ui:SymbolIcon Symbol="Delete24" FontSize="14" Margin="0,0,4,0"/>
                                                    <TextBlock Text="删除项目"/>
                                                </StackPanel>
                                            </ui:HyperlinkButton>
                                        </WrapPanel>
                                    </GroupBox>
                                </StackPanel>
                            </StackPanel>
                        </ui:Card>

                        <!-- 代码示例 -->
                        <codeExample:CodeExampleControl Title="实际应用场景 MVVM 示例"
                                                      Language="C#"
                                                      Description="展示在真实应用中如何使用 MVVM 模式管理超链接按钮"
                                                      ShowTabs="True"
                                                      XamlCode="{Binding ApplicationScenarioXamlExample}"
                                                      CSharpCode="{Binding ApplicationScenarioCSharpExample}"/>
                    </StackPanel>
                </ui:CardExpander>

            </StackPanel>

            <!-- 底部信息 -->
            <ui:Card Grid.Row="2" Padding="16" Margin="0,20,0,0">
                <StackPanel>
                    <TextBlock Text="💡 使用提示" FontWeight="Bold" FontSize="14" Margin="0,0,0,8"/>
                    <TextBlock TextWrapping="Wrap" FontSize="12" Foreground="{DynamicResource TextFillColorSecondaryBrush}">
                        • HyperlinkButton 适用于导航和外部链接操作<LineBreak/>
                        • 支持命令绑定和参数传递<LineBreak/>
                        • 可以包含图标和文本的组合内容<LineBreak/>
                        • 具有悬停和点击的视觉反馈效果<LineBreak/>
                        • 建议为重要链接添加图标以提高可识别性
                    </TextBlock>
                </StackPanel>
            </ui:Card>
        </Grid>
    </ScrollViewer>
</UserControl>
