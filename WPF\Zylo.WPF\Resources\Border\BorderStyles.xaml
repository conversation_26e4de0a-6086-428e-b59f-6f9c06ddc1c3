<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml">

    <!-- Border 基础样式 -->
    <Style x:Key="BorderBaseStyle" TargetType="Border">
        <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="4"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="Margin" Value="4"/>

        <!-- 添加鼠标悬停效果 -->
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Border 标准样式 -->
    <Style x:Key="BorderStyle" TargetType="Border" BasedOn="{StaticResource BorderBaseStyle}">
        <!-- 标准样式的特定设置 -->
        <Setter Property="CornerRadius" Value="6"/>
        <Setter Property="Padding" Value="12"/>
    </Style>

    <!-- Border 小型样式 -->
    <Style x:Key="SmallBorderStyle" TargetType="Border" BasedOn="{StaticResource BorderBaseStyle}">
        <Setter Property="Padding" Value="6"/>
        <Setter Property="CornerRadius" Value="3"/>
        <Setter Property="BorderThickness" Value="1"/>
    </Style>

    <!-- Border 大型样式 -->
    <Style x:Key="LargeBorderStyle" TargetType="Border" BasedOn="{StaticResource BorderBaseStyle}">
        <Setter Property="Padding" Value="16"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="BorderThickness" Value="2"/>
    </Style>

    <!-- Border 透明样式 -->
    <Style x:Key="TransparentBorderStyle" TargetType="Border" BasedOn="{StaticResource BorderBaseStyle}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
    </Style>

    <!-- Border 现代化样式 -->
    <Style x:Key="ModernBorderStyle" TargetType="Border" BasedOn="{StaticResource BorderBaseStyle}">
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Background" Value="{DynamicResource CardBackgroundFillColorDefaultBrush}"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" 
                                  Opacity="0.1" 
                                  ShadowDepth="4" 
                                  BlurRadius="8"/>
            </Setter.Value>
        </Setter>
        
        <!-- 悬停效果 -->
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="Black" 
                                          Opacity="0.15" 
                                          ShadowDepth="6" 
                                          BlurRadius="12"/>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Border 圆角样式 -->
    <Style x:Key="RoundedBorderStyle" TargetType="Border" BasedOn="{StaticResource BorderBaseStyle}">
        <Setter Property="CornerRadius" Value="16"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="Background" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource AccentStrokeColorDefaultBrush}"/>
    </Style>

    <!-- Border 强调样式 -->
    <Style x:Key="AccentBorderStyle" TargetType="Border" BasedOn="{StaticResource BorderBaseStyle}">
        <Setter Property="Background" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource AccentStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="CornerRadius" Value="6"/>
    </Style>

    <!-- Border 卡片样式 -->
    <Style x:Key="CardBorderStyle" TargetType="Border">
        <Setter Property="Background" Value="{DynamicResource CardBackgroundFillColorDefaultBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource CardStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" 
                                  Opacity="0.08" 
                                  ShadowDepth="2" 
                                  BlurRadius="8"/>
            </Setter.Value>
        </Setter>
        
        <!-- 悬停效果 -->
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="Black" 
                                          Opacity="0.15" 
                                          ShadowDepth="4" 
                                          BlurRadius="12"/>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Border 危险样式 -->
    <Style x:Key="DangerBorderStyle" TargetType="Border" BasedOn="{StaticResource BorderBaseStyle}">
        <Setter Property="Background" Value="#FFFFE6E6"/>
        <Setter Property="BorderBrush" Value="#FFFF4444"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="CornerRadius" Value="4"/>
    </Style>

    <!-- Border 成功样式 -->
    <Style x:Key="SuccessBorderStyle" TargetType="Border" BasedOn="{StaticResource BorderBaseStyle}">
        <Setter Property="Background" Value="#FFE6FFE6"/>
        <Setter Property="BorderBrush" Value="#FF44AA44"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="CornerRadius" Value="4"/>
    </Style>

    <!-- Border 警告样式 -->
    <Style x:Key="WarningBorderStyle" TargetType="Border" BasedOn="{StaticResource BorderBaseStyle}">
        <Setter Property="Background" Value="#FFFFF0E6"/>
        <Setter Property="BorderBrush" Value="#FFFF8800"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="CornerRadius" Value="4"/>
    </Style>

    <!-- Border 信息样式 -->
    <Style x:Key="InfoBorderStyle" TargetType="Border" BasedOn="{StaticResource BorderBaseStyle}">
        <Setter Property="Background" Value="#FFE6F3FF"/>
        <Setter Property="BorderBrush" Value="#FF0088FF"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="CornerRadius" Value="4"/>
    </Style>

</ResourceDictionary>
