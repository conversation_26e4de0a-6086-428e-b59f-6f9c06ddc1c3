# DataGrid右键菜单功能说明

## 🎯 概述
为DwgManagerTabView的DataGrid添加了完整的右键菜单功能，提供丰富的文件操作选项，提升用户体验。

## 📋 菜单功能列表

### 📂 文件操作
1. **打开文件** - 使用默认程序打开选中的DWG文件
2. **重命名** - 重命名选中的文件
3. **复制文件** - 在当前目录创建文件副本
4. **删除文件** - 删除选中的文件（带确认对话框）

### 🖥️ 复制操作
5. **复制到桌面** - 将文件复制到桌面
6. **复制路径** - 复制文件完整路径到剪贴板

### ➕ 创建操作
7. **新建DWG文件** - 基于模板创建新的DWG文件
8. **基于模板创建** - 选择模板文件创建新文件

### 🔍 高级操作
9. **在资源管理器中显示** - 在Windows资源管理器中定位文件
10. **属性** - 显示文件详细属性信息

## 🎨 XAML实现

```xml
<!--  #region 右键菜单  -->
<DataGrid.ContextMenu>
    <ContextMenu>
        <!--  打开文件  -->
        <MenuItem Header="📂 打开文件" Command="{Binding OpenFileCommand}" 
                  CommandParameter="{Binding PlacementTarget.SelectedItem, RelativeSource={RelativeSource AncestorType=ContextMenu}}">
            <MenuItem.Icon>
                <ui:SymbolIcon Symbol="Open24" />
            </MenuItem.Icon>
        </MenuItem>

        <Separator />

        <!--  文件操作  -->
        <MenuItem Header="📝 重命名" Command="{Binding RenameFileCommand}" 
                  CommandParameter="{Binding PlacementTarget.SelectedItem, RelativeSource={RelativeSource AncestorType=ContextMenu}}">
            <MenuItem.Icon>
                <ui:SymbolIcon Symbol="Rename24" />
            </MenuItem.Icon>
        </MenuItem>

        <!-- 更多菜单项... -->
    </ContextMenu>
</DataGrid.ContextMenu>
<!--  #endregion  -->
```

## 🔧 ViewModel命令实现

### 基础文件操作命令

```csharp
#region 右键菜单命令

/// <summary>
/// 打开文件命令
/// </summary>
[RelayCommand]
private void OpenFile(DwgFileModel? fileModel)
{
    // 使用拖拽服务打开文件
    var success = _dragService.TestFileOpen(fileModel);
    // 更新状态消息
}

/// <summary>
/// 重命名文件命令
/// </summary>
[RelayCommand]
private async Task RenameFileAsync(DwgFileModel? fileModel)
{
    // 显示重命名对话框
    var newName = await ShowRenameDialogAsync(fileModel.FileName);
    // 执行文件重命名
    File.Move(fileModel.FullPath, newPath);
    // 刷新文件列表
}

/// <summary>
/// 复制文件命令
/// </summary>
[RelayCommand]
private async Task CopyFileAsync(DwgFileModel? fileModel)
{
    // 生成副本文件名
    var copyName = $"{fileName}_副本{extension}";
    // 执行文件复制
    await Task.Run(() => File.Copy(fileModel.FullPath, copyPath));
    // 刷新文件列表
}

/// <summary>
/// 删除文件命令
/// </summary>
[RelayCommand]
private async Task DeleteFileAsync(DwgFileModel? fileModel)
{
    // 显示确认对话框
    var result = await ShowDeleteConfirmationAsync(fileModel.FileName);
    // 执行文件删除
    await Task.Run(() => File.Delete(fileModel.FullPath));
    // 刷新文件列表
}

#endregion
```

### 高级操作命令

```csharp
/// <summary>
/// 复制到桌面命令
/// </summary>
[RelayCommand]
private async Task CopyToDesktopAsync(DwgFileModel? fileModel)
{
    var desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
    var targetPath = Path.Combine(desktopPath, fileModel.FileName);
    await Task.Run(() => File.Copy(fileModel.FullPath, targetPath));
}

/// <summary>
/// 新建DWG文件命令
/// </summary>
[RelayCommand]
private async Task CreateNewDwgAsync()
{
    // 获取模板文件路径
    var templatePath = Path.Combine(GetProjectRoot(), "Assets", "DWG", "Drawing1.dwg");
    // 生成新文件名
    var newFileName = await GenerateNewFileNameAsync(targetDirectory, "新建图纸", ".dwg");
    // 复制模板文件
    await Task.Run(() => File.Copy(templatePath, newFilePath));
}

/// <summary>
/// 在资源管理器中显示命令
/// </summary>
[RelayCommand]
private void ShowInExplorer(DwgFileModel? fileModel)
{
    Process.Start("explorer.exe", $"/select,\"{fileModel.FullPath}\"");
}
```

## 🛠️ 辅助方法

### 对话框方法
```csharp
#region 右键菜单辅助方法

/// <summary>
/// 显示重命名对话框
/// </summary>
private async Task<string?> ShowRenameDialogAsync(string currentName)
{
    return await Task.Run(() =>
    {
        var result = Microsoft.VisualBasic.Interaction.InputBox(
            "请输入新的文件名:",
            "重命名文件",
            currentName);
        return string.IsNullOrEmpty(result) ? null : result;
    });
}

/// <summary>
/// 显示删除确认对话框
/// </summary>
private async Task<bool> ShowDeleteConfirmationAsync(string fileName)
{
    return await Task.Run(() =>
    {
        var result = MessageBox.Show(
            $"确定要删除文件 '{fileName}' 吗？\n\n此操作不可撤销！",
            "确认删除",
            MessageBoxButton.YesNo,
            MessageBoxImage.Warning);
        return result == MessageBoxResult.Yes;
    });
}

#endregion
```

### 文件操作方法
```csharp
/// <summary>
/// 生成新文件名（避免重复）
/// </summary>
private async Task<string> GenerateNewFileNameAsync(string directory, string baseName, string extension)
{
    // 检查文件是否存在，如果存在则添加数字后缀
}

/// <summary>
/// 确保模板文件存在
/// </summary>
private void EnsureTemplateFileExists()
{
    // 确保Assets/DWG/Drawing1.dwg模板文件存在
    // 如果不存在则创建一个基础的DWG文件
}
```

## 🎮 使用方法

1. **右键点击** - 在DataGrid的任意文件行上右键点击
2. **选择操作** - 从弹出的上下文菜单中选择需要的操作
3. **确认操作** - 对于删除等危险操作，会显示确认对话框
4. **查看结果** - 操作结果会在状态栏显示

## ✨ 功能特点

### 🔒 安全性
- **删除确认** - 删除文件前显示确认对话框
- **文件名冲突处理** - 自动处理重名文件，添加数字后缀
- **错误处理** - 完善的异常处理和用户提示

### 🚀 便利性
- **智能命名** - 复制文件时自动生成合适的文件名
- **快速访问** - 一键复制到桌面、打开资源管理器
- **模板支持** - 基于模板快速创建新文件

### 📊 用户体验
- **图标支持** - 每个菜单项都有对应的图标
- **状态反馈** - 操作结果实时显示在状态栏
- **异步操作** - 文件操作异步执行，不阻塞UI

## 🔧 模板文件管理

### 默认模板
- **位置**: `Assets/DWG/Drawing1.dwg`
- **自动创建**: 如果模板文件不存在，系统会自动创建
- **用途**: 作为"新建DWG文件"功能的基础模板

### 自定义模板
- **选择模板**: "基于模板创建"功能允许用户选择任意DWG文件作为模板
- **模板目录**: 默认打开Assets/DWG目录供用户选择

## 🐛 故障排除

### 常见问题
1. **模板文件不存在** - 系统会自动创建基础模板文件
2. **文件被占用** - 操作前确保文件没有被其他程序打开
3. **权限不足** - 确保应用程序有文件操作权限

### 调试信息
- 所有操作都有详细的日志记录
- 错误信息会显示在状态栏
- 可以通过日志查看具体的错误原因

## 📝 更新日志

- **2025-01-27**: 初始实现完整的右键菜单功能
  - 添加10个常用文件操作功能
  - 实现模板文件管理
  - 完善错误处理和用户体验
