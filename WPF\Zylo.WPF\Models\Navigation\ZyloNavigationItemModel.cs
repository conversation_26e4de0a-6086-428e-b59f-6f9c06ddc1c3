using System.Collections.ObjectModel;
using Wpf.Ui.Controls;
using Zylo.WPF.Enums;

namespace Zylo.WPF.Models.Navigation;

/// <summary>
/// 导航项数据模型 - 支持多层级导航结构的现代化数据模型
/// </summary>
/// <remarks>
/// 核心特性：
/// • 基于 CommunityToolkit.Mvvm 的响应式属性
/// • 支持任意深度的父子层级关系
/// • 兼容多种图标系统（WPF-UI、Zylo、Emoji）
/// • 提供完整的静态帮助方法集合
/// • 完全支持 MVVM 数据绑定模式
///
/// 适用场景：
/// • TreeView 分层导航
/// • NavigationView 导航菜单
/// • 面包屑导航路径
/// • 侧边栏菜单系统
/// </remarks>
public partial class ZyloNavigationItemModel : ObservableObject
{
    #region 核心属性

    /// <summary>
    /// 导航项唯一编号
    /// </summary>
    [ObservableProperty]
    public partial string Number { get; set; } = string.Empty;

    /// <summary>
    /// 父项目编号（用于建立层级关系）
    /// </summary>
    [ObservableProperty]
    public partial string? ParentNumber { get; set; }

    /// <summary>
    /// 导航项显示名称
    /// </summary>
    [ObservableProperty]
    public partial string Name { get; set; } = string.Empty;

    /// <summary>
    /// 导航目标（页面/视图名称，空值表示分组项）
    /// </summary>
    [ObservableProperty]
    public partial string NavigationTarget { get; set; } = string.Empty;

    #endregion

    #region 图标属性

    /// <summary>
    /// WPF-UI 图标（优先级最高）
    /// </summary>
    [ObservableProperty]
    public partial SymbolRegular? WpfUiSymbol { get; set; }

    /// <summary>
    /// Zylo 自定义图标
    /// </summary>
    [ObservableProperty]
    public partial ZyloSymbol? ZyloSymbol { get; set; }

    /// <summary>
    /// Emoji 图标字符
    /// </summary>
    [ObservableProperty]
    public partial string? Emoji { get; set; }

    #endregion

    #region 状态属性

    /// <summary>
    /// 是否选中
    /// </summary>
    [ObservableProperty]
    public partial bool IsSelected { get; set; }

    /// <summary>
    /// 是否展开（仅对父项目有效）
    /// </summary>
    [ObservableProperty]
    public partial bool IsExpanded { get; set; }

    #endregion

    #region 层级关系

    /// <summary>
    /// 子项目集合
    /// </summary>
    [ObservableProperty]
    public partial ObservableCollection<ZyloNavigationItemModel> Children { get; set; } = new();

    /// <summary>
    /// 父项目引用（用于双向关系）
    /// </summary>
    public ZyloNavigationItemModel? Parent { get; set; }

    #endregion

    #region 构造函数

    /// <summary>
    /// 默认构造函数
    /// </summary>
    public ZyloNavigationItemModel()
    {
        Children = new ObservableCollection<ZyloNavigationItemModel>();
    }

    /// <summary>
    /// 创建导航项
    /// </summary>
    /// <param name="name">显示名称</param>
    public ZyloNavigationItemModel(string name) : this()
    {
        Name = name;
    }

    /// <summary>
    /// 创建导航项（最常用）
    /// </summary>
    /// <param name="number">编号</param>
    /// <param name="name">显示名称</param>
    /// <param name="navigationTarget">导航目标</param>
    public ZyloNavigationItemModel(string number, string name, string navigationTarget) : this(name)
    {
        Number = number;
        NavigationTarget = navigationTarget;
    }

    #endregion

    #region 静态帮助方法

    #region 核心查找方法

    /// <summary>
    /// 递归查找导航项（核心方法）
    /// </summary>
    /// <param name="collection">搜索集合</param>
    /// <param name="predicate">查找条件</param>
    /// <returns>找到的项目或 null</returns>
    public static ZyloNavigationItemModel? FindItemRecursively(
        ObservableCollection<ZyloNavigationItemModel> collection,
        Func<ZyloNavigationItemModel, bool> predicate)
    {
        foreach (var item in collection)
        {
            if (predicate(item))
                return item;

            if (item.Children != null)
            {
                var found = FindItemRecursively(item.Children, predicate);
                if (found != null)
                    return found;
            }
        }

        return null;
    }

    #endregion

    #region 便捷查找方法

    /// <summary>
    /// 按编号查找
    /// </summary>
    public static ZyloNavigationItemModel? FindByNumber(
        ObservableCollection<ZyloNavigationItemModel> collection, string number)
        => FindItemRecursively(collection, item => item.Number == number);

    /// <summary>
    /// 按名称查找
    /// </summary>
    public static ZyloNavigationItemModel? FindByName(
        ObservableCollection<ZyloNavigationItemModel> collection, string name)
        => FindItemRecursively(collection, item => item.Name == name);

    /// <summary>
    /// 按导航目标查找
    /// </summary>
    public static ZyloNavigationItemModel? FindByNavigationTarget(
        ObservableCollection<ZyloNavigationItemModel> collection, string navigationTarget)
        => FindItemRecursively(collection, item => item.NavigationTarget == navigationTarget);

    #endregion

    #region 高级查找方法

    /// <summary>
    /// 查找所有父项目（有子项目的项目）
    /// </summary>
    public static List<ZyloNavigationItemModel> FindAllParentItems(
        ObservableCollection<ZyloNavigationItemModel> collection)
    {
        var parentItems = new List<ZyloNavigationItemModel>();
        FindParentItemsRecursively(collection, parentItems);
        return parentItems;
    }

    private static void FindParentItemsRecursively(
        ObservableCollection<ZyloNavigationItemModel> collection,
        List<ZyloNavigationItemModel> parentItems)
    {
        foreach (var item in collection)
        {
            if (item.Children != null && item.Children.Count > 0)
            {
                parentItems.Add(item);
                FindParentItemsRecursively(item.Children, parentItems);
            }
        }
    }

    /// <summary>
    /// 获取项目的完整路径（从根到当前项目）
    /// </summary>
    public static string GetItemPath(
        ObservableCollection<ZyloNavigationItemModel> collection,
        ZyloNavigationItemModel targetItem,
        string separator = " > ")
    {
        var path = new List<string>();
        if (FindItemPath(collection, targetItem, path))
        {
            return string.Join(separator, path);
        }
        return string.Empty;
    }

    private static bool FindItemPath(
        ObservableCollection<ZyloNavigationItemModel> collection,
        ZyloNavigationItemModel targetItem,
        List<string> path)
    {
        foreach (var item in collection)
        {
            path.Add(item.Name);

            if (item == targetItem)
                return true;

            if (item.Children != null && FindItemPath(item.Children, targetItem, path))
                return true;

            path.RemoveAt(path.Count - 1);
        }
        return false;
    }

    #endregion

    #region 集合操作方法

    /// <summary>
    /// 递归删除项目 - 支持任意深度的嵌套结构
    /// </summary>
    /// <param name="collection">要搜索的集合</param>
    /// <param name="itemToRemove">要删除的项目</param>
    /// <param name="onItemRemoved">删除成功后的回调委托</param>
    /// <returns>是否删除成功</returns>
    public static bool RemoveItemRecursively(
        ObservableCollection<ZyloNavigationItemModel> collection,
        ZyloNavigationItemModel itemToRemove,
        Action<int, ObservableCollection<ZyloNavigationItemModel>>? onItemRemoved = null)
    {
        // 尝试从当前集合中删除
        if (collection.Contains(itemToRemove))
        {
            var index = collection.IndexOf(itemToRemove);
            collection.Remove(itemToRemove);
            onItemRemoved?.Invoke(index, collection);
            return true;
        }

        // 递归搜索所有子集合
        foreach (var item in collection)
        {
            if (item.Children != null && RemoveItemRecursively(item.Children, itemToRemove, onItemRemoved))
                return true;
        }

        return false;
    }

    #endregion

    #endregion
}

