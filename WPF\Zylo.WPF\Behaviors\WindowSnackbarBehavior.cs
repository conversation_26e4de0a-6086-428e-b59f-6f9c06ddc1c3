using System.Windows;
using Zylo.WPF.Controls;
using Zylo.WPF.Services;

namespace Zylo.WPF.Behaviors;

/// <summary>
/// 多窗口安全的 Snackbar 行为 - 自动注册到窗口级服务
/// </summary>
/// <remarks>
/// 🎯 设计目标：
/// - 自动将Snackbar注册到多窗口服务
/// - 窗口关闭时自动清理资源
/// - 支持MVVM模式的自动绑定
/// - 避免单例服务的冲突问题
/// 
/// 🔧 使用方式：
/// ```xml
/// <zylo:ZyloSnackbar behaviors:WindowSnackbarBehavior.AutoRegister="True"/>
/// ```
/// </remarks>
public static class WindowSnackbarBehavior
{
    #region AutoRegister 附加属性

    /// <summary>
    /// AutoRegister 附加属性 - 自动注册到窗口服务
    /// </summary>
    public static readonly DependencyProperty AutoRegisterProperty =
        DependencyProperty.RegisterAttached(
            "AutoRegister",
            typeof(bool),
            typeof(WindowSnackbarBehavior),
            new PropertyMetadata(false, OnAutoRegisterChanged)
        );

    /// <summary>
    /// 获取 AutoRegister
    /// </summary>
    public static bool GetAutoRegister(DependencyObject obj)
    {
        return (bool)obj.GetValue(AutoRegisterProperty);
    }

    /// <summary>
    /// 设置 AutoRegister
    /// </summary>
    public static void SetAutoRegister(DependencyObject obj, bool value)
    {
        obj.SetValue(AutoRegisterProperty, value);
    }

    /// <summary>
    /// AutoRegister 属性改变回调
    /// </summary>
    private static void OnAutoRegisterChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is ZyloSnackbar snackbar)
        {
            if ((bool)e.NewValue)
            {
                // 启用自动注册
                snackbar.Loaded += OnSnackbarLoaded;
                snackbar.Unloaded += OnSnackbarUnloaded;

                // 如果已经加载，立即注册
                if (snackbar.IsLoaded)
                {
                    RegisterSnackbarToWindow(snackbar);
                }
            }
            else
            {
                // 禁用自动注册
                snackbar.Loaded -= OnSnackbarLoaded;
                snackbar.Unloaded -= OnSnackbarUnloaded;
                UnregisterSnackbarFromWindow(snackbar);
            }
        }
    }

    #endregion

    #region 事件处理

    /// <summary>
    /// Snackbar 加载完成事件
    /// </summary>
    private static void OnSnackbarLoaded(object sender, RoutedEventArgs e)
    {
        if (sender is ZyloSnackbar snackbar)
        {
            RegisterSnackbarToWindow(snackbar);
        }
    }

    /// <summary>
    /// Snackbar 卸载事件
    /// </summary>
    private static void OnSnackbarUnloaded(object sender, RoutedEventArgs e)
    {
        if (sender is ZyloSnackbar snackbar)
        {
            UnregisterSnackbarFromWindow(snackbar);
        }
    }

    #endregion

    #region 注册管理

    /// <summary>
    /// 将Snackbar注册到窗口服务
    /// </summary>
    private static void RegisterSnackbarToWindow(ZyloSnackbar snackbar)
    {
        try
        {
            // 查找包含此Snackbar的窗口
            var window = Window.GetWindow(snackbar);
            if (window == null) return;

            // 获取窗口级Snackbar服务
            var windowService = GetWindowSnackbarService();
            if (windowService != null)
            {
                windowService.RegisterSnackbar(window, snackbar);
            }
            else
            {
                // 降级处理：使用传统的单例服务
                var legacyService = GetLegacySnackbarService();
                legacyService?.SetSnackbar(snackbar);
            }
        }
        catch
        {
            // 静默处理注册失败
        }
    }

    /// <summary>
    /// 从窗口服务注销Snackbar
    /// </summary>
    private static void UnregisterSnackbarFromWindow(ZyloSnackbar snackbar)
    {
        try
        {
            // 查找包含此Snackbar的窗口
            var window = Window.GetWindow(snackbar);
            if (window == null) return;

            // 获取窗口级Snackbar服务
            var windowService = GetWindowSnackbarService();
            windowService?.UnregisterSnackbar(window);
        }
        catch
        {
            // 静默处理注销失败
        }
    }

    #endregion

    #region 服务获取

    /// <summary>
    /// 获取窗口级Snackbar服务
    /// </summary>
    private static IWindowSnackbarService? GetWindowSnackbarService()
    {
        try
        {
            return ZyloContainer.Resolve<IWindowSnackbarService>();
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 获取传统的单例Snackbar服务（降级处理）
    /// </summary>
    private static ISnackbarService? GetLegacySnackbarService()
    {
        try
        {
            return ZyloContainer.Resolve<ISnackbarService>();
        }
        catch
        {
            return null;
        }
    }

    #endregion

    #region 兼容性支持

    /// <summary>
    /// 兼容性属性：支持旧的AutoBind行为
    /// </summary>
    public static readonly DependencyProperty AutoBindProperty =
        DependencyProperty.RegisterAttached(
            "AutoBind",
            typeof(bool),
            typeof(WindowSnackbarBehavior),
            new PropertyMetadata(false, OnAutoBindChanged)
        );

    /// <summary>
    /// 获取 AutoBind（兼容性）
    /// </summary>
    public static bool GetAutoBind(DependencyObject obj)
    {
        return (bool)obj.GetValue(AutoBindProperty);
    }

    /// <summary>
    /// 设置 AutoBind（兼容性）
    /// </summary>
    public static void SetAutoBind(DependencyObject obj, bool value)
    {
        obj.SetValue(AutoBindProperty, value);
    }

    /// <summary>
    /// AutoBind 属性改变回调（兼容性）
    /// </summary>
    private static void OnAutoBindChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        // 将AutoBind映射到AutoRegister
        SetAutoRegister(d, (bool)e.NewValue);
    }

    #endregion
}
