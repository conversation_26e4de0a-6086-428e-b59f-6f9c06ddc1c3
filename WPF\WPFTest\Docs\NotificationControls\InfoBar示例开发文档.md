# InfoBar 示例开发文档

## 🎯 概述

本文档详细介绍了 InfoBar 示例页面的开发过程，展示了如何结合 **CommunityToolkit.Mvvm** 现代化 MVVM 模式和 **CodeExampleControl** 开发文档控件，创建一个完整的 WPF-UI InfoBar 控件示例。

## 🏗️ 项目结构

```
WPF\WPFTest\
├── Views\NotificationControls\
│   ├── InfoBarPageView.xaml          # InfoBar 示例页面 XAML
│   └── InfoBarPageView.xaml.cs       # 页面代码后台
├── ViewModels\NotificationControls\
│   └── InfoBarPageViewModel.cs       # ViewModel (CommunityToolkit.Mvvm)
└── Docs\
    └── InfoBar示例开发文档.md        # 本文档
```

## 🚀 核心技术栈

### ✅ **CommunityToolkit.Mvvm 特性**
- `[ObservableProperty]` - 自动生成属性和通知
- `[RelayCommand]` - 自动生成命令
- `partial class` - 支持源代码生成器
- `async Task` - 异步命令支持

### ✅ **WPF-UI 控件**
- `InfoBar` - 主要演示控件
- `Card`, `CardExpander` - 布局容器
- `Button`, `ProgressBar` - 交互控件
- `SymbolIcon` - 图标系统

### ✅ **CodeExampleControl**
- 双选项卡模式 (XAML/C#)
- AvalonEdit 语法高亮
- 代码复制功能
- 展开收缩控制

## 📋 功能模块详解

### 1. **基础功能展示**

展示 InfoBar 的四种基本信息类型：

```csharp
// ViewModel 属性定义
[ObservableProperty]
private bool isInfoBarOpen = true;

[ObservableProperty]
private bool isSuccessBarOpen = true;

[ObservableProperty]
private bool isWarningBarOpen = true;

[ObservableProperty]
private bool isErrorBarOpen = true;

// 控制命令
[RelayCommand]
private void ShowInfoBar()
{
    IsInfoBarOpen = true;
    UpdateStatus("显示信息栏");
}

[RelayCommand]
private void HideAllBars()
{
    IsInfoBarOpen = false;
    IsSuccessBarOpen = false;
    IsWarningBarOpen = false;
    IsErrorBarOpen = false;
}
```

```xml
<!-- XAML 绑定 -->
<ui:InfoBar Title="信息提示"
            Message="这是一个信息类型的 InfoBar"
            Severity="Informational"
            IsOpen="{Binding IsInfoBarOpen}"
            IsClosable="True"/>
```

### 2. **高级功能展示**

#### **带动作按钮的 InfoBar**
```xml
<ui:InfoBar Title="更新可用"
            Message="发现新版本，包含重要更新"
            Severity="Informational"
            IsOpen="{Binding IsUpdateBarOpen}"
            IsClosable="True">
    <ui:InfoBar.ActionButton>
        <ui:Button Content="立即更新"
                   Command="{Binding UpdateCommand}"
                   Appearance="Primary"/>
    </ui:InfoBar.ActionButton>
</ui:InfoBar>
```

#### **带自定义内容的 InfoBar**
```xml
<ui:InfoBar Title="文件同步"
            Severity="Warning"
            IsOpen="{Binding IsSyncBarOpen}"
            IsClosable="True">
    <ui:InfoBar.Content>
        <StackPanel>
            <TextBlock Text="正在同步文件到云端..."/>
            <ui:ProgressBar Value="{Binding SyncProgress}" Maximum="100"/>
            <TextBlock Text="{Binding SyncProgressText}"/>
        </StackPanel>
    </ui:InfoBar.Content>
</ui:InfoBar>
```

#### **异步操作支持**
```csharp
[RelayCommand]
private async Task StartSyncAsync()
{
    try
    {
        _syncCancellationTokenSource = new CancellationTokenSource();
        IsSyncBarOpen = true;
        
        // 模拟同步进度
        for (int i = 0; i <= 100; i += 5)
        {
            if (_syncCancellationTokenSource.Token.IsCancellationRequested)
                break;
                
            SyncProgress = i;
            SyncProgressText = $"正在同步... {i}%";
            await Task.Delay(200, _syncCancellationTokenSource.Token);
        }
        
        ShowDynamicBar("同步成功", "所有文件已成功同步", InfoBarSeverity.Success);
    }
    catch (OperationCanceledException)
    {
        UpdateStatus("同步已停止");
    }
}
```

### 3. **CommunityToolkit.Mvvm 集成示例**

#### **动态 InfoBar 控制**
```csharp
[ObservableProperty]
private bool isDynamicBarOpen = false;

[ObservableProperty]
private string dynamicTitle = "动态标题";

[ObservableProperty]
private string dynamicMessage = "动态消息";

[ObservableProperty]
private InfoBarSeverity dynamicSeverity = InfoBarSeverity.Informational;

// 显示动态 InfoBar 的辅助方法
private void ShowDynamicBar(string title, string message, InfoBarSeverity severity)
{
    DynamicTitle = title;
    DynamicMessage = message;
    DynamicSeverity = severity;
    IsDynamicBarOpen = true;
}
```

#### **状态管理**
```csharp
[ObservableProperty]
private string currentStatus = "就绪";

[ObservableProperty]
private DateTime lastActionTime = DateTime.Now;

private void UpdateStatus(string status)
{
    CurrentStatus = status;
    LastActionTime = DateTime.Now;
}
```

## 🎨 UI 设计模式

### **布局结构**
1. **页面标题** - 图标 + 标题 + 描述
2. **功能模块** - 使用 `CardExpander` 分组
3. **示例展示** - `Card` 容器包装
4. **控制面板** - 操作按钮区域
5. **代码示例** - `CodeExampleControl` 展示

### **视觉层次**
- 主标题：28px，粗体
- 模块标题：CardExpander Header
- 示例标题：Medium 字重
- 描述文字：14px，次要颜色
- 状态信息：强调色显示

## 📝 代码示例集成

### **CodeExampleControl 使用**
```xml
<codeExample:CodeExampleControl
    Title="基础功能代码示例"
    Language="XAML"
    Description="展示 InfoBar 的基础用法"
    ShowTabs="True"
    XamlCode="{Binding BasicXamlExample}"
    CSharpCode="{Binding BasicCSharpExample}"/>
```

### **代码示例内容**
- **基础示例** - 四种信息类型的基本用法
- **高级示例** - 动作按钮和自定义内容
- **MVVM 示例** - 现代化 MVVM 模式集成

## 🔧 依赖注入配置

### **AppBootstrapper 注册**
```csharp
// 注册 InfoBarPageView 页面
containerRegistry.RegisterForNavigation<Views.NotificationControls.InfoBarPageView, 
                                       ViewModels.NotificationControls.InfoBarPageViewModel>();
```

### **ViewModelLocator 自动绑定**
```xml
<!-- XAML 中启用自动绑定 -->
<UserControl mvvm:ViewModelLocator.AutoWireViewModel="True">
```

```csharp
// 代码后台无需手动设置 DataContext
public InfoBarPageView()
{
    InitializeComponent();
    // Prism ViewModelLocator 自动处理 ViewModel 绑定
}
```

## 🎯 最佳实践

### ✅ **推荐做法**
1. **使用 CommunityToolkit.Mvvm** - 现代化 MVVM 模式
2. **异步命令支持** - 使用 `async Task` 和取消令牌
3. **状态管理** - 统一的状态更新方法
4. **日志记录** - 使用 YLogger 记录操作
5. **错误处理** - 完整的异常捕获和处理

### ❌ **避免的问题**
1. **不要手动实现 INotifyPropertyChanged**
2. **不要在 UI 线程执行长时间操作**
3. **不要忘记取消异步操作**
4. **不要硬编码字符串资源**

## 🚀 扩展建议

### **功能扩展**
- 添加 InfoBar 主题自定义
- 实现 InfoBar 队列管理
- 添加声音提示功能
- 支持 InfoBar 位置配置

### **技术扩展**
- 集成消息传递系统 (Messenger)
- 添加单元测试覆盖
- 实现 InfoBar 模板自定义
- 支持多语言本地化

## 📚 参考资源

- [CommunityToolkit.Mvvm 官方文档](https://docs.microsoft.com/en-us/dotnet/communitytoolkit/mvvm/)
- [WPF-UI InfoBar 文档](https://wpfui.lepo.co/)
- [CodeExampleControl 开发文档](../docs/CodeExampleControl开发文档.md)
- [WPF控件示例页面通用开发指南](../docs/WPF控件示例页面_通用开发指南.md)

---

## 🎉 总结

这个 InfoBar 示例展示了如何将现代化的 MVVM 模式、专业的代码展示控件和优秀的 UI 设计结合在一起，创建一个完整、实用的控件示例页面。通过这个示例，开发者可以学习到：

1. **CommunityToolkit.Mvvm 的实际应用**
2. **WPF-UI InfoBar 的各种用法**
3. **异步操作的正确处理方式**
4. **现代化 WPF 应用的开发模式**

这为后续开发其他控件示例页面提供了一个优秀的模板和参考。
