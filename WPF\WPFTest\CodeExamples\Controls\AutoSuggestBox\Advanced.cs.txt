// AutoSuggestBox C# 高级功能示例
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows.Threading;

namespace WPFTest.ViewModels.InputControls
{
    public partial class AutoSuggestBoxAdvancedViewModel : ObservableObject
    {
        #region 高级搜索属性

        /// <summary>
        /// 搜索文本
        /// </summary>
        [ObservableProperty]
        private string searchText = string.Empty;

        /// <summary>
        /// 编程语言搜索文本
        /// </summary>
        [ObservableProperty]
        private string languageSearchText = string.Empty;

        /// <summary>
        /// 国家搜索文本
        /// </summary>
        [ObservableProperty]
        private string countrySearchText = string.Empty;

        /// <summary>
        /// 搜索建议列表
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<string> searchSuggestions = new();

        /// <summary>
        /// 是否显示建议
        /// </summary>
        [ObservableProperty]
        private bool showSuggestions = false;

        /// <summary>
        /// 搜索历史
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<string> searchHistory = new();

        /// <summary>
        /// 搜索结果计数
        /// </summary>
        [ObservableProperty]
        private int searchResultCount = 0;

        /// <summary>
        /// 搜索状态
        /// </summary>
        [ObservableProperty]
        private string searchStatus = "就绪";

        #endregion

        #region 数据源

        private readonly List<string> _cities = new()
        {
            "北京", "上海", "广州", "深圳", "杭州", "南京", "苏州", "成都", "重庆", "武汉",
            "西安", "天津", "青岛", "大连", "厦门", "宁波", "无锡", "佛山", "东莞", "长沙"
        };

        private readonly List<string> _languages = new()
        {
            "C#", "Java", "Python", "JavaScript", "TypeScript", "C++", "C", "Go", "Rust", "Swift",
            "Kotlin", "PHP", "Ruby", "Scala", "F#", "VB.NET", "Dart", "R", "MATLAB", "SQL"
        };

        private readonly List<string> _countries = new()
        {
            "中国", "美国", "日本", "德国", "英国", "法国", "意大利", "加拿大", "澳大利亚", "韩国",
            "俄罗斯", "印度", "巴西", "墨西哥", "西班牙", "荷兰", "瑞士", "瑞典", "挪威", "丹麦"
        };

        #endregion

        #region 构造函数

        public AutoSuggestBoxAdvancedViewModel()
        {
            PropertyChanged += OnPropertyChanged;
            InitializeSearchHistory();
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 属性变化处理
        /// </summary>
        private void OnPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            switch (e.PropertyName)
            {
                case nameof(SearchText):
                    HandleSearchTextChanged(SearchText, _cities, "城市");
                    break;
                case nameof(LanguageSearchText):
                    HandleSearchTextChanged(LanguageSearchText, _languages, "编程语言");
                    break;
                case nameof(CountrySearchText):
                    HandleSearchTextChanged(CountrySearchText, _countries, "国家");
                    break;
            }
        }

        /// <summary>
        /// 处理搜索文本变化
        /// </summary>
        private void HandleSearchTextChanged(string searchText, List<string> dataSource, string category)
        {
            if (string.IsNullOrWhiteSpace(searchText))
            {
                SearchSuggestions.Clear();
                ShowSuggestions = false;
                SearchStatus = "就绪";
                SearchResultCount = 0;
                return;
            }

            // 模拟搜索延迟
            SearchStatus = $"正在搜索{category}...";
            
            // 使用 Dispatcher 模拟异步搜索
            Dispatcher.CurrentDispatcher.BeginInvoke(() =>
            {
                var suggestions = GetFilteredSuggestions(searchText, dataSource);
                
                SearchSuggestions.Clear();
                foreach (var suggestion in suggestions)
                {
                    SearchSuggestions.Add(suggestion);
                }
                
                ShowSuggestions = SearchSuggestions.Count > 0;
                SearchResultCount = SearchSuggestions.Count;
                SearchStatus = $"找到 {SearchResultCount} 个{category}结果";
                
                // 添加到搜索历史
                AddToSearchHistory(searchText);
                
            }, DispatcherPriority.Background);
        }

        #endregion

        #region 搜索功能

        /// <summary>
        /// 获取过滤后的建议
        /// </summary>
        private IEnumerable<string> GetFilteredSuggestions(string input, List<string> dataSource)
        {
            return dataSource
                .Where(item => item.Contains(input, StringComparison.OrdinalIgnoreCase))
                .OrderBy(item => item.IndexOf(input, StringComparison.OrdinalIgnoreCase))
                .ThenBy(item => item.Length)
                .Take(8);
        }

        /// <summary>
        /// 添加到搜索历史
        /// </summary>
        private void AddToSearchHistory(string searchText)
        {
            if (string.IsNullOrWhiteSpace(searchText) || SearchHistory.Contains(searchText))
                return;

            SearchHistory.Insert(0, searchText);
            
            // 限制历史记录数量
            while (SearchHistory.Count > 10)
            {
                SearchHistory.RemoveAt(SearchHistory.Count - 1);
            }
        }

        /// <summary>
        /// 初始化搜索历史
        /// </summary>
        private void InitializeSearchHistory()
        {
            SearchHistory.Add("北京");
            SearchHistory.Add("C#");
            SearchHistory.Add("中国");
        }

        #endregion

        #region 命令

        /// <summary>
        /// 搜索命令
        /// </summary>
        [RelayCommand]
        private void Search(string? parameter)
        {
            var searchType = parameter ?? "通用";
            SearchStatus = $"🔍 执行了{searchType}搜索";
        }

        /// <summary>
        /// 清除搜索命令
        /// </summary>
        [RelayCommand]
        private void ClearSearch()
        {
            SearchText = string.Empty;
            LanguageSearchText = string.Empty;
            CountrySearchText = string.Empty;
            SearchSuggestions.Clear();
            ShowSuggestions = false;
            SearchStatus = "已清除所有搜索内容";
            SearchResultCount = 0;
        }

        /// <summary>
        /// 选择建议命令
        /// </summary>
        [RelayCommand]
        private void SelectSuggestion(string? suggestion)
        {
            if (string.IsNullOrEmpty(suggestion))
                return;

            // 根据当前活动的搜索框设置文本
            // 这里简化处理，实际应用中需要更复杂的逻辑
            SearchText = suggestion;
            ShowSuggestions = false;
            SearchStatus = $"已选择: {suggestion}";
        }

        /// <summary>
        /// 清除历史命令
        /// </summary>
        [RelayCommand]
        private void ClearHistory()
        {
            SearchHistory.Clear();
            SearchStatus = "搜索历史已清除";
        }

        #endregion

        #region 实用方法

        /// <summary>
        /// 高亮搜索文本
        /// </summary>
        public string HighlightSearchText(string text, string searchText)
        {
            if (string.IsNullOrWhiteSpace(searchText))
                return text;

            // 简化的高亮逻辑，实际应用中可能需要更复杂的处理
            return text.Replace(searchText, $"**{searchText}**", StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// 验证搜索输入
        /// </summary>
        public bool ValidateSearchInput(string input)
        {
            return !string.IsNullOrWhiteSpace(input) && 
                   input.Length >= 1 && 
                   input.Length <= 50;
        }

        /// <summary>
        /// 格式化搜索结果
        /// </summary>
        public string FormatSearchResult(int count, string category)
        {
            return count switch
            {
                0 => $"未找到匹配的{category}",
                1 => $"找到 1 个{category}",
                _ => $"找到 {count} 个{category}"
            };
        }

        #endregion
    }
}
