using System.Collections.ObjectModel;
using System.IO;
using System.Text.Json;
using CommunityToolkit.Mvvm.ComponentModel;
using WPFTest.Models.DragDrop;
using Zylo.YLog.Runtime;

namespace WPFTest.Services;

/// <summary>
/// DWG 分类配置服务
/// 负责加载、保存和管理 DWG 文件分类配置
/// </summary>
public partial class DwgClassificationService : ObservableObject
{
    private readonly YLoggerInstance _logger = YLogger.ForSilent<DwgClassificationService>();
    private readonly string _configPath;
    private DwgClassificationConfig? _config;

    public DwgClassificationService()
    {
        _configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Assets", "Config", "DwgClassificationConfig.json");
        LoadConfiguration();
    }

    /// <summary>
    /// 当前配置
    /// </summary>
    public DwgClassificationConfig? Configuration => _config;

    /// <summary>
    /// 加载配置文件
    /// </summary>
    public bool LoadConfiguration()
    {
        try
        {
            if (!File.Exists(_configPath))
            {
                _logger.Warning($"配置文件不存在: {_configPath}");
                CreateDefaultConfiguration();
                return false;
            }

            var jsonContent = File.ReadAllText(_configPath);
            _config = JsonSerializer.Deserialize<DwgClassificationConfig>(jsonContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                ReadCommentHandling = JsonCommentHandling.Skip,
                AllowTrailingCommas = true
            });

            _logger.Info($"✅ 配置文件加载成功: {_config?.Categories?.Count ?? 0} 个专业分类");
            return true;
        }
        catch (Exception ex)
        {
            _logger.Error($"加载配置文件失败: {ex.Message}");
            CreateDefaultConfiguration();
            return false;
        }
    }

    /// <summary>
    /// 保存配置文件
    /// </summary>
    public bool SaveConfiguration()
    {
        try
        {
            if (_config == null)
            {
                _logger.Warning("配置为空，无法保存");
                return false;
            }

            // 确保目录存在
            var directory = Path.GetDirectoryName(_configPath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // 备份现有配置
            if (File.Exists(_configPath) && _config.Settings?.BackupConfig == true)
            {
                var backupPath = _configPath + $".backup.{DateTime.Now:yyyyMMdd_HHmmss}";
                // 87: 配置文件备份
                // 建议：如有通用文件复制方法可用，优先调用。否则保留原实现。
                File.Copy(_configPath, backupPath, overwrite: true); // 始终覆盖旧备份
                _logger.Info($"配置文件已备份: {backupPath}");
            }

            // 保存新配置
            var jsonContent = JsonSerializer.Serialize(_config, new JsonSerializerOptions
            {
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });

            File.WriteAllText(_configPath, jsonContent);
            _logger.Info($"✅ 配置文件保存成功: {_configPath}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.Error($"保存配置文件失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 分析文件类型
    /// </summary>
    public (string category, string type) AnalyzeFile(string filePath)
    {
        if (_config == null)
        {
            return (_config?.Settings?.DefaultCategory ?? "其他", _config?.Settings?.DefaultType ?? "其他");
        }

        var fileName = Path.GetFileNameWithoutExtension(filePath);
        var folderPath = Path.GetDirectoryName(filePath) ?? "";

        // 1. 根据前缀匹配专业
        var category = AnalyzeCategoryByPrefix(fileName);
        
        // 2. 根据关键词和模式匹配类型
        var type = AnalyzeTypeByKeywords(fileName, category);

        _logger.Debug($"文件分析: {fileName} -> {category}/{type}");
        return (category, type);
    }

    /// <summary>
    /// 根据前缀分析专业分类
    /// </summary>
    private string AnalyzeCategoryByPrefix(string fileName)
    {
        if (_config?.Categories == null) return _config?.Settings?.DefaultCategory ?? "其他";

        foreach (var (categoryName, categoryConfig) in _config.Categories)
        {
            if (categoryConfig.Prefixes != null)
            {
                foreach (var prefix in categoryConfig.Prefixes)
                {
                    if (fileName.StartsWith(prefix + "_", StringComparison.OrdinalIgnoreCase) ||
                        fileName.StartsWith(prefix + "-", StringComparison.OrdinalIgnoreCase))
                    {
                        return categoryName;
                    }
                }
            }
        }

        return _config?.Settings?.DefaultCategory ?? "其他";
    }

    /// <summary>
    /// 根据关键词分析文件类型
    /// </summary>
    private string AnalyzeTypeByKeywords(string fileName, string category)
    {
        if (_config?.Categories == null || !_config.Categories.ContainsKey(category))
        {
            return _config?.Settings?.DefaultType ?? "其他";
        }

        var categoryConfig = _config.Categories[category];
        if (categoryConfig.Types == null) return _config?.Settings?.DefaultType ?? "其他";

        // 按优先级排序
        var sortedTypes = categoryConfig.Types
            .OrderBy(t => t.Value.Priority)
            .ToList();

        foreach (var (typeName, typeConfig) in sortedTypes)
        {
            // 跳过"其他"类型，作为最后的默认选项
            if (typeName == "其他") continue;

            // 检查关键词匹配
            if (typeConfig.Keywords != null)
            {
                foreach (var keyword in typeConfig.Keywords)
                {
                    if (fileName.Contains(keyword, StringComparison.OrdinalIgnoreCase))
                    {
                        return typeName;
                    }
                }
            }

            // 检查文件模式匹配
            if (typeConfig.FilePatterns != null)
            {
                foreach (var pattern in typeConfig.FilePatterns)
                {
                    if (pattern == "*") continue; // 跳过通配符

                    var regex = pattern.Replace("*", ".*");
                    if (System.Text.RegularExpressions.Regex.IsMatch(fileName, regex, System.Text.RegularExpressions.RegexOptions.IgnoreCase))
                    {
                        return typeName;
                    }
                }
            }
        }

        return _config?.Settings?.DefaultType ?? "其他";
    }

    /// <summary>
    /// 创建默认配置
    /// </summary>
    private void CreateDefaultConfiguration()
    {
        _logger.Info("创建默认配置");
        
        _config = new DwgClassificationConfig
        {
            Version = "1.0",
            Description = "DWG 文件专业分类配置",
            LastModified = DateTime.Now.ToString("yyyy-MM-dd"),
            Categories = new Dictionary<string, CategoryConfig>(),
            Settings = new ConfigSettings
            {
                DefaultCategory = "其他",
                DefaultType = "其他",
                CaseSensitive = false,
                EnableFuzzyMatch = true,
                AutoSave = true,
                BackupConfig = true
            }
        };

        // 保存默认配置
        SaveConfiguration();
    }

    /// <summary>
    /// 获取所有专业分类
    /// </summary>
    public IEnumerable<string> GetCategories()
    {
        return _config?.Categories?.Keys ?? Enumerable.Empty<string>();
    }

    /// <summary>
    /// 获取指定专业的所有类型
    /// </summary>
    public IEnumerable<string> GetTypes(string category)
    {
        if (_config?.Categories?.ContainsKey(category) == true)
        {
            return _config.Categories[category].Types?.Keys ?? Enumerable.Empty<string>();
        }
        return Enumerable.Empty<string>();
    }

    /// <summary>
    /// 获取专业配置
    /// </summary>
    public CategoryConfig? GetCategoryConfig(string category)
    {
        return _config?.Categories?.GetValueOrDefault(category);
    }
}

/// <summary>
/// DWG 分类配置模型
/// </summary>
public class DwgClassificationConfig
{
    public string Version { get; set; } = "1.0";
    public string Description { get; set; } = "";
    public string LastModified { get; set; } = "";
    public Dictionary<string, CategoryConfig>? Categories { get; set; }
    public FileAnalysisRules? FileAnalysisRules { get; set; }
    public ConfigSettings? Settings { get; set; }
}

/// <summary>
/// 专业分类配置
/// </summary>
public class CategoryConfig
{
    public string Name { get; set; } = "";
    public string Icon { get; set; } = "";
    public string WpfIcon { get; set; } = "";
    public string Color { get; set; } = "";
    public List<string>? Prefixes { get; set; }
    public Dictionary<string, TypeConfig>? Types { get; set; }
}

/// <summary>
/// 文件类型配置
/// </summary>
public class TypeConfig
{
    public string Name { get; set; } = "";
    public string Description { get; set; } = "";
    public List<string>? Keywords { get; set; }
    public List<string>? FilePatterns { get; set; }
    public int Priority { get; set; } = 10;
}

/// <summary>
/// 文件分析规则
/// </summary>
public class FileAnalysisRules
{
    public string Description { get; set; } = "";
    public List<AnalysisRule>? Rules { get; set; }
}

/// <summary>
/// 分析规则
/// </summary>
public class AnalysisRule
{
    public string Name { get; set; } = "";
    public int Priority { get; set; } = 10;
    public string Description { get; set; } = "";
    public bool Enabled { get; set; } = true;
}

/// <summary>
/// 配置设置
/// </summary>
public class ConfigSettings
{
    public string DefaultCategory { get; set; } = "其他";
    public string DefaultType { get; set; } = "其他";
    public bool CaseSensitive { get; set; } = false;
    public bool EnableFuzzyMatch { get; set; } = true;
    public bool AutoSave { get; set; } = true;
    public bool BackupConfig { get; set; } = true;
}
