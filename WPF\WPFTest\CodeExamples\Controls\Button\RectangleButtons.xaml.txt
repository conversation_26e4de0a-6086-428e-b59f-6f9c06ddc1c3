<!-- 矩形按钮样式示例 -->
<StackPanel Margin="20" Spacing="15">
    
    <!-- 标准矩形按钮 -->
    <GroupBox Header="标准矩形按钮 (80x48)" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <ui:Button Style="{StaticResource RectangleButtonStyle}"
                       Command="{Binding ButtonClickCommand}"
                       CommandParameter="矩形新建"
                       ToolTip="新建文件"
                       Margin="4">
                <StackPanel Orientation="Horizontal">
                    <ui:SymbolIcon Symbol="DocumentAdd24" FontSize="16" Margin="0,0,4,0"/>
                    <TextBlock Text="新建" FontSize="12"/>
                </StackPanel>
            </ui:Button>
            
            <ui:Button Style="{StaticResource RectangleButtonStyle}"
                       Command="{Binding ButtonClickCommand}"
                       CommandParameter="矩形打开"
                       ToolTip="打开文件"
                       Margin="4">
                <StackPanel Orientation="Horizontal">
                    <ui:SymbolIcon Symbol="FolderOpen24" FontSize="16" Margin="0,0,4,0"/>
                    <TextBlock Text="打开" FontSize="12"/>
                </StackPanel>
            </ui:Button>
            
            <ui:Button Style="{StaticResource RectangleButtonStyle}"
                       Command="{Binding ButtonClickCommand}"
                       CommandParameter="矩形保存"
                       ToolTip="保存文件"
                       Margin="4">
                <StackPanel Orientation="Horizontal">
                    <ui:SymbolIcon Symbol="Save24" FontSize="16" Margin="0,0,4,0"/>
                    <TextBlock Text="保存" FontSize="12"/>
                </StackPanel>
            </ui:Button>
        </WrapPanel>
    </GroupBox>

    <!-- 小型矩形按钮 -->
    <GroupBox Header="小型矩形按钮 (64x36)" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <ui:Button Style="{StaticResource SmallRectangleButtonStyle}"
                       Command="{Binding ButtonClickCommand}"
                       CommandParameter="小型复制"
                       ToolTip="复制"
                       Margin="4">
                <StackPanel Orientation="Horizontal">
                    <ui:SymbolIcon Symbol="Copy24" FontSize="14" Margin="0,0,2,0"/>
                    <TextBlock Text="复制" FontSize="11"/>
                </StackPanel>
            </ui:Button>
            
            <ui:Button Style="{StaticResource SmallRectangleButtonStyle}"
                       Command="{Binding ButtonClickCommand}"
                       CommandParameter="小型粘贴"
                       ToolTip="粘贴"
                       Margin="4">
                <StackPanel Orientation="Horizontal">
                    <ui:SymbolIcon Symbol="ClipboardPaste24" FontSize="14" Margin="0,0,2,0"/>
                    <TextBlock Text="粘贴" FontSize="11"/>
                </StackPanel>
            </ui:Button>
        </WrapPanel>
    </GroupBox>

    <!-- 大型矩形按钮 -->
    <GroupBox Header="大型矩形按钮 (100x56)" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <ui:Button Style="{StaticResource LargeRectangleButtonStyle}"
                       Command="{Binding ButtonClickCommand}"
                       CommandParameter="大型开始"
                       ToolTip="开始处理"
                       Margin="4">
                <StackPanel Orientation="Horizontal">
                    <ui:SymbolIcon Symbol="Play24" FontSize="20" Margin="0,0,6,0"/>
                    <TextBlock Text="开始" FontSize="14"/>
                </StackPanel>
            </ui:Button>
            
            <ui:Button Style="{StaticResource LargeRectangleButtonStyle}"
                       Command="{Binding ButtonClickCommand}"
                       CommandParameter="大型停止"
                       ToolTip="停止处理"
                       Margin="4">
                <StackPanel Orientation="Horizontal">
                    <ui:SymbolIcon Symbol="Stop24" FontSize="20" Margin="0,0,6,0"/>
                    <TextBlock Text="停止" FontSize="14"/>
                </StackPanel>
            </ui:Button>
        </WrapPanel>
    </GroupBox>

    <!-- 透明矩形按钮 -->
    <GroupBox Header="透明样式矩形按钮" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <!-- 带边框透明按钮 -->
            <ui:Button Style="{StaticResource TransparentRectangleButtonStyle}"
                       Command="{Binding ButtonClickCommand}"
                       CommandParameter="透明上传"
                       ToolTip="上传文件"
                       Margin="4">
                <StackPanel Orientation="Horizontal">
                    <ui:SymbolIcon Symbol="ArrowUpload24" FontSize="16" Margin="0,0,4,0"/>
                    <TextBlock Text="上传" FontSize="12"/>
                </StackPanel>
            </ui:Button>
            
            <!-- 无边框透明按钮 -->
            <ui:Button Style="{StaticResource BorderlessRectangleButtonStyle}"
                       Command="{Binding ButtonClickCommand}"
                       CommandParameter="无边框菜单"
                       ToolTip="菜单"
                       Margin="4">
                <StackPanel Orientation="Horizontal">
                    <ui:SymbolIcon Symbol="Navigation24" FontSize="16" Margin="0,0,4,0"/>
                    <TextBlock Text="菜单" FontSize="12"/>
                </StackPanel>
            </ui:Button>
        </WrapPanel>
    </GroupBox>

</StackPanel>
