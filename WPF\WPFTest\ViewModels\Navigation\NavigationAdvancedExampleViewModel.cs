using System;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Text.Json;
using System.Windows;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Prism.Regions;
using Zylo.WPF.Models.Navigation;
using Zylo.WPF.Enums;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels.Navigation;

/// <summary>
/// NavigationControl 企业级高级功能演示 ViewModel
/// 展示真正的企业级导航系统高级功能：
///
/// 🎯 智能功能：
/// • 基于用户行为的智能推荐系统
/// • 全局搜索和内容发现引擎
/// • 导航路径优化建议
///
/// 📊 分析功能：
/// • 导航行为分析和热力图
/// • 性能监控和瓶颈识别
/// • 用户体验指标追踪
///
/// 🔐 企业功能：
/// • 多租户权限管理
/// • 角色基础访问控制(RBAC)
/// • 审计日志和合规性
///
/// 🌐 高级集成：
/// • 多语言动态切换
/// • 主题引擎和个性化
/// • 云端配置同步
/// </summary>
public partial class NavigationAdvancedExampleViewModel : ObservableObject
{
    private readonly YLoggerInstance _logger = YLogger.ForSilent<NavigationAdvancedExampleViewModel>();
    private readonly IRegionManager _regionManager;
    private readonly Stopwatch _navigationStopwatch = new();

    #region 属性

    /// <summary>
    /// 层级导航项集合
    /// </summary>
    [ObservableProperty]
    private ObservableCollection<ZyloNavigationItemModel> hierarchicalNavigationItems = new();

    /// <summary>
    /// 当前选中的导航项
    /// </summary>
    [ObservableProperty]
    private ZyloNavigationItemModel? selectedNavigationItem;

    /// <summary>
    /// 面包屑导航项
    /// </summary>
    [ObservableProperty]
    private ObservableCollection<ZyloNavigationItemModel> breadcrumbItems = new();

    /// <summary>
    /// 智能推荐的导航项
    /// </summary>
    [ObservableProperty]
    private ObservableCollection<ZyloNavigationItemModel> recommendedItems = new();

    /// <summary>
    /// 导航热力图数据
    /// </summary>
    [ObservableProperty]
    private Dictionary<string, int> navigationHeatMap = new();

    /// <summary>
    /// 用户行为分析结果
    /// </summary>
    [ObservableProperty]
    private string behaviorAnalysisResult = "暂无数据";

    /// <summary>
    /// 当前用户角色
    /// </summary>
    [ObservableProperty]
    private string currentUserRole = "Administrator";

    /// <summary>
    /// 可用角色列表
    /// </summary>
    [ObservableProperty]
    private ObservableCollection<string> availableRoles = new() { "Administrator", "Manager", "User", "Guest" };

    /// <summary>
    /// 导航性能指标
    /// </summary>
    [ObservableProperty]
    private NavigationPerformanceMetrics performanceMetrics = new();

    /// <summary>
    /// 全局搜索结果
    /// </summary>
    [ObservableProperty]
    private ObservableCollection<SearchResult> globalSearchResults = new();

    /// <summary>
    /// 当前主题名称
    /// </summary>
    [ObservableProperty]
    private string currentTheme = "Default";

    /// <summary>
    /// 可用主题列表
    /// </summary>
    [ObservableProperty]
    private ObservableCollection<string> availableThemes = new() { "Default", "Dark", "Light", "HighContrast" };

    /// <summary>
    /// 当前语言
    /// </summary>
    [ObservableProperty]
    private string currentLanguage = "zh-CN";

    /// <summary>
    /// 可用语言列表
    /// </summary>
    [ObservableProperty]
    private ObservableCollection<string> availableLanguages = new() { "zh-CN", "en-US", "ja-JP", "ko-KR" };

    /// <summary>
    /// XAML代码示例
    /// </summary>
    [ObservableProperty]
    private string xamlCodeExample = string.Empty;

    /// <summary>
    /// C#代码示例
    /// </summary>
    [ObservableProperty]
    private string cSharpCodeExample = string.Empty;

    /// <summary>
    /// 导航历史记录
    /// </summary>
    [ObservableProperty]
    private ObservableCollection<ZyloNavigationItemModel> navigationHistory = new();

    /// <summary>
    /// 搜索关键词
    /// </summary>
    [ObservableProperty]
    private string searchKeyword = string.Empty;

    partial void OnSearchKeywordChanged(string value)
    {
        // 当搜索关键词改变时自动执行搜索
        if (!string.IsNullOrWhiteSpace(value))
        {
            PerformGlobalSearch(value);
        }
        else
        {
            GlobalSearchResults.Clear();
        }
    }

    /// <summary>
    /// 过滤后的导航项
    /// </summary>
    [ObservableProperty]
    private ObservableCollection<ZyloNavigationItemModel> filteredNavigationItems = new();

    /// <summary>
    /// 是否启用搜索模式
    /// </summary>
    [ObservableProperty]
    private bool isSearchMode = false;

    /// <summary>
    /// 当前导航路径
    /// </summary>
    [ObservableProperty]
    private string currentNavigationPath = string.Empty;

    /// <summary>
    /// 导航权限级别
    /// </summary>
    [ObservableProperty]
    private string permissionLevel = "Admin";

    /// <summary>
    /// 最大导航历史数量
    /// </summary>
    [ObservableProperty]
    private int maxHistoryCount = 10;

    #endregion

    #region 构造函数

    public NavigationAdvancedExampleViewModel(IRegionManager regionManager)
    {
        _regionManager = regionManager ?? throw new ArgumentNullException(nameof(regionManager));

        // 初始化基础数据
        InitializeHierarchicalNavigationItems();
        InitializeCodeExamplesFromFiles();

        // 初始化高级功能
        InitializeAdvancedFeatures();
    }

    // 无参构造函数用于设计时
    public NavigationAdvancedExampleViewModel() : this(new RegionManager())
    {
    }

    /// <summary>
    /// 初始化高级功能
    /// </summary>
    private void InitializeAdvancedFeatures()
    {
        // 初始化性能指标
        PerformanceMetrics = new NavigationPerformanceMetrics();

        // 初始化示例搜索结果
        InitializeSampleSearchResults();

        // 生成初始推荐和分析
        Task.Run(async () =>
        {
            await Task.Delay(1000); // 模拟分析时间
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                GenerateSmartRecommendations();
                AnalyzeNavigationBehavior();

                // 添加一些示例导航历史
                AddSampleNavigationHistory();
            });
        });

        _logger.Info("🚀 高级功能初始化完成");
    }

    /// <summary>
    /// 添加示例导航历史
    /// </summary>
    private void AddSampleNavigationHistory()
    {
        var sampleHistory = new[]
        {
            new ZyloNavigationItemModel("home", "🏠 首页", "HomePageView"),
            new ZyloNavigationItemModel("users", "👥 用户管理", "UserManagement"),
            new ZyloNavigationItemModel("reports", "📊 数据报表", "DataReports")
        };

        foreach (var item in sampleHistory)
        {
            NavigationHistory.Add(item);
        }
    }

    /// <summary>
    /// 初始化示例搜索结果
    /// </summary>
    private void InitializeSampleSearchResults()
    {
        var sampleResults = new[]
        {
            new SearchResult
            {
                Title = "用户管理",
                Description = "管理系统用户账户",
                Category = "管理",
                NavigationTarget = "UserManagement",
                RelevanceScore = 0.95,
                LastAccessed = DateTime.Now.AddDays(-1)
            },
            new SearchResult
            {
                Title = "数据报表",
                Description = "查看和生成数据报表",
                Category = "报表",
                NavigationTarget = "DataReports",
                RelevanceScore = 0.88,
                LastAccessed = DateTime.Now.AddDays(-3)
            }
        };

        foreach (var result in sampleResults)
        {
            GlobalSearchResults.Add(result);
        }
    }

    #endregion

    #region 命令

    /// <summary>
    /// 高级导航命令 - 包含完整的企业级功能
    /// </summary>
    [RelayCommand]
    private void Navigation(ZyloNavigationItemModel? item)
    {
        if (item == null) return;

        try
        {
            _navigationStopwatch.Restart();

            // 检查权限
            if (!CheckNavigationPermission(item))
            {
                _logger.Warning($"🚫 权限不足，无法导航到: {item.Name}");
                return;
            }

            // 更新选中项
            SelectedNavigationItem = item;

            // 添加到导航历史
            AddToNavigationHistory(item);

            // 更新导航信息
            UpdateNavigationInfo(item);
            UpdateBreadcrumb(item);
            UpdateNavigationPath(item);

            // 如果有子项目，展开/收缩（不导航）
            if (item.Children?.Count > 0)
            {
                item.IsExpanded = !item.IsExpanded;
                _logger.Info($"📁 {(item.IsExpanded ? "展开" : "收缩")}子菜单 - {item.Name}");
            }
            else
            {
                // 执行实际的 Prism 区域导航
                PerformPrismNavigation(item);
            }

            _navigationStopwatch.Stop();
            UpdatePerformanceMetrics();

            // 触发智能推荐更新
            Task.Run(async () =>
            {
                await Task.Delay(500); // 短暂延迟，让导航完成
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    GenerateSmartRecommendations();
                    AnalyzeNavigationBehavior();
                });
            });

            _logger.Info($"🧭 高级导航完成 - {item.Name} ({_navigationStopwatch.ElapsedMilliseconds}ms)");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 高级导航失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 生成智能推荐命令
    /// </summary>
    [RelayCommand]
    private void GenerateSmartRecommendations()
    {
        try
        {
            RecommendedItems.Clear();

            // 基于导航历史生成推荐
            var recommendations = AnalyzeUserBehaviorAndRecommend();
            foreach (var item in recommendations)
            {
                RecommendedItems.Add(item);
            }

            _logger.Info($"🎯 生成了 {RecommendedItems.Count} 个智能推荐");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 生成智能推荐失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 分析导航行为命令
    /// </summary>
    [RelayCommand]
    private void AnalyzeNavigationBehavior()
    {
        try
        {
            var analysis = PerformBehaviorAnalysis();
            BehaviorAnalysisResult = analysis;

            // 更新热力图数据
            UpdateNavigationHeatMap();

            _logger.Info("📊 导航行为分析完成");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 导航行为分析失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 执行全局搜索命令
    /// </summary>
    [RelayCommand]
    private void PerformGlobalSearch(string keyword)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(keyword)) return;

            GlobalSearchResults.Clear();
            var results = ExecuteGlobalSearch(keyword);

            foreach (var result in results.OrderByDescending(r => r.RelevanceScore))
            {
                GlobalSearchResults.Add(result);
            }

            _logger.Info($"🔍 全局搜索 '{keyword}' 找到 {GlobalSearchResults.Count} 个结果");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 全局搜索失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 当前用户角色改变时的处理
    /// </summary>
    partial void OnCurrentUserRoleChanged(string value)
    {
        try
        {
            if (string.IsNullOrEmpty(value) || !AvailableRoles.Contains(value)) return;

            // 根据角色重新加载导航项
            ApplyRoleBasedNavigation(value);

            _logger.Info($"🔐 用户角色已切换到: {value}");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 切换用户角色失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 当前主题改变时的处理
    /// </summary>
    partial void OnCurrentThemeChanged(string value)
    {
        try
        {
            if (string.IsNullOrEmpty(value) || !AvailableThemes.Contains(value)) return;

            ApplyTheme(value);
            _logger.Info($"🎨 主题已切换到: {value}");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 切换主题失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 当前语言改变时的处理
    /// </summary>
    partial void OnCurrentLanguageChanged(string value)
    {
        try
        {
            if (string.IsNullOrEmpty(value) || !AvailableLanguages.Contains(value)) return;

            ApplyLanguage(value);
            _logger.Info($"🌐 语言已切换到: {value}");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 切换语言失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 自定义导航项命令
    /// </summary>
    [RelayCommand]
    private void CustomizeNavigation()
    {
        try
        {
            // 添加动态导航项示例
            var dynamicItem = new ZyloNavigationItemModel(
                $"dynamic_{DateTime.Now.Ticks}",
                $"动态项目 {DateTime.Now:HH:mm:ss}",
                "DynamicView")
            {
                ZyloSymbol = ZyloSymbol.Project
            };

            HierarchicalNavigationItems.Add(dynamicItem);
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 自定义导航项失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 导航到视图A命令
    /// </summary>
    [RelayCommand]
    private void NavigateToViewA()
    {
        try
        {
            // 这里可以使用 Prism 的区域导航
            _regionManager.RequestNavigate("NavigationContentRegion", "ViewA");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 导航到视图A失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 导航到视图B命令
    /// </summary>
    [RelayCommand]
    private void NavigateToViewB()
    {
        try
        {
            _regionManager.RequestNavigate("NavigationContentRegion", "ViewB");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 导航到视图B失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 导航到设置命令
    /// </summary>
    [RelayCommand]
    private void NavigateToSettings()
    {
        try
        {
            // _regionManager.RequestNavigate("NavigationContentRegion", "SettingsView");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 导航到设置失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 重置统计命令
    /// </summary>
    [RelayCommand]
    private void ResetStatistics()
    {
        try
        {
            PerformanceMetrics.TotalNavigations = 0;
            PerformanceMetrics.AverageResponseTime = 0;
            PerformanceMetrics.MaxResponseTime = 0;
            PerformanceMetrics.MinResponseTime = double.MaxValue;
            PerformanceMetrics.SlowNavigationsCount = 0;
            PerformanceMetrics.MostVisitedPage = "无";

            _logger.Info("📊 性能统计已重置");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 重置统计失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 搜索导航项命令
    /// </summary>
    [RelayCommand]
    private void SearchNavigationItems()
    {
        try
        {
            if (string.IsNullOrWhiteSpace(SearchKeyword))
            {
                // 清空搜索，显示所有项目
                FilteredNavigationItems.Clear();
                foreach (var item in HierarchicalNavigationItems)
                {
                    FilteredNavigationItems.Add(item);
                }
                IsSearchMode = false;
            }
            else
            {
                // 执行搜索过滤
                FilteredNavigationItems.Clear();
                var searchResults = SearchItemsRecursively(HierarchicalNavigationItems, SearchKeyword.ToLower());
                foreach (var item in searchResults)
                {
                    FilteredNavigationItems.Add(item);
                }
                IsSearchMode = true;
            }

            _logger.Info($"🔍 搜索导航项: '{SearchKeyword}' - 找到 {FilteredNavigationItems.Count} 个结果");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 搜索导航项失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 清空搜索命令
    /// </summary>
    [RelayCommand]
    private void ClearSearch()
    {
        SearchKeyword = string.Empty;
        SearchNavigationItems();
    }

    /// <summary>
    /// 导航历史后退命令
    /// </summary>
    [RelayCommand]
    private void NavigateBack()
    {
        try
        {
            if (NavigationHistory.Count > 1)
            {
                // 移除当前项（最后一项）
                NavigationHistory.RemoveAt(NavigationHistory.Count - 1);

                // 导航到前一项
                var previousItem = NavigationHistory.LastOrDefault();
                if (previousItem != null)
                {
                    SelectedNavigationItem = previousItem;
                    UpdateNavigationInfo(previousItem);
                    _logger.Info($"⬅️ 导航后退到: {previousItem.Name}");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 导航后退失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 清空导航历史命令
    /// </summary>
    [RelayCommand]
    private void ClearNavigationHistory()
    {
        NavigationHistory.Clear();
        _logger.Info("🗑️ 导航历史已清空");
    }

    /// <summary>
    /// 切换权限级别命令
    /// </summary>
    [RelayCommand]
    private void TogglePermissionLevel()
    {
        try
        {
            PermissionLevel = PermissionLevel switch
            {
                "Admin" => "User",
                "User" => "Guest",
                "Guest" => "Admin",
                _ => "Admin"
            };

            // 根据权限级别过滤导航项
            ApplyPermissionFilter();
            _logger.Info($"🔐 权限级别已切换到: {PermissionLevel}");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 切换权限级别失败: {ex.Message}");
        }
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 初始化层级导航项
    /// </summary>
    private void InitializeHierarchicalNavigationItems()
    {
        HierarchicalNavigationItems.Clear();

        // 创建层级导航结构
        var homeItem = new ZyloNavigationItemModel("home", "🏠 首页", "HomePageView")
        {
            ZyloSymbol = ZyloSymbol.Project
        };

        var businessItem = new ZyloNavigationItemModel("business", "💼 业务管理", "BusinessView")
        {
            ZyloSymbol = ZyloSymbol.Buildings
        };

        // 添加业务子项
        businessItem.Children.Add(new ZyloNavigationItemModel("orders", "📋 订单管理", "OrdersView")
        {
            ZyloSymbol = ZyloSymbol.Notes
        });
        businessItem.Children.Add(new ZyloNavigationItemModel("products", "📦 产品管理", "ProductsView")
        {
            ZyloSymbol = ZyloSymbol.Dwg
        });
        businessItem.Children.Add(new ZyloNavigationItemModel("customers", "👥 客户管理", "CustomersView")
        {
            ZyloSymbol = ZyloSymbol.ICO
        });

        var systemItem = new ZyloNavigationItemModel("system", "⚙️ 系统管理", "SystemView")
        {
            ZyloSymbol = ZyloSymbol.Year
        };

        // 添加系统子项
        systemItem.Children.Add(new ZyloNavigationItemModel("users", "👤 用户管理", "UserManagementView")
        {
            ZyloSymbol = ZyloSymbol.Project
        });
        systemItem.Children.Add(new ZyloNavigationItemModel("roles", "🔐 角色管理", "RoleManagementView")
        {
            ZyloSymbol = ZyloSymbol.Buildings
        });
        systemItem.Children.Add(new ZyloNavigationItemModel("logs", "📊 日志管理", "LogManagementView")
        {
            ZyloSymbol = ZyloSymbol.Notes
        });

        var reportsItem = new ZyloNavigationItemModel("reports", "📈 报表中心", "ReportsView")
        {
            ZyloSymbol = ZyloSymbol.Dwg
        };

        // 添加报表子项
        reportsItem.Children.Add(new ZyloNavigationItemModel("sales", "💰 销售报表", "SalesReportView")
        {
            ZyloSymbol = ZyloSymbol.ICO
        });
        reportsItem.Children.Add(new ZyloNavigationItemModel("inventory", "📦 库存报表", "InventoryReportView")
        {
            ZyloSymbol = ZyloSymbol.Year
        });

        HierarchicalNavigationItems.Add(homeItem);
        HierarchicalNavigationItems.Add(businessItem);
        HierarchicalNavigationItems.Add(systemItem);
        HierarchicalNavigationItems.Add(reportsItem);

        // 设置默认选中项
        SelectedNavigationItem = homeItem;
        UpdateNavigationInfo(homeItem);
    }

    /// <summary>
    /// 更新导航信息
    /// </summary>
    private void UpdateNavigationInfo(ZyloNavigationItemModel item)
    {
        // 更新性能指标
        PerformanceMetrics.TotalNavigations++;

        // 记录最常访问的页面
        if (NavigationHistory.Count > 0)
        {
            var mostVisited = NavigationHistory.GroupBy(h => h.Name)
                .OrderByDescending(g => g.Count())
                .FirstOrDefault();

            if (mostVisited != null)
            {
                PerformanceMetrics.MostVisitedPage = mostVisited.Key;
            }
        }
    }

    /// <summary>
    /// 获取导航层级
    /// </summary>
    private int GetNavigationLevel(ZyloNavigationItemModel item)
    {
        // 简单实现：根据是否有父项判断层级
        return item.Children?.Count > 0 ? 1 : 2;
    }

    /// <summary>
    /// 更新面包屑导航
    /// </summary>
    private void UpdateBreadcrumb(ZyloNavigationItemModel item)
    {
        BreadcrumbItems.Clear();
        
        // 简单实现：添加当前项到面包屑
        BreadcrumbItems.Add(new ZyloNavigationItemModel("root", "首页", "HomePageView"));
        
        if (item.Number != "home")
        {
            BreadcrumbItems.Add(item);
        }
    }

    /// <summary>
    /// 更新性能指标
    /// </summary>
    private void UpdatePerformanceMetrics()
    {
        var currentTime = _navigationStopwatch.ElapsedMilliseconds;

        // 更新性能指标
        PerformanceMetrics.AverageResponseTime =
            (PerformanceMetrics.AverageResponseTime * (PerformanceMetrics.TotalNavigations - 1) + currentTime) /
            Math.Max(1, PerformanceMetrics.TotalNavigations);

        // 更新最大和最小响应时间
        if (currentTime > PerformanceMetrics.MaxResponseTime)
            PerformanceMetrics.MaxResponseTime = currentTime;

        if (currentTime < PerformanceMetrics.MinResponseTime)
            PerformanceMetrics.MinResponseTime = currentTime;
    }

    /// <summary>
    /// 初始化代码示例
    /// </summary>
    private void InitializeCodeExamplesFromFiles()
    {
        try
        {
            // 获取代码示例文件的基础路径
            var baseDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
            var codeExamplesPath = Path.Combine(baseDirectory, "CodeExamples", "Navigation", "NavigationControl");

            // 读取高级导航示例
            XamlCodeExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Advanced.xaml.txt"));
            CSharpCodeExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Advanced.cs.txt"));

            _logger.Info("高级导航代码示例加载完成");
        }
        catch (Exception ex)
        {
            _logger.Error($"加载高级导航代码示例失败: {ex.Message}");

            // 如果文件读取失败，使用默认示例
            SetDefaultAdvancedCodeExamples();
        }
    }

    /// <summary>
    /// 读取代码示例文件
    /// </summary>
    private string ReadCodeExampleFile(string filePath)
    {
        try
        {
            if (File.Exists(filePath))
            {
                return File.ReadAllText(filePath);
            }
            else
            {
                _logger.Warning($"代码示例文件不存在: {filePath}");
                return $"<!-- 文件不存在: {Path.GetFileName(filePath)} -->";
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"读取代码示例文件失败: {ex.Message}");
            return $"<!-- 读取失败: {Path.GetFileName(filePath)} -->";
        }
    }

    /// <summary>
    /// 设置默认高级代码示例（当文件读取失败时使用）
    /// </summary>
    private void SetDefaultAdvancedCodeExamples()
    {
        XamlCodeExample = @"<!-- 高级NavigationControl配置 -->
<zylo:NavigationControl
    ItemsSource=""{Binding HierarchicalNavigationItems}""
    SelectedItem=""{Binding SelectedNavigationItem, Mode=TwoWay}""
    NavigationItemSelectedCommand=""{Binding NavigationCommand}""
    ShowSubMenuOnClick=""{Binding ShowSubMenuOnClick}""
    IsCollapsed=""{Binding IsNavigationCollapsed}""
    Background=""Transparent""
    Margin=""10"">

    <!-- 自定义样式 -->
    <zylo:NavigationControl.ItemContainerStyle>
        <Style TargetType=""TreeViewItem"">
            <Setter Property=""IsExpanded"" Value=""True""/>
            <Setter Property=""FontWeight"" Value=""Normal""/>
            <Style.Triggers>
                <Trigger Property=""IsSelected"" Value=""True"">
                    <Setter Property=""FontWeight"" Value=""Bold""/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </zylo:NavigationControl.ItemContainerStyle>
</zylo:NavigationControl>

<!-- 面包屑导航 -->
<ui:BreadcrumbBar ItemsSource=""{Binding BreadcrumbItems}""
                  Visibility=""{Binding ShowBreadcrumb, Converter={StaticResource BooleanToVisibilityConverter}}""/>

<!-- 性能监控面板 -->
<ui:Card Margin=""10"">
    <StackPanel>
        <TextBlock Text=""性能监控"" FontWeight=""Bold"" Margin=""0,0,0,10""/>
        <TextBlock Text=""{Binding NavigationCount, StringFormat='导航次数: {0}'}""/>
        <TextBlock Text=""{Binding AverageResponseTime, StringFormat='平均响应时间: {0:F2}ms'}""/>
        <TextBlock Text=""{Binding NavigationLevel, StringFormat='当前层级: {0}'}""/>
        <TextBlock Text=""{Binding ChildrenCount, StringFormat='子项数量: {0}'}""/>
    </StackPanel>
</ui:Card>";

        // C#代码示例 - 高级ViewModel实现
        CSharpCodeExample = @"using System.Collections.ObjectModel;
using System.Diagnostics;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Prism.Regions;
using Zylo.WPF.Models.Navigation;

// 高级NavigationControl ViewModel实现
public partial class NavigationAdvancedExampleViewModel : ObservableObject
{
    private readonly IRegionManager _regionManager;
    private readonly Stopwatch _navigationStopwatch = new();

    // 层级导航项集合
    [ObservableProperty]
    private ObservableCollection<ZyloNavigationItemModel> hierarchicalNavigationItems = new();

    // 面包屑导航项
    [ObservableProperty]
    private ObservableCollection<ZyloNavigationItemModel> breadcrumbItems = new();

    // 性能监控属性
    [ObservableProperty]
    private int navigationCount = 0;

    [ObservableProperty]
    private double averageResponseTime = 0;

    // 高级导航命令处理
    [RelayCommand]
    private void Navigation(ZyloNavigationItemModel? item)
    {
        if (item == null) return;

        try
        {
            _navigationStopwatch.Restart();

            SelectedNavigationItem = item;
            UpdateNavigationInfo(item);
            UpdateBreadcrumb(item);

            // Prism 区域导航
            _regionManager.RequestNavigate(""NavigationContentRegion"", item.ViewName);

            _navigationStopwatch.Stop();
            UpdatePerformanceMetrics();
        }
        catch (Exception ex)
        {
            _logger.Error($""❌ 高级导航失败: {ex.Message}"");
        }
    }

    // 初始化层级导航结构
    private void InitializeHierarchicalNavigationItems()
    {
        var businessItem = new ZyloNavigationItemModel(""business"", ""💼 业务管理"", ""BusinessView"");

        // 添加业务子项
        businessItem.Children.Add(new ZyloNavigationItemModel(""orders"", ""📋 订单管理"", ""OrdersView""));
        businessItem.Children.Add(new ZyloNavigationItemModel(""products"", ""📦 产品管理"", ""ProductsView""));
        businessItem.Children.Add(new ZyloNavigationItemModel(""customers"", ""👥 客户管理"", ""CustomersView""));

        var systemItem = new ZyloNavigationItemModel(""system"", ""⚙️ 系统管理"", ""SystemView"");

        // 添加系统子项
        systemItem.Children.Add(new ZyloNavigationItemModel(""users"", ""👤 用户管理"", ""UserManagementView""));
        systemItem.Children.Add(new ZyloNavigationItemModel(""roles"", ""🔐 角色管理"", ""RoleManagementView""));
        systemItem.Children.Add(new ZyloNavigationItemModel(""logs"", ""📊 日志管理"", ""LogManagementView""));

        HierarchicalNavigationItems.Add(businessItem);
        HierarchicalNavigationItems.Add(systemItem);
    }


}";
    }

    /// <summary>
    /// 检查导航权限
    /// </summary>
    private bool CheckNavigationPermission(ZyloNavigationItemModel item)
    {
        // 根据权限级别和项目类型检查权限
        return PermissionLevel switch
        {
            "Admin" => true, // 管理员可以访问所有项目
            "User" => !item.Name.Contains("管理") && !item.Name.Contains("设置"), // 用户不能访问管理和设置
            "Guest" => item.Name.Contains("首页") || item.Name.Contains("关于"), // 访客只能访问首页和关于
            _ => false
        };
    }

    /// <summary>
    /// 添加到导航历史
    /// </summary>
    private void AddToNavigationHistory(ZyloNavigationItemModel item)
    {
        // 避免重复添加相同项目
        if (NavigationHistory.LastOrDefault()?.Number != item.Number)
        {
            NavigationHistory.Add(item);

            // 限制历史记录数量
            while (NavigationHistory.Count > MaxHistoryCount)
            {
                NavigationHistory.RemoveAt(0);
            }
        }
    }

    /// <summary>
    /// 更新导航路径
    /// </summary>
    private void UpdateNavigationPath(ZyloNavigationItemModel item)
    {
        var path = ZyloNavigationItemModel.GetItemPath(HierarchicalNavigationItems, item, " → ");
        CurrentNavigationPath = string.IsNullOrEmpty(path) ? item.Name : path;
    }

    /// <summary>
    /// 执行 Prism 区域导航
    /// </summary>
    private void PerformPrismNavigation(ZyloNavigationItemModel item)
    {
        try
        {
            if (!string.IsNullOrEmpty(item.NavigationTarget))
            {
                // 这里可以实现真正的 Prism 区域导航
                // _regionManager.RequestNavigate("NavigationContentRegion", item.NavigationTarget);
                _logger.Info($"🎯 Prism 导航到: {item.NavigationTarget}");
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ Prism 导航失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 递归搜索导航项
    /// </summary>
    private List<ZyloNavigationItemModel> SearchItemsRecursively(ObservableCollection<ZyloNavigationItemModel> items, string keyword)
    {
        var results = new List<ZyloNavigationItemModel>();

        foreach (var item in items)
        {
            // 检查当前项目是否匹配
            if (item.Name.ToLower().Contains(keyword) ||
                item.NavigationTarget.ToLower().Contains(keyword))
            {
                results.Add(item);
            }

            // 递归搜索子项目
            if (item.Children?.Count > 0)
            {
                results.AddRange(SearchItemsRecursively(item.Children, keyword));
            }
        }

        return results;
    }

    /// <summary>
    /// 应用权限过滤
    /// </summary>
    private void ApplyPermissionFilter()
    {
        // 这里可以根据权限级别重新初始化导航项
        InitializeHierarchicalNavigationItems();

        // 过滤掉没有权限的项目
        var filteredItems = new List<ZyloNavigationItemModel>();
        foreach (var item in HierarchicalNavigationItems.ToList())
        {
            if (CheckNavigationPermission(item))
            {
                filteredItems.Add(item);
            }
            else
            {
                HierarchicalNavigationItems.Remove(item);
            }
        }
    }

    /// <summary>
    /// 分析用户行为并生成推荐
    /// </summary>
    private List<ZyloNavigationItemModel> AnalyzeUserBehaviorAndRecommend()
    {
        var recommendations = new List<ZyloNavigationItemModel>();

        // 模拟基于用户行为的智能推荐算法
        var mostVisited = NavigationHistory.GroupBy(item => item.NavigationTarget)
            .OrderByDescending(g => g.Count())
            .Take(3)
            .Select(g => g.Key);

        foreach (var target in mostVisited)
        {
            var relatedItems = FindRelatedNavigationItems(target);
            recommendations.AddRange(relatedItems.Take(2));
        }

        // 添加基于时间的推荐（工作时间推荐工作相关功能）
        var currentHour = DateTime.Now.Hour;
        if (currentHour >= 9 && currentHour <= 17)
        {
            recommendations.Add(new ZyloNavigationItemModel("work_dashboard", "📊 工作仪表板", "WorkDashboard")
            {
                ZyloSymbol = ZyloSymbol.Project
            });
        }

        return recommendations.Distinct().Take(5).ToList();
    }

    /// <summary>
    /// 执行行为分析
    /// </summary>
    private string PerformBehaviorAnalysis()
    {
        var analysis = new StringBuilder();

        // 分析导航模式
        var totalNavigations = NavigationHistory.Count;
        var uniquePages = NavigationHistory.Select(h => h.NavigationTarget).Distinct().Count();
        var averageSessionLength = totalNavigations > 0 ? NavigationHistory.Count / Math.Max(1, totalNavigations / 10) : 0;

        analysis.AppendLine($"📊 导航行为分析报告");
        analysis.AppendLine($"总导航次数: {totalNavigations}");
        analysis.AppendLine($"访问页面数: {uniquePages}");
        analysis.AppendLine($"平均会话长度: {averageSessionLength:F1}");

        // 分析最常用功能
        var topPages = NavigationHistory.GroupBy(h => h.Name)
            .OrderByDescending(g => g.Count())
            .Take(3)
            .Select(g => $"{g.Key} ({g.Count()}次)");

        analysis.AppendLine($"最常用功能: {string.Join(", ", topPages)}");

        // 分析使用时间模式
        var hourlyUsage = NavigationHistory.GroupBy(h => DateTime.Now.Hour)
            .OrderByDescending(g => g.Count())
            .FirstOrDefault();

        if (hourlyUsage != null)
        {
            analysis.AppendLine($"活跃时段: {hourlyUsage.Key}:00-{hourlyUsage.Key + 1}:00");
        }

        return analysis.ToString();
    }

    /// <summary>
    /// 更新导航热力图
    /// </summary>
    private void UpdateNavigationHeatMap()
    {
        NavigationHeatMap.Clear();

        foreach (var group in NavigationHistory.GroupBy(h => h.NavigationTarget))
        {
            NavigationHeatMap[group.Key] = group.Count();
        }
    }

    /// <summary>
    /// 执行全局搜索
    /// </summary>
    private List<SearchResult> ExecuteGlobalSearch(string keyword)
    {
        var results = new List<SearchResult>();
        var lowerKeyword = keyword.ToLower();

        // 搜索导航项
        foreach (var item in GetAllNavigationItemsFlat())
        {
            var relevance = CalculateRelevance(item, lowerKeyword);
            if (relevance > 0)
            {
                results.Add(new SearchResult
                {
                    Title = item.Name,
                    Description = $"导航到 {item.Name}",
                    Category = "导航",
                    NavigationTarget = item.NavigationTarget,
                    RelevanceScore = relevance,
                    LastAccessed = DateTime.Now.AddDays(-Random.Shared.Next(0, 30))
                });
            }
        }

        // 模拟搜索页面内容
        var contentResults = new[]
        {
            new SearchResult { Title = "用户管理指南", Description = "如何管理系统用户", Category = "帮助", RelevanceScore = 0.8 },
            new SearchResult { Title = "系统设置说明", Description = "系统配置相关说明", Category = "文档", RelevanceScore = 0.7 },
            new SearchResult { Title = "数据报表功能", Description = "生成和查看数据报表", Category = "功能", RelevanceScore = 0.6 }
        };

        results.AddRange(contentResults.Where(r =>
            r.Title.ToLower().Contains(lowerKeyword) ||
            r.Description.ToLower().Contains(lowerKeyword)));

        return results;
    }

    /// <summary>
    /// 查找相关导航项
    /// </summary>
    private List<ZyloNavigationItemModel> FindRelatedNavigationItems(string target)
    {
        var related = new List<ZyloNavigationItemModel>();
        var allItems = GetAllNavigationItemsFlat();

        // 简单的相关性算法：同类别或相似名称
        foreach (var item in allItems)
        {
            if (item.NavigationTarget != target &&
                (item.NavigationTarget.Contains(target.Split('.')[0]) ||
                 item.Name.Contains(target.Split('.')[0])))
            {
                related.Add(item);
            }
        }

        return related;
    }

    /// <summary>
    /// 获取所有导航项的扁平列表
    /// </summary>
    private List<ZyloNavigationItemModel> GetAllNavigationItemsFlat()
    {
        var allItems = new List<ZyloNavigationItemModel>();

        foreach (var item in HierarchicalNavigationItems)
        {
            allItems.Add(item);
            AddChildrenRecursively(item, allItems);
        }

        return allItems;
    }

    /// <summary>
    /// 递归添加子项
    /// </summary>
    private void AddChildrenRecursively(ZyloNavigationItemModel parent, List<ZyloNavigationItemModel> list)
    {
        if (parent.Children == null) return;

        foreach (var child in parent.Children)
        {
            list.Add(child);
            AddChildrenRecursively(child, list);
        }
    }

    /// <summary>
    /// 计算搜索相关性
    /// </summary>
    private double CalculateRelevance(ZyloNavigationItemModel item, string keyword)
    {
        var name = item.Name.ToLower();
        var target = item.NavigationTarget.ToLower();

        if (name == keyword) return 1.0;
        if (name.StartsWith(keyword)) return 0.9;
        if (name.Contains(keyword)) return 0.7;
        if (target.Contains(keyword)) return 0.5;

        return 0;
    }

    /// <summary>
    /// 应用基于角色的导航
    /// </summary>
    private void ApplyRoleBasedNavigation(string role)
    {
        // 根据角色过滤导航项
        InitializeHierarchicalNavigationItems();

        // 简单的角色过滤逻辑
        var itemsToRemove = new List<ZyloNavigationItemModel>();

        foreach (var item in HierarchicalNavigationItems.ToList())
        {
            if (!IsItemAccessibleForRole(item, role))
            {
                itemsToRemove.Add(item);
            }
        }

        foreach (var item in itemsToRemove)
        {
            HierarchicalNavigationItems.Remove(item);
        }
    }

    /// <summary>
    /// 检查项目是否对角色可访问
    /// </summary>
    private bool IsItemAccessibleForRole(ZyloNavigationItemModel item, string role)
    {
        return role switch
        {
            "Administrator" => true,
            "Manager" => !item.Name.Contains("系统") || item.Name.Contains("报表"),
            "User" => !item.Name.Contains("管理") && !item.Name.Contains("系统"),
            "Guest" => item.Name.Contains("首页") || item.Name.Contains("帮助"),
            _ => false
        };
    }

    /// <summary>
    /// 应用主题
    /// </summary>
    private void ApplyTheme(string theme)
    {
        // 这里可以实现主题切换逻辑
        // 在实际应用中，这里会更新应用程序的主题资源
        _logger.Info($"🎨 应用主题: {theme}");
    }

    /// <summary>
    /// 应用语言
    /// </summary>
    private void ApplyLanguage(string language)
    {
        // 这里可以实现语言切换逻辑
        // 在实际应用中，这里会更新应用程序的本地化资源
        var culture = new CultureInfo(language);
        CultureInfo.CurrentCulture = culture;
        CultureInfo.CurrentUICulture = culture;

        _logger.Info($"🌐 应用语言: {language}");
    }

    #endregion
}

/// <summary>
/// 导航性能指标数据模型
/// </summary>
public partial class NavigationPerformanceMetrics : ObservableObject
{
    [ObservableProperty]
    private int totalNavigations = 0;

    [ObservableProperty]
    private double averageResponseTime = 0;

    [ObservableProperty]
    private double maxResponseTime = 0;

    [ObservableProperty]
    private double minResponseTime = double.MaxValue;

    [ObservableProperty]
    private int slowNavigationsCount = 0;

    [ObservableProperty]
    private string mostVisitedPage = "无";

    [ObservableProperty]
    private DateTime lastAnalysisTime = DateTime.Now;
}

/// <summary>
/// 全局搜索结果数据模型
/// </summary>
public partial class SearchResult : ObservableObject
{
    [ObservableProperty]
    private string title = string.Empty;

    [ObservableProperty]
    private string description = string.Empty;

    [ObservableProperty]
    private string category = string.Empty;

    [ObservableProperty]
    private string navigationTarget = string.Empty;

    [ObservableProperty]
    private double relevanceScore = 0;

    [ObservableProperty]
    private DateTime lastAccessed = DateTime.Now;
}
