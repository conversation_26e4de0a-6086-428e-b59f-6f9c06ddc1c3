<UserControl
    d:DataContext="{d:DesignInstance Type=vm:DwgFolderEditorViewModel}"
    d:DesignHeight="600"
    d:DesignWidth="1000"
    mc:Ignorable="d"
    mvvm:ViewModelLocator.AutoWireViewModel="True"
    x:Class="WPFTest.Views.DWG.DwgFolderEditorView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:behaviors="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:behaviors1="clr-namespace:Zylo.WPF.Behaviors;assembly=Zylo.WPF"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dd="urn:gong-wpf-dragdrop"
    xmlns:listView="clr-namespace:Zylo.WPF.Behaviors.ListView;assembly=Zylo.WPF"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:mvvm="http://prismlibrary.com/"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:vm="clr-namespace:WPFTest.ViewModels.DWG"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:zylo="clr-namespace:Zylo.WPF.Controls;assembly=Zylo.WPF">

    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  工具栏  -->
        <Border
            Background="{DynamicResource ControlFillColorDefaultBrush}"
            Grid.Row="0"
            Padding="16,12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <StackPanel
                    Grid.Column="0"
                    Orientation="Horizontal"
                    VerticalAlignment="Center">
                    <ui:SymbolIcon
                        FontSize="20"
                        Margin="0,0,8,0"
                        Symbol="Folder24" />
                    <TextBlock
                        FontSize="16"
                        FontWeight="SemiBold"
                        Text="DWG文件夹管理" />
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <ui:Button
                        Command="{Binding LoadDataCommand}"
                        Content="🔄 刷新"
                        Margin="0,0,8,0" />
                    <!--  <ui:Button Content="🔍 扫描路径" Command="{Binding ScanPathCommand}"  -->
                    <!--  Appearance="Primary"  -->
                    <!--  d:Foreground="White"/>  -->
                </StackPanel>
            </Grid>
        </Border>

        <!--  主内容  -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300" />
                <ColumnDefinition Width="4" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <!--  左侧：列表  -->
            <Grid Grid.Column="0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <!--  搜索框  -->
                <ui:TextBox
                    Grid.Row="0"
                    Margin="8"
                    PlaceholderText="搜索..."
                    Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}">
                    <ui:TextBox.Icon>
                        <ui:SymbolIcon Symbol="Search24" />
                    </ui:TextBox.Icon>
                </ui:TextBox>

                <!--  文件夹列表  -->
                <ui:ListView
                    Background="Transparent"
                    BorderThickness="0"
                    Grid.Row="1"
                    ItemsSource="{Binding DisplayFolders}"
                    Margin="8,0,8,8"
                    ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                    SelectedItem="{Binding SelectedFolder}"
                    dd:DragDrop.DropHandler="{Binding}"
                    dd:DragDrop.IsDragSource="True"
                    dd:DragDrop.IsDropTarget="True"
                    dd:DragDrop.UseDefaultDragAdorner="False">
                    <behaviors:Interaction.Behaviors>
                        <listView:AutoEditOnSelectionBehavior
                            DelayMilliseconds="200"
                            EditCommand="{Binding EditFolderCommand}"
                            IsEnabled="True" />
                    </behaviors:Interaction.Behaviors>
                    <ui:ListView.ItemTemplate>
                        <DataTemplate>
                            <Grid Margin="5,1" MinHeight="32">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="28" />
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>

                                <!--  序号显示  -->
                                <TextBlock
                                    FontSize="12"
                                    Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                    Grid.Column="0"
                                    HorizontalAlignment="Center"
                                    Margin="0,0,8,0"
                                    MinWidth="20"
                                    Text="{Binding SortOrder}"
                                    VerticalAlignment="Center" />

                                <!--  图标区域 - 独立控件，精确对齐  -->
                                <Border
                                    Grid.Column="1"
                                    Height="24"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Width="24">
                                    <TextBlock
                                        FontSize="16"
                                        HorizontalAlignment="Center"
                                        Text="{Binding Icon}"
                                        TextAlignment="Center"
                                        VerticalAlignment="Center" />
                                </Border>

                                <!--  文字区域 - 独立控件，垂直居中  -->
                                <Border
                                    Grid.Column="2"
                                    Margin="8,0,0,0"
                                    VerticalAlignment="Center">
                                    <TextBlock
                                        FontSize="14"
                                        FontWeight="SemiBold"
                                        Text="{Binding Name}"
                                        TextTrimming="CharacterEllipsis"
                                        VerticalAlignment="Center" />
                                </Border>

                                <!--  状态指示器 - 精确定位  -->
                                <Border
                                    Grid.Column="3"
                                    Height="12"
                                    HorizontalAlignment="Center"
                                    Margin="8,0,4,0"
                                    VerticalAlignment="Center"
                                    Width="12">
                                    <Ellipse Height="8" Width="8">
                                        <Ellipse.Style>
                                            <Style TargetType="Ellipse">
                                                <Setter Property="Fill" Value="{DynamicResource SystemAccentColorSecondaryBrush}" />
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsDefault}" Value="True">
                                                        <Setter Property="Fill" Value="{DynamicResource SystemAccentColorPrimaryBrush}" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding IsEnabled}" Value="False">
                                                        <Setter Property="Fill" Value="{DynamicResource TextFillColorDisabledBrush}" />
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Ellipse.Style>
                                    </Ellipse>
                                </Border>
                            </Grid>
                        </DataTemplate>
                    </ui:ListView.ItemTemplate>
                </ui:ListView>
            </Grid>


            <!--  分隔线  -->
            <GridSplitter Background="{DynamicResource ControlStrokeColorDefaultBrush}" Grid.Column="1" />

            <!--  右侧：编辑表单  -->
            <Border
                Background="{DynamicResource ControlFillColorDefaultBrush}"
                Grid.Column="2"
                Padding="24">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <!--  表单标题  -->
                        <Grid Margin="0,0,0,24">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <TextBlock
                                FontSize="18"
                                FontWeight="SemiBold"
                                Grid.Column="0"
                                Text="文件夹编辑" />

                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                <ui:Button
                                    Appearance="Primary"
                                    Command="{Binding NewFolderCommand}"
                                    Content="➕ 新建"
                                    Margin="0,0,8,0"
                                    d:Foreground="White" />
                                <ui:Button
                                    Command="{Binding EditFolderCommand}"
                                    Content="✏️ 编辑"
                                    Margin="0,0,8,0" />
                                <ui:Button
                                    Appearance="Danger"
                                    Command="{Binding DeleteCommand}"
                                    Content="🗑️ 删除"
                                    d:Foreground="White" />
                            </StackPanel>
                        </Grid>

                        <!--  路径扫描功能  -->
                        <!-- <ui:Card Margin="0,0,0,16"> -->
                        <!--     <Grid Margin="16"> -->
                        <!--         <Grid.RowDefinitions> -->
                        <!--             <RowDefinition Height="Auto"/> -->
                        <!--             <RowDefinition Height="Auto"/> -->
                        <!--             <RowDefinition Height="Auto"/> -->
                        <!--         </Grid.RowDefinitions> -->
                        <!--    -->
                        <!--         <TextBlock Grid.Row="0" Text="路径扫描" FontSize="14" FontWeight="SemiBold" Margin="0,0,0,8"/> -->
                        <!--    -->
                        <!--         <Grid Grid.Row="1"> -->
                        <!--             <Grid.ColumnDefinitions> -->
                        <!--                 <ColumnDefinition Width="*"/> -->
                        <!--                 <ColumnDefinition Width="Auto"/> -->
                        <!--             </Grid.ColumnDefinitions> -->
                        <!--    -->
                        <!--  <ui:TextBox Grid.Column="0"  -->
                        <!--  Text="{Binding ScanPath, UpdateSourceTrigger=PropertyChanged}"  -->
                        <!--  PlaceholderText="选择要扫描的文件夹路径"  -->
                        <!--  Margin="0,0,8,0"/>  -->
                        <!--  <ui:Button Grid.Column="1" Content="浏览"  -->
                        <!--  Command="{Binding SelectPathCommand}"  -->
                        <!--  Icon="{ui:SymbolIcon FolderOpen24}"/>  -->
                        <!--         </Grid> -->
                        <!--    -->
                        <!--         <StackPanel Grid.Row="2" Orientation="Horizontal" Margin="0,8,0,0"> -->
                        <!--  <ui:Button Content="开始扫描" Command="{Binding ScanPathCommand}"  -->
                        <!--  Icon="{ui:SymbolIcon Play24}" Appearance="Primary" Margin="0,0,8,0"/>  -->
                        <!--  <ui:ProgressRing IsIndeterminate="True" Width="16" Height="16"  -->
                        <!--  Visibility="{Binding IsScanning, Converter={StaticResource BooleanToVisibilityConverter}}"/>  -->
                        <!--         </StackPanel> -->
                        <!--     </Grid> -->
                        <!-- </ui:Card> -->

                        <!--  编辑表单  -->
                        <ui:Card
                            DataContext="{Binding EditingFolder}"
                            Margin="0,0,0,16"
                            Visibility="{Binding DataContext.IsEditing, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <Grid Margin="16">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="100" />
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="100" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>

                                <!--  文件夹名称  -->
                                <TextBlock
                                    Grid.Column="0"
                                    Grid.Row="0"
                                    Text="名称*"
                                    VerticalAlignment="Center" />
                                <ui:TextBox
                                    Grid.Column="1"
                                    Grid.Row="0"
                                    Margin="8,0"
                                    Text="{Binding Name, UpdateSourceTrigger=PropertyChanged}" />

                                <!--  图标选择器  -->
                                <TextBlock
                                    Grid.Column="2"
                                    Grid.Row="0"
                                    Text="图标*"
                                    VerticalAlignment="Center" />
                                <StackPanel
                                    Grid.Column="3"
                                    Grid.Row="0"
                                    Margin="8,0">
                                    <ui:Button
                                        Command="{Binding DataContext.SelectIconCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                        Content="{Binding Icon, StringFormat='{}{0} 点击选择图标'}"
                                        FontSize="14"
                                        HorizontalAlignment="Stretch"
                                        HorizontalContentAlignment="Left"
                                        Padding="12,8">
                                        <ui:Button.ToolTip>
                                            <ToolTip Content="点击切换到下一个图标" />
                                        </ui:Button.ToolTip>
                                    </ui:Button>
                                </StackPanel>

                                <!--  默认标记  -->
                                <TextBlock
                                    Grid.Column="0"
                                    Grid.Row="1"
                                    Margin="0,16,0,0"
                                    Text="默认"
                                    VerticalAlignment="Center" />
                                <ui:ToggleSwitch
                                    Grid.Column="1"
                                    Grid.Row="1"
                                    IsChecked="{Binding IsDefault}"
                                    Margin="8,16,8,0" />
                            </Grid>
                        </ui:Card>

                        <!--  操作按钮  -->
                        <StackPanel Orientation="Horizontal" Visibility="{Binding IsEditing, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <ui:Button
                                Appearance="Primary"
                                Command="{Binding SaveCommand}"
                                Icon="{ui:SymbolIcon Save24}"
                                Margin="0,0,8,0">
                                <ui:Button.Style>
                                    <Style BasedOn="{StaticResource {x:Type ui:Button}}" TargetType="ui:Button">
                                        <Setter Property="Content" Value="保存修改" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding IsNewMode}" Value="True">
                                                <Setter Property="Content" Value="确认添加" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </ui:Button.Style>
                            </ui:Button>
                            <ui:Button
                                Command="{Binding CancelCommand}"
                                Content="取消"
                                Icon="{ui:SymbolIcon Dismiss24}" />
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </Border>
        </Grid>

        <!--  状态栏  -->
        <Border
            Background="{DynamicResource ControlFillColorSecondaryBrush}"
            Grid.Row="2"
            Padding="16,8">
            <Grid>
                <TextBlock Text="{Binding StatusMessage}" VerticalAlignment="Center" />
                <ui:ProgressRing
                    Height="16"
                    HorizontalAlignment="Right"
                    IsIndeterminate="True"
                    Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                    Width="16" />
            </Grid>
        </Border>

        <!--  ZyloSnackbar 通知控件 - 优化显示效果  -->
        <zylo:ZyloSnackbar
            FontSize="14"
            Grid.Row="0"
            Grid.RowSpan="3"
            HorizontalAlignment="Stretch"
            Margin="12,0,12,12"
            ShowCloseButton="True"
            UseAccentColor="True"
            behaviors1:SnackbarServiceBehavior.AutoBind="True"
            x:Name="MainSnackbar" />
    </Grid>
</UserControl>
