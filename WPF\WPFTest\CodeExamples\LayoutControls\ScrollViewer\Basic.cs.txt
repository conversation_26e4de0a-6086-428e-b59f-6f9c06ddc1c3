// ScrollViewer C# 基础示例
// ScrollViewer 是 WPF 中的滚动容器控件，用于显示超出可视区域的内容

using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace WPFTest.Examples.LayoutControls
{
    public class ScrollViewerBasicExample
    {
        /// <summary>
        /// 创建基础垂直滚动 ScrollViewer
        /// </summary>
        public static ScrollViewer CreateVerticalScrollViewer()
        {
            var scrollViewer = new ScrollViewer
            {
                Height = 200,
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                HorizontalScrollBarVisibility = ScrollBarVisibility.Disabled,
                Background = new SolidColorBrush(Colors.LightBlue),
                Margin = new Thickness(8)
            };

            // 创建内容
            var content = new StackPanel
            {
                Margin = new Thickness(16)
            };

            content.Children.Add(new TextBlock
            {
                Text = "这是一个垂直滚动的示例。",
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 12)
            });

            content.Children.Add(new TextBlock
            {
                Text = "当内容超过容器高度时，会自动显示垂直滚动条。",
                Margin = new Thickness(0, 0, 0, 8)
            });

            // 添加多行内容以演示滚动
            for (int i = 1; i <= 15; i++)
            {
                content.Children.Add(new TextBlock
                {
                    Text = $"第 {i} 行内容",
                    Margin = new Thickness(0, 0, 0, 4)
                });
            }

            content.Children.Add(new Button
            {
                Content = "点击测试",
                Margin = new Thickness(0, 12, 0, 0),
                HorizontalAlignment = HorizontalAlignment.Left
            });

            scrollViewer.Content = content;
            return scrollViewer;
        }

        /// <summary>
        /// 创建水平滚动 ScrollViewer
        /// </summary>
        public static ScrollViewer CreateHorizontalScrollViewer()
        {
            var scrollViewer = new ScrollViewer
            {
                Height = 150,
                VerticalScrollBarVisibility = ScrollBarVisibility.Disabled,
                HorizontalScrollBarVisibility = ScrollBarVisibility.Auto,
                Background = new SolidColorBrush(Colors.LightGreen),
                Margin = new Thickness(8)
            };

            // 创建水平内容
            var content = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(16)
            };

            // 添加多个卡片以演示水平滚动
            var colors = new[] { Colors.Red, Colors.Blue, Colors.Green, Colors.Orange, Colors.Purple, Colors.Brown };
            for (int i = 1; i <= 6; i++)
            {
                var card = new Border
                {
                    Background = new SolidColorBrush(colors[i - 1]),
                    Width = 100,
                    Height = 80,
                    Margin = new Thickness(0, 0, i < 6 ? 8 : 0, 0),
                    CornerRadius = new CornerRadius(4)
                };

                card.Child = new TextBlock
                {
                    Text = $"卡片 {i}",
                    Foreground = Brushes.White,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center
                };

                content.Children.Add(card);
            }

            scrollViewer.Content = content;
            return scrollViewer;
        }

        /// <summary>
        /// 创建双向滚动 ScrollViewer
        /// </summary>
        public static ScrollViewer CreateBidirectionalScrollViewer()
        {
            var scrollViewer = new ScrollViewer
            {
                Height = 200,
                Width = 300,
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                HorizontalScrollBarVisibility = ScrollBarVisibility.Auto,
                Background = new SolidColorBrush(Colors.LightYellow),
                Margin = new Thickness(8)
            };

            // 创建网格内容
            var grid = new Grid
            {
                Margin = new Thickness(16)
            };

            // 设置列定义
            for (int i = 0; i < 6; i++)
            {
                grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(120) });
            }

            // 设置行定义
            for (int i = 0; i < 10; i++)
            {
                grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(40) });
            }

            // 添加网格内容
            var colors = new[] { Colors.Red, Colors.Blue, Colors.Green, Colors.Orange };
            for (int row = 0; row < 10; row++)
            {
                for (int col = 0; col < 6; col++)
                {
                    var cell = new Border
                    {
                        Background = new SolidColorBrush(colors[(row + col) % colors.Length]),
                        Margin = new Thickness(2)
                    };

                    cell.Child = new TextBlock
                    {
                        Text = $"{(char)('A' + col)}{row + 1}",
                        Foreground = Brushes.White,
                        HorizontalAlignment = HorizontalAlignment.Center,
                        VerticalAlignment = VerticalAlignment.Center
                    };

                    Grid.SetRow(cell, row);
                    Grid.SetColumn(cell, col);
                    grid.Children.Add(cell);
                }
            }

            scrollViewer.Content = grid;
            return scrollViewer;
        }

        /// <summary>
        /// 创建嵌套滚动 ScrollViewer
        /// </summary>
        public static ScrollViewer CreateNestedScrollViewer()
        {
            var outerScrollViewer = new ScrollViewer
            {
                Height = 200,
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                HorizontalScrollBarVisibility = ScrollBarVisibility.Disabled,
                Background = new SolidColorBrush(Colors.LightGray),
                Margin = new Thickness(8)
            };

            var outerContent = new StackPanel
            {
                Margin = new Thickness(16)
            };

            outerContent.Children.Add(new TextBlock
            {
                Text = "外层滚动区域",
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 8)
            });

            outerContent.Children.Add(new TextBlock
            {
                Text = "这是外层的一些内容。",
                Margin = new Thickness(0, 0, 0, 8)
            });

            // 创建内层滚动区域
            var innerScrollViewer = new ScrollViewer
            {
                Height = 80,
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                HorizontalScrollBarVisibility = ScrollBarVisibility.Disabled,
                Background = new SolidColorBrush(Colors.White),
                BorderBrush = new SolidColorBrush(Colors.Gray),
                BorderThickness = new Thickness(1),
                Margin = new Thickness(0, 0, 0, 8)
            };

            var innerContent = new StackPanel
            {
                Margin = new Thickness(8)
            };

            innerContent.Children.Add(new TextBlock
            {
                Text = "内层滚动区域",
                FontWeight = FontWeights.Medium,
                Margin = new Thickness(0, 0, 0, 4)
            });

            for (int i = 1; i <= 6; i++)
            {
                innerContent.Children.Add(new TextBlock
                {
                    Text = $"内层内容 {i}",
                    Margin = new Thickness(0, 0, 0, 2)
                });
            }

            innerScrollViewer.Content = innerContent;
            outerContent.Children.Add(innerScrollViewer);

            // 添加更多外层内容
            outerContent.Children.Add(new TextBlock
            {
                Text = "外层继续的内容。",
                Margin = new Thickness(0, 0, 0, 4)
            });

            for (int i = 1; i <= 5; i++)
            {
                outerContent.Children.Add(new TextBlock
                {
                    Text = $"更多外层内容 {i}",
                    Margin = new Thickness(0, 0, 0, 4)
                });
            }

            outerScrollViewer.Content = outerContent;
            return outerScrollViewer;
        }

        /// <summary>
        /// 演示滚动条可见性设置
        /// </summary>
        public static StackPanel CreateScrollBarVisibilityDemo()
        {
            var container = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(8)
            };

            // Auto 模式
            var autoScrollViewer = CreateScrollViewerWithVisibility(
                "Auto 模式", 
                ScrollBarVisibility.Auto, 
                ScrollBarVisibility.Auto,
                Colors.LightBlue);
            container.Children.Add(autoScrollViewer);

            // Visible 模式
            var visibleScrollViewer = CreateScrollViewerWithVisibility(
                "Visible 模式", 
                ScrollBarVisibility.Visible, 
                ScrollBarVisibility.Visible,
                Colors.LightGreen);
            container.Children.Add(visibleScrollViewer);

            // Hidden 模式
            var hiddenScrollViewer = CreateScrollViewerWithVisibility(
                "Hidden 模式", 
                ScrollBarVisibility.Hidden, 
                ScrollBarVisibility.Hidden,
                Colors.LightYellow);
            container.Children.Add(hiddenScrollViewer);

            return container;
        }

        /// <summary>
        /// 辅助方法：创建指定可见性的 ScrollViewer
        /// </summary>
        private static StackPanel CreateScrollViewerWithVisibility(
            string title, 
            ScrollBarVisibility verticalVisibility, 
            ScrollBarVisibility horizontalVisibility,
            Color backgroundColor)
        {
            var container = new StackPanel
            {
                Margin = new Thickness(0, 0, 8, 0)
            };

            container.Children.Add(new TextBlock
            {
                Text = title,
                FontWeight = FontWeights.Medium,
                Margin = new Thickness(0, 0, 0, 4)
            });

            var scrollViewer = new ScrollViewer
            {
                Height = 100,
                Width = 150,
                VerticalScrollBarVisibility = verticalVisibility,
                HorizontalScrollBarVisibility = horizontalVisibility,
                Background = new SolidColorBrush(backgroundColor),
                BorderBrush = new SolidColorBrush(Colors.Gray),
                BorderThickness = new Thickness(1)
            };

            var content = new StackPanel
            {
                Margin = new Thickness(8)
            };

            for (int i = 1; i <= 6; i++)
            {
                content.Children.Add(new TextBlock
                {
                    Text = $"内容 {i}"
                });
            }

            scrollViewer.Content = content;
            container.Children.Add(scrollViewer);

            return container;
        }

        /// <summary>
        /// 演示滚动控制方法
        /// </summary>
        public static void DemonstrateScrollControl(ScrollViewer scrollViewer)
        {
            // 滚动到顶部
            scrollViewer.ScrollToTop();

            // 滚动到底部
            scrollViewer.ScrollToBottom();

            // 滚动到左端
            scrollViewer.ScrollToLeftEnd();

            // 滚动到右端
            scrollViewer.ScrollToRightEnd();

            // 滚动到指定位置
            scrollViewer.ScrollToVerticalOffset(100);
            scrollViewer.ScrollToHorizontalOffset(50);

            // 按行滚动
            scrollViewer.LineUp();
            scrollViewer.LineDown();
            scrollViewer.LineLeft();
            scrollViewer.LineRight();

            // 按页滚动
            scrollViewer.PageUp();
            scrollViewer.PageDown();
            scrollViewer.PageLeft();
            scrollViewer.PageRight();
        }

        /// <summary>
        /// 获取滚动信息
        /// </summary>
        public static void GetScrollInfo(ScrollViewer scrollViewer)
        {
            // 当前滚动位置
            double verticalOffset = scrollViewer.VerticalOffset;
            double horizontalOffset = scrollViewer.HorizontalOffset;

            // 可视区域大小
            double viewportWidth = scrollViewer.ViewportWidth;
            double viewportHeight = scrollViewer.ViewportHeight;

            // 内容总大小
            double extentWidth = scrollViewer.ExtentWidth;
            double extentHeight = scrollViewer.ExtentHeight;

            // 滚动条可见性
            bool canVerticallyScroll = scrollViewer.ComputedVerticalScrollBarVisibility == Visibility.Visible;
            bool canHorizontallyScroll = scrollViewer.ComputedHorizontalScrollBarVisibility == Visibility.Visible;

            // 输出信息
            System.Diagnostics.Debug.WriteLine($"垂直偏移: {verticalOffset}, 水平偏移: {horizontalOffset}");
            System.Diagnostics.Debug.WriteLine($"可视区域: {viewportWidth} x {viewportHeight}");
            System.Diagnostics.Debug.WriteLine($"内容大小: {extentWidth} x {extentHeight}");
            System.Diagnostics.Debug.WriteLine($"滚动条可见: 垂直={canVerticallyScroll}, 水平={canHorizontallyScroll}");
        }
    }
}
