<!-- 基础 MenuBar 使用示例 -->
<StackPanel Margin="20" Spacing="15">
    
    <!-- 标准 MenuBar -->
    <GroupBox Header="标准 MenuBar" Padding="15">
        <StackPanel>
            <!-- 基础菜单栏 -->
            <zylo:MenuBarControl MenuItems="{Binding BasicMenuItems}"
                                 MenuItemClickCommand="{Binding MenuItemClickCommand}"
                                 IsMenuEnabled="True"
                                 MenuHeight="40"
                                 Margin="0,0,0,10"/>
            
            <TextBlock Text="💡 基础菜单栏包含文件、编辑、帮助等常用菜单项" 
                       FontSize="12" 
                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
        </StackPanel>
    </GroupBox>
    
    <!-- 禁用状态 MenuBar -->
    <GroupBox Header="禁用状态 MenuBar" Padding="15">
        <StackPanel>
            <!-- 禁用的菜单栏 -->
            <zylo:MenuBarControl MenuItems="{Binding BasicMenuItems}"
                                 MenuItemClickCommand="{Binding MenuItemClickCommand}"
                                 IsMenuEnabled="False"
                                 MenuHeight="40"
                                 Margin="0,0,0,10"/>
            
            <TextBlock Text="💡 禁用状态的菜单栏，所有菜单项都不可点击" 
                       FontSize="12" 
                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
        </StackPanel>
    </GroupBox>
    
    <!-- 自定义高度 MenuBar -->
    <GroupBox Header="自定义高度 MenuBar" Padding="15">
        <StackPanel>
            <!-- 高度为50的菜单栏 -->
            <zylo:MenuBarControl MenuItems="{Binding BasicMenuItems}"
                                 MenuItemClickCommand="{Binding MenuItemClickCommand}"
                                 IsMenuEnabled="True"
                                 MenuHeight="50"
                                 Margin="0,0,0,10"/>
            
            <TextBlock Text="💡 自定义菜单栏高度为50像素，适合大屏幕应用" 
                       FontSize="12" 
                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
        </StackPanel>
    </GroupBox>
    
    <!-- 简化菜单 MenuBar -->
    <GroupBox Header="简化菜单 MenuBar" Padding="15">
        <StackPanel>
            <!-- 只有文件和帮助菜单 -->
            <zylo:MenuBarControl MenuItems="{Binding SimpleMenuItems}"
                                 MenuItemClickCommand="{Binding MenuItemClickCommand}"
                                 IsMenuEnabled="True"
                                 MenuHeight="40"
                                 Margin="0,0,0,10"/>
            
            <TextBlock Text="💡 简化版菜单栏，只包含最基本的文件和帮助菜单" 
                       FontSize="12" 
                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
        </StackPanel>
    </GroupBox>
    
    <!-- 响应式菜单栏 -->
    <GroupBox Header="响应式菜单栏" Padding="15">
        <StackPanel>
            <!-- 响应式菜单栏 -->
            <Border BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="6">
                <zylo:MenuBarControl MenuItems="{Binding BasicMenuItems}"
                                     MenuItemClickCommand="{Binding MenuItemClickCommand}"
                                     IsMenuEnabled="True"
                                     MenuHeight="40"
                                     HorizontalAlignment="Stretch"/>
            </Border>
            
            <TextBlock Text="💡 响应式菜单栏，自动适应容器宽度" 
                       FontSize="12" 
                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                       Margin="0,10,0,0"/>
        </StackPanel>
    </GroupBox>
    
</StackPanel>
