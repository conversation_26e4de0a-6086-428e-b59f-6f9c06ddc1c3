<UserControl x:Class="WPFTest.Views.Navigation.NavigationAdvancedExample"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:zylo="clr-namespace:Zylo.WPF.Controls.Navigation;assembly=Zylo.WPF"
             xmlns:codeExample="clr-namespace:Zylo.WPF.Controls.CodeExample;assembly=Zylo.WPF"
             xmlns:prism="http://prismlibrary.com/"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200"
             prism:ViewModelLocator.AutoWireViewModel="True">
    
    <UserControl.Resources>
        <!-- 转换器 -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- 高级样式 -->
        <Style x:Key="AdvancedCardStyle" TargetType="ui:Card">
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="BorderBrush" Value="{DynamicResource SystemAccentColorSecondaryBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
        </Style>

        <!-- 导航区域样式 -->
        <Style x:Key="NavigationAreaStyle" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="15"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 标题区域 -->
        <ui:Card Grid.Row="0" Style="{StaticResource AdvancedCardStyle}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="🚀 NavigationControl 企业级高级功能"
                              FontSize="24"
                              FontWeight="Bold"
                              Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                              Margin="0,0,0,10"/>

                    <TextBlock Text="展示真正的企业级导航系统：智能推荐、行为分析、全局搜索、权限管理、多语言支持"
                              FontSize="14"
                              Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                              TextWrapping="Wrap"/>
                </StackPanel>

                <!-- 快速操作按钮 -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <ui:Button Content="🎯 智能推荐"
                              Command="{Binding GenerateSmartRecommendationsCommand}"
                              Margin="5,0"/>
                    <ui:Button Content="📊 行为分析"
                              Command="{Binding AnalyzeNavigationBehaviorCommand}"
                              Margin="5,0"/>
                </StackPanel>
            </Grid>
        </ui:Card>

        <!-- 主要内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="350"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧：高级NavigationControl演示 -->
            <ui:Card Grid.Column="0" Style="{StaticResource AdvancedCardStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 导航标题和状态 -->
                    <StackPanel Grid.Row="0" Margin="0,0,0,15">
                        <TextBlock Text="🌳 企业级导航菜单"
                                  FontSize="16"
                                  FontWeight="SemiBold"
                                  Margin="0,0,0,10"/>

                        <Border Style="{StaticResource NavigationAreaStyle}">
                            <StackPanel>
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                                    <ui:SymbolIcon Symbol="Person24" FontSize="16" Margin="0,0,5,0"/>
                                    <TextBlock Text="{Binding CurrentUserRole, StringFormat='当前角色: {0}'}"
                                              FontSize="12"/>
                                </StackPanel>
                                <StackPanel Orientation="Horizontal">
                                    <ui:SymbolIcon Symbol="Globe24" FontSize="16" Margin="0,0,5,0"/>
                                    <TextBlock Text="{Binding CurrentLanguage, StringFormat='语言: {0}'}"
                                              FontSize="12"/>
                                </StackPanel>
                            </StackPanel>
                        </Border>
                    </StackPanel>

                    <!-- 高级NavigationControl -->
                    <Border Grid.Row="1" Style="{StaticResource NavigationAreaStyle}">
                        <zylo:NavigationControl x:Name="AdvancedNavigationControl"
                                               ItemsSource="{Binding HierarchicalNavigationItems}"
                                               SelectedItem="{Binding SelectedNavigationItem, Mode=TwoWay}"
                                               NavigationItemSelectedCommand="{Binding NavigationCommand}"
                                               ShowSubMenuOnClick="True"
                                               Background="Transparent"/>
                    </Border>

                    <!-- 企业级功能面板 -->
                    <Expander Grid.Row="2"
                             Header="🚀 企业级功能"
                             Margin="0,15,0,0"
                             IsExpanded="False">
                        <StackPanel Margin="0,10,0,0">

                            <!-- 权限管理 -->
                            <TextBlock Text="🔐 权限管理:" FontWeight="SemiBold" Margin="0,5,0,5"/>
                            <ComboBox SelectedItem="{Binding CurrentUserRole, Mode=TwoWay}"
                                     ItemsSource="{Binding AvailableRoles}"
                                     Margin="0,0,0,10">
                                <ComboBox.ItemTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding}"/>
                                    </DataTemplate>
                                </ComboBox.ItemTemplate>
                            </ComboBox>

                            <!-- 主题切换 -->
                            <TextBlock Text="🎨 主题:" FontWeight="SemiBold" Margin="0,5,0,5"/>
                            <ComboBox SelectedItem="{Binding CurrentTheme, Mode=TwoWay}"
                                     ItemsSource="{Binding AvailableThemes}"
                                     Margin="0,0,0,10">
                                <ComboBox.ItemTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding}"/>
                                    </DataTemplate>
                                </ComboBox.ItemTemplate>
                            </ComboBox>

                            <!-- 语言切换 -->
                            <TextBlock Text="🌐 语言:" FontWeight="SemiBold" Margin="0,5,0,5"/>
                            <ComboBox SelectedItem="{Binding CurrentLanguage, Mode=TwoWay}"
                                     ItemsSource="{Binding AvailableLanguages}"
                                     Margin="0,0,0,10">
                                <ComboBox.ItemTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding}"/>
                                    </DataTemplate>
                                </ComboBox.ItemTemplate>
                            </ComboBox>
                        </StackPanel>
                    </Expander>
                </Grid>
            </ui:Card>

            <!-- 右侧：企业级高级功能展示 -->
            <Grid Grid.Column="1">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 全局搜索栏 -->
                <ui:Card Grid.Row="0" Style="{StaticResource AdvancedCardStyle}">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <ui:TextBox Grid.Column="0"
                                   PlaceholderText="🔍 全局搜索导航项、内容、文档..."
                                   Text="{Binding SearchKeyword, UpdateSourceTrigger=PropertyChanged}"
                                   Margin="0,0,10,0"/>

                        <ui:Button Grid.Column="1"
                                  Content="搜索"
                                  Command="{Binding PerformGlobalSearchCommand}"
                                  CommandParameter="{Binding SearchKeyword}"/>
                    </Grid>
                </ui:Card>

                <!-- 主内容区域 -->
                <ui:Card Grid.Row="1" Style="{StaticResource AdvancedCardStyle}">
                    <ScrollViewer>
                        <StackPanel>

                            <!-- 智能推荐面板 -->
                            <ui:Card Margin="0,0,0,15" Padding="15">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                                        <ui:SymbolIcon Symbol="Lightbulb24" FontSize="20" Margin="0,0,10,0"/>
                                        <TextBlock Text="🎯 智能推荐" FontSize="16" FontWeight="SemiBold" VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <ItemsControl ItemsSource="{Binding RecommendedItems}">
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <Border Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                                       CornerRadius="4"
                                                       Padding="8"
                                                       Margin="0,2">
                                                    <StackPanel Orientation="Horizontal">
                                                        <ui:SymbolIcon Symbol="Star24" FontSize="14" Margin="0,0,8,0"/>
                                                        <TextBlock Text="{Binding Name}" VerticalAlignment="Center"/>
                                                    </StackPanel>
                                                </Border>
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </StackPanel>
                            </ui:Card>

                            <!-- 行为分析面板 -->
                            <ui:Card Margin="0,0,0,15" Padding="15">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                                        <ui:SymbolIcon Symbol="DataBarVertical24" FontSize="20" Margin="0,0,10,0"/>
                                        <TextBlock Text="📊 导航行为分析" FontSize="16" FontWeight="SemiBold" VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <TextBlock Text="{Binding BehaviorAnalysisResult}"
                                              TextWrapping="Wrap"
                                              FontFamily="Consolas"
                                              Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                              Padding="10"
                                              FontSize="12"/>
                                </StackPanel>
                            </ui:Card>

                            <!-- 全局搜索结果面板 -->
                            <ui:Card Margin="0,0,0,15" Padding="15">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                                        <ui:SymbolIcon Symbol="Search24" FontSize="20" Margin="0,0,10,0"/>
                                        <TextBlock Text="🔍 搜索结果" FontSize="16" FontWeight="SemiBold" VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <ItemsControl ItemsSource="{Binding GlobalSearchResults}" MaxHeight="200">
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <Border Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                                       CornerRadius="4"
                                                       Padding="10"
                                                       Margin="0,2">
                                                    <StackPanel>
                                                        <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                                                            <TextBlock Text="{Binding Title}" FontWeight="SemiBold" Margin="0,0,10,0"/>
                                                            <TextBlock Text="{Binding Category}"
                                                                      FontSize="10"
                                                                      Background="{DynamicResource SystemAccentColorSecondaryBrush}"
                                                                      Padding="4,2"
                                                                      />
                                                        </StackPanel>
                                                        <TextBlock Text="{Binding Description}"
                                                                  FontSize="12"
                                                                  Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                                                        <TextBlock Text="{Binding RelevanceScore, StringFormat='相关性: {0:P0}'}"
                                                                  FontSize="10"
                                                                  Foreground="{DynamicResource TextFillColorTertiaryBrush}"/>
                                                    </StackPanel>
                                                </Border>
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </StackPanel>
                            </ui:Card>

                            <!-- 导航历史面板 -->
                            <ui:Card Margin="0,0,0,15" Padding="15">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                                        <ui:SymbolIcon Symbol="History24" FontSize="20" Margin="0,0,10,0"/>
                                        <TextBlock Text="📚 导航历史" FontSize="16" FontWeight="SemiBold" VerticalAlignment="Center"/>
                                        <ui:Button Content="清空"
                                                  Command="{Binding ClearNavigationHistoryCommand}"
                                                  Margin="10,0,0,0"
                                                  FontSize="12"/>
                                    </StackPanel>

                                    <ItemsControl ItemsSource="{Binding NavigationHistory}" MaxHeight="150">
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <Border Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                                       CornerRadius="4"
                                                       Padding="8"
                                                       Margin="0,2">
                                                    <StackPanel Orientation="Horizontal">
                                                        <ui:SymbolIcon Symbol="Clock24" FontSize="12" Margin="0,0,8,0"/>
                                                        <TextBlock Text="{Binding Name}" VerticalAlignment="Center"/>
                                                    </StackPanel>
                                                </Border>
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </StackPanel>
                            </ui:Card>

                            <!-- 性能监控面板 -->
                            <ui:Card Margin="0,0,0,15" Padding="15">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                                        <ui:SymbolIcon Symbol="Gauge24" FontSize="20" Margin="0,0,10,0"/>
                                        <TextBlock Text="⚡ 性能监控" FontSize="16" FontWeight="SemiBold" VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition/>
                                            <RowDefinition/>
                                            <RowDefinition/>
                                        </Grid.RowDefinitions>

                                        <TextBlock Grid.Row="0" Grid.Column="0" Text="总导航次数:" Margin="0,2"/>
                                        <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding PerformanceMetrics.TotalNavigations}" Margin="0,2"/>

                                        <TextBlock Grid.Row="1" Grid.Column="0" Text="平均响应时间:" Margin="0,2"/>
                                        <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding PerformanceMetrics.AverageResponseTime, StringFormat={}{0:F2}ms}" Margin="0,2"/>

                                        <TextBlock Grid.Row="2" Grid.Column="0" Text="最常访问:" Margin="0,2"/>
                                        <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding PerformanceMetrics.MostVisitedPage}" Margin="0,2"/>
                                    </Grid>
                                </StackPanel>
                            </ui:Card>

                            <!-- 企业级代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="NavigationControl 企业级高级功能"
                                Language="XAML"
                                Description="展示智能推荐、行为分析、全局搜索、权限管理等企业级功能的完整实现"
                                ShowTabs="True"
                                XamlCode="{Binding XamlCodeExample}"
                                CSharpCode="{Binding CSharpCodeExample}"
                                Margin="0,15,0,0"/>

                        </StackPanel>
                    </ScrollViewer>
                </ui:Card>
            </Grid>
        </Grid>
    </Grid>
</UserControl>
