using System.Windows;
using Wpf.Ui.Controls;
using Zylo.WPF.Controls;

namespace Zylo.WPF.Extensions;

/// <summary>
/// ZyloSnackbar 扩展方法
/// </summary>
public static class ZyloSnackbarExtensions
{
    /// <summary>
    /// 显示成功消息
    /// </summary>
    public static void ShowSuccess(this ZyloSnackbar snackbar, string message, string title = "操作成功", int timeout = 3000)
    {
        snackbar.Show(title, message, SymbolRegular.CheckmarkCircle24, ControlAppearance.Success, timeout);
    }

    /// <summary>
    /// 显示错误消息
    /// </summary>
    public static void ShowError(this ZyloSnackbar snackbar, string message, string title = "操作失败", int timeout = 5000)
    {
        snackbar.Show(title, message, SymbolRegular.ErrorCircle24, ControlAppearance.Danger, timeout);
    }

    /// <summary>
    /// 显示警告消息
    /// </summary>
    public static void ShowWarning(this ZyloSnackbar snackbar, string message, string title = "警告", int timeout = 4000)
    {
        snackbar.Show(title, message, SymbolRegular.Warning24, ControlAppearance.Caution, timeout);
    }

    /// <summary>
    /// 显示信息消息
    /// </summary>
    public static void ShowInfo(this ZyloSnackbar snackbar, string message, string title = "提示", int timeout = 3000)
    {
        snackbar.Show(title, message, SymbolRegular.Info24, ControlAppearance.Info, timeout);
    }

    /// <summary>
    /// 显示主要消息
    /// </summary>
    public static void ShowPrimary(this ZyloSnackbar snackbar, string message, string title = "消息", int timeout = 3000)
    {
        snackbar.Show(title, message, SymbolRegular.Info24, ControlAppearance.Primary, timeout);
    }

    /// <summary>
    /// 在指定容器中查找 ZyloSnackbar 控件
    /// </summary>
    public static ZyloSnackbar? FindSnackbar(this FrameworkElement container, string? name = null)
    {
        if (string.IsNullOrEmpty(name))
        {
            return FindVisualChild<ZyloSnackbar>(container);
        }
        
        return container.FindName(name) as ZyloSnackbar;
    }

    /// <summary>
    /// 查找可视化子控件
    /// </summary>
    private static T? FindVisualChild<T>(DependencyObject parent) where T : DependencyObject
    {
        for (int i = 0; i < System.Windows.Media.VisualTreeHelper.GetChildrenCount(parent); i++)
        {
            var child = System.Windows.Media.VisualTreeHelper.GetChild(parent, i);
            
            if (child is T result)
            {
                return result;
            }
            
            var childOfChild = FindVisualChild<T>(child);
            if (childOfChild != null)
            {
                return childOfChild;
            }
        }
        
        return null;
    }
}

/// <summary>
/// ZyloSnackbar 静态帮助类
/// </summary>
public static class ZyloSnackbarHelper
{
    /// <summary>
    /// 创建一个新的 ZyloSnackbar 实例
    /// </summary>
    public static ZyloSnackbar Create()
    {
        return new ZyloSnackbar();
    }

    /// <summary>
    /// 创建一个配置好的 ZyloSnackbar 实例
    /// </summary>
    public static ZyloSnackbar Create(string title, string message, 
        SymbolRegular icon = SymbolRegular.Info24, 
        ControlAppearance appearance = ControlAppearance.Primary,
        bool useAccentColor = true,
        bool showCloseButton = true,
        int timeout = 3000)
    {
        return new ZyloSnackbar
        {
            Title = title,
            Message = message,
            Icon = icon,
            Appearance = appearance,
            UseAccentColor = useAccentColor,
            ShowCloseButton = showCloseButton,
            Timeout = timeout
        };
    }

    /// <summary>
    /// 创建成功类型的 Snackbar
    /// </summary>
    public static ZyloSnackbar CreateSuccess(string message, string title = "操作成功", int timeout = 3000)
    {
        return Create(title, message, SymbolRegular.CheckmarkCircle24, ControlAppearance.Success, true, true, timeout);
    }

    /// <summary>
    /// 创建错误类型的 Snackbar
    /// </summary>
    public static ZyloSnackbar CreateError(string message, string title = "操作失败", int timeout = 5000)
    {
        return Create(title, message, SymbolRegular.ErrorCircle24, ControlAppearance.Danger, true, true, timeout);
    }

    /// <summary>
    /// 创建警告类型的 Snackbar
    /// </summary>
    public static ZyloSnackbar CreateWarning(string message, string title = "警告", int timeout = 4000)
    {
        return Create(title, message, SymbolRegular.Warning24, ControlAppearance.Caution, true, true, timeout);
    }

    /// <summary>
    /// 创建信息类型的 Snackbar
    /// </summary>
    public static ZyloSnackbar CreateInfo(string message, string title = "提示", int timeout = 3000)
    {
        return Create(title, message, SymbolRegular.Info24, ControlAppearance.Info, true, true, timeout);
    }
}
