using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Globalization;
using System.IO;
using System.Text.RegularExpressions;
using System.Windows.Media;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels.InputControls;

/// <summary>
/// NumberBox 控件示例页面 ViewModel
/// </summary>
public partial class NumberBoxPageViewModel : ObservableObject
{
    private readonly YLoggerInstance _logger = YLogger.For<PasswordBoxPageViewModel>();

    public NumberBoxPageViewModel()
    {
        // 初始化数据
        InitializeData();
        InitializeCodeExamples();

        // 设置属性变化监听
        SetupPropertyChangeHandlers();

        _logger.Info("NumberBoxPageViewModel 初始化完成");
    }

    #region 基础属性

    /// <summary>
    /// 基础数值
    /// </summary>
    [ObservableProperty]
    private double basicValue = 0;

    /// <summary>
    /// 最小值
    /// </summary>
    [ObservableProperty]
    private double minimumValue = 0;

    /// <summary>
    /// 最大值
    /// </summary>
    [ObservableProperty]
    private double maximumValue = 100;

    /// <summary>
    /// 步进值
    /// </summary>
    [ObservableProperty]
    private double stepValue = 1;

    /// <summary>
    /// 小数位数
    /// </summary>
    [ObservableProperty]
    private int decimalPlaces = 2;

    /// <summary>
    /// 货币值
    /// </summary>
    [ObservableProperty]
    private double currencyValue = 1234.56;

    /// <summary>
    /// 百分比值
    /// </summary>
    [ObservableProperty]
    private double percentageValue = 0.75;

    /// <summary>
    /// 温度值
    /// </summary>
    [ObservableProperty]
    private double temperatureValue = 25.5;

    /// <summary>
    /// 重量值
    /// </summary>
    [ObservableProperty]
    private double weightValue = 68.5;

    /// <summary>
    /// 华氏度值（计算属性）
    /// </summary>
    public double FahrenheitValue => TemperatureValue * 9.0 / 5.0 + 32.0;

    /// <summary>
    /// 磅值（计算属性）
    /// </summary>
    public double PoundsValue => WeightValue * 2.20462;

    /// <summary>
    /// 状态消息
    /// </summary>
    [ObservableProperty]
    private string statusMessage = "准备就绪";

    /// <summary>
    /// 验证消息
    /// </summary>
    [ObservableProperty]
    private string validationMessage = string.Empty;

    /// <summary>
    /// 验证消息颜色
    /// </summary>
    [ObservableProperty]
    private Brush validationMessageColor = Brushes.Gray;

    #endregion

    #region 格式化属性

    /// <summary>
    /// 科学计数法值
    /// </summary>
    [ObservableProperty]
    private double scientificValue = 123456789;

    /// <summary>
    /// 当前数字格式
    /// </summary>
    [ObservableProperty]
    private string currentFormat = "N2";

    /// <summary>
    /// 格式化选项
    /// </summary>
    public ObservableCollection<NumberFormatItem> FormatOptions { get; } = new();

    /// <summary>
    /// 选中的格式项
    /// </summary>
    [ObservableProperty]
    private NumberFormatItem? selectedFormatItem;

    #endregion

    #region 数据验证属性

    /// <summary>
    /// 范围验证值
    /// </summary>
    [ObservableProperty]
    private double? rangeValidationValue;

    /// <summary>
    /// 范围验证消息
    /// </summary>
    [ObservableProperty]
    private string rangeValidationMessage = string.Empty;

    /// <summary>
    /// 范围验证颜色
    /// </summary>
    [ObservableProperty]
    private Brush rangeValidationColor = Brushes.Gray;

    /// <summary>
    /// 必填验证值
    /// </summary>
    [ObservableProperty]
    private double? requiredValue;

    /// <summary>
    /// 必填验证消息
    /// </summary>
    [ObservableProperty]
    private string requiredValidationMessage = string.Empty;

    /// <summary>
    /// 必填验证颜色
    /// </summary>
    [ObservableProperty]
    private Brush requiredValidationColor = Brushes.Gray;

    /// <summary>
    /// 自定义验证值
    /// </summary>
    [ObservableProperty]
    private double? customValidationValue;

    /// <summary>
    /// 自定义验证消息
    /// </summary>
    [ObservableProperty]
    private string customValidationMessage = string.Empty;

    /// <summary>
    /// 自定义验证颜色
    /// </summary>
    [ObservableProperty]
    private Brush customValidationColor = Brushes.Gray;

    /// <summary>
    /// 验证摘要
    /// </summary>
    [ObservableProperty]
    private string validationSummary = "等待验证...";

    #endregion

    #region 验证属性

    /// <summary>
    /// 验证结果
    /// </summary>
    [ObservableProperty]
    private NumberValidationResult validationResult = new();

    /// <summary>
    /// 是否启用范围验证
    /// </summary>
    [ObservableProperty]
    private bool enableRangeValidation = true;

    /// <summary>
    /// 是否启用格式验证
    /// </summary>
    [ObservableProperty]
    private bool enableFormatValidation = true;

    #endregion

    #region 代码示例属性

    /// <summary>
    /// 基础功能 XAML 示例
    /// </summary>
    [ObservableProperty]
    public partial string BasicXamlExample { get; set; } = string.Empty;

    /// <summary>
    /// 基础功能 C# 示例
    /// </summary>
    [ObservableProperty]
    public partial string BasicCSharpExample { get; set; } = string.Empty;

    /// <summary>
    /// 高级功能 XAML 示例
    /// </summary>
    [ObservableProperty]
    public partial string AdvancedXamlExample { get; set; } = string.Empty;

    /// <summary>
    /// 高级功能 C# 示例
    /// </summary>
    [ObservableProperty]
    public partial string AdvancedCSharpExample { get; set; } = string.Empty;

    /// <summary>
    /// 样式 XAML 示例
    /// </summary>
    [ObservableProperty]
    public partial string StylesXamlExample { get; set; } = string.Empty;

    /// <summary>
    /// 格式化 XAML 示例
    /// </summary>
    [ObservableProperty]
    public partial string FormattingXamlExample { get; set; } = string.Empty;

    /// <summary>
    /// 格式化 C# 示例
    /// </summary>
    [ObservableProperty]
    public partial string FormattingCSharpExample { get; set; } = string.Empty;

    /// <summary>
    /// 验证 XAML 示例
    /// </summary>
    [ObservableProperty]
    public partial string ValidationXamlExample { get; set; } = string.Empty;

    /// <summary>
    /// 验证 C# 示例
    /// </summary>
    [ObservableProperty]
    public partial string ValidationCSharpExample { get; set; } = string.Empty;

    #endregion

    #region 初始化方法

    /// <summary>
    /// 初始化数据
    /// </summary>
    private void InitializeData()
    {
        try
        {
            // 初始化格式化选项
            FormatOptions.Add(new NumberFormatItem("标准数字", "N2", "1,234.56"));
            FormatOptions.Add(new NumberFormatItem("货币", "C2", "¥1,234.56"));
            FormatOptions.Add(new NumberFormatItem("百分比", "P2", "123,456.00%"));
            FormatOptions.Add(new NumberFormatItem("科学计数法", "E2", "1.23E+003"));
            FormatOptions.Add(new NumberFormatItem("固定小数点", "F2", "1234.56"));
            FormatOptions.Add(new NumberFormatItem("十六进制", "X", "4D2"));

            SelectedFormatItem = FormatOptions.First();

            // 初始化验证结果
            ValidationResult = new NumberValidationResult();

            _logger.Info("数据初始化完成");
        }
        catch (Exception ex)
        {
            _logger.Error($"数据初始化失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 初始化代码示例
    /// </summary>
    private void InitializeCodeExamples()
    {
        try
        {
            LoadCodeExamples();
            _logger.Info("代码示例初始化完成");
        }
        catch (Exception ex)
        {
            _logger.Error($"代码示例初始化失败: {ex.Message}");
            SetDefaultCodeExamples();
        }
    }

    #endregion

    #region 命令

    /// <summary>
    /// 验证数值命令
    /// </summary>
    [RelayCommand]
    private void ValidateNumber()
    {
        try
        {
            var result = ValidateNumberValue(BasicValue);
            ValidationResult = result;
            
            if (result.IsValid)
            {
                ValidationMessage = "✅ 数值验证通过";
                ValidationMessageColor = new SolidColorBrush(Colors.Green);
                StatusMessage = $"数值 {BasicValue} 验证成功";
            }
            else
            {
                ValidationMessage = $"❌ {string.Join(", ", result.Errors)}";
                ValidationMessageColor = new SolidColorBrush(Colors.Red);
                StatusMessage = "数值验证失败";
            }

            HandleInteraction("验证数值");
        }
        catch (Exception ex)
        {
            ValidationMessage = $"❌ 验证过程出错: {ex.Message}";
            ValidationMessageColor = new SolidColorBrush(Colors.Red);
            _logger.Error($"数值验证失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 格式化数值命令
    /// </summary>
    [RelayCommand]
    private void FormatNumber()
    {
        try
        {
            if (SelectedFormatItem == null) return;

            var formattedValue = BasicValue.ToString(SelectedFormatItem.Format, CultureInfo.CurrentCulture);
            StatusMessage = $"格式化结果: {formattedValue}";
            
            HandleInteraction("格式化数值");
        }
        catch (Exception ex)
        {
            StatusMessage = $"格式化失败: {ex.Message}";
            _logger.Error($"数值格式化失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 重置数值命令
    /// </summary>
    [RelayCommand]
    private void ResetValues()
    {
        BasicValue = 0;
        CurrencyValue = 1234.56;
        PercentageValue = 0.75;
        TemperatureValue = 25.5;
        WeightValue = 68.5;
        ValidationMessage = string.Empty;
        StatusMessage = "数值已重置";
        
        HandleInteraction("重置数值");
    }

    /// <summary>
    /// 重置计数命令
    /// </summary>
    [RelayCommand]
    private void ResetCount()
    {
        // 这里可以添加计数重置逻辑
        StatusMessage = "计数已重置";
        _logger.Info("重置了交互计数");
    }

    /// <summary>
    /// 清除状态命令
    /// </summary>
    [RelayCommand]
    private void ClearStatus()
    {
        StatusMessage = "状态已清除";
        ValidationMessage = string.Empty;
        _logger.Info("清除了状态信息");
    }

    /// <summary>
    /// 验证所有命令
    /// </summary>
    [RelayCommand]
    private void ValidateAll()
    {
        try
        {
            var validationResults = new List<string>();

            // 范围验证
            if (RangeValidationValue.HasValue)
            {
                if (RangeValidationValue.Value >= 1 && RangeValidationValue.Value <= 100)
                {
                    RangeValidationMessage = "✅ 范围验证通过";
                    RangeValidationColor = new SolidColorBrush(Colors.Green);
                    validationResults.Add("范围验证: 通过");
                }
                else
                {
                    RangeValidationMessage = "❌ 数值必须在1-100之间";
                    RangeValidationColor = new SolidColorBrush(Colors.Red);
                    validationResults.Add("范围验证: 失败");
                }
            }
            else
            {
                RangeValidationMessage = "⚠️ 请输入数值";
                RangeValidationColor = new SolidColorBrush(Colors.Orange);
                validationResults.Add("范围验证: 未输入");
            }

            // 必填验证
            if (RequiredValue.HasValue)
            {
                RequiredValidationMessage = "✅ 必填验证通过";
                RequiredValidationColor = new SolidColorBrush(Colors.Green);
                validationResults.Add("必填验证: 通过");
            }
            else
            {
                RequiredValidationMessage = "❌ 此字段为必填";
                RequiredValidationColor = new SolidColorBrush(Colors.Red);
                validationResults.Add("必填验证: 失败");
            }

            // 自定义验证（偶数）
            if (CustomValidationValue.HasValue)
            {
                if (CustomValidationValue.Value % 2 == 0)
                {
                    CustomValidationMessage = "✅ 偶数验证通过";
                    CustomValidationColor = new SolidColorBrush(Colors.Green);
                    validationResults.Add("自定义验证: 通过");
                }
                else
                {
                    CustomValidationMessage = "❌ 必须输入偶数";
                    CustomValidationColor = new SolidColorBrush(Colors.Red);
                    validationResults.Add("自定义验证: 失败");
                }
            }
            else
            {
                CustomValidationMessage = "⚠️ 请输入数值";
                CustomValidationColor = new SolidColorBrush(Colors.Orange);
                validationResults.Add("自定义验证: 未输入");
            }

            ValidationSummary = string.Join("\n", validationResults);
            StatusMessage = "验证完成";

            HandleInteraction("验证所有");
        }
        catch (Exception ex)
        {
            ValidationSummary = $"验证过程出错: {ex.Message}";
            _logger.Error($"验证所有失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 清除验证命令
    /// </summary>
    [RelayCommand]
    private void ClearValidation()
    {
        RangeValidationValue = null;
        RangeValidationMessage = string.Empty;
        RangeValidationColor = Brushes.Gray;

        RequiredValue = null;
        RequiredValidationMessage = string.Empty;
        RequiredValidationColor = Brushes.Gray;

        CustomValidationValue = null;
        CustomValidationMessage = string.Empty;
        CustomValidationColor = Brushes.Gray;

        ValidationSummary = "验证已清除";
        StatusMessage = "验证已清除";

        HandleInteraction("清除验证");
    }

    #endregion

    #region 属性变化处理

    /// <summary>
    /// 设置属性变化处理器
    /// </summary>
    private void SetupPropertyChangeHandlers()
    {
        PropertyChanged += OnPropertyChanged;
    }

    /// <summary>
    /// 属性变化处理
    /// </summary>
    private void OnPropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        switch (e.PropertyName)
        {
            case nameof(RangeValidationValue):
                ValidateRangeValue();
                break;
            case nameof(RequiredValue):
                ValidateRequiredValue();
                break;
            case nameof(CustomValidationValue):
                ValidateCustomValue();
                break;
            case nameof(TemperatureValue):
                OnPropertyChanged(nameof(FahrenheitValue));
                break;
            case nameof(WeightValue):
                OnPropertyChanged(nameof(PoundsValue));
                break;
        }
    }

    /// <summary>
    /// 验证范围值
    /// </summary>
    private void ValidateRangeValue()
    {
        if (RangeValidationValue.HasValue)
        {
            if (RangeValidationValue.Value >= 1 && RangeValidationValue.Value <= 100)
            {
                RangeValidationMessage = "✅ 范围验证通过";
                RangeValidationColor = new SolidColorBrush(Colors.Green);
            }
            else
            {
                RangeValidationMessage = "❌ 数值必须在1-100之间";
                RangeValidationColor = new SolidColorBrush(Colors.Red);
            }
        }
        else
        {
            RangeValidationMessage = string.Empty;
            RangeValidationColor = Brushes.Gray;
        }
    }

    /// <summary>
    /// 验证必填值
    /// </summary>
    private void ValidateRequiredValue()
    {
        if (RequiredValue.HasValue)
        {
            RequiredValidationMessage = "✅ 必填验证通过";
            RequiredValidationColor = new SolidColorBrush(Colors.Green);
        }
        else
        {
            RequiredValidationMessage = "❌ 此字段为必填";
            RequiredValidationColor = new SolidColorBrush(Colors.Red);
        }
    }

    /// <summary>
    /// 验证自定义值
    /// </summary>
    private void ValidateCustomValue()
    {
        if (CustomValidationValue.HasValue)
        {
            if (CustomValidationValue.Value % 2 == 0)
            {
                CustomValidationMessage = "✅ 偶数验证通过";
                CustomValidationColor = new SolidColorBrush(Colors.Green);
            }
            else
            {
                CustomValidationMessage = "❌ 必须输入偶数";
                CustomValidationColor = new SolidColorBrush(Colors.Red);
            }
        }
        else
        {
            CustomValidationMessage = string.Empty;
            CustomValidationColor = Brushes.Gray;
        }
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 验证数值
    /// </summary>
    private NumberValidationResult ValidateNumberValue(double value)
    {
        var result = new NumberValidationResult();
        
        if (EnableRangeValidation)
        {
            if (value < MinimumValue)
            {
                result.Errors.Add($"数值不能小于 {MinimumValue}");
            }
            
            if (value > MaximumValue)
            {
                result.Errors.Add($"数值不能大于 {MaximumValue}");
            }
        }

        if (EnableFormatValidation)
        {
            if (double.IsNaN(value))
            {
                result.Errors.Add("数值格式无效");
            }
            
            if (double.IsInfinity(value))
            {
                result.Errors.Add("数值超出范围");
            }
        }

        result.IsValid = result.Errors.Count == 0;
        return result;
    }

    /// <summary>
    /// 处理交互事件
    /// </summary>
    private void HandleInteraction(string action)
    {
        _logger.Info($"用户操作: {action}");
    }

    /// <summary>
    /// 加载代码示例
    /// </summary>
    private void LoadCodeExamples()
    {
        try
        {
            var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
            var codeExamplesPath = Path.Combine(baseDirectory, "CodeExamples", "Controls", "NumberBox");

            _logger.Info($"基础目录: {baseDirectory}");
            _logger.Info($"代码示例路径: {codeExamplesPath}");
            _logger.Info($"目录是否存在: {Directory.Exists(codeExamplesPath)}");

            // 读取各种示例文件
            BasicXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.xaml.txt"));
            BasicCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.cs.txt"));
            AdvancedXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Advanced.xaml.txt"));
            AdvancedCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Advanced.cs.txt"));
            StylesXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Styles.xaml.txt"));
            FormattingXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Formatting.xaml.txt"));
            FormattingCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Formatting.cs.txt"));
            ValidationXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Validation.xaml.txt"));
            ValidationCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Validation.cs.txt"));

            _logger.Info("代码示例加载完成");
        }
        catch (Exception ex)
        {
            _logger.Error($"加载代码示例失败: {ex.Message}");
            SetDefaultCodeExamples();
        }
    }

    /// <summary>
    /// 读取代码示例文件
    /// </summary>
    private string ReadCodeExampleFile(string filePath)
    {
        try
        {
            if (File.Exists(filePath))
            {
                var content = File.ReadAllText(filePath, System.Text.Encoding.UTF8);
                _logger.Info($"成功读取文件: {filePath}");
                return content;
            }
            else
            {
                _logger.Warning($"文件不存在: {filePath}");
                return $"<!-- 文件不存在: {Path.GetFileName(filePath)} -->";
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"读取文件失败 {filePath}: {ex.Message}");
            return $"<!-- 读取文件失败: {ex.Message} -->";
        }
    }

    /// <summary>
    /// 设置默认代码示例（当文件读取失败时使用）
    /// </summary>
    private void SetDefaultCodeExamples()
    {
        BasicXamlExample = "<!-- NumberBox 基础示例 -->\n<!-- 在这里添加基础用法示例 -->";
        BasicCSharpExample = "// NumberBox C# 基础示例\n// 在这里添加 C# 代码示例";
        AdvancedXamlExample = "<!-- NumberBox 高级示例 -->\n<!-- 在这里添加高级用法示例 -->";
        AdvancedCSharpExample = "// NumberBox C# 高级示例\n// 在这里添加高级 C# 代码示例";
        StylesXamlExample = "<!-- NumberBox 样式示例 -->\n<!-- 在这里添加样式使用示例 -->";
        FormattingXamlExample = "<!-- NumberBox 格式化示例 -->\n<!-- 在这里添加格式化示例 -->";
        FormattingCSharpExample = "// NumberBox C# 格式化示例\n// 在这里添加格式化 C# 代码示例";
        ValidationXamlExample = "<!-- NumberBox 验证示例 -->\n<!-- 在这里添加验证示例 -->";
        ValidationCSharpExample = "// NumberBox C# 验证示例\n// 在这里添加验证 C# 代码示例";
    }

    #endregion
}

/// <summary>
/// 数字格式化项
/// </summary>
public class NumberFormatItem
{
    public string Name { get; set; }
    public string Format { get; set; }
    public string Example { get; set; }

    public NumberFormatItem(string name, string format, string example)
    {
        Name = name;
        Format = format;
        Example = example;
    }

    public override string ToString() => $"{Name} ({Example})";
}

/// <summary>
/// 数字验证结果
/// </summary>
public class NumberValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public string Summary => IsValid ? "验证通过" : $"发现 {Errors.Count} 个错误";
}
