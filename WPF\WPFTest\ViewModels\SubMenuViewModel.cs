using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Prism.Navigation;
using Prism.Regions;
using Zylo.WPF.Models.Navigation;
using Zylo.WPF.YPrism;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels;

/// <summary>
/// 子菜单视图模型
/// </summary>
public partial class SubMenuViewModel : ObservableObject, INavigationAware
{
    
    public readonly YLoggerInstance _logger = YLogger.ForSilent<SubMenuViewModel>();
    #region 字段

    private readonly IRegionManager _regionManager;

    #endregion

    #region 属性

    private ZyloNavigationItemModel _parentItem = new();
    /// <summary>
    /// 父导航项
    /// </summary>
    public ZyloNavigationItemModel ParentItem
    {
        get => _parentItem;
        set
        {
            _parentItem = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(ParentItemName));
            SubMenuItems = value?.Children ?? new ObservableCollection<ZyloNavigationItemModel>();
        }
    }

    /// <summary>
    /// 父项目名称
    /// </summary>
    public string ParentItemName => ParentItem?.Name ?? "子菜单";

    private ObservableCollection<ZyloNavigationItemModel> _subMenuItems = new();
    /// <summary>
    /// 子菜单项集合
    /// </summary>
    public ObservableCollection<ZyloNavigationItemModel> SubMenuItems
    {
        get => _subMenuItems;
        set
        {
            _subMenuItems = value;
            OnPropertyChanged();
            FilterSubMenuItems();
        }
    }

    private ObservableCollection<ZyloNavigationItemModel> _filteredSubMenuItems = new();
    /// <summary>
    /// 过滤后的子菜单项集合
    /// </summary>
    public ObservableCollection<ZyloNavigationItemModel> FilteredSubMenuItems
    {
        get => _filteredSubMenuItems;
        set
        {
            _filteredSubMenuItems = value;
            OnPropertyChanged();
        }
    }

    private string _searchText = string.Empty;
    /// <summary>
    /// 搜索文本
    /// </summary>
    public string SearchText
    {
        get => _searchText;
        set
        {
            _searchText = value;
            OnPropertyChanged();
            FilterSubMenuItems();
        }
    }

    private ZyloNavigationItemModel? _selectedSubMenuItem;
    /// <summary>
    /// 选中的子菜单项
    /// </summary>
    public ZyloNavigationItemModel? SelectedSubMenuItem
    {
        get => _selectedSubMenuItem;
        set
        {
            _selectedSubMenuItem = value;
            OnPropertyChanged();
        }
    }

    private ObservableCollection<ZyloNavigationItemModel> _breadcrumbItems = new();
    /// <summary>
    /// 面包屑导航项集合
    /// </summary>
    public ObservableCollection<ZyloNavigationItemModel> BreadcrumbItems
    {
        get => _breadcrumbItems;
        set
        {
            _breadcrumbItems = value;
            OnPropertyChanged();
        }
    }

    #endregion

    #region 命令

    /// <summary>
    /// 子菜单项点击命令
    /// </summary>
    public ICommand SubMenuItemClickCommand { get; }

    /// <summary>
    /// 面包屑点击命令
    /// </summary>
    public ICommand BreadcrumbClickCommand { get; }

    /// <summary>
    /// 判断是否为当前项
    /// </summary>
    /// <param name="item">要判断的项</param>
    /// <returns>是否为当前项</returns>
    public bool IsCurrentItem(ZyloNavigationItemModel item)
    {
        return item == ParentItem;
    }

    #endregion

    #region 构造函数

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="regionManager">区域管理器</param>
    public SubMenuViewModel(IRegionManager regionManager)
    {
        _regionManager = regionManager;

        // 初始化命令
        SubMenuItemClickCommand = new RelayCommand<ZyloNavigationItemModel>(OnSubMenuItemClick);
        BreadcrumbClickCommand = new RelayCommand<ZyloNavigationItemModel>(OnBreadcrumbClick);
    }

    #endregion

    #region 事件处理

    /// <summary>
    /// 子菜单项点击处理
    /// </summary>
    /// <param name="item">点击的子菜单项</param>
    private void OnSubMenuItemClick(ZyloNavigationItemModel? item)
    {
        if (item == null) return;

        _logger.Info($"🎯 子菜单项被点击: {item.Name}");

        // 如果有子项目，导航到下一层子菜单
        if (item.Children?.Count > 0)
        {
            _logger.Info($"🔄 导航项有子项目，导航到下一层子菜单: {item.Name}");

            // 创建导航参数，传递当前项目作为父项目
            var navigationParams = new NavigationParameters();
            navigationParams.Add("ParentItem", item);
            navigationParams.Add("Title", $"{item.Name} - 子菜单");

            // 导航到子菜单页面
            _regionManager.Regions[PrismManager.MainViewRegionName].RequestNavigate(
                "SubMenuView",
                navigationResult =>
                {
                    if (navigationResult.Result == true)
                    {
                        _logger.Info($"✅ 导航到下一层子菜单成功: {item.Name}");
                    }
                    else
                    {
                        _logger.Error($"❌ 导航到下一层子菜单失败: {item.Name} - {navigationResult.Error?.Message}");
                    }
                },
                navigationParams);
        }
        // 如果有导航目标，直接导航到目标页面
        else if (!string.IsNullOrEmpty(item.NavigationTarget))
        {
            _logger.Info($"🔄 准备导航到目标页面: {item.NavigationTarget}");

            // 创建导航参数
            var navigationParams = new NavigationParameters();
            navigationParams.Add("NavigationItem", item);
            navigationParams.Add("Title", item.Name);

            // 执行导航到目标页面
            _regionManager.Regions[PrismManager.MainViewRegionName].RequestNavigate(
                item.NavigationTarget,
                navigationResult =>
                {
                    if (navigationResult.Result == true)
                    {
                        _logger.Info($"✅ 导航到目标页面成功: {item.NavigationTarget}");
                    }
                    else
                    {
                        _logger.Error($"❌ 导航到目标页面失败: {item.NavigationTarget} - {navigationResult.Error?.Message}");
                    }
                },
                navigationParams);
        }
        else
        {
            _logger.Warning($"⚠️ 导航项 {item.Name} 没有导航目标也没有子项目");
        }
    }

    /// <summary>
    /// 面包屑点击处理
    /// </summary>
    /// <param name="item">点击的面包屑项</param>
    private void OnBreadcrumbClick(ZyloNavigationItemModel? item)
    {
        if (item == null) return;

        _logger.Info($"🍞 面包屑被点击: {item.Name}");

        // 如果点击的是当前项目，不需要导航
        if (item == ParentItem)
        {
            _logger.Info($"🍞 点击的是当前项目，无需导航: {item.Name}");
            return;
        }

        // 如果点击的项目有子项目，导航到其子菜单
        if (item.Children?.Count > 0)
        {
            var navigationParams = new NavigationParameters();
            navigationParams.Add("ParentItem", item);
            navigationParams.Add("Title", $"{item.Name} - 子菜单");

            _regionManager.Regions[PrismManager.MainViewRegionName].RequestNavigate(
                "SubMenuView",
                navigationResult =>
                {
                    if (navigationResult.Result == true)
                    {
                        _logger.Info($"✅ 面包屑导航到子菜单成功: {item.Name}");
                    }
                    else
                    {
                        _logger.Error($"❌ 面包屑导航到子菜单失败: {item.Name} - {navigationResult.Error?.Message}");
                    }
                },
                navigationParams);
        }
        // 如果点击的项目没有子项目但有导航目标，直接导航到目标页面
        else if (!string.IsNullOrEmpty(item.NavigationTarget))
        {
            var navigationParams = new NavigationParameters();
            navigationParams.Add("NavigationItem", item);
            navigationParams.Add("Title", item.Name);

            _regionManager.Regions[PrismManager.MainViewRegionName].RequestNavigate(
                item.NavigationTarget,
                navigationResult =>
                {
                    if (navigationResult.Result == true)
                    {
                        _logger.Info($"✅ 面包屑导航到目标页面成功: {item.NavigationTarget}");
                    }
                    else
                    {
                        _logger.Error($"❌ 面包屑导航到目标页面失败: {item.NavigationTarget} - {navigationResult.Error?.Message}");
                    }
                },
                navigationParams);
        }
    }

    /// <summary>
    /// 过滤子菜单项
    /// </summary>
    private void FilterSubMenuItems()
    {
        if (SubMenuItems == null)
        {
            FilteredSubMenuItems = new ObservableCollection<ZyloNavigationItemModel>();
            return;
        }

        if (string.IsNullOrWhiteSpace(SearchText))
        {
            FilteredSubMenuItems = new ObservableCollection<ZyloNavigationItemModel>(SubMenuItems);
        }
        else
        {
            var filtered = SubMenuItems.Where(item =>
                item.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                (!string.IsNullOrEmpty(item.NavigationTarget) && 
                 item.NavigationTarget.Contains(SearchText, StringComparison.OrdinalIgnoreCase)))
                .ToList();

            FilteredSubMenuItems = new ObservableCollection<ZyloNavigationItemModel>(filtered);
        }
    }

    /// <summary>
    /// 构建面包屑导航项
    /// </summary>
    /// <param name="currentItem">当前项目</param>
    private void BuildBreadcrumbItems(ZyloNavigationItemModel currentItem)
    {
        var breadcrumbs = new List<ZyloNavigationItemModel>();

        // 从当前项目向上遍历到根项目，构建路径
        var current = currentItem;
        while (current != null)
        {
            breadcrumbs.Insert(0, current);
            current = current.Parent;
        }

        BreadcrumbItems = new ObservableCollection<ZyloNavigationItemModel>(breadcrumbs);
        _logger.Info($"🍞 构建面包屑导航: {string.Join(" > ", breadcrumbs.Select(b => b.Name))}");
    }

    #endregion

    #region INavigationAware

    /// <summary>
    /// 导航到此视图时调用
    /// </summary>
    /// <param name="navigationContext">导航上下文</param>
    public void OnNavigatedTo(NavigationContext navigationContext)
    {
        _logger.Info("🎯 SubMenuView 导航到");

        // 从导航参数中获取父项目
        if (navigationContext.Parameters.TryGetValue("ParentItem", out ZyloNavigationItemModel parentItem))
        {
            ParentItem = parentItem;
            _logger.Info($"✅ 接收到父项目: {parentItem.Name}, 子项数量: {parentItem.Children?.Count ?? 0}");

            // 构建面包屑导航
            BuildBreadcrumbItems(parentItem);
        }
        else
        {
            _logger.Warning("⚠️ 未接收到父项目参数");
        }
    }

    /// <summary>
    /// 是否可以导航到此视图
    /// </summary>
    /// <param name="navigationContext">导航上下文</param>
    /// <returns>是否可以导航</returns>
    public bool IsNavigationTarget(NavigationContext navigationContext)
    {
        return true;
    }

    /// <summary>
    /// 从此视图导航离开时调用
    /// </summary>
    /// <param name="navigationContext">导航上下文</param>
    public void OnNavigatedFrom(NavigationContext navigationContext)
    {
        _logger.Info("🎯 SubMenuView 导航离开");
    }

    #endregion
}
