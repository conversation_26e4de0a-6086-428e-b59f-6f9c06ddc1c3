// AutoSuggestBox C# 基础用法示例 - MVVM模式
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using System.ComponentModel;

namespace WPFTest.ViewModels.InputControls
{
    /// <summary>
    /// AutoSuggestBox 基础示例 ViewModel - 展示正确的MVVM模式
    /// 关键点：使用Command处理TextChanged事件，而不是在View代码后面处理
    /// </summary>
    public partial class AutoSuggestBoxPageViewModel : ObservableObject
    {
        #region 搜索属性

        /// <summary>
        /// 城市搜索文本
        /// </summary>
        [ObservableProperty]
        private string searchText = string.Empty;

        /// <summary>
        /// 编程语言搜索文本
        /// </summary>
        [ObservableProperty]
        private string languageSearchText = string.Empty;

        /// <summary>
        /// 国家搜索文本
        /// </summary>
        [ObservableProperty]
        private string countrySearchText = string.Empty;

        /// <summary>
        /// 城市建议列表
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<string> citySuggestions = new();

        /// <summary>
        /// 编程语言建议列表
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<string> languageSuggestions = new();

        /// <summary>
        /// 国家建议列表
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<string> countrySuggestions = new();

        /// <summary>
        /// 交互次数
        /// </summary>
        [ObservableProperty]
        private int interactionCount = 0;

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        private string statusMessage = "欢迎使用 AutoSuggestBox！";

        #endregion

        #region 数据源

        private readonly List<string> _cities = new()
        {
            "北京", "上海", "广州", "深圳", "杭州", "南京", "武汉", "成都", "西安", "重庆"
        };

        private readonly List<string> _languages = new()
        {
            "C#", "Java", "Python", "JavaScript", "TypeScript", "Go", "Rust", "C++", "Swift", "Kotlin"
        };

        private readonly List<string> _countries = new()
        {
            "中国", "美国", "日本", "德国", "法国", "英国", "加拿大", "澳大利亚", "韩国", "新加坡"
        };

        #endregion

        #region 构造函数

        public AutoSuggestBoxPageViewModel()
        {
            // 监听属性变化
            PropertyChanged += OnPropertyChanged;

            // 初始化建议列表
            InitializeSuggestions();

            StatusMessage = "AutoSuggestBox 示例已加载，开始搜索吧！";
        }

        #endregion

        #region 命令 - MVVM模式的核心

        /// <summary>
        /// 城市搜索命令 - 通过Command处理TextChanged事件
        /// 这是MVVM模式的正确做法：View通过Command与ViewModel交互
        /// </summary>
        [RelayCommand]
        private void CitySearch(string? searchText)
        {
            if (SearchText != searchText)
            {
                SearchText = searchText ?? string.Empty;
            }
        }

        /// <summary>
        /// 编程语言搜索命令
        /// </summary>
        [RelayCommand]
        private void LanguageSearch(string? searchText)
        {
            if (LanguageSearchText != searchText)
            {
                LanguageSearchText = searchText ?? string.Empty;
            }
        }

        /// <summary>
        /// 国家搜索命令
        /// </summary>
        [RelayCommand]
        private void CountrySearch(string? searchText)
        {
            if (CountrySearchText != searchText)
            {
                CountrySearchText = searchText ?? string.Empty;
            }
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 属性变化处理 - 响应数据绑定的变化
        /// </summary>
        private void OnPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            switch (e.PropertyName)
            {
                case nameof(SearchText):
                    UpdateCitySuggestions();
                    break;
                case nameof(LanguageSearchText):
                    UpdateLanguageSuggestions();
                    break;
                case nameof(CountrySearchText):
                    UpdateCountrySuggestions();
                    break;
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化建议列表
        /// </summary>
        private void InitializeSuggestions()
        {
            CitySuggestions.Clear();
            LanguageSuggestions.Clear();
            CountrySuggestions.Clear();

            foreach (var city in _cities) CitySuggestions.Add(city);
            foreach (var language in _languages) LanguageSuggestions.Add(language);
            foreach (var country in _countries) CountrySuggestions.Add(country);
        }

        /// <summary>
        /// 更新城市建议
        /// </summary>
        private void UpdateCitySuggestions()
        {
            CitySuggestions.Clear();

            var filtered = string.IsNullOrWhiteSpace(SearchText)
                ? _cities
                : _cities.Where(c => c.Contains(SearchText, StringComparison.OrdinalIgnoreCase));

            foreach (var city in filtered)
            {
                CitySuggestions.Add(city);
            }

            InteractionCount++;
            StatusMessage = $"城市搜索: '{SearchText}' - 找到 {CitySuggestions.Count} 个结果";
        }

        /// <summary>
        /// 更新编程语言建议
        /// </summary>
        private void UpdateLanguageSuggestions()
        {
            LanguageSuggestions.Clear();

            var filtered = string.IsNullOrWhiteSpace(LanguageSearchText)
                ? _languages
                : _languages.Where(l => l.Contains(LanguageSearchText, StringComparison.OrdinalIgnoreCase));

            foreach (var language in filtered)
            {
                LanguageSuggestions.Add(language);
            }

            InteractionCount++;
            StatusMessage = $"语言搜索: '{LanguageSearchText}' - 找到 {LanguageSuggestions.Count} 个结果";
        }

        /// <summary>
        /// 更新国家建议
        /// </summary>
        private void UpdateCountrySuggestions()
        {
            CountrySuggestions.Clear();

            var filtered = string.IsNullOrWhiteSpace(CountrySearchText)
                ? _countries
                : _countries.Where(c => c.Contains(CountrySearchText, StringComparison.OrdinalIgnoreCase));

            foreach (var country in filtered)
            {
                CountrySuggestions.Add(country);
            }

            InteractionCount++;
            StatusMessage = $"国家搜索: '{CountrySearchText}' - 找到 {CountrySuggestions.Count} 个结果";
        }

        #endregion
        /// </summary>
        [RelayCommand]
        private void Search(string? parameter)
        {
            var searchType = parameter ?? "未知";
            InteractionCount++;
            StatusMessage = $"🔍 执行了{searchType}搜索";
        }

        /// <summary>
        /// 清除搜索命令
        /// </summary>
        [RelayCommand]
        private void ClearSearch()
        {
            SearchText = string.Empty;
            LanguageSearchText = string.Empty;
            CountrySearchText = string.Empty;
            StatusMessage = "已清除所有搜索内容";
        }

        /// <summary>
        /// 重置计数命令
        /// </summary>
        [RelayCommand]
        private void ResetCount()
        {
            InteractionCount = 0;
            StatusMessage = "计数已重置";
        }

        #endregion

        #region 实用方法

        /// <summary>
        /// 格式化搜索结果
        /// </summary>
        /// <param name="searchText">搜索文本</param>
        /// <returns>格式化后的结果</returns>
        public string FormatSearchResult(string searchText)
        {
            if (string.IsNullOrWhiteSpace(searchText))
                return "无搜索内容";
            
            return $"搜索: \"{searchText}\" (长度: {searchText.Length})";
        }

        /// <summary>
        /// 验证搜索输入
        /// </summary>
        /// <param name="input">输入文本</param>
        /// <returns>是否有效</returns>
        public bool ValidateSearchInput(string input)
        {
            // 基础验证：不为空且长度合理
            return !string.IsNullOrWhiteSpace(input) && input.Length <= 50;
        }

        /// <summary>
        /// 获取搜索建议
        /// </summary>
        /// <param name="input">输入文本</param>
        /// <param name="suggestions">建议列表</param>
        /// <returns>匹配的建议</returns>
        public IEnumerable<string> GetSearchSuggestions(string input, IEnumerable<string> suggestions)
        {
            if (string.IsNullOrWhiteSpace(input))
                return Enumerable.Empty<string>();
            
            return suggestions
                .Where(s => s.Contains(input, StringComparison.OrdinalIgnoreCase))
                .Take(10); // 限制建议数量
        }

        #endregion
    }
}
