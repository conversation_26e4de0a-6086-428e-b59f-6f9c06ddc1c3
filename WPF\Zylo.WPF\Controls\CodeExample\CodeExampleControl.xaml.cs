using System;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Animation;
using ICSharpCode.AvalonEdit.Highlighting;
using ICSharpCode.AvalonEdit.Highlighting.Xshd;
using System.Xml;
using Zylo.YLog.Runtime;

namespace Zylo.WPF.Controls.CodeExample
{
    /// <summary>
    /// CodeExampleControl - 代码示例展示控件
    /// 支持语法高亮、复制、展开收缩等功能
    /// </summary>
    public partial class CodeExampleControl : UserControl
    {
        // 日志记录器实例，用于记录控件运行时的信息和错误
        private readonly YLoggerInstance _logger = YLogger.ForSilent<CodeExampleControl>();

        // 控件当前的展开/收缩状态
        private bool _isExpanded = false;

        // 当前选项卡显示的代码内容
        private string _currentTabContent = string.Empty;

        // 当前选项卡的编程语言类型，用于语法高亮
        private string _currentTabLanguage = "XAML";

        #region 依赖属性

        /// <summary>
        /// 标题
        /// </summary>
        public static readonly DependencyProperty TitleProperty =
            DependencyProperty.Register(nameof(Title), typeof(string), typeof(CodeExampleControl),
                new PropertyMetadata("代码示例"));

        public string Title
        {
            get => (string)GetValue(TitleProperty);
            set => SetValue(TitleProperty, value);
        }

        /// <summary>
        /// 编程语言
        /// </summary>
        public static readonly new DependencyProperty LanguageProperty =
            DependencyProperty.Register(nameof(Language), typeof(string), typeof(CodeExampleControl),
                new PropertyMetadata("XAML"));

        public new string Language
        {
            get => (string)GetValue(LanguageProperty);
            set => SetValue(LanguageProperty, value);
        }

        /// <summary>
        /// 代码内容
        /// </summary>
        public static readonly DependencyProperty CodeContentProperty =
            DependencyProperty.Register(nameof(CodeContent), typeof(string), typeof(CodeExampleControl),
                new PropertyMetadata(string.Empty, OnCodeContentChanged));

        public string CodeContent
        {
            get => (string)GetValue(CodeContentProperty);
            set => SetValue(CodeContentProperty, value);
        }

        /// <summary>
        /// XAML代码内容
        /// </summary>
        public static readonly DependencyProperty XamlCodeProperty =
            DependencyProperty.Register(nameof(XamlCode), typeof(string), typeof(CodeExampleControl),
                new PropertyMetadata(string.Empty));

        public string XamlCode
        {
            get => (string)GetValue(XamlCodeProperty);
            set => SetValue(XamlCodeProperty, value);
        }

        /// <summary>
        /// C#代码内容
        /// </summary>
        public static readonly DependencyProperty CSharpCodeProperty =
            DependencyProperty.Register(nameof(CSharpCode), typeof(string), typeof(CodeExampleControl),
                new PropertyMetadata(string.Empty));

        public string CSharpCode
        {
            get => (string)GetValue(CSharpCodeProperty);
            set => SetValue(CSharpCodeProperty, value);
        }

        /// <summary>
        /// 是否显示选项卡
        /// </summary>
        public static readonly DependencyProperty ShowTabsProperty =
            DependencyProperty.Register(nameof(ShowTabs), typeof(bool), typeof(CodeExampleControl),
                new PropertyMetadata(false, OnShowTabsChanged));

        public bool ShowTabs
        {
            get => (bool)GetValue(ShowTabsProperty);
            set => SetValue(ShowTabsProperty, value);
        }

        /// <summary>
        /// 描述信息
        /// </summary>
        public static readonly DependencyProperty DescriptionProperty =
            DependencyProperty.Register(nameof(Description), typeof(string), typeof(CodeExampleControl),
                new PropertyMetadata("代码示例"));

        public string Description
        {
            get => (string)GetValue(DescriptionProperty);
            set => SetValue(DescriptionProperty, value);
        }

        /// <summary>
        /// 是否默认展开
        /// </summary>
        public static readonly DependencyProperty IsExpandedProperty =
            DependencyProperty.Register(nameof(IsExpanded), typeof(bool), typeof(CodeExampleControl),
                new PropertyMetadata(false, OnIsExpandedChanged));

        public bool IsExpanded
        {
            get => (bool)GetValue(IsExpandedProperty);
            set => SetValue(IsExpandedProperty, value);
        }

        #endregion

        #region 构造函数

        public CodeExampleControl()
        {
            // 初始化XAML定义的UI组件
            InitializeComponent();

            // 订阅控件加载完成事件，用于初始化编辑器和更新UI状态
            Loaded += OnLoaded;
        }

        #endregion

        #region 事件处理

        private void OnLoaded(object sender, RoutedEventArgs e)
        {
            // 更新代码行数显示
            UpdateLineCount();

            // 根据IsExpanded属性设置初始展开/收缩状态
            UpdateExpandCollapseState();

            // 初始化AvalonEdit代码编辑器的配置和语法高亮
            InitializeAvalonEditor();

            // 根据ShowTabs属性决定显示模式
            if (ShowTabs)
            {
                // 选项卡模式：显示XAML/C#切换选项卡
                UpdateTabVisibility();
            }
            else
            {
                // 单一模式：直接显示CodeContent属性的内容
                UpdateCurrentTabContent();
            }
        }

        /// <summary>
        /// 复制代码按钮点击
        /// </summary>
        private void CopyCodeButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 根据当前显示模式确定要复制的内容
                // 选项卡模式：复制当前选中选项卡的内容
                // 单一模式：复制CodeContent属性的内容
                var contentToCopy = ShowTabs ? _currentTabContent : CodeContent;

                if (!string.IsNullOrEmpty(contentToCopy))
                {
                    // 将代码内容复制到系统剪贴板
                    Clipboard.SetText(contentToCopy);

                    // 显示复制成功的视觉反馈
                    ShowCopyFeedback(sender as FrameworkElement);
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 复制代码失败: {ex.Message}");
            }
        }





        /// <summary>
        /// 标题栏点击 - 展开/收缩
        /// </summary>
        private void TitleBarButton_Click(object sender, RoutedEventArgs e)
        {
            // 检查事件源，避免复制按钮的点击事件触发展开/收缩
            // 这样可以确保只有点击标题栏其他区域才会切换展开状态
            if (e.OriginalSource is Button button && button.Name == "CopyButton")
            {
                return;
            }

            // 切换展开/收缩状态
            IsExpanded = !IsExpanded;
        }

        /// <summary>
        /// 展开收缩按钮点击（保留兼容性）
        /// </summary>
        private void ExpandCollapseButton_Click(object sender, RoutedEventArgs e)
        {
            IsExpanded = !IsExpanded;
        }

        /// <summary>
        /// XAML选项卡点击
        /// </summary>
        private void XamlTabButton_Click(object sender, RoutedEventArgs e)
        {
            SwitchToTab("XAML", XamlCode);
        }

        /// <summary>
        /// C#选项卡点击
        /// </summary>
        private void CSharpTabButton_Click(object sender, RoutedEventArgs e)
        {
            SwitchToTab("C#", CSharpCode);
        }

        /// <summary>
        /// AvalonEdit鼠标滚轮事件处理 - 传播到外部容器
        /// </summary>
        private void AvalonCodeEditor_PreviewMouseWheel(object sender, System.Windows.Input.MouseWheelEventArgs e)
        {
            try
            {
                // 检查AvalonEdit编辑器是否隐藏了垂直滚动条
                // 如果隐藏了滚动条，说明内容应该由外部容器来处理滚动
                if (AvalonCodeEditor.VerticalScrollBarVisibility == System.Windows.Controls.ScrollBarVisibility.Hidden)
                {
                    // 标记事件已处理，防止AvalonEdit内部处理
                    e.Handled = true;

                    // 创建新的鼠标滚轮事件，将滚动操作传播给父容器
                    // 这样可以让外部的ScrollViewer等容器处理滚动
                    var eventArg = new System.Windows.Input.MouseWheelEventArgs(e.MouseDevice, e.Timestamp, e.Delta)
                    {
                        RoutedEvent = UIElement.MouseWheelEvent,
                        Source = this
                    };

                    // 向父容器传播滚轮事件
                    var parent = Parent as UIElement;
                    parent?.RaiseEvent(eventArg);
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 处理鼠标滚轮事件失败: {ex.Message}");
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 代码内容变化时更新行数
        /// </summary>
        private static void OnCodeContentChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is CodeExampleControl control)
            {
                control.UpdateLineCount();
                control.UpdateCurrentTabContent();
            }
        }

        private static void OnShowTabsChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is CodeExampleControl control)
            {
                control.UpdateTabVisibility();
            }
        }

        /// <summary>
        /// 展开状态变化时更新UI
        /// </summary>
        private static void OnIsExpandedChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is CodeExampleControl control)
            {
                control._isExpanded = (bool)e.NewValue;
                control.UpdateExpandCollapseState();
            }
        }

        /// <summary>
        /// 更新行数显示
        /// </summary>
        private void UpdateLineCount()
        {
            // 检查行数显示控件是否存在且代码内容不为空
            if (LineCountTextBlock != null && !string.IsNullOrEmpty(CodeContent))
            {
                // 通过换行符分割计算代码总行数
                var lineCount = CodeContent.Split('\n').Length;

                // 更新UI显示的行数信息
                LineCountTextBlock.Text = $"{lineCount} 行";
            }
        }

        /// <summary>
        /// 更新展开收缩状态
        /// </summary>
        private void UpdateExpandCollapseState()
        {
            // 检查代码内容容器是否存在
            if (CodeContentBorder == null) return;

            try
            {
                // 创建平滑的透明度动画效果
                // 使用300毫秒的动画时长和缓出效果，提供良好的用户体验
                var animation = new DoubleAnimation
                {
                    Duration = TimeSpan.FromMilliseconds(300),
                    EasingFunction = new CubicEase { EasingMode = EasingMode.EaseOut }
                };

                if (_isExpanded)
                {
                    // 展开状态：显示代码内容区域
                    CodeContentBorder.Visibility = Visibility.Visible;
                    animation.To = 1.0; // 完全不透明

                    // 更新展开/收缩指示器图标 - 展开时显示向下箭头
                    if (ExpandCollapseIndicator != null)
                    {
                        ExpandCollapseIndicator.Symbol = Wpf.Ui.Controls.SymbolRegular.ChevronDown24;
                    }
                }
                else
                {
                    // 收缩状态：隐藏代码内容区域
                    animation.To = 0.0; // 完全透明

                    // 更新展开/收缩指示器图标 - 收缩时显示向上箭头
                    if (ExpandCollapseIndicator != null)
                    {
                        ExpandCollapseIndicator.Symbol = Wpf.Ui.Controls.SymbolRegular.ChevronUp24;
                    }

                    // 动画完成后完全隐藏元素，释放布局空间
                    animation.Completed += (s, e) =>
                    {
                        if (!_isExpanded)
                            CodeContentBorder.Visibility = Visibility.Collapsed;
                    };
                }

                // 开始执行透明度动画
                CodeContentBorder.BeginAnimation(OpacityProperty, animation);
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 更新展开收缩状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化AvalonEdit编辑器
        /// </summary>
        private void InitializeAvalonEditor()
        {
            try
            {
                // 配置AvalonEdit编辑器的基本选项和行为
                ConfigureAvalonEditor();

                // 根据Language属性设置相应的语法高亮
                SetAvalonSyntaxHighlighting(Language);

                // 将CodeContent属性的内容加载到编辑器中
                AvalonCodeEditor.Text = CodeContent;

                _logger.Info("✅ AvalonEdit 编辑器初始化完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 初始化AvalonEdit失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 配置AvalonEdit编辑器
        /// </summary>
        private void ConfigureAvalonEditor()
        {
            try
            {
                // 禁用不需要的功能，保持代码展示的简洁性
                AvalonCodeEditor.Options.EnableHyperlinks = false;          // 禁用超链接检测
                AvalonCodeEditor.Options.EnableEmailHyperlinks = false;     // 禁用邮箱链接检测
                AvalonCodeEditor.Options.ShowBoxForControlCharacters = false; // 不显示控制字符框
                AvalonCodeEditor.Options.ShowEndOfLine = false;             // 不显示行尾符号
                AvalonCodeEditor.Options.ShowSpaces = false;                // 不显示空格符号
                AvalonCodeEditor.Options.ShowTabs = false;                  // 不显示制表符符号

                // 缩进和格式化配置
                AvalonCodeEditor.Options.ConvertTabsToSpaces = true;        // 将制表符转换为空格
                AvalonCodeEditor.Options.IndentationSize = 4;               // 设置缩进大小为4个空格
                AvalonCodeEditor.Options.WordWrapIndentation = 4;           // 换行缩进大小

                // 编辑行为配置
                AvalonCodeEditor.Options.CutCopyWholeLine = true;           // 允许复制整行
                AvalonCodeEditor.Options.AllowScrollBelowDocument = false;  // 禁止滚动到文档末尾之后

                // 换行和选择配置
                AvalonCodeEditor.WordWrap = false;                          // 禁用自动换行，保持代码原始格式
                AvalonCodeEditor.Options.EnableRectangularSelection = true; // 启用矩形选择功能

                // 设置为只读模式，仅用于代码展示
                AvalonCodeEditor.IsReadOnly = true;                         // 禁止编辑
                AvalonCodeEditor.Options.EnableTextDragDrop = false;        // 禁用拖拽功能
                AvalonCodeEditor.Options.EnableVirtualSpace = false;        // 禁用虚拟空间

                // 隐藏滚动条，由外部容器统一处理滚动
                AvalonCodeEditor.HorizontalScrollBarVisibility = System.Windows.Controls.ScrollBarVisibility.Hidden;
                AvalonCodeEditor.VerticalScrollBarVisibility = System.Windows.Controls.ScrollBarVisibility.Hidden;

                // 注册鼠标滚轮事件处理，确保滚动事件能传播到父容器
                AvalonCodeEditor.PreviewMouseWheel += AvalonCodeEditor_PreviewMouseWheel;

                _logger.Info("✅ AvalonEdit 编辑器配置完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 配置AvalonEdit失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置AvalonEdit语法高亮
        /// </summary>
        private void SetAvalonSyntaxHighlighting(string language)
        {
            try
            {
                var highlightingName = MapLanguageToHighlighting(language);
                var highlighting = HighlightingManager.Instance.GetDefinition(highlightingName);

                if (highlighting != null)
                {
                    AvalonCodeEditor.SyntaxHighlighting = highlighting;
                    _logger.Info($"✅ 设置语法高亮: {highlightingName}");
                }
                else
                {
                    _logger.Info($"⚠️ 未找到语法高亮定义: {highlightingName}");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 设置语法高亮失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 映射语言到AvalonEdit高亮定义
        /// </summary>
        private string MapLanguageToHighlighting(string language)
        {
            // 将输入的语言名称映射到AvalonEdit支持的语法高亮定义名称
            // 使用switch表达式提供更简洁的映射逻辑
            return language.ToUpper() switch
            {
                "XAML" => "XML",           // XAML使用XML语法高亮
                "XML" => "XML",            // 标准XML格式
                "C#" => "C#",              // C#语言
                "CSHARP" => "C#",          // C#的另一种表示
                "JAVASCRIPT" => "JavaScript", // JavaScript语言
                "JS" => "JavaScript",      // JavaScript的简写
                "JSON" => "JavaScript",    // JSON格式使用JavaScript高亮显示
                "CSS" => "CSS",            // CSS样式表
                "HTML" => "HTML",          // HTML标记语言
                "SQL" => "SQL",            // SQL数据库查询语言
                "PYTHON" => "Python",      // Python编程语言
                "JAVA" => "Java",          // Java编程语言
                _ => "XML"                 // 默认情况使用XML高亮，适用于大多数标记语言
            };
        }



        /// <summary>
        /// 切换选项卡
        /// </summary>
        private void SwitchToTab(string language, string content)
        {
            try
            {
                // 更新当前选项卡的状态信息
                _currentTabLanguage = language;
                _currentTabContent = content ?? string.Empty;

                _logger.Info($"🔄 切换选项卡: {language}, 内容长度: {_currentTabContent.Length}");

                // 更新选项卡按钮的视觉状态，突出显示当前活动的选项卡
                UpdateTabButtonStyles(language);

                // 将新内容加载到代码编辑器中，并应用相应的语法高亮
                UpdateEditorContent();
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 切换选项卡失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新选项卡按钮样式
        /// </summary>
        private void UpdateTabButtonStyles(string activeTab)
        {
            try
            {
                // 从WPF-UI主题资源中获取颜色画刷，确保与应用主题保持一致
                // 活动选项卡使用强调色背景
                var activeBrush = TryFindResource("AccentFillColorDefaultBrush") as System.Windows.Media.Brush ??
                                 TryFindResource("SystemAccentColorPrimaryBrush") as System.Windows.Media.Brush;

                // 活动选项卡的文本颜色
                var activeTextBrush = TryFindResource("AccentTextFillColorPrimaryBrush") as System.Windows.Media.Brush ??
                                     TryFindResource("TextFillColorPrimaryBrush") as System.Windows.Media.Brush;

                // 非活动选项卡使用默认控件背景色
                var inactiveBrush = TryFindResource("ControlFillColorDefaultBrush") as System.Windows.Media.Brush ??
                                   TryFindResource("LayerFillColorDefaultBrush") as System.Windows.Media.Brush;

                // 非活动选项卡使用次要文本颜色，降低视觉重要性
                var inactiveTextBrush = TryFindResource("TextFillColorSecondaryBrush") as System.Windows.Media.Brush ??
                                       TryFindResource("TextFillColorPrimaryBrush") as System.Windows.Media.Brush;

                if (XamlTabButton != null)
                {
                    if (activeTab == "XAML")
                    {
                        // XamlTabButton.Background = activeBrush;
                        XamlTabButton.Foreground = activeTextBrush;
                        XamlTabButton.BorderBrush = TryFindResource("ControlStrokeColorDefaultBrush") as System.Windows.Media.Brush;
                        XamlTabButton.BorderThickness = new System.Windows.Thickness(1);
                    }
                    else
                    {
                        // XamlTabButton.Background = inactiveBrush;
                        XamlTabButton.Foreground = inactiveTextBrush;
                        XamlTabButton.BorderBrush = TryFindResource("ControlStrokeColorDefaultBrush") as System.Windows.Media.Brush;
                        XamlTabButton.BorderThickness = new System.Windows.Thickness(1);
                    }
                }

                if (CSharpTabButton != null)
                {
                    if (activeTab == "C#")
                    {
                        // CSharpTabButton.Background = activeBrush;
                        CSharpTabButton.Foreground = activeTextBrush;
                        CSharpTabButton.BorderBrush = TryFindResource("ControlStrokeColorDefaultBrush") as System.Windows.Media.Brush;
                        CSharpTabButton.BorderThickness = new System.Windows.Thickness(1);
                    }
                    else
                    {
                        // CSharpTabButton.Background = inactiveBrush;
                        CSharpTabButton.Foreground = inactiveTextBrush;
                        CSharpTabButton.BorderBrush = TryFindResource("ControlStrokeColorDefaultBrush") as System.Windows.Media.Brush;
                        CSharpTabButton.BorderThickness = new System.Windows.Thickness(0);
                    }
                }

                _logger.Debug($"✅ 选项卡按钮样式已更新: {activeTab}");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 更新选项卡按钮样式失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新编辑器内容
        /// </summary>
        private void UpdateEditorContent()
        {
            try
            {
                if (AvalonCodeEditor != null)
                {
                    // 将当前选项卡的内容加载到AvalonEdit编辑器中
                    AvalonCodeEditor.Text = _currentTabContent ?? string.Empty;

                    // 根据当前语言类型重新设置语法高亮
                    SetAvalonSyntaxHighlighting(_currentTabLanguage);

                    _logger.Info($"✅ 编辑器内容已更新: {_currentTabLanguage}, 长度: {_currentTabContent?.Length ?? 0}");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 更新编辑器内容失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新选项卡可见性
        /// </summary>
        private void UpdateTabVisibility()
        {
            try
            {
                // 根据ShowTabs属性控制选项卡容器的显示/隐藏
                if (TabsContainer != null)
                {
                    TabsContainer.Visibility = ShowTabs ? Visibility.Visible : Visibility.Collapsed;
                }

                // 在选项卡模式下，智能选择默认显示的选项卡
                if (ShowTabs)
                {
                    _logger.Info($"📋 选项卡模式 - XAML长度: {XamlCode?.Length ?? 0}, C#长度: {CSharpCode?.Length ?? 0}");

                    // 优先级：XAML > C# > CodeContent > 空内容提示
                    if (!string.IsNullOrEmpty(XamlCode))
                    {
                        _logger.Info("🎯 默认选择XAML选项卡");
                        SwitchToTab("XAML", XamlCode);
                    }
                    else if (!string.IsNullOrEmpty(CSharpCode))
                    {
                        _logger.Info("🎯 默认选择C#选项卡");
                        SwitchToTab("C#", CSharpCode);
                    }
                    else if (!string.IsNullOrEmpty(CodeContent))
                    {
                        _logger.Info("🎯 默认选择CodeContent作为XAML");
                        SwitchToTab("XAML", CodeContent);
                    }
                    else
                    {
                        _logger.Info("⚠️ 没有可用内容");
                        SwitchToTab("XAML", "<!-- 暂无内容 -->");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 更新选项卡可见性失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新当前选项卡内容
        /// </summary>
        private void UpdateCurrentTabContent()
        {
            try
            {
                if (!ShowTabs)
                {
                    _currentTabContent = CodeContent;
                    _currentTabLanguage = Language;
                    UpdateEditorContent();
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 更新当前选项卡内容失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示复制反馈
        /// </summary>
        private void ShowCopyFeedback(FrameworkElement? button)
        {
            if (button == null) return;

            try
            {
                // 保存按钮的原始内容，以便稍后恢复
                var originalContent = button.GetValue(ContentProperty);

                // 临时将按钮内容改为成功图标，给用户视觉反馈
                button.SetValue(ContentProperty, "✅");

                // 创建定时器，1秒后自动恢复按钮原始内容
                var timer = new System.Windows.Threading.DispatcherTimer
                {
                    Interval = TimeSpan.FromSeconds(1)
                };

                // 定时器触发时恢复按钮内容并停止定时器
                timer.Tick += (s, e) =>
                {
                    button.SetValue(ContentProperty, originalContent);
                    timer.Stop();
                };

                // 启动定时器
                timer.Start();
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 显示复制反馈失败: {ex.Message}");
            }
        }

        #endregion
    }
}
