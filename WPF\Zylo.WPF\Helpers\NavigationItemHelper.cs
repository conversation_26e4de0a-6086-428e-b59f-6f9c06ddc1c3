using System.Collections.ObjectModel;
using Zylo.WPF.Models.Navigation;
using Wpf.Ui.Controls;
using Zylo.WPF.Enums;

namespace Zylo.WPF.Helpers;

/// <summary>
/// NavigationItemModel帮助类
/// 提供便捷的方法来组装和管理导航项
/// </summary>
public static class NavigationItemHelper
{
    /// <summary>
    /// 创建简单导航项
    /// </summary>
    /// <param name="number">编号</param>
    /// <param name="name">名称</param>
    /// <param name="navigationTarget">导航目标</param>
    /// <returns>导航项</returns>
    public static ZyloNavigationItemModel CreateItem(string number, string name, string navigationTarget = "")
    {
        return new ZyloNavigationItemModel(number, name, navigationTarget);
    }



    /// <summary>
    /// 创建分组项（不可导航的父级项）
    /// </summary>
    /// <param name="number">编号</param>
    /// <param name="name">名称</param>
    /// <param name="isExpanded">是否默认展开</param>
    /// <returns>分组导航项</returns>
    public static ZyloNavigationItemModel CreateGroup(string number, string name, bool isExpanded = false)
    {
        return new ZyloNavigationItemModel(number, name, "")
        {
            IsExpanded = isExpanded
        };
    }

    /// <summary>
    /// 添加子项到父项
    /// </summary>
    /// <param name="parent">父项</param>
    /// <param name="child">子项</param>
    /// <exception cref="ArgumentNullException">当parent或child为null时抛出</exception>
    public static void  AddChild(ZyloNavigationItemModel parent, ZyloNavigationItemModel child)
    {
        if (parent == null) throw new ArgumentNullException(nameof(parent));
        if (child == null) throw new ArgumentNullException(nameof(child));

        child.Parent = parent;
        child.ParentNumber = parent.Number;
        parent.Children.Add(child);
    }

    /// <summary>
    /// 批量添加子项到父项
    /// </summary>
    /// <param name="parent">父项</param>
    /// <param name="children">子项集合</param>
    public static void AddChildren(ZyloNavigationItemModel parent, params ZyloNavigationItemModel[] children)
    {
        foreach (var child in children)
        {
            AddChild(parent, child);
        }
    }



    /// <summary>
    /// 按父级编号组装导航结构 - 新的组装方法
    /// 根据 ParentNumber 属性自动组装层级导航结构
    /// </summary>
    /// <param name="navigationData">导航数据数组，每个元素包含编号、名称、目标等信息</param>
    /// <returns>按父级编号组装的导航项集合</returns>
    public static ObservableCollection<ZyloNavigationItemModel> AssembleByParentNumber(NavigationData[] navigationData)
    {
        var result = new ObservableCollection<ZyloNavigationItemModel>();
        var allItems = new Dictionary<string, ZyloNavigationItemModel>();

        // 第一步：创建所有导航项
        foreach (var data in navigationData)
        {
            var item = CreateNavigationItem(data);
            allItems[data.Number] = item;
        }

        // 第二步：建立父子关系
        foreach (var data in navigationData)
        {
            var item = allItems[data.Number];

            if (!string.IsNullOrEmpty(data.ParentNumber) && allItems.ContainsKey(data.ParentNumber))
            {
                // 有父项目，添加到父项目的子集合中
                var parentItem = allItems[data.ParentNumber];

                // 确保父项目有 Children 集合（ZyloNavigationItemModel 构造函数已经初始化了，但为了安全起见）
                if (parentItem.Children == null)
                {
                    parentItem.Children = new ObservableCollection<ZyloNavigationItemModel>();
                }

                parentItem.Children.Add(item);
                item.Parent = parentItem;
                item.ParentNumber = data.ParentNumber;

                // 调试：确认父项目的子项数量
                System.Diagnostics.Debug.WriteLine($"父项目 '{parentItem.Name}' 现在有 {parentItem.Children.Count} 个子项目");
            }
            else
            {
                // 没有父项目，添加到顶级集合中
                result.Add(item);
            }
        }

        return result;
    }

    /// <summary>
    /// 根据导航数据创建导航项
    /// </summary>
    private static ZyloNavigationItemModel CreateNavigationItem(NavigationData data)
    {
        var item = new ZyloNavigationItemModel(data.Number, data.Name, data.NavigationTarget);

        // 设置图标 - 直接赋值，优先级：WpfUiSymbol > ZyloSymbol > Emoji
        item.WpfUiSymbol = data.WpfUiSymbol;
        item.ZyloSymbol = data.ZyloSymbol;
        item.Emoji = data.Emoji;

        item.IsExpanded = data.IsExpanded;
        return item;
    }

    /// <summary>
    /// 验证导航项结构是否正确
    /// </summary>
    /// <param name="items">导航项集合</param>
    /// <returns>验证结果</returns>
    public static bool ValidateNavigationStructure(ObservableCollection<ZyloNavigationItemModel> items)
    {
        foreach (var item in items)
        {
            // 检查编号是否为空
            if (string.IsNullOrEmpty(item.Number))
                return false;

            // 检查名称是否为空
            if (string.IsNullOrEmpty(item.Name))
                return false;

            // 递归检查子项
            if (item.Children.Count > 0)
            {
                foreach (var child in item.Children)
                {
                    // 检查子项的父级关系
                    if (child.Parent != item || child.ParentNumber != item.Number)
                        return false;
                }

                if (!ValidateNavigationStructure(item.Children))
                    return false;
            }
        }

        return true;
    }

    /// <summary>
    /// 获取导航项统计信息
    /// </summary>
    /// <param name="items">导航项集合</param>
    /// <returns>统计信息</returns>
    public static NavigationStatistics GetStatistics(ObservableCollection<ZyloNavigationItemModel> items)
    {
        var stats = new NavigationStatistics();
        CountItems(items, stats);
        return stats;
    }

    /// <summary>
    /// 递归统计导航项
    /// </summary>
    private static void CountItems(ObservableCollection<ZyloNavigationItemModel> items, NavigationStatistics stats)
    {
        foreach (var item in items)
        {
            stats.TotalItems++;

            if (item.WpfUiSymbol.HasValue && item.WpfUiSymbol.Value != SymbolRegular.Empty)
                stats.WpfUiSymbolCount++;
            else if (item.ZyloSymbol.HasValue && item.ZyloSymbol.Value != ZyloSymbol.None)
                stats.ZyloIconCount++;
            else if (!string.IsNullOrEmpty(item.Emoji))
                stats.FontIconCount++;
            else
                stats.NoIconCount++;

            if (string.IsNullOrEmpty(item.NavigationTarget))
                stats.GroupCount++;
            else
                stats.NavigableCount++;

            if (item.Children.Count > 0)
                CountItems(item.Children, stats);
        }
    }
}

/// <summary>
/// 导航数据 - 用于按编号组装
/// </summary>
public class NavigationData
{
    public string Number { get; set; }
    public string Name { get; set; }
    public string NavigationTarget { get; set; }

    /// <summary>
    /// 父级编号 - 用于 AssembleByParentNumber 方法
    /// 如果为 null 或空字符串，则表示顶级项目
    /// </summary>
    public string? ParentNumber { get; set; }

    /// <summary>
    /// WPF-UI Symbol图标枚举
    /// 使用WPF-UI原生的SymbolRegular枚举
    /// </summary>
    public SymbolRegular? WpfUiSymbol { get; set; }

    /// <summary>
    /// Zylo自定义图标枚举
    /// 对应iconfont.ttf字体文件中的图标
    /// </summary>
    public ZyloSymbol? ZyloSymbol { get; set; }

    /// <summary>
    /// Emoji图标字符串
    /// 直接显示的Emoji字符
    /// </summary>
    public string? Emoji { get; set; }

    public bool IsExpanded { get; set; }

    public NavigationData(string number, string name, string navigationTarget)
    {
        Number = number;
        Name = name;
        NavigationTarget = navigationTarget;
    }

    /// <summary>
    /// 完整构造函数 - 支持所有属性的初始化
    /// </summary>
    /// <param name="number">导航项编号</param>
    /// <param name="name">显示名称</param>
    /// <param name="navigationTarget">导航目标</param>
    /// <param name="wpfUiSymbol">WPF-UI图标</param>
    /// <param name="parentNumber">父级编号</param>
    /// <param name="isExpanded">是否展开</param>
    /// <param name="zyloSymbol">Zylo自定义图标</param>
    /// <param name="emoji">Emoji图标</param>
    public NavigationData(string number, string name, string navigationTarget,
        SymbolRegular? wpfUiSymbol = null, string? parentNumber = null, bool isExpanded = false,
        ZyloSymbol? zyloSymbol = null, string? emoji = null)
    {
        Number = number;
        Name = name;
        NavigationTarget = navigationTarget;
        WpfUiSymbol = wpfUiSymbol;
        ParentNumber = parentNumber;
        IsExpanded = isExpanded;
        ZyloSymbol = zyloSymbol;
        Emoji = emoji;
    }
}

/// <summary>
/// 导航项统计信息
/// </summary>
public class NavigationStatistics
{
    public int TotalItems { get; set; }
    public int ZyloIconCount { get; set; }
    public int WpfUiSymbolCount { get; set; }
    public int FontIconCount { get; set; }
    public int NoIconCount { get; set; }
    public int GroupCount { get; set; }
    public int NavigableCount { get; set; }

    public override string ToString()
    {
        return $"总计: {TotalItems}, Zylo图标: {ZyloIconCount}, WPF-UI图标: {WpfUiSymbolCount}, " +
               $"字体图标: {FontIconCount}, 无图标: {NoIconCount}, 分组: {GroupCount}, 可导航: {NavigableCount}";
    }
}
