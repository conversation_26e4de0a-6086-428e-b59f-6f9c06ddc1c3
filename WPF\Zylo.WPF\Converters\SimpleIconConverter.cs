using System.Globalization;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Media;
using Zylo.WPF.Enums;
using Wpf.Ui.Controls;
using Zylo.WPF.Controls.Icon;

namespace Zylo.WPF.Converters;

/// <summary>
/// 简化的图标转换器
/// 支持：SymbolRegular枚举、ZyloSymbol枚举、Emoji字符串
/// </summary>
public class SimpleIconConverter : IMultiValueConverter
{
    /// <summary>
    /// 转换多个图标值为UI元素
    /// </summary>
    /// <param name="values">图标值数组：[WpfUiSymbol, ZyloSymbol, Emoji, DefaultIcon?, FontSize?, ColorBrush?]</param>
    /// <param name="targetType">目标类型</param>
    /// <param name="parameter">转换参数 - 可以传递字体大小或"大小,颜色"格式</param>
    /// <param name="culture">文化信息</param>
    /// <returns>图标UI元素</returns>
    public object? Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
    {
        if (values == null || values.Length < 3)
            return null;

        var wpfUiSymbol = values[0] as SymbolRegular?;
        var zyloSymbol = values[1] as ZyloSymbol?;
        var emoji = values[2] as string;
        var defaultIcon = values.Length > 3 ? values[3] as string : null;

        // 获取字体大小和颜色参数
        double fontSize = 20;
        string? colorResource = null;

        // 支持从values数组获取字体大小和颜色（考虑DefaultIcon占用了第4个位置）
        if (values.Length > 4 && values[4] != null && double.TryParse(values[4].ToString(), out double sizeFromValues))
        {
            fontSize = sizeFromValues;
        }
        if (values.Length > 5 && values[5] != null)
        {
            colorResource = values[5].ToString();
        }

        // 支持从parameter获取参数
        if (parameter != null)
        {
            var paramStr = parameter.ToString();
            if (paramStr?.Contains(',') == true)
            {
                // 格式: "大小,颜色资源名"
                var parts = paramStr.Split(',');
                if (parts.Length >= 1 && double.TryParse(parts[0], out double paramSize))
                    fontSize = paramSize;
                if (parts.Length >= 2 && !string.IsNullOrEmpty(parts[1]))
                    colorResource = parts[1];
            }
            else if (double.TryParse(paramStr, out double paramSize))
            {
                fontSize = paramSize;
            }
        }

        // 优先级：WpfUiSymbol > ZyloSymbol > Emoji
        if (wpfUiSymbol.HasValue && wpfUiSymbol.Value != SymbolRegular.Empty)
        {
            var symbolIcon = new SymbolIcon
            {
                Symbol = wpfUiSymbol.Value,
                FontSize = fontSize
            };

            // 设置颜色
            if (!string.IsNullOrEmpty(colorResource))
                symbolIcon.SetResourceReference(SymbolIcon.ForegroundProperty, colorResource);
            else
                symbolIcon.SetResourceReference(SymbolIcon.ForegroundProperty, "TextFillColorPrimaryBrush");

            return symbolIcon;
        }

        if (zyloSymbol.HasValue && zyloSymbol.Value != ZyloSymbol.None)
        {
            var zyloIcon = new ZyloIcon
            {
                Symbol = zyloSymbol.Value,
                FontSize = fontSize
            };

            // 设置颜色
            if (!string.IsNullOrEmpty(colorResource))
                zyloIcon.SetResourceReference(ZyloIcon.ForegroundProperty, colorResource);
            else
                zyloIcon.SetResourceReference(ZyloIcon.ForegroundProperty, "TextFillColorPrimaryBrush");

            return zyloIcon;
        }

        if (!string.IsNullOrEmpty(emoji))
        {
            // Emoji字体大小稍微小一点，确保显示完整
            var emojiFontSize = fontSize * 0.8;

            var textBlock = new System.Windows.Controls.TextBlock
            {
                Text = emoji,
                FontFamily = new FontFamily("Segoe UI Emoji"),
                FontSize = emojiFontSize,
                VerticalAlignment = VerticalAlignment.Center,
                HorizontalAlignment = HorizontalAlignment.Center,
                TextAlignment = TextAlignment.Center
            };

            // 设置颜色 (Emoji通常不需要改颜色，但也支持)
            if (!string.IsNullOrEmpty(colorResource))
                textBlock.SetResourceReference(System.Windows.Controls.TextBlock.ForegroundProperty, colorResource);
            else
                textBlock.SetResourceReference(System.Windows.Controls.TextBlock.ForegroundProperty, "TextFillColorPrimaryBrush");

            return textBlock;
        }

        // 如果前面的图标都没有，使用DefaultIcon作为回退
        if (!string.IsNullOrEmpty(defaultIcon))
        {
            // DefaultIcon通常是Emoji，使用与Emoji相同的处理方式
            var emojiFontSize = fontSize * 0.8;

            var textBlock = new System.Windows.Controls.TextBlock
            {
                Text = defaultIcon,
                FontFamily = new FontFamily("Segoe UI Emoji"),
                FontSize = emojiFontSize,
                VerticalAlignment = VerticalAlignment.Center,
                HorizontalAlignment = HorizontalAlignment.Center,
                TextAlignment = TextAlignment.Center
            };

            // 设置颜色
            if (!string.IsNullOrEmpty(colorResource))
                textBlock.SetResourceReference(System.Windows.Controls.TextBlock.ForegroundProperty, colorResource);
            else
                textBlock.SetResourceReference(System.Windows.Controls.TextBlock.ForegroundProperty, "TextFillColorPrimaryBrush");

            return textBlock;
        }

        return null;
    }

    /// <summary>
    /// 反向转换（不支持）
    /// </summary>
    public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
