// WPF-UI Flyout 基础功能 C# 示例

// 1. 创建简单的 Flyout
private void CreateSimpleFlyout()
{
    var button = new Wpf.Ui.Controls.Button
    {
        Content = "显示 Flyout"
    };

    var flyout = new Wpf.Ui.Controls.Flyout
    {
        Placement = PlacementMode.Bottom,
        Content = new StackPanel
        {
            Margin = new Thickness(16),
            Children =
            {
                new TextBlock
                {
                    Text = "这是一个简单的 Flyout",
                    FontWeight = FontWeights.Medium,
                    Margin = new Thickness(0, 0, 0, 8)
                },
                new TextBlock
                {
                    Text = "Flyout 提供了轻量级的浮出面板功能。",
                    TextWrapping = TextWrapping.Wrap,
                    Margin = new Thickness(0, 0, 0, 12)
                },
                new Button
                {
                    Content = "关闭",
                    HorizontalAlignment = HorizontalAlignment.Left
                }
            }
        }
    };

    // 绑定到按钮
    button.Click += (s, e) => flyout.IsOpen = !flyout.IsOpen;
}

// 2. 通过 ViewModel 控制 Flyout
public class FlyoutViewModel : ObservableObject
{
    [ObservableProperty]
    private bool isSimpleFlyoutOpen = false;

    [RelayCommand]
    private void ShowSimpleFlyout()
    {
        IsSimpleFlyoutOpen = !IsSimpleFlyoutOpen;
    }

    [RelayCommand]
    private void CloseSimpleFlyout()
    {
        IsSimpleFlyoutOpen = false;
    }
}

// 3. 处理 Flyout 事件
private void SetupFlyoutEvents()
{
    var flyout = new Wpf.Ui.Controls.Flyout();
    
    flyout.Opened += (sender, e) =>
    {
        Console.WriteLine("Flyout 已打开");
    };
    
    flyout.Closed += (sender, e) =>
    {
        Console.WriteLine("Flyout 已关闭");
    };
}

// 4. 动态设置 Flyout 内容
private void SetDynamicContent()
{
    var flyout = new Wpf.Ui.Controls.Flyout();
    
    // 根据条件设置不同的内容
    if (DateTime.Now.Hour < 12)
    {
        flyout.Content = new TextBlock { Text = "早上好！" };
    }
    else
    {
        flyout.Content = new TextBlock { Text = "下午好！" };
    }
}
