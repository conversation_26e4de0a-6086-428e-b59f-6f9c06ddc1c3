using System.Collections;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using Microsoft.Xaml.Behaviors;

namespace Zylo.WPF.Behaviors
{
    /// <summary>
    /// 通用TreeView行为 - 支持多种绑定和事件
    /// </summary>
    public class TreeViewBehavior : Behavior<TreeView>
    {
        #region SelectedItem 依赖属性

        /// <summary>
        /// SelectedItem依赖属性
        /// </summary>
        public static readonly DependencyProperty SelectedItemProperty =
            DependencyProperty.Register(
                nameof(SelectedItem),
                typeof(object),
                typeof(TreeViewBehavior),
                new FrameworkPropertyMetadata(null, FrameworkPropertyMetadataOptions.BindsTwoWayByDefault, OnSelectedItemChanged));

        /// <summary>
        /// 选中的项目
        /// </summary>
        public object SelectedItem
        {
            get => GetValue(SelectedItemProperty);
            set => SetValue(SelectedItemProperty, value);
        }

        private static void OnSelectedItemChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is TreeViewBehavior behavior && behavior.AssociatedObject != null)
            {
                behavior.UpdateTreeViewSelection(e.NewValue);
            }
        }

        #endregion

        #region ExpandedItems 依赖属性

        /// <summary>
        /// ExpandedItems依赖属性
        /// </summary>
        public static readonly DependencyProperty ExpandedItemsProperty =
            DependencyProperty.Register(
                nameof(ExpandedItems),
                typeof(IEnumerable),
                typeof(TreeViewBehavior),
                new PropertyMetadata(null));

        /// <summary>
        /// 展开的项目集合
        /// </summary>
        public IEnumerable ExpandedItems
        {
            get => (IEnumerable)GetValue(ExpandedItemsProperty);
            set => SetValue(ExpandedItemsProperty, value);
        }

        #endregion

        #region SelectionChangedCommand 依赖属性

        /// <summary>
        /// SelectionChangedCommand依赖属性
        /// </summary>
        public static readonly DependencyProperty SelectionChangedCommandProperty =
            DependencyProperty.Register(
                nameof(SelectionChangedCommand),
                typeof(ICommand),
                typeof(TreeViewBehavior),
                new PropertyMetadata(null));

        /// <summary>
        /// 选择变化命令
        /// </summary>
        public ICommand SelectionChangedCommand
        {
            get => (ICommand)GetValue(SelectionChangedCommandProperty);
            set => SetValue(SelectionChangedCommandProperty, value);
        }

        #endregion

        #region ItemExpandedCommand 依赖属性

        /// <summary>
        /// ItemExpandedCommand依赖属性
        /// </summary>
        public static readonly DependencyProperty ItemExpandedCommandProperty =
            DependencyProperty.Register(
                nameof(ItemExpandedCommand),
                typeof(ICommand),
                typeof(TreeViewBehavior),
                new PropertyMetadata(null));

        /// <summary>
        /// 项目展开命令
        /// </summary>
        public ICommand ItemExpandedCommand
        {
            get => (ICommand)GetValue(ItemExpandedCommandProperty);
            set => SetValue(ItemExpandedCommandProperty, value);
        }

        #endregion

        #region ItemCollapsedCommand 依赖属性

        /// <summary>
        /// ItemCollapsedCommand依赖属性
        /// </summary>
        public static readonly DependencyProperty ItemCollapsedCommandProperty =
            DependencyProperty.Register(
                nameof(ItemCollapsedCommand),
                typeof(ICommand),
                typeof(TreeViewBehavior),
                new PropertyMetadata(null));

        /// <summary>
        /// 项目折叠命令
        /// </summary>
        public ICommand ItemCollapsedCommand
        {
            get => (ICommand)GetValue(ItemCollapsedCommandProperty);
            set => SetValue(ItemCollapsedCommandProperty, value);
        }

        #endregion

        #region Behavior重写

        /// <summary>
        /// 附加到TreeView时
        /// </summary>
        protected override void OnAttached()
        {
            base.OnAttached();

            if (AssociatedObject != null)
            {
                AssociatedObject.SelectedItemChanged += OnTreeViewSelectedItemChanged;
                // TreeViewItem的Expanded/Collapsed事件需要通过AddHandler添加
                AssociatedObject.AddHandler(TreeViewItem.ExpandedEvent, new RoutedEventHandler(OnTreeViewItemExpanded));
                AssociatedObject.AddHandler(TreeViewItem.CollapsedEvent, new RoutedEventHandler(OnTreeViewItemCollapsed));
            }
        }

        /// <summary>
        /// 从TreeView分离时
        /// </summary>
        protected override void OnDetaching()
        {
            if (AssociatedObject != null)
            {
                AssociatedObject.SelectedItemChanged -= OnTreeViewSelectedItemChanged;
                AssociatedObject.RemoveHandler(TreeViewItem.ExpandedEvent, new RoutedEventHandler(OnTreeViewItemExpanded));
                AssociatedObject.RemoveHandler(TreeViewItem.CollapsedEvent, new RoutedEventHandler(OnTreeViewItemCollapsed));
            }

            base.OnDetaching();
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// TreeView选择变化时
        /// </summary>
        private void OnTreeViewSelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
        {
            // 更新绑定的SelectedItem
            SelectedItem = e.NewValue;
            
            // 执行选择变化命令
            if (SelectionChangedCommand?.CanExecute(e.NewValue) == true)
            {
                SelectionChangedCommand.Execute(e.NewValue);
            }
        }

        /// <summary>
        /// TreeView项目展开时
        /// </summary>
        private void OnTreeViewItemExpanded(object sender, RoutedEventArgs e)
        {
            if (e.OriginalSource is TreeViewItem item && item.DataContext != null)
            {
                // 执行展开命令
                if (ItemExpandedCommand?.CanExecute(item.DataContext) == true)
                {
                    ItemExpandedCommand.Execute(item.DataContext);
                }
            }
        }

        /// <summary>
        /// TreeView项目折叠时
        /// </summary>
        private void OnTreeViewItemCollapsed(object sender, RoutedEventArgs e)
        {
            if (e.OriginalSource is TreeViewItem item && item.DataContext != null)
            {
                // 执行折叠命令
                if (ItemCollapsedCommand?.CanExecute(item.DataContext) == true)
                {
                    ItemCollapsedCommand.Execute(item.DataContext);
                }
            }
        }

        /// <summary>
        /// 更新TreeView选择
        /// </summary>
        private void UpdateTreeViewSelection(object newValue)
        {
            if (AssociatedObject?.SelectedItem != newValue)
            {
                // 这里可以添加更复杂的选择逻辑
                // 比如根据数据对象查找对应的TreeViewItem并选中
            }
        }

        #endregion
    }
}
