<!-- PasswordBox 样式使用示例 -->
<StackPanel Margin="20" Spacing="15">

    <!-- 基础样式系列 -->
    <GroupBox Header="基础样式系列" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <!-- 基础样式 -->
            <StackPanel Margin="8">
                <TextBlock Text="基础样式" FontWeight="Bold" Margin="0,0,0,4"/>
                <PasswordBox Style="{StaticResource PasswordBoxBaseStyle}"
                             ui:TextBoxHelper.PlaceholderText="基础样式"
                             Width="150"/>
            </StackPanel>

            <!-- 标准样式 -->
            <StackPanel Margin="8">
                <TextBlock Text="标准样式" FontWeight="Bold" Margin="0,0,0,4"/>
                <PasswordBox Style="{StaticResource PasswordBoxStyle}"
                             ui:TextBoxHelper.PlaceholderText="标准样式"/>
            </StackPanel>

            <!-- 小型样式 -->
            <StackPanel Margin="8">
                <TextBlock Text="小型样式" FontWeight="Bold" Margin="0,0,0,4"/>
                <PasswordBox Style="{StaticResource SmallPasswordBoxStyle}"
                             ui:TextBoxHelper.PlaceholderText="小型样式"/>
            </StackPanel>

            <!-- 大型样式 -->
            <StackPanel Margin="8">
                <TextBlock Text="大型样式" FontWeight="Bold" Margin="0,0,0,4"/>
                <PasswordBox Style="{StaticResource LargePasswordBoxStyle}"
                             ui:TextBoxHelper.PlaceholderText="大型样式"/>
            </StackPanel>
        </WrapPanel>
    </GroupBox>

    <!-- 特殊样式系列 -->
    <GroupBox Header="特殊样式系列" Padding="15">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- 透明样式 -->
            <Border Grid.Column="0" Background="{DynamicResource ControlFillColorSecondaryBrush}"
                    CornerRadius="8" Padding="12" Margin="0,0,4,0">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                        <TextBlock Text="🔍" FontSize="14" Margin="0,0,4,0"/>
                        <TextBlock Text="透明样式" FontWeight="Bold"/>
                    </StackPanel>
                    <TextBlock Text="底部边框，透明背景"
                               FontSize="11"
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                               Margin="0,0,0,8"/>
                    <PasswordBox Style="{StaticResource TransparentPasswordBoxStyle}"
                                 ui:TextBoxHelper.PlaceholderText="透明背景"/>
                </StackPanel>
            </Border>
            
            <!-- 圆角样式 -->
            <Border Grid.Column="1" Background="{DynamicResource ControlFillColorSecondaryBrush}"
                    CornerRadius="8" Padding="12" Margin="4,0">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                        <TextBlock Text="🎨" FontSize="14" Margin="0,0,4,0"/>
                        <TextBlock Text="圆角样式" FontWeight="Bold"/>
                    </StackPanel>
                    <TextBlock Text="圆角边框，增大内边距"
                               FontSize="11"
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                               Margin="0,0,0,8"/>
                    <PasswordBox Style="{StaticResource RoundedPasswordBoxStyle}"
                                 ui:TextBoxHelper.PlaceholderText="圆角边框"/>
                </StackPanel>
            </Border>
            
            <!-- 安全增强样式 -->
            <Border Grid.Column="2" Background="{DynamicResource ControlFillColorSecondaryBrush}"
                    CornerRadius="8" Padding="12" Margin="4,0,0,0">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                        <TextBlock Text="🛡️" FontSize="14" Margin="0,0,4,0"/>
                        <TextBlock Text="安全增强" FontWeight="Bold"/>
                    </StackPanel>
                    <TextBlock Text="安全增强，禁用复制粘贴"
                               FontSize="11"
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                               Margin="0,0,0,8"/>
                    <PasswordBox Style="{StaticResource SecurePasswordBoxStyle}"
                                 ui:TextBoxHelper.PlaceholderText="安全增强"/>
                </StackPanel>
            </Border>
        </Grid>
    </GroupBox>

    <!-- 验证状态样式 -->
    <GroupBox Header="验证状态样式" Padding="15">
        <StackPanel Spacing="12">
            <!-- 错误状态 -->
            <StackPanel>
                <TextBlock Text="错误状态 (ErrorPasswordBoxStyle)" FontWeight="Bold" Margin="0,0,0,4"/>
                <PasswordBox Style="{StaticResource ErrorPasswordBoxStyle}"
                             ui:TextBoxHelper.PlaceholderText="密码格式错误"
                             Width="250"
                             HorizontalAlignment="Left"/>
                <TextBlock Text="密码必须包含至少8个字符" 
                           FontSize="11" 
                           Foreground="Red" 
                           Margin="0,2,0,0"/>
            </StackPanel>

            <!-- 成功状态 -->
            <StackPanel>
                <TextBlock Text="成功状态 (SuccessPasswordBoxStyle)" FontWeight="Bold" Margin="0,0,0,4"/>
                <PasswordBox Style="{StaticResource SuccessPasswordBoxStyle}"
                             ui:TextBoxHelper.PlaceholderText="密码格式正确"
                             Width="250"
                             HorizontalAlignment="Left"/>
                <TextBlock Text="密码符合安全要求" 
                           FontSize="11" 
                           Foreground="Green" 
                           Margin="0,2,0,0"/>
            </StackPanel>
        </StackPanel>
    </GroupBox>

    <!-- 自定义样式示例 -->
    <GroupBox Header="自定义样式示例" Padding="15">
        <StackPanel>
            <TextBlock Text="基于基础样式创建自定义样式：" FontWeight="Bold" Margin="0,0,0,8"/>
            
            <!-- 自定义样式代码示例 -->
            <Border Background="{DynamicResource ControlFillColorSecondaryBrush}"
                    CornerRadius="4" Padding="12" Margin="0,0,0,8">
                <TextBlock FontFamily="Consolas" FontSize="11" TextWrapping="Wrap">
                    <Run Text="&lt;Style x:Key=&quot;MyCustomPasswordBoxStyle&quot; TargetType=&quot;PasswordBox&quot; BasedOn=&quot;{StaticResource PasswordBoxBaseStyle}&quot;&gt;"/>
                    <LineBreak/>
                    <Run Text="    &lt;Setter Property=&quot;Background&quot; Value=&quot;LightBlue&quot;/&gt;"/>
                    <LineBreak/>
                    <Run Text="    &lt;Setter Property=&quot;BorderBrush&quot; Value=&quot;DarkBlue&quot;/&gt;"/>
                    <LineBreak/>
                    <Run Text="    &lt;Setter Property=&quot;BorderThickness&quot; Value=&quot;2&quot;/&gt;"/>
                    <LineBreak/>
                    <Run Text="    &lt;Setter Property=&quot;CornerRadius&quot; Value=&quot;8&quot;/&gt;"/>
                    <LineBreak/>
                    <Run Text="&lt;/Style&gt;"/>
                </TextBlock>
            </Border>
            
            <!-- 样式使用示例 -->
            <TextBlock Text="使用自定义样式：" FontWeight="Bold" Margin="0,8,0,4"/>
            <Border Background="{DynamicResource ControlFillColorSecondaryBrush}"
                    CornerRadius="4" Padding="12">
                <TextBlock FontFamily="Consolas" FontSize="11">
                    <Run Text="&lt;PasswordBox Style=&quot;{StaticResource MyCustomPasswordBoxStyle}&quot;"/>
                    <LineBreak/>
                    <Run Text="             ui:TextBoxHelper.PlaceholderText=&quot;自定义样式密码框&quot;/&gt;"/>
                </TextBlock>
            </Border>
        </StackPanel>
    </GroupBox>

    <!-- 样式继承说明 -->
    <GroupBox Header="样式继承关系" Padding="15">
        <StackPanel>
            <TextBlock Text="Zylo.WPF PasswordBox 样式继承关系：" FontWeight="Bold" Margin="0,0,0,8"/>
            
            <Border Background="{DynamicResource ControlFillColorSecondaryBrush}"
                    CornerRadius="4" Padding="12">
                <StackPanel>
                    <TextBlock Text="PasswordBoxBaseStyle (基础样式)" FontWeight="Bold"/>
                    <TextBlock Text="├── PasswordBoxStyle (标准样式)" Margin="16,2,0,2"/>
                    <TextBlock Text="├── SmallPasswordBoxStyle (小型样式)" Margin="16,2,0,2"/>
                    <TextBlock Text="├── LargePasswordBoxStyle (大型样式)" Margin="16,2,0,2"/>
                    <TextBlock Text="├── TransparentPasswordBoxStyle (透明样式)" Margin="16,2,0,2"/>
                    <TextBlock Text="├── RoundedPasswordBoxStyle (圆角样式)" Margin="16,2,0,2"/>
                    <TextBlock Text="├── SecurePasswordBoxStyle (安全增强样式)" Margin="16,2,0,2"/>
                    <TextBlock Text="├── ErrorPasswordBoxStyle (错误状态样式)" Margin="16,2,0,2"/>
                    <TextBlock Text="└── SuccessPasswordBoxStyle (成功状态样式)" Margin="16,2,0,2"/>
                </StackPanel>
            </Border>
        </StackPanel>
    </GroupBox>

</StackPanel>
