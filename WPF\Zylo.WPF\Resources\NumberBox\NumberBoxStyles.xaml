<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml">

    <!-- NumberBox 基础样式 -->
    <Style x:Key="NumberBoxBaseStyle" TargetType="ui:NumberBox">
        <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontFamily" Value="{DynamicResource ContentControlThemeFontFamily}"/>
        <Setter Property="HorizontalAlignment" Value="Stretch"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="MinHeight" Value="32"/>

        <!-- 添加鼠标悬停和焦点效果 -->
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorSecondaryBrush}"/>
            </Trigger>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="Background" Value="{DynamicResource ControlFillColorInputActiveBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource AccentControlElevationBorderBrush}"/>
                <Setter Property="BorderThickness" Value="2"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Opacity" Value="0.5"/>
                <Setter Property="Background" Value="{DynamicResource ControlFillColorDisabledBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- NumberBox 标准样式 -->
    <Style x:Key="NumberBoxStyle" TargetType="ui:NumberBox" BasedOn="{StaticResource NumberBoxBaseStyle}">
        <!-- 标准样式的特定设置 -->
    </Style>

    <!-- NumberBox 小型样式 -->
    <Style x:Key="SmallNumberBoxStyle" TargetType="ui:NumberBox" BasedOn="{StaticResource NumberBoxBaseStyle}">
        <Setter Property="Padding" Value="6"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="MinHeight" Value="28"/>
        <!-- 小型样式的特定设置 -->
    </Style>

    <!-- NumberBox 大型样式 -->
    <Style x:Key="LargeNumberBoxStyle" TargetType="ui:NumberBox" BasedOn="{StaticResource NumberBoxBaseStyle}">
        <Setter Property="Padding" Value="12"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="MinHeight" Value="40"/>
        <!-- 大型样式的特定设置 -->
    </Style>

    <!-- NumberBox 透明样式 -->
    <Style x:Key="TransparentNumberBoxStyle" TargetType="ui:NumberBox" BasedOn="{StaticResource NumberBoxBaseStyle}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
        <!-- 透明样式的特定设置 -->
    </Style>

    <!-- NumberBox 圆角样式 -->
    <Style x:Key="RoundedNumberBoxStyle" TargetType="ui:NumberBox" BasedOn="{StaticResource NumberBoxBaseStyle}">
        <!-- 注意：WPF-UI NumberBox 可能不支持 CornerRadius 属性 -->
        <!-- 圆角样式的特定设置 -->
    </Style>

    <!-- NumberBox 无边框样式 -->
    <Style x:Key="BorderlessNumberBoxStyle" TargetType="ui:NumberBox" BasedOn="{StaticResource NumberBoxBaseStyle}">
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Background" Value="{DynamicResource ControlFillColorTertiaryBrush}"/>
        <!-- 无边框样式的特定设置 -->
    </Style>

    <!-- NumberBox 货币样式 -->
    <Style x:Key="CurrencyNumberBoxStyle" TargetType="ui:NumberBox" BasedOn="{StaticResource NumberBoxBaseStyle}">
        <Setter Property="Background" Value="{DynamicResource SystemFillColorSuccessBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource SystemFillColorSuccessTextBrush}"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <!-- 货币样式的特定设置 -->
    </Style>

    <!-- NumberBox 百分比样式 -->
    <Style x:Key="PercentageNumberBoxStyle" TargetType="ui:NumberBox" BasedOn="{StaticResource NumberBoxBaseStyle}">
        <Setter Property="Background" Value="{DynamicResource SystemFillColorCautionBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource SystemFillColorCautionTextBrush}"/>
        <!-- 百分比样式的特定设置 -->
    </Style>

</ResourceDictionary>
