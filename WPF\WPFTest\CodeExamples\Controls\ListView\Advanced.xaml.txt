<!-- ListView 高级功能示例 -->
<!-- 展示自定义模板、多选模式和复杂数据绑定 -->

<StackPanel>
    <!-- 控制面板 -->
    <Border Background="LightBlue" Padding="12" CornerRadius="6" Margin="0,0,0,16">
        <StackPanel Orientation="Horizontal">
            <ui:TextBox Text="{Binding NewItemName, Mode=TwoWay}"
                        PlaceholderText="输入新项目名称"
                        Width="200"
                        Margin="0,0,8,0"/>
            <Button Content="添加项目"
                    Command="{Binding AddDataItemCommand}"
                    Margin="0,0,8,0"/>
            <Button Content="删除选中"
                    Command="{Binding DeleteSelectedItemCommand}"
                    Margin="0,0,8,0"/>
            <ToggleButton Content="多选模式"
                          IsChecked="{Binding IsMultiSelectEnabled, Mode=TwoWay}"/>
        </StackPanel>
    </Border>

    <!-- 复杂数据项列表 -->
    <ListView ItemsSource="{Binding DataItems}"
              SelectedItem="{Binding SelectedDataItem, Mode=TwoWay}"
              SelectionMode="{Binding IsMultiSelectEnabled, Converter={StaticResource BoolToSelectionModeConverter}}"
              Height="300"
              ScrollViewer.HorizontalScrollBarVisibility="Disabled">
        
        <!-- 自定义项模板 -->
        <ListView.ItemTemplate>
            <DataTemplate>
                <Border Background="White"
                        BorderBrush="LightGray"
                        BorderThickness="1"
                        CornerRadius="6"
                        Padding="12"
                        Margin="4">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- 图标 -->
                        <Border Grid.Column="0"
                                Background="LightBlue"
                                Width="40"
                                Height="40"
                                CornerRadius="20"
                                Margin="0,0,12,0">
                            <TextBlock Text="{Binding Icon}"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       FontWeight="Bold"/>
                        </Border>
                        
                        <!-- 内容 -->
                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                            <TextBlock Text="{Binding Name}"
                                       FontWeight="Medium"
                                       FontSize="14"/>
                            <TextBlock Text="{Binding Description}"
                                       FontSize="12"
                                       Foreground="Gray"
                                       Margin="0,2,0,0"/>
                            <TextBlock Text="{Binding CreatedDate, StringFormat='创建时间: {0:yyyy-MM-dd HH:mm}'}"
                                       FontSize="10"
                                       Foreground="DarkGray"
                                       Margin="0,2,0,0"/>
                        </StackPanel>
                        
                        <!-- 状态标签 -->
                        <StackPanel Grid.Column="2" VerticalAlignment="Center">
                            <Border Background="Orange"
                                    CornerRadius="12"
                                    Padding="8,4">
                                <TextBlock Text="{Binding Category}"
                                           FontSize="10"
                                           Foreground="White"
                                           FontWeight="Medium"/>
                            </Border>
                            <CheckBox IsChecked="{Binding IsEnabled}"
                                      Content="启用"
                                      FontSize="10"
                                      Margin="0,4,0,0"/>
                        </StackPanel>
                    </Grid>
                </Border>
            </DataTemplate>
        </ListView.ItemTemplate>
        
        <!-- 自定义容器样式 -->
        <ListView.ItemContainerStyle>
            <Style TargetType="ListViewItem">
                <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                <Setter Property="Margin" Value="2"/>
                <Setter Property="Padding" Value="0"/>
                <Style.Triggers>
                    <Trigger Property="IsSelected" Value="True">
                        <Setter Property="Background" Value="LightBlue"/>
                    </Trigger>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="LightGray"/>
                    </Trigger>
                </Style.Triggers>
            </Style>
        </ListView.ItemContainerStyle>
    </ListView>

    <!-- 选择状态显示 -->
    <Border Background="LightGreen" Padding="8" CornerRadius="4" Margin="0,16,0,0">
        <StackPanel>
            <TextBlock Text="选择状态：" FontWeight="Medium"/>
            <TextBlock Text="{Binding SelectedDataItem.Name, StringFormat='当前选中: {0}', TargetNullValue='未选择任何项'}"
                       Foreground="DarkGreen"/>
            <TextBlock Text="{Binding DataItems.Count, StringFormat='总项目数: {0}'}"
                       Foreground="DarkGreen"/>
        </StackPanel>
    </Border>
</StackPanel>

<!-- 
高级特性：
1. 自定义 ItemTemplate 定义项目外观
2. 自定义 ItemContainerStyle 定义容器样式
3. 支持多选模式切换
4. 复杂数据对象绑定
5. 触发器实现交互效果
6. 数据转换器使用
-->
