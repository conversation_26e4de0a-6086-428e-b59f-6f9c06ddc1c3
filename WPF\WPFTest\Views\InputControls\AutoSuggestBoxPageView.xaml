<UserControl
    d:DataContext="{d:DesignInstance inputControls:AutoSuggestBoxPageViewModel}"
    d:DesignHeight="2000"
    d:DesignWidth="1200"
    mc:Ignorable="d"
    mvvm:ViewModelLocator.AutoWireViewModel="True"
    x:Class="WPFTest.Views.InputControls.AutoSuggestBoxPageView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:codeExample="clr-namespace:Zylo.WPF.Controls.CodeExample;assembly=Zylo.WPF"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:inputControls="clr-namespace:WPFTest.ViewModels.InputControls"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:mvvm="http://prismlibrary.com/"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <UserControl.Resources>
        <!--  🎨 所有样式来自 Zylo.WPF 项目  -->
        <!--  AutoSuggestBox 样式已在 Zylo.WPF/Resources/AutoSuggestBox/ 目录下定义  -->
        <!--  可用样式：AutoSuggestBoxStyle, SmallAutoSuggestBoxStyle, LargeAutoSuggestBoxStyle  -->
    </UserControl.Resources>

    <ScrollViewer
        HorizontalScrollBarVisibility="Disabled"
        Padding="24"
        VerticalScrollBarVisibility="Auto">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <!--  标题区域  -->
                <RowDefinition Height="*" />
                <!--  主要内容  -->
                <RowDefinition Height="Auto" />
                <!--  底部操作栏  -->
            </Grid.RowDefinitions>

            <!--  标题区域  -->
            <StackPanel Grid.Row="0" Margin="0,0,0,20">
                <TextBlock
                    FontSize="32"
                    FontWeight="Bold"
                    Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                    Margin="0,0,0,8"
                    Text="🔍 AutoSuggestBox 控件展示" />
                <TextBlock
                    FontSize="16"
                    Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                    Text="展示 AutoSuggestBox 控件的搜索建议、自动完成和数据绑定功能"
                    TextWrapping="Wrap" />

                <!--  状态显示  -->
                <Border
                    Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Margin="0,16,0,0"
                    Padding="16,12">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <ui:SymbolIcon
                            FontSize="20"
                            Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                            Margin="0,0,12,0"
                            Symbol="Info24"
                            VerticalAlignment="Center" />
                        <TextBlock
                            FontSize="14"
                            Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                            Grid.Column="1"
                            Text="{Binding StatusMessage}"
                            TextWrapping="Wrap"
                            VerticalAlignment="Center" />
                    </Grid>
                </Border>
            </StackPanel>

            <!--  主要内容区域  -->
            <Border Grid.Row="1" Margin="0,0,0,20">
                <StackPanel>

                    <!--  基础功能展示  -->
                    <ui:CardExpander
                        Header="🎯 基础功能"
                        IsExpanded="True"
                        Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock
                                FontSize="14"
                                Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                Margin="0,0,0,16"
                                Text="展示 AutoSuggestBox 的基础搜索和建议功能" />

                            <!--  AutoSuggestBox 搜索功能演示  -->
                            <ui:Card Margin="0,0,0,16" Padding="20">
                                <StackPanel>
                                    <TextBlock
                                        FontWeight="Medium"
                                        Margin="0,0,0,16"
                                        Text="AutoSuggestBox 搜索功能演示：" />

                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*" />
                                            <ColumnDefinition Width="16" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>

                                        <!--  左列：搜索框  -->
                                        <StackPanel Grid.Column="0">
                                            <TextBlock
                                                FontWeight="Medium"
                                                Margin="0,0,0,8"
                                                Text="搜索输入区域" />

                                            <!--  城市搜索  -->
                                            <StackPanel Margin="0,0,0,12">
                                                <TextBlock
                                                    FontSize="12"
                                                    Margin="0,0,0,4"
                                                    Text="🏙️ 城市搜索" />

                                                <!--  测试用普通TextBox  -->
                                                <ui:TextBox
                                                    Margin="0,0,0,4"
                                                    PlaceholderText="测试绑定 - 输入城市名称..."
                                                    Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}" />

                                                <!--  AutoSuggestBox  -->
                                                <ui:AutoSuggestBox
                                                    Icon="{ui:SymbolIcon Search16}"
                                                    ItemsSource="{Binding CitySuggestions}"
                                                    PlaceholderText="输入城市名称..."
                                                    Text="{Binding SearchText, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                    x:Name="CityAutoSuggestBox">
                                                    <b:Interaction.Triggers>
                                                        <b:EventTrigger EventName="TextChanged">
                                                            <b:InvokeCommandAction Command="{Binding CitySearchCommand}" CommandParameter="{Binding Text, ElementName=CityAutoSuggestBox}" />
                                                        </b:EventTrigger>
                                                    </b:Interaction.Triggers>
                                                </ui:AutoSuggestBox>
                                            </StackPanel>

                                            <!--  编程语言搜索  -->
                                            <StackPanel Margin="0,0,0,12">
                                                <TextBlock
                                                    FontSize="12"
                                                    Margin="0,0,0,4"
                                                    Text="💻 编程语言搜索" />
                                                <ui:AutoSuggestBox
                                                    Icon="{ui:SymbolIcon Code16}"
                                                    ItemsSource="{Binding LanguageSuggestions}"
                                                    PlaceholderText="输入编程语言..."
                                                    Text="{Binding LanguageSearchText, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                    x:Name="LanguageAutoSuggestBox">
                                                    <b:Interaction.Triggers>
                                                        <b:EventTrigger EventName="TextChanged">
                                                            <b:InvokeCommandAction Command="{Binding LanguageSearchCommand}" CommandParameter="{Binding Text, ElementName=LanguageAutoSuggestBox}" />
                                                        </b:EventTrigger>
                                                    </b:Interaction.Triggers>
                                                </ui:AutoSuggestBox>
                                            </StackPanel>

                                            <!--  国家搜索  -->
                                            <StackPanel Margin="0,0,0,12">
                                                <TextBlock
                                                    FontSize="12"
                                                    Margin="0,0,0,4"
                                                    Text="🌍 国家搜索" />
                                                <ui:AutoSuggestBox
                                                    Icon="{ui:SymbolIcon Globe16}"
                                                    ItemsSource="{Binding CountrySuggestions}"
                                                    PlaceholderText="输入国家名称..."
                                                    Text="{Binding CountrySearchText, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                    x:Name="CountryAutoSuggestBox">
                                                    <b:Interaction.Triggers>
                                                        <b:EventTrigger EventName="TextChanged">
                                                            <b:InvokeCommandAction Command="{Binding CountrySearchCommand}" CommandParameter="{Binding Text, ElementName=CountryAutoSuggestBox}" />
                                                        </b:EventTrigger>
                                                    </b:Interaction.Triggers>
                                                </ui:AutoSuggestBox>
                                            </StackPanel>

                                            <!--  产品搜索  -->
                                            <StackPanel Margin="0,0,0,12">
                                                <TextBlock
                                                    FontSize="12"
                                                    Margin="0,0,0,4"
                                                    Text="🛍️ 产品搜索" />
                                                <ui:AutoSuggestBox
                                                    Icon="{ui:SymbolIcon ShoppingBag24}"
                                                    ItemsSource="{Binding ProductSuggestions}"
                                                    PlaceholderText="输入产品名称..."
                                                    Text="{Binding ProductSearchText, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                    x:Name="ProductAutoSuggestBox">
                                                    <b:Interaction.Triggers>
                                                        <b:EventTrigger EventName="TextChanged">
                                                            <b:InvokeCommandAction Command="{Binding ProductSearchCommand}" CommandParameter="{Binding Text, ElementName=ProductAutoSuggestBox}" />
                                                        </b:EventTrigger>
                                                    </b:Interaction.Triggers>
                                                </ui:AutoSuggestBox>
                                            </StackPanel>

                                            <!--  用户搜索  -->
                                            <StackPanel Margin="0,0,0,12">
                                                <TextBlock
                                                    FontSize="12"
                                                    Margin="0,0,0,4"
                                                    Text="👤 用户搜索" />
                                                <ui:AutoSuggestBox
                                                    Icon="{ui:SymbolIcon Person24}"
                                                    ItemsSource="{Binding UserSuggestions}"
                                                    PlaceholderText="输入用户名称..."
                                                    Text="{Binding UserSearchText, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                    x:Name="UserAutoSuggestBox">
                                                    <b:Interaction.Triggers>
                                                        <b:EventTrigger EventName="TextChanged">
                                                            <b:InvokeCommandAction Command="{Binding UserSearchCommand}" CommandParameter="{Binding Text, ElementName=UserAutoSuggestBox}" />
                                                        </b:EventTrigger>
                                                    </b:Interaction.Triggers>
                                                </ui:AutoSuggestBox>
                                            </StackPanel>

                                            <!--  操作按钮  -->
                                            <StackPanel Margin="0,8,0,0" Orientation="Horizontal">
                                                <ui:Button
                                                    Appearance="Secondary"
                                                    Command="{Binding ClearStatusCommand}"
                                                    Content="清除搜索"
                                                    Margin="0,0,8,0" />
                                                <ui:Button
                                                    Appearance="Secondary"
                                                    Command="{Binding ResetCountCommand}"
                                                    Content="重置计数" />
                                            </StackPanel>
                                        </StackPanel>

                                        <!--  右列：状态信息  -->
                                        <StackPanel Grid.Column="2">
                                            <TextBlock
                                                FontWeight="Medium"
                                                Margin="0,0,0,8"
                                                Text="搜索状态信息" />

                                            <!--  当前状态  -->
                                            <Border
                                                Background="{DynamicResource ControlFillColorTertiaryBrush}"
                                                CornerRadius="4"
                                                Margin="0,0,0,8"
                                                Padding="12">
                                                <StackPanel>
                                                    <TextBlock
                                                        FontSize="12"
                                                        FontWeight="Medium"
                                                        Margin="0,0,0,4"
                                                        Text="📊 当前状态" />
                                                    <TextBlock
                                                        FontSize="11"
                                                        Text="{Binding StatusMessage}"
                                                        TextWrapping="Wrap" />
                                                </StackPanel>
                                            </Border>

                                            <!--  交互统计  -->
                                            <Border
                                                Background="{DynamicResource ControlFillColorTertiaryBrush}"
                                                CornerRadius="4"
                                                Margin="0,0,0,8"
                                                Padding="12">
                                                <StackPanel>
                                                    <TextBlock
                                                        FontSize="12"
                                                        FontWeight="Medium"
                                                        Margin="0,0,0,4"
                                                        Text="🔢 交互统计" />
                                                    <TextBlock FontSize="11" Text="{Binding InteractionCount, StringFormat='交互次数: {0}'}" />
                                                </StackPanel>
                                            </Border>

                                            <!--  搜索结果  -->
                                            <Border
                                                Background="{DynamicResource ControlFillColorTertiaryBrush}"
                                                CornerRadius="4"
                                                Margin="0,0,0,8"
                                                Padding="12">
                                                <StackPanel>
                                                    <TextBlock
                                                        FontSize="12"
                                                        FontWeight="Medium"
                                                        Margin="0,0,0,4"
                                                        Text="🔍 搜索内容" />
                                                    <TextBlock
                                                        FontSize="11"
                                                        Margin="0,0,0,2"
                                                        Text="{Binding SearchText, StringFormat='城市: {0}'}" />
                                                    <TextBlock
                                                        FontSize="11"
                                                        Margin="0,0,0,2"
                                                        Text="{Binding LanguageSearchText, StringFormat='语言: {0}'}" />
                                                    <TextBlock
                                                        FontSize="11"
                                                        Margin="0,0,0,2"
                                                        Text="{Binding CountrySearchText, StringFormat='国家: {0}'}" />
                                                    <TextBlock
                                                        FontSize="11"
                                                        Margin="0,0,0,2"
                                                        Text="{Binding ProductSearchText, StringFormat='产品: {0}'}" />
                                                    <TextBlock FontSize="11" Text="{Binding UserSearchText, StringFormat='用户: {0}'}" />
                                                </StackPanel>
                                            </Border>

                                            <!--  数据源展示  -->
                                            <Border
                                                Background="{DynamicResource ControlFillColorTertiaryBrush}"
                                                CornerRadius="4"
                                                Padding="12">
                                                <StackPanel>
                                                    <TextBlock
                                                        FontSize="12"
                                                        FontWeight="Medium"
                                                        Margin="0,0,0,8"
                                                        Text="📋 可搜索数据源" />

                                                    <Expander Header="🏙️ 城市数据" IsExpanded="False" Margin="0,0,0,4">
                                                        <ScrollViewer MaxHeight="100" VerticalScrollBarVisibility="Auto">
                                                            <TextBlock FontSize="10" TextWrapping="Wrap"
                                                                       Text="北京, 上海, 广州, 深圳, 杭州, 南京, 苏州, 成都, 重庆, 武汉, 西安, 天津, 青岛, 大连, 厦门, 宁波, 无锡, 佛山, 东莞, 长沙"/>
                                                        </ScrollViewer>
                                                    </Expander>

                                                    <Expander Header="💻 编程语言" IsExpanded="False" Margin="0,0,0,4">
                                                        <ScrollViewer MaxHeight="100" VerticalScrollBarVisibility="Auto">
                                                            <TextBlock FontSize="10" TextWrapping="Wrap"
                                                                       Text="C#, Java, Python, JavaScript, TypeScript, C++, C, Go, Rust, Swift, Kotlin, PHP, Ruby, Scala, F#, VB.NET, Dart, R, MATLAB, SQL"/>
                                                        </ScrollViewer>
                                                    </Expander>

                                                    <Expander Header="🌍 国家数据" IsExpanded="False" Margin="0,0,0,4">
                                                        <ScrollViewer MaxHeight="100" VerticalScrollBarVisibility="Auto">
                                                            <TextBlock FontSize="10" TextWrapping="Wrap"
                                                                       Text="中国, 美国, 日本, 德国, 英国, 法国, 意大利, 加拿大, 澳大利亚, 韩国, 俄罗斯, 印度, 巴西, 墨西哥, 西班牙, 荷兰, 瑞士, 瑞典, 挪威, 丹麦"/>
                                                        </ScrollViewer>
                                                    </Expander>

                                                    <Expander Header="🛍️ 产品数据" IsExpanded="False" Margin="0,0,0,4">
                                                        <ScrollViewer MaxHeight="100" VerticalScrollBarVisibility="Auto">
                                                            <TextBlock FontSize="10" TextWrapping="Wrap"
                                                                       Text="笔记本电脑, 台式电脑, 平板电脑, 智能手机, 智能手表, 耳机, 音响, 键盘, 鼠标, 显示器, 打印机, 扫描仪, 路由器, 交换机, 硬盘, 内存条, 显卡, 主板, 电源, 机箱"/>
                                                        </ScrollViewer>
                                                    </Expander>

                                                    <Expander Header="👤 用户数据" IsExpanded="False">
                                                        <ScrollViewer MaxHeight="100" VerticalScrollBarVisibility="Auto">
                                                            <TextBlock FontSize="10" TextWrapping="Wrap"
                                                                       Text="张三, 李四, 王五, 赵六, 钱七, 孙八, 周九, 吴十, 郑十一, 王十二, 冯十三, 陈十四, 褚十五, 卫十六, 蒋十七, 沈十八, 韩十九, 杨二十, 朱二十一, 秦二十二"/>
                                                        </ScrollViewer>
                                                    </Expander>
                                                </StackPanel>
                                            </Border>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </ui:Card>

                            <!--  搜索结果数据展示  -->
                            <ui:Card Margin="0,0,0,16" Padding="20">
                                <StackPanel>
                                    <TextBlock
                                        FontWeight="Medium"
                                        Margin="0,0,0,16"
                                        Text="搜索结果数据展示：" />

                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*" />
                                            <ColumnDefinition Width="16" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>

                                        <!--  左列：搜索结果列表  -->
                                        <StackPanel Grid.Column="0">
                                            <TextBlock
                                                FontWeight="Medium"
                                                Margin="0,0,0,8"
                                                Text="🏙️ 城市搜索结果" />

                                            <Border
                                                Background="{DynamicResource ControlFillColorTertiaryBrush}"
                                                CornerRadius="4"
                                                MinHeight="120"
                                                Padding="8">
                                                <ScrollViewer MaxHeight="120" VerticalScrollBarVisibility="Auto">
                                                    <ItemsControl ItemsSource="{Binding CitySuggestions}">
                                                        <ItemsControl.ItemTemplate>
                                                            <DataTemplate>
                                                                <Border
                                                                    Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                                                    BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                                                    BorderThickness="1"
                                                                    CornerRadius="4"
                                                                    Margin="0,0,0,2"
                                                                    Padding="8,4">
                                                                    <StackPanel Orientation="Horizontal">
                                                                        <TextBlock Margin="0,0,8,0" Text="🏙️" />
                                                                        <TextBlock FontWeight="Medium" Text="{Binding}" />
                                                                    </StackPanel>
                                                                </Border>
                                                            </DataTemplate>
                                                        </ItemsControl.ItemTemplate>
                                                    </ItemsControl>
                                                </ScrollViewer>
                                            </Border>

                                            <TextBlock
                                                FontWeight="Medium"
                                                Margin="0,12,0,8"
                                                Text="💻 编程语言搜索结果" />

                                            <Border
                                                Background="{DynamicResource ControlFillColorTertiaryBrush}"
                                                CornerRadius="4"
                                                MinHeight="120"
                                                Padding="8">
                                                <ScrollViewer MaxHeight="120" VerticalScrollBarVisibility="Auto">
                                                    <ItemsControl ItemsSource="{Binding LanguageSuggestions}">
                                                        <ItemsControl.ItemTemplate>
                                                            <DataTemplate>
                                                                <Border
                                                                    Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                                                    BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                                                    BorderThickness="1"
                                                                    CornerRadius="4"
                                                                    Margin="0,0,0,2"
                                                                    Padding="8,4">
                                                                    <StackPanel Orientation="Horizontal">
                                                                        <TextBlock Margin="0,0,8,0" Text="💻" />
                                                                        <TextBlock FontWeight="Medium" Text="{Binding}" />
                                                                    </StackPanel>
                                                                </Border>
                                                            </DataTemplate>
                                                        </ItemsControl.ItemTemplate>
                                                    </ItemsControl>
                                                </ScrollViewer>
                                            </Border>
                                        </StackPanel>

                                        <!--  右列：国家搜索结果和统计  -->
                                        <StackPanel Grid.Column="2">
                                            <TextBlock
                                                FontWeight="Medium"
                                                Margin="0,0,0,8"
                                                Text="🌍 国家搜索结果" />

                                            <Border
                                                Background="{DynamicResource ControlFillColorTertiaryBrush}"
                                                CornerRadius="4"
                                                MinHeight="120"
                                                Padding="8">
                                                <ScrollViewer MaxHeight="120" VerticalScrollBarVisibility="Auto">
                                                    <ItemsControl ItemsSource="{Binding CountrySuggestions}">
                                                        <ItemsControl.ItemTemplate>
                                                            <DataTemplate>
                                                                <Border
                                                                    Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                                                    BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                                                    BorderThickness="1"
                                                                    CornerRadius="4"
                                                                    Margin="0,0,0,2"
                                                                    Padding="8,4">
                                                                    <StackPanel Orientation="Horizontal">
                                                                        <TextBlock Margin="0,0,8,0" Text="🌍" />
                                                                        <TextBlock FontWeight="Medium" Text="{Binding}" />
                                                                    </StackPanel>
                                                                </Border>
                                                            </DataTemplate>
                                                        </ItemsControl.ItemTemplate>
                                                    </ItemsControl>
                                                </ScrollViewer>
                                            </Border>

                                            <!--  搜索统计信息  -->
                                            <TextBlock
                                                FontWeight="Medium"
                                                Margin="0,12,0,8"
                                                Text="📊 搜索统计" />

                                            <Border
                                                Background="{DynamicResource ControlFillColorTertiaryBrush}"
                                                CornerRadius="4"
                                                Padding="12">
                                                <StackPanel>
                                                    <StackPanel Margin="0,0,0,4" Orientation="Horizontal">
                                                        <TextBlock
                                                            FontSize="12"
                                                            Margin="0,0,8,0"
                                                            Text="🏙️ 城市结果数:" />
                                                        <TextBlock
                                                            FontSize="12"
                                                            FontWeight="Bold"
                                                            Text="{Binding CitySuggestions.Count}" />
                                                    </StackPanel>

                                                    <StackPanel Margin="0,0,0,4" Orientation="Horizontal">
                                                        <TextBlock
                                                            FontSize="12"
                                                            Margin="0,0,8,0"
                                                            Text="💻 语言结果数:" />
                                                        <TextBlock
                                                            FontSize="12"
                                                            FontWeight="Bold"
                                                            Text="{Binding LanguageSuggestions.Count}" />
                                                    </StackPanel>

                                                    <StackPanel Margin="0,0,0,4" Orientation="Horizontal">
                                                        <TextBlock
                                                            FontSize="12"
                                                            Margin="0,0,8,0"
                                                            Text="🌍 国家结果数:" />
                                                        <TextBlock
                                                            FontSize="12"
                                                            FontWeight="Bold"
                                                            Text="{Binding CountrySuggestions.Count}" />
                                                    </StackPanel>

                                                    <StackPanel Margin="0,0,0,4" Orientation="Horizontal">
                                                        <TextBlock
                                                            FontSize="12"
                                                            Margin="0,0,8,0"
                                                            Text="🛍️ 产品结果数:" />
                                                        <TextBlock
                                                            FontSize="12"
                                                            FontWeight="Bold"
                                                            Text="{Binding ProductSuggestions.Count}" />
                                                    </StackPanel>

                                                    <StackPanel Margin="0,0,0,4" Orientation="Horizontal">
                                                        <TextBlock
                                                            FontSize="12"
                                                            Margin="0,0,8,0"
                                                            Text="👤 用户结果数:" />
                                                        <TextBlock
                                                            FontSize="12"
                                                            FontWeight="Bold"
                                                            Text="{Binding UserSuggestions.Count}" />
                                                    </StackPanel>

                                                    <Separator Margin="0,4" />

                                                    <StackPanel Orientation="Horizontal">
                                                        <TextBlock
                                                            FontSize="12"
                                                            Margin="0,0,8,0"
                                                            Text="📈 总结果数:" />
                                                        <TextBlock
                                                            FontSize="12"
                                                            FontWeight="Bold"
                                                            Text="{Binding TotalResultsCount}" />
                                                    </StackPanel>
                                                </StackPanel>
                                            </Border>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </ui:Card>

                            <!--  代码示例  -->
                            <ui:Card Padding="20">
                                <StackPanel>
                                    <TextBlock
                                        FontSize="16"
                                        FontWeight="Bold"
                                        Margin="0,0,0,16"
                                        Text="代码示例" />

                                    <TabControl>
                                        <TabItem Header="基础功能">
                                            <codeExample:CodeExampleControl CSharpCode="{Binding BasicAutoSuggestBoxCs}" XamlCode="{Binding BasicAutoSuggestBoxXaml}" />
                                        </TabItem>
                                        <TabItem Header="高级功能">
                                            <codeExample:CodeExampleControl CSharpCode="{Binding AdvancedAutoSuggestBoxCs}" XamlCode="{Binding AdvancedAutoSuggestBoxXaml}" />
                                        </TabItem>
                                    </TabControl>
                                </StackPanel>
                            </ui:Card>
                        </StackPanel>
                    </ui:CardExpander>

                </StackPanel>
            </Border>

            <!--  底部操作栏  -->
            <Border
                Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                BorderThickness="1"
                CornerRadius="8"
                Grid.Row="2"
                Padding="16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>

                    <!--  统计信息  -->
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock
                            FontSize="14"
                            Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                            Margin="0,0,20,0"
                            Text="{Binding InteractionCount, StringFormat='交互次数: {0}'}" />
                        <TextBlock
                            FontSize="14"
                            Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                            Text="{Binding LastAction, StringFormat='最后操作: {0}'}" />
                    </StackPanel>

                    <!--  操作按钮  -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <ui:Button
                            Appearance="Secondary"
                            Command="{Binding ResetCountCommand}"
                            Content="重置计数"
                            Margin="0,0,8,0" />
                        <ui:Button
                            Appearance="Secondary"
                            Command="{Binding ClearStatusCommand}"
                            Content="清除状态" />
                    </StackPanel>
                </Grid>
            </Border>

        </Grid>
    </ScrollViewer>
</UserControl>
