<UserControl x:Class="WPFTest.Views.Navigation.BreadcrumbBarView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:codeExample="clr-namespace:Zylo.WPF.Controls.CodeExample;assembly=Zylo.WPF">

    <UserControl.Resources>
        <!-- 示例卡片样式 -->
        <Style x:Key="ExampleCardStyle" TargetType="ui:Card">
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Padding" Value="20"/>
        </Style>
    </UserControl.Resources>

    <Border>
        <!-- 触发器 - 使用最新的分类动画系统 -->
        <Border.Triggers>
            <EventTrigger RoutedEvent="Loaded">
                <BeginStoryboard Storyboard="{StaticResource ZyloSubMenuPageLoadAnimation}"/>
                <BeginStoryboard Storyboard="{StaticResource ZyloDotsAnimation}"/>
            </EventTrigger>
        </Border.Triggers>

        <Grid>
            <!-- 背景装饰圆点 -->
            <Canvas>
                <Ellipse x:Name="Dot1" Width="8" Height="8" Fill="{DynamicResource SystemAccentColorSecondaryBrush}" Canvas.Left="100" Canvas.Top="50" Opacity="0.3"/>
                <Ellipse x:Name="Dot2" Width="12" Height="12" Fill="{DynamicResource SystemAccentColorPrimaryBrush}" Canvas.Left="300" Canvas.Top="150" Opacity="0.2"/>
                <Ellipse x:Name="Dot3" Width="6" Height="6" Fill="{DynamicResource SystemAccentColorSecondaryBrush}" Canvas.Left="500" Canvas.Top="80" Opacity="0.4"/>
                <Ellipse x:Name="Dot4" Width="10" Height="10" Fill="{DynamicResource SystemAccentColorPrimaryBrush}" Canvas.Left="700" Canvas.Top="200" Opacity="0.25"/>
            </Canvas>

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 顶部横幅 -->
                <Border x:Name="TopBanner" Grid.Row="0" Margin="20,20,20,10" Opacity="0">
                    <Border.RenderTransform>
                        <TranslateTransform Y="-50"/>
                    </Border.RenderTransform>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- 大图标 -->
                        <Border Grid.Column="0"
                                Width="80"
                                Height="80"
                                BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                CornerRadius="16"
                                BorderThickness="2"
                                Margin="5"
                                VerticalAlignment="Center">
                            <ui:SymbolIcon Symbol="ChevronRight24"
                                          FontSize="40"
                                          Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                          HorizontalAlignment="Center"
                                          VerticalAlignment="Center"/>
                        </Border>

                        <!-- 标题和描述 -->
                        <StackPanel Grid.Column="1" VerticalAlignment="Center" Margin="20,0,0,0">
                            <TextBlock Text="🍞 BreadcrumbBar 示例"
                                       FontSize="28"
                                       FontWeight="Bold"
                                       Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                            <TextBlock Text="展示 WPF-UI BreadcrumbBar 控件的硬编码和数据绑定两种使用方式"
                                       FontSize="14"
                                       Margin="0,8,0,0"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Opacity="0.9"
                                       TextWrapping="Wrap"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- 面包屑导航 -->
                <ui:BreadcrumbBar x:Name="BreadcrumbSection"
                                  Grid.Row="1"
                                  Margin="20,10,20,10"
                                  FontSize="14"
                                  Opacity="0">
                    <ui:BreadcrumbBar.RenderTransform>
                        <TranslateTransform Y="30"/>
                    </ui:BreadcrumbBar.RenderTransform>
                    <ui:BreadcrumbBarItem Content="首页" Icon="{ui:SymbolIcon Home24}"/>
                    <ui:BreadcrumbBarItem Content="导航控件" Icon="{ui:SymbolIcon Navigation24}"/>
                    <ui:BreadcrumbBarItem Content="BreadcrumbBar" Icon="{ui:SymbolIcon ChevronRight24}"/>
                </ui:BreadcrumbBar>

                <!-- 主要内容区域 -->
                <ScrollViewer x:Name="ContentSection" Grid.Row="2" Opacity="0">
                    <ScrollViewer.RenderTransform>
                        <TranslateTransform Y="40"/>
                    </ScrollViewer.RenderTransform>
                    
                    <StackPanel Margin="20">
                        <!-- 硬编码 BreadcrumbBar 示例 -->
                        <ui:Card Style="{StaticResource ExampleCardStyle}">
                            <StackPanel>
                                <TextBlock Text="📱 硬编码 BreadcrumbBar" 
                                          FontSize="16" 
                                          FontWeight="SemiBold"
                                          Margin="0,0,0,15"/>

                                <!-- 静态面包屑 - 可点击 -->
                                <TextBlock Text="基础面包屑 (可点击):" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                                    <Button Content="🏠 首页"
                                            Command="{Binding NavigateToStaticCommand}"
                                            CommandParameter="home"
                                            Style="{DynamicResource DefaultButtonStyle}"
                                            Background="Transparent"
                                            BorderThickness="0"
                                            Padding="8,4"
                                            Margin="0,0,5,0"/>
                                    <TextBlock Text=">" VerticalAlignment="Center" Margin="0,0,5,0" Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>

                                    <Button Content="📄 文档"
                                            Command="{Binding NavigateToStaticCommand}"
                                            CommandParameter="docs"
                                            Style="{DynamicResource DefaultButtonStyle}"
                                            Background="Transparent"
                                            BorderThickness="0"
                                            Padding="8,4"
                                            Margin="0,0,5,0"/>
                                    <TextBlock Text=">" VerticalAlignment="Center" Margin="0,0,5,0" Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>

                                    <Button Content="📋 API 参考"
                                            Command="{Binding NavigateToStaticCommand}"
                                            CommandParameter="api"
                                            Style="{DynamicResource DefaultButtonStyle}"
                                            Background="Transparent"
                                            BorderThickness="0"
                                            Padding="8,4"
                                            Margin="0,0,5,0"/>
                                    <TextBlock Text=">" VerticalAlignment="Center" Margin="0,0,5,0" Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>

                                    <Button Content="🧩 控件"
                                            Command="{Binding NavigateToStaticCommand}"
                                            CommandParameter="controls"
                                            Style="{DynamicResource DefaultButtonStyle}"
                                            Background="Transparent"
                                            BorderThickness="0"
                                            Padding="8,4"/>
                                </StackPanel>

                                <!-- 系统路径面包屑 - 可点击 -->
                                <TextBlock Text="系统路径面包屑 (可点击):" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                                    <Button Content="⚙️ 系统"
                                            Command="{Binding NavigateToStaticCommand}"
                                            CommandParameter="system"
                                            Style="{DynamicResource DefaultButtonStyle}"
                                            Background="Transparent"
                                            BorderThickness="0"
                                            Padding="8,4"
                                            Margin="0,0,5,0"/>
                                    <TextBlock Text=">" VerticalAlignment="Center" Margin="0,0,5,0" Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>

                                    <Button Content="👥 用户管理"
                                            Command="{Binding NavigateToStaticCommand}"
                                            CommandParameter="users"
                                            Style="{DynamicResource DefaultButtonStyle}"
                                            Background="Transparent"
                                            BorderThickness="0"
                                            Padding="8,4"
                                            Margin="0,0,5,0"/>
                                    <TextBlock Text=">" VerticalAlignment="Center" Margin="0,0,5,0" Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>

                                    <Button Content="🛡️ 权限设置"
                                            Command="{Binding NavigateToStaticCommand}"
                                            CommandParameter="permissions"
                                            Style="{DynamicResource DefaultButtonStyle}"
                                            Background="Transparent"
                                            BorderThickness="0"
                                            Padding="8,4"
                                            Margin="0,0,5,0"/>
                                    <TextBlock Text=">" VerticalAlignment="Center" Margin="0,0,5,0" Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>

                                    <Button Content="👤 角色配置"
                                            Command="{Binding NavigateToStaticCommand}"
                                            CommandParameter="roles"
                                            Style="{DynamicResource DefaultButtonStyle}"
                                            Background="Transparent"
                                            BorderThickness="0"
                                            Padding="8,4"/>
                                </StackPanel>

                                <!-- 文件路径面包屑 - 可点击 -->
                                <TextBlock Text="文件路径面包屑 (可点击):" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Disabled">
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                                        <Button Content="📁 根目录"
                                                Command="{Binding NavigateToStaticCommand}"
                                                CommandParameter="root"
                                                Style="{DynamicResource DefaultButtonStyle}"
                                                Background="Transparent"
                                                BorderThickness="0"
                                                Padding="8,4"
                                                Margin="0,0,5,0"/>
                                        <TextBlock Text=">" VerticalAlignment="Center" Margin="0,0,5,0" Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>

                                        <Button Content="📂 项目文件夹"
                                                Command="{Binding NavigateToStaticCommand}"
                                                CommandParameter="project"
                                                Style="{DynamicResource DefaultButtonStyle}"
                                                Background="Transparent"
                                                BorderThickness="0"
                                                Padding="8,4"
                                                Margin="0,0,5,0"/>
                                        <TextBlock Text=">" VerticalAlignment="Center" Margin="0,0,5,0" Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>

                                        <Button Content="💻 源代码"
                                                Command="{Binding NavigateToStaticCommand}"
                                                CommandParameter="source"
                                                Style="{DynamicResource DefaultButtonStyle}"
                                                Background="Transparent"
                                                BorderThickness="0"
                                                Padding="8,4"
                                                Margin="0,0,5,0"/>
                                        <TextBlock Text=">" VerticalAlignment="Center" Margin="0,0,5,0" Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>

                                        <Button Content="🧩 组件"
                                                Command="{Binding NavigateToStaticCommand}"
                                                CommandParameter="components"
                                                Style="{DynamicResource DefaultButtonStyle}"
                                                Background="Transparent"
                                                BorderThickness="0"
                                                Padding="8,4"
                                                Margin="0,0,5,0"/>
                                        <TextBlock Text=">" VerticalAlignment="Center" Margin="0,0,5,0" Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>

                                        <Button Content="🧭 导航组件"
                                                Command="{Binding NavigateToStaticCommand}"
                                                CommandParameter="navigation"
                                                Style="{DynamicResource DefaultButtonStyle}"
                                                Background="Transparent"
                                                BorderThickness="0"
                                                Padding="8,4"
                                                Margin="0,0,5,0"/>
                                        <TextBlock Text=">" VerticalAlignment="Center" Margin="0,0,5,0" Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>

                                        <Button Content="🍞 面包屑组件"
                                                Command="{Binding NavigateToStaticCommand}"
                                                CommandParameter="breadcrumb"
                                                Style="{DynamicResource DefaultButtonStyle}"
                                                Background="Transparent"
                                                BorderThickness="0"
                                                Padding="8,4"/>
                                    </StackPanel>
                                </ScrollViewer>
                            </StackPanel>
                        </ui:Card>

                        <!-- 数据绑定 BreadcrumbBar 示例 -->
                        <ui:Card Style="{StaticResource ExampleCardStyle}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- 左侧：数据绑定面包屑 -->
                                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                    <TextBlock Text="🔄 数据绑定 BreadcrumbBar" 
                                              FontSize="16" 
                                              FontWeight="SemiBold"
                                              Margin="0,0,0,15"/>

                                    <!-- 当前路径显示 -->
                                    <TextBlock Text="当前路径:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                    <TextBlock Text="{Binding CurrentPath}" 
                                              FontFamily="Consolas"
                                              Background="{DynamicResource ControlFillColorDefaultBrush}"
                                              Padding="8,4"
                                              Margin="0,0,0,10"/>

                                    <!-- 动态面包屑 - 使用简单的文本显示 -->
                                    <TextBlock Text="动态面包屑:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                    <ItemsControl ItemsSource="{Binding BreadcrumbItems}" Margin="0,0,0,20">
                                        <ItemsControl.ItemsPanel>
                                            <ItemsPanelTemplate>
                                                <StackPanel Orientation="Horizontal"/>
                                            </ItemsPanelTemplate>
                                        </ItemsControl.ItemsPanel>
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal">
                                                    <Button Content="{Binding Name}"
                                                            Command="{Binding DataContext.NavigateToBreadcrumbCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                            CommandParameter="{Binding}"
                                                            Style="{DynamicResource DefaultButtonStyle}"
                                                            Background="Transparent"
                                                            BorderThickness="0"
                                                            Padding="4,2"
                                                            Margin="0,0,5,0"/>
                                                    <TextBlock Text=">" 
                                                              VerticalAlignment="Center"
                                                              Margin="0,0,5,0"
                                                              Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>

                                    <!-- 模拟导航按钮 -->
                                    <TextBlock Text="模拟导航:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                    <WrapPanel Margin="0,0,0,10">
                                        <Button Content="📁 项目" 
                                                Command="{Binding NavigateToCommand}" 
                                                CommandParameter="projects"
                                                Margin="0,0,5,5"/>
                                        <Button Content="📄 文档" 
                                                Command="{Binding NavigateToCommand}" 
                                                CommandParameter="docs"
                                                Margin="0,0,5,5"/>
                                        <Button Content="⚙️ 设置" 
                                                Command="{Binding NavigateToCommand}" 
                                                CommandParameter="settings"
                                                Margin="0,0,5,5"/>
                                        <Button Content="👥 团队" 
                                                Command="{Binding NavigateToCommand}" 
                                                CommandParameter="team"
                                                Margin="0,0,5,5"/>
                                    </WrapPanel>

                                    <!-- 操作按钮 -->
                                    <WrapPanel Margin="0,10,0,0">
                                        <Button Content="🏠 返回首页"
                                                Command="{Binding ResetBreadcrumbCommand}"
                                                Margin="0,0,5,5"/>
                                        <Button Content="🗑️ 清空"
                                                Command="{Binding ClearBreadcrumbCommand}"
                                                Margin="0,0,5,5"/>
                                        <Button Content="📋 复制路径"
                                                Command="{Binding CopyCurrentPathCommand}"
                                                Margin="0,0,5,5"/>
                                    </WrapPanel>

                                    <!-- 添加自定义项 -->
                                    <TextBlock Text="添加自定义项:" FontWeight="SemiBold" Margin="0,10,0,5"/>
                                    <Grid Margin="0,0,0,10">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBox x:Name="CustomItemTextBox"
                                                 Grid.Column="0"
                                                 Text="自定义项目"
                                                 Margin="0,0,5,0"/>
                                        <Button Grid.Column="1"
                                                Content="➕"
                                                Command="{Binding AddCustomBreadcrumbCommand}"
                                                CommandParameter="{Binding ElementName=CustomItemTextBox, Path=Text}"/>
                                    </Grid>
                                </StackPanel>

                                <!-- 右侧：状态信息和自定义样式 -->
                                <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                    <TextBlock Text="📊 状态信息"
                                              FontSize="16"
                                              FontWeight="SemiBold"
                                              Margin="0,0,0,15"/>

                                    <!-- 统计信息 -->
                                    <ui:Card Padding="10" Margin="0,0,0,10">
                                        <StackPanel>
                                            <TextBlock Text="面包屑统计:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                            <TextBlock Text="{Binding BreadcrumbStats}"
                                                       FontFamily="Consolas"
                                                       FontSize="12"
                                                       Background="{DynamicResource ControlFillColorDefaultBrush}"
                                                       Padding="5"/>
                                        </StackPanel>
                                    </ui:Card>

                                    <!-- 当前选中项 -->
                                    <ui:Card Padding="10" Margin="0,0,0,10">
                                        <StackPanel>
                                            <TextBlock Text="当前选中项:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                            <TextBlock Text="{Binding SelectedBreadcrumbItem.Name, FallbackValue='无'}"
                                                       FontWeight="SemiBold"/>
                                            <TextBlock Text="{Binding SelectedBreadcrumbItem.Description, FallbackValue='无描述'}"
                                                       FontSize="12"
                                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                                        </StackPanel>
                                    </ui:Card>

                                    <!-- 导航历史 -->
                                    <ui:Card Padding="10" Margin="0,0,0,15">
                                        <StackPanel>
                                            <TextBlock Text="导航历史:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                            <ScrollViewer MaxHeight="100" VerticalScrollBarVisibility="Auto">
                                                <TextBlock Text="{Binding NavigationHistory}"
                                                           FontFamily="Consolas"
                                                           FontSize="11"
                                                           TextWrapping="Wrap"/>
                                            </ScrollViewer>
                                        </StackPanel>
                                    </ui:Card>

                                    <TextBlock Text="🎨 自定义样式"
                                              FontSize="16"
                                              FontWeight="SemiBold"
                                              Margin="0,0,0,15"/>

                                    <!-- 紧凑样式 - 可点击 -->
                                    <TextBlock Text="紧凑样式 (可点击):" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                        <Button Content="Home"
                                                Command="{Binding NavigateToStaticCommand}"
                                                CommandParameter="home"
                                                Style="{DynamicResource DefaultButtonStyle}"
                                                Background="Transparent"
                                                BorderThickness="0"
                                                FontSize="12"
                                                Padding="6,2"
                                                Margin="0,0,3,0"/>
                                        <TextBlock Text=">" VerticalAlignment="Center" Margin="0,0,3,0" FontSize="10" Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>

                                        <Button Content="Projects"
                                                Command="{Binding NavigateToStaticCommand}"
                                                CommandParameter="projects"
                                                Style="{DynamicResource DefaultButtonStyle}"
                                                Background="Transparent"
                                                BorderThickness="0"
                                                FontSize="12"
                                                Padding="6,2"
                                                Margin="0,0,3,0"/>
                                        <TextBlock Text=">" VerticalAlignment="Center" Margin="0,0,3,0" FontSize="10" Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>

                                        <Button Content="Current"
                                                Command="{Binding NavigateToStaticCommand}"
                                                CommandParameter="current"
                                                Style="{DynamicResource DefaultButtonStyle}"
                                                Background="Transparent"
                                                BorderThickness="0"
                                                FontSize="12"
                                                Padding="6,2"/>
                                    </StackPanel>

                                    <!-- 大字体样式 - 可点击 -->
                                    <TextBlock Text="大字体样式 (可点击):" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                        <Button Content="⚙️ 系统"
                                                Command="{Binding NavigateToStaticCommand}"
                                                CommandParameter="system"
                                                Style="{DynamicResource DefaultButtonStyle}"
                                                Background="Transparent"
                                                BorderThickness="0"
                                                FontSize="16"
                                                FontWeight="SemiBold"
                                                Padding="8,4"
                                                Margin="0,0,5,0"/>
                                        <TextBlock Text=">" VerticalAlignment="Center" Margin="0,0,5,0" FontSize="14" Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>

                                        <Button Content="🔧 配置"
                                                Command="{Binding NavigateToStaticCommand}"
                                                CommandParameter="config"
                                                Style="{DynamicResource DefaultButtonStyle}"
                                                Background="Transparent"
                                                BorderThickness="0"
                                                FontSize="16"
                                                FontWeight="SemiBold"
                                                Padding="8,4"/>
                                    </StackPanel>

                                    <!-- 自定义颜色 - 可点击 -->
                                    <TextBlock Text="自定义颜色 (可点击):" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                        <Button Content="⚠️ 重要"
                                                Command="{Binding NavigateToStaticCommand}"
                                                CommandParameter="important"
                                                Style="{DynamicResource DefaultButtonStyle}"
                                                Background="Transparent"
                                                BorderThickness="0"
                                                Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                                FontWeight="SemiBold"
                                                Padding="8,4"
                                                Margin="0,0,5,0"/>
                                        <TextBlock Text=">" VerticalAlignment="Center" Margin="0,0,5,0" Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"/>

                                        <Button Content="🛤️ 路径"
                                                Command="{Binding NavigateToStaticCommand}"
                                                CommandParameter="path"
                                                Style="{DynamicResource DefaultButtonStyle}"
                                                Background="Transparent"
                                                BorderThickness="0"
                                                Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                                FontWeight="SemiBold"
                                                Padding="8,4"/>
                                    </StackPanel>
                                </StackPanel>
                            </Grid>
                        </ui:Card>

                        <!-- 基础面包屑示例 -->
                        <codeExample:CodeExampleControl
                            Title="基础 BreadcrumbBar 示例"
                            Language="XAML"
                            Description="展示基础面包屑导航的使用方法"
                            ShowTabs="True"
                            XamlCode="{Binding BasicBreadcrumbXaml}"
                            CSharpCode="{Binding BasicBreadcrumbCs}"
                            IsExpanded="True"
                            Margin="0,15,0,0"/>

                        <!-- 高级面包屑示例 -->
                        <codeExample:CodeExampleControl
                            Title="高级 BreadcrumbBar 示例"
                            Language="XAML"
                            Description="展示高级面包屑功能，包括自定义模板、交互行为和导航历史"
                            ShowTabs="True"
                            XamlCode="{Binding AdvancedBreadcrumbXaml}"
                            CSharpCode="{Binding AdvancedBreadcrumbCs}"
                            IsExpanded="False"
                            Margin="0,10,0,0"/>

                    </StackPanel>
                </ScrollViewer>
            </Grid>
        </Grid>
    </Border>
</UserControl>
