<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 🔔 通知和提示动画 -->
    
    <!-- ========================================= -->
    <!-- 📢 通知进入动画 -->
    <!-- ========================================= -->
    
    <!-- 从右侧滑入通知 -->
    <Storyboard x:Key="ZyloNotificationSlideInRightAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                       From="300" To="0" Duration="0:0:0.4">
            <DoubleAnimation.EasingFunction>
                <BackEase EasingMode="EaseOut" Amplitude="0.3"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="0" To="1" Duration="0:0:0.3"/>
    </Storyboard>

    <!-- 从左侧滑入通知 -->
    <Storyboard x:Key="ZyloNotificationSlideInLeftAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                       From="-300" To="0" Duration="0:0:0.4">
            <DoubleAnimation.EasingFunction>
                <BackEase EasingMode="EaseOut" Amplitude="0.3"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="0" To="1" Duration="0:0:0.3"/>
    </Storyboard>

    <!-- 从顶部滑入通知 -->
    <Storyboard x:Key="ZyloNotificationSlideInTopAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                       From="-100" To="0" Duration="0:0:0.4">
            <DoubleAnimation.EasingFunction>
                <BackEase EasingMode="EaseOut" Amplitude="0.3"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="0" To="1" Duration="0:0:0.3"/>
    </Storyboard>

    <!-- 从底部滑入通知 -->
    <Storyboard x:Key="ZyloNotificationSlideInBottomAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                       From="100" To="0" Duration="0:0:0.4">
            <DoubleAnimation.EasingFunction>
                <BackEase EasingMode="EaseOut" Amplitude="0.3"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="0" To="1" Duration="0:0:0.3"/>
    </Storyboard>

    <!-- ========================================= -->
    <!-- 📤 通知退出动画 -->
    <!-- ========================================= -->
    
    <!-- 向右滑出通知 -->
    <Storyboard x:Key="ZyloNotificationSlideOutRightAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                       From="0" To="300" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseIn" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="1" To="0" Duration="0:0:0.3"/>
    </Storyboard>

    <!-- 向左滑出通知 -->
    <Storyboard x:Key="ZyloNotificationSlideOutLeftAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                       From="0" To="-300" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseIn" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="1" To="0" Duration="0:0:0.3"/>
    </Storyboard>

    <!-- 向上滑出通知 -->
    <Storyboard x:Key="ZyloNotificationSlideOutTopAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                       From="0" To="-100" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseIn" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="1" To="0" Duration="0:0:0.3"/>
    </Storyboard>

    <!-- 向下滑出通知 -->
    <Storyboard x:Key="ZyloNotificationSlideOutBottomAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                       From="0" To="100" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseIn" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="1" To="0" Duration="0:0:0.3"/>
    </Storyboard>

    <!-- ========================================= -->
    <!-- 🎯 弹出式通知动画 -->
    <!-- ========================================= -->
    
    <!-- 弹出进入动画 -->
    <Storyboard x:Key="ZyloNotificationPopInAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                       From="0.3" To="1" Duration="0:0:0.4">
            <DoubleAnimation.EasingFunction>
                <BackEase EasingMode="EaseOut" Amplitude="0.5"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                       From="0.3" To="1" Duration="0:0:0.4">
            <DoubleAnimation.EasingFunction>
                <BackEase EasingMode="EaseOut" Amplitude="0.5"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="0" To="1" Duration="0:0:0.3"/>
    </Storyboard>

    <!-- 弹出退出动画 -->
    <Storyboard x:Key="ZyloNotificationPopOutAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                       From="1" To="0.3" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseIn" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                       From="1" To="0.3" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseIn" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="1" To="0" Duration="0:0:0.2"/>
    </Storyboard>

    <!-- ========================================= -->
    <!-- ⚠️ 警告和错误动画 -->
    <!-- ========================================= -->
    
    <!-- 警告摇摆动画 -->
    <Storyboard x:Key="ZyloWarningShakeAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                       From="0" To="5" Duration="0:0:0.1"
                       AutoReverse="True" RepeatBehavior="4x">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseInOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <ColorAnimation Storyboard.TargetProperty="(Panel.Background).(SolidColorBrush.Color)"
                       To="#FFF3CD" Duration="0:0:0.2"/>
        <ColorAnimation Storyboard.TargetProperty="(Panel.Background).(SolidColorBrush.Color)"
                       Duration="0:0:0.8" BeginTime="0:0:0.2">
            <ColorAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </ColorAnimation.EasingFunction>
        </ColorAnimation>
    </Storyboard>

    <!-- 错误闪烁动画 -->
    <Storyboard x:Key="ZyloErrorBlinkAnimation">
        <ColorAnimation Storyboard.TargetProperty="(Panel.Background).(SolidColorBrush.Color)"
                       To="#F8D7DA" Duration="0:0:0.2"/>
        <ColorAnimation Storyboard.TargetProperty="(Panel.Background).(SolidColorBrush.Color)"
                       Duration="0:0:0.8" BeginTime="0:0:0.2">
            <ColorAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </ColorAnimation.EasingFunction>
        </ColorAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="1" To="0.7" Duration="0:0:0.15"
                       AutoReverse="True" RepeatBehavior="3x"/>
    </Storyboard>

    <!-- 成功确认动画 -->
    <Storyboard x:Key="ZyloSuccessConfirmAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                       From="1" To="1.1" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                       From="1" To="1.1" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                       From="1.1" To="1" Duration="0:0:0.3" BeginTime="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <ElasticEase EasingMode="EaseOut" Oscillations="2" Springiness="3"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                       From="1.1" To="1" Duration="0:0:0.3" BeginTime="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <ElasticEase EasingMode="EaseOut" Oscillations="2" Springiness="3"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <ColorAnimation Storyboard.TargetProperty="(Panel.Background).(SolidColorBrush.Color)"
                       To="#D4EDDA" Duration="0:0:0.2"/>
        <ColorAnimation Storyboard.TargetProperty="(Panel.Background).(SolidColorBrush.Color)"
                       Duration="0:0:0.8" BeginTime="0:0:0.2">
            <ColorAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </ColorAnimation.EasingFunction>
        </ColorAnimation>
    </Storyboard>

    <!-- ========================================= -->
    <!-- 💡 提示动画 -->
    <!-- ========================================= -->
    
    <!-- 提示闪烁动画 -->
    <Storyboard x:Key="ZyloHintBlinkAnimation" RepeatBehavior="3x">
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="1" To="0.5" Duration="0:0:0.4"
                       AutoReverse="True">
            <DoubleAnimation.EasingFunction>
                <SineEase EasingMode="EaseInOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 提示高亮动画 -->
    <Storyboard x:Key="ZyloHintHighlightAnimation">
        <ColorAnimation Storyboard.TargetProperty="(Panel.Background).(SolidColorBrush.Color)"
                       To="#FFF9C4" Duration="0:0:0.3"/>
        <ColorAnimation Storyboard.TargetProperty="(Panel.Background).(SolidColorBrush.Color)"
                       Duration="0:0:1" BeginTime="0:0:0.3">
            <ColorAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </ColorAnimation.EasingFunction>
        </ColorAnimation>
    </Storyboard>

</ResourceDictionary>
