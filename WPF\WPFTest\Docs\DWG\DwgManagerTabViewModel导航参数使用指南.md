# DwgManagerTabViewModel 导航参数使用指南

## 🎯 概述
DwgManagerTabViewModel 现在支持完整的导航参数处理，可以在页面导航时传递参数来设置初始状态，提供更好的用户体验。

## 📋 支持的导航参数

### 1. DwgFolderPath - DWG文件夹路径
**类型**: `string`  
**说明**: 指定DWG根文件夹的路径  
**示例**: `@"C:\Projects\DWG Files"`

### 2. SelectedProfession - 默认选中的专业
**类型**: `string`  
**说明**: 指定默认选中的专业文件夹名称  
**示例**: `"建筑"`, `"结构"`, `"暖通"`

### 3. SelectedFileType - 默认选中的文件类型
**类型**: `string`  
**说明**: 指定默认选中的文件类型（中文名或英文名）  
**示例**: `"出图"`, `"底图"`, `"Drawing"`

### 4. AutoLoad - 自动加载
**类型**: `bool`  
**说明**: 是否自动加载文件列表  
**示例**: `true`, `false`

### 5. ForceNewInstance - 强制新实例
**类型**: `bool`  
**说明**: 是否强制创建新的页面实例  
**示例**: `true`, `false`

## 🚀 使用示例

### 基本导航（无参数）
```csharp
// 简单导航到DWG管理页面
_regionManager.RequestNavigate("ContentRegion", "DwgManagerTabView");
```

### 指定DWG文件夹路径
```csharp
var parameters = new NavigationParameters();
parameters.Add("DwgFolderPath", @"D:\CAD Projects\Building A\DWG");

_regionManager.RequestNavigate("ContentRegion", "DwgManagerTabView", parameters);
```

### 设置默认选择项
```csharp
var parameters = new NavigationParameters();
parameters.Add("DwgFolderPath", @"C:\Projects\DWG");
parameters.Add("SelectedProfession", "建筑");
parameters.Add("SelectedFileType", "出图");
parameters.Add("AutoLoad", true);

_regionManager.RequestNavigate("ContentRegion", "DwgManagerTabView", parameters);
```

### 强制创建新实例
```csharp
var parameters = new NavigationParameters();
parameters.Add("DwgFolderPath", @"C:\New Project\DWG");
parameters.Add("ForceNewInstance", true);

_regionManager.RequestNavigate("ContentRegion", "DwgManagerTabView", parameters);
```

### 完整配置示例
```csharp
public void NavigateToDwgManager(string projectPath, string profession = null)
{
    var parameters = new NavigationParameters();
    
    // 设置项目DWG文件夹
    var dwgPath = Path.Combine(projectPath, "DWG");
    parameters.Add("DwgFolderPath", dwgPath);
    
    // 设置默认专业（如果指定）
    if (!string.IsNullOrEmpty(profession))
    {
        parameters.Add("SelectedProfession", profession);
        parameters.Add("SelectedFileType", "出图"); // 默认显示出图
    }
    
    // 启用自动加载
    parameters.Add("AutoLoad", true);
    
    _regionManager.RequestNavigate("ContentRegion", "DwgManagerTabView", parameters);
}
```

## 🔄 导航生命周期

### OnNavigatedTo - 导航到页面
```csharp
public void OnNavigatedTo(NavigationContext navigationContext)
{
    // 1. 处理DWG文件夹路径
    // 2. 设置默认选中的专业文件夹
    // 3. 设置默认选中的文件类型
    // 4. 处理自动加载参数
    // 5. 更新状态消息
}
```

### IsNavigationTarget - 判断是否重用实例
```csharp
public bool IsNavigationTarget(NavigationContext navigationContext)
{
    // 1. 比较DWG文件夹路径
    // 2. 检查强制新实例参数
    // 3. 返回是否可以重用当前实例
}
```

### OnNavigatedFrom - 离开页面
```csharp
public void OnNavigatedFrom(NavigationContext navigationContext)
{
    // 1. 记录当前状态（用于日志和统计）
    // 2. 保存状态到静态缓存（供下次导航参考）
    // 3. 取消异步操作
    // 4. 清理资源
    // 5. 记录统计信息

    // 注意：NavigationContext.Parameters 是只读的，不能修改
    // 状态保存通过静态变量实现
}
```

## 💡 最佳实践

### 1. 路径验证
```csharp
// 导航前验证路径
if (Directory.Exists(dwgFolderPath))
{
    parameters.Add("DwgFolderPath", dwgFolderPath);
}
else
{
    // 处理路径不存在的情况
    MessageBox.Show($"DWG文件夹不存在: {dwgFolderPath}");
    return;
}
```

### 2. 异步导航
```csharp
// 对于可能耗时的操作，使用异步导航
await Task.Run(() =>
{
    Application.Current.Dispatcher.Invoke(() =>
    {
        _regionManager.RequestNavigate("ContentRegion", "DwgManagerTabView", parameters);
    });
});
```

### 3. 错误处理
```csharp
try
{
    _regionManager.RequestNavigate("ContentRegion", "DwgManagerTabView",
        navigationResult =>
        {
            if (!navigationResult.Result.HasValue || !navigationResult.Result.Value)
            {
                // 导航失败处理
                MessageBox.Show($"导航失败: {navigationResult.Error?.Message}");
            }
        }, parameters);
}
catch (Exception ex)
{
    // 异常处理
    _logger.Error($"导航异常: {ex.Message}");
}
```

## 🎨 实际应用场景

### 场景1: 项目切换
```csharp
// 用户切换到新项目时
public void SwitchToProject(ProjectInfo project)
{
    var parameters = new NavigationParameters();
    parameters.Add("DwgFolderPath", project.DwgFolderPath);
    parameters.Add("SelectedProfession", project.DefaultProfession);
    parameters.Add("ForceNewInstance", true); // 确保清洁状态
    
    _regionManager.RequestNavigate("ContentRegion", "DwgManagerTabView", parameters);
}
```

### 场景2: 快速访问
```csharp
// 提供快速访问按钮
public void QuickAccessArchitecture()
{
    var parameters = new NavigationParameters();
    parameters.Add("SelectedProfession", "建筑");
    parameters.Add("SelectedFileType", "出图");
    parameters.Add("AutoLoad", true);
    
    _regionManager.RequestNavigate("ContentRegion", "DwgManagerTabView", parameters);
}
```

### 场景3: 搜索结果导航
```csharp
// 从搜索结果导航到具体文件
public void NavigateToSearchResult(SearchResult result)
{
    var parameters = new NavigationParameters();
    parameters.Add("DwgFolderPath", result.ProjectPath);
    parameters.Add("SelectedProfession", result.Profession);
    parameters.Add("SelectedFileType", result.FileType);
    
    _regionManager.RequestNavigate("ContentRegion", "DwgManagerTabView", parameters);
}
```

## 🔍 调试和日志

所有导航操作都会记录详细的日志信息：

- `🧭 导航到DWG管理页面` - 开始导航
- `📂 设置DWG文件夹路径: {path}` - 路径设置
- `🎯 设置默认专业: {profession}` - 专业选择
- `🎯 设置默认文件类型: {fileType}` - 文件类型选择
- `✅ DWG管理页面导航完成` - 导航完成

查看日志可以帮助调试导航问题和优化用户体验。
