<UserControl x:Class="WPFTest.Views.WindowControls.TitleBarPageView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:codeExample="clr-namespace:Zylo.WPF.Controls.CodeExample;assembly=Zylo.WPF">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 状态栏 -->
        <ui:InfoBar Grid.Row="0"
                   IsOpen="True"
                   IsClosable="False"
                   Severity="Informational"
                   Title="TitleBar 控件示例"
                   Message="{Binding StatusMessage}"
                   Margin="20,20,20,10"/>

        <!-- 主要内容区域 -->
        <ScrollViewer Grid.Row="1" Margin="20,0,20,20">
            <StackPanel>
                <!-- 主要演示区域 -->
                <ui:Card Margin="0,0,0,20" Padding="20">
                    <StackPanel>
                        <TextBlock Text="🪟 TitleBar 演示" FontSize="18" FontWeight="Bold" Margin="0,0,0,15"/>
                        
                        <!-- TitleBar 演示 -->
                        <Border BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                BorderThickness="1"
                                CornerRadius="8"
                                Margin="0,0,0,20">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="200"/>
                                </Grid.RowDefinitions>

                                <!-- 模拟的 TitleBar -->
                                <ui:TitleBar Grid.Row="0"
                                           Title="{Binding TitleBarTitle}"
                                           Height="{Binding TitleBarHeight}"
                                           ShowMinimize="{Binding ShowMinimize}"
                                           ShowMaximize="{Binding ShowMaximize}"
                                           ShowClose="{Binding ShowClose}"
                                           CanMaximize="{Binding CanMaximize}">
                                    <ui:TitleBar.Icon>
                                        <ui:SymbolIcon Symbol="{Binding SelectedIcon}" 
                                                      Visibility="{Binding UseCustomIcon, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                    </ui:TitleBar.Icon>
                                </ui:TitleBar>

                                <!-- 模拟窗口内容 -->
                                <Border Grid.Row="1" 
                                        Background="{DynamicResource LayerFillColorDefaultBrush}"
                                        Padding="20">
                                    <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                                        <TextBlock Text="🖼️ 模拟窗口内容区域" 
                                                  FontSize="16" 
                                                  FontWeight="Medium"
                                                  HorizontalAlignment="Center"
                                                  Margin="0,0,0,10"/>
                                        <TextBlock Text="这里展示了 TitleBar 控件的效果" 
                                                  FontSize="12" 
                                                  Opacity="0.7"
                                                  HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                            </Grid>
                        </Border>

                        <!-- 操作按钮 -->
                        <WrapPanel HorizontalAlignment="Center">
                            <ui:Button Content="📉 模拟最小化" 
                                      Command="{Binding SimulateMinimizeCommand}"
                                      Margin="0,0,10,0"/>
                            <ui:Button Content="📈 模拟最大化" 
                                      Command="{Binding SimulateMaximizeCommand}"
                                      Margin="0,0,10,0"/>
                            <ui:Button Content="❌ 模拟关闭" 
                                      Command="{Binding SimulateCloseCommand}"
                                      Appearance="Secondary"/>
                        </WrapPanel>
                    </StackPanel>
                </ui:Card>

                <!-- 配置面板 -->
                <ui:Card Margin="0,0,0,20" Padding="20">
                    <StackPanel>
                        <TextBlock Text="⚙️ TitleBar 配置" FontSize="16" FontWeight="Bold" Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- 基础配置 -->
                            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                <TextBlock Text="基础设置" FontWeight="Medium" Margin="0,0,0,10"/>
                                
                                <TextBlock Text="标题" FontSize="12" Margin="0,0,0,4"/>
                                <ui:TextBox Text="{Binding TitleBarTitle}" Margin="0,0,0,10"/>
                                
                                <TextBlock Text="高度" FontSize="12" Margin="0,0,0,4"/>
                                <ui:NumberBox Value="{Binding TitleBarHeight}" 
                                             Minimum="25" 
                                             Maximum="60" 
                                             Margin="0,0,0,10"/>
                            </StackPanel>

                            <!-- 按钮显示配置 -->
                            <StackPanel Grid.Column="1" Margin="10,0">
                                <TextBlock Text="按钮显示" FontWeight="Medium" Margin="0,0,0,10"/>
                                
                                <CheckBox Content="显示最小化按钮" IsChecked="{Binding ShowMinimize}" Margin="0,0,0,8"/>
                                <CheckBox Content="显示最大化按钮" IsChecked="{Binding ShowMaximize}" Margin="0,0,0,8"/>
                                <CheckBox Content="显示关闭按钮" IsChecked="{Binding ShowClose}" Margin="0,0,0,8"/>
                                <CheckBox Content="允许最大化" IsChecked="{Binding CanMaximize}" Margin="0,0,0,8"/>
                            </StackPanel>

                            <!-- 图标配置 -->
                            <StackPanel Grid.Column="2" Margin="10,0,0,0">
                                <TextBlock Text="图标设置" FontWeight="Medium" Margin="0,0,0,10"/>
                                
                                <CheckBox Content="使用自定义图标" IsChecked="{Binding UseCustomIcon}" Margin="0,0,0,8"/>
                                
                                <TextBlock Text="选择图标" FontSize="12" Margin="0,0,0,4"/>
                                <ComboBox ItemsSource="{Binding AvailableIcons}"
                                         SelectedValue="{Binding SelectedIcon}"
                                         SelectedValuePath="Symbol"
                                         DisplayMemberPath="Name"
                                         IsEnabled="{Binding UseCustomIcon}"
                                         Margin="0,0,0,10"/>
                            </StackPanel>
                        </Grid>

                        <!-- 配置操作按钮 -->
                        <WrapPanel HorizontalAlignment="Center" Margin="0,15,0,0">
                            <ui:Button Content="✅ 应用配置" 
                                      Command="{Binding ApplyConfigurationCommand}"
                                      Appearance="Primary"
                                      Margin="0,0,10,0"/>
                            <ui:Button Content="🔄 重置配置" 
                                      Command="{Binding ResetConfigurationCommand}"
                                      Appearance="Secondary"/>
                        </WrapPanel>
                    </StackPanel>
                </ui:Card>

                <!-- 统计信息 -->
                <ui:Card Margin="0,0,0,20" Padding="20">
                    <StackPanel>
                        <TextBlock Text="📊 使用统计" FontSize="16" FontWeight="Bold" Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                <TextBlock Text="{Binding ButtonClickCount}" 
                                          FontSize="24" 
                                          FontWeight="Bold" 
                                          Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                          HorizontalAlignment="Center"/>
                                <TextBlock Text="按钮点击" FontSize="12" HorizontalAlignment="Center"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                <TextBlock Text="{Binding ConfigChangeCount}" 
                                          FontSize="24" 
                                          FontWeight="Bold" 
                                          Foreground="{DynamicResource SystemAccentColorSecondaryBrush}"
                                          HorizontalAlignment="Center"/>
                                <TextBlock Text="配置更改" FontSize="12" HorizontalAlignment="Center"/>
                            </StackPanel>

                            <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                                <TextBlock Text="{Binding WindowOperationCount}" 
                                          FontSize="24" 
                                          FontWeight="Bold" 
                                          Foreground="{DynamicResource SystemAccentColorTertiaryBrush}"
                                          HorizontalAlignment="Center"/>
                                <TextBlock Text="窗口操作" FontSize="12" HorizontalAlignment="Center"/>
                            </StackPanel>

                            <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                                <ui:Button Content="📊 显示详情" 
                                          Command="{Binding ShowStatisticsCommand}"
                                          Appearance="Secondary"
                                          FontSize="12"/>
                            </StackPanel>
                        </Grid>

                        <TextBlock Text="{Binding LastUpdated, StringFormat='最后更新: {0:yyyy-MM-dd HH:mm:ss}'}" 
                                  FontSize="11" 
                                  Opacity="0.7" 
                                  HorizontalAlignment="Center"
                                  Margin="0,10,0,0"/>
                    </StackPanel>
                </ui:Card>

                <!-- 基础代码示例 -->
                <codeExample:CodeExampleControl
                    Title="基础 TitleBar 示例"
                    Language="XAML"
                    Description="展示基础 TitleBar 控件的使用方法"
                    ShowTabs="True"
                    XamlCode="{Binding BasicTitleBarXaml}"
                    CSharpCode="{Binding BasicTitleBarCs}"
                    IsExpanded="True"
                    Margin="0,0,0,15"/>

                <!-- 高级代码示例 -->
                <codeExample:CodeExampleControl
                    Title="高级 TitleBar 示例"
                    Language="XAML"
                    Description="展示高级 TitleBar 功能，包括数据绑定和自定义配置"
                    ShowTabs="True"
                    XamlCode="{Binding AdvancedTitleBarXaml}"
                    CSharpCode="{Binding AdvancedTitleBarCs}"
                    IsExpanded="False"
                    Margin="0,0,0,15"/>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
