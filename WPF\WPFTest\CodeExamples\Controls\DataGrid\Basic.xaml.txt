<!-- DataGrid 基础用法示例 -->
<StackPanel Margin="20" Spacing="15">

    <!-- 标准 DataGrid -->
    <GroupBox Header="标准 DataGrid" Padding="15">
        <DataGrid ItemsSource="{Binding EmployeeData}"
                  SelectedItem="{Binding SelectedEmployee, Mode=TwoWay}"
                  Style="{StaticResource DataGridStyle}"
                  Height="200"
                  AutoGenerateColumns="False"
                  CanUserAddRows="False"
                  CanUserDeleteRows="False"
                  IsReadOnly="True"
                  GridLinesVisibility="Horizontal"
                  HeadersVisibility="Column">
            <DataGrid.Columns>
                <DataGridTextColumn Header="ID" Binding="{Binding Id}" Width="60"/>
                <DataGridTextColumn Header="姓名" Binding="{Binding Name}" Width="120"/>
                <DataGridTextColumn Header="部门" Binding="{Binding Department}" Width="100"/>
                <DataGridTextColumn Header="职位" Binding="{Binding Position}" Width="120"/>
                <DataGridTextColumn Header="薪资" Binding="{Binding Salary, StringFormat=C}" Width="100"/>
                <DataGridTextColumn Header="入职日期" Binding="{Binding HireDate, StringFormat=yyyy-MM-dd}" Width="120"/>
            </DataGrid.Columns>
        </DataGrid>
    </GroupBox>

    <!-- 不同样式 -->
    <GroupBox Header="不同样式" Padding="15">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 紧凑样式 -->
            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                <TextBlock Text="紧凑样式" FontWeight="Medium" Margin="0,0,0,8"/>
                <DataGrid ItemsSource="{Binding EmployeeData}"
                          Style="{StaticResource CompactDataGridStyle}"
                          Height="120"
                          AutoGenerateColumns="False"
                          IsReadOnly="True">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="姓名" Binding="{Binding Name}" Width="80"/>
                        <DataGridTextColumn Header="部门" Binding="{Binding Department}" Width="80"/>
                    </DataGrid.Columns>
                </DataGrid>
            </StackPanel>

            <!-- 现代化样式 -->
            <StackPanel Grid.Column="1" Margin="10,0,0,0">
                <TextBlock Text="现代化样式" FontWeight="Medium" Margin="0,0,0,8"/>
                <DataGrid ItemsSource="{Binding EmployeeData}"
                          Style="{StaticResource ModernDataGridStyle}"
                          Height="120"
                          AutoGenerateColumns="False"
                          IsReadOnly="True">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="姓名" Binding="{Binding Name}" Width="80"/>
                        <DataGridTextColumn Header="部门" Binding="{Binding Department}" Width="80"/>
                    </DataGrid.Columns>
                </DataGrid>
            </StackPanel>
        </Grid>
    </GroupBox>

</StackPanel>
