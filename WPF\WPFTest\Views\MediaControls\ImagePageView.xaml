<UserControl
    d:DataContext="{d:DesignInstance mediaControls:ImagePageViewModel}"
    d:DesignHeight="2000"
    d:DesignWidth="1200"
    mc:Ignorable="d"
    mvvm:ViewModelLocator.AutoWireViewModel="True"
    x:Class="WPFTest.Views.MediaControls.ImagePageView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:codeExample="clr-namespace:Zylo.WPF.Controls.CodeExample;assembly=Zylo.WPF"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:mediaControls="clr-namespace:WPFTest.ViewModels.MediaControls"
    xmlns:mvvm="http://prismlibrary.com/"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <UserControl.Resources>
        <!--  转换器  -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />

        <!--  使用 Zylo 演示卡片样式 - 从 CardExpanderStyles.xaml 引用  -->
        <!--  DemoCardStyle 现在是 ZyloDemoCardStyle 的别名  -->

        <!--  文本块样式 - 适应主题  -->
        <Style TargetType="TextBlock" x:Key="ThemeTextStyle">
            <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}" />
        </Style>

        <!--  标题文本样式  -->
        <Style
            BasedOn="{StaticResource ThemeTextStyle}"
            TargetType="TextBlock"
            x:Key="HeaderTextStyle">
            <Setter Property="FontSize" Value="16" />
            <Setter Property="FontWeight" Value="SemiBold" />
        </Style>

        <!--  标签文本样式  -->
        <Style
            BasedOn="{StaticResource ThemeTextStyle}"
            TargetType="TextBlock"
            x:Key="LabelTextStyle">
            <Setter Property="FontWeight" Value="SemiBold" />
            <Setter Property="Margin" Value="0,0,0,8" />
        </Style>

        <!--  示例图片边框样式  -->
        <Style TargetType="Border" x:Key="ImageBorderStyle">
            <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="CornerRadius" Value="4" />
            <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}" />
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!--  页面标题和状态  -->
        <StackPanel Grid.Row="0" Margin="0,0,0,24">
            <TextBlock
                FontSize="28"
                FontWeight="Bold"
                Margin="0,0,0,8"
                Style="{StaticResource HeaderTextStyle}"
                Text="Image 控件示例" />
            <TextBlock
                FontSize="14"
                Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                Margin="0,0,0,16"
                Text="演示 WPF Image 控件的各种功能和用法" />

            <!--  状态栏  -->
            <ui:InfoBar
                IsClosable="False"
                IsOpen="True"
                Message="{Binding StatusMessage}"
                Severity="Informational"
                Title="状态信息" />
        </StackPanel>

        <!--  主要内容  -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="0">

                <!--  基础图片功能  -->
                <ui:CardExpander Style="{StaticResource DemoCardStyle}">
                    <ui:CardExpander.Header>
                        <StackPanel Orientation="Horizontal">
                            <ui:SymbolIcon
                                FontSize="20"
                                Margin="0,0,12,0"
                                Symbol="Image24" />
                            <TextBlock Style="{StaticResource HeaderTextStyle}" Text="基础图片功能" />
                        </StackPanel>
                    </ui:CardExpander.Header>

                    <StackPanel>
                        <!--  图片选择和基本设置  -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="300" />
                            </Grid.ColumnDefinitions>

                            <!--  图片显示区域  -->
                            <Border
                                Grid.Column="0"
                                Margin="0,0,16,0"
                                MinHeight="200"
                                Style="{StaticResource ImageBorderStyle}">
                                <Grid>
                                    <!--  图片本身 - 始终显示  -->
                                    <Image
                                        Height="{Binding ImageHeight}"
                                        HorizontalAlignment="Center"
                                        Source="{Binding SelectedImagePath}"
                                        Stretch="{Binding CurrentStretch}"
                                        VerticalAlignment="Center"
                                        Width="{Binding ImageWidth}" />

                                    <!--  边框装饰 - 根据设置显示/隐藏  -->
                                    <Border
                                        BorderBrush="{Binding BorderBrush}"
                                        BorderThickness="{Binding BorderThickness}"
                                        Visibility="{Binding ShowBorder, Converter={StaticResource BooleanToVisibilityConverter}}" />
                                </Grid>
                            </Border>

                            <!--  控制面板  -->
                            <StackPanel Grid.Column="1">
                                <ui:Button
                                    Command="{Binding SelectImageCommand}"
                                    Content="选择图片"
                                    HorizontalAlignment="Stretch"
                                    Icon="{ui:SymbolIcon FolderOpen24}" />

                                <Separator />

                                <!--  拉伸模式  -->
                                <StackPanel>
                                    <TextBlock Style="{StaticResource LabelTextStyle}" Text="拉伸模式" />
                                    <ComboBox
                                        HorizontalAlignment="Stretch"
                                        ItemsSource="{Binding StretchModes}"
                                        SelectedItem="{Binding CurrentStretch, Converter={StaticResource EnumToStringConverter}}" />
                                </StackPanel>

                                <!--  图片尺寸  -->
                                <StackPanel>
                                    <TextBlock Style="{StaticResource LabelTextStyle}" Text="图片尺寸" />
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*" />
                                            <ColumnDefinition Width="8" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <ui:NumberBox
                                            Grid.Column="0"
                                            Maximum="800"
                                            Minimum="50"
                                            PlaceholderText="宽度"
                                            Value="{Binding ImageWidth}" />
                                        <ui:NumberBox
                                            Grid.Column="2"
                                            Maximum="600"
                                            Minimum="50"
                                            PlaceholderText="高度"
                                            Value="{Binding ImageHeight}" />
                                    </Grid>
                                </StackPanel>

                                <!--  边框设置  -->
                                <StackPanel>
                                    <CheckBox
                                        Content="显示边框"
                                        IsChecked="{Binding ShowBorder}"
                                        Margin="0,0,0,8" />
                                    <ui:Button
                                        Command="{Binding ResetImageSizeCommand}"
                                        Content="重置大小"
                                        HorizontalAlignment="Stretch" />
                                </StackPanel>
                            </StackPanel>
                        </Grid>

                        <!--  代码示例  -->
                        <codeExample:CodeExampleControl
                            CSharpCode="{Binding BasicImageCSharpExample}"
                            Description="演示 Image 控件的基本属性和拉伸模式"
                            IsExpanded="False"
                            ShowTabs="True"
                            Title="基础图片用法"
                            XamlCode="{Binding BasicImageXamlExample}" />
                    </StackPanel>
                </ui:CardExpander>

                <!--  图片轮播功能  -->
                <ui:CardExpander Style="{StaticResource DemoCardStyle}">
                    <ui:CardExpander.Header>
                        <StackPanel Orientation="Horizontal">
                            <ui:SymbolIcon
                                FontSize="20"
                                Margin="0,0,12,0"
                                Symbol="Showerhead20" />
                            <TextBlock Style="{StaticResource HeaderTextStyle}" Text="图片轮播功能" />
                        </StackPanel>
                    </ui:CardExpander.Header>

                    <StackPanel>
                        <!--  轮播显示区域  -->
                        <Border
                            Height="250"
                            HorizontalAlignment="Center"
                            Style="{StaticResource ImageBorderStyle}"
                            Width="400">
                            <Grid>
                                <!--  轮播图片  -->
                                <Image Source="{Binding CurrentCarouselImage}" Stretch="UniformToFill" />

                                <!--  控制按钮  -->
                                <StackPanel
                                    HorizontalAlignment="Center"
                                    Margin="0,0,0,16"
                                    Orientation="Horizontal"
                                    VerticalAlignment="Bottom">
                                    <ui:Button
                                        Command="{Binding PreviousImageCommand}"
                                        Content="◀"
                                        Height="40"
                                        Margin="4"
                                        Width="40" />
                                    <ui:Button
                                        Command="{Binding ToggleAutoPlayCommand}"
                                        Content="{Binding PlayPauseButtonText}"
                                        Height="40"
                                        Margin="4"
                                        Width="40" />
                                    <ui:Button
                                        Command="{Binding NextImageCommand}"
                                        Content="▶"
                                        Height="40"
                                        Margin="4"
                                        Width="40" />
                                </StackPanel>

                                <!--  指示器  -->
                                <StackPanel
                                    HorizontalAlignment="Center"
                                    Margin="0,16,0,0"
                                    Orientation="Horizontal"
                                    VerticalAlignment="Top">
                                    <Ellipse
                                        Fill="White"
                                        Height="8"
                                        Margin="2"
                                        Opacity="0.5"
                                        Width="8" />
                                    <Ellipse
                                        Fill="White"
                                        Height="8"
                                        Margin="2"
                                        Opacity="1.0"
                                        Width="8" />
                                    <Ellipse
                                        Fill="White"
                                        Height="8"
                                        Margin="2"
                                        Opacity="0.5"
                                        Width="8" />
                                    <Ellipse
                                        Fill="White"
                                        Height="8"
                                        Margin="2"
                                        Opacity="0.5"
                                        Width="8" />
                                </StackPanel>
                            </Grid>
                        </Border>

                        <!--  轮播控制  -->
                        <StackPanel HorizontalAlignment="Center" Orientation="Horizontal">
                            <CheckBox Content="自动播放" IsChecked="{Binding IsAutoPlay}" />
                            <TextBlock Text="间隔:" VerticalAlignment="Center" />
                            <ui:NumberBox
                                Maximum="10"
                                Minimum="1"
                                Value="{Binding CarouselInterval}"
                                Width="80" />
                            <TextBlock Text="秒" VerticalAlignment="Center" />
                        </StackPanel>

                        <!--  代码示例  -->
                        <codeExample:CodeExampleControl
                            CSharpCode="{Binding AdvancedImageCSharpExample}"
                            Description="使用定时器实现图片自动轮播功能"
                            IsExpanded="False"
                            ShowTabs="True"
                            Title="图片轮播实现"
                            XamlCode="{Binding AdvancedImageXamlExample}" />
                    </StackPanel>
                </ui:CardExpander>

                <!--  图片特效和样式  -->
                <ui:CardExpander Style="{StaticResource DemoCardStyle}">
                    <ui:CardExpander.Header>
                        <StackPanel Margin="12" Orientation="Horizontal">
                            <ui:SymbolIcon FontSize="20" Symbol="TextEffects20" />
                            <TextBlock Style="{StaticResource HeaderTextStyle}" Text="图片特效和样式" />
                        </StackPanel>
                    </ui:CardExpander.Header>

                    <StackPanel Margin="16">
                        <!--  特效演示区域  -->
                        <WrapPanel HorizontalAlignment="Center">
                            <!--  原图  -->
                            <StackPanel Margin="8">
                                <TextBlock
                                    FontWeight="SemiBold"
                                    HorizontalAlignment="Center"
                                    Margin="0,0,0,8"
                                    Text="原图" />
                                <Border
                                    Height="90"
                                    Style="{StaticResource ImageBorderStyle}"
                                    Width="120">
                                    <Image Source="{Binding SelectedImagePath}" Stretch="UniformToFill" />
                                </Border>
                            </StackPanel>

                            <!--  模糊效果  -->
                            <StackPanel Margin="8">
                                <TextBlock
                                    FontWeight="SemiBold"
                                    HorizontalAlignment="Center"
                                    Margin="0,0,0,8"
                                    Text="模糊效果" />
                                <Border
                                    Height="90"
                                    Style="{StaticResource ImageBorderStyle}"
                                    Width="120">
                                    <Image Source="{Binding SelectedImagePath}" Stretch="UniformToFill">
                                        <Image.Effect>
                                            <BlurEffect Radius="{Binding BlurRadius}" />
                                        </Image.Effect>
                                    </Image>
                                </Border>
                            </StackPanel>

                            <!--  阴影效果  -->
                            <StackPanel Margin="8">
                                <TextBlock
                                    FontWeight="SemiBold"
                                    HorizontalAlignment="Center"
                                    Margin="0,0,0,8"
                                    Text="阴影效果" />
                                <Border
                                    Height="90"
                                    Style="{StaticResource ImageBorderStyle}"
                                    Width="120">
                                    <Image Source="{Binding SelectedImagePath}" Stretch="UniformToFill">
                                        <Image.Effect>
                                            <DropShadowEffect
                                                Color="Black"
                                                Direction="{Binding ShadowDirection}"
                                                Opacity="{Binding ShadowOpacity}"
                                                ShadowDepth="{Binding ShadowDepth}" />
                                        </Image.Effect>
                                    </Image>
                                </Border>
                            </StackPanel>

                            <!--  圆形裁剪  -->
                            <StackPanel Margin="8">
                                <TextBlock
                                    FontWeight="SemiBold"
                                    HorizontalAlignment="Center"
                                    Margin="0,0,0,8"
                                    Text="圆形裁剪" />
                                <Ellipse Height="90" Width="90">
                                    <Ellipse.Fill>
                                        <ImageBrush ImageSource="{Binding SelectedImagePath}" Stretch="UniformToFill" />
                                    </Ellipse.Fill>
                                </Ellipse>
                            </StackPanel>
                        </WrapPanel>

                        <!--  特效参数控制  -->
                        <Expander Header="特效参数调节" IsExpanded="False">
                            <Grid Margin="0,12,0,0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>

                                <!--  模糊参数  -->
                                <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                    <TextBlock
                                        FontWeight="SemiBold"
                                        Margin="0,0,0,8"
                                        Text="模糊半径" />
                                    <Slider
                                        IsSnapToTickEnabled="True"
                                        Maximum="20"
                                        Minimum="0"
                                        TickFrequency="1"
                                        Value="{Binding BlurRadius}" />
                                    <TextBlock HorizontalAlignment="Center" Text="{Binding BlurRadius, StringFormat='{}{0:F1}'}" />
                                </StackPanel>

                                <!--  阴影参数  -->
                                <StackPanel Grid.Column="1" Margin="8,0">
                                    <TextBlock
                                        FontWeight="SemiBold"
                                        Margin="0,0,0,8"
                                        Text="阴影深度" />
                                    <Slider
                                        IsSnapToTickEnabled="True"
                                        Maximum="20"
                                        Minimum="0"
                                        TickFrequency="1"
                                        Value="{Binding ShadowDepth}" />
                                    <TextBlock HorizontalAlignment="Center" Text="{Binding ShadowDepth, StringFormat='{}{0:F1}'}" />
                                </StackPanel>

                                <!--  阴影方向  -->
                                <StackPanel Grid.Column="2" Margin="8,0,0,0">
                                    <TextBlock
                                        FontWeight="SemiBold"
                                        Margin="0,0,0,8"
                                        Text="阴影方向" />
                                    <Slider
                                        IsSnapToTickEnabled="True"
                                        Maximum="360"
                                        Minimum="0"
                                        TickFrequency="15"
                                        Value="{Binding ShadowDirection}" />
                                    <TextBlock HorizontalAlignment="Center" Text="{Binding ShadowDirection, StringFormat='{}{0:F0}°'}" />
                                </StackPanel>
                            </Grid>
                        </Expander>

                        <!--  代码示例  -->
                        <codeExample:CodeExampleControl
                            CSharpCode="{Binding ImageEffectsCSharpExample}"
                            Description="使用 WPF 内置特效为图片添加视觉效果"
                            IsExpanded="False"
                            ShowTabs="True"
                            Title="图片特效实现"
                            XamlCode="{Binding ImageEffectsXamlExample}" />
                    </StackPanel>
                </ui:CardExpander>

                <!--  图片画廊  -->
                <ui:CardExpander Style="{StaticResource DemoCardStyle}">
                    <ui:CardExpander.Header>
                        <StackPanel Margin="12" Orientation="Horizontal">
                            <ui:SymbolIcon FontSize="20" Symbol="ImageMultiple24" />
                            <TextBlock Style="{StaticResource HeaderTextStyle}" Text="图片画廊" />
                        </StackPanel>
                    </ui:CardExpander.Header>

                    <StackPanel Margin="16">
                        <!--  画廊网格  -->
                        <ScrollViewer
                            Height="200"
                            HorizontalScrollBarVisibility="Auto"
                            VerticalScrollBarVisibility="Disabled">
                            <ItemsControl ItemsSource="{Binding GalleryImages}">
                                <ItemsControl.ItemsPanel>
                                    <ItemsPanelTemplate>
                                        <StackPanel Orientation="Horizontal" />
                                    </ItemsPanelTemplate>
                                </ItemsControl.ItemsPanel>
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border
                                            Height="120"
                                            Margin="8,0"
                                            Style="{StaticResource ImageBorderStyle}"
                                            Width="150">
                                            <Image Source="{Binding}" Stretch="UniformToFill" />
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>

                        <!--  画廊控制  -->
                        <StackPanel
                            HorizontalAlignment="Center"
                            Margin="12"
                            Orientation="Horizontal">
                            <ui:Button
                                Command="{Binding AddToGalleryCommand}"
                                Content="添加图片"
                                Icon="{ui:SymbolIcon Add24}" />
                            <ui:Button
                                Command="{Binding ClearGalleryCommand}"
                                Content="清空画廊"
                                Icon="{ui:SymbolIcon Delete24}" />
                        </StackPanel>
                    </StackPanel>
                </ui:CardExpander>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
