﻿using System;
using System.Windows;
using Zylo.WPF;
using Zylo.YLog.Runtime;
using WPFTest.Services;

namespace WPFTest;

/// <summary>
/// 使用 Zylo.WPF 和 Prism 的 WPFTest 应用程序
/// </summary>
public partial class App : Application
{
    public readonly YLoggerInstance _logger = YLogger.ForSilent<App>();
    protected override async void OnStartup(StartupEventArgs e)
    {
  

        try
        {
            // 🎯 使用 Zylo.WPF 扩展方法，一行代码搞定！
            this.UseZyloMvvm<AppBootstrapper>();
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ WPFTest 应用程序启动失败: {ex.Message}");
            MessageBox.Show($"应用程序启动失败：{ex.Message}", "启动错误", MessageBoxButton.OK, MessageBoxImage.Error);
            Shutdown(1);
        }
    }

    protected override void OnExit(ExitEventArgs e)
    {
        base.OnExit(e);
    }
}