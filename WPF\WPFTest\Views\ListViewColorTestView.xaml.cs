using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using Zylo.WPF;
using Zylo.WPF.Services;
using Zylo.YLog.Runtime;

namespace WPFTest.Views
{
    /// <summary>
    /// ListView颜色测试页面
    /// </summary>
    public partial class ListViewColorTestView : UserControl
    {
        private readonly YLoggerInstance _logger = YLogger.ForSimple<ListViewColorTestView>();
        private readonly IThemeManagementService _themeService;

        public ListViewColorTestView()
        {
            InitializeComponent();
            _themeService = ZyloContainer.Resolve<IThemeManagementService>();
            
            // 初始化时刷新资源信息
            Loaded += (s, e) => RefreshResourceInfo(s, e);
        }

        private void SetRedAccent(object sender, RoutedEventArgs e)
        {
            _themeService.ApplyAccentColor(Colors.Red);
            RefreshResourceInfo(sender, e);
            _logger.Info("🔴 已设置红色强调色");
        }

        private void SetGreenAccent(object sender, RoutedEventArgs e)
        {
            _themeService.ApplyAccentColor(Colors.Green);
            RefreshResourceInfo(sender, e);
            _logger.Info("🟢 已设置绿色强调色");
        }

        private void SetBlueAccent(object sender, RoutedEventArgs e)
        {
            _themeService.ApplyAccentColor(Colors.Blue);
            RefreshResourceInfo(sender, e);
            _logger.Info("🔵 已设置蓝色强调色");
        }

        private void SetYellowAccent(object sender, RoutedEventArgs e)
        {
            _themeService.ApplyAccentColor(Colors.Gold);
            RefreshResourceInfo(sender, e);
            _logger.Info("🟡 已设置黄色强调色");
        }

        private void SetPurpleAccent(object sender, RoutedEventArgs e)
        {
            _themeService.ApplyAccentColor(Colors.Purple);
            RefreshResourceInfo(sender, e);
            _logger.Info("🟣 已设置紫色强调色");
        }

        private void RefreshResourceInfo(object? sender, RoutedEventArgs? e)
        {
            try
            {
                var app = Application.Current;
                if (app?.Resources != null)
                {
                    // 获取ListView相关资源
                    var pillColor = GetResourceColor("ListViewItemPillFillBrush");
                    var hoverColor = GetResourceColor("ListViewItemBackgroundPointerOver");
                    var accentColor = GetResourceColor("SystemAccentColorPrimaryBrush");

                    // 更新显示
                    PillColorText.Text = $"选中指示器: {pillColor}";
                    HoverColorText.Text = $"悬停背景: {hoverColor}";
                    AccentColorText.Text = $"系统强调色: {accentColor}";

                    _logger.Info($"🔄 资源信息已刷新 - 指示器:{pillColor}, 悬停:{hoverColor}, 强调色:{accentColor}");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 刷新资源信息失败: {ex.Message}");
            }
        }

        private string GetResourceColor(string resourceKey)
        {
            try
            {
                var app = Application.Current;
                if (app?.Resources?.Contains(resourceKey) == true)
                {
                    var resource = app.Resources[resourceKey];
                    if (resource is SolidColorBrush brush)
                    {
                        var color = brush.Color;
                        return $"#{color.A:X2}{color.R:X2}{color.G:X2}{color.B:X2}";
                    }
                    else if (resource is Color colorResource)
                    {
                        return $"#{colorResource.A:X2}{colorResource.R:X2}{colorResource.G:X2}{colorResource.B:X2}";
                    }
                    else
                    {
                        return $"{resource?.GetType().Name ?? "null"}";
                    }
                }
                return "未找到";
            }
            catch (Exception ex)
            {
                _logger.Warning($"⚠️ 获取资源颜色失败 {resourceKey}: {ex.Message}");
                return "错误";
            }
        }
    }
}
