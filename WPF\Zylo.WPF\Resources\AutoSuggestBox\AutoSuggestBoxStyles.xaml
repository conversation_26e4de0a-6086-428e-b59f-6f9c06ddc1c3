<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml">

    <!-- AutoSuggestBox 基础样式 -->
    <Style x:Key="AutoSuggestBoxBaseStyle" TargetType="ui:AutoSuggestBox">
        <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontFamily" Value="{DynamicResource ContentControlThemeFontFamily}"/>
        <Setter Property="MinHeight" Value="32"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="HorizontalContentAlignment" Value="Left"/>

        <!-- 添加鼠标悬停和聚焦效果 -->
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorSecondaryBrush}"/>
            </Trigger>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="Background" Value="{DynamicResource ControlFillColorInputActiveBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource SystemAccentColorPrimaryBrush}"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Opacity" Value="0.5"/>
                <Setter Property="Background" Value="{DynamicResource ControlFillColorDisabledBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- AutoSuggestBox 标准样式 -->
    <Style x:Key="AutoSuggestBoxStyle" TargetType="ui:AutoSuggestBox" BasedOn="{StaticResource AutoSuggestBoxBaseStyle}">
        <!-- 标准样式的特定设置 -->
        <Setter Property="Width" Value="250"/>
    </Style>

    <!-- AutoSuggestBox 小型样式 -->
    <Style x:Key="SmallAutoSuggestBoxStyle" TargetType="ui:AutoSuggestBox" BasedOn="{StaticResource AutoSuggestBoxBaseStyle}">
        <Setter Property="Padding" Value="6"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="MinHeight" Value="28"/>
        <Setter Property="Width" Value="200"/>
    </Style>

    <!-- AutoSuggestBox 大型样式 -->
    <Style x:Key="LargeAutoSuggestBoxStyle" TargetType="ui:AutoSuggestBox" BasedOn="{StaticResource AutoSuggestBoxBaseStyle}">
        <Setter Property="Padding" Value="12"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="MinHeight" Value="40"/>
        <Setter Property="Width" Value="300"/>
    </Style>

    <!-- AutoSuggestBox 透明样式 -->
    <Style x:Key="TransparentAutoSuggestBoxStyle" TargetType="ui:AutoSuggestBox" BasedOn="{StaticResource AutoSuggestBoxBaseStyle}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}"/>
            </Trigger>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="Background" Value="{DynamicResource ControlFillColorInputActiveBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- AutoSuggestBox 圆角样式 -->
    <Style x:Key="RoundedAutoSuggestBoxStyle" TargetType="ui:AutoSuggestBox" BasedOn="{StaticResource AutoSuggestBoxBaseStyle}">
        <Setter Property="Padding" Value="12,8"/>
        <!-- 注意：AutoSuggestBox 不支持 CornerRadius 属性 -->
    </Style>

    <!-- AutoSuggestBox 无边框样式 -->
    <Style x:Key="BorderlessAutoSuggestBoxStyle" TargetType="ui:AutoSuggestBox" BasedOn="{StaticResource AutoSuggestBoxBaseStyle}">
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Background" Value="{DynamicResource ControlFillColorTertiaryBrush}"/>
    </Style>

    <!-- AutoSuggestBox 搜索框样式 -->
    <Style x:Key="SearchAutoSuggestBoxStyle" TargetType="ui:AutoSuggestBox" BasedOn="{StaticResource AutoSuggestBoxBaseStyle}">
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Width" Value="300"/>
        <Setter Property="Icon" Value="{ui:SymbolIcon Search16}"/>
        <Style.Triggers>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="BorderBrush" Value="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                <Setter Property="Background" Value="{DynamicResource ControlFillColorInputActiveBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- AutoSuggestBox 紧凑样式 -->
    <Style x:Key="CompactAutoSuggestBoxStyle" TargetType="ui:AutoSuggestBox" BasedOn="{StaticResource AutoSuggestBoxBaseStyle}">
        <Setter Property="Padding" Value="4"/>
        <Setter Property="FontSize" Value="11"/>
        <Setter Property="MinHeight" Value="24"/>
        <Setter Property="Width" Value="150"/>
        <Setter Property="Margin" Value="2"/>
    </Style>

</ResourceDictionary>
