<!-- PasswordBox 安全特性示例 -->
<StackPanel Margin="20" Spacing="15">

    <!-- 安全输入控制 -->
    <GroupBox Header="安全输入控制" Padding="15">
        <StackPanel>
            <TextBlock Text="禁用复制粘贴和拖放功能的安全密码框：" FontWeight="Bold" Margin="0,0,0,8"/>
            
            <PasswordBox Style="{StaticResource SecurePasswordBoxStyle}"
                         ui:TextBoxHelper.PlaceholderText="安全密码输入（禁用复制粘贴）"
                         AllowDrop="False"
                         ContextMenu="{x:Null}"
                         Width="300"
                         HorizontalAlignment="Left"
                         Margin="0,0,0,8"/>
            
            <TextBlock Text="特性：" FontWeight="Bold" Margin="0,8,0,4"/>
            <StackPanel Margin="16,0,0,0">
                <TextBlock Text="• 禁用右键上下文菜单" FontSize="12"/>
                <TextBlock Text="• 禁用拖放操作" FontSize="12"/>
                <TextBlock Text="• 使用特殊密码字符 (■)" FontSize="12"/>
                <TextBlock Text="• 限制最大长度为64字符" FontSize="12"/>
                <TextBlock Text="• 禁用自动完成功能" FontSize="12"/>
            </StackPanel>
        </StackPanel>
    </GroupBox>

    <!-- 密码强度实时检测 -->
    <GroupBox Header="密码强度实时检测" Padding="15">
        <StackPanel>
            <TextBlock Text="输入密码查看实时强度分析：" FontWeight="Bold" Margin="0,0,0,8"/>
            
            <Grid Width="400" HorizontalAlignment="Left">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- 密码输入框 -->
                <PasswordBox Grid.Row="0"
                             Style="{StaticResource PasswordBoxStyle}"
                             ui:TextBoxHelper.PlaceholderText="输入密码查看强度分析"
                             x:Name="StrengthAnalysisPasswordBox"
                             Margin="0,0,0,8"/>
                
                <!-- 强度指示器 -->
                <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,8">
                    <TextBlock Text="强度：" VerticalAlignment="Center" Margin="0,0,8,0"/>
                    <ProgressBar Width="200" Height="8" Value="0" Maximum="100" VerticalAlignment="Center"/>
                    <TextBlock Text="请输入密码" Margin="8,0,0,0" VerticalAlignment="Center" FontSize="12"/>
                </StackPanel>
                
                <!-- 详细分析 -->
                <Border Grid.Row="2" 
                        Background="{DynamicResource ControlFillColorSecondaryBrush}"
                        CornerRadius="4" 
                        Padding="12"
                        Margin="0,0,0,8">
                    <StackPanel>
                        <TextBlock Text="安全性分析：" FontWeight="Bold" Margin="0,0,0,4"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="✗ 长度 ≥ 8 字符" FontSize="11" Foreground="Red"/>
                                <TextBlock Text="✗ 包含大写字母" FontSize="11" Foreground="Red"/>
                                <TextBlock Text="✗ 包含小写字母" FontSize="11" Foreground="Red"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="✗ 包含数字" FontSize="11" Foreground="Red"/>
                                <TextBlock Text="✗ 包含特殊字符" FontSize="11" Foreground="Red"/>
                                <TextBlock Text="✗ 字符多样性" FontSize="11" Foreground="Red"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>
                
                <!-- 安全建议 -->
                <Border Grid.Row="3"
                        Background="{DynamicResource SystemFillColorAttentionBrush}"
                        CornerRadius="4"
                        Padding="12">
                    <StackPanel>
                        <TextBlock Text="💡 安全建议：" FontWeight="Bold" Margin="0,0,0,4"/>
                        <TextBlock Text="• 使用至少12个字符的密码" FontSize="11"/>
                        <TextBlock Text="• 混合使用大小写字母、数字和符号" FontSize="11"/>
                        <TextBlock Text="• 避免使用个人信息或常见词汇" FontSize="11"/>
                        <TextBlock Text="• 定期更换密码" FontSize="11"/>
                    </StackPanel>
                </Border>
            </Grid>
        </StackPanel>
    </GroupBox>

    <!-- 密码泄露检测 -->
    <GroupBox Header="密码泄露检测" Padding="15">
        <StackPanel>
            <TextBlock Text="检测密码是否在已知泄露数据库中：" FontWeight="Bold" Margin="0,0,0,8"/>
            
            <Grid Width="400" HorizontalAlignment="Left">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <PasswordBox Grid.Column="0"
                             Style="{StaticResource PasswordBoxStyle}"
                             ui:TextBoxHelper.PlaceholderText="输入密码检测是否泄露"
                             Margin="0,0,8,0"/>
                
                <ui:Button Grid.Column="1"
                           Content="检测"
                           Appearance="Secondary"/>
            </Grid>
            
            <!-- 检测结果 -->
            <Border Background="{DynamicResource SystemFillColorSuccessBrush}"
                    CornerRadius="4"
                    Padding="12"
                    Margin="0,8,0,0"
                    Width="400"
                    HorizontalAlignment="Left">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="✓" FontSize="16" Foreground="Green" Margin="0,0,8,0"/>
                    <TextBlock Text="此密码未在已知泄露数据库中发现" FontSize="12"/>
                </StackPanel>
            </Border>
        </StackPanel>
    </GroupBox>

    <!-- 密码过期提醒 -->
    <GroupBox Header="密码过期提醒" Padding="15">
        <StackPanel>
            <TextBlock Text="密码使用时间跟踪和过期提醒：" FontWeight="Bold" Margin="0,0,0,8"/>
            
            <Border Background="{DynamicResource SystemFillColorCautionBrush}"
                    CornerRadius="4"
                    Padding="12"
                    Margin="0,0,0,8">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                        <TextBlock Text="⚠️" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="密码过期提醒" FontWeight="Bold"/>
                    </StackPanel>
                    <TextBlock Text="您的密码已使用 89 天，建议更换新密码" FontSize="12"/>
                    <TextBlock Text="上次更换时间：2024-01-15" FontSize="11" Foreground="Gray"/>
                </StackPanel>
            </Border>
            
            <StackPanel Orientation="Horizontal">
                <ui:Button Content="立即更换密码" Appearance="Primary" Margin="0,0,8,0"/>
                <ui:Button Content="稍后提醒" Appearance="Secondary"/>
            </StackPanel>
        </StackPanel>
    </GroupBox>

    <!-- 安全审计日志 -->
    <GroupBox Header="安全审计日志" Padding="15">
        <StackPanel>
            <TextBlock Text="密码相关操作的安全日志：" FontWeight="Bold" Margin="0,0,0,8"/>
            
            <ListBox Height="120" Background="{DynamicResource ControlFillColorSecondaryBrush}">
                <ListBoxItem>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Column="0" Text="🔒" Margin="0,0,8,0"/>
                        <TextBlock Grid.Column="1" Text="密码更换成功" VerticalAlignment="Center"/>
                        <TextBlock Grid.Column="2" Text="2024-01-20 14:30" FontSize="10" Foreground="Gray"/>
                    </Grid>
                </ListBoxItem>
                
                <ListBoxItem>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Column="0" Text="⚠️" Margin="0,0,8,0"/>
                        <TextBlock Grid.Column="1" Text="检测到弱密码尝试" VerticalAlignment="Center"/>
                        <TextBlock Grid.Column="2" Text="2024-01-20 14:25" FontSize="10" Foreground="Gray"/>
                    </Grid>
                </ListBoxItem>
                
                <ListBoxItem>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Column="0" Text="🛡️" Margin="0,0,8,0"/>
                        <TextBlock Grid.Column="1" Text="启用双因素认证" VerticalAlignment="Center"/>
                        <TextBlock Grid.Column="2" Text="2024-01-15 09:15" FontSize="10" Foreground="Gray"/>
                    </Grid>
                </ListBoxItem>
            </ListBox>
            
            <ui:Button Content="导出安全日志"
                       Appearance="Secondary"
                       HorizontalAlignment="Left"
                       Margin="0,8,0,0"/>
        </StackPanel>
    </GroupBox>

</StackPanel>
