<UserControl
    d:DataContext="{d:DesignInstance Type=dragDrop:DwgManagerTabViewModel}"
    d:DesignHeight="800"
    d:DesignWidth="1400"
    mc:Ignorable="d"
    mvvm:ViewModelLocator.AutoWireViewModel="True"
    x:Class="WPFTest.Views.DragDrop.DwgManagerTabView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:behaviors1="clr-namespace:Zylo.WPF.Behaviors;assembly=Zylo.WPF"
    xmlns:componentModel="clr-namespace:System.ComponentModel;assembly=WindowsBase"
    xmlns:converters="clr-namespace:Zylo.WPF.Converters;assembly=Zylo.WPF"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dd="urn:gong-wpf-dragdrop"
    xmlns:dragDrop="clr-namespace:WPFTest.ViewModels.DragDrop"
    xmlns:dwg="clr-namespace:WPFTest.Models.DWG"
    xmlns:dwgViews="clr-namespace:WPFTest.Views.DWG"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:listView="clr-namespace:Zylo.WPF.Behaviors.ListView;assembly=Zylo.WPF"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:mvvm="http://prismlibrary.com/"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <UserControl.Resources>
        <!--  转换器资源  -->
        <converters:CountToVisibilityConverter x:Key="CountToVisibilityConverter" />
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
        <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter" />

        <!--  只保留复杂和重复的样式  -->

        <!--  侧边栏容器样式  -->
        <Style TargetType="Border" x:Key="SidebarContainerStyle">
            <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}" />
            <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="CornerRadius" Value="8" />
        </Style>


        <!--  专业Tab按钮样式  -->
        <Style
            BasedOn="{StaticResource {x:Type ui:Button}}"
            TargetType="ui:Button"
            x:Key="ProfessionTabStyle">
            <Setter Property="MinWidth" Value="100" />
            <Setter Property="Margin" Value="4,2" />
            <Setter Property="Padding" Value="12,8" />
            <Setter Property="Appearance" Value="Secondary" />
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="CornerRadius" Value="6" />
            <Setter Property="HorizontalContentAlignment" Value="Center" />
            <Setter Property="VerticalContentAlignment" Value="Center" />
        </Style>

        <!--  拖拽装饰器模板  -->
        <DataTemplate x:Key="CustomDragAdornerTemplate">
            <Border
                Background="{DynamicResource SystemFillColorAttentionBrush}"
                BorderBrush="{DynamicResource SystemFillColorAttentionBrush}"
                BorderThickness="2"
                CornerRadius="20"
                Height="40"
                Opacity="0.9"
                Width="40">
                <TextBlock
                    FontSize="20"
                    Foreground="White"
                    HorizontalAlignment="Center"
                    Text="📐"
                    VerticalAlignment="Center" />
            </Border>
        </DataTemplate>
    </UserControl.Resources>

    <Grid Margin="5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />

            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  标题栏  -->
        <Grid Grid.Row="0" Margin="0,0,0,16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <StackPanel Grid.Column="0">
                <TextBlock
                    FontSize="24"
                    FontWeight="Bold"
                    Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                    Text="🏗️ DWG 图纸管理系统" />
                <TextBlock
                    FontSize="14"
                    Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                    Margin="0,4,0,0"
                    Text="专业分类的 CAD 图纸管理和拖拽工具" />
            </StackPanel>

        </Grid>


        <!--  主内容区域：左侧文件类型编辑器 + 右侧文件列表  -->
        <Grid Grid.Row="1">



            <!--  右侧：文件列表区域  -->
            <Border Style="{StaticResource SidebarContainerStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <!--  专业文件夹 Tab  -->
                    <Border
                        Background="{DynamicResource ControlFillColorDefaultBrush}"
                        BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                        BorderThickness="0,0,0,1"
                        Grid.Row="0"
                        Padding="16,8">
                        <ScrollViewer
                            Background="Transparent"
                            HorizontalScrollBarVisibility="Hidden"
                            PanningMode="HorizontalOnly"
                            VerticalScrollBarVisibility="Disabled"
                            x:Name="FolderScrollViewer">
                            <b:Interaction.Behaviors>
                                <listView:MouseWheelScrollBehavior />
                            </b:Interaction.Behaviors>
                            <ListBox
                                Background="Transparent"
                                BorderThickness="0"
                                ItemsSource="{Binding LDwgFolderModels}"
                                ScrollViewer.CanContentScroll="False"
                                ScrollViewer.HorizontalScrollBarVisibility="Hidden"
                                ScrollViewer.VerticalScrollBarVisibility="Hidden"
                                SelectedItem="{Binding TDwgFolderModel, Mode=TwoWay}"
                                dd:DragDrop.DropHandler="{Binding}"
                                dd:DragDrop.IsDropTarget="True">
                                <ListBox.ItemsPanel>
                                    <ItemsPanelTemplate>
                                        <StackPanel Orientation="Horizontal" />
                                    </ItemsPanelTemplate>
                                </ListBox.ItemsPanel>
                                <ListBox.ItemContainerStyle>
                                    <Style TargetType="ListBoxItem">
                                        <Setter Property="Padding" Value="0" />
                                        <Setter Property="Margin" Value="0" />
                                        <Setter Property="BorderThickness" Value="0" />
                                        <Setter Property="Background" Value="Transparent" />
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="ListBoxItem">
                                                    <ContentPresenter />
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </ListBox.ItemContainerStyle>
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <ui:Button
                                            Command="{Binding DataContext.SelectFolderCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                            CommandParameter="{Binding}"
                                            Margin="0"
                                            Padding="16,8">
                                            <ui:Button.Style>
                                                <Style BasedOn="{x:Null}" TargetType="ui:Button">
                                                    <!--  重置所有默认样式  -->
                                                    <Setter Property="Template">
                                                        <Setter.Value>
                                                            <ControlTemplate TargetType="ui:Button">
                                                                <Border
                                                                    Background="{TemplateBinding Background}"
                                                                    BorderBrush="{TemplateBinding BorderBrush}"
                                                                    BorderThickness="{TemplateBinding BorderThickness}"
                                                                    CornerRadius="{TemplateBinding CornerRadius}"
                                                                    Padding="{TemplateBinding Padding}"
                                                                    x:Name="border">
                                                                    <ContentPresenter
                                                                        Content="{TemplateBinding Content}"
                                                                        HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}" />
                                                                </Border>
                                                            </ControlTemplate>
                                                        </Setter.Value>
                                                    </Setter>

                                                    <!--  TabControl 样式 - 默认状态  -->
                                                    <Setter Property="Background" Value="Transparent" />
                                                    <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}" />
                                                    <Setter Property="BorderThickness" Value="1" />
                                                    <Setter Property="CornerRadius" Value="0" />
                                                    <Setter Property="MinHeight" Value="36" />
                                                    <Setter Property="HorizontalContentAlignment" Value="Center" />
                                                    <Setter Property="VerticalContentAlignment" Value="Center" />

                                                    <Style.Triggers>
                                                        <!--  选中状态 - TabControl 风格  -->
                                                        <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType=ListBoxItem}, Path=IsSelected}" Value="True">
                                                            <Setter Property="Background" Value="{DynamicResource SystemAccentColorLightBrush}" />
                                                            <Setter Property="BorderBrush" Value="{DynamicResource SystemAccentColorPrimaryBrush}" />
                                                            <Setter Property="BorderThickness" Value="1" />
                                                        </DataTrigger>

                                                        <!--  悬停效果 - 浅强调色  -->
                                                        <Trigger Property="IsMouseOver" Value="True">
                                                            <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}" />
                                                        </Trigger>

                                                        <!--  选中且悬停 - 保持选中样式  -->
                                                        <MultiDataTrigger>
                                                            <MultiDataTrigger.Conditions>
                                                                <Condition Binding="{Binding RelativeSource={RelativeSource Self}, Path=IsMouseOver}" Value="True" />
                                                                <Condition Binding="{Binding IsSelected}" Value="True" />
                                                            </MultiDataTrigger.Conditions>
                                                            <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}" />
                                                        </MultiDataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </ui:Button.Style>
                                            <StackPanel
                                                HorizontalAlignment="Center"
                                                Orientation="Horizontal"
                                                VerticalAlignment="Center">
                                                <!--  图标  -->
                                                <TextBlock
                                                    FontSize="18"
                                                    Margin="0,0,8,0"
                                                    Text="{Binding Icon}"
                                                    VerticalAlignment="Center">
                                                    <TextBlock.Style>
                                                        <Style TargetType="TextBlock">
                                                            <!--  默认颜色  -->
                                                            <Setter Property="Foreground" Value="{DynamicResource TextFillColorSecondaryBrush}" />
                                                            <Style.Triggers>
                                                                <!--  选中时变为强调色  -->
                                                                <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType=ListBoxItem}, Path=IsSelected}" Value="True">
                                                                    <Setter Property="Foreground" Value="{DynamicResource SystemAccentColorPrimaryBrush}" />
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </TextBlock.Style>
                                                </TextBlock>

                                                <!--  专业名称  -->
                                                <TextBlock
                                                    FontSize="14"
                                                    FontWeight="SemiBold"
                                                    Text="{Binding Name}"
                                                    VerticalAlignment="Center">
                                                    <TextBlock.Style>
                                                        <Style TargetType="TextBlock">
                                                            <!--  默认颜色  -->
                                                            <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}" />
                                                            <Style.Triggers>
                                                                <!--  选中时变为强调色  -->
                                                                <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType=ListBoxItem}, Path=IsSelected}" Value="True">
                                                                    <Setter Property="Foreground" Value="{DynamicResource SystemAccentColorPrimaryBrush}" />
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </TextBlock.Style>
                                                </TextBlock>
                                            </StackPanel>
                                        </ui:Button>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>
                            </ListBox>
                        </ScrollViewer>
                    </Border>

                    <!--  文件搜索栏  -->
                    <Border
                        Background="{DynamicResource ControlFillColorSecondaryBrush}"
                        BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                        BorderThickness="0,0,0,1"
                        Grid.Row="1"
                        Padding="16,12">
                        <ui:TextBox
                            ClearButtonEnabled="True"
                            Icon="{ui:SymbolIcon Search24}"
                            PlaceholderText="🔍 搜索图纸名称、类型..."
                            Text="{Binding SearchKeyword, UpdateSourceTrigger=PropertyChanged}" />
                    </Border>

                    <Grid Grid.Row="2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition MinWidth="120" Width="120" />
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition MinWidth="500" Width="*" />
                        </Grid.ColumnDefinitions>

                        <!--  左侧：DWG文件类型管理  -->
                        <Border Grid.Column="0" Style="{StaticResource SidebarContainerStyle}">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <!--  搜索框  -->
                                    <RowDefinition Height="*" />
                                    <!--  文件类型列表  -->
                                </Grid.RowDefinitions>


                                <!--  文件类型列表 - 完全匹配 DwgFileTypeEditorView  -->
                                <ui:ListView
                                    Background="Transparent"
                                    BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                    BorderThickness="1"
                                    Grid.Row="1"
                                    ItemsSource="{Binding LDwgFileTypeModels}"
                                    Margin="8,0,8,8"
                                    SelectedItem="{Binding TDwgFileTypeModel}"
                                    dd:DragDrop.DropHandler="{Binding}"
                                    dd:DragDrop.IsDropTarget="True">


                                    <!--  列表项模板  -->
                                    <ListView.ItemTemplate>
                                        <DataTemplate>
                                            <Grid Margin="2">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto" />
                                                    <!--  图标  -->
                                                    <ColumnDefinition Width="*" />
                                                    <!--  名称  -->
                                                    <ColumnDefinition Width="Auto" />
                                                    <!--  状态指示器  -->
                                                </Grid.ColumnDefinitions>

                                                <!--  文件类型图标  -->
                                                <Border
                                                    Grid.Column="0"
                                                    Height="24"
                                                    HorizontalAlignment="Center"
                                                    VerticalAlignment="Center"
                                                    Width="24">
                                                    <TextBlock
                                                        FontSize="16"
                                                        HorizontalAlignment="Center"
                                                        Text="{Binding Icon}"
                                                        TextAlignment="Center"
                                                        VerticalAlignment="Center" />
                                                </Border>

                                                <!--  文件类型名称  -->
                                                <StackPanel
                                                    Grid.Column="1"
                                                    Margin="5,0,0,0"
                                                    VerticalAlignment="Center">
                                                    <TextBlock
                                                        FontSize="14"
                                                        FontWeight="SemiBold"
                                                        Text="{Binding ChineseName}" />
                                                </StackPanel>


                                            </Grid>
                                        </DataTemplate>
                                    </ListView.ItemTemplate>
                                </ui:ListView>
                            </Grid>
                        </Border>

                        <!--  分隔符  -->
                        <GridSplitter
                            Background="Transparent"
                            Grid.Column="1"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Stretch"
                            Width="8" />


                        <!--  文件详细列表  -->
                        <Border Grid.Column="2" Padding="8">
                            <DataGrid
                                AutoGenerateColumns="False"
                                Background="Transparent"
                                BorderThickness="0"
                                CanUserAddRows="False"
                                CanUserDeleteRows="False"
                                GridLinesVisibility="None"
                                HeadersVisibility="None"
                                ItemsSource="{Binding GroupedFilesView.View}"
                                RowHeight="28"
                                ScrollViewer.HorizontalScrollBarVisibility="Hidden"
                                ScrollViewer.PanningMode="Both"
                                ScrollViewer.VerticalScrollBarVisibility="Hidden"
                                SelectedItem="{Binding TDwgFileModel}"
                                dd:DragDrop.DragAdornerTemplate="{StaticResource CustomDragAdornerTemplate}"
                                dd:DragDrop.DragHandler="{Binding}"
                                dd:DragDrop.IsDragSource="True"
                                dd:DragDrop.UseDefaultDragAdorner="False"
                                dd:DragDrop.UseDefaultEffectDataTemplate="False">

                                <!--  双击事件绑定  -->
                                <i:Interaction.Triggers>
                                    <i:EventTrigger EventName="MouseDoubleClick">
                                        <i:InvokeCommandAction Command="{Binding OpenFileOnDoubleClickCommand}" CommandParameter="{Binding SelectedItem, RelativeSource={RelativeSource AncestorType=DataGrid}}" />
                                    </i:EventTrigger>
                                </i:Interaction.Triggers>

                                <!--#region 右键菜单-->
                                <DataGrid.ContextMenu>
                                    <ContextMenu>
                                        <!--  打开文件  -->
                                        <MenuItem
                                            Command="{Binding OpenFileCommand}"
                                            CommandParameter="{Binding PlacementTarget.SelectedItem, RelativeSource={RelativeSource AncestorType=ContextMenu}}"
                                            Header="打开文件">
                                            <MenuItem.Icon>
                                                <ui:SymbolIcon Symbol="Open24" />
                                            </MenuItem.Icon>
                                        </MenuItem>

                                        <Separator />

                                        <!--  文件操作  -->
                                        <MenuItem
                                            Command="{Binding RenameFileCommand}"
                                            CommandParameter="{Binding PlacementTarget.SelectedItem, RelativeSource={RelativeSource AncestorType=ContextMenu}}"
                                            Header="重命名">
                                            <MenuItem.Icon>
                                                <ui:SymbolIcon Symbol="Rename24" />
                                            </MenuItem.Icon>
                                        </MenuItem>

                                        <MenuItem
                                            Command="{Binding CopyFileCommand}"
                                            CommandParameter="{Binding PlacementTarget.SelectedItem, RelativeSource={RelativeSource AncestorType=ContextMenu}}"
                                            Header="复制文件">
                                            <MenuItem.Icon>
                                                <ui:SymbolIcon Symbol="Copy24" />
                                            </MenuItem.Icon>
                                        </MenuItem>

                                        <MenuItem
                                            Command="{Binding DeleteFileCommand}"
                                            CommandParameter="{Binding PlacementTarget.SelectedItem, RelativeSource={RelativeSource AncestorType=ContextMenu}}"
                                            Header="删除文件">
                                            <MenuItem.Icon>
                                                <ui:SymbolIcon Symbol="Delete24" />
                                            </MenuItem.Icon>
                                        </MenuItem>

                                        <Separator />

                                        <!--  复制操作  -->
                                        <MenuItem
                                            Command="{Binding CopyToDesktopCommand}"
                                            CommandParameter="{Binding PlacementTarget.SelectedItem, RelativeSource={RelativeSource AncestorType=ContextMenu}}"
                                            Header="复制到桌面">
                                            <MenuItem.Icon>
                                                <ui:SymbolIcon Symbol="Desktop24" />
                                            </MenuItem.Icon>
                                        </MenuItem>

                                        <MenuItem
                                            Command="{Binding CopyFilePathCommand}"
                                            CommandParameter="{Binding PlacementTarget.SelectedItem, RelativeSource={RelativeSource AncestorType=ContextMenu}}"
                                            Header="复制路径">
                                            <MenuItem.Icon>
                                                <ui:SymbolIcon Symbol="Link24" />
                                            </MenuItem.Icon>
                                        </MenuItem>

                                        <MenuItem
                                            Command="{Binding CopyFileJSCommand}"
                                            CommandParameter="{Binding PlacementTarget.SelectedItem, RelativeSource={RelativeSource AncestorType=ContextMenu}}"
                                            Header="建筑底图">
                                            <MenuItem.Icon>
                                                <ui:SymbolIcon Symbol="Link24" />
                                            </MenuItem.Icon>
                                        </MenuItem>
                                        <Separator />

                                        <!--  创建操作  -->
                                        <MenuItem Command="{Binding CreateNewDwgCommand}" Header="新建DWG文件">
                                            <MenuItem.Icon>
                                                <ui:SymbolIcon Symbol="Add24" />
                                            </MenuItem.Icon>
                                        </MenuItem>

                                        <MenuItem Command="{Binding CreateFromTemplateCommand}" Header="基于模板创建">
                                            <MenuItem.Icon>
                                                <ui:SymbolIcon Symbol="DocumentAdd24" />
                                            </MenuItem.Icon>
                                        </MenuItem>

                                        <Separator />

                                        <!--  高级操作  -->
                                        <MenuItem
                                            Command="{Binding ShowInExplorerCommand}"
                                            CommandParameter="{Binding PlacementTarget.SelectedItem, RelativeSource={RelativeSource AncestorType=ContextMenu}}"
                                            Header="在资源管理器中显示">
                                            <MenuItem.Icon>
                                                <ui:SymbolIcon Symbol="Folder24" />
                                            </MenuItem.Icon>
                                        </MenuItem>

                                        <MenuItem
                                            Command="{Binding ShowFilePropertiesCommand}"
                                            CommandParameter="{Binding PlacementTarget.SelectedItem, RelativeSource={RelativeSource AncestorType=ContextMenu}}"
                                            Header="属性">
                                            <MenuItem.Icon>
                                                <ui:SymbolIcon Symbol="Info24" />
                                            </MenuItem.Icon>
                                        </MenuItem>
                                    </ContextMenu>
                                </DataGrid.ContextMenu>
                                <!--#endregion-->

                                <!--  分组样式  -->
                                <DataGrid.GroupStyle>
                                    <GroupStyle>
                                        <GroupStyle.HeaderTemplate>
                                            <DataTemplate>
                                                <Border
                                                    Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                                    BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                                    BorderThickness="0,0,0,1"
                                                    Margin="-12,0"
                                                    Padding="12,6"
                                                    d:DataContext="{d:DesignInstance Type=CollectionViewGroup}">
                                                    <Grid>
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="*" />
                                                            <ColumnDefinition Width="Auto" />
                                                        </Grid.ColumnDefinitions>

                                                        <StackPanel Grid.Column="0" Orientation="Horizontal">
                                                            <TextBlock
                                                                FontSize="14"
                                                                Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                                                Margin="0,0,8,0"
                                                                Text="📋"
                                                                VerticalAlignment="Center" />
                                                            <!--  Name 是 CollectionViewGroup 的标准属性  -->
                                                            <TextBlock
                                                                FontSize="13"
                                                                FontWeight="SemiBold"
                                                                Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                                                Text="{Binding Name}"
                                                                VerticalAlignment="Center" />
                                                        </StackPanel>

                                                        <!--  ItemCount 是 CollectionViewGroup 的标准属性  -->
                                                        <TextBlock
                                                            FontSize="11"
                                                            Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                            Grid.Column="1"
                                                            Text="{Binding ItemCount, StringFormat='{}{0} 个'}"
                                                            VerticalAlignment="Center" />
                                                    </Grid>
                                                </Border>
                                            </DataTemplate>
                                        </GroupStyle.HeaderTemplate>
                                    </GroupStyle>
                                </DataGrid.GroupStyle>

                                <DataGrid.Columns>
                                    <!--  文件信息  -->
                                    <DataGridTemplateColumn Width="*">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <Grid Margin="8,4" d:DataContext="{d:DesignInstance Type=dwg:DwgFileModel}">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*" />
                                                        <ColumnDefinition Width="80" />
                                                        <ColumnDefinition Width="120" />
                                                    </Grid.ColumnDefinitions>

                                                    <!--  文件名  -->
                                                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                                                        <TextBlock
                                                            FontSize="14"
                                                            Margin="0,0,8,0"
                                                            Text="{Binding DrawingIcon}"
                                                            VerticalAlignment="Center" />
                                                        <TextBlock
                                                            FontSize="12"
                                                            Text="{Binding FileName}"
                                                            VerticalAlignment="Center" />
                                                    </StackPanel>

                                                    <!--  文件大小  -->
                                                    <TextBlock
                                                        FontSize="11"
                                                        Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                        Grid.Column="1"
                                                        HorizontalAlignment="Right"
                                                        Text="{Binding FileSizeText}"
                                                        VerticalAlignment="Center" />

                                                    <!--  修改时间  -->
                                                    <TextBlock
                                                        FontSize="11"
                                                        Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                        Grid.Column="2"
                                                        HorizontalAlignment="Right"
                                                        Text="{Binding ModifiedDate, StringFormat=yyyy-MM-dd HH:mm}"
                                                        VerticalAlignment="Center" />
                                                </Grid>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Border>
                    </Grid>

                </Grid>
            </Border>
        </Grid>


        <!--  状态栏  -->
        <Border
            Background="{DynamicResource ControlFillColorDefaultBrush}"
            BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
            BorderThickness="0,1,0,0"
            Grid.Row="2"
            Margin="0,4,0,0"
            Padding="2,2">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <TextBlock
                    FontSize="12"
                    Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                    Grid.Column="0"
                    Text="{Binding StatusMessage}"
                    VerticalAlignment="Center" />

                <StackPanel Grid.Column="1" Orientation="Horizontal">


                    <!--  文件类型统计  -->
                    <TextBlock
                        FontSize="11"
                        Foreground="{DynamicResource TextFillColorTertiaryBrush}"
                        Margin="0,0,5,0"
                        Text="{Binding CountFileType}"
                        VerticalAlignment="Center" />

                    <!--  文件总数统计  -->
                    <TextBlock
                        FontSize="11"
                        Foreground="{DynamicResource TextFillColorTertiaryBrush}"
                        Text="{Binding CountFile}"
                        VerticalAlignment="Center" />
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>