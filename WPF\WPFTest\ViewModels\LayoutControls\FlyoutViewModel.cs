using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Windows;
using System.IO;
using System.Reflection;
using System.Collections.ObjectModel;
using System.ComponentModel;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels.LayoutControls
{
    /// <summary>
    /// Flyout 页面的 ViewModel，演示 WPF-UI Flyout 控件的各种功能
    /// </summary>
    public partial class FlyoutViewModel : ObservableObject
    {
        private readonly YLoggerInstance _logger = YLogger.For<FlyoutViewModel>();

        #region 属性

        /// <summary>
        /// 交互次数
        /// </summary>
        [ObservableProperty]
        private int interactionCount = 0;

        /// <summary>
        /// 最后执行的操作
        /// </summary>
        [ObservableProperty]
        private string lastAction = "无操作";

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        private string statusMessage = "欢迎使用 WPF-UI Flyout 示例库！";

        #region Flyout 开关状态

        /// <summary>
        /// 简单 Flyout 是否打开
        /// </summary>
        [ObservableProperty]
        private bool isSimpleFlyoutOpen = false;

        /// <summary>
        /// 信息 Flyout 是否打开
        /// </summary>
        [ObservableProperty]
        private bool isInfoFlyoutOpen = false;

        /// <summary>
        /// 顶部 Flyout 是否打开
        /// </summary>
        [ObservableProperty]
        private bool isTopFlyoutOpen = false;

        /// <summary>
        /// 左侧 Flyout 是否打开
        /// </summary>
        [ObservableProperty]
        private bool isLeftFlyoutOpen = false;

        /// <summary>
        /// 中心 Flyout 是否打开
        /// </summary>
        [ObservableProperty]
        private bool isCenterFlyoutOpen = false;

        /// <summary>
        /// 右侧 Flyout 是否打开
        /// </summary>
        [ObservableProperty]
        private bool isRightFlyoutOpen = false;

        /// <summary>
        /// 底部 Flyout 是否打开
        /// </summary>
        [ObservableProperty]
        private bool isBottomFlyoutOpen = false;

        /// <summary>
        /// 菜单 Flyout 是否打开
        /// </summary>
        [ObservableProperty]
        private bool isMenuFlyoutOpen = false;

        #endregion

        #region 代码示例属性

        /// <summary>
        /// 基础功能 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 基础功能 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级功能 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级功能 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 样式 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string StylesXamlExample { get; set; } = string.Empty;

        #endregion

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化 FlyoutViewModel
        /// </summary>
        public FlyoutViewModel()
        {
            try
            {
                _logger.Info("🚀 Flyout 页面 ViewModel 开始初始化");

                StatusMessage = "WPF-UI Flyout 示例库已加载，开始体验浮出面板功能！";
                InitializeCodeExamples();

                _logger.Info("✅ Flyout 页面 ViewModel 初始化完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ Flyout 页面 ViewModel 初始化失败: {ex.Message}");
                StatusMessage = "初始化失败，请检查日志";
            }
        }

        #endregion

        #region 命令

        /// <summary>
        /// 通用交互命令
        /// </summary>
        /// <param name="parameter">操作参数</param>
        [RelayCommand]
        private void HandleInteraction(string? parameter)
        {
            try
            {
                InteractionCount++;
                LastAction = parameter ?? "未知操作";
                
                _logger.Info($"🎯 用户交互操作: {parameter}");
                _logger.Info($"🔢 当前交互次数: {InteractionCount}");

                // 根据不同的操作显示不同的状态消息
                StatusMessage = parameter switch
                {
                    "关闭简单Flyout" => HandleCloseFlyout("简单", () => IsSimpleFlyoutOpen = false),
                    "确认信息" => HandleCloseFlyout("信息", () => IsInfoFlyoutOpen = false),
                    "菜单-新建" => HandleMenuAction("新建"),
                    "菜单-编辑" => HandleMenuAction("编辑"),
                    "菜单-删除" => HandleMenuAction("删除"),
                    "菜单-设置" => HandleMenuAction("设置"),
                    _ => $"🎯 执行了操作: {parameter}"
                };
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 处理用户交互失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示简单 Flyout 命令
        /// </summary>
        [RelayCommand]
        private void ShowSimpleFlyout()
        {
            InteractionCount++;
            LastAction = "显示简单Flyout";
            StatusMessage = "🎯 显示了简单 Flyout";
            IsSimpleFlyoutOpen = !IsSimpleFlyoutOpen;
            _logger.Info("显示简单 Flyout");
        }

        /// <summary>
        /// 显示信息 Flyout 命令
        /// </summary>
        [RelayCommand]
        private void ShowInfoFlyout()
        {
            InteractionCount++;
            LastAction = "显示信息Flyout";
            StatusMessage = "🎯 显示了信息 Flyout";
            IsInfoFlyoutOpen = !IsInfoFlyoutOpen;
            _logger.Info("显示信息 Flyout");
        }

        /// <summary>
        /// 显示顶部 Flyout 命令
        /// </summary>
        [RelayCommand]
        private void ShowTopFlyout()
        {
            InteractionCount++;
            LastAction = "显示顶部Flyout";
            StatusMessage = "🎯 显示了顶部 Flyout";
            IsTopFlyoutOpen = !IsTopFlyoutOpen;
            _logger.Info("显示顶部 Flyout");
        }

        /// <summary>
        /// 显示左侧 Flyout 命令
        /// </summary>
        [RelayCommand]
        private void ShowLeftFlyout()
        {
            InteractionCount++;
            LastAction = "显示左侧Flyout";
            StatusMessage = "🎯 显示了左侧 Flyout";
            IsLeftFlyoutOpen = !IsLeftFlyoutOpen;
            _logger.Info("显示左侧 Flyout");
        }

        /// <summary>
        /// 显示中心 Flyout 命令
        /// </summary>
        [RelayCommand]
        private void ShowCenterFlyout()
        {
            InteractionCount++;
            LastAction = "显示中心Flyout";
            StatusMessage = "🎯 显示了中心 Flyout";
            IsCenterFlyoutOpen = !IsCenterFlyoutOpen;
            _logger.Info("显示中心 Flyout");
        }

        /// <summary>
        /// 显示右侧 Flyout 命令
        /// </summary>
        [RelayCommand]
        private void ShowRightFlyout()
        {
            InteractionCount++;
            LastAction = "显示右侧Flyout";
            StatusMessage = "🎯 显示了右侧 Flyout";
            IsRightFlyoutOpen = !IsRightFlyoutOpen;
            _logger.Info("显示右侧 Flyout");
        }

        /// <summary>
        /// 显示底部 Flyout 命令
        /// </summary>
        [RelayCommand]
        private void ShowBottomFlyout()
        {
            InteractionCount++;
            LastAction = "显示底部Flyout";
            StatusMessage = "🎯 显示了底部 Flyout";
            IsBottomFlyoutOpen = !IsBottomFlyoutOpen;
            _logger.Info("显示底部 Flyout");
        }

        /// <summary>
        /// 显示菜单 Flyout 命令
        /// </summary>
        [RelayCommand]
        private void ShowMenuFlyout()
        {
            InteractionCount++;
            LastAction = "显示菜单Flyout";
            StatusMessage = "🎯 显示了菜单 Flyout";
            IsMenuFlyoutOpen = !IsMenuFlyoutOpen;
            _logger.Info("显示菜单 Flyout");
        }

        /// <summary>
        /// 重置计数命令
        /// </summary>
        [RelayCommand]
        private void ResetCount()
        {
            InteractionCount = 0;
            StatusMessage = "计数已重置";
            _logger.Info("重置了交互计数");
        }

        /// <summary>
        /// 清除状态命令
        /// </summary>
        [RelayCommand]
        private void ClearStatus()
        {
            LastAction = "无操作";
            StatusMessage = "状态已清除";
            _logger.Info("清除了状态信息");
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 处理关闭 Flyout 操作
        /// </summary>
        /// <param name="flyoutName">Flyout 名称</param>
        /// <param name="closeAction">关闭操作</param>
        /// <returns>状态消息</returns>
        private string HandleCloseFlyout(string flyoutName, Action closeAction)
        {
            closeAction();
            return $"🎯 关闭了{flyoutName} Flyout";
        }

        /// <summary>
        /// 处理菜单操作
        /// </summary>
        /// <param name="action">操作名称</param>
        /// <returns>状态消息</returns>
        private string HandleMenuAction(string action)
        {
            IsMenuFlyoutOpen = false; // 关闭菜单
            return $"🎯 从菜单选择了{action}";
        }

        /// <summary>
        /// 初始化代码示例
        /// </summary>
        private void InitializeCodeExamples()
        {
            try
            {
                // 获取代码示例文件的基础路径
                var baseDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
                var codeExamplesPath = Path.Combine(baseDirectory, "CodeExamples", "LayoutControls", "Flyout");

                _logger.Info($"基础目录: {baseDirectory}");
                _logger.Info($"代码示例路径: {codeExamplesPath}");
                _logger.Info($"目录是否存在: {Directory.Exists(codeExamplesPath)}");

                // 读取各种示例文件
                BasicXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.xaml.txt"));
                BasicCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.cs.txt"));
                AdvancedXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Advanced.xaml.txt"));
                AdvancedCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Advanced.cs.txt"));
                StylesXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Styles.xaml.txt"));

                _logger.Info("Flyout 代码示例加载完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"加载 Flyout 代码示例失败: {ex.Message}");
                SetDefaultCodeExamples();
            }
        }

        /// <summary>
        /// 读取代码示例文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件内容</returns>
        private string ReadCodeExampleFile(string filePath)
        {
            _logger.Info($"尝试读取文件: {filePath}");
            
            if (File.Exists(filePath))
            {
                var content = File.ReadAllText(filePath);
                _logger.Info($"成功读取文件，内容长度: {content.Length}");
                return content;
            }
            else
            {
                _logger.Warning($"代码示例文件不存在: {filePath}");
                return $"// 代码示例文件不存在: {Path.GetFileName(filePath)}\n// 完整路径: {filePath}";
            }
        }

        /// <summary>
        /// 设置默认代码示例（当文件读取失败时使用）
        /// </summary>
        private void SetDefaultCodeExamples()
        {
            BasicXamlExample = @"<!-- WPF-UI Flyout 基础示例 -->
<ui:Button Content=""显示简单 Flyout"">
    <ui:Button.Flyout>
        <ui:Flyout>
            <StackPanel Margin=""16"">
                <TextBlock Text=""这是一个简单的 Flyout"" 
                           FontWeight=""Medium"" 
                           Margin=""0,0,0,8""/>
                <TextBlock Text=""Flyout 提供了轻量级的浮出面板功能。"" 
                           TextWrapping=""Wrap""
                           Margin=""0,0,0,12""/>
                <Button Content=""关闭"" 
                        HorizontalAlignment=""Left""/>
            </StackPanel>
        </ui:Flyout>
    </ui:Button.Flyout>
</ui:Button>";

            BasicCSharpExample = @"// WPF-UI Flyout C# 基础示例
using Wpf.Ui.Controls;

// 创建带 Flyout 的按钮
var button = new Button
{
    Content = ""显示简单 Flyout"",
    Margin = new Thickness(8)
};

// 创建 Flyout
var flyout = new Flyout();

var content = new StackPanel
{
    Margin = new Thickness(16)
};

content.Children.Add(new TextBlock 
{ 
    Text = ""这是一个简单的 Flyout"",
    FontWeight = FontWeights.Medium,
    Margin = new Thickness(0, 0, 0, 8)
});

content.Children.Add(new TextBlock 
{ 
    Text = ""Flyout 提供了轻量级的浮出面板功能。"",
    TextWrapping = TextWrapping.Wrap,
    Margin = new Thickness(0, 0, 0, 12)
});

content.Children.Add(new Button 
{ 
    Content = ""关闭"",
    HorizontalAlignment = HorizontalAlignment.Left
});

flyout.Content = content;
button.Flyout = flyout;";

            AdvancedXamlExample = @"<!-- WPF-UI Flyout 高级示例 -->
<!-- 菜单类型的 Flyout -->
<ui:Button Content=""操作菜单"">
    <ui:Button.Flyout>
        <ui:Flyout>
            <StackPanel Margin=""8"" MinWidth=""150"">
                <ui:Button Content=""新建"" 
                           HorizontalAlignment=""Stretch""
                           HorizontalContentAlignment=""Left""
                           Appearance=""Transparent""
                           Margin=""0,0,0,4""/>
                <ui:Button Content=""编辑"" 
                           HorizontalAlignment=""Stretch""
                           HorizontalContentAlignment=""Left""
                           Appearance=""Transparent""
                           Margin=""0,0,0,4""/>
                <ui:Button Content=""删除"" 
                           HorizontalAlignment=""Stretch""
                           HorizontalContentAlignment=""Left""
                           Appearance=""Transparent""
                           Margin=""0,0,0,4""/>
                <Separator Margin=""0,4,0,4""/>
                <ui:Button Content=""设置"" 
                           HorizontalAlignment=""Stretch""
                           HorizontalContentAlignment=""Left""
                           Appearance=""Transparent""/>
            </StackPanel>
        </ui:Flyout>
    </ui:Button.Flyout>
</ui:Button>";

            AdvancedCSharpExample = @"// WPF-UI Flyout 高级 C# 示例
// 创建菜单类型的 Flyout
public static Button CreateMenuFlyoutButton()
{
    var button = new Button
    {
        Content = ""操作菜单"",
        Margin = new Thickness(8)
    };
    
    var flyout = new Flyout();
    var menuPanel = new StackPanel
    {
        Margin = new Thickness(8),
        MinWidth = 150
    };
    
    // 添加菜单项
    var menuItems = new[] { ""新建"", ""编辑"", ""删除"", ""设置"" };
    
    for (int i = 0; i < menuItems.Length; i++)
    {
        if (i == 3) // 在设置前添加分隔符
        {
            menuPanel.Children.Add(new Separator
            {
                Margin = new Thickness(0, 4, 0, 4)
            });
        }
        
        var menuButton = new Button
        {
            Content = menuItems[i],
            HorizontalAlignment = HorizontalAlignment.Stretch,
            HorizontalContentAlignment = HorizontalAlignment.Left,
            Appearance = ControlAppearance.Transparent,
            Margin = new Thickness(0, 0, 0, 4)
        };
        
        menuPanel.Children.Add(menuButton);
    }
    
    flyout.Content = menuPanel;
    button.Flyout = flyout;
    
    return button;
}";

            StylesXamlExample = @"<!-- WPF-UI Flyout 样式示例 -->
<!-- 不同位置的 Flyout -->
<ui:Button Content=""顶部"">
    <ui:Button.Flyout>
        <ui:Flyout Placement=""Top"">
            <StackPanel Margin=""12"">
                <TextBlock Text=""顶部 Flyout"" FontWeight=""Medium""/>
                <TextBlock Text=""显示在按钮上方"" FontSize=""12""/>
            </StackPanel>
        </ui:Flyout>
    </ui:Button.Flyout>
</ui:Button>

<ui:Button Content=""底部"">
    <ui:Button.Flyout>
        <ui:Flyout Placement=""Bottom"">
            <StackPanel Margin=""12"">
                <TextBlock Text=""底部 Flyout"" FontWeight=""Medium""/>
                <TextBlock Text=""显示在按钮下方"" FontSize=""12""/>
            </StackPanel>
        </ui:Flyout>
    </ui:Button.Flyout>
</ui:Button>

<ui:Button Content=""左侧"">
    <ui:Button.Flyout>
        <ui:Flyout Placement=""Left"">
            <StackPanel Margin=""12"">
                <TextBlock Text=""左侧 Flyout"" FontWeight=""Medium""/>
                <TextBlock Text=""显示在按钮左侧"" FontSize=""12""/>
            </StackPanel>
        </ui:Flyout>
    </ui:Button.Flyout>
</ui:Button>

<ui:Button Content=""右侧"">
    <ui:Button.Flyout>
        <ui:Flyout Placement=""Right"">
            <StackPanel Margin=""12"">
                <TextBlock Text=""右侧 Flyout"" FontWeight=""Medium""/>
                <TextBlock Text=""显示在按钮右侧"" FontSize=""12""/>
            </StackPanel>
        </ui:Flyout>
    </ui:Button.Flyout>
</ui:Button>";
        }

        #endregion
    }
}
