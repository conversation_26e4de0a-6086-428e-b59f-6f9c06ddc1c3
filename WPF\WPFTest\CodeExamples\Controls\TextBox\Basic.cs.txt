// TextBox C# 基础用法示例
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace WPFTest.ViewModels.InputControls
{
    public partial class TextBoxPageViewModel : ObservableObject
    {
        /// <summary>
        /// 标准 TextBox 的值
        /// </summary>
        [ObservableProperty]
        private string standardTextValue = "";

        /// <summary>
        /// 小型 TextBox 的值
        /// </summary>
        [ObservableProperty]
        private string smallTextValue = "";

        /// <summary>
        /// 大型 TextBox 的值
        /// </summary>
        [ObservableProperty]
        private string largeTextValue = "";

        /// <summary>
        /// TextBox 交互命令
        /// </summary>
        [RelayCommand]
        private void HandleTextBoxInteraction(string parameter)
        {
            var message = parameter switch
            {
                "标准输入" => "🎯 标准 TextBox 交互",
                "小型输入" => "🎯 小型 TextBox 交互",
                "大型输入" => "🎯 大型 TextBox 交互",
                _ => $"🔘 执行了操作: {parameter}"
            };

            StatusMessage = message;
            InteractionCount++;
        }

        /// <summary>
        /// 文本验证示例
        /// </summary>
        private bool ValidateText(string text)
        {
            // 基础验证逻辑
            if (string.IsNullOrWhiteSpace(text))
                return false;
            
            if (text.Length < 3)
                return false;
            
            return true;
        }

        /// <summary>
        /// 文本格式化示例
        /// </summary>
        private string FormatText(string text)
        {
            if (string.IsNullOrEmpty(text))
                return text;
            
            // 移除多余空格
            return text.Trim().Replace("  ", " ");
        }

        /// <summary>
        /// 清除所有文本框内容
        /// </summary>
        [RelayCommand]
        private void ClearAllText()
        {
            StandardTextValue = "";
            SmallTextValue = "";
            LargeTextValue = "";
            StatusMessage = "所有文本框已清空";
        }
    }
}
