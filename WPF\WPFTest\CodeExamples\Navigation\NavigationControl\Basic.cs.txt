using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Zylo.WPF.Models.Navigation;
using Wpf.Ui.Controls;
using Prism.Regions;
using System.Linq;

namespace WPFTest.ViewModels.Navigation
{
    /// <summary>
    /// NavigationControl 基础功能示例 ViewModel
    /// </summary>
    public partial class NavigationBasicExampleViewModel : ObservableObject
    {
        private readonly IRegionManager _regionManager;

        /// <summary>
        /// 导航项集合
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<NavigationItemModel> navigationItems = new();

        /// <summary>
        /// 当前选中的导航项
        /// </summary>
        [ObservableProperty]
        private NavigationItemModel? selectedNavigationItem;

        /// <summary>
        /// 搜索文本
        /// </summary>
        [ObservableProperty]
        private string searchText = string.Empty;

        /// <summary>
        /// 导航是否折叠
        /// </summary>
        [ObservableProperty]
        private bool isNavigationCollapsed = false;

        /// <summary>
        /// 构造函数
        /// </summary>
        public NavigationBasicExampleViewModel(IRegionManager regionManager)
        {
            _regionManager = regionManager;
            InitializeNavigationItems();
        }

        /// <summary>
        /// 导航命令
        /// </summary>
        [RelayCommand]
        private void Navigation(NavigationItemModel? item)
        {
            if (item == null) return;

            try
            {
                // 更新选中项
                SelectedNavigationItem = item;

                // 执行导航逻辑
                if (!string.IsNullOrEmpty(item.NavigationParameter))
                {
                    _regionManager.RequestNavigate("ContentRegion", item.NavigationParameter);
                }

                // 记录导航日志
                System.Diagnostics.Debug.WriteLine($"导航到: {item.Title} ({item.NavigationParameter})");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"导航失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 切换搜索功能
        /// </summary>
        [RelayCommand]
        private void ToggleSearch()
        {
            // 清空搜索文本
            SearchText = string.Empty;
            
            // 这里可以添加切换搜索可见性的逻辑
            System.Diagnostics.Debug.WriteLine("切换搜索功能");
        }

        /// <summary>
        /// 展开所有项
        /// </summary>
        [RelayCommand]
        private void ExpandAll()
        {
            foreach (var item in NavigationItems)
            {
                ExpandItemRecursively(item);
            }
        }

        /// <summary>
        /// 折叠所有项
        /// </summary>
        [RelayCommand]
        private void CollapseAll()
        {
            foreach (var item in NavigationItems)
            {
                CollapseItemRecursively(item);
            }
        }

        /// <summary>
        /// 刷新数据
        /// </summary>
        [RelayCommand]
        private void RefreshData()
        {
            InitializeNavigationItems();
            System.Diagnostics.Debug.WriteLine("数据已刷新");
        }

        /// <summary>
        /// 初始化导航项
        /// </summary>
        private void InitializeNavigationItems()
        {
            NavigationItems.Clear();

            var items = new[]
            {
                new NavigationItemModel
                {
                    Title = "首页",
                    Icon = "Home",
                    NavigationParameter = "HomeView",
                    Children = new ObservableCollection<NavigationItemModel>()
                },
                new NavigationItemModel
                {
                    Title = "控件示例",
                    Icon = "Apps",
                    Children = new ObservableCollection<NavigationItemModel>
                    {
                        new NavigationItemModel
                        {
                            Title = "按钮控件",
                            Icon = "TouchApp",
                            NavigationParameter = "ButtonExampleView"
                        },
                        new NavigationItemModel
                        {
                            Title = "输入控件",
                            Icon = "Edit",
                            NavigationParameter = "InputExampleView"
                        },
                        new NavigationItemModel
                        {
                            Title = "列表控件",
                            Icon = "List",
                            NavigationParameter = "ListExampleView"
                        }
                    }
                },
                new NavigationItemModel
                {
                    Title = "导航示例",
                    Icon = "Navigation",
                    Children = new ObservableCollection<NavigationItemModel>
                    {
                        new NavigationItemModel
                        {
                            Title = "基础导航",
                            Icon = "ArrowForward",
                            NavigationParameter = "NavigationBasicExampleView"
                        },
                        new NavigationItemModel
                        {
                            Title = "高级导航",
                            Icon = "Settings",
                            NavigationParameter = "NavigationAdvancedExampleView"
                        },
                        new NavigationItemModel
                        {
                            Title = "面包屑导航",
                            Icon = "Breadcrumb",
                            NavigationParameter = "BreadcrumbBarExampleView"
                        }
                    }
                },
                new NavigationItemModel
                {
                    Title = "设置",
                    Icon = "Settings",
                    NavigationParameter = "SettingsView",
                    Children = new ObservableCollection<NavigationItemModel>()
                }
            };

            foreach (var item in items)
            {
                NavigationItems.Add(item);
            }

            // 默认选中第一项
            if (NavigationItems.Any())
            {
                SelectedNavigationItem = NavigationItems.First();
            }
        }

        /// <summary>
        /// 递归展开项
        /// </summary>
        private void ExpandItemRecursively(NavigationItemModel item)
        {
            item.IsExpanded = true;
            if (item.Children != null)
            {
                foreach (var child in item.Children)
                {
                    ExpandItemRecursively(child);
                }
            }
        }

        /// <summary>
        /// 递归折叠项
        /// </summary>
        private void CollapseItemRecursively(NavigationItemModel item)
        {
            item.IsExpanded = false;
            if (item.Children != null)
            {
                foreach (var child in item.Children)
                {
                    CollapseItemRecursively(child);
                }
            }
        }
    }
}
