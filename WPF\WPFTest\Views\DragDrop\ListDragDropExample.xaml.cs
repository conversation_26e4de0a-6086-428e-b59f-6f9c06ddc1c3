using System.Windows.Controls;
using Zylo.YLog.Runtime;

namespace WPFTest.Views.DragDrop
{
    /// <summary>
    /// ListDragDropExample.xaml 的交互逻辑
    /// </summary>
    public partial class ListDragDropExample : UserControl
    {
        private readonly YLoggerInstance _logger = YLogger.ForSilent<ListDragDropExample>();

        public ListDragDropExample()
        {
            InitializeComponent();
            _logger.Info("🎯 ListDragDropExample 初始化完成");
        }
    }
}
