# 重命名扩展名处理改进

## 🎯 问题描述
在DWG文件重命名时，用户可能输入包含或不包含`.dwg`扩展名的文件名，需要确保最终文件名有且仅有一个正确的`.dwg`扩展名。

### 可能出现的问题
```
用户输入: "新文件名.dwg"
旧逻辑结果: "新文件名.dwg.dwg"  ❌
期望结果: "新文件名.dwg"        ✅
```

## 🔧 解决方案：先去除再添加

### 核心思路
1. **先去除** - 如果用户输入已包含目标扩展名，先去除
2. **再添加** - 统一添加正确的扩展名
3. **多重保证** - 避免出现重复扩展名

### 实现逻辑
```csharp
// 确保新文件名有正确的扩展名 - 先去除再添加，避免重复
var originalExtension = Path.GetExtension(fileModel.FullPath);
if (!string.IsNullOrEmpty(originalExtension))
{
    // 如果用户输入的文件名已经包含该扩展名，先去除
    if (newName.EndsWith(originalExtension, StringComparison.OrdinalIgnoreCase))
    {
        newName = newName.Substring(0, newName.Length - originalExtension.Length);
    }
    
    // 然后统一添加正确的扩展名
    newName += originalExtension;
}
```

## 📋 测试用例

### 基本情况
| 用户输入 | 原始扩展名 | 处理结果 | 状态 |
|----------|------------|----------|------|
| `新文件名` | `.dwg` | `新文件名.dwg` | ✅ |
| `新文件名.dwg` | `.dwg` | `新文件名.dwg` | ✅ |
| `新文件名.DWG` | `.dwg` | `新文件名.dwg` | ✅ |
| `新文件名.txt` | `.dwg` | `新文件名.txt.dwg` | ✅ |

### 复杂情况
| 用户输入 | 原始扩展名 | 处理结果 | 说明 |
|----------|------------|----------|------|
| `新文件名.dwg.dwg` | `.dwg` | `新文件名.dwg` | 去除重复扩展名 |
| `包含.点号.的文件名` | `.dwg` | `包含.点号.的文件名.dwg` | 正确处理多点号 |
| `包含.点号.的文件名.dwg` | `.dwg` | `包含.点号.的文件名.dwg` | 保持正确格式 |

## 🎨 用户体验改进

### 旧逻辑的问题
```csharp
// 旧逻辑：只检查是否以扩展名结尾
if (!newName.EndsWith(originalExtension, StringComparison.OrdinalIgnoreCase))
{
    newName += originalExtension;
}

// 问题：
// 输入 "文件名.dwg" → 结果 "文件名.dwg" ✅
// 输入 "文件名.DWG" → 结果 "文件名.DWG.dwg" ❌ (大小写敏感问题)
```

### 新逻辑的优势
```csharp
// 新逻辑：先去除再添加
if (newName.EndsWith(originalExtension, StringComparison.OrdinalIgnoreCase))
{
    newName = newName.Substring(0, newName.Length - originalExtension.Length);
}
newName += originalExtension;

// 优势：
// 输入 "文件名.dwg" → 结果 "文件名.dwg" ✅
// 输入 "文件名.DWG" → 结果 "文件名.dwg" ✅ (统一格式)
// 输入 "文件名.dwg.dwg" → 结果 "文件名.dwg" ✅ (避免重复)
```

## 🔍 实现位置

### 1. 重命名对话框 (RenameFileDialog.xaml.cs)
```csharp
private void ConfirmRename()
{
    var newName = FileNameTextBox.Text?.Trim();
    
    // ... 其他验证 ...
    
    // 确保有正确的扩展名 - 先去除再添加，避免重复
    var originalExtension = Path.GetExtension(_originalFileName);
    if (!string.IsNullOrEmpty(originalExtension))
    {
        if (newName.EndsWith(originalExtension, StringComparison.OrdinalIgnoreCase))
        {
            newName = newName.Substring(0, newName.Length - originalExtension.Length);
        }
        newName += originalExtension;
    }
    
    NewFileName = newName;
}
```

### 2. ViewModel重命名方法 (DwgManagerTabViewModel.cs)
```csharp
private async Task RenameFileAsync(DwgFileModel? fileModel)
{
    var newName = await ShowRenameDialogAsync(fileModel.FileName);
    
    // 确保新文件名有正确的扩展名 - 先去除再添加，避免重复
    var originalExtension = Path.GetExtension(fileModel.FullPath);
    if (!string.IsNullOrEmpty(originalExtension))
    {
        if (newName.EndsWith(originalExtension, StringComparison.OrdinalIgnoreCase))
        {
            newName = newName.Substring(0, newName.Length - originalExtension.Length);
        }
        newName += originalExtension;
    }
    
    // ... 执行重命名 ...
}
```

## 🎯 特殊情况处理

### 大小写不敏感
```
输入: "文件名.DWG"
处理: 去除 ".DWG" → "文件名"
结果: "文件名.dwg"
```

### 多重扩展名
```
输入: "文件名.dwg.dwg"
处理: 去除最后的 ".dwg" → "文件名.dwg"
结果: "文件名.dwg"
```

### 其他扩展名
```
输入: "文件名.txt"
处理: 不匹配 ".dwg"，不去除
结果: "文件名.txt.dwg"
```

## 📊 改进效果

### 避免的问题
1. **重复扩展名** - `文件名.dwg.dwg`
2. **大小写混乱** - `文件名.DWG.dwg`
3. **格式不统一** - 各种扩展名格式混合

### 提升的体验
1. **智能处理** - 用户无需担心扩展名问题
2. **格式统一** - 所有文件都有正确的小写`.dwg`扩展名
3. **容错性强** - 处理各种用户输入情况

## 🔄 日志记录

### 详细的处理日志
```
[INFO] 📎 去除现有扩展名: 新文件名
[INFO] 📎 添加正确扩展名: 新文件名.dwg
```

这样可以清楚地看到扩展名的处理过程，便于调试和用户理解。

## 📝 最佳实践

### 用户指导
1. **可以输入任何格式** - 系统会自动处理扩展名
2. **不用担心重复** - 系统会避免`.dwg.dwg`的情况
3. **大小写无关** - `.DWG`和`.dwg`都会被正确处理

### 开发建议
1. **双重保证** - 对话框和ViewModel都有扩展名处理
2. **日志记录** - 记录扩展名处理过程
3. **测试覆盖** - 测试各种边界情况

通过这个改进，重命名功能现在能够智能处理各种用户输入，确保文件名格式的正确性和一致性。
