# ZyloSnackbar 使用指南

## 🎯 概述

ZyloSnackbar 是一个现代化的自定义 Snackbar 控件，采用主题适应性卡片设计，支持依赖属性、状态指示和流畅动画效果。

## ✨ 主要特性

- ✅ **主题适应性设计** - 自动适应浅色/深色主题
- ✅ **卡片式外观** - 现代化的卡片设计，类似系统通知
- ✅ **状态指示器** - 左侧彩色条形指示器表示消息类型
- ✅ **完整的依赖属性支持** - Title, Message, Icon, Appearance, IsShown, Timeout
- ✅ **流畅动画效果** - 滑入滑出动画，支持缓动函数
- ✅ **自动隐藏功能** - 可配置超时时间，支持手动关闭
- ✅ **多种消息类型** - Success, Error, Warning, Info, Primary
- ✅ **高度可定制** - 支持自定义背景色、图标、内容模板

## 🚀 基本用法

### **XAML 声明**

```xml
<UserControl xmlns:zylo="clr-namespace:Zylo.WPF.Controls;assembly=Zylo.WPF">
    <Grid>
        <!-- ZyloSnackbar 控件 -->
        <zylo:ZyloSnackbar x:Name="MainSnackbar"
                           Title="{Binding SnackbarTitle}"
                           Message="{Binding SnackbarMessage}"
                           Icon="{Binding SnackbarIcon}"
                           Appearance="{Binding SnackbarAppearance}"
                           IsShown="{Binding IsSnackbarVisible}"
                           Timeout="{Binding SnackbarTimeout}"
                           UseAccentColor="True"
                           ShowCloseButton="True"/>
    </Grid>
</UserControl>
```

### **ViewModel 绑定**

```csharp
public partial class MainViewModel : ObservableObject
{
    [ObservableProperty] private string snackbarTitle = string.Empty;
    [ObservableProperty] private string snackbarMessage = string.Empty;
    [ObservableProperty] private SymbolRegular snackbarIcon = SymbolRegular.Info24;
    [ObservableProperty] private ControlAppearance snackbarAppearance = ControlAppearance.Primary;
    [ObservableProperty] private bool isSnackbarVisible = false;
    [ObservableProperty] private int snackbarTimeout = 3000;

    [RelayCommand]
    private void ShowSuccess()
    {
        SnackbarTitle = "操作成功";
        SnackbarMessage = "文件已成功保存";
        SnackbarIcon = SymbolRegular.CheckmarkCircle24;
        SnackbarAppearance = ControlAppearance.Success;
        SnackbarTimeout = 3000;
        IsSnackbarVisible = true;
    }
}
```

## 🎨 依赖属性详解

### **核心属性**

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `Title` | string | "" | 标题文本 |
| `Message` | string | "" | 消息内容 |
| `Icon` | SymbolRegular | Info24 | 显示图标 |
| `Appearance` | ControlAppearance | Primary | 外观类型 |
| `IsShown` | bool | false | 是否显示 |
| `Timeout` | int | 3000 | 超时时间（毫秒，500-30000） |

### **样式属性**

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `UseAccentColor` | bool | true | 是否使用强调色 |
| `CustomBackground` | Brush | null | 自定义背景色 |
| `ShowCloseButton` | bool | true | 是否显示关闭按钮 |

## 🎯 使用扩展方法

### **便捷显示方法**

```csharp
using Zylo.WPF.Extensions;

// 在 View 的代码后台或 ViewModel 中
MainSnackbar.ShowSuccess("操作成功！");
MainSnackbar.ShowError("操作失败，请重试");
MainSnackbar.ShowWarning("请注意数据格式");
MainSnackbar.ShowInfo("这是一条提示信息");
```

### **静态帮助类**

```csharp
using Zylo.WPF.Controls;

// 创建不同类型的 Snackbar
var successSnackbar = ZyloSnackbarHelper.CreateSuccess("保存成功");
var errorSnackbar = ZyloSnackbarHelper.CreateError("网络连接失败");
var warningSnackbar = ZyloSnackbarHelper.CreateWarning("磁盘空间不足");
var infoSnackbar = ZyloSnackbarHelper.CreateInfo("新版本可用");

// 显示
successSnackbar.Show();
```

## 🎨 样式定制

### **主题适应性设计**

```xml
<!-- 默认：主题适应性卡片设计 -->
<zylo:ZyloSnackbar UseAccentColor="True" Appearance="Success"/>
<!--
  - 背景色：自动适应当前主题（浅色/深色）
  - 状态指示：左侧绿色条形指示器
  - 文字颜色：自动适应主题对比度
-->

<!-- 自定义背景色（覆盖主题适应） -->
<zylo:ZyloSnackbar CustomBackground="{StaticResource MyCustomBrush}"/>

<!-- 无状态指示器的纯主题模式 -->
<zylo:ZyloSnackbar UseAccentColor="False"/>
```

### **状态指示器系统**

```csharp
// 不同的外观类型对应不同的状态指示器颜色
ControlAppearance.Success  // 绿色指示器 - 成功操作
ControlAppearance.Danger   // 红色指示器 - 错误/危险
ControlAppearance.Caution  // 橙色指示器 - 警告
ControlAppearance.Info     // 蓝色指示器 - 信息提示
ControlAppearance.Primary  // 系统强调色指示器 - 主要消息
```

### **设计理念**

- 🎨 **背景色**：始终使用主题背景色，确保与应用整体风格一致
- 🎯 **状态表示**：通过左侧彩色指示器表示消息类型，而非整个背景
- 📱 **现代化**：类似系统通知的卡片式设计
- 🌓 **主题适应**：自动适应浅色/深色主题切换

## 🎬 动画效果

ZyloSnackbar 内置了流畅的动画效果：

- **显示动画** - 从底部滑入，带回弹效果
- **隐藏动画** - 向底部滑出，带淡出效果
- **自动触发** - 根据 IsShown 属性自动播放动画

## 📝 最佳实践

### **1. 超时时间建议**

```csharp
// 根据消息类型设置合适的超时时间
ShowSuccess("操作成功", timeout: 3000);    // 成功消息 - 3秒
ShowError("操作失败", timeout: 5000);      // 错误消息 - 5秒  
ShowWarning("警告信息", timeout: 4000);    // 警告消息 - 4秒
ShowInfo("提示信息", timeout: 3000);       // 信息消息 - 3秒
```

### **2. 消息内容建议**

```csharp
// ✅ 好的做法
ShowSuccess("文件保存成功", "操作完成");
ShowError("网络连接失败，请检查网络设置", "连接错误");

// ❌ 避免的做法
ShowSuccess("OK");  // 信息不够明确
ShowError("Error"); // 没有具体说明
```

### **3. 位置和布局**

```xml
<!-- 推荐：放在主容器的最后，确保在最上层显示 -->
<Grid>
    <!-- 其他内容 -->
    <ContentPresenter Content="{Binding CurrentView}"/>
    
    <!-- Snackbar 放在最后 -->
    <zylo:ZyloSnackbar x:Name="MainSnackbar" Grid.RowSpan="3"/>
</Grid>
```

## 🔧 高级用法

### **自定义内容模板**

```xml
<zylo:ZyloSnackbar>
    <zylo:ZyloSnackbar.ContentTemplate>
        <DataTemplate>
            <StackPanel Orientation="Horizontal">
                <ProgressBar Width="100" Height="4" IsIndeterminate="True"/>
                <TextBlock Text="正在处理..." Margin="8,0,0,0"/>
            </StackPanel>
        </DataTemplate>
    </zylo:ZyloSnackbar.ContentTemplate>
</zylo:ZyloSnackbar>
```

### **事件处理**

```csharp
// 监听关闭事件
MainSnackbar.Closed += (sender, e) =>
{
    // Snackbar 关闭后的处理逻辑
    Debug.WriteLine("Snackbar 已关闭");
};
```

## 🎊 总结

ZyloSnackbar 提供了一个完整、专业的通知解决方案，支持：

- 🎨 **丰富的样式选项** - 强调色、自定义颜色、多种外观
- 🎬 **流畅的动画效果** - 专业的滑入滑出动画
- 🔧 **完整的依赖属性** - 支持数据绑定和 MVVM 模式
- 🚀 **便捷的扩展方法** - 简化常见使用场景
- 📱 **响应式设计** - 适配不同屏幕尺寸

通过合理使用 ZyloSnackbar，可以为应用程序提供优秀的用户反馈体验！
