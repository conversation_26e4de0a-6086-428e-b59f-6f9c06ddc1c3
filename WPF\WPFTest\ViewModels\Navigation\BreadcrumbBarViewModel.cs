using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Reflection;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels.Navigation
{
    /// <summary>
    /// 面包屑项模型
    /// </summary>
    public class BreadcrumbItemModel
    {
        public string Name { get; set; } = string.Empty;
        public string Tag { get; set; } = string.Empty;
        public string Icon { get; set; } = "📄";
        public string Description { get; set; } = string.Empty;
        public DateTime CreatedTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// BreadcrumbBar 示例 ViewModel
    /// </summary>
    public partial class BreadcrumbBarViewModel : ObservableObject
    {
        private readonly YLoggerInstance _logger = YLogger.ForSilent<BreadcrumbBarViewModel>();

        #region 属性
        /// <summary>
        /// 面包屑项集合
        /// </summary>
        [ObservableProperty]
        public partial ObservableCollection<BreadcrumbItemModel> BreadcrumbItems { get; set; } = new();

        /// <summary>
        /// 当前路径字符串
        /// </summary>
        [ObservableProperty]
        public partial string CurrentPath { get; set; } = "/首页";

        /// <summary>
        /// 基础面包屑 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicBreadcrumbXaml { get; set; } = string.Empty;

        /// <summary>
        /// 基础面包屑 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicBreadcrumbCs { get; set; } = string.Empty;

        /// <summary>
        /// 高级面包屑 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedBreadcrumbXaml { get; set; } = string.Empty;

        /// <summary>
        /// 高级面包屑 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedBreadcrumbCs { get; set; } = string.Empty;



        /// <summary>
        /// 当前选中的面包屑项
        /// </summary>
        [ObservableProperty]
        public partial BreadcrumbItemModel? SelectedBreadcrumbItem { get; set; }

        /// <summary>
        /// 导航历史记录
        /// </summary>
        [ObservableProperty]
        public partial string NavigationHistory { get; set; } = string.Empty;

        /// <summary>
        /// 面包屑统计信息
        /// </summary>
        [ObservableProperty]
        public partial string BreadcrumbStats { get; set; } = string.Empty;

        #endregion

        #region 构造函数
        /// <summary>
        /// 构造函数
        /// </summary>
        public BreadcrumbBarViewModel()
        {
            _logger.Info("🍞 BreadcrumbBarViewModel 初始化开始");
            InitializeBreadcrumbItems();
            InitializeCodeExamples();
            UpdateBreadcrumbStats();
            UpdateNavigationHistory();
            _logger.Info("✅ BreadcrumbBarViewModel 初始化完成");
        }

        #endregion

        #region 命令

        /// <summary>
        /// 导航到指定页面命令
        /// </summary>
        [RelayCommand]
        private void NavigateTo(string target)
        {
            try
            {
                // 根据目标添加面包屑项
                var newItem = target switch
                {
                    "projects" => new BreadcrumbItemModel
                    {
                        Name = "项目管理",
                        Tag = "projects",
                        Icon = "📁",
                        Description = "管理所有项目文件和配置"
                    },
                    "docs" => new BreadcrumbItemModel
                    {
                        Name = "文档中心",
                        Tag = "docs",
                        Icon = "📄",
                        Description = "查看和编辑项目文档"
                    },
                    "settings" => new BreadcrumbItemModel
                    {
                        Name = "系统设置",
                        Tag = "settings",
                        Icon = "⚙️",
                        Description = "配置系统参数和选项"
                    },
                    "team" => new BreadcrumbItemModel
                    {
                        Name = "团队管理",
                        Tag = "team",
                        Icon = "👥",
                        Description = "管理团队成员和权限"
                    },
                    _ => new BreadcrumbItemModel
                    {
                        Name = target,
                        Tag = target,
                        Icon = "📄",
                        Description = $"导航到 {target}"
                    }
                };

                // 检查是否已存在相同项，避免重复
                var existingItem = BreadcrumbItems.FirstOrDefault(item => item.Tag == newItem.Tag);
                if (existingItem == null)
                {
                    // 添加新项
                    BreadcrumbItems.Add(newItem);
                }

                // 更新相关信息
                UpdateCurrentPath();
                UpdateBreadcrumbStats();
                UpdateNavigationHistory();
                SelectedBreadcrumbItem = newItem;

                _logger.Info($"🧭 导航到 {newItem.Name} ({newItem.Description})");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 导航失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 导航到面包屑项命令
        /// </summary>
        [RelayCommand]
        private void NavigateToBreadcrumb(BreadcrumbItemModel item)
        {
            try
            {
                var index = BreadcrumbItems.IndexOf(item);
                if (index >= 0)
                {
                    // 移除该项之后的所有项
                    for (int i = BreadcrumbItems.Count - 1; i > index; i--)
                    {
                        BreadcrumbItems.RemoveAt(i);
                    }
                }

                // 更新相关信息
                UpdateCurrentPath();
                UpdateBreadcrumbStats();
                UpdateNavigationHistory();
                SelectedBreadcrumbItem = item;

                _logger.Info($"🍞 面包屑导航到: {item.Name} ({item.Description})");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 面包屑导航失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 重置面包屑命令
        /// </summary>
        [RelayCommand]
        private void ResetBreadcrumb()
        {
            try
            {
                InitializeBreadcrumbItems();
                UpdateCurrentPath();
                UpdateBreadcrumbStats();
                UpdateNavigationHistory();
                SelectedBreadcrumbItem = BreadcrumbItems.FirstOrDefault();
                _logger.Info("🔄 面包屑已重置");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 重置面包屑失败: {ex.Message}");
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化面包屑项
        /// </summary>
        private void InitializeBreadcrumbItems()
        {
            BreadcrumbItems.Clear();
            BreadcrumbItems.Add(new BreadcrumbItemModel
            {
                Name = "首页",
                Tag = "home",
                Icon = "🏠",
                Description = "应用程序主页"
            });
        }

        /// <summary>
        /// 更新当前路径字符串
        /// </summary>
        private void UpdateCurrentPath()
        {
            if (BreadcrumbItems.Count == 0)
            {
                CurrentPath = "/";
                return;
            }

            CurrentPath = "/" + string.Join("/", BreadcrumbItems.Select(item => item.Name));
        }

        /// <summary>
        /// 更新面包屑统计信息
        /// </summary>
        private void UpdateBreadcrumbStats()
        {
            BreadcrumbStats = $"共 {BreadcrumbItems.Count} 项 | 当前: {SelectedBreadcrumbItem?.Name ?? "无"}";
        }

        /// <summary>
        /// 更新导航历史记录
        /// </summary>
        private void UpdateNavigationHistory()
        {
            NavigationHistory = BreadcrumbItems.Count > 0
                ? string.Join(" → ", BreadcrumbItems.Select(item => item.Name))
                : "暂无导航历史";
        }

        /// <summary>
        /// 初始化代码示例
        /// </summary>
        private void InitializeCodeExamples()
        {
            try
            {
                // 获取代码示例文件的基础路径
                var baseDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
                var codeExamplesPath = Path.Combine(baseDirectory, "CodeExamples", "Navigation", "BreadcrumbBar");

                // 读取基础面包屑示例
                BasicBreadcrumbXaml = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "BasicBreadcrumb.xaml.txt"));
                BasicBreadcrumbCs = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "BasicBreadcrumb.cs.txt"));

                // 读取高级面包屑示例
                AdvancedBreadcrumbXaml = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "AdvancedBreadcrumb.xaml.txt"));
                AdvancedBreadcrumbCs = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "AdvancedBreadcrumb.cs.txt"));

                _logger.Info("代码示例加载完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"加载代码示例失败: {ex.Message}");

                // 如果文件读取失败，使用默认示例
                SetDefaultCodeExamples();
            }
        }

        /// <summary>
        /// 读取代码示例文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件内容</returns>
        private string ReadCodeExampleFile(string filePath)
        {
            if (File.Exists(filePath))
            {
                return File.ReadAllText(filePath);
            }
            else
            {
                _logger.Warning($"代码示例文件不存在: {filePath}");
                return $"// 代码示例文件不存在: {Path.GetFileName(filePath)}";
            }
        }

        /// <summary>
        /// 设置默认代码示例（当文件读取失败时使用）
        /// </summary>
        private void SetDefaultCodeExamples()
        {
            BasicBreadcrumbXaml = "<!-- 基础面包屑示例 -->\n<ui:BreadcrumbBar ItemsSource=\"{Binding BreadcrumbItems}\">\n    <ui:BreadcrumbBar.ItemTemplate>\n        <DataTemplate>\n            <TextBlock Text=\"{Binding Name}\" />\n        </DataTemplate>\n    </ui:BreadcrumbBar.ItemTemplate>\n</ui:BreadcrumbBar>";
            BasicBreadcrumbCs = "// 基础面包屑 C# 示例\n[ObservableProperty]\npublic partial ObservableCollection<BreadcrumbItemModel> BreadcrumbItems { get; set; } = new();\n\n[RelayCommand]\nprivate void NavigateTo(string target)\n{\n    // 导航逻辑\n}";

            AdvancedBreadcrumbXaml = "<!-- 高级面包屑示例 -->\n<ui:BreadcrumbBar ItemsSource=\"{Binding BreadcrumbItems}\">\n    <ui:BreadcrumbBar.ItemTemplate>\n        <DataTemplate>\n            <StackPanel Orientation=\"Horizontal\">\n                <TextBlock Text=\"{Binding Icon}\" Margin=\"0,0,4,0\" />\n                <TextBlock Text=\"{Binding Name}\" />\n            </StackPanel>\n        </DataTemplate>\n    </ui:BreadcrumbBar.ItemTemplate>\n</ui:BreadcrumbBar>";
            AdvancedBreadcrumbCs = "// 高级面包屑 C# 示例\npublic class BreadcrumbItemModel\n{\n    public string Name { get; set; } = string.Empty;\n    public string Icon { get; set; } = \"📄\";\n    public string Tag { get; set; } = string.Empty;\n}";
        }

        #endregion
    }
}
