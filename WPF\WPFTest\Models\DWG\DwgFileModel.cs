using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Text.RegularExpressions;
using CommunityToolkit.Mvvm.ComponentModel;
using ImTools;
using WPFTest.Services.DWG;

namespace WPFTest.Models.DWG;

/// <summary>
/// DWG 文件信息模型 - 专业版
/// </summary>
public partial class DwgFileModel : ObservableObject
{
    /// <summary>
    /// 文件完整路径
    /// </summary>
    [ObservableProperty]
    [Description("文件完整路径")]
    public partial string FullPath { get; set; } = "";

    /// <summary>
    /// 文件名（不含扩展名）
    /// </summary>
    [ObservableProperty]
    [Description("文件名（不含扩展名）")]
    public partial string FileName { get; set; } = "";

    /// <summary>
    /// 图纸类型前缀（如：GS、JT、底图等）
    /// </summary>
    [ObservableProperty]
    [Description("图纸类型前缀（如：GS、JT、底图等）")]
    public partial string DrawingTypePrefix { get; set; } = "";
    

    /// <summary>
    /// 图纸分类（出图、施工图、底图等）
    /// </summary>
    [ObservableProperty]
    [Description("图纸分类（出图、施工图、底图等）")]
    public partial string Category { get; set; } = "";

    /// <summary>
    /// 文件大小（字节）
    /// </summary>
    [ObservableProperty]
    [Description("文件大小（字节）")]
    public partial long FileSize { get; set; } = 0;

    /// <summary>
    /// 文件大小文本
    /// </summary>
    public string FileSizeText => FormatFileSize(FileSize);

    /// <summary>
    /// 修改时间
    /// </summary>
    [ObservableProperty]
    [Description("修改时间")]
    public partial DateTime ModifiedDate { get; set; } = DateTime.MinValue;

    /// <summary>
    /// 创建时间
    /// </summary>
    [ObservableProperty]
    [Description("创建时间")]
    public partial DateTime CreatedDate { get; set; } = DateTime.MinValue;

    /// <summary>
    /// 图纸状态（最新、历史版本等）
    /// </summary>
    [ObservableProperty]
    [Description("图纸状态（最新、历史版本等）")]
    public partial string Status { get; set; } = "";

    /// <summary>
    /// 版本号
    /// </summary>
    [ObservableProperty]
    [Description("版本号")]
    public partial string Version { get; set; } = "";

    /// <summary>
    /// 备注信息
    /// </summary>
    [ObservableProperty]
    [Description("备注信息")]
    public partial string Remarks { get; set; } = "";


    /// <summary>
    /// 是否为最新版本
    /// </summary>
    [ObservableProperty]
    [Description("是否为最新版本")]
    public partial bool IsLatestVersion { get; set; } = true;


    /// <summary>
    /// 图纸图标（根据类型显示不同图标）
    /// </summary>
    public string DrawingIcon => GetDrawingIcon();

    /// <summary>
    /// 从文件路径创建 DWG 文件模型
    /// </summary>
    public static DwgFileModel FromFilePath( string filePath)
    {
        var fileInfo = new FileInfo(filePath);
        var fileName = Path.GetFileNameWithoutExtension(filePath);

        var model = new DwgFileModel
        {
            FullPath = filePath,
            FileName = fileName,
            FileSize = fileInfo.Length,
            ModifiedDate = fileInfo.LastWriteTime,
            CreatedDate = fileInfo.CreationTime
        };

      

        return model;
    }

    /// <summary>
    /// 解析图纸类型
    /// </summary>
    private void ParseDrawingType(ObservableCollection<DwgFileTypeModel> DwgFileTypeModels,string fileName)
    {
        string outstring = "";
        // 解析图纸类型前缀（如：GS_、JT_、底图_等）
        if (fileName.Contains("_"))
        {
            var parts = fileName.Split('_');
            if (parts.Length >= 2)
            {
                DrawingTypePrefix = parts[0].Trim();
                var data= GetDrawingTypeName(DwgFileTypeModels,DrawingTypePrefix);

                if (data!=null)
                {
                    Category = data.ChineseName;
                }
                else
                {
                    Category= "其他";
                
                }
        
            }
        }
        else if (fileName.Contains("-"))
        {
            // 兼容旧的命名规则
            var parts = fileName.Split('-');
            if (parts.Length >= 2)
            {
                DrawingTypePrefix = parts[0].Trim();
                var data= GetDrawingTypeName(DwgFileTypeModels,DrawingTypePrefix);

                if (data!=null)
                {
                    Category = data.ChineseName;
                }
                else
                {
                    Category= "其他";
                
                }
            }
        }
        else
        {
            // 处理其他命名规则
            DrawingTypePrefix = "";
            Category = "其他";
        }
    }

    /// <summary>
    /// 获取图纸类型名称
    /// </summary>
    private DwgFileTypeModel GetDrawingTypeName(ObservableCollection<DwgFileTypeModel> DwgFileTypeModels,  string prefix)
    {
       
         

        var fietype =  DwgFileTypeModels.ToList().FirstOrDefault(f =>
        {
            var parts = f.Prefixes.Split(',', StringSplitOptions.RemoveEmptyEntries);
            return parts.Any(p => p.Trim().Equals(prefix, StringComparison.OrdinalIgnoreCase));
        });

        return fietype;
    }


    /// <summary>
    /// 获取图纸图标
    /// </summary>
    private string GetDrawingIcon()
    {
        return DrawingTypePrefix.ToUpper() switch
        {
            "GS" => "💧", // 给水
            "WS" => "🚰", // 污水
            "YS" => "🌧️", // 雨水
            "JT" => "🚗", // 交通
            "DL" => "🛣️", // 道路
            "LD" => "🌳", // 绿化
            "JG" => "🏗️", // 结构
            "JZ" => "🏢", // 建筑
            "DQ" => "⚡", // 电气
            "HVAC" => "🌡️", // 暖通
            _ => "📐" // 默认
        };
    }

    #region 前缀处理功能

    /// <summary>
    /// 智能去除文件名前缀（使用正则表达式循环移除）
    /// </summary>
    /// <param name="fileTypes">文件类型列表（此参数保留兼容性，实际不使用）</param>
    /// <returns>去除前缀后的基础文件名</returns>
    public string RemovePrefix(IEnumerable<DwgFileTypeModel> fileTypes)
    {
        if (string.IsNullOrEmpty(FileName))
            return FileName;

        var currentFileName = FileName;

        // 正则表达式：匹配开头的1-2个字母 + 分隔符(_或-)
        var prefixPattern = @"^[A-Za-z]{1,2}[_-]";
        var maxIterations = 10; // 防止无限循环
        var iteration = 0;

        System.Diagnostics.Debug.WriteLine($"🔍 开始移除前缀: {FileName}");

        // 循环移除前缀，直到没有更多前缀可移除
        while (iteration < maxIterations && Regex.IsMatch(currentFileName, prefixPattern))
        {
            var originalFileName = currentFileName;

            // 找到匹配的前缀
            var match = Regex.Match(currentFileName, prefixPattern);
            if (match.Success)
            {
                var removedPrefix = match.Value;
                currentFileName = currentFileName.Substring(match.Length);

                System.Diagnostics.Debug.WriteLine($"🔄 第{iteration + 1}次循环: 移除前缀 '{removedPrefix}' → {currentFileName}");
            }

            // 防止无限循环
            if (currentFileName == originalFileName)
            {
                break;
            }

            iteration++;
        }

        System.Diagnostics.Debug.WriteLine($"🎯 最终结果: {FileName} → {currentFileName}");
        return currentFileName;
    }

    /// <summary>
    /// 获取去除前缀后的基础文件名
    /// </summary>
    /// <param name="fileTypes">文件类型列表</param>
    /// <returns>基础文件名</returns>
    public string GetBaseName(IEnumerable<DwgFileTypeModel> fileTypes)
    {
        return RemovePrefix(fileTypes);
    }

    #endregion

    /// <summary>
    /// 格式化文件大小
    /// </summary>
    private static string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }

        return $"{len:0.##} {sizes[order]}";
    }
}