using System;
using System.IO;
using System.Threading.Tasks;
using WPFTest.Models.DWG;
using WPFTest.ViewModels.DragDrop;
using WPFTest.ViewModels.DWG;

namespace WPFTest.Tests;

/// <summary>
/// DWG右键菜单功能测试类
/// </summary>
/// <remarks>
/// 测试DataGrid右键菜单的各项功能是否正常工作
/// </remarks>
public class DwgContextMenuTest
{
    private readonly DwgManagerTabViewModel _viewModel;
    private readonly string _testDirectory;

    public DwgContextMenuTest(DwgManagerTabViewModel viewModel)
    {
        _viewModel = viewModel ?? throw new ArgumentNullException(nameof(viewModel));
        _testDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "DwgMenuTest");
    }

    /// <summary>
    /// 初始化测试环境
    /// </summary>
    public void InitializeTestEnvironment()
    {
        try
        {
            // 创建测试目录
            if (!Directory.Exists(_testDirectory))
            {
                Directory.CreateDirectory(_testDirectory);
                Console.WriteLine($"✅ 创建测试目录: {_testDirectory}");
            }

            // 创建测试文件
            var testFilePath = Path.Combine(_testDirectory, "测试文件.dwg");
            if (!File.Exists(testFilePath))
            {
                File.WriteAllText(testFilePath, "测试DWG文件内容");
                Console.WriteLine($"✅ 创建测试文件: {testFilePath}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 初始化测试环境失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 测试打开文件功能
    /// </summary>
    public void TestOpenFile()
    {
        try
        {
            Console.WriteLine("🧪 测试打开文件功能...");

            var testFile = CreateTestFileModel();
            _viewModel.OpenFileCommand.Execute(testFile);

            Console.WriteLine("✅ 打开文件功能测试通过");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 打开文件功能测试失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 测试复制文件功能
    /// </summary>
    public async Task TestCopyFileAsync()
    {
        try
        {
            Console.WriteLine("🧪 测试复制文件功能...");

            var testFile = CreateTestFileModel();
            await _viewModel.CopyFileCommand.ExecuteAsync(testFile);

            // 检查副本是否创建成功
            var copyPath = Path.Combine(_testDirectory, "测试文件_副本.dwg");
            if (File.Exists(copyPath))
            {
                Console.WriteLine("✅ 复制文件功能测试通过");
            }
            else
            {
                Console.WriteLine("❌ 复制文件功能测试失败: 副本文件未创建");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 复制文件功能测试失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 测试复制到桌面功能
    /// </summary>
    public async Task TestCopyToDesktopAsync()
    {
        try
        {
            Console.WriteLine("🧪 测试复制到桌面功能...");

            var testFile = CreateTestFileModel();
            await _viewModel.CopyToDesktopCommand.ExecuteAsync(testFile);

            // 检查桌面是否有文件
            var desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
            var desktopFilePath = Path.Combine(desktopPath, "测试文件.dwg");
            
            if (File.Exists(desktopFilePath))
            {
                Console.WriteLine("✅ 复制到桌面功能测试通过");
                // 清理桌面文件
                File.Delete(desktopFilePath);
            }
            else
            {
                Console.WriteLine("❌ 复制到桌面功能测试失败: 桌面文件未创建");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 复制到桌面功能测试失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 测试复制路径功能
    /// </summary>
    public void TestCopyFilePath()
    {
        try
        {
            Console.WriteLine("🧪 测试复制路径功能...");

            var testFile = CreateTestFileModel();
            _viewModel.CopyFilePathCommand.Execute(testFile);

            Console.WriteLine("✅ 复制路径功能测试通过");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 复制路径功能测试失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 测试新建DWG文件功能
    /// </summary>
    public async Task TestCreateNewDwgAsync()
    {
        try
        {
            Console.WriteLine("🧪 测试新建DWG文件功能...");

            // 设置当前专业文件夹
            if (_viewModel.LDwgFolderModels.Count > 0)
            {
                _viewModel.TDwgFolderModel = _viewModel.LDwgFolderModels.First();
            }

            await _viewModel.CreateNewDwgCommand.ExecuteAsync(null);

            Console.WriteLine("✅ 新建DWG文件功能测试通过");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 新建DWG文件功能测试失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 测试显示文件属性功能
    /// </summary>
    public void TestShowFileProperties()
    {
        try
        {
            Console.WriteLine("🧪 测试显示文件属性功能...");

            var testFile = CreateTestFileModel();
            _viewModel.ShowFilePropertiesCommand.Execute(testFile);

            Console.WriteLine("✅ 显示文件属性功能测试通过");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 显示文件属性功能测试失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 运行所有测试
    /// </summary>
    public async Task RunAllTestsAsync()
    {
        Console.WriteLine("🚀 开始DWG右键菜单功能测试...");
        Console.WriteLine(new string('=', 50));

        InitializeTestEnvironment();

        TestOpenFile();
        await TestCopyFileAsync();
        await TestCopyToDesktopAsync();
        TestCopyFilePath();
        await TestCreateNewDwgAsync();
        TestShowFileProperties();

        Console.WriteLine(new string('=', 50));
        Console.WriteLine("🏁 所有测试完成");
    }

    /// <summary>
    /// 清理测试数据
    /// </summary>
    public void CleanupTestData()
    {
        try
        {
            if (Directory.Exists(_testDirectory))
            {
                Directory.Delete(_testDirectory, true);
                Console.WriteLine("🧹 测试数据清理完成");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"⚠️ 清理测试数据失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 创建测试文件模型
    /// </summary>
    /// <returns>测试用的DwgFileModel</returns>
    private DwgFileModel CreateTestFileModel()
    {
        var testFilePath = Path.Combine(_testDirectory, "测试文件.dwg");
        return DwgFileModel.FromFilePath(testFilePath);
    }
}

/// <summary>
/// 右键菜单测试运行器
/// </summary>
public static class DwgContextMenuTestRunner
{
    /// <summary>
    /// 运行右键菜单测试
    /// </summary>
    /// <param name="viewModel">DwgManagerTabViewModel实例</param>
    public static async Task RunTestsAsync(DwgManagerTabViewModel viewModel)
    {
        var tester = new DwgContextMenuTest(viewModel);
        
        try
        {
            await tester.RunAllTestsAsync();
        }
        finally
        {
            tester.CleanupTestData();
        }
    }

    /// <summary>
    /// 快速测试 - 只测试基本功能
    /// </summary>
    /// <param name="viewModel">DwgManagerTabViewModel实例</param>
    public static async Task QuickTestAsync(DwgManagerTabViewModel viewModel)
    {
        var tester = new DwgContextMenuTest(viewModel);
        
        Console.WriteLine("⚡ 快速测试模式");
        tester.InitializeTestEnvironment();
        tester.TestOpenFile();
        tester.TestCopyFilePath();
        await tester.TestCopyToDesktopAsync();
        tester.CleanupTestData();
        Console.WriteLine("⚡ 快速测试完成");
    }
}
