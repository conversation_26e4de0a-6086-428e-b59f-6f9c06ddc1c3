<!-- TimePicker 高级用法示例 -->
<StackPanel Margin="20" Spacing="15">

    <!-- 时间范围选择 -->
    <GroupBox Header="时间范围选择" Padding="15">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0" 
                       Text="开始时间：" 
                       VerticalAlignment="Center" 
                       Margin="0,0,12,0"/>
            <ui:TimePicker Grid.Column="1" 
                          SelectedTime="{Binding StartTime, Mode=TwoWay}"
                          Style="{StaticResource TimePickerStyle}"
                          Width="150"
                          Margin="0,0,20,0"/>
            
            <TextBlock Grid.Column="2" 
                       Text="结束时间：" 
                       VerticalAlignment="Center" 
                       Margin="0,0,12,0"/>
            <ui:TimePicker Grid.Column="3" 
                          SelectedTime="{Binding EndTime, Mode=TwoWay}"
                          Style="{StaticResource TimePickerStyle}"
                          Width="150"/>
        </Grid>
    </GroupBox>

    <!-- 程序化控制 -->
    <GroupBox Header="程序化控制" Padding="15">
        <StackPanel>
            <Grid Margin="0,0,0,15">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" 
                           Text="程序化时间：" 
                           VerticalAlignment="Center" 
                           Margin="0,0,12,0"/>
                <ui:TimePicker Grid.Column="1"
                              SelectedTime="{Binding ProgrammaticTime, Mode=TwoWay}"
                              Style="{StaticResource AccentTimePickerStyle}"
                              Width="150"
                              Margin="0,0,20,0"/>
                
                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <ui:Button Content="9:00" 
                               Command="{Binding SetSpecificTimeCommand}"
                               CommandParameter="09:00"
                               Appearance="Secondary"
                               Margin="0,0,4,0"/>
                    <ui:Button Content="12:30" 
                               Command="{Binding SetSpecificTimeCommand}"
                               CommandParameter="12:30"
                               Appearance="Secondary"
                               Margin="0,0,4,0"/>
                    <ui:Button Content="18:00" 
                               Command="{Binding SetSpecificTimeCommand}"
                               CommandParameter="18:00"
                               Appearance="Secondary"/>
                </StackPanel>
            </Grid>

            <!-- 快速操作按钮 -->
            <WrapPanel>
                <ui:Button Content="设置当前时间" 
                           Command="{Binding SetCurrentTimeCommand}"
                           Appearance="Primary"
                           Icon="{ui:SymbolIcon Clock24}"
                           Margin="0,0,8,0"/>
                <ui:Button Content="清除时间" 
                           Command="{Binding ClearTimeCommand}"
                           Appearance="Secondary"
                           Icon="{ui:SymbolIcon Delete24}"
                           Margin="0,0,8,0"/>
                <ui:Button Content="设置工作时间" 
                           Command="{Binding SetWorkTimeCommand}"
                           Appearance="Secondary"
                           Margin="0,0,8,0"/>
                <ui:Button Content="设置休息时间" 
                           Command="{Binding SetBreakTimeCommand}"
                           Appearance="Secondary"/>
            </WrapPanel>
        </StackPanel>
    </GroupBox>

    <!-- 时间格式展示 -->
    <GroupBox Header="时间格式展示" Padding="15">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Row="0" Grid.Column="0" 
                       Text="24小时格式：" 
                       VerticalAlignment="Center" 
                       Margin="0,0,12,8"/>
            <TextBlock Grid.Row="0" Grid.Column="1"
                       Text="{Binding SelectedTime, StringFormat='{}{0:HH:mm:ss}'}"
                       FontWeight="Medium"
                       Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                       Margin="0,0,0,8"/>

            <TextBlock Grid.Row="1" Grid.Column="0" 
                       Text="12小时格式：" 
                       VerticalAlignment="Center" 
                       Margin="0,0,12,8"/>
            <TextBlock Grid.Row="1" Grid.Column="1"
                       Text="{Binding SelectedTime, StringFormat='{}{0:hh:mm:ss tt}'}"
                       FontWeight="Medium"
                       Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                       Margin="0,0,0,8"/>

            <TextBlock Grid.Row="2" Grid.Column="0" 
                       Text="简短格式：" 
                       VerticalAlignment="Center" 
                       Margin="0,0,12,8"/>
            <TextBlock Grid.Row="2" Grid.Column="1"
                       Text="{Binding SelectedTime, StringFormat='{}{0:HH:mm}'}"
                       FontWeight="Medium"
                       Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                       Margin="0,0,0,8"/>

            <TextBlock Grid.Row="3" Grid.Column="0" 
                       Text="时间范围：" 
                       VerticalAlignment="Center" 
                       Margin="0,0,12,0"/>
            <TextBlock Grid.Row="3" Grid.Column="1"
                       FontWeight="Medium"
                       Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}">
                <TextBlock.Text>
                    <MultiBinding StringFormat="{}{0:HH:mm} - {1:HH:mm}">
                        <Binding Path="StartTime"/>
                        <Binding Path="EndTime"/>
                    </MultiBinding>
                </TextBlock.Text>
            </TextBlock>
        </Grid>
    </GroupBox>

    <!-- 验证和约束 -->
    <GroupBox Header="时间验证和约束" Padding="15">
        <StackPanel>
            <TextBlock Text="工作时间约束示例（8:00 - 18:00）" 
                       FontWeight="Medium" 
                       Margin="0,0,0,10"/>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" 
                           Text="受限时间：" 
                           VerticalAlignment="Center" 
                           Margin="0,0,12,0"/>
                <ui:TimePicker Grid.Column="1"
                              SelectedTime="{Binding RestrictedTime, Mode=TwoWay}"
                              Style="{StaticResource WorkTimePickerStyle}"
                              Width="150"
                              Margin="0,0,20,0"/>
                
                <TextBlock Grid.Column="2"
                           Text="{Binding TimeValidationMessage}"
                           VerticalAlignment="Center"
                           Foreground="{DynamicResource SystemFillColorCriticalBrush}"/>
            </Grid>
        </StackPanel>
    </GroupBox>

</StackPanel>
