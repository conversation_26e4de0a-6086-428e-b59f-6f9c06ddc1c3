<!-- ScrollViewer 基础用法示例 -->
<StackPanel Margin="20">

    <!-- 垂直滚动示例 -->
    <TextBlock Text="垂直滚动示例" FontWeight="Bold" Margin="0,0,0,8"/>
    <Border BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
            BorderThickness="1"
            CornerRadius="4"
            Margin="0,0,0,16">
        <ScrollViewer Height="200"
                      VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Disabled"
                      Background="{DynamicResource ControlFillColorDefaultBrush}">
            <StackPanel Margin="16">
                <TextBlock Text="这是一个垂直滚动的示例。" FontWeight="Bold" Margin="0,0,0,12"/>
                <TextBlock Text="当内容超过容器高度时，会自动显示垂直滚动条。" Margin="0,0,0,8"/>
                <TextBlock Text="第 1 行内容" Margin="0,0,0,4"/>
                <TextBlock Text="第 2 行内容" Margin="0,0,0,4"/>
                <TextBlock Text="第 3 行内容" Margin="0,0,0,4"/>
                <TextBlock Text="第 4 行内容" Margin="0,0,0,4"/>
                <TextBlock Text="第 5 行内容" Margin="0,0,0,4"/>
                <TextBlock Text="第 6 行内容" Margin="0,0,0,4"/>
                <TextBlock Text="第 7 行内容" Margin="0,0,0,4"/>
                <TextBlock Text="第 8 行内容" Margin="0,0,0,4"/>
                <TextBlock Text="第 9 行内容" Margin="0,0,0,4"/>
                <TextBlock Text="第 10 行内容" Margin="0,0,0,4"/>
                <TextBlock Text="第 11 行内容" Margin="0,0,0,4"/>
                <TextBlock Text="第 12 行内容" Margin="0,0,0,4"/>
                <TextBlock Text="第 13 行内容" Margin="0,0,0,4"/>
                <TextBlock Text="第 14 行内容" Margin="0,0,0,4"/>
                <TextBlock Text="第 15 行内容" Margin="0,0,0,4"/>
                <Button Content="点击测试" Margin="0,12,0,0"/>
            </StackPanel>
        </ScrollViewer>
    </Border>

    <!-- 水平滚动示例 -->
    <TextBlock Text="水平滚动示例" FontWeight="Bold" Margin="0,0,0,8"/>
    <Border BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
            BorderThickness="1"
            CornerRadius="4"
            Margin="0,0,0,16">
        <ScrollViewer Height="150"
                      VerticalScrollBarVisibility="Disabled"
                      HorizontalScrollBarVisibility="Auto"
                      Background="{DynamicResource ControlFillColorDefaultBrush}">
            <StackPanel Orientation="Horizontal" Margin="16">
                <Border Background="{DynamicResource AccentFillColorDefaultBrush}"
                        Width="100" Height="80" Margin="0,0,8,0" CornerRadius="4">
                    <TextBlock Text="卡片 1" 
                               Foreground="White" 
                               HorizontalAlignment="Center" 
                               VerticalAlignment="Center"/>
                </Border>
                <Border Background="{DynamicResource AccentFillColorSecondaryBrush}"
                        Width="100" Height="80" Margin="0,0,8,0" CornerRadius="4">
                    <TextBlock Text="卡片 2" 
                               Foreground="White" 
                               HorizontalAlignment="Center" 
                               VerticalAlignment="Center"/>
                </Border>
                <Border Background="{DynamicResource AccentFillColorTertiaryBrush}"
                        Width="100" Height="80" Margin="0,0,8,0" CornerRadius="4">
                    <TextBlock Text="卡片 3" 
                               Foreground="White" 
                               HorizontalAlignment="Center" 
                               VerticalAlignment="Center"/>
                </Border>
                <Border Background="{DynamicResource AccentFillColorDefaultBrush}"
                        Width="100" Height="80" Margin="0,0,8,0" CornerRadius="4">
                    <TextBlock Text="卡片 4" 
                               Foreground="White" 
                               HorizontalAlignment="Center" 
                               VerticalAlignment="Center"/>
                </Border>
                <Border Background="{DynamicResource AccentFillColorSecondaryBrush}"
                        Width="100" Height="80" Margin="0,0,8,0" CornerRadius="4">
                    <TextBlock Text="卡片 5" 
                               Foreground="White" 
                               HorizontalAlignment="Center" 
                               VerticalAlignment="Center"/>
                </Border>
                <Border Background="{DynamicResource AccentFillColorTertiaryBrush}"
                        Width="100" Height="80" CornerRadius="4">
                    <TextBlock Text="卡片 6" 
                               Foreground="White" 
                               HorizontalAlignment="Center" 
                               VerticalAlignment="Center"/>
                </Border>
            </StackPanel>
        </ScrollViewer>
    </Border>

    <!-- 滚动条可见性设置 -->
    <TextBlock Text="滚动条可见性设置" FontWeight="Bold" Margin="0,0,0,8"/>
    <Grid Margin="0,0,0,16">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>
        
        <!-- Auto 模式 -->
        <StackPanel Grid.Column="0" Margin="0,0,8,0">
            <TextBlock Text="Auto 模式" FontWeight="Medium" Margin="0,0,0,4"/>
            <Border BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="4">
                <ScrollViewer Height="100"
                              VerticalScrollBarVisibility="Auto"
                              HorizontalScrollBarVisibility="Auto"
                              Background="{DynamicResource ControlFillColorDefaultBrush}">
                    <StackPanel Margin="8">
                        <TextBlock Text="内容 1"/>
                        <TextBlock Text="内容 2"/>
                        <TextBlock Text="内容 3"/>
                        <TextBlock Text="内容 4"/>
                        <TextBlock Text="内容 5"/>
                        <TextBlock Text="内容 6"/>
                    </StackPanel>
                </ScrollViewer>
            </Border>
        </StackPanel>
        
        <!-- Visible 模式 -->
        <StackPanel Grid.Column="1" Margin="4,0,4,0">
            <TextBlock Text="Visible 模式" FontWeight="Medium" Margin="0,0,0,4"/>
            <Border BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="4">
                <ScrollViewer Height="100"
                              VerticalScrollBarVisibility="Visible"
                              HorizontalScrollBarVisibility="Visible"
                              Background="{DynamicResource ControlFillColorDefaultBrush}">
                    <StackPanel Margin="8">
                        <TextBlock Text="内容 1"/>
                        <TextBlock Text="内容 2"/>
                        <TextBlock Text="内容 3"/>
                    </StackPanel>
                </ScrollViewer>
            </Border>
        </StackPanel>
        
        <!-- Hidden 模式 -->
        <StackPanel Grid.Column="2" Margin="8,0,0,0">
            <TextBlock Text="Hidden 模式" FontWeight="Medium" Margin="0,0,0,4"/>
            <Border BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="4">
                <ScrollViewer Height="100"
                              VerticalScrollBarVisibility="Hidden"
                              HorizontalScrollBarVisibility="Hidden"
                              Background="{DynamicResource ControlFillColorDefaultBrush}">
                    <StackPanel Margin="8">
                        <TextBlock Text="内容 1"/>
                        <TextBlock Text="内容 2"/>
                        <TextBlock Text="内容 3"/>
                        <TextBlock Text="内容 4"/>
                        <TextBlock Text="内容 5"/>
                        <TextBlock Text="内容 6"/>
                    </StackPanel>
                </ScrollViewer>
            </Border>
        </StackPanel>
    </Grid>

    <!-- 嵌套滚动示例 -->
    <TextBlock Text="嵌套滚动示例" FontWeight="Bold" Margin="0,0,0,8"/>
    <Border BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
            BorderThickness="1"
            CornerRadius="4"
            Margin="0,0,0,16">
        <ScrollViewer Height="200"
                      VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Disabled"
                      Background="{DynamicResource ControlFillColorDefaultBrush}">
            <StackPanel Margin="16">
                <TextBlock Text="外层滚动区域" FontWeight="Bold" Margin="0,0,0,8"/>
                <TextBlock Text="这是外层的一些内容。" Margin="0,0,0,8"/>
                
                <!-- 内层滚动区域 -->
                <Border BorderBrush="{DynamicResource ControlStrokeColorSecondaryBrush}"
                        BorderThickness="1"
                        CornerRadius="4"
                        Margin="0,0,0,8">
                    <ScrollViewer Height="80"
                                  VerticalScrollBarVisibility="Auto"
                                  HorizontalScrollBarVisibility="Disabled"
                                  Background="{DynamicResource ControlFillColorSecondaryBrush}">
                        <StackPanel Margin="8">
                            <TextBlock Text="内层滚动区域" FontWeight="Medium" Margin="0,0,0,4"/>
                            <TextBlock Text="内层内容 1" Margin="0,0,0,2"/>
                            <TextBlock Text="内层内容 2" Margin="0,0,0,2"/>
                            <TextBlock Text="内层内容 3" Margin="0,0,0,2"/>
                            <TextBlock Text="内层内容 4" Margin="0,0,0,2"/>
                            <TextBlock Text="内层内容 5" Margin="0,0,0,2"/>
                            <TextBlock Text="内层内容 6"/>
                        </StackPanel>
                    </ScrollViewer>
                </Border>
                
                <TextBlock Text="外层继续的内容。" Margin="0,0,0,4"/>
                <TextBlock Text="更多外层内容 1" Margin="0,0,0,4"/>
                <TextBlock Text="更多外层内容 2" Margin="0,0,0,4"/>
                <TextBlock Text="更多外层内容 3" Margin="0,0,0,4"/>
                <TextBlock Text="更多外层内容 4" Margin="0,0,0,4"/>
                <TextBlock Text="更多外层内容 5"/>
            </StackPanel>
        </ScrollViewer>
    </Border>

    <!-- 滚动条样式示例 -->
    <TextBlock Text="滚动条样式示例" FontWeight="Bold" Margin="0,0,0,8"/>
    <Border BorderBrush="{DynamicResource AccentStrokeColorDefaultBrush}"
            BorderThickness="2"
            CornerRadius="4"
            Background="{DynamicResource AccentFillColorDefaultBrush}">
        <ScrollViewer Height="150"
                      VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Disabled"
                      Background="Transparent">
            <StackPanel Margin="16">
                <TextBlock Text="自定义样式的滚动区域" 
                           FontWeight="Bold" 
                           Foreground="White" 
                           Margin="0,0,0,8"/>
                <TextBlock Text="这个滚动区域使用了自定义的样式。" 
                           Foreground="White" 
                           Margin="0,0,0,4"/>
                <TextBlock Text="样式内容 1" Foreground="White" Margin="0,0,0,4"/>
                <TextBlock Text="样式内容 2" Foreground="White" Margin="0,0,0,4"/>
                <TextBlock Text="样式内容 3" Foreground="White" Margin="0,0,0,4"/>
                <TextBlock Text="样式内容 4" Foreground="White" Margin="0,0,0,4"/>
                <TextBlock Text="样式内容 5" Foreground="White" Margin="0,0,0,4"/>
                <TextBlock Text="样式内容 6" Foreground="White" Margin="0,0,0,4"/>
                <TextBlock Text="样式内容 7" Foreground="White" Margin="0,0,0,4"/>
                <TextBlock Text="样式内容 8" Foreground="White"/>
            </StackPanel>
        </ScrollViewer>
    </Border>

</StackPanel>
