<UserControl x:Class="WPFTest.Views.DragDrop.TreeDragDropExample"
           xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
           xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
           xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
           xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
           xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
           xmlns:dd="urn:gong-wpf-dragdrop"
           xmlns:local="clr-namespace:WPFTest.Views.DragDrop"
           mc:Ignorable="d" 
           d:DesignHeight="800" d:DesignWidth="1200"
          >

    <UserControl.Resources>
        <!-- 节点类型图标转换器 -->
        <local:NodeTypeToIconConverter x:Key="NodeTypeToIconConverter"/>
        
        <!-- 层级缩进转换器 -->
        <local:LevelToMarginConverter x:Key="LevelToMarginConverter"/>
    </UserControl.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题和状态区域 -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="🌳 树形拖拽示例" 
                       FontSize="24" 
                       FontWeight="Bold" 
                       Margin="0,0,0,10"/>
            
            <TextBlock Text="演示 gong-wpf-dragdrop 库在 TreeView 控件中的应用，支持节点间的拖拽移动和层级调整。" 
                       FontSize="14" 
                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                       TextWrapping="Wrap"
                       Margin="0,0,0,15"/>

            <!-- 状态信息 -->
            <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Padding="15">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" 
                               Text="{Binding StatusMessage}" 
                               VerticalAlignment="Center"
                               FontWeight="Medium"/>

                    <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="20,0">
                        <ui:SymbolIcon Symbol="TreeDeciduous24" FontSize="16" Margin="0,0,5,0"/>
                        <TextBlock Text="{Binding TotalNodesCount, StringFormat='总节点: {0}'}" 
                                   VerticalAlignment="Center"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2" Orientation="Horizontal" Margin="20,0">
                        <ui:SymbolIcon Symbol="FolderOpen24" FontSize="16" Margin="0,0,5,0"/>
                        <TextBlock Text="{Binding ExpandedNodesCount, StringFormat='已展开: {0}'}" 
                                   VerticalAlignment="Center"/>
                    </StackPanel>

                    <StackPanel Grid.Column="3" Orientation="Horizontal">
                        <ui:SymbolIcon Symbol="ArrowMove24" FontSize="16" Margin="0,0,5,0"/>
                        <TextBlock Text="{Binding InteractionCount, StringFormat='操作次数: {0}'}" 
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </Border>
        </StackPanel>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="20"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧：树形拖拽演示 -->
            <Border Grid.Column="0"
                    Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 工具栏 -->
                    <Border Grid.Row="0"
                            Background="{DynamicResource CardBackgroundFillColorSecondaryBrush}"
                            BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                            BorderThickness="0,0,0,1"
                            CornerRadius="8,8,0,0"
                            Padding="15,10">
                        <StackPanel Orientation="Horizontal">
                            <ui:Button Content="展开全部" 
                                       Command="{Binding ExpandAllCommand}"
                                       Icon="{ui:SymbolIcon FolderOpen24}"
                                       Appearance="Secondary"
                                       Margin="0,0,10,0"/>
                            
                            <ui:Button Content="折叠全部" 
                                       Command="{Binding CollapseAllCommand}"
                                       Icon="{ui:SymbolIcon Folder24}"
                                       Appearance="Secondary"
                                       Margin="0,0,10,0"/>
                            
                            <ui:Button Content="添加根节点" 
                                       Command="{Binding AddRootNodeCommand}"
                                       Icon="{ui:SymbolIcon Add24}"
                                       Appearance="Primary"
                                       Margin="0,0,10,0"/>
                            
                            <ui:Button Content="删除选中" 
                                       Command="{Binding DeleteSelectedNodeCommand}"
                                       Icon="{ui:SymbolIcon Delete24}"
                                       Appearance="Danger"
                                       Margin="0,0,10,0"/>
                        </StackPanel>
                    </Border>

                    <!-- 树形控件 -->
                    <ScrollViewer Grid.Row="1" 
                                  Padding="15"
                                  VerticalScrollBarVisibility="Auto"
                                  HorizontalScrollBarVisibility="Auto">
                        
                        <!-- TreeView 支持拖拽功能
                             关键属性说明：
                             - dd:DragDrop.IsDragSource="True": 启用拖拽源功能
                             - dd:DragDrop.IsDropTarget="True": 启用放置目标功能
                             - dd:DragDrop.DragHandler="{Binding}": 绑定拖拽处理器
                             - dd:DragDrop.DropHandler="{Binding}": 绑定放置处理器
                             - dd:DragDrop.UseDefaultDragAdorner="True": 使用默认拖拽装饰器
                        -->
                        <TreeView ItemsSource="{Binding TreeNodes}"

                                  dd:DragDrop.IsDragSource="True"
                                  dd:DragDrop.IsDropTarget="True"
                                  dd:DragDrop.DragHandler="{Binding}"
                                  dd:DragDrop.DropHandler="{Binding}"
                                  dd:DragDrop.UseDefaultDragAdorner="True"
                                  dd:DragDrop.DefaultDragAdornerOpacity="0.8">
                            
                            <TreeView.ItemTemplate>
                                <HierarchicalDataTemplate ItemsSource="{Binding Children}">
                                    <Border Background="Transparent"
                                            Padding="8,4"
                                            CornerRadius="4"
                                            Cursor="Hand">
                                        <Border.Style>
                                            <Style TargetType="Border">
                                                <Style.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background" 
                                                                Value="{DynamicResource ListViewItemBackgroundPointerOver}"/>
                                                    </Trigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Border.Style>
                                        
                                        <StackPanel Orientation="Horizontal">
                                            <!-- 节点图标 -->
                                            <ui:SymbolIcon Symbol="{Binding NodeType, Converter={StaticResource NodeTypeToIconConverter}}"
                                                           FontSize="16"
                                                           Margin="0,0,8,0"
                                                           VerticalAlignment="Center"/>
                                            
                                            <!-- 节点信息 -->
                                            <StackPanel>
                                                <TextBlock Text="{Binding Name}" 
                                                           FontWeight="Medium"
                                                           FontSize="14"/>
                                                <TextBlock Text="{Binding Description}" 
                                                           FontSize="11"
                                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                           Visibility="{Binding Description, Converter={StaticResource StringToVisibilityConverter}}"/>
                                            </StackPanel>
                                            
                                            <!-- 节点类型标签 -->
                                            <Border Background="{DynamicResource AccentFillColorSecondaryBrush}"
                                                    CornerRadius="10"
                                                    Padding="6,2"
                                                    Margin="10,0,0,0"
                                                    VerticalAlignment="Center">
                                                <TextBlock Text="{Binding NodeType}" 
                                                           FontSize="10"
                                                           Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"/>
                                            </Border>
                                        </StackPanel>
                                    </Border>
                                </HierarchicalDataTemplate>
                            </TreeView.ItemTemplate>
                        </TreeView>
                    </ScrollViewer>
                </Grid>
            </Border>

            <!-- 右侧：操作面板和代码示例 -->
            <StackPanel Grid.Column="2">
                <!-- 操作统计 -->
                <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                        BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Padding="15"
                        Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="📊 操作统计" 
                                   FontWeight="Bold" 
                                   FontSize="16"
                                   Margin="0,0,0,10"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="总节点:" Margin="0,2"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding TotalNodesCount}" 
                                       FontWeight="Bold" Margin="0,2"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="已展开:" Margin="0,2"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding ExpandedNodesCount}" 
                                       FontWeight="Bold" Margin="0,2"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="操作次数:" Margin="0,2"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding InteractionCount}" 
                                       FontWeight="Bold" Margin="0,2"/>
                        </Grid>

                        <TextBlock Text="{Binding LastAction, StringFormat='最后操作: {0}'}" 
                                   FontSize="12"
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   Margin="0,10,0,0"/>
                    </StackPanel>
                </Border>

                <!-- 快捷操作 -->
                <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                        BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Padding="15">
                    <StackPanel>
                        <TextBlock Text="⚡ 快捷操作" 
                                   FontWeight="Bold" 
                                   FontSize="16"
                                   Margin="0,0,0,10"/>

                        <ui:Button Content="重置数据" 
                                   Command="{Binding ResetDataCommand}"
                                   Icon="{ui:SymbolIcon ArrowReset24}"
                                   Appearance="Secondary"
                                   HorizontalAlignment="Stretch"
                                   Margin="0,0,0,8"/>

                        <ui:Button Content="重置计数" 
                                   Command="{Binding ResetCountCommand}"
                                   Icon="{ui:SymbolIcon NumberSymbol24}"
                                   Appearance="Secondary"
                                   HorizontalAlignment="Stretch"/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </Grid>

        <!-- 底部：使用说明 -->
        <Border Grid.Row="2"
                Background="{DynamicResource CardBackgroundFillColorSecondaryBrush}"
                BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                BorderThickness="1"
                CornerRadius="8"
                Padding="15"
                Margin="0,20,0,0">
            <StackPanel>
                <TextBlock Text="💡 使用说明" 
                           FontWeight="Bold" 
                           FontSize="14"
                           Margin="0,0,0,8"/>
                
                <TextBlock TextWrapping="Wrap" 
                           FontSize="12"
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}">
                    <Run Text="• 拖拽节点：点击并拖拽任意节点到其他位置"/>
                    <LineBreak/>
                    <Run Text="• 层级移动：将节点拖拽到文件夹节点上可改变层级关系"/>
                    <LineBreak/>
                    <Run Text="• 根级移动：将节点拖拽到空白区域可移动到根级"/>
                    <LineBreak/>
                    <Run Text="• 防循环：系统会自动防止将父节点拖拽到子节点上"/>
                    <LineBreak/>
                    <Run Text="• 实时反馈：拖拽过程中会显示视觉反馈和状态更新"/>
                </TextBlock>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
