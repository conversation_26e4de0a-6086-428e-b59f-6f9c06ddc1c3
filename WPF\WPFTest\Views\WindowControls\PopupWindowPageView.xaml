<UserControl x:Class="WPFTest.Views.WindowControls.PopupWindowPageView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             mvvm:ViewModelLocator.AutoWireViewModel="True"
             d:DesignHeight="800" d:DesignWidth="1200"
             xmlns:controls="clr-namespace:Zylo.WPF.Controls;assembly=Zylo.WPF"
             xmlns:windowControls="clr-namespace:WPFTest.ViewModels.WindowControls"
             xmlns:mvvm="http://prismlibrary.com/"
             d:DataContext="{d:DesignInstance Type=windowControls:PopupWindowPageViewModel}">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 标题区域 -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="弹出窗口示例" 
                       FontSize="28" 
                       FontWeight="Bold" 
                       Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
            <TextBlock Text="展示各种弹出窗口的实现方式：模态窗口、非模态窗口、自定义窗口等" 
                       FontSize="14" 
                       Foreground="{DynamicResource TextFillColorSecondaryBrush}" 
                       Margin="0,5,0,0"/>
        </StackPanel>

        <!-- 主内容区域 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>

                <!-- 基础弹出窗口 -->
                <ui:CardExpander Header="基础弹出窗口"
                                 Icon="{ui:SymbolIcon Window24}"
                                 IsExpanded="True"
                                 Margin="0,0,0,20">
                    <StackPanel Margin="16">
                        <TextBlock Text="基础的弹出窗口功能，包括模态和非模态窗口。"
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   Margin="0,0,0,12"/>

                        <WrapPanel Orientation="Horizontal" HorizontalAlignment="Left">
                            <ui:Button Content="模态窗口"
                                       Icon="{ui:SymbolIcon Window24}"
                                       Command="{Binding ShowModalWindowCommand}"
                                       Margin="0,0,8,8"/>

                            <ui:Button Content="非模态窗口"
                                       Icon="{ui:SymbolIcon Apps24}"
                                       Command="{Binding ShowModelessWindowCommand}"
                                       Margin="0,0,8,8"/>

                            <ui:Button Content="自定义大小窗口"
                                       Icon="{ui:SymbolIcon Settings24}"
                                       Command="{Binding ShowCustomSizeWindowCommand}"
                                       Margin="0,0,8,8"/>

                            <ui:Button Content="居中显示窗口"
                                       Icon="{ui:SymbolIcon Target24}"
                                       Command="{Binding ShowCenterWindowCommand}"
                                       Margin="0,0,8,8"/>

                            <ui:Button Content="功能完整窗口"
                                       Icon="{ui:SymbolIcon WindowNew24}"
                                       Command="{Binding ShowFeatureWindowCommand}"
                                       Margin="0,0,8,8"/>
                        </WrapPanel>
                    </StackPanel>
                </ui:CardExpander>

                <!-- 自定义内容窗口 -->
                <ui:CardExpander Header="自定义内容窗口"
                                 Icon="{ui:SymbolIcon Edit24}"
                                 Margin="0,0,0,20">
                    <StackPanel Margin="16">
                        <TextBlock Text="包含各种自定义内容的弹出窗口。"
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   Margin="0,0,0,12"/>

                        <WrapPanel Orientation="Horizontal" HorizontalAlignment="Left">
                            <ui:Button Content="表单输入窗口"
                                       Icon="{ui:SymbolIcon Document24}"
                                       Command="{Binding ShowFormWindowCommand}"
                                       Margin="0,0,8,8"/>

                            <ui:Button Content="数据展示窗口"
                                       Icon="{ui:SymbolIcon Table24}"
                                       Command="{Binding ShowDataWindowCommand}"
                                       Margin="0,0,8,8"/>

                            <ui:Button Content="图片预览窗口"
                                       Icon="{ui:SymbolIcon Image24}"
                                       Command="{Binding ShowImageWindowCommand}"
                                       Margin="0,0,8,8"/>

                            <ui:Button Content="设置配置窗口"
                                       Icon="{ui:SymbolIcon Settings24}"
                                       Command="{Binding ShowSettingsWindowCommand}"
                                       Margin="0,0,8,8"/>
                        </WrapPanel>
                    </StackPanel>
                </ui:CardExpander>

                <!-- 特殊功能窗口 -->
                <ui:CardExpander Header="特殊功能窗口"
                                 Icon="{ui:SymbolIcon Shield24}"
                                 Margin="0,0,0,20">
                    <StackPanel Margin="16">
                        <TextBlock Text="具有特殊功能的弹出窗口。"
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   Margin="0,0,0,12"/>

                        <WrapPanel Orientation="Horizontal" HorizontalAlignment="Left">
                            <ui:Button Content="进度显示窗口"
                                       Icon="{ui:SymbolIcon ArrowClockwise24}"
                                       Command="{Binding ShowProgressWindowCommand}"
                                       Margin="0,0,8,8"/>

                            <ui:Button Content="登录验证窗口"
                                       Icon="{ui:SymbolIcon Person24}"
                                       Command="{Binding ShowLoginWindowCommand}"
                                       Margin="0,0,8,8"/>

                            <ui:Button Content="文件选择窗口"
                                       Icon="{ui:SymbolIcon Folder24}"
                                       Command="{Binding ShowFilePickerWindowCommand}"
                                       Margin="0,0,8,8"/>

                            <ui:Button Content="关于窗口"
                                       Icon="{ui:SymbolIcon Info24}"
                                       Command="{Binding ShowAboutWindowCommand}"
                                       Margin="0,0,8,8"/>
                        </WrapPanel>
                    </StackPanel>
                </ui:CardExpander>

                <!-- 窗口管理 -->
                <ui:CardExpander Header="窗口管理"
                                 Icon="{ui:SymbolIcon Apps24}"
                                 Margin="0,0,0,20">
                    <StackPanel Margin="16">
                        <TextBlock Text="窗口管理和控制功能。"
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   Margin="0,0,0,12"/>

                        <WrapPanel Orientation="Horizontal" HorizontalAlignment="Left">
                            <ui:Button Content="关闭所有弹出窗口"
                                       Icon="{ui:SymbolIcon Dismiss24}"
                                       Command="{Binding CloseAllWindowsCommand}"
                                       Appearance="Danger"
                                       Margin="0,0,8,8"/>

                            <ui:Button Content="显示窗口列表"
                                       Icon="{ui:SymbolIcon List24}"
                                       Command="{Binding ShowWindowListCommand}"
                                       Margin="0,0,8,8"/>
                        </WrapPanel>
                        
                        <!-- 当前打开的窗口列表 -->
                        <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                                BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                BorderThickness="1"
                                CornerRadius="6"
                                Padding="12"
                                Margin="0,8,0,0">
                            <StackPanel>
                                <TextBlock Text="当前打开的窗口:" 
                                           FontWeight="SemiBold" 
                                           Margin="0,0,0,8"/>
                                <ItemsControl ItemsSource="{Binding OpenWindows}">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding}" 
                                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                       Margin="0,2"/>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                                <TextBlock Text="暂无打开的窗口" 
                                           Foreground="{DynamicResource TextFillColorTertiaryBrush}"
                                           FontStyle="Italic"
                                           Visibility="{Binding HasNoOpenWindows, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </ui:CardExpander>

                <!-- 操作状态 -->
                <ui:CardExpander Header="操作状态"
                                 Icon="{ui:SymbolIcon Info24}">
                    <StackPanel Margin="16">
                        <TextBlock Text="显示最近的窗口操作状态和结果。"
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   Margin="0,0,0,12"/>

                        <Border Background="{DynamicResource CardBackgroundFillColorSecondaryBrush}"
                                BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                BorderThickness="1"
                                CornerRadius="6"
                                Padding="12">
                            <StackPanel>
                                <TextBlock Text="最近操作:" 
                                           FontWeight="SemiBold" 
                                           Margin="0,0,0,8"/>
                                <TextBlock Text="{Binding LastOperation}" 
                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                           TextWrapping="Wrap"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </ui:CardExpander>

            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
