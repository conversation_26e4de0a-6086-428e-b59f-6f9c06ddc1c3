<!-- NumberBox 样式示例 -->
<StackPanel Margin="20">

    <!-- 基础样式 -->
    <GroupBox Header="基础样式" Padding="15">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- 标准样式 -->
            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                <TextBlock Text="标准样式" FontWeight="Bold" Margin="0,0,0,4"/>
                <ui:NumberBox Value="123.45"
                              PlaceholderText="标准样式"/>
            </StackPanel>
            
            <!-- 小型样式 -->
            <StackPanel Grid.Column="1" Margin="4,0,4,0">
                <TextBlock Text="小型样式" FontWeight="Bold" Margin="0,0,0,4"/>
                <ui:NumberBox Value="67.89"
                              PlaceholderText="小型样式"
                              Style="{StaticResource SmallNumberBoxStyle}"/>
            </StackPanel>

            <!-- 大型样式 -->
            <StackPanel Grid.Column="2" Margin="8,0,0,0">
                <TextBlock Text="大型样式" FontWeight="Bold" Margin="0,0,0,4"/>
                <ui:NumberBox Value="456.78"
                              PlaceholderText="大型样式"
                              Style="{StaticResource LargeNumberBoxStyle}"/>
            </StackPanel>
        </Grid>
    </GroupBox>

    <!-- 特殊样式 -->
    <GroupBox Header="特殊样式" Padding="15" Margin="0,0,0,15">
        <StackPanel>
            
            <!-- 透明样式 -->
            <StackPanel>
                <TextBlock Text="透明样式" FontWeight="Bold" Margin="0,0,0,4"/>
                <ui:NumberBox Value="789.12"
                              PlaceholderText="透明背景"
                              Style="{StaticResource TransparentNumberBoxStyle}"
                              Width="200"
                              HorizontalAlignment="Left"/>
            </StackPanel>

            <!-- 圆角样式 -->
            <StackPanel>
                <TextBlock Text="圆角样式" FontWeight="Bold" Margin="0,0,0,4"/>
                <ui:NumberBox Value="345.67"
                              PlaceholderText="圆角样式"
                              Style="{StaticResource RoundedNumberBoxStyle}"
                              Width="200"
                              HorizontalAlignment="Left"/>
            </StackPanel>

            <!-- 无边框样式 -->
            <StackPanel>
                <TextBlock Text="无边框样式" FontWeight="Bold" Margin="0,0,0,4"/>
                <ui:NumberBox Value="901.23"
                              PlaceholderText="无边框"
                              Style="{StaticResource BorderlessNumberBoxStyle}"
                              Width="200"
                              HorizontalAlignment="Left"/>
            </StackPanel>
            
            <!-- 货币样式 -->
            <StackPanel>
                <TextBlock Text="货币样式" FontWeight="Bold" Margin="0,0,0,4"/>
                <ui:NumberBox Value="1234.56"
                              PlaceholderText="货币金额"
                              Style="{StaticResource CurrencyNumberBoxStyle}"
                              Width="200"
                              HorizontalAlignment="Left"/>
            </StackPanel>

            <!-- 百分比样式 -->
            <StackPanel>
                <TextBlock Text="百分比样式" FontWeight="Bold" Margin="0,0,0,4"/>
                <ui:NumberBox Value="75.5"
                              PlaceholderText="百分比值"
                              Style="{StaticResource PercentageNumberBoxStyle}"
                              Width="200"
                              HorizontalAlignment="Left"/>
            </StackPanel>
        </StackPanel>
    </GroupBox>

    <!-- 状态样式 -->
    <GroupBox Header="状态样式" Padding="15">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- 左列 -->
            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                <!-- 只读状态 -->
                <StackPanel>
                    <TextBlock Text="只读状态" FontWeight="Bold" Margin="0,0,0,4"/>
                    <ui:NumberBox Value="123.45"
                                  IsReadOnly="True"
                                  PlaceholderText="只读数值"/>
                </StackPanel>
                
                <!-- 禁用状态 -->
                <StackPanel>
                    <TextBlock Text="禁用状态" FontWeight="Bold" Margin="0,0,0,4"/>
                    <ui:NumberBox Value="678.90"
                                  IsEnabled="False"
                                  PlaceholderText="禁用数值"/>
                </StackPanel>
            </StackPanel>
            
            <!-- 右列 -->
            <StackPanel Grid.Column="1" Margin="8,0,0,0">
                <!-- 错误状态 -->
                <StackPanel>
                    <TextBlock Text="错误状态" FontWeight="Bold" Margin="0,0,0,4"/>
                    <ui:NumberBox Value="999999"
                                  PlaceholderText="超出范围"
                                  BorderBrush="Red"
                                  BorderThickness="2"/>
                </StackPanel>
                
                <!-- 成功状态 -->
                <StackPanel>
                    <TextBlock Text="成功状态" FontWeight="Bold" Margin="0,0,0,4"/>
                    <ui:NumberBox Value="42.00"
                                  PlaceholderText="验证通过"
                                  BorderBrush="Green"
                                  BorderThickness="2"/>
                </StackPanel>
            </StackPanel>
        </Grid>
    </GroupBox>

    <!-- 自定义样式资源 -->
    <GroupBox Header="自定义样式资源" Padding="15">
        <StackPanel>
            <TextBlock Text="使用自定义样式资源：" FontWeight="Bold" Margin="0,0,0,8"/>
            
            <!-- 这里可以展示如何定义和使用自定义样式 -->
            <Border Background="{DynamicResource ControlFillColorTertiaryBrush}"
                    CornerRadius="4"
                    Padding="12">
                <StackPanel>
                    <TextBlock Text="💡 提示：" FontWeight="Bold" FontSize="12" Margin="0,0,0,4"/>
                    <TextBlock Text="可以通过定义 Style 资源来创建可重用的 NumberBox 样式。"
                               FontSize="11"
                               TextWrapping="Wrap"
                               Margin="0,0,0,4"/>
                    <TextBlock Text="例如：&lt;Style x:Key=&quot;CustomNumberBoxStyle&quot; TargetType=&quot;ui:NumberBox&quot;&gt;"
                               FontSize="10"
                               FontFamily="Consolas"
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                </StackPanel>
            </Border>
        </StackPanel>
    </GroupBox>

</StackPanel>
