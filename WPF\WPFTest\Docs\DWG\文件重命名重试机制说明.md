# 文件重命名重试机制说明

## 🎯 问题背景
在DWG文件管理中，重命名操作经常遇到"文件被其他进程占用"的错误，导致重命名失败。

### 常见错误日志
```
[2025-07-27 16:20:21.798] ERR [DwgManagerTabViewModel.RenameFileAsync] 
重命名文件失败: The process cannot access the file because it is being used by another process.
```

### 文件被占用的常见原因
1. **CAD软件占用** - 文件在AutoCAD、浩辰CAD等软件中打开
2. **Windows预览** - 资源管理器的预览功能锁定文件
3. **杀毒软件扫描** - 实时保护功能临时锁定文件
4. **其他程序** - 文件查看器、编辑器等程序占用

## ✅ 解决方案：重试机制

### 核心改进
```csharp
// 原来的简单重命名
File.Move(fileModel.FullPath, newPath);

// 改进后的重试机制
await RenameFileWithRetryAsync(fileModel.FullPath, newPath, newName);
```

### 重试机制特点
1. **最多重试3次** - 给临时占用足够的恢复时间
2. **延迟重试** - 每次重试间隔1秒
3. **智能错误处理** - 区分不同类型的错误
4. **用户友好提示** - 显示重试进度和详细错误信息

## 🔧 实现细节

### 重试逻辑
```csharp
private async Task RenameFileWithRetryAsync(string oldPath, string newPath, string newName)
{
    const int maxRetries = 3;
    const int delayMs = 1000;

    for (int attempt = 1; attempt <= maxRetries; attempt++)
    {
        try
        {
            // 尝试重命名
            File.Move(oldPath, newPath);
            
            // 成功后刷新文件列表
            LoadDwgFiles(TDwgFolderModel.Name);
            UpdateCurrentFiles();
            
            StatusMessage = $"✅ 文件已重命名: {newName}";
            return;
        }
        catch (IOException ex) when (ex.Message.Contains("being used by another process"))
        {
            if (attempt == maxRetries)
            {
                // 最后一次失败，显示详细错误
                await ShowFileInUseErrorAsync(newName);
                throw;
            }
            
            // 等待后重试
            StatusMessage = $"⏳ 文件被占用，{delayMs / 1000}秒后重试... (第{attempt}/{maxRetries}次)";
            await Task.Delay(delayMs);
        }
        catch (UnauthorizedAccessException ex)
        {
            // 权限问题，不重试
            StatusMessage = "❌ 重命名失败: 权限不足，请检查文件权限";
            throw;
        }
    }
}
```

### 错误分类处理
1. **IOException (文件占用)** - 重试处理
2. **UnauthorizedAccessException (权限不足)** - 立即失败，不重试
3. **其他异常** - 立即失败，记录详细错误

## 🎨 用户体验改进

### 状态消息提示
```
⏳ 文件被占用，1秒后重试... (第1/3次)
⏳ 文件被占用，1秒后重试... (第2/3次)
❌ 重命名失败: 文件被其他程序占用
```

### 详细错误对话框
当所有重试都失败时，显示用户友好的错误对话框：

```
标题: 文件被占用

内容: 无法重命名文件 'filename.dwg'。

可能的原因：
• 文件在CAD软件中打开
• 文件被Windows资源管理器预览
• 文件被其他程序使用

请关闭相关程序后重试。
```

## 📊 重试策略分析

### 重试参数选择
- **重试次数: 3次** - 平衡用户等待时间和成功率
- **重试间隔: 1秒** - 给临时占用足够恢复时间
- **总等待时间: 最多3秒** - 用户可接受的等待时间

### 成功率提升
- **临时占用** - 通常在1-2秒内释放，重试可解决
- **预览锁定** - 切换文件夹或关闭预览后释放
- **杀毒扫描** - 扫描完成后自动释放

## 🔍 日志记录改进

### 详细的重试日志
```
[INFO] 🔄 尝试重命名文件 (第1次): 原文件.dwg -> 新文件.dwg
[WARN] ⚠️ 文件被占用 (第1次尝试): The process cannot access the file...
[INFO] 🔄 尝试重命名文件 (第2次): 原文件.dwg -> 新文件.dwg
[INFO] ✅ 文件重命名成功: 原文件.dwg -> 新文件.dwg
```

### 日志级别
- **INFO** - 正常的重试尝试
- **WARN** - 可重试的错误
- **ERROR** - 不可重试的错误

## 🎯 使用场景

### 适用情况
1. **CAD文件管理** - DWG文件经常被CAD软件占用
2. **批量操作** - 大量文件重命名时的稳定性
3. **网络文件** - 网络延迟可能导致临时占用

### 不适用情况
1. **权限问题** - 需要管理员权限的操作
2. **磁盘空间不足** - 硬件限制问题
3. **文件损坏** - 文件系统错误

## 📝 最佳实践

### 用户操作建议
1. **关闭CAD软件** - 重命名前确保文件未在CAD中打开
2. **切换文件夹** - 避免Windows预览功能锁定
3. **等待完成** - 不要在重试过程中进行其他文件操作

### 开发建议
1. **异步操作** - 避免阻塞UI线程
2. **用户反馈** - 及时显示操作状态
3. **错误分类** - 区分可重试和不可重试的错误
4. **日志记录** - 详细记录重试过程便于调试

## 🚀 扩展可能

### 未来改进方向
1. **智能重试间隔** - 根据错误类型调整重试间隔
2. **进程检测** - 检测占用文件的具体进程
3. **批量重试** - 支持批量文件操作的重试
4. **配置化参数** - 允许用户自定义重试次数和间隔

通过这个重试机制，大大提高了DWG文件重命名操作的成功率和用户体验。
