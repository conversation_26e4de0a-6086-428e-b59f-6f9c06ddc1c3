using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;
using Zylo.WPF.Models.Navigation;

namespace Zylo.WPF.Converters;

/// <summary>
/// 面包屑样式转换器 - 判断是否为当前项
/// </summary>
public class BreadcrumbStyleConverter : IMultiValueConverter
{
    public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
    {
        if (values.Length != 2 || values[0] is not ZyloNavigationItemModel currentItem || values[1] is not ZyloNavigationItemModel parentItem)
        {
            return GetDefaultValue(targetType);
        }

        bool isCurrentItem = currentItem == parentItem;

        if (targetType == typeof(Brush))
        {
            // 返回颜色：当前项用强调色，其他用次要色
            if (isCurrentItem)
            {
                return Application.Current.Resources["SystemAccentColorPrimaryBrush"] as Brush 
                       ?? new SolidColorBrush(Colors.Blue);
            }
            else
            {
                return Application.Current.Resources["TextFillColorSecondaryBrush"] as Brush 
                       ?? new SolidColorBrush(Colors.Gray);
            }
        }
        else if (targetType == typeof(FontWeight))
        {
            // 返回字体粗细：当前项用半粗体，其他用普通
            return isCurrentItem ? FontWeights.SemiBold : FontWeights.Normal;
        }

        return GetDefaultValue(targetType);
    }

    public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }

    private static object GetDefaultValue(Type targetType)
    {
        if (targetType == typeof(Brush))
        {
            return new SolidColorBrush(Colors.Gray);
        }
        else if (targetType == typeof(FontWeight))
        {
            return FontWeights.Normal;
        }
        return DependencyProperty.UnsetValue;
    }
}
