using System.Windows;
using Zylo.WPF.Controls;
using Zylo.WPF.Services;

namespace Zylo.WPF.Behaviors;

/// <summary>
/// Snackbar 服务行为 - 符合 MVVM 的自动绑定
/// </summary>
public static class SnackbarServiceBehavior
{
    #region SnackbarService 附加属性

    /// <summary>
    /// SnackbarService 附加属性
    /// </summary>
    public static readonly DependencyProperty SnackbarServiceProperty =
        DependencyProperty.RegisterAttached(
            "SnackbarService",
            typeof(ISnackbarService),
            typeof(SnackbarServiceBehavior),
            new PropertyMetadata(null, OnSnackbarServiceChanged)
        );

    /// <summary>
    /// 获取 SnackbarService
    /// </summary>
    public static ISnackbarService? GetSnackbarService(DependencyObject obj)
    {
        return (ISnackbarService?)obj.GetValue(SnackbarServiceProperty);
    }

    /// <summary>
    /// 设置 SnackbarService
    /// </summary>
    public static void SetSnackbarService(DependencyObject obj, ISnackbarService? value)
    {
        obj.SetValue(SnackbarServiceProperty, value);
    }

    /// <summary>
    /// SnackbarService 属性改变回调
    /// </summary>
    private static void OnSnackbarServiceChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is ZyloSnackbar snackbar && e.NewValue is ISnackbarService service)
        {
            // 自动将 Snackbar 控件注册到服务
            service.SetSnackbar(snackbar);
        }
    }

    #endregion

    #region AutoBind 附加属性

    /// <summary>
    /// AutoBind 附加属性 - 自动绑定到 DataContext 中的 SnackbarService
    /// </summary>
    public static readonly DependencyProperty AutoBindProperty =
        DependencyProperty.RegisterAttached(
            "AutoBind",
            typeof(bool),
            typeof(SnackbarServiceBehavior),
            new PropertyMetadata(false, OnAutoBindChanged)
        );

    /// <summary>
    /// 获取 AutoBind
    /// </summary>
    public static bool GetAutoBind(DependencyObject obj)
    {
        return (bool)obj.GetValue(AutoBindProperty);
    }

    /// <summary>
    /// 设置 AutoBind
    /// </summary>
    public static void SetAutoBind(DependencyObject obj, bool value)
    {
        obj.SetValue(AutoBindProperty, value);
    }

    /// <summary>
    /// AutoBind 属性改变回调
    /// </summary>
    private static void OnAutoBindChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is ZyloSnackbar snackbar && (bool)e.NewValue)
        {
            // 监听 DataContext 变化
            snackbar.DataContextChanged += OnSnackbarDataContextChanged;

            // 监听 Loaded 事件，确保控件完全加载后再绑定
            snackbar.Loaded += OnSnackbarLoaded;

            // 如果已经有 DataContext，立即绑定
            if (snackbar.DataContext != null)
            {
                TryBindSnackbarService(snackbar);
            }
        }
        else if (d is ZyloSnackbar snackbar2 && !(bool)e.NewValue)
        {
            // 取消监听
            snackbar2.DataContextChanged -= OnSnackbarDataContextChanged;
            snackbar2.Loaded -= OnSnackbarLoaded;
        }
    }

    /// <summary>
    /// Snackbar 加载完成事件
    /// </summary>
    private static void OnSnackbarLoaded(object sender, RoutedEventArgs e)
    {
        if (sender is ZyloSnackbar snackbar)
        {
            // 控件加载完成后，确保绑定成功
            TryBindSnackbarService(snackbar);
        }
    }

    /// <summary>
    /// Snackbar DataContext 改变事件
    /// </summary>
    private static void OnSnackbarDataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
    {
        if (sender is ZyloSnackbar snackbar)
        {
            TryBindSnackbarService(snackbar);
        }
    }

    /// <summary>
    /// 尝试绑定 SnackbarService
    /// </summary>
    private static void TryBindSnackbarService(ZyloSnackbar snackbar)
    {
        // 通过反射查找 DataContext 中的 SnackbarService 字段
        var dataContext = snackbar.DataContext;
        if (dataContext == null) return;

        // 查找 protected readonly ISnackbarService SnackbarService 字段
        var serviceField = dataContext.GetType().GetField("SnackbarService",
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

        if (serviceField?.GetValue(dataContext) is ISnackbarService service)
        {
            service.SetSnackbar(snackbar);
        }
        else
        {
            // 如果找不到字段，尝试从容器获取服务
            try
            {
                var containerService = ZyloContainer.Resolve<ISnackbarService>();
                containerService.SetSnackbar(snackbar);
            }
            catch
            {
                // 最后的降级处理
                var defaultService = new SnackbarService();
                defaultService.SetSnackbar(snackbar);
            }
        }
    }

    #endregion
}
