using System;
using System.Collections.ObjectModel;
using System.Reflection;
using System.Windows.Threading;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Wpf.Ui.Controls;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels.MediaControls
{
    /// <summary>
    /// InfoBadge 控件示例页面 ViewModel
    /// 使用 CommunityToolkit.MVVM 8.4.0 的 Partial Properties 语法
    /// </summary>
    public partial class InfoBadgePageViewModel : ObservableObject
    {
        #region 私有字段

        private readonly YLoggerInstance _logger = YLogger.For<ImagePageViewModel>();
        private readonly DispatcherTimer _animationTimer;
        private readonly Random _random = new();

        #endregion

        #region 构造函数

        public InfoBadgePageViewModel()
        {
      
            
            // 初始化动画计时器
            _animationTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(2)
            };
            _animationTimer.Tick += OnAnimationTimerTick;

            // 初始化默认值
            InitializeDefaults();
            
            _logger.Info("✅ InfoBadgePageViewModel 初始化完成");
        }

        #endregion

        #region 状态属性

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        public partial string StatusMessage { get; set; } = "欢迎使用 InfoBadge 控件示例！";

        #endregion

        #region InfoBadge 控制属性

        /// <summary>
        /// InfoBadge 数值（用于数字徽章）
        /// </summary>
        [ObservableProperty]
        public partial int BadgeValue { get; set; } = 5;

        /// <summary>
        /// InfoBadge 图标源（用于图标徽章）
        /// </summary>
        [ObservableProperty]
        public partial string BadgeIconSource { get; set; } = "Mail24";

        /// <summary>
        /// InfoBadge 类型
        /// </summary>
        [ObservableProperty]
        public partial InfoBadgeType BadgeType { get; set; } = InfoBadgeType.Numeric;

        /// <summary>
        /// 是否显示 InfoBadge
        /// </summary>
        [ObservableProperty]
        public partial bool ShowInfoBadge { get; set; } = true;

        /// <summary>
        /// InfoBadge 背景色
        /// </summary>
        [ObservableProperty]
        public partial string BadgeBackground { get; set; } = "#FF0078D4";

        /// <summary>
        /// InfoBadge 前景色
        /// </summary>
        [ObservableProperty]
        public partial string BadgeForeground { get; set; } = "White";

        /// <summary>
        /// InfoBadge 宽度
        /// </summary>
        [ObservableProperty]
        public partial double BadgeWidth { get; set; } = 20;

        /// <summary>
        /// InfoBadge 高度
        /// </summary>
        [ObservableProperty]
        public partial double BadgeHeight { get; set; } = 20;

        /// <summary>
        /// 通知计数
        /// </summary>
        [ObservableProperty]
        public partial int NotificationCount { get; set; } = 3;

        /// <summary>
        /// 邮件计数
        /// </summary>
        [ObservableProperty]
        public partial int MailCount { get; set; } = 12;

        /// <summary>
        /// 消息计数
        /// </summary>
        [ObservableProperty]
        public partial int MessageCount { get; set; } = 8;

        /// <summary>
        /// 是否启用动画
        /// </summary>
        [ObservableProperty]
        public partial bool IsAnimationEnabled { get; set; } = false;

        #endregion

        #region 选项集合

        /// <summary>
        /// InfoBadge 类型选项
        /// </summary>
        public ObservableCollection<InfoBadgeTypeOption> BadgeTypeOptions { get; } = new()
        {
            new("Dot", InfoBadgeType.Dot, "点状徽章"),
            new("Icon", InfoBadgeType.Icon, "图标徽章"),
            new("Numeric", InfoBadgeType.Numeric, "数字徽章")
        };

        /// <summary>
        /// 图标选项
        /// </summary>
        public ObservableCollection<IconOption> IconOptions { get; } = new()
        {
            new("Mail24", "邮件"),
            new("Alert24", "警告"),
            new("Checkmark24", "完成"),
            new("Info24", "信息"),
            new("Warning24", "警告"),
            new("Heart24", "喜欢"),
            new("Star24", "收藏"),
            new("Settings24", "设置")
        };

        /// <summary>
        /// 预设背景色选项
        /// </summary>
        public ObservableCollection<ColorOption> BackgroundColorOptions { get; } = new()
        {
            new("#FF0078D4", "蓝色"),
            new("#FF107C10", "绿色"),
            new("#FFFF4343", "红色"),
            new("#FFFF8C00", "橙色"),
            new("#FF744DA9", "紫色"),
            new("#FF00BCF2", "青色"),
            new("#FF767676", "灰色")
        };

        #endregion

        #region 代码示例属性

        /// <summary>
        /// 基础 InfoBadge XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicInfoBadgeXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 基础 InfoBadge C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicInfoBadgeCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级 InfoBadge XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedInfoBadgeXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级 InfoBadge C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedInfoBadgeCSharpExample { get; set; } = string.Empty;

        #endregion

        #region 命令

        /// <summary>
        /// 应用 InfoBadge 类型命令
        /// </summary>
        [RelayCommand]
        private void ApplyBadgeType()
        {
            try
            {
                StatusMessage = $"已切换到 {BadgeType} 类型的 InfoBadge";
                _logger.Info($"应用 InfoBadge 类型: {BadgeType}");
            }
            catch (Exception ex)
            {
                StatusMessage = $"应用 InfoBadge 类型失败: {ex.Message}";
                _logger.Error($"❌ 应用 InfoBadge 类型时出错: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 增加数值命令
        /// </summary>
        [RelayCommand]
        private void IncreaseValue()
        {
            try
            {
                BadgeValue++;
                NotificationCount++;
                StatusMessage = $"数值已增加到: {BadgeValue}";
                _logger.Info($"InfoBadge 数值增加到: {BadgeValue}");
            }
            catch (Exception ex)
            {
                StatusMessage = $"增加数值失败: {ex.Message}";
                _logger.Error($"❌ 增加数值时出错: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 减少数值命令
        /// </summary>
        [RelayCommand]
        private void DecreaseValue()
        {
            try
            {
                if (BadgeValue > 0)
                {
                    BadgeValue--;
                }
                if (NotificationCount > 0)
                {
                    NotificationCount--;
                }
                StatusMessage = $"数值已减少到: {BadgeValue}";
                _logger.Info($"InfoBadge 数值减少到: {BadgeValue}");
            }
            catch (Exception ex)
            {
                StatusMessage = $"减少数值失败: {ex.Message}";
                _logger.Error($"❌ 减少数值时出错: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 重置数值命令
        /// </summary>
        [RelayCommand]
        private void ResetValue()
        {
            try
            {
                BadgeValue = 0;
                NotificationCount = 0;
                MailCount = 0;
                MessageCount = 0;
                StatusMessage = "所有计数已重置";
                _logger.Info("InfoBadge 数值已重置");
            }
            catch (Exception ex)
            {
                StatusMessage = $"重置数值失败: {ex.Message}";
                _logger.Error($"❌ 重置数值时出错: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 切换显示命令
        /// </summary>
        [RelayCommand]
        private void ToggleVisibility()
        {
            try
            {
                ShowInfoBadge = !ShowInfoBadge;
                StatusMessage = $"InfoBadge 显示状态: {(ShowInfoBadge ? "显示" : "隐藏")}";
                _logger.Info($"InfoBadge 显示状态: {ShowInfoBadge}");
            }
            catch (Exception ex)
            {
                StatusMessage = $"切换显示失败: {ex.Message}";
                _logger.Error($"❌ 切换显示时出错: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 切换动画命令
        /// </summary>
        [RelayCommand]
        private void ToggleAnimation()
        {
            try
            {
                IsAnimationEnabled = !IsAnimationEnabled;
                if (IsAnimationEnabled)
                {
                    _animationTimer.Start();
                    StatusMessage = "动画已启用";
                }
                else
                {
                    _animationTimer.Stop();
                    StatusMessage = "动画已停用";
                }
                _logger.Info($"InfoBadge 动画状态: {IsAnimationEnabled}");
            }
            catch (Exception ex)
            {
                StatusMessage = $"切换动画失败: {ex.Message}";
                _logger.Error($"❌ 切换动画时出错: {ex.Message}", ex);
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化默认值
        /// </summary>
        private void InitializeDefaults()
        {
            try
            {
                // 加载代码示例
                LoadCodeExamples();
                
                _logger.Info("默认值设置完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 设置默认值时出错: {ex.Message}", ex);
                StatusMessage = "初始化默认值时出错";
            }
        }

        /// <summary>
        /// 加载代码示例
        /// </summary>
        private void LoadCodeExamples()
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                
                // 加载基础示例
                BasicInfoBadgeXamlExample = LoadEmbeddedResource(assembly, "WPFTest.CodeExamples.Controls.InfoBadge.BasicInfoBadge.xaml.txt");
                BasicInfoBadgeCSharpExample = LoadEmbeddedResource(assembly, "WPFTest.CodeExamples.Controls.InfoBadge.BasicInfoBadge.cs.txt");
                
                // 加载高级示例
                AdvancedInfoBadgeXamlExample = LoadEmbeddedResource(assembly, "WPFTest.CodeExamples.Controls.InfoBadge.AdvancedInfoBadge.xaml.txt");
                AdvancedInfoBadgeCSharpExample = LoadEmbeddedResource(assembly, "WPFTest.CodeExamples.Controls.InfoBadge.AdvancedInfoBadge.cs.txt");
                
                _logger.Info("代码示例加载完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 加载代码示例时出错: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 加载嵌入式资源
        /// </summary>
        private string LoadEmbeddedResource(Assembly assembly, string resourceName)
        {
            try
            {
                using var stream = assembly.GetManifestResourceStream(resourceName);
                if (stream == null)
                {
                    _logger.Error($"❌ 未找到嵌入式资源: {resourceName}", null);
                    return $"// 未找到资源: {resourceName}";
                }

                using var reader = new System.IO.StreamReader(stream);
                return reader.ReadToEnd();
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 加载嵌入式资源失败: {resourceName}, 错误: {ex.Message}", ex);
                return $"// 加载资源失败: {resourceName}";
            }
        }

        /// <summary>
        /// 动画计时器事件处理
        /// </summary>
        private void OnAnimationTimerTick(object? sender, EventArgs e)
        {
            try
            {
                // 随机更新计数
                var increment = _random.Next(1, 4);
                NotificationCount += increment;
                MailCount += _random.Next(0, 3);
                MessageCount += _random.Next(0, 2);
                
                // 限制最大值
                if (NotificationCount > 99) NotificationCount = 1;
                if (MailCount > 50) MailCount = 1;
                if (MessageCount > 30) MessageCount = 1;
                
                StatusMessage = $"动画更新 - 通知: {NotificationCount}, 邮件: {MailCount}, 消息: {MessageCount}";
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 动画计时器处理时出错: {ex.Message}", ex);
            }
        }

        #endregion
    }

    #region 辅助类

    /// <summary>
    /// InfoBadge 类型枚举
    /// </summary>
    public enum InfoBadgeType
    {
        Dot,
        Icon,
        Numeric
    }

    /// <summary>
    /// InfoBadge 类型选项
    /// </summary>
    public record InfoBadgeTypeOption(string Name, InfoBadgeType Type, string Description);

    /// <summary>
    /// 图标选项
    /// </summary>
    public record IconOption(string Icon, string Name);

    /// <summary>
    /// 颜色选项
    /// </summary>
    public record ColorOption(string Color, string Name);

    #endregion
}
