<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 📄 页面级动画 - 针对特定页面布局 -->
    
    <!-- ========================================= -->
    <!-- 🏠 HomePageView 专用动画 -->
    <!-- ========================================= -->
    
    <!-- HomePageView 页面加载动画 -->
    <Storyboard x:Key="ZyloHomePageLoadAnimation">
        <!-- 顶部横幅动画 -->
        <DoubleAnimation Storyboard.TargetName="TopBanner"
                       Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                       From="-50" To="0" Duration="0:0:0.6">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetName="TopBanner"
                       Storyboard.TargetProperty="Opacity"
                       From="0" To="1" Duration="0:0:0.6"/>

        <!-- 移除了最近使用导航区域动画 -->

        <!-- 内容区域动画 -->
        <DoubleAnimation Storyboard.TargetName="ContentSection"
                       Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                       From="40" To="0" Duration="0:0:0.7"
                       BeginTime="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetName="ContentSection"
                       Storyboard.TargetProperty="Opacity"
                       From="0" To="1" Duration="0:0:0.7"
                       BeginTime="0:0:0.3"/>
    </Storyboard>

    <!-- ========================================= -->
    <!-- 📋 SubMenuView 专用动画 -->
    <!-- ========================================= -->
    
    <!-- SubMenuView 页面加载动画 -->
    <Storyboard x:Key="ZyloSubMenuPageLoadAnimation">
        <!-- 顶部横幅动画 -->
        <DoubleAnimation Storyboard.TargetName="TopBanner"
                       Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                       From="-50" To="0" Duration="0:0:0.6">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetName="TopBanner"
                       Storyboard.TargetProperty="Opacity"
                       From="0" To="1" Duration="0:0:0.6"/>

        <!-- 面包屑动画 -->
        <DoubleAnimation Storyboard.TargetName="BreadcrumbSection"
                       Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                       From="30" To="0" Duration="0:0:0.5"
                       BeginTime="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetName="BreadcrumbSection"
                       Storyboard.TargetProperty="Opacity"
                       From="0" To="1" Duration="0:0:0.5"
                       BeginTime="0:0:0.2"/>

        <!-- 内容区域动画 -->
        <DoubleAnimation Storyboard.TargetName="ContentSection"
                       Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                       From="40" To="0" Duration="0:0:0.7"
                       BeginTime="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetName="ContentSection"
                       Storyboard.TargetProperty="Opacity"
                       From="0" To="1" Duration="0:0:0.7"
                       BeginTime="0:0:0.3"/>
    </Storyboard>

    <!-- ========================================= -->
    <!-- 🎨 装饰动画 -->
    <!-- ========================================= -->
    
    <!-- 装饰圆点动画 - 背景装饰效果 -->
    <Storyboard x:Key="ZyloDotsAnimation" RepeatBehavior="Forever">
        <DoubleAnimation Storyboard.TargetName="Dot1"
                       Storyboard.TargetProperty="Opacity"
                       From="0.3" To="0.8" Duration="0:0:2"
                       AutoReverse="True"/>
        <DoubleAnimation Storyboard.TargetName="Dot2"
                       Storyboard.TargetProperty="Opacity"
                       From="0.2" To="0.6" Duration="0:0:2.5"
                       BeginTime="0:0:0.5"
                       AutoReverse="True"/>
        <DoubleAnimation Storyboard.TargetName="Dot3"
                       Storyboard.TargetProperty="Opacity"
                       From="0.4" To="0.9" Duration="0:0:1.8"
                       BeginTime="0:0:1"
                       AutoReverse="True"/>
        <DoubleAnimation Storyboard.TargetName="Dot4"
                       Storyboard.TargetProperty="Opacity"
                       From="0.25" To="0.7" Duration="0:0:2.2"
                       BeginTime="0:0:0.3"
                       AutoReverse="True"/>
    </Storyboard>

    <!-- ========================================= -->
    <!-- 🔄 通用页面动画模板 -->
    <!-- ========================================= -->
    
    <!-- 通用页面加载动画 - 只包含基本元素 -->
    <Storyboard x:Key="ZyloPageLoadAnimation">
        <!-- 顶部横幅动画 -->
        <DoubleAnimation Storyboard.TargetName="TopBanner"
                       Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                       From="-50" To="0" Duration="0:0:0.6">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetName="TopBanner"
                       Storyboard.TargetProperty="Opacity"
                       From="0" To="1" Duration="0:0:0.6"/>

        <!-- 内容区域动画 -->
        <DoubleAnimation Storyboard.TargetName="ContentSection"
                       Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                       From="40" To="0" Duration="0:0:0.7"
                       BeginTime="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetName="ContentSection"
                       Storyboard.TargetProperty="Opacity"
                       From="0" To="1" Duration="0:0:0.7"
                       BeginTime="0:0:0.3"/>
    </Storyboard>

</ResourceDictionary>
