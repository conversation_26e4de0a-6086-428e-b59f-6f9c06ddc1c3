<UserControl x:Class="WPFTest.Views.IconTestPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:converters="clr-namespace:Zylo.WPF.Converters;assembly=Zylo.WPF"
             xmlns:enums="clr-namespace:Zylo.WPF.Enums;assembly=Zylo.WPF"
             xmlns:zyloIcon="clr-namespace:Zylo.WPF.Controls.Icon;assembly=Zylo.WPF"
             mc:Ignorable="d" 
             d:DesignHeight="1500" d:DesignWidth="800">
    
    <UserControl.Resources>
        <converters:SimpleIconConverter x:Key="SimpleIconConverter" />
    </UserControl.Resources>
    
    <ScrollViewer>
        <StackPanel Margin="20">
            <TextBlock Text="🎯 图标测试页面" FontSize="32" FontWeight="Bold" Foreground="{DynamicResource SystemAccentColorPrimaryBrush}" Margin="0,0,0,20"/>
            
            <!-- WPF-UI 图标测试 -->
            <GroupBox Header="WPF-UI 图标测试" Margin="0,0,0,20">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,10">
                        <TextBlock Text="Home24: " VerticalAlignment="Center" Width="100"/>
                        <ui:SymbolIcon Symbol="Home24" FontSize="24"/>
                    </StackPanel>
                    
                    <StackPanel Orientation="Horizontal" Margin="0,10">
                        <TextBlock Text="Settings24: " VerticalAlignment="Center" Width="100"/>
                        <ui:SymbolIcon Symbol="Settings24" FontSize="24"/>
                    </StackPanel>
                </StackPanel>
            </GroupBox>
            
            <!-- Zylo 自定义图标测试 -->
            <GroupBox Header="Zylo 自定义图标测试" Margin="0,0,0,20">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,10">
                        <TextBlock Text="DWG (ZyloIcon默认): " VerticalAlignment="Center" Width="150"/>
                        <zyloIcon:ZyloIcon Symbol="Dwg"/>
                    </StackPanel>

                    <StackPanel Orientation="Horizontal" Margin="0,10">
                        <TextBlock Text="Notes (ZyloIcon默认): " VerticalAlignment="Center" Width="150"/>
                        <zyloIcon:ZyloIcon Symbol="Notes"/>
                    </StackPanel>

                    <StackPanel Orientation="Horizontal" Margin="0,10">
                        <TextBlock Text="Year (ZyloIcon默认): " VerticalAlignment="Center" Width="150"/>
                        <zyloIcon:ZyloIcon Symbol="Year"/>
                    </StackPanel>

                    <StackPanel Orientation="Horizontal" Margin="0,10">
                        <TextBlock Text="Buildings (ZyloIcon默认): " VerticalAlignment="Center" Width="150"/>
                        <zyloIcon:ZyloIcon Symbol="Buildings"/>
                    </StackPanel>

                    <StackPanel Orientation="Horizontal" Margin="0,10">
                        <TextBlock Text="Project (ZyloIcon默认): " VerticalAlignment="Center" Width="150"/>
                        <zyloIcon:ZyloIcon Symbol="Project"/>
                    </StackPanel>

                    <StackPanel Orientation="Horizontal" Margin="0,10">
                        <TextBlock Text="ICO (ZyloIcon): " VerticalAlignment="Center" Width="150"/>
                        <zyloIcon:ZyloIcon Symbol="ICO" FontSize="20"/>
                    </StackPanel>
                    
                    <TextBlock Text="对比 - 原始FontIcon:" FontWeight="Bold" Margin="0,10,0,5"/>

                    <StackPanel Orientation="Horizontal" Margin="0,10">
                        <TextBlock Text="DWG (FontIcon): " VerticalAlignment="Center" Width="150"/>
                        <ui:FontIcon Glyph="&#xE661;" FontFamily="/Zylo.WPF;component/Assets/Font/#iconfont" FontSize="20"/>
                    </StackPanel>

                    <StackPanel Orientation="Horizontal" Margin="0,10">
                        <TextBlock Text="Notes (FontIcon): " VerticalAlignment="Center" Width="150"/>
                        <ui:FontIcon Glyph="&#xE6F3;" FontFamily="/Zylo.WPF;component/Assets/Font/#iconfont" FontSize="24"/>
                    </StackPanel>

                    <StackPanel Orientation="Horizontal" Margin="0,10">
                        <TextBlock Text="Year (FontIcon): " VerticalAlignment="Center" Width="150"/>
                        <ui:FontIcon Glyph="&#xE621;" FontFamily="/Zylo.WPF;component/Assets/Font/#iconfont" FontSize="24"/>
                    </StackPanel>

                    <StackPanel Orientation="Horizontal" Margin="0,10">
                        <TextBlock Text="Buildings (FontIcon): " VerticalAlignment="Center" Width="150"/>
                        <ui:FontIcon Glyph="&#xe63f;" FontFamily="/Zylo.WPF;component/Assets/Font/#iconfont" FontSize="24"/>
                    </StackPanel>

                    <StackPanel Orientation="Horizontal" Margin="0,10">
                        <TextBlock Text="Project (FontIcon): " VerticalAlignment="Center" Width="150"/>
                        <ui:FontIcon Glyph="&#xe628;" FontFamily="/Zylo.WPF;component/Assets/Font/#iconfont" FontSize="24"/>
                    </StackPanel>
                </StackPanel>
            </GroupBox>
            
            <!-- 转换器测试 -->
            <GroupBox Header="转换器测试" Margin="0,0,0,20">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,10">
                        <TextBlock Text="转换器 - WPF-UI: " VerticalAlignment="Center" Width="150"/>
                        <ContentPresenter>
                            <ContentPresenter.Content>
                                <MultiBinding Converter="{StaticResource SimpleIconConverter}">
                                    <Binding Source="{x:Static ui:SymbolRegular.Home24}"/>
                                    <Binding Source="{x:Null}"/>
                                    <Binding Source="{x:Null}"/>
                                </MultiBinding>
                            </ContentPresenter.Content>
                        </ContentPresenter>
                    </StackPanel>
                    
                    <StackPanel Orientation="Horizontal" Margin="0,10">
                        <TextBlock Text="转换器 - Zylo: " VerticalAlignment="Center" Width="150"/>
                        <ContentPresenter>
                            <ContentPresenter.Content>
                                <MultiBinding Converter="{StaticResource SimpleIconConverter}">
                                    <Binding Source="{x:Null}"/>
                                    <Binding Source="{x:Static enums:ZyloSymbol.Dwg}"/>
                                    <Binding Source="{x:Null}"/>
                                </MultiBinding>
                            </ContentPresenter.Content>
                        </ContentPresenter>
                    </StackPanel>
                    
                    <StackPanel Orientation="Horizontal" Margin="0,10">
                        <TextBlock Text="转换器 - Emoji: " VerticalAlignment="Center" Width="150"/>
                        <ContentPresenter>
                            <ContentPresenter.Content>
                                <MultiBinding Converter="{StaticResource SimpleIconConverter}">
                                    <Binding Source="{x:Null}"/>
                                    <Binding Source="{x:Null}"/>
                                    <Binding Source="🏠"/>
                                </MultiBinding>
                            </ContentPresenter.Content>
                        </ContentPresenter>
                    </StackPanel>
                </StackPanel>
            </GroupBox>
            
            <!-- Emoji 测试 -->
            <GroupBox Header="Emoji 测试">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,10">
                        <TextBlock Text="直接 Emoji: " VerticalAlignment="Center" Width="100"/>
                        <TextBlock Text="🏠⚙️📊🎨" FontSize="24"/>
                    </StackPanel>
                </StackPanel>
            </GroupBox>
        </StackPanel>
    </ScrollViewer>
</UserControl>
