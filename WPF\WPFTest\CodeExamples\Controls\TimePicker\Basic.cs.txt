// TimePicker C# 基础用法示例
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace WPFTest.ViewModels.InputControls
{
    public partial class TimePickerPageViewModel : ObservableObject
    {
        /// <summary>
        /// 选择的时间
        /// </summary>
        [ObservableProperty]
        private DateTime? selectedTime = DateTime.Now;

        /// <summary>
        /// 工作时间
        /// </summary>
        [ObservableProperty]
        private DateTime? workTime = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 9, 0, 0);

        /// <summary>
        /// 提醒时间
        /// </summary>
        [ObservableProperty]
        private DateTime? reminderTime = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 14, 30, 0);

        /// <summary>
        /// TimePicker 交互命令
        /// </summary>
        [RelayCommand]
        private void HandleTimePickerInteraction(string parameter)
        {
            var message = parameter switch
            {
                "设置当前时间" => $"🕐 设置为当前时间: {DateTime.Now:HH:mm:ss}",
                "清除时间" => "🗑️ 已清除选择的时间",
                "设置工作时间" => $"💼 设置工作时间: {WorkTime:HH:mm}",
                "设置提醒时间" => $"⏰ 设置提醒时间: {ReminderTime:HH:mm}",
                _ => $"🔘 执行了操作: {parameter}"
            };

            StatusMessage = message;
            InteractionCount++;
        }

        /// <summary>
        /// 设置当前时间
        /// </summary>
        [RelayCommand]
        private void SetCurrentTime()
        {
            SelectedTime = DateTime.Now;
            StatusMessage = $"🕐 已设置为当前时间: {DateTime.Now:HH:mm:ss}";
        }

        /// <summary>
        /// 清除时间
        /// </summary>
        [RelayCommand]
        private void ClearTime()
        {
            SelectedTime = null;
            StatusMessage = "🗑️ 已清除选择的时间";
        }

        /// <summary>
        /// 设置特定时间
        /// </summary>
        [RelayCommand]
        private void SetSpecificTime(string timeString)
        {
            if (TimeSpan.TryParse(timeString, out var timeSpan))
            {
                var today = DateTime.Today;
                SelectedTime = today.Add(timeSpan);
                StatusMessage = $"⏰ 已设置时间为: {timeString}";
            }
        }

        /// <summary>
        /// 时间格式化示例
        /// </summary>
        public string FormattedTime => SelectedTime?.ToString("HH:mm:ss") ?? "未选择";
        public string ShortFormattedTime => SelectedTime?.ToString("HH:mm") ?? "未选择";
        public string AmPmFormattedTime => SelectedTime?.ToString("hh:mm:ss tt") ?? "未选择";
    }
}
