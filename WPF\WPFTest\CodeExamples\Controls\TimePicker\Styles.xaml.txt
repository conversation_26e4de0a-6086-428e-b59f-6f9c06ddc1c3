<!-- TimePicker 样式使用示例 -->
<StackPanel Margin="20" Spacing="15">

    <!-- 基础样式系列 -->
    <GroupBox Header="基础样式系列" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <!-- 标准样式 -->
            <StackPanel Margin="0,0,20,0">
                <TextBlock Text="标准样式" FontWeight="Medium" Margin="0,0,0,8"/>
                <ui:TimePicker Style="{StaticResource TimePickerStyle}"
                              SelectedTime="{Binding SelectedTime, Mode=TwoWay}"
                              Width="150"/>
            </StackPanel>

            <!-- 小型样式 -->
            <StackPanel Margin="0,0,20,0">
                <TextBlock Text="小型样式" FontWeight="Medium" Margin="0,0,0,8"/>
                <ui:TimePicker Style="{StaticResource SmallTimePickerStyle}"
                              SelectedTime="{Binding SelectedTime, Mode=TwoWay}"
                              Width="120"/>
            </StackPanel>

            <!-- 大型样式 -->
            <StackPanel Margin="0,0,20,0">
                <TextBlock Text="大型样式" FontWeight="Medium" Margin="0,0,0,8"/>
                <ui:TimePicker Style="{StaticResource LargeTimePickerStyle}"
                              SelectedTime="{Binding SelectedTime, Mode=TwoWay}"
                              Width="180"/>
            </StackPanel>

            <!-- 紧凑样式 -->
            <StackPanel>
                <TextBlock Text="紧凑样式" FontWeight="Medium" Margin="0,0,0,8"/>
                <ui:TimePicker Style="{StaticResource CompactTimePickerStyle}"
                              SelectedTime="{Binding SelectedTime, Mode=TwoWay}"
                              Width="100"/>
            </StackPanel>
        </WrapPanel>
    </GroupBox>

    <!-- 特殊效果样式 -->
    <GroupBox Header="特殊效果样式" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <!-- 透明样式 -->
            <StackPanel Margin="0,0,20,0">
                <TextBlock Text="透明样式" FontWeight="Medium" Margin="0,0,0,8"/>
                <ui:TimePicker Style="{StaticResource TransparentTimePickerStyle}"
                              SelectedTime="{Binding SelectedTime, Mode=TwoWay}"
                              Width="150"/>
            </StackPanel>

            <!-- 强调色样式 -->
            <StackPanel Margin="0,0,20,0">
                <TextBlock Text="强调色样式" FontWeight="Medium" Margin="0,0,0,8"/>
                <ui:TimePicker Style="{StaticResource AccentTimePickerStyle}"
                              SelectedTime="{Binding SelectedTime, Mode=TwoWay}"
                              Width="150"/>
            </StackPanel>

            <!-- 圆角样式 -->
            <StackPanel>
                <TextBlock Text="圆角样式" FontWeight="Medium" Margin="0,0,0,8"/>
                <ui:TimePicker Style="{StaticResource RoundedTimePickerStyle}"
                              SelectedTime="{Binding SelectedTime, Mode=TwoWay}"
                              Width="150"/>
            </StackPanel>
        </WrapPanel>
    </GroupBox>

    <!-- 主题样式 -->
    <GroupBox Header="主题样式" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <!-- 工作时间样式 -->
            <StackPanel Margin="0,0,20,0">
                <TextBlock Text="工作时间样式" FontWeight="Medium" Margin="0,0,0,8"/>
                <ui:TimePicker Style="{StaticResource WorkTimePickerStyle}"
                              SelectedTime="{Binding WorkTime, Mode=TwoWay}"
                              Width="150"/>
            </StackPanel>

            <!-- 提醒时间样式 -->
            <StackPanel>
                <TextBlock Text="提醒时间样式" FontWeight="Medium" Margin="0,0,0,8"/>
                <ui:TimePicker Style="{StaticResource ReminderTimePickerStyle}"
                              SelectedTime="{Binding ReminderTime, Mode=TwoWay}"
                              Width="150"/>
            </StackPanel>
        </WrapPanel>
    </GroupBox>

    <!-- 自定义样式示例 -->
    <GroupBox Header="自定义样式示例" Padding="15">
        <StackPanel>
            <TextBlock Text="您可以基于基础样式创建自定义样式：" 
                       FontWeight="Medium" 
                       Margin="0,0,0,15"/>

            <!-- 自定义样式代码示例 -->
            <Border Background="{DynamicResource LayerFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="4"
                    Padding="15">
                <TextBlock FontFamily="Consolas" FontSize="12">
                    <Run Text="&lt;Style x:Key=&quot;CustomTimePickerStyle&quot; TargetType=&quot;ui:TimePicker&quot; BasedOn=&quot;{StaticResource TimePickerBaseStyle}&quot;&gt;"/>
                    <LineBreak/>
                    <Run Text="    &lt;Setter Property=&quot;Background&quot; Value=&quot;#FF1E3A8A&quot;/&gt;"/>
                    <LineBreak/>
                    <Run Text="    &lt;Setter Property=&quot;Foreground&quot; Value=&quot;White&quot;/&gt;"/>
                    <LineBreak/>
                    <Run Text="    &lt;Setter Property=&quot;BorderBrush&quot; Value=&quot;#FF3B82F6&quot;/&gt;"/>
                    <LineBreak/>
                    <Run Text="    &lt;Setter Property=&quot;CornerRadius&quot; Value=&quot;8&quot;/&gt;"/>
                    <LineBreak/>
                    <Run Text="&lt;/Style&gt;"/>
                </TextBlock>
            </Border>

            <!-- 自定义样式应用示例 -->
            <StackPanel Orientation="Horizontal" Margin="0,15,0,0">
                <TextBlock Text="自定义样式效果：" 
                           VerticalAlignment="Center" 
                           Margin="0,0,15,0"/>
                <ui:TimePicker SelectedTime="{Binding SelectedTime, Mode=TwoWay}"
                              Background="#FF1E3A8A"
                              Foreground="White"
                              BorderBrush="#FF3B82F6"
                              CornerRadius="8"
                              Width="150"
                              Height="36"
                              Padding="12"/>
            </StackPanel>
        </StackPanel>
    </GroupBox>

    <!-- 响应式样式 -->
    <GroupBox Header="响应式样式" Padding="15">
        <StackPanel>
            <TextBlock Text="根据窗口大小自动调整的响应式样式：" 
                       FontWeight="Medium" 
                       Margin="0,0,0,15"/>

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 小屏幕样式 -->
                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                    <TextBlock Text="小屏幕 (&lt; 800px)" FontWeight="Medium" Margin="0,0,0,8"/>
                    <ui:TimePicker Style="{StaticResource CompactTimePickerStyle}"
                                  SelectedTime="{Binding SelectedTime, Mode=TwoWay}"
                                  HorizontalAlignment="Stretch"/>
                </StackPanel>

                <!-- 中等屏幕样式 -->
                <StackPanel Grid.Column="1" Margin="5,0,5,0">
                    <TextBlock Text="中等屏幕 (800-1200px)" FontWeight="Medium" Margin="0,0,0,8"/>
                    <ui:TimePicker Style="{StaticResource TimePickerStyle}"
                                  SelectedTime="{Binding SelectedTime, Mode=TwoWay}"
                                  HorizontalAlignment="Stretch"/>
                </StackPanel>

                <!-- 大屏幕样式 -->
                <StackPanel Grid.Column="2" Margin="10,0,0,0">
                    <TextBlock Text="大屏幕 (&gt; 1200px)" FontWeight="Medium" Margin="0,0,0,8"/>
                    <ui:TimePicker Style="{StaticResource LargeTimePickerStyle}"
                                  SelectedTime="{Binding SelectedTime, Mode=TwoWay}"
                                  HorizontalAlignment="Stretch"/>
                </StackPanel>
            </Grid>
        </StackPanel>
    </GroupBox>

</StackPanel>
