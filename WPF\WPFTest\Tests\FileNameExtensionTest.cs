using System;
using System.IO;

namespace WPFTest.Tests;

/// <summary>
/// 文件名扩展名处理测试
/// </summary>
public static class FileNameExtensionTest
{
    /// <summary>
    /// 测试复杂文件名的扩展名处理
    /// </summary>
    public static void TestComplexFileNames()
    {
        Console.WriteLine("🧪 测试复杂文件名的扩展名处理...");
        Console.WriteLine(new string('=', 80));

        var testFileNames = new[]
        {
            "GS_2025.6_副本.13-誉峰四期地下室与二期地下室交界局部梁板修改(1).dwg",
            "简单文件.dwg",
            "包含.多个.点号.的文件.dwg",
            "没有扩展名的文件",
            "文件名.txt.bak.dwg",
            "A1.2-建筑平面图.dwg",
            "S1.1-结构平面图(修改版).dwg"
        };

        foreach (var fileName in testFileNames)
        {
            Console.WriteLine($"\n📄 原始文件名: {fileName}");
            
            var nameWithoutExt = Path.GetFileNameWithoutExtension(fileName);
            var extension = Path.GetExtension(fileName);
            
            Console.WriteLine($"   📝 文件名部分: {nameWithoutExt}");
            Console.WriteLine($"   📎 扩展名部分: {extension}");
            
            // 模拟复制文件的命名逻辑
            var copyName = $"{nameWithoutExt}_副本{extension}";
            Console.WriteLine($"   📋 复制后文件名: {copyName}");
            
            // 检查是否正确保留了.dwg扩展名
            if (fileName.EndsWith(".dwg") && !copyName.EndsWith(".dwg"))
            {
                Console.WriteLine($"   ❌ 错误：丢失了.dwg扩展名！");
            }
            else if (fileName.EndsWith(".dwg") && copyName.EndsWith(".dwg"))
            {
                Console.WriteLine($"   ✅ 正确：保留了.dwg扩展名");
            }
            else
            {
                Console.WriteLine($"   ℹ️ 非DWG文件");
            }
        }

        Console.WriteLine(new string('=', 80));
        Console.WriteLine("✅ 复杂文件名扩展名处理测试完成");
    }

    /// <summary>
    /// 测试改进的文件名处理逻辑
    /// </summary>
    public static void TestImprovedFileNameHandling()
    {
        Console.WriteLine("\n🔧 测试改进的文件名处理逻辑...");
        Console.WriteLine(new string('=', 80));

        var testFileNames = new[]
        {
            "GS_2025.6_副本.13-誉峰四期地下室与二期地下室交界局部梁板修改(1).dwg",
            "A1.2-建筑平面图.dwg",
            "包含.多个.点号.的文件.dwg"
        };

        foreach (var fileName in testFileNames)
        {
            Console.WriteLine($"\n📄 原始文件名: {fileName}");
            
            // 改进的逻辑：确保正确处理.dwg扩展名
            string nameWithoutExt, extension;
            
            if (fileName.EndsWith(".dwg", StringComparison.OrdinalIgnoreCase))
            {
                // 对于.dwg文件，手动处理扩展名
                nameWithoutExt = fileName.Substring(0, fileName.Length - 4);
                extension = ".dwg";
            }
            else
            {
                // 对于其他文件，使用标准方法
                nameWithoutExt = Path.GetFileNameWithoutExtension(fileName);
                extension = Path.GetExtension(fileName);
            }
            
            Console.WriteLine($"   📝 改进后文件名部分: {nameWithoutExt}");
            Console.WriteLine($"   📎 改进后扩展名部分: {extension}");
            
            var copyName = $"{nameWithoutExt}_副本{extension}";
            Console.WriteLine($"   📋 改进后复制文件名: {copyName}");
            
            if (fileName.EndsWith(".dwg") && copyName.EndsWith(".dwg"))
            {
                Console.WriteLine($"   ✅ 改进逻辑正确：保留了.dwg扩展名");
            }
            else
            {
                Console.WriteLine($"   ❌ 改进逻辑仍有问题");
            }
        }

        Console.WriteLine(new string('=', 80));
        Console.WriteLine("✅ 改进的文件名处理逻辑测试完成");
    }

    /// <summary>
    /// 运行所有测试
    /// </summary>
    public static void RunAllTests()
    {
        Console.WriteLine("🚀 开始文件名扩展名处理测试...");
        
        TestComplexFileNames();
        TestImprovedFileNameHandling();
        
        Console.WriteLine("\n🏁 所有文件名扩展名处理测试完成");
    }

    /// <summary>
    /// 演示问题文件名的处理
    /// </summary>
    public static void DemonstrateProblem()
    {
        var problemFileName = "GS_2025.6_副本.13-誉峰四期地下室与二期地下室交界局部梁板修改(1).dwg";
        
        Console.WriteLine("🔍 问题演示:");
        Console.WriteLine($"原始文件名: {problemFileName}");
        
        var nameWithoutExt = Path.GetFileNameWithoutExtension(problemFileName);
        var extension = Path.GetExtension(problemFileName);
        
        Console.WriteLine($"Path.GetFileNameWithoutExtension(): {nameWithoutExt}");
        Console.WriteLine($"Path.GetExtension(): {extension}");
        
        var copyName = $"{nameWithoutExt}_副本{extension}";
        Console.WriteLine($"生成的复制文件名: {copyName}");
        
        if (!copyName.EndsWith(".dwg"))
        {
            Console.WriteLine("❌ 问题确认：丢失了.dwg扩展名！");
            
            // 展示正确的处理方式
            var correctNameWithoutExt = problemFileName.Substring(0, problemFileName.Length - 4);
            var correctExtension = ".dwg";
            var correctCopyName = $"{correctNameWithoutExt}_副本{correctExtension}";
            
            Console.WriteLine($"\n✅ 正确的处理方式:");
            Console.WriteLine($"正确的文件名部分: {correctNameWithoutExt}");
            Console.WriteLine($"正确的扩展名: {correctExtension}");
            Console.WriteLine($"正确的复制文件名: {correctCopyName}");
        }
    }
}
