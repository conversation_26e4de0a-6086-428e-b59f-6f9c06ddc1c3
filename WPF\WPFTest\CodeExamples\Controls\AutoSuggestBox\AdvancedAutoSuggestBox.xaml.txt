<!-- AutoSuggestBox 高级功能示例 -->
<UserControl x:Class="WPFTest.Views.Controls.AdvancedAutoSuggestBoxExample"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:i="http://schemas.microsoft.com/xaml/behaviors">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="20"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="20"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="20"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="20"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 智能搜索 - 带历史记录 -->
        <StackPanel Grid.Row="0">
            <TextBlock Text="智能搜索（带历史记录）" FontWeight="Bold" Margin="0,0,0,10"/>
            <ui:AutoSuggestBox x:Name="SmartSearchBox"
                               PlaceholderText="输入关键词，支持历史记录..."
                               Text="{Binding SmartSearchText, UpdateSourceTrigger=PropertyChanged}"
                               ItemsSource="{Binding SmartSuggestions}"
                               MaxSuggestionListHeight="300"
                               UpdateTextOnSelect="True"
                               ClearButtonEnabled="True">
                <ui:AutoSuggestBox.Icon>
                    <ui:SymbolIcon Symbol="Search24"/>
                </ui:AutoSuggestBox.Icon>
                <i:Interaction.Triggers>
                    <i:EventTrigger EventName="QuerySubmitted">
                        <i:InvokeCommandAction Command="{Binding SubmitQueryCommand}" 
                                               CommandParameter="{Binding Text, ElementName=SmartSearchBox}"/>
                    </i:EventTrigger>
                    <i:EventTrigger EventName="SuggestionChosen">
                        <i:InvokeCommandAction Command="{Binding ChooseSuggestionCommand}"/>
                    </i:EventTrigger>
                </i:Interaction.Triggers>
            </ui:AutoSuggestBox>
            <TextBlock Text="{Binding SearchHistory}" 
                       Margin="0,10,0,0" 
                       FontSize="12" 
                       Foreground="Gray"
                       TextWrapping="Wrap"/>
        </StackPanel>

        <!-- 多类型搜索 - 自动分类 -->
        <StackPanel Grid.Row="2">
            <TextBlock Text="多类型搜索（自动分类）" FontWeight="Bold" Margin="0,0,0,10"/>
            <ui:AutoSuggestBox x:Name="MultiTypeSearchBox"
                               PlaceholderText="输入任意内容，自动识别类型..."
                               Text="{Binding MultiTypeSearchText, UpdateSourceTrigger=PropertyChanged}"
                               ItemsSource="{Binding MultiTypeSuggestions}"
                               MaxSuggestionListHeight="250"
                               UpdateTextOnSelect="True"
                               ClearButtonEnabled="True">
                <ui:AutoSuggestBox.Icon>
                    <ui:SymbolIcon Symbol="Filter24"/>
                </ui:AutoSuggestBox.Icon>

            </ui:AutoSuggestBox>
            <TextBlock Text="{Binding SearchTypeInfo}" 
                       Margin="0,10,0,0" 
                       FontSize="12" 
                       Foreground="Blue"/>
        </StackPanel>

        <!-- 异步搜索 - 模拟网络请求 -->
        <StackPanel Grid.Row="4">
            <TextBlock Text="异步搜索（模拟网络请求）" FontWeight="Bold" Margin="0,0,0,10"/>
            <Grid>
                <ui:AutoSuggestBox x:Name="AsyncSearchBox"
                                   PlaceholderText="输入内容进行异步搜索..."
                                   Text="{Binding AsyncSearchText, UpdateSourceTrigger=PropertyChanged}"
                                   ItemsSource="{Binding AsyncSuggestions}"
                                   MaxSuggestionListHeight="200"
                                   UpdateTextOnSelect="True"
                                   ClearButtonEnabled="True"
                                   IsEnabled="{Binding IsAsyncSearchEnabled}">
                    <ui:AutoSuggestBox.Icon>
                        <ui:SymbolIcon Symbol="Cloud24"/>
                    </ui:AutoSuggestBox.Icon>
                </ui:AutoSuggestBox>
                <ui:ProgressRing Width="20" Height="20" 
                                 HorizontalAlignment="Right" 
                                 VerticalAlignment="Center"
                                 Margin="0,0,40,0"
                                 IsIndeterminate="True"
                                 Visibility="{Binding IsAsyncSearching, Converter={StaticResource BooleanToVisibilityConverter}}"/>
            </Grid>
            <TextBlock Text="{Binding AsyncSearchStatus}" 
                       Margin="0,10,0,0" 
                       FontSize="12" 
                       Foreground="Green"/>
        </StackPanel>

        <!-- 自定义样式搜索 -->
        <StackPanel Grid.Row="6">
            <TextBlock Text="自定义样式搜索" FontWeight="Bold" Margin="0,0,0,10"/>
            <ui:AutoSuggestBox x:Name="CustomStyleSearchBox"
                               PlaceholderText="自定义样式的搜索框..."
                               Text="{Binding CustomSearchText, UpdateSourceTrigger=PropertyChanged}"
                               ItemsSource="{Binding CustomSuggestions}"
                               MaxSuggestionListHeight="180"
                               UpdateTextOnSelect="True"
                               ClearButtonEnabled="True"
                               Background="{DynamicResource ApplicationBackgroundBrush}"
                               BorderBrush="{DynamicResource AccentTextFillColorPrimaryBrush}"
                               BorderThickness="2"
                               CornerRadius="15">
                <ui:AutoSuggestBox.Icon>
                    <ui:SymbolIcon Symbol="Sparkle24" Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"/>
                </ui:AutoSuggestBox.Icon>
            </ui:AutoSuggestBox>
        </StackPanel>

        <!-- 控制面板 -->
        <StackPanel Grid.Row="8" Orientation="Horizontal" HorizontalAlignment="Center">
            <ui:Button Content="清除所有搜索" 
                       Command="{Binding ClearAllSearchesCommand}"
                       Margin="0,0,10,0"/>
            <ui:Button Content="重置历史记录" 
                       Command="{Binding ResetHistoryCommand}"
                       Margin="0,0,10,0"/>
            <ui:Button Content="模拟网络延迟" 
                       Command="{Binding ToggleNetworkDelayCommand}"/>
        </StackPanel>
    </Grid>
</UserControl>

<!-- 
AutoSuggestBox 高级功能说明：

1. 智能搜索：
   - 历史记录支持
   - QuerySubmitted 和 SuggestionChosen 事件处理
   - 搜索历史显示

2. 多类型搜索：
   - 自动识别输入内容类型
   - 自定义 ItemTemplate 显示分类信息
   - 图标和类别标签

3. 异步搜索：
   - 模拟网络请求延迟
   - 加载状态指示器
   - 搜索状态反馈

4. 自定义样式：
   - 自定义背景、边框、圆角
   - 主题色彩适配
   - 图标样式定制

5. 事件处理：
   - Microsoft.Xaml.Behaviors 支持
   - 命令绑定模式
   - 参数传递

6. 用户体验优化：
   - 实时搜索反馈
   - 加载状态显示
   - 操作历史记录
-->
