<!-- ListView 自定义样式示例 -->
<!-- 展示如何使用 Zylo.WPF 中的自定义 ListView 样式 -->

<StackPanel>
    <!-- 现代化样式 -->
    <TextBlock Text="现代化样式：" FontWeight="Medium" Margin="0,0,0,8"/>
    
    <!-- 现代化 ListView -->
    <ListView ItemsSource="{Binding DataItems}"
              Style="{StaticResource ModernListViewStyle}"
              Height="150"
              Margin="0,0,0,16">
        <ListView.ItemTemplate>
            <DataTemplate>
                <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                        BorderBrush="{DynamicResource AccentFillColorDefaultBrush}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Padding="12"
                        Margin="4">
                    <StackPanel Orientation="Horizontal">
                        <ui:SymbolIcon Symbol="{Binding Icon}" 
                                       FontSize="20" 
                                       Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                       Margin="0,0,12,0"/>
                        <StackPanel>
                            <TextBlock Text="{Binding Name}" 
                                       FontWeight="Medium"
                                       FontSize="14"/>
                            <TextBlock Text="{Binding Description}" 
                                       FontSize="12"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                        </StackPanel>
                    </StackPanel>
                </Border>
            </DataTemplate>
        </ListView.ItemTemplate>
    </ListView>

    <!-- 卡片式样式 -->
    <TextBlock Text="卡片式样式：" FontWeight="Medium" Margin="0,0,0,8"/>
    
    <!-- 卡片式 ListView -->
    <ListView ItemsSource="{Binding DataItems}"
              Style="{StaticResource CardListViewStyle}"
              Height="200"
              Margin="0,0,0,16">
        <ListView.ItemTemplate>
            <DataTemplate>
                <ui:Card Padding="16" Margin="8">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- 图标 -->
                        <Border Grid.Column="0"
                                Background="{DynamicResource AccentFillColorDefaultBrush}"
                                Width="40"
                                Height="40"
                                CornerRadius="20"
                                Margin="0,0,12,0">
                            <ui:SymbolIcon Symbol="{Binding Icon}"
                                           FontSize="20"
                                           Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"/>
                        </Border>
                        
                        <!-- 内容 -->
                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                            <TextBlock Text="{Binding Name}"
                                       FontWeight="Medium"
                                       FontSize="16"/>
                            <TextBlock Text="{Binding Description}"
                                       FontSize="12"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,4,0,0"/>
                        </StackPanel>
                        
                        <!-- 状态标签 -->
                        <Border Grid.Column="2"
                                Background="{DynamicResource AccentFillColorSecondaryBrush}"
                                CornerRadius="12"
                                Padding="8,4"
                                VerticalAlignment="Top">
                            <TextBlock Text="{Binding Category}"
                                       FontSize="10"
                                       Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                       FontWeight="Medium"/>
                        </Border>
                    </Grid>
                </ui:Card>
            </DataTemplate>
        </ListView.ItemTemplate>
    </ListView>

    <!-- 网格式样式 -->
    <TextBlock Text="网格式样式：" FontWeight="Medium" Margin="0,0,0,8"/>
    
    <!-- 网格式 ListView -->
    <ListView ItemsSource="{Binding DataItems}"
              Style="{StaticResource GridListViewStyle}"
              Height="200">
        <ListView.ItemTemplate>
            <DataTemplate>
                <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                        BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Padding="16"
                        Margin="4">
                    <StackPanel HorizontalAlignment="Center">
                        <ui:SymbolIcon Symbol="{Binding Icon}"
                                       FontSize="32"
                                       Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                       HorizontalAlignment="Center"
                                       Margin="0,0,0,8"/>
                        <TextBlock Text="{Binding Name}"
                                   FontWeight="Medium"
                                   FontSize="14"
                                   HorizontalAlignment="Center"
                                   TextAlignment="Center"/>
                        <TextBlock Text="{Binding Category}"
                                   FontSize="10"
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   HorizontalAlignment="Center"
                                   Margin="0,4,0,0"/>
                    </StackPanel>
                </Border>
            </DataTemplate>
        </ListView.ItemTemplate>
    </ListView>
</StackPanel>

<!-- 
自定义样式特点：

1. ModernListViewStyle:
   - 现代化外观设计
   - 增强的视觉效果
   - 适合现代应用界面

2. CardListViewStyle:
   - 卡片式布局
   - 阴影效果
   - 适合内容展示

3. GridListViewStyle:
   - 网格布局
   - 图标居中显示
   - 适合应用程序列表

使用建议：
- 现代应用：使用 ModernListViewStyle
- 内容展示：使用 CardListViewStyle  
- 图标展示：使用 GridListViewStyle
- 可以基于这些样式进一步自定义
-->
