using Wpf.Ui.Controls;

namespace Zylo.WPF.Controls
{
    /// <summary>
    /// 封装的消息框结果枚举
    /// </summary>
    public enum YMessageBoxResult
    {
        /// <summary>
        /// 无结果
        /// </summary>
        None = 0,

        /// <summary>
        /// 主要按钮（确定/删除等）
        /// </summary>
        Primary = 1,

        /// <summary>
        /// 次要按钮（取消等）
        /// </summary>
        Secondary = 2,

        /// <summary>
        /// 关闭按钮
        /// </summary>
        Close = 3
    }

    /// <summary>
    /// 封装的 WPF-UI MessageBox，简化使用
    /// </summary>
    public static class YMessageBox
    {
        /// <summary>
        /// 转换 WPF-UI MessageBoxResult 到自定义枚举
        /// </summary>
        private static YMessageBoxResult ConvertResult(Wpf.Ui.Controls.MessageBoxResult result)
        {
            return result switch
            {
                Wpf.Ui.Controls.MessageBoxResult.Primary => YMessageBoxResult.Primary,
                Wpf.Ui.Controls.MessageBoxResult.Secondary => YMessageBoxResult.Secondary,
                Wpf.Ui.Controls.MessageBoxResult.None => YMessageBoxResult.Close,
                _ => YMessageBoxResult.None
            };
        }

        /// <summary>
        /// 显示信息对话框
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="title">标题</param>
        /// <returns>对话框结果</returns>
        public static async Task<YMessageBoxResult> ShowInfoAsync(string message, string title = "信息")
        {
            var messageBox = new Wpf.Ui.Controls.MessageBox
            {
                Title = title,
                Content = message,
                PrimaryButtonText = "确定",
                PrimaryButtonAppearance = ControlAppearance.Primary
            };

            var result = await messageBox.ShowDialogAsync();
            return ConvertResult(result);
        }

        /// <summary>
        /// 显示警告对话框
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="title">标题</param>
        /// <returns>对话框结果</returns>
        public static async Task<Wpf.Ui.Controls.MessageBoxResult> ShowWarningAsync(string message, string title = "警告")
        {
            var messageBox = new Wpf.Ui.Controls.MessageBox
            {
                Title = title,
                Content = message,
                PrimaryButtonText = "确定",
                PrimaryButtonAppearance = ControlAppearance.Caution
            };

            return await messageBox.ShowDialogAsync();
        }

        /// <summary>
        /// 显示错误对话框
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="title">标题</param>
        /// <returns>对话框结果</returns>
        public static async Task<Wpf.Ui.Controls.MessageBoxResult> ShowErrorAsync(string message, string title = "错误")
        {
            var messageBox = new Wpf.Ui.Controls.MessageBox
            {
                Title = title,
                Content = message,
                PrimaryButtonText = "确定",
                PrimaryButtonAppearance = ControlAppearance.Danger
            };

            return await messageBox.ShowDialogAsync();
        }

        /// <summary>
        /// 显示确认对话框（确定/取消）
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="title">标题</param>
        /// <param name="confirmText">确认按钮文本</param>
        /// <param name="cancelText">取消按钮文本</param>
        /// <returns>对话框结果</returns>
        public static async Task<YMessageBoxResult> ShowConfirmAsync(string message, string title = "确认",
            string confirmText = "确定", string cancelText = "取消")
        {
            var messageBox = new Wpf.Ui.Controls.MessageBox
            {
                Title = title,
                Content = message,
                PrimaryButtonText = confirmText,
                CloseButtonText = cancelText,  // 使用 CloseButtonText 而不是 SecondaryButtonText
                PrimaryButtonAppearance = ControlAppearance.Primary
            };

            var result = await messageBox.ShowDialogAsync();
            return ConvertResult(result);
        }

        /// <summary>
        /// 显示删除确认对话框
        /// </summary>
        /// <param name="itemName">要删除的项目名称</param>
        /// <param name="count">删除的项目数量</param>
        /// <returns>对话框结果</returns>
        public static async Task<YMessageBoxResult> ShowDeleteConfirmAsync(string itemName = "", int count = 1)
        {
            string message;
            if (count == 1)
            {
                message = string.IsNullOrEmpty(itemName) 
                    ? "确定要删除选中的项目吗？\n\n⚠️ 删除的文件将移动到回收站。"
                    : $"确定要删除 \"{itemName}\" 吗？\n\n⚠️ 删除的文件将移动到回收站。";
            }
            else
            {
                message = $"确定要删除选中的 {count} 个项目吗？\n\n⚠️ 删除的文件将移动到回收站。";
            }

            var messageBox = new Wpf.Ui.Controls.MessageBox
            {
                Title = "确认删除",
                Content = message,
                PrimaryButtonText = "删除",
                CloseButtonText = "取消",  // 使用 CloseButtonText 而不是 SecondaryButtonText
                PrimaryButtonAppearance = ControlAppearance.Caution
            };

            var result = await messageBox.ShowDialogAsync();
            return ConvertResult(result);
        }
    }
}
