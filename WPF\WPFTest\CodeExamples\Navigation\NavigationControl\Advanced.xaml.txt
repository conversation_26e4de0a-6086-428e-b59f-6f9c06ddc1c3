<!-- 高级NavigationControl配置 -->
<zylo:NavigationControl
    ItemsSource="{Binding HierarchicalNavigationItems}"
    SelectedItem="{Binding SelectedNavigationItem, Mode=TwoWay}"
    NavigationItemSelectedCommand="{Binding NavigationCommand}"
    ShowSubMenuOnClick="{Binding ShowSubMenuOnClick}"
    IsSearchVisible="{Binding IsSearchVisible}"
    SearchText="{Binding SearchText, Mode=TwoWay}"
    IsCollapsed="{Binding IsNavigationCollapsed}"
    Width="320"
    Background="{DynamicResource LayerFillColorDefaultBrush}"
    BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
    BorderThickness="1"
    CornerRadius="8">

    <!-- 自定义项容器样式 -->
    <zylo:NavigationControl.ItemContainerStyle>
        <Style TargetType="TreeViewItem">
            <Setter Property="IsExpanded" Value="{Binding IsExpanded, Mode=TwoWay}"/>
            <Setter Property="IsSelected" Value="{Binding IsSelected, Mode=TwoWay}"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="Padding" Value="5"/>
            <Style.Triggers>
                <Trigger Property="IsSelected" Value="True">
                    <Setter Property="Background" Value="{DynamicResource AccentFillColorSecondaryBrush}"/>
                    <Setter Property="Foreground" Value="{DynamicResource TextOnAccentFillColorPrimaryBrush}"/>
                </Trigger>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </zylo:NavigationControl.ItemContainerStyle>

    <!-- 高级自定义项模板 -->
    <zylo:NavigationControl.ItemTemplate>
        <HierarchicalDataTemplate ItemsSource="{Binding Children}">
            <Grid Margin="5" MinHeight="32">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 图标 -->
                <zylo:ZyloIcon Grid.Column="0"
                              Icon="{Binding Icon}"
                              FontSize="18"
                              Margin="0,0,10,0"
                              Foreground="{Binding IsSelected, Converter={StaticResource BoolToAccentBrushConverter}}"
                              VerticalAlignment="Center"/>

                <!-- 标题和描述 -->
                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                    <TextBlock Text="{Binding Title}"
                              FontSize="14"
                              FontWeight="{Binding IsSelected, Converter={StaticResource BoolToFontWeightConverter}}"
                              Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                    
                    <TextBlock Text="{Binding Description}"
                              FontSize="11"
                              Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                              Visibility="{Binding Description, Converter={StaticResource StringToVisibilityConverter}}"
                              TextTrimming="CharacterEllipsis"
                              MaxWidth="180"/>
                </StackPanel>

                <!-- 状态指示器 -->
                <Border Grid.Column="2"
                       Background="{Binding StatusColor, Converter={StaticResource ColorToBrushConverter}}"
                       Width="8" Height="8"
                       CornerRadius="4"
                       Margin="5,0"
                       VerticalAlignment="Center"
                       Visibility="{Binding HasStatus, Converter={StaticResource BoolToVisibilityConverter}}"/>

                <!-- 子项数量和操作按钮 -->
                <StackPanel Grid.Column="3" Orientation="Horizontal" VerticalAlignment="Center">
                    <!-- 子项数量徽章 -->
                    <Border Background="{DynamicResource AccentFillColorDefaultBrush}"
                           CornerRadius="10"
                           Padding="6,2"
                           Margin="0,0,5,0"
                           Visibility="{Binding ChildrenCount, Converter={StaticResource CountToVisibilityConverter}}">
                        <TextBlock Text="{Binding ChildrenCount}"
                                  FontSize="10"
                                  FontWeight="SemiBold"
                                  Foreground="{DynamicResource TextOnAccentFillColorPrimaryBrush}"
                                  HorizontalAlignment="Center"/>
                    </Border>

                    <!-- 快捷操作按钮 -->
                    <StackPanel Orientation="Horizontal" 
                               Visibility="{Binding IsMouseOver, RelativeSource={RelativeSource AncestorType=Grid}, Converter={StaticResource BoolToVisibilityConverter}}">
                        
                        <ui:Button Width="24" Height="24"
                                  Padding="0"
                                  Margin="2,0"
                                  Background="Transparent"
                                  BorderThickness="0"
                                  Command="{Binding DataContext.EditItemCommand, RelativeSource={RelativeSource AncestorType=zylo:NavigationControl}}"
                                  CommandParameter="{Binding}"
                                  ToolTip="编辑项">
                            <zylo:ZyloIcon Icon="Edit" FontSize="12"/>
                        </ui:Button>

                        <ui:Button Width="24" Height="24"
                                  Padding="0"
                                  Margin="2,0"
                                  Background="Transparent"
                                  BorderThickness="0"
                                  Command="{Binding DataContext.DeleteItemCommand, RelativeSource={RelativeSource AncestorType=zylo:NavigationControl}}"
                                  CommandParameter="{Binding}"
                                  ToolTip="删除项">
                            <zylo:ZyloIcon Icon="Delete" FontSize="12" Foreground="Red"/>
                        </ui:Button>
                    </StackPanel>
                </StackPanel>
            </Grid>
        </HierarchicalDataTemplate>
    </zylo:NavigationControl.ItemTemplate>

    <!-- 自定义上下文菜单 -->
    <zylo:NavigationControl.ContextMenu>
        <ContextMenu>
            <MenuItem Header="添加子项" 
                     Command="{Binding AddChildItemCommand}"
                     CommandParameter="{Binding PlacementTarget.DataContext, RelativeSource={RelativeSource AncestorType=ContextMenu}}">
                <MenuItem.Icon>
                    <zylo:ZyloIcon Icon="Add" FontSize="14"/>
                </MenuItem.Icon>
            </MenuItem>
            
            <MenuItem Header="编辑项" 
                     Command="{Binding EditItemCommand}"
                     CommandParameter="{Binding PlacementTarget.DataContext, RelativeSource={RelativeSource AncestorType=ContextMenu}}">
                <MenuItem.Icon>
                    <zylo:ZyloIcon Icon="Edit" FontSize="14"/>
                </MenuItem.Icon>
            </MenuItem>
            
            <Separator/>
            
            <MenuItem Header="复制项" 
                     Command="{Binding CopyItemCommand}"
                     CommandParameter="{Binding PlacementTarget.DataContext, RelativeSource={RelativeSource AncestorType=ContextMenu}}">
                <MenuItem.Icon>
                    <zylo:ZyloIcon Icon="Copy" FontSize="14"/>
                </MenuItem.Icon>
            </MenuItem>
            
            <MenuItem Header="删除项" 
                     Command="{Binding DeleteItemCommand}"
                     CommandParameter="{Binding PlacementTarget.DataContext, RelativeSource={RelativeSource AncestorType=ContextMenu}}">
                <MenuItem.Icon>
                    <zylo:ZyloIcon Icon="Delete" FontSize="14" Foreground="Red"/>
                </MenuItem.Icon>
            </MenuItem>
            
            <Separator/>
            
            <MenuItem Header="展开所有" Command="{Binding ExpandAllCommand}">
                <MenuItem.Icon>
                    <zylo:ZyloIcon Icon="ExpandMore" FontSize="14"/>
                </MenuItem.Icon>
            </MenuItem>
            
            <MenuItem Header="折叠所有" Command="{Binding CollapseAllCommand}">
                <MenuItem.Icon>
                    <zylo:ZyloIcon Icon="ExpandLess" FontSize="14"/>
                </MenuItem.Icon>
            </MenuItem>
        </ContextMenu>
    </zylo:NavigationControl.ContextMenu>
</zylo:NavigationControl>
