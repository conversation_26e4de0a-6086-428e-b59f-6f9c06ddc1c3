<UserControl
    d:DataContext="{d:DesignInstance Type=vm:DwgFileTypeEditorViewModel}"
    d:DesignHeight="600"
    d:DesignWidth="1000"
    mc:Ignorable="d"
    mvvm:ViewModelLocator.AutoWireViewModel="True"
    x:Class="WPFTest.Views.DWG.DwgFileTypeEditorView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:behaviors="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dd="urn:gong-wpf-dragdrop"
    xmlns:listView="clr-namespace:Zylo.WPF.Behaviors.ListView;assembly=Zylo.WPF"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:mvvm="http://prismlibrary.com/"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:vm="clr-namespace:WPFTest.ViewModels.DWG"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!--#region 资源定义-->
    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
    </UserControl.Resources>
    <!--#endregion-->

    <!--#region 主布局结构-->
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <!--  顶部工具栏  -->
            <RowDefinition Height="*" />
            <!--  主内容区域  -->
            <RowDefinition Height="Auto" />
            <!--  底部状态栏  -->
        </Grid.RowDefinitions>

        <!--#region 顶部工具栏-->
        <Border
            Background="{DynamicResource ControlFillColorDefaultBrush}"
            Grid.Row="0"
            Padding="16,12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <!--  标题区域  -->
                    <ColumnDefinition Width="Auto" />
                    <!--  操作按钮区域  -->
                </Grid.ColumnDefinitions>

                <!--  标题和图标  -->
                <StackPanel
                    Grid.Column="0"
                    Orientation="Horizontal"
                    VerticalAlignment="Center">
                    <ui:SymbolIcon
                        FontSize="20"
                        Margin="0,0,8,0"
                        Symbol="Database24" />
                    <TextBlock
                        FontSize="16"
                        FontWeight="SemiBold"
                        Text="DWG文件类型管理" />
                </StackPanel>

                <!--  统计信息和操作按钮  -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <!--  统计信息  -->
                    <TextBlock
                        FontSize="12"
                        Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                        Text="{Binding FileTypes.Count, StringFormat='共 {0} 种类型'}"
                        VerticalAlignment="Center" />

                    <!--  调试按钮  -->
                    <ui:Button
                        Appearance="Secondary"
                        Command="{Binding TestDragDropCommand}"
                        Content="测试拖拽"
                        FontSize="11"
                        Margin="8,0,0,0"
                        Padding="8,4" />

                    <ui:Button
                        Appearance="Secondary"
                        Command="{Binding AnalyzeFileTypesCommand}"
                        Content="分析文件类型"
                        FontSize="11"
                        Margin="4,0,0,0"
                        Padding="8,4" />
                </StackPanel>
            </Grid>
        </Border>
        <!--#endregion-->

        <!--#region 主内容区域-->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300" />
                <!--  左侧文件类型列表  -->
                <ColumnDefinition Width="4" />
                <!--  分隔线  -->
                <ColumnDefinition Width="*" />
                <!--  右侧编辑表单  -->
            </Grid.ColumnDefinitions>

            <!--#region 左侧文件类型列表-->
            <Border Grid.Column="0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <!--  搜索框  -->
                        <RowDefinition Height="*" />
                        <!--  文件类型列表  -->
                    </Grid.RowDefinitions>

                    <!--  搜索框  -->
                    <ui:TextBox
                        Grid.Row="0"
                        Margin="8"
                        PlaceholderText="搜索..."
                        Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}">
                        <ui:TextBox.Icon>
                            <ui:SymbolIcon Symbol="Search24" />
                        </ui:TextBox.Icon>
                    </ui:TextBox>

                    <!--  文件类型列表 - 支持拖拽排序和DWG文件拖拽  -->
                    <ui:ListView
                        Background="Transparent"
                        BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                        BorderThickness="1"
                        Grid.Row="1"
                        ItemsSource="{Binding FilteredFileTypes}"
                        Margin="8,0,8,8"
                        SelectedItem="{Binding SelectedFileType}"
                        dd:DragDrop.DropHandler="{Binding}"
                        dd:DragDrop.IsDragSource="True"
                        dd:DragDrop.IsDropTarget="True"
                        dd:DragDrop.UseDefaultDragAdorner="False"
                        dd:DragDrop.UseDefaultEffectDataTemplate="False">

                        <!--  自动编辑行为  -->
                        <behaviors:Interaction.Behaviors>
                            <listView:AutoEditOnSelectionBehavior
                                DelayMilliseconds="200"
                                EditCommand="{Binding EditFileTypeCommand}"
                                IsEnabled="True" />
                        </behaviors:Interaction.Behaviors>
                        <!--  列表项模板  -->
                        <ui:ListView.ItemTemplate>
                            <DataTemplate>
                                <Grid Margin="2">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <!--  图标  -->
                                        <ColumnDefinition Width="*" />
                                        <!--  名称  -->
                                        <ColumnDefinition Width="Auto" />
                                        <!--  状态指示器  -->
                                    </Grid.ColumnDefinitions>

                                    <!--  文件类型图标  -->
                                    <Border
                                        Grid.Column="0"
                                        Height="24"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Width="24">
                                        <TextBlock
                                            FontSize="16"
                                            HorizontalAlignment="Center"
                                            Text="{Binding Icon}"
                                            TextAlignment="Center"
                                            VerticalAlignment="Center" />
                                    </Border>

                                    <!--  文件类型名称  -->
                                    <StackPanel
                                        Grid.Column="1"
                                        Margin="5,0,0,0"
                                        VerticalAlignment="Center">
                                        <TextBlock
                                            FontSize="14"
                                            FontWeight="SemiBold"
                                            Text="{Binding ChineseName}" />
                                    </StackPanel>

                                    <!--  启用状态指示器  -->
                                    <Ellipse
                                        Fill="Green"
                                        Grid.Column="2"
                                        Height="6"
                                        VerticalAlignment="Center"
                                        Width="6" />
                                </Grid>
                            </DataTemplate>
                        </ui:ListView.ItemTemplate>
                    </ui:ListView>
                </Grid>
            </Border>
            <!--#endregion-->

            <!--  分隔线  -->
            <GridSplitter Background="{DynamicResource ControlStrokeColorDefaultBrush}" Grid.Column="1" />

            <!--#region 右侧编辑表单区域-->
            <Border
                Background="{DynamicResource ControlFillColorDefaultBrush}"
                Grid.Column="2"
                Padding="24">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <!--#region 表单标题和操作按钮-->
                        <Grid Margin="0,0,0,24">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <!--  标题  -->
                                <ColumnDefinition Width="Auto" />
                                <!--  操作按钮  -->
                            </Grid.ColumnDefinitions>

                            <!--  表单标题  -->
                            <TextBlock
                                FontSize="18"
                                FontWeight="SemiBold"
                                Grid.Column="0"
                                Text="文件类型编辑" />

                            <!--  操作按钮组  -->
                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                <ui:Button
                                    Appearance="Primary"
                                    Command="{Binding NewFileTypeCommand}"
                                    Content="新建"
                                    Icon="{ui:SymbolIcon Add24}"
                                    Margin="0,0,8,0" />
                                <ui:Button
                                    Command="{Binding EditFileTypeCommand}"
                                    Content="编辑"
                                    Icon="{ui:SymbolIcon Edit24}"
                                    Margin="0,0,8,0" />
                                <ui:Button
                                    Appearance="Danger"
                                    Command="{Binding DeleteCommand}"
                                    Content="删除"
                                    Icon="{ui:SymbolIcon Delete24}" />
                            </StackPanel>
                        </Grid>
                        <!--#endregion-->

                        <!--#region 编辑表单-->
                        <ui:Card
                            DataContext="{Binding EditingFileType}"
                            Margin="0,0,0,16"
                            Visibility="{Binding DataContext.IsEditing, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <Grid Margin="16">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <!--  基本信息行  -->
                                    <RowDefinition Height="Auto" />
                                    <!--  图标和颜色行  -->
                                    <RowDefinition Height="Auto" />
                                    <!--  启用状态行  -->
                                    <RowDefinition Height="Auto" />
                                    <!--  前缀行  -->
                                    <RowDefinition Height="Auto" />
                                    <!--  描述行  -->
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="100" />
                                    <!--  标签列  -->
                                    <ColumnDefinition Width="*" />
                                    <!--  输入列1  -->
                                    <ColumnDefinition Width="100" />
                                    <!--  标签列2  -->
                                    <ColumnDefinition Width="*" />
                                    <!--  输入列2  -->
                                </Grid.ColumnDefinitions>

                                <!--  第一行：基本信息  -->
                                <!--  英文名称  -->
                                <TextBlock
                                    Grid.Column="0"
                                    Grid.Row="0"
                                    Text="英文名称*"
                                    VerticalAlignment="Center" />
                                <ui:TextBox
                                    Grid.Column="1"
                                    Grid.Row="0"
                                    Margin="8,0"
                                    Text="{Binding EnglishName, UpdateSourceTrigger=PropertyChanged}" />

                                <!--  中文名称  -->
                                <TextBlock
                                    Grid.Column="2"
                                    Grid.Row="0"
                                    Text="中文名称*"
                                    VerticalAlignment="Center" />
                                <ui:TextBox
                                    Grid.Column="3"
                                    Grid.Row="0"
                                    Margin="8,0"
                                    Text="{Binding ChineseName, UpdateSourceTrigger=PropertyChanged}" />

                                <!--  图标选择器  -->
                                <TextBlock
                                    Grid.Column="0"
                                    Grid.Row="1"
                                    Margin="0,16,0,0"
                                    Text="图标"
                                    VerticalAlignment="Center" />
                                <StackPanel
                                    Grid.Column="1"
                                    Grid.Row="1"
                                    Margin="8,16,8,0">
                                    <!--  当前选中的图标显示  -->
                                    <Border
                                        Background="{DynamicResource ControlFillColorDefaultBrush}"
                                        BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                        BorderThickness="1"
                                        CornerRadius="4"
                                        Padding="8">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="Auto" />
                                            </Grid.ColumnDefinitions>

                                            <TextBlock
                                                FontSize="20"
                                                Grid.Column="0"
                                                Margin="0,0,8,0"
                                                Text="{Binding Icon}"
                                                VerticalAlignment="Center" />
                                            <TextBlock
                                                Grid.Column="1"
                                                Text="{Binding Icon, StringFormat='图标: {0}'}"
                                                VerticalAlignment="Center" />
                                            <ui:Button
                                                Appearance="Secondary"
                                                Command="{Binding SelectIconCommand}"
                                                Content="选择图标"
                                                FontSize="11"
                                                Grid.Column="2"
                                                Padding="8,4" />
                                        </Grid>
                                    </Border>
                                </StackPanel>

                                <!--  颜色  -->
                                <TextBlock
                                    Grid.Column="2"
                                    Grid.Row="1"
                                    Margin="0,16,0,0"
                                    Text="颜色"
                                    VerticalAlignment="Center" />
                                <ui:TextBox
                                    Grid.Column="3"
                                    Grid.Row="1"
                                    Margin="8,16,8,0"
                                    PlaceholderText="如：#4CAF50"
                                    Text="{Binding Color, UpdateSourceTrigger=PropertyChanged}" />

                                <!--  启用状态  -->
                                <TextBlock
                                    Grid.Column="0"
                                    Grid.Row="2"
                                    Margin="0,16,0,0"
                                    Text="启用"
                                    VerticalAlignment="Center" />
                                <ui:ToggleSwitch
                                    Grid.Column="1"
                                    Grid.Row="2"
                                    IsChecked="{Binding IsEnabled}"
                                    Margin="8,16,8,0" />

                                <!--  前缀  -->
                                <TextBlock
                                    Grid.Column="0"
                                    Grid.Row="3"
                                    Margin="0,16,0,0"
                                    Text="前缀"
                                    VerticalAlignment="Top" />
                                <ui:TextBox
                                    Grid.Column="1"
                                    Grid.ColumnSpan="3"
                                    Grid.Row="3"
                                    Margin="8,16,0,0"
                                    PlaceholderText="用逗号分隔，如：S_, S-, GS_"
                                    Text="{Binding PrefixesString, UpdateSourceTrigger=PropertyChanged}" />

                                <!--  描述  -->
                                <TextBlock
                                    Grid.Column="0"
                                    Grid.Row="4"
                                    Margin="0,16,0,0"
                                    Text="描述"
                                    VerticalAlignment="Top" />
                                <ui:TextBox
                                    AcceptsReturn="True"
                                    Grid.Column="1"
                                    Grid.ColumnSpan="3"
                                    Grid.Row="4"
                                    Margin="8,16,0,0"
                                    MinHeight="60"
                                    Text="{Binding Description, UpdateSourceTrigger=PropertyChanged}"
                                    TextWrapping="Wrap" />
                            </Grid>
                        </ui:Card>

                        <!--  操作按钮  -->
                        <StackPanel Orientation="Horizontal" Visibility="{Binding IsEditing, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <ui:Button
                                Appearance="Primary"
                                Command="{Binding SaveCommand}"
                                Icon="{ui:SymbolIcon Save24}"
                                Margin="0,0,8,0">
                                <ui:Button.Style>
                                    <Style BasedOn="{StaticResource {x:Type ui:Button}}" TargetType="ui:Button">
                                        <Setter Property="Content" Value="保存修改" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding IsNewMode}" Value="True">
                                                <Setter Property="Content" Value="确认添加" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </ui:Button.Style>
                            </ui:Button>
                            <ui:Button
                                Command="{Binding CancelCommand}"
                                Content="取消"
                                Icon="{ui:SymbolIcon Dismiss24}" />
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </Border>
        </Grid>

        <!--  状态栏 - 增强调试信息  -->
        <Border
            Background="{DynamicResource ControlFillColorSecondaryBrush}"
            Grid.Row="2"
            Padding="16,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <!--  状态消息  -->
                <TextBlock
                    Grid.Column="0"
                    Text="{Binding StatusMessage}"
                    TextWrapping="Wrap"
                    VerticalAlignment="Center" />

                <!--  调试信息  -->
                <StackPanel
                    Grid.Column="1"
                    Margin="8,0"
                    Orientation="Horizontal">
                    <TextBlock
                        FontSize="10"
                        Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                        Text="{Binding FileTypes.Count, StringFormat='类型: {0}'}"
                        VerticalAlignment="Center" />
                    <TextBlock
                        FontSize="10"
                        Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                        Margin="8,0,0,0"
                        Text="{Binding SelectedFileType.ChineseName, StringFormat=' | 选中: {0}'}"
                        VerticalAlignment="Center" />
                </StackPanel>

                <!--  加载指示器  -->
                <ui:ProgressRing
                    Grid.Column="2"
                    Height="16"
                    IsIndeterminate="True"
                    Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                    Width="16" />
            </Grid>
        </Border>
    </Grid>
</UserControl>