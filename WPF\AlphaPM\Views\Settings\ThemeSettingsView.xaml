<UserControl
    d:DataContext="{d:DesignInstance settings:ThemeSettingsViewModel}"
    d:DesignHeight="2000"
    d:DesignWidth="600"
    mc:Ignorable="d"
    prism:ViewModelLocator.AutoWireViewModel="True"
    x:Class="AlphaPM.Views.Settings.ThemeSettingsView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:prism="http://prismlibrary.com/"
    xmlns:settings="clr-namespace:AlphaPM.ViewModels.Settings"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:viewModels="clr-namespace:AlphaPM.ViewModels"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <UserControl.Resources>
        <!--  现代化主题模式按钮样式  -->
        <Style TargetType="ui:Button" x:Key="ModernThemeModeButtonStyle">
            <Setter Property="Width" Value="160" />
            <Setter Property="Height" Value="120" />
            <Setter Property="Margin" Value="12" />
            <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}" />
            <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="CornerRadius" Value="12" />
            <Setter Property="Cursor" Value="Hand" />
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect
                        BlurRadius="8"
                        Color="{DynamicResource SystemBaseLowColor}"
                        Opacity="0.1"
                        ShadowDepth="2" />
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="BorderBrush" Value="{DynamicResource SystemAccentColorPrimaryBrush}" />
                    <Setter Property="BorderThickness" Value="2" />
                    <Setter Property="Effect">
                        <Setter.Value>
                            <DropShadowEffect
                                BlurRadius="12"
                                Color="{DynamicResource SystemAccentColorPrimaryColor}"
                                Opacity="0.3"
                                ShadowDepth="3" />
                        </Setter.Value>
                    </Setter>
                </Trigger>
                <DataTrigger Binding="{Binding IsSelected}" Value="True">
                    <Setter Property="BorderBrush" Value="{DynamicResource SystemAccentColorPrimaryBrush}" />
                    <Setter Property="BorderThickness" Value="3" />
                    <Setter Property="Background" Value="{DynamicResource AccentFillColorTertiaryBrush}" />
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!--  主题图标样式  -->
        <Style TargetType="ui:SymbolIcon" x:Key="ThemeIconStyle">
            <Setter Property="FontSize" Value="32" />
            <Setter Property="Foreground" Value="{DynamicResource SystemAccentColorPrimaryBrush}" />
            <Setter Property="Margin" Value="0,0,0,8" />
        </Style>

        <!--  主题标题样式  -->
        <Style TargetType="TextBlock" x:Key="ThemeTitleStyle">
            <Setter Property="FontSize" Value="14" />
            <Setter Property="FontWeight" Value="SemiBold" />
            <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}" />
            <Setter Property="HorizontalAlignment" Value="Center" />
            <Setter Property="Margin" Value="0,0,0,4" />
        </Style>

        <!--  主题描述样式  -->
        <Style TargetType="TextBlock" x:Key="ThemeDescriptionStyle">
            <Setter Property="FontSize" Value="11" />
            <Setter Property="Foreground" Value="{DynamicResource TextFillColorSecondaryBrush}" />
            <Setter Property="HorizontalAlignment" Value="Center" />
            <Setter Property="TextWrapping" Value="Wrap" />
            <Setter Property="TextAlignment" Value="Center" />
        </Style>

        <!--  强调色按钮样式  -->
        <Style TargetType="Button" x:Key="AccentColorButtonStyle">
            <Setter Property="Width" Value="48" />
            <Setter Property="Height" Value="48" />
            <Setter Property="Margin" Value="8" />
            <Setter Property="BorderThickness" Value="2" />
            <Setter Property="BorderBrush" Value="Transparent" />
            <Setter Property="Cursor" Value="Hand" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="24">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="BorderBrush" Value="{DynamicResource TextFillColorPrimaryBrush}" />
                </Trigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid Background="{DynamicResource ApplicationBackgroundBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!--  页面标题  -->
        <Border
            Background="{DynamicResource LayerFillColorDefaultBrush}"
            BorderBrush="{DynamicResource DividerStrokeColorDefaultBrush}"
            BorderThickness="0,0,0,1"
            Grid.Row="0"
            Padding="32,24">
            <StackPanel Orientation="Horizontal">
                <ui:SymbolIcon
                    FontSize="32"
                    Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                    Margin="0,0,16,0"
                    Symbol="Settings24" />
                <StackPanel VerticalAlignment="Center">
                    <TextBlock
                        FontSize="24"
                        FontWeight="Bold"
                        Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                        Text="{Binding PageTitle}" />
                    <TextBlock
                        FontSize="14"
                        Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                        Text="{Binding PageDescription}" />
                </StackPanel>
            </StackPanel>
        </Border>

        <!--  主要内容  -->
        <ScrollViewer
            CanContentScroll="False"
            Grid.Row="1"
            HorizontalScrollBarVisibility="Disabled"
            Padding="32"
            VerticalScrollBarVisibility="Auto">
            <StackPanel>

                <!--  主题模式选择  -->
                <ui:Card Margin="0,0,0,32" Padding="24">
                    <StackPanel>
                        <StackPanel Margin="0,0,0,16" Orientation="Horizontal">
                            <ui:SymbolIcon
                                FontSize="24"
                                Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                Symbol="WeatherSunny24" />
                            <StackPanel>
                                <TextBlock
                                    FontSize="18"
                                    FontWeight="SemiBold"
                                    Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                    Text="主题模式" />
                                <TextBlock
                                    FontSize="14"
                                    Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                    Text="选择应用程序的主题外观" />
                            </StackPanel>
                        </StackPanel>

                        <ItemsControl ItemsSource="{Binding ThemeModes}">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <StackPanel HorizontalAlignment="Center" Orientation="Horizontal" />
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <ui:Button
                                        Background="Transparent"
                                        BorderThickness="0"
                                        Command="{Binding DataContext.ChangeThemeCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                        CommandParameter="{Binding Theme}"
                                        Padding="0"
                                        Style="{StaticResource ModernThemeModeButtonStyle}">
                                        <Grid>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="*" />
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="Auto" />
                                            </Grid.RowDefinitions>

                                            <!--  主题图标  -->
                                            <ui:SymbolIcon
                                                Grid.Row="0"
                                                Style="{StaticResource ThemeIconStyle}"
                                                Symbol="{Binding Icon}"
                                                VerticalAlignment="Center" />

                                            <!--  主题名称  -->
                                            <TextBlock
                                                Grid.Row="1"
                                                Style="{StaticResource ThemeTitleStyle}"
                                                Text="{Binding Name}" />

                                            <!--  主题描述  -->
                                            <TextBlock
                                                Grid.Row="2"
                                                Style="{StaticResource ThemeDescriptionStyle}"
                                                Text="{Binding Description}" />

                                            <!--  选中指示器  -->
                                            <Border
                                                Background="Transparent"
                                                CornerRadius="12"
                                                Grid.Row="0"
                                                Grid.RowSpan="3">
                                                <Border.Style>
                                                    <Style TargetType="Border">
                                                        <Setter Property="Visibility" Value="Collapsed" />
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding IsSelected}" Value="True">
                                                                <Setter Property="Visibility" Value="Visible" />
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </Border.Style>
                                                <ui:SymbolIcon
                                                    FontSize="20"
                                                    Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                                    HorizontalAlignment="Right"
                                                    Margin="0,8,8,0"
                                                    Symbol="Checkmark24"
                                                    VerticalAlignment="Top" />
                                            </Border>
                                        </Grid>
                                    </ui:Button>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </ui:Card>

                <!--  强调色选择  -->
                <ui:Card Margin="0,0,0,32" Padding="24">
                    <StackPanel>
                        <StackPanel Margin="0,0,0,24" Orientation="Horizontal">
                            <ui:SymbolIcon
                                FontSize="24"
                                Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                Symbol="Color24" />
                            <StackPanel>
                                <TextBlock
                                    FontSize="18"
                                    FontWeight="SemiBold"
                                    Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                    Text="强调色" />
                                <TextBlock
                                    FontSize="14"
                                    Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                    Text="选择应用程序的强调色" />
                            </StackPanel>
                        </StackPanel>

                        <ItemsControl ItemsSource="{Binding AccentColors}">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <WrapPanel Orientation="Horizontal" />
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Button
                                        Background="{Binding Brush}"
                                        Command="{Binding DataContext.ChangeAccentColorCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                        CommandParameter="{Binding Color}"
                                        Style="{StaticResource AccentColorButtonStyle}"
                                        ToolTip="{Binding Name}" />
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </ui:Card>

                <!--  🎨 自定义颜色选择器  -->
                <ui:Card Margin="0,0,0,32" Padding="24">
                    <StackPanel>
                        <StackPanel Margin="0,0,0,24" Orientation="Horizontal">
                            <ui:SymbolIcon
                                FontSize="24"
                                Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                Symbol="ColorBackground24" />
                            <StackPanel Margin="16,0,0,0">
                                <TextBlock
                                    FontSize="18"
                                    FontWeight="SemiBold"
                                    Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                    Text="🎨 自定义颜色" />
                                <TextBlock
                                    FontSize="14"
                                    Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                    Text="选择流行色彩或中性色调作为强调色" />
                            </StackPanel>
                        </StackPanel>

                        <!--  流行色彩调色板  -->
                        <StackPanel Margin="0,0,0,24">
                            <TextBlock
                                FontSize="15"
                                FontWeight="Medium"
                                Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                Margin="0,0,0,12"
                                Text="流行色彩" />

                            <UniformGrid Columns="8" Rows="3">
                                <!--  第一行：蓝色系  -->
                                <Button
                                    Background="#0078D4"
                                    Command="{Binding SelectCustomColorCommand}"
                                    CommandParameter="#0078D4"
                                    Margin="4"
                                    Style="{StaticResource AccentColorButtonStyle}"
                                    ToolTip="Azure Blue - 微软蓝" />
                                <Button
                                    Background="#106EBE"
                                    Command="{Binding SelectCustomColorCommand}"
                                    CommandParameter="#106EBE"
                                    Margin="4"
                                    Style="{StaticResource AccentColorButtonStyle}"
                                    ToolTip="Deep Blue - 深蓝" />
                                <Button
                                    Background="#005A9E"
                                    Command="{Binding SelectCustomColorCommand}"
                                    CommandParameter="#005A9E"
                                    Margin="4"
                                    Style="{StaticResource AccentColorButtonStyle}"
                                    ToolTip="Navy Blue - 海军蓝" />
                                <Button
                                    Background="#004578"
                                    Command="{Binding SelectCustomColorCommand}"
                                    CommandParameter="#004578"
                                    Margin="4"
                                    Style="{StaticResource AccentColorButtonStyle}"
                                    ToolTip="Dark Navy - 深海军蓝" />
                                <Button
                                    Background="#40E0D0"
                                    Command="{Binding SelectCustomColorCommand}"
                                    CommandParameter="#40E0D0"
                                    Margin="4"
                                    Style="{StaticResource AccentColorButtonStyle}"
                                    ToolTip="Turquoise - 绿松石" />
                                <Button
                                    Background="#00CED1"
                                    Command="{Binding SelectCustomColorCommand}"
                                    CommandParameter="#00CED1"
                                    Margin="4"
                                    Style="{StaticResource AccentColorButtonStyle}"
                                    ToolTip="Dark Turquoise - 深绿松石" />
                                <Button
                                    Background="#5F9EA0"
                                    Command="{Binding SelectCustomColorCommand}"
                                    CommandParameter="#5F9EA0"
                                    Margin="4"
                                    Style="{StaticResource AccentColorButtonStyle}"
                                    ToolTip="Cadet Blue - 军校蓝" />
                                <Button
                                    Background="#4682B4"
                                    Command="{Binding SelectCustomColorCommand}"
                                    CommandParameter="#4682B4"
                                    Margin="4"
                                    Style="{StaticResource AccentColorButtonStyle}"
                                    ToolTip="Steel Blue - 钢蓝" />

                                <!--  第二行：绿色系  -->
                                <Button
                                    Background="#107C10"
                                    Command="{Binding SelectCustomColorCommand}"
                                    CommandParameter="#107C10"
                                    Margin="4"
                                    Style="{StaticResource AccentColorButtonStyle}"
                                    ToolTip="Forest Green - 森林绿" />
                                <Button
                                    Background="#0E700E"
                                    Command="{Binding SelectCustomColorCommand}"
                                    CommandParameter="#0E700E"
                                    Margin="4"
                                    Style="{StaticResource AccentColorButtonStyle}"
                                    ToolTip="Dark Green - 深绿" />
                                <Button
                                    Background="#0C5F0C"
                                    Command="{Binding SelectCustomColorCommand}"
                                    CommandParameter="#0C5F0C"
                                    Margin="4"
                                    Style="{StaticResource AccentColorButtonStyle}"
                                    ToolTip="Deep Green - 墨绿" />
                                <Button
                                    Background="#0A4F0A"
                                    Command="{Binding SelectCustomColorCommand}"
                                    CommandParameter="#0A4F0A"
                                    Margin="4"
                                    Style="{StaticResource AccentColorButtonStyle}"
                                    ToolTip="Pine Green - 松绿" />
                                <Button
                                    Background="#32CD32"
                                    Command="{Binding SelectCustomColorCommand}"
                                    CommandParameter="#32CD32"
                                    Margin="4"
                                    Style="{StaticResource AccentColorButtonStyle}"
                                    ToolTip="Lime Green - 酸橙绿" />
                                <Button
                                    Background="#00FF7F"
                                    Command="{Binding SelectCustomColorCommand}"
                                    CommandParameter="#00FF7F"
                                    Margin="4"
                                    Style="{StaticResource AccentColorButtonStyle}"
                                    ToolTip="Spring Green - 春绿" />
                                <Button
                                    Background="#98FB98"
                                    Command="{Binding SelectCustomColorCommand}"
                                    CommandParameter="#98FB98"
                                    Margin="4"
                                    Style="{StaticResource AccentColorButtonStyle}"
                                    ToolTip="Pale Green - 淡绿" />
                                <Button
                                    Background="#90EE90"
                                    Command="{Binding SelectCustomColorCommand}"
                                    CommandParameter="#90EE90"
                                    Margin="4"
                                    Style="{StaticResource AccentColorButtonStyle}"
                                    ToolTip="Light Green - 浅绿" />

                                <!--  第三行：暖色系  -->
                                <Button
                                    Background="#D13438"
                                    Command="{Binding SelectCustomColorCommand}"
                                    CommandParameter="#D13438"
                                    Margin="4"
                                    Style="{StaticResource AccentColorButtonStyle}"
                                    ToolTip="Crimson Red - 深红" />
                                <Button
                                    Background="#FF4500"
                                    Command="{Binding SelectCustomColorCommand}"
                                    CommandParameter="#FF4500"
                                    Margin="4"
                                    Style="{StaticResource AccentColorButtonStyle}"
                                    ToolTip="Orange Red - 橙红" />
                                <Button
                                    Background="#FF8C00"
                                    Command="{Binding SelectCustomColorCommand}"
                                    CommandParameter="#FF8C00"
                                    Margin="4"
                                    Style="{StaticResource AccentColorButtonStyle}"
                                    ToolTip="Dark Orange - 深橙" />
                                <Button
                                    Background="#FFD700"
                                    Command="{Binding SelectCustomColorCommand}"
                                    CommandParameter="#FFD700"
                                    Margin="4"
                                    Style="{StaticResource AccentColorButtonStyle}"
                                    ToolTip="Gold - 金色" />
                                <Button
                                    Background="#9A0089"
                                    Command="{Binding SelectCustomColorCommand}"
                                    CommandParameter="#9A0089"
                                    Margin="4"
                                    Style="{StaticResource AccentColorButtonStyle}"
                                    ToolTip="Purple - 紫色" />
                                <Button
                                    Background="#8B008B"
                                    Command="{Binding SelectCustomColorCommand}"
                                    CommandParameter="#8B008B"
                                    Margin="4"
                                    Style="{StaticResource AccentColorButtonStyle}"
                                    ToolTip="Dark Magenta - 深洋红" />
                                <Button
                                    Background="#DA70D6"
                                    Command="{Binding SelectCustomColorCommand}"
                                    CommandParameter="#DA70D6"
                                    Margin="4"
                                    Style="{StaticResource AccentColorButtonStyle}"
                                    ToolTip="Orchid - 兰花紫" />
                                <Button
                                    Background="#DDA0DD"
                                    Command="{Binding SelectCustomColorCommand}"
                                    CommandParameter="#DDA0DD"
                                    Margin="4"
                                    Style="{StaticResource AccentColorButtonStyle}"
                                    ToolTip="Plum - 梅花紫" />
                            </UniformGrid>
                        </StackPanel>

                        <!--  中性色调色板  -->
                        <StackPanel Margin="0,0,0,24">
                            <TextBlock
                                FontSize="15"
                                FontWeight="Medium"
                                Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                Margin="0,0,0,12"
                                Text="中性色调" />

                            <UniformGrid Columns="8" Rows="1">
                                <Button
                                    Background="#000000"
                                    Command="{Binding SelectCustomColorCommand}"
                                    CommandParameter="#000000"
                                    Margin="4"
                                    Style="{StaticResource AccentColorButtonStyle}"
                                    ToolTip="Black - 黑色" />
                                <Button
                                    Background="#2D2D30"
                                    Command="{Binding SelectCustomColorCommand}"
                                    CommandParameter="#2D2D30"
                                    Margin="4"
                                    Style="{StaticResource AccentColorButtonStyle}"
                                    ToolTip="Dark Gray - 深灰" />
                                <Button
                                    Background="#3E3E42"
                                    Command="{Binding SelectCustomColorCommand}"
                                    CommandParameter="#3E3E42"
                                    Margin="4"
                                    Style="{StaticResource AccentColorButtonStyle}"
                                    ToolTip="Charcoal - 炭灰" />
                                <Button
                                    Background="#68768A"
                                    Command="{Binding SelectCustomColorCommand}"
                                    CommandParameter="#68768A"
                                    Margin="4"
                                    Style="{StaticResource AccentColorButtonStyle}"
                                    ToolTip="Slate Gray - 石板灰" />
                                <Button
                                    Background="#8A8886"
                                    Command="{Binding SelectCustomColorCommand}"
                                    CommandParameter="#8A8886"
                                    Margin="4"
                                    Style="{StaticResource AccentColorButtonStyle}"
                                    ToolTip="Gray - 灰色" />
                                <Button
                                    Background="#C8C6C4"
                                    Command="{Binding SelectCustomColorCommand}"
                                    CommandParameter="#C8C6C4"
                                    Margin="4"
                                    Style="{StaticResource AccentColorButtonStyle}"
                                    ToolTip="Light Gray - 浅灰" />
                                <Button
                                    Background="#EDEBE9"
                                    Command="{Binding SelectCustomColorCommand}"
                                    CommandParameter="#EDEBE9"
                                    Margin="4"
                                    Style="{StaticResource AccentColorButtonStyle}"
                                    ToolTip="Off White - 米白" />
                                <Button
                                    Background="#FFFFFF"
                                    Command="{Binding SelectCustomColorCommand}"
                                    CommandParameter="#FFFFFF"
                                    Margin="4"
                                    Style="{StaticResource AccentColorButtonStyle}"
                                    ToolTip="White - 白色" />
                            </UniformGrid>
                        </StackPanel>

                        <!--  RGB 颜色选择器  -->
                        <StackPanel>
                            <TextBlock
                                FontSize="15"
                                FontWeight="Medium"
                                Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                Margin="0,0,0,12"
                                Text="🎨 RGB 颜色选择器" />

                            <!--  当前选择的颜色预览  -->
                            <Border
                                Background="{Binding CurrentRgbColor, FallbackValue=#0078D4}"
                                BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                BorderThickness="1"
                                CornerRadius="8"
                                Height="60"
                                Margin="0,0,0,16">
                                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <TextBlock
                                        FontSize="14"
                                        FontWeight="SemiBold"
                                        Foreground="White"
                                        HorizontalAlignment="Center"
                                        Text="当前颜色" />
                                    <TextBlock
                                        FontSize="12"
                                        Foreground="White"
                                        HorizontalAlignment="Center"
                                        Text="{Binding CurrentRgbColorHex, FallbackValue=#0078D4}" />
                                </StackPanel>
                            </Border>

                            <!--  RGB 滑块  -->
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="30" />
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="60" />
                                </Grid.ColumnDefinitions>

                                <!--  Red 滑块  -->
                                <TextBlock
                                    FontWeight="SemiBold"
                                    Foreground="#FF4444"
                                    Grid.Column="0"
                                    Grid.Row="0"
                                    Text="R"
                                    VerticalAlignment="Center" />
                                <Slider
                                    Grid.Column="1"
                                    Grid.Row="0"
                                    Margin="8,0"
                                    Maximum="255"
                                    Minimum="0"
                                    Value="{Binding RedValue, Mode=TwoWay}" />
                                <TextBox
                                    Grid.Column="2"
                                    Grid.Row="0"
                                    Margin="8,0,0,0"
                                    Text="{Binding RedValue, Mode=TwoWay}"
                                    VerticalAlignment="Center" />

                                <!--  Green 滑块  -->
                                <TextBlock
                                    FontWeight="SemiBold"
                                    Foreground="#44FF44"
                                    Grid.Column="0"
                                    Grid.Row="1"
                                    Margin="0,8,0,0"
                                    Text="G"
                                    VerticalAlignment="Center" />
                                <Slider
                                    Grid.Column="1"
                                    Grid.Row="1"
                                    Margin="8,8,8,0"
                                    Maximum="255"
                                    Minimum="0"
                                    Value="{Binding GreenValue, Mode=TwoWay}" />
                                <TextBox
                                    Grid.Column="2"
                                    Grid.Row="1"
                                    Margin="8,8,0,0"
                                    Text="{Binding GreenValue, Mode=TwoWay}"
                                    VerticalAlignment="Center" />

                                <!--  Blue 滑块  -->
                                <TextBlock
                                    FontWeight="SemiBold"
                                    Foreground="#4444FF"
                                    Grid.Column="0"
                                    Grid.Row="2"
                                    Margin="0,8,0,0"
                                    Text="B"
                                    VerticalAlignment="Center" />
                                <Slider
                                    Grid.Column="1"
                                    Grid.Row="2"
                                    Margin="8,8,8,0"
                                    Maximum="255"
                                    Minimum="0"
                                    Value="{Binding BlueValue, Mode=TwoWay}" />
                                <TextBox
                                    Grid.Column="2"
                                    Grid.Row="2"
                                    Margin="8,8,0,0"
                                    Text="{Binding BlueValue, Mode=TwoWay}"
                                    VerticalAlignment="Center" />

                                <!--  应用按钮  -->
                                <ui:Button
                                    Appearance="Primary"
                                    Background="{DynamicResource SystemAccentColorPrimaryBrush}"
                                    Command="{Binding ApplyRgbColorCommand}"
                                    Content="🎨 应用此颜色"
                                    Grid.Column="1"
                                    Grid.Row="3"
                                    HorizontalAlignment="Stretch"
                                    Margin="8,16,8,0" />
                            </Grid>
                        </StackPanel>
                    </StackPanel>
                </ui:Card>

                <!--  预览效果  -->
                <ui:Card Margin="0,0,0,32" Padding="24">
                    <StackPanel>
                        <StackPanel Margin="0,0,0,24" Orientation="Horizontal">
                            <ui:SymbolIcon
                                FontSize="24"
                                Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                Symbol="Eye24" />
                            <StackPanel>
                                <TextBlock
                                    FontSize="18"
                                    FontWeight="SemiBold"
                                    Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                    Text="预览效果" />
                                <TextBlock
                                    FontSize="14"
                                    Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                    Text="查看当前主题设置的效果" />
                            </StackPanel>
                        </StackPanel>

                        <StackPanel>
                            <!--  按钮预览  -->
                            <WrapPanel Margin="0,0,0,16">
                                <ui:Button
                                    Appearance="Primary"
                                    Background="{DynamicResource SystemAccentColorPrimaryBrush}"
                                    Content="主要按钮"
                                    Margin="0,0,16,8" />
                                <ui:Button
                                    Appearance="Secondary"
                                    Content="次要按钮"
                                    Margin="0,0,16,8" />
                                <ui:Button
                                    Appearance="Transparent"
                                    Content="透明按钮"
                                    Margin="0,0,16,8" />
                            </WrapPanel>

                            <!--  文本预览  -->
                            <StackPanel Margin="0,0,0,24">
                                <TextBlock
                                    FontWeight="SemiBold"
                                    Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                    Margin="0,0,0,8"
                                    Text="这是主要文本" />
                                <TextBlock
                                    Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                    Margin="0,0,0,8"
                                    Text="这是次要文本" />
                                <TextBlock
                                    FontWeight="SemiBold"
                                    Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                    Text="这是强调色文本" />
                            </StackPanel>
                        </StackPanel>

                        <!--  高级设置按钮  -->
                        <ui:Button
                            Appearance="Secondary"
                            Command="{Binding NavigateToColorShowcaseCommand}"
                            Content="🎨 高级颜色设置"
                            HorizontalAlignment="Left" />
                    </StackPanel>
                </ui:Card>

                <!--  强调色效果展示  -->
                <ui:Card Margin="0,0,0,32" Padding="24">
                    <StackPanel>
                        <StackPanel Margin="0,0,0,24" Orientation="Horizontal">
                            <ui:SymbolIcon
                                FontSize="24"
                                Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                Symbol="ColorBackground24" />
                            <StackPanel Margin="16,0,0,0">
                                <TextBlock
                                    FontSize="18"
                                    FontWeight="SemiBold"
                                    Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                    Text="强调色效果展示" />
                                <TextBlock
                                    FontSize="14"
                                    Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                    Text="查看当前强调色的所有变体效果" />
                            </StackPanel>
                        </StackPanel>

                        <StackPanel>
                            <!--  System强调色系列  -->
                            <TextBlock
                                FontWeight="SemiBold"
                                Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                Margin="0,0,0,16"
                                Text="System强调色系列 (用于文本、边框、图标):" />

                            <!--  SystemAccentColorPrimaryBrush  -->
                            <Border
                                Background="{DynamicResource SystemAccentColorPrimaryBrush}"
                                BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                BorderThickness="1"
                                CornerRadius="4"
                                Cursor="Hand"
                                Height="40"
                                Margin="0,0,0,8"
                                Tag="SystemAccentColorPrimaryBrush"
                                ToolTip="点击复制资源名称">
                                <TextBlock
                                    FontSize="12"
                                    FontWeight="SemiBold"
                                    Foreground="{DynamicResource TextFillColorInverseBrush}"
                                    HorizontalAlignment="Center"
                                    Text="SystemAccentColorPrimaryBrush"
                                    VerticalAlignment="Center" />
                            </Border>

                            <!--  SystemAccentColorSecondaryBrush  -->
                            <Border
                                Background="{DynamicResource SystemAccentColorSecondaryBrush}"
                                BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                BorderThickness="1"
                                CornerRadius="4"
                                Cursor="Hand"
                                Height="40"
                                Margin="0,0,0,8"
                                Tag="SystemAccentColorSecondaryBrush"
                                ToolTip="点击复制资源名称">
                                <TextBlock
                                    FontSize="12"
                                    FontWeight="SemiBold"
                                    Foreground="{DynamicResource TextFillColorInverseBrush}"
                                    HorizontalAlignment="Center"
                                    Text="SystemAccentColorSecondaryBrush"
                                    VerticalAlignment="Center" />
                            </Border>

                            <!--  SystemAccentColorTertiaryBrush  -->
                            <Border
                                Background="{DynamicResource SystemAccentColorTertiaryBrush}"
                                BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                BorderThickness="1"
                                CornerRadius="4"
                                Cursor="Hand"
                                Height="40"
                                Margin="0,0,0,24"
                                Tag="SystemAccentColorTertiaryBrush"
                                ToolTip="点击复制资源名称">
                                <TextBlock
                                    FontSize="12"
                                    FontWeight="SemiBold"
                                    Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                    HorizontalAlignment="Center"
                                    Text="SystemAccentColorTertiaryBrush"
                                    VerticalAlignment="Center" />
                            </Border>

                            <!--  AccentFill强调色系列  -->
                            <TextBlock
                                FontWeight="SemiBold"
                                Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                Margin="0,0,0,16"
                                Text="AccentFill强调色系列 (用于按钮背景、控件填充):" />

                            <!--  AccentFillColorDefaultBrush  -->
                            <Border
                                Background="{DynamicResource AccentFillColorDefaultBrush}"
                                BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                BorderThickness="1"
                                CornerRadius="4"
                                Cursor="Hand"
                                Height="40"
                                Margin="0,0,0,8"
                                Tag="AccentFillColorDefaultBrush"
                                ToolTip="点击复制资源名称">
                                <TextBlock
                                    FontSize="12"
                                    FontWeight="SemiBold"
                                    Foreground="{DynamicResource TextFillColorInverseBrush}"
                                    HorizontalAlignment="Center"
                                    Text="AccentFillColorDefaultBrush"
                                    VerticalAlignment="Center" />
                            </Border>

                            <!--  AccentFillColorSecondaryBrush  -->
                            <Border
                                Background="{DynamicResource AccentFillColorSecondaryBrush}"
                                BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                BorderThickness="1"
                                CornerRadius="4"
                                Cursor="Hand"
                                Height="40"
                                Margin="0,0,0,8"
                                Tag="AccentFillColorSecondaryBrush"
                                ToolTip="点击复制资源名称">
                                <TextBlock
                                    FontSize="12"
                                    FontWeight="SemiBold"
                                    Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                    HorizontalAlignment="Center"
                                    Text="AccentFillColorSecondaryBrush"
                                    VerticalAlignment="Center" />
                            </Border>

                            <!--  AccentFillColorTertiaryBrush  -->
                            <Border
                                Background="{DynamicResource AccentFillColorTertiaryBrush}"
                                BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                BorderThickness="1"
                                CornerRadius="4"
                                Cursor="Hand"
                                Height="40"
                                Margin="0,0,0,8"
                                Tag="AccentFillColorTertiaryBrush"
                                ToolTip="点击复制资源名称">
                                <TextBlock
                                    FontSize="12"
                                    FontWeight="SemiBold"
                                    Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                    HorizontalAlignment="Center"
                                    Text="AccentFillColorTertiaryBrush"
                                    VerticalAlignment="Center" />
                            </Border>

                            <!--  AccentFillColorDisabledBrush  -->
                            <Border
                                Background="{DynamicResource AccentFillColorDisabledBrush}"
                                BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                BorderThickness="1"
                                CornerRadius="4"
                                Cursor="Hand"
                                Height="40"
                                Margin="0,0,0,0"
                                Tag="AccentFillColorDisabledBrush"
                                ToolTip="点击复制资源名称">
                                <TextBlock
                                    FontSize="12"
                                    FontWeight="SemiBold"
                                    Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                    HorizontalAlignment="Center"
                                    Text="AccentFillColorDisabledBrush"
                                    VerticalAlignment="Center" />
                            </Border>
                        </StackPanel>
                    </StackPanel>
                </ui:Card>

            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
