<!-- Button 基础用法示例 -->
<StackPanel Margin="20" Spacing="15">
    
    <!-- 基础按钮 -->
    <GroupBox Header="基础按钮" Padding="15">
        <StackPanel Orientation="Horizontal" Spacing="10">
            <ui:Button Content="默认按钮"/>
            <ui:Button Content="主要按钮" Appearance="Primary"/>
            <ui:Button Content="次要按钮" Appearance="Secondary"/>
            <ui:Button Content="危险按钮" Appearance="Danger"/>
        </StackPanel>
    </GroupBox>

    <!-- 带图标的按钮 -->
    <GroupBox Header="带图标的按钮" Padding="15">
        <StackPanel Orientation="Horizontal" Spacing="10">
            <ui:Button Appearance="Primary">
                <StackPanel Orientation="Horizontal">
                    <zylo:ZyloIcon Icon="Add" FontSize="16" Margin="0,0,5,0"/>
                    <TextBlock Text="添加"/>
                </StackPanel>
            </ui:Button>
            
            <ui:Button Appearance="Secondary">
                <StackPanel Orientation="Horizontal">
                    <zylo:ZyloIcon Icon="Edit" FontSize="16" Margin="0,0,5,0"/>
                    <TextBlock Text="编辑"/>
                </StackPanel>
            </ui:Button>
            
            <ui:Button Appearance="Danger">
                <StackPanel Orientation="Horizontal">
                    <zylo:ZyloIcon Icon="Delete" FontSize="16" Margin="0,0,5,0"/>
                    <TextBlock Text="删除"/>
                </StackPanel>
            </ui:Button>
        </StackPanel>
    </GroupBox>

    <!-- 不同尺寸的按钮 -->
    <GroupBox Header="不同尺寸" Padding="15">
        <StackPanel Orientation="Horizontal" Spacing="10" VerticalAlignment="Center">
            <ui:Button Content="小按钮" Padding="8,4" FontSize="12"/>
            <ui:Button Content="默认按钮" Padding="12,6" FontSize="14"/>
            <ui:Button Content="大按钮" Padding="16,8" FontSize="16"/>
        </StackPanel>
    </GroupBox>

    <!-- 按钮状态 -->
    <GroupBox Header="按钮状态" Padding="15">
        <StackPanel Orientation="Horizontal" Spacing="10">
            <ui:Button Content="正常状态" Appearance="Primary"/>
            <ui:Button Content="禁用状态" Appearance="Primary" IsEnabled="False"/>
            <ui:Button Content="加载中..." Appearance="Primary">
                <ui:Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <ui:ProgressRing Width="16" Height="16" Margin="0,0,5,0"/>
                        <TextBlock Text="加载中..."/>
                    </StackPanel>
                </ui:Button.Content>
            </ui:Button>
        </StackPanel>
    </GroupBox>

    <!-- 命令绑定 -->
    <GroupBox Header="命令绑定" Padding="15">
        <StackPanel Orientation="Horizontal" Spacing="10">
            <ui:Button Content="保存" 
                      Command="{Binding SaveCommand}"
                      Appearance="Primary"/>
            
            <ui:Button Content="取消" 
                      Command="{Binding CancelCommand}"
                      Appearance="Secondary"/>
            
            <ui:Button Content="重置" 
                      Command="{Binding ResetCommand}"
                      CommandParameter="reset_all"/>
        </StackPanel>
    </GroupBox>

</StackPanel>
