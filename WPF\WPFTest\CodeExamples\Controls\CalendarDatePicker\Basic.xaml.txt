<!-- DatePicker 基础用法示例 - MVVM模式 -->
<StackPanel Margin="20" Spacing="15">

    <!-- 标准 DatePicker -->
    <GroupBox Header="标准 DatePicker" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <DatePicker SelectedDate="{Binding SelectedDate, Mode=TwoWay}"
                        Width="160"
                        Height="36"
                        Margin="4"/>

            <DatePicker SelectedDate="{Binding BirthDate, Mode=TwoWay}"
                        Width="160"
                        Height="36"
                        Margin="4"/>

            <DatePicker SelectedDate="{Binding EventDate, Mode=TwoWay}"
                        Width="160"
                        Height="36"
                        Margin="4"/>
        </WrapPanel>
    </GroupBox>

    <!-- 不同尺寸 -->
    <GroupBox Header="不同尺寸" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <DatePicker SelectedDate="{Binding SelectedDate, Mode=TwoWay}"
                        Width="140"
                        Height="32"
                        FontSize="12"
                        Margin="4"/>
            <DatePicker SelectedDate="{Binding SelectedDate, Mode=TwoWay}"
                        Width="160"
                        Height="36"
                        FontSize="14"
                        Margin="4"/>
            <DatePicker SelectedDate="{Binding SelectedDate, Mode=TwoWay}"
                        Width="180"
                        Height="44"
                        FontSize="16"
                        Margin="4"/>
        </WrapPanel>
    </GroupBox>

    <!-- 不同配置 -->
    <GroupBox Header="不同配置" Padding="15">
        <StackPanel>
            <!-- 基础配置 -->
            <DatePicker SelectedDate="{Binding SelectedDate, Mode=TwoWay}"
                        IsTodayHighlighted="True"
                        FirstDayOfWeek="Monday"
                        Width="160"
                        Height="36"
                        Margin="0,0,0,8"/>

            <!-- 自定义配置 -->
            <DatePicker SelectedDate="{Binding SelectedDate, Mode=TwoWay}"
                        IsTodayHighlighted="False"
                        FirstDayOfWeek="Sunday"
                        Width="160"
                        Height="36"
                        Margin="0,0,0,8"/>

            <!-- 禁用状态 -->
            <DatePicker SelectedDate="{Binding SelectedDate, Mode=TwoWay}"
                        IsEnabled="False"
                        Width="160"
                        Height="36"
                        Margin="0,0,0,8"/>
        </StackPanel>
    </GroupBox>

    <!-- 数据绑定示例 -->
    <GroupBox Header="数据绑定示例" Padding="15">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- 左侧：日期选择器 -->
            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                <TextBlock Text="选择日期：" Margin="0,0,0,4"/>
                <DatePicker SelectedDate="{Binding SelectedDate, Mode=TwoWay}"
                           Width="160"
                           Height="36"/>
            </StackPanel>
            
            <!-- 右侧：显示结果 -->
            <StackPanel Grid.Column="1" Margin="10,0,0,0">
                <TextBlock Text="选择的日期：" Margin="0,0,0,4"/>
                <TextBlock Text="{Binding SelectedDate, StringFormat='{}{0:yyyy-MM-dd}'}"
                           FontWeight="Bold"
                           Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                <TextBlock Text="{Binding SelectedDate, StringFormat='星期{0:dddd}'}"
                           FontSize="12"
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                           Margin="0,4,0,0"/>
            </StackPanel>
        </Grid>
    </GroupBox>

</StackPanel>
