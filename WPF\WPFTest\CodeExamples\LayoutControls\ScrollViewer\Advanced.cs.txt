// ScrollViewer 高级 C# 示例
// 展示如何通过代码实现高级滚动功能和自定义行为

using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Collections.ObjectModel;
using System.ComponentModel;

namespace WPFTest.Examples.LayoutControls
{
    public class ScrollViewerAdvancedExample
    {
        /// <summary>
        /// 创建双向滚动的大型数据表格
        /// </summary>
        public static ScrollViewer CreateLargeDataGrid()
        {
            var scrollViewer = new ScrollViewer
            {
                Height = 200,
                Width = 400,
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                HorizontalScrollBarVisibility = ScrollBarVisibility.Auto,
                Background = new SolidColorBrush(Colors.White),
                BorderBrush = new SolidColorBrush(Colors.Gray),
                BorderThickness = new Thickness(1)
            };

            var grid = new Grid
            {
                Margin = new Thickness(16)
            };

            // 创建大型网格（10行 x 8列）
            for (int i = 0; i < 8; i++)
            {
                grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(120) });
            }

            for (int i = 0; i < 10; i++)
            {
                grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(40) });
            }

            // 填充网格数据
            var colors = new[] { Colors.LightBlue, Colors.LightGreen, Colors.LightYellow, Colors.LightPink };
            for (int row = 0; row < 10; row++)
            {
                for (int col = 0; col < 8; col++)
                {
                    var cell = new Border
                    {
                        Background = new SolidColorBrush(colors[(row + col) % colors.Length]),
                        BorderBrush = new SolidColorBrush(Colors.Gray),
                        BorderThickness = new Thickness(0.5),
                        Margin = new Thickness(1)
                    };

                    cell.Child = new TextBlock
                    {
                        Text = $"{(char)('A' + col)}{row + 1}",
                        HorizontalAlignment = HorizontalAlignment.Center,
                        VerticalAlignment = VerticalAlignment.Center,
                        FontWeight = FontWeights.Bold
                    };

                    Grid.SetRow(cell, row);
                    Grid.SetColumn(cell, col);
                    grid.Children.Add(cell);
                }
            }

            scrollViewer.Content = grid;
            return scrollViewer;
        }

        /// <summary>
        /// 创建虚拟化滚动列表
        /// </summary>
        public static ScrollViewer CreateVirtualizedList()
        {
            var scrollViewer = new ScrollViewer
            {
                Height = 200,
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                HorizontalScrollBarVisibility = ScrollBarVisibility.Disabled,
                Background = new SolidColorBrush(Colors.White)
            };

            var listBox = new ListBox
            {
                VirtualizingPanel.VirtualizationMode = VirtualizationMode.Recycling,
                VirtualizingPanel.IsVirtualizing = true,
                ScrollViewer.CanContentScroll = true
            };

            // 创建大量数据项
            var items = new ObservableCollection<string>();
            for (int i = 1; i <= 10000; i++)
            {
                items.Add($"虚拟化项目 {i} - 这是一个大型列表中的项目");
            }

            listBox.ItemsSource = items;
            scrollViewer.Content = listBox;

            return scrollViewer;
        }

        /// <summary>
        /// 创建带有自定义滚动行为的 ScrollViewer
        /// </summary>
        public static ScrollViewer CreateCustomScrollBehavior()
        {
            var scrollViewer = new ScrollViewer
            {
                Height = 150,
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                HorizontalScrollBarVisibility = ScrollBarVisibility.Disabled,
                Background = new SolidColorBrush(Colors.LightGray),
                CanContentScroll = true,
                IsDeferredScrollingEnabled = true,
                PanningMode = PanningMode.VerticalOnly
            };

            // 添加滚动事件处理
            scrollViewer.ScrollChanged += OnScrollChanged;

            var content = new StackPanel
            {
                Margin = new Thickness(16)
            };

            content.Children.Add(new TextBlock
            {
                Text = "自定义滚动行为示例",
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 8)
            });

            content.Children.Add(new TextBlock
            {
                Text = "• CanContentScroll: 启用逻辑滚动",
                Margin = new Thickness(0, 0, 0, 4)
            });

            content.Children.Add(new TextBlock
            {
                Text = "• IsDeferredScrollingEnabled: 延迟滚动",
                Margin = new Thickness(0, 0, 0, 4)
            });

            content.Children.Add(new TextBlock
            {
                Text = "• PanningMode: 触摸平移模式",
                Margin = new Thickness(0, 0, 0, 4)
            });

            for (int i = 1; i <= 8; i++)
            {
                content.Children.Add(new TextBlock
                {
                    Text = $"内容 {i}",
                    Margin = new Thickness(0, 0, 0, 4)
                });
            }

            scrollViewer.Content = content;
            return scrollViewer;
        }

        /// <summary>
        /// 滚动事件处理程序
        /// </summary>
        private static void OnScrollChanged(object sender, ScrollChangedEventArgs e)
        {
            var scrollViewer = sender as ScrollViewer;
            if (scrollViewer != null)
            {
                System.Diagnostics.Debug.WriteLine($"滚动位置改变: 垂直={e.VerticalOffset:F1}, 水平={e.HorizontalOffset:F1}");
                System.Diagnostics.Debug.WriteLine($"可视区域: {e.ViewportWidth:F1} x {e.ViewportHeight:F1}");
                System.Diagnostics.Debug.WriteLine($"内容大小: {e.ExtentWidth:F1} x {e.ExtentHeight:F1}");
            }
        }

        /// <summary>
        /// 创建支持滚动到指定元素的 ScrollViewer
        /// </summary>
        public static (ScrollViewer scrollViewer, FrameworkElement targetElement) CreateScrollToElementDemo()
        {
            var scrollViewer = new ScrollViewer
            {
                Height = 150,
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                HorizontalScrollBarVisibility = ScrollBarVisibility.Disabled,
                Background = new SolidColorBrush(Colors.White),
                BorderBrush = new SolidColorBrush(Colors.Gray),
                BorderThickness = new Thickness(1)
            };

            var content = new StackPanel
            {
                Margin = new Thickness(16)
            };

            content.Children.Add(new TextBlock
            {
                Text = "滚动到指定元素示例",
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 8)
            });

            // 添加多个元素
            for (int i = 1; i <= 4; i++)
            {
                content.Children.Add(new TextBlock
                {
                    Text = $"元素 {i}",
                    Margin = new Thickness(0, 0, 0, 4)
                });
            }

            // 目标元素
            var targetElement = new TextBlock
            {
                Text = "🎯 目标元素",
                Background = new SolidColorBrush(Colors.Orange),
                Foreground = Brushes.White,
                Padding = new Thickness(8, 4),
                Margin = new Thickness(0, 0, 0, 4),
                FontWeight = FontWeights.Bold
            };
            content.Children.Add(targetElement);

            // 添加更多元素
            for (int i = 5; i <= 10; i++)
            {
                content.Children.Add(new TextBlock
                {
                    Text = $"元素 {i}",
                    Margin = new Thickness(0, 0, 0, 4)
                });
            }

            scrollViewer.Content = content;
            return (scrollViewer, targetElement);
        }

        /// <summary>
        /// 滚动到指定元素
        /// </summary>
        public static void ScrollToElement(ScrollViewer scrollViewer, FrameworkElement element)
        {
            if (scrollViewer?.Content is FrameworkElement content)
            {
                // 获取元素相对于内容的位置
                var transform = element.TransformToAncestor(content);
                var position = transform.Transform(new Point(0, 0));

                // 滚动到元素位置
                scrollViewer.ScrollToVerticalOffset(position.Y);
            }
        }

        /// <summary>
        /// 创建平滑滚动动画
        /// </summary>
        public static void AnimateScrollToPosition(ScrollViewer scrollViewer, double targetOffset, TimeSpan duration)
        {
            var animation = new DoubleAnimation
            {
                From = scrollViewer.VerticalOffset,
                To = targetOffset,
                Duration = duration,
                EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseInOut }
            };

            // 创建自定义动画目标
            var scrollTarget = new ScrollTarget(scrollViewer);
            scrollTarget.BeginAnimation(ScrollTarget.VerticalOffsetProperty, animation);
        }

        /// <summary>
        /// 自定义滚动动画目标类
        /// </summary>
        private class ScrollTarget : DependencyObject
        {
            private readonly ScrollViewer _scrollViewer;

            public ScrollTarget(ScrollViewer scrollViewer)
            {
                _scrollViewer = scrollViewer;
            }

            public static readonly DependencyProperty VerticalOffsetProperty =
                DependencyProperty.Register(
                    nameof(VerticalOffset),
                    typeof(double),
                    typeof(ScrollTarget),
                    new PropertyMetadata(0.0, OnVerticalOffsetChanged));

            public double VerticalOffset
            {
                get => (double)GetValue(VerticalOffsetProperty);
                set => SetValue(VerticalOffsetProperty, value);
            }

            private static void OnVerticalOffsetChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
            {
                if (d is ScrollTarget target)
                {
                    target._scrollViewer.ScrollToVerticalOffset((double)e.NewValue);
                }
            }
        }

        /// <summary>
        /// 创建响应式滚动容器
        /// </summary>
        public static ScrollViewer CreateResponsiveScrollViewer()
        {
            var scrollViewer = new ScrollViewer
            {
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                HorizontalScrollBarVisibility = ScrollBarVisibility.Auto,
                Background = new SolidColorBrush(Colors.White)
            };

            // 响应式高度设置
            scrollViewer.SizeChanged += (sender, e) =>
            {
                var sv = sender as ScrollViewer;
                if (sv != null)
                {
                    // 根据容器大小调整滚动行为
                    if (e.NewSize.Width < 400)
                    {
                        sv.HorizontalScrollBarVisibility = ScrollBarVisibility.Auto;
                    }
                    else
                    {
                        sv.HorizontalScrollBarVisibility = ScrollBarVisibility.Disabled;
                    }
                }
            };

            var content = new StackPanel
            {
                Margin = new Thickness(16)
            };

            content.Children.Add(new TextBlock
            {
                Text = "响应式滚动示例",
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 8)
            });

            content.Children.Add(new TextBlock
            {
                Text = "根据容器大小自动调整滚动行为。",
                Margin = new Thickness(0, 0, 0, 4)
            });

            for (int i = 1; i <= 5; i++)
            {
                content.Children.Add(new TextBlock
                {
                    Text = $"响应式内容 {i}",
                    Margin = new Thickness(0, 0, 0, 4)
                });
            }

            scrollViewer.Content = content;
            return scrollViewer;
        }

        /// <summary>
        /// 滚动控制工具类
        /// </summary>
        public static class ScrollViewerHelper
        {
            /// <summary>
            /// 平滑滚动到顶部
            /// </summary>
            public static void SmoothScrollToTop(ScrollViewer scrollViewer, TimeSpan duration = default)
            {
                if (duration == default)
                    duration = TimeSpan.FromMilliseconds(500);

                AnimateScrollToPosition(scrollViewer, 0, duration);
            }

            /// <summary>
            /// 平滑滚动到底部
            /// </summary>
            public static void SmoothScrollToBottom(ScrollViewer scrollViewer, TimeSpan duration = default)
            {
                if (duration == default)
                    duration = TimeSpan.FromMilliseconds(500);

                var targetOffset = scrollViewer.ExtentHeight - scrollViewer.ViewportHeight;
                AnimateScrollToPosition(scrollViewer, Math.Max(0, targetOffset), duration);
            }

            /// <summary>
            /// 获取滚动百分比
            /// </summary>
            public static double GetScrollPercentage(ScrollViewer scrollViewer)
            {
                if (scrollViewer.ExtentHeight <= scrollViewer.ViewportHeight)
                    return 0;

                return scrollViewer.VerticalOffset / (scrollViewer.ExtentHeight - scrollViewer.ViewportHeight) * 100;
            }

            /// <summary>
            /// 设置滚动百分比
            /// </summary>
            public static void SetScrollPercentage(ScrollViewer scrollViewer, double percentage)
            {
                percentage = Math.Max(0, Math.Min(100, percentage));
                var targetOffset = (scrollViewer.ExtentHeight - scrollViewer.ViewportHeight) * percentage / 100;
                scrollViewer.ScrollToVerticalOffset(targetOffset);
            }

            /// <summary>
            /// 检查元素是否在可视区域内
            /// </summary>
            public static bool IsElementVisible(ScrollViewer scrollViewer, FrameworkElement element)
            {
                if (scrollViewer?.Content is FrameworkElement content)
                {
                    var transform = element.TransformToAncestor(content);
                    var position = transform.Transform(new Point(0, 0));

                    var elementTop = position.Y;
                    var elementBottom = elementTop + element.ActualHeight;
                    var viewportTop = scrollViewer.VerticalOffset;
                    var viewportBottom = viewportTop + scrollViewer.ViewportHeight;

                    return elementBottom > viewportTop && elementTop < viewportBottom;
                }

                return false;
            }
        }
    }
}
