using System.Collections.ObjectModel;
using System.ComponentModel;
using Wpf.Ui.Controls;
using Zylo.WPF.Enums;

namespace Zylo.WPF.Models.TreeNode;

/// <summary>
/// 树形节点数据模型 - 核心属性
/// </summary>
public partial class TreeNodeData : ObservableObject
{
    #region 基础属性

    /// <summary>
    /// 节点ID
    /// </summary>
    [ObservableProperty]
    [Description("节点ID")]
    public partial int Id { get; set; }

    /// <summary>
    /// 节点名称
    /// </summary>
    [ObservableProperty]
    [Description("节点名称")]
    public partial string Name { get; set; } = string.Empty;

    /// <summary>
    /// 节点描述
    /// </summary>
    [ObservableProperty]
    [Description("节点描述")]
    public partial string Description { get; set; } = string.Empty;

    /// <summary>
    /// 节点类型（通用字符串，可以是任何业务类型）
    /// </summary>
    [ObservableProperty]
    [Description("节点类型（通用字符串，可以是任何业务类型）")]
    public partial string NodeType { get; set; } = "Default";

    /// <summary>
    /// 节点分类（用于分组和样式）
    /// </summary>
    [ObservableProperty]
    [Description("节点分类（用于分组和样式）")]
    public partial string Category { get; set; } = "General";

    /// <summary>
    /// 节点标签（支持多标签）
    /// </summary>
    [ObservableProperty]
    [Description("节点标签（支持多标签）")]
    public partial List<string> Tags { get; set; } = new();

    #endregion

    #region 状态属性

    /// <summary>
    /// 是否展开
    /// </summary>
    [ObservableProperty]
    [Description("是否展开")]
    public partial bool IsExpanded { get; set; }

    /// <summary>
    /// 是否选中
    /// </summary>
    [ObservableProperty]
    [Description("是否选中")]
    public partial bool IsSelected { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    [ObservableProperty]
    [Description("是否启用")]
    public partial bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 是否可见
    /// </summary>
    [ObservableProperty]
    [Description("是否可见")]
    public partial bool IsVisible { get; set; } = true;

    /// <summary>
    /// 是否正在加载
    /// </summary>
    [ObservableProperty]
    [Description("是否正在加载")]
    public partial bool IsLoading { get; set; }

    #endregion

    #region 层级关系

    /// <summary>
    /// 父节点
    /// </summary>
    [ObservableProperty]
    [Description("父节点")]
    public partial TreeNodeData? Parent { get; set; }

    /// <summary>
    /// 子节点集合
    /// </summary>
    [ObservableProperty]
    [Description("子节点集合")]
    public partial ObservableCollection<TreeNodeData> Children { get; set; } = new();

    #endregion

    #region 图标属性 - 三图标支持

    /// <summary>
    /// WPF-UI 图标（优先级最高）
    /// </summary>
    [ObservableProperty]
    [Description("WPF-UI 图标（优先级最高）")]
    public partial SymbolRegular? WpfUiSymbol { get; set; }

    /// <summary>
    /// Zylo 自定义图标
    /// </summary>
    [ObservableProperty]
    [Description("Zylo 自定义图标")]
    public partial ZyloSymbol? ZyloSymbol { get; set; }

    /// <summary>
    /// Emoji 图标字符
    /// </summary>
    [ObservableProperty]
    [Description("Emoji 图标字符")]
    public partial string? Emoji { get; set; }

    #endregion

    #region 扩展属性

    /// <summary>
    /// 创建时间
    /// </summary>
    [ObservableProperty]
    [Description("创建时间")]
    public partial DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 修改时间
    /// </summary>
    [ObservableProperty]
    [Description("修改时间")]
    public partial DateTime ModifiedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 排序权重
    /// </summary>
    [ObservableProperty]
    [Description("排序权重")]
    public partial int SortOrder { get; set; }

    /// <summary>
    /// 自定义数据（用于存储额外信息）
    /// </summary>
    [ObservableProperty]
    [Description("自定义数据（用于存储额外信息）")]
    public partial Dictionary<string, object> CustomData { get; set; } = new();

    #endregion

    #region 计算属性

    /// <summary>
    /// 默认图标（如果没有设置三图标属性，则返回基于NodeType的默认图标）
    /// </summary>
    public string DefaultIcon => TreeNodeIconHelper.GetDefaultIconByType(NodeType);

    /// <summary>
    /// 节点颜色（根据类型返回通用颜色）
    /// </summary>
    public string Color => TreeNodeColorHelper.GetColorByType(NodeType);

    /// <summary>
    /// 是否有子节点
    /// </summary>
    public bool HasChildren => Children.Count > 0;

    /// <summary>
    /// 子节点数量
    /// </summary>
    public int ChildrenCount => Children.Count;

    /// <summary>
    /// 节点深度（从根节点开始计算）
    /// </summary>
    public int Depth
    {
        get
        {
            int depth = 0;
            var current = Parent;
            while (current != null)
            {
                depth++;
                current = current.Parent;
            }
            return depth;
        }
    }

    /// <summary>
    /// 是否为根节点
    /// </summary>
    public bool IsRoot => Parent == null;

    /// <summary>
    /// 是否为叶子节点
    /// </summary>
    public bool IsLeaf => !HasChildren;

    /// <summary>
    /// 节点路径（从根到当前节点的路径）
    /// </summary>
    public string Path
    {
        get
        {
            var path = new List<string>();
            var current = this;
            while (current != null)
            {
                path.Insert(0, current.Name);
                current = current.Parent;
            }
            return string.Join(" > ", path);
        }
    }

    #endregion

    #region 构造函数

    /// <summary>
    /// 默认构造函数
    /// </summary>
    public TreeNodeData()
    {
        Children = new ObservableCollection<TreeNodeData>();
    }

    /// <summary>
    /// 创建树节点（基础）
    /// </summary>
    /// <param name="name">节点名称</param>
    public TreeNodeData(string name) : this()
    {
        Name = name;
    }

    /// <summary>
    /// 创建树节点（常用）
    /// </summary>
    /// <param name="id">节点ID</param>
    /// <param name="name">节点名称</param>
    /// <param name="nodeType">节点类型</param>
    public TreeNodeData(int id, string name, string nodeType) : this(name)
    {
        Id = id;
        NodeType = nodeType;
    }

    /// <summary>
    /// 创建树节点（完整）
    /// </summary>
    /// <param name="id">节点ID</param>
    /// <param name="name">节点名称</param>
    /// <param name="description">节点描述</param>
    /// <param name="nodeType">节点类型</param>
    public TreeNodeData(int id, string name, string description, string nodeType) : this(id, name, nodeType)
    {
        Description = description;
    }

    /// <summary>
    /// 创建树节点（带WPF-UI图标）
    /// </summary>
    /// <param name="id">节点ID</param>
    /// <param name="name">节点名称</param>
    /// <param name="description">节点描述</param>
    /// <param name="nodeType">节点类型</param>
    /// <param name="wpfUiSymbol">WPF-UI图标</param>
    public TreeNodeData(int id, string name, string description, string nodeType, SymbolRegular wpfUiSymbol) 
        : this(id, name, description, nodeType)
    {
        WpfUiSymbol = wpfUiSymbol;
    }

    /// <summary>
    /// 创建树节点（带Zylo图标）
    /// </summary>
    /// <param name="id">节点ID</param>
    /// <param name="name">节点名称</param>
    /// <param name="description">节点描述</param>
    /// <param name="nodeType">节点类型</param>
    /// <param name="zyloSymbol">Zylo图标</param>
    public TreeNodeData(int id, string name, string description, string nodeType, ZyloSymbol zyloSymbol) 
        : this(id, name, description, nodeType)
    {
        ZyloSymbol = zyloSymbol;
    }

    /// <summary>
    /// 创建树节点（带Emoji图标）
    /// </summary>
    /// <param name="id">节点ID</param>
    /// <param name="name">节点名称</param>
    /// <param name="description">节点描述</param>
    /// <param name="nodeType">节点类型</param>
    /// <param name="emoji">Emoji图标</param>
    public TreeNodeData(int id, string name, string description, string nodeType, string emoji) 
        : this(id, name, description, nodeType)
    {
        Emoji = emoji;
    }

    #endregion

    #region 克隆方法

    /// <summary>
    /// 浅克隆节点（不包含子节点）
    /// </summary>
    public TreeNodeData Clone()
    {
        var cloned = new TreeNodeData
        {
            Id = this.Id,
            Name = this.Name,
            Description = this.Description,
            NodeType = this.NodeType,
            Category = this.Category,
            Tags = new List<string>(this.Tags),
            IsExpanded = this.IsExpanded,
            IsSelected = false, // 克隆的节点默认不选中
            IsEnabled = this.IsEnabled,
            IsVisible = this.IsVisible,
            IsLoading = false,
            WpfUiSymbol = this.WpfUiSymbol,
            ZyloSymbol = this.ZyloSymbol,
            Emoji = this.Emoji,
            CreatedAt = DateTime.Now,
            ModifiedAt = DateTime.Now,
            SortOrder = this.SortOrder,
            CustomData = new Dictionary<string, object>(this.CustomData)
        };
        return cloned;
    }

    /// <summary>
    /// 深度克隆节点（包含所有子节点）
    /// </summary>
    public TreeNodeData DeepClone()
    {
        var cloned = Clone();

        // 递归克隆所有子节点
        foreach (var child in Children)
        {
            var clonedChild = child.DeepClone();
            clonedChild.Parent = cloned;
            cloned.Children.Add(clonedChild);
        }

        return cloned;
    }

    #endregion

    #region 重写方法

    /// <summary>
    /// 转换为字符串 - 只显示图标和名称
    /// </summary>
    public override string ToString()
    {
        return $"{DefaultIcon} {Name}";
    }

    #endregion
}
