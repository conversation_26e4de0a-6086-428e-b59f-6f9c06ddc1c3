<!-- 基础 Badge 控件示例 -->
<UserControl
    x:Class="WPFTest.Examples.BasicBadgeExample"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml">

    <StackPanel Margin="20" Spacing="20">

        <!-- 基础 Badge 示例 -->
        <GroupBox Header="基础 Badge">
            <StackPanel Spacing="12">
                
                <!-- 简单文本 Badge -->
                <ui:Badge Appearance="Primary">
                    <TextBlock Text="New" />
                </ui:Badge>

                <!-- 不同大小的 Badge -->
                <StackPanel Orientation="Horizontal" Spacing="8">
                    <ui:Badge Appearance="Success" Width="40" Height="20">
                        <TextBlock Text="S" FontSize="10" />
                    </ui:Badge>
                    <ui:Badge Appearance="Success" Width="60" Height="24">
                        <TextBlock Text="M" FontSize="12" />
                    </ui:Badge>
                    <ui:Badge Appearance="Success" Width="80" Height="28">
                        <TextBlock Text="L" FontSize="14" />
                    </ui:Badge>
                </StackPanel>

                <!-- 数字 Badge -->
                <StackPanel Orientation="Horizontal" Spacing="8">
                    <ui:Badge Appearance="Danger">
                        <TextBlock Text="1" />
                    </ui:Badge>
                    <ui:Badge Appearance="Danger">
                        <TextBlock Text="5" />
                    </ui:Badge>
                    <ui:Badge Appearance="Danger">
                        <TextBlock Text="99" />
                    </ui:Badge>
                    <ui:Badge Appearance="Danger">
                        <TextBlock Text="99+" />
                    </ui:Badge>
                </StackPanel>

            </StackPanel>
        </GroupBox>

        <!-- 不同外观样式的 Badge -->
        <GroupBox Header="外观样式">
            <StackPanel Spacing="12">
                
                <!-- 主要样式 -->
                <StackPanel Orientation="Horizontal" Spacing="8">
                    <ui:Badge Appearance="Primary">
                        <TextBlock Text="Primary" />
                    </ui:Badge>
                    <ui:Badge Appearance="Secondary">
                        <TextBlock Text="Secondary" />
                    </ui:Badge>
                    <ui:Badge Appearance="Success">
                        <TextBlock Text="Success" />
                    </ui:Badge>
                </StackPanel>

                <!-- 状态样式 -->
                <StackPanel Orientation="Horizontal" Spacing="8">
                    <ui:Badge Appearance="Danger">
                        <TextBlock Text="Danger" />
                    </ui:Badge>
                    <ui:Badge Appearance="Warning">
                        <TextBlock Text="Warning" />
                    </ui:Badge>
                    <ui:Badge Appearance="Info">
                        <TextBlock Text="Info" />
                    </ui:Badge>
                </StackPanel>

                <!-- 颜色样式 -->
                <StackPanel Orientation="Horizontal" Spacing="8">
                    <ui:Badge Appearance="Light">
                        <TextBlock Text="Light" />
                    </ui:Badge>
                    <ui:Badge Appearance="Dark">
                        <TextBlock Text="Dark" />
                    </ui:Badge>
                    <ui:Badge Appearance="Transparent">
                        <TextBlock Text="Transparent" />
                    </ui:Badge>
                </StackPanel>

            </StackPanel>
        </GroupBox>

        <!-- 带图标的 Badge -->
        <GroupBox Header="带图标的 Badge">
            <StackPanel Spacing="12">
                
                <StackPanel Orientation="Horizontal" Spacing="8">
                    <ui:Badge Appearance="Success">
                        <StackPanel Orientation="Horizontal" Spacing="4">
                            <ui:SymbolIcon Symbol="Checkmark24" FontSize="12" />
                            <TextBlock Text="完成" />
                        </StackPanel>
                    </ui:Badge>
                    
                    <ui:Badge Appearance="Warning">
                        <StackPanel Orientation="Horizontal" Spacing="4">
                            <ui:SymbolIcon Symbol="Warning24" FontSize="12" />
                            <TextBlock Text="警告" />
                        </StackPanel>
                    </ui:Badge>
                    
                    <ui:Badge Appearance="Danger">
                        <StackPanel Orientation="Horizontal" Spacing="4">
                            <ui:SymbolIcon Symbol="Dismiss24" FontSize="12" />
                            <TextBlock Text="错误" />
                        </StackPanel>
                    </ui:Badge>
                </StackPanel>

            </StackPanel>
        </GroupBox>

        <!-- 实际应用场景 -->
        <GroupBox Header="实际应用">
            <StackPanel Spacing="12">
                
                <!-- 通知 Badge -->
                <StackPanel Spacing="8">
                    <TextBlock Text="通知示例:" FontWeight="Medium" />
                    <ui:Badge Appearance="Danger">
                        <ui:Button 
                            Appearance="Primary" 
                            Content="消息" 
                            Icon="{ui:SymbolIcon Mail24}" />
                    </ui:Badge>
                </StackPanel>

                <!-- 状态 Badge -->
                <StackPanel Spacing="8">
                    <TextBlock Text="状态示例:" FontWeight="Medium" />
                    <StackPanel Orientation="Horizontal" Spacing="8">
                        <ui:Badge Appearance="Success">
                            <TextBlock Text="在线" />
                        </ui:Badge>
                        <ui:Badge Appearance="Warning">
                            <TextBlock Text="忙碌" />
                        </ui:Badge>
                        <ui:Badge Appearance="Secondary">
                            <TextBlock Text="离开" />
                        </ui:Badge>
                        <ui:Badge Appearance="Danger">
                            <TextBlock Text="离线" />
                        </ui:Badge>
                    </StackPanel>
                </StackPanel>

                <!-- 标签 Badge -->
                <StackPanel Spacing="8">
                    <TextBlock Text="标签示例:" FontWeight="Medium" />
                    <StackPanel Orientation="Horizontal" Spacing="8">
                        <ui:Badge Appearance="Info">
                            <TextBlock Text="技术" />
                        </ui:Badge>
                        <ui:Badge Appearance="Success">
                            <TextBlock Text="设计" />
                        </ui:Badge>
                        <ui:Badge Appearance="Warning">
                            <TextBlock Text="产品" />
                        </ui:Badge>
                        <ui:Badge Appearance="Primary">
                            <TextBlock Text="管理" />
                        </ui:Badge>
                    </StackPanel>
                </StackPanel>

            </StackPanel>
        </GroupBox>

    </StackPanel>
</UserControl>
