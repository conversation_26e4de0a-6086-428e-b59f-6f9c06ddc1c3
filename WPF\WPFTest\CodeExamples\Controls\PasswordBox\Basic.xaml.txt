<!-- PasswordBox 基础用法示例 -->
<!-- 注意：需要添加命名空间 xmlns:attachedProps="clr-namespace:Zylo.WPF.AttachedProperties;assembly=Zylo.WPF" -->
<StackPanel Margin="20" Spacing="15">

    <!-- 标准 PasswordBox（支持数据绑定） -->
    <GroupBox Header="标准 PasswordBox（支持数据绑定）" Padding="15">
        <StackPanel Spacing="10">
            <TextBlock Text="使用附加属性实现数据绑定：" FontWeight="Bold"/>
            <PasswordBox Style="{StaticResource PasswordBoxStyle}"
                         PlaceholderText="请输入密码"
                         attachedProps:PasswordBoxProperties.EnablePasswordBinding="True"
                         attachedProps:PasswordBoxProperties.BoundPassword="{Binding Password, UpdateSourceTrigger=PropertyChanged}"
                         Margin="4"/>
            <PasswordBox Style="{StaticResource PasswordBoxStyle}"
                         PlaceholderText="确认密码"
                         attachedProps:PasswordBoxProperties.EnablePasswordBinding="True"
                         attachedProps:PasswordBoxProperties.BoundPassword="{Binding ConfirmPassword, UpdateSourceTrigger=PropertyChanged}"
                         Margin="4"/>
            <!-- 密码匹配状态 -->
            <TextBlock Text="{Binding PasswordMatchMessage}"
                       Foreground="{Binding PasswordMatchColor}"
                       FontSize="12"/>
        </StackPanel>
    </GroupBox>

    <!-- 不同尺寸 -->
    <GroupBox Header="不同尺寸" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <PasswordBox Style="{StaticResource SmallPasswordBoxStyle}"
                         ui:TextBoxHelper.PlaceholderText="小型密码框"
                         Margin="4"/>
            <PasswordBox Style="{StaticResource PasswordBoxStyle}"
                         ui:TextBoxHelper.PlaceholderText="标准密码框"
                         Margin="4"/>
            <PasswordBox Style="{StaticResource LargePasswordBoxStyle}"
                         ui:TextBoxHelper.PlaceholderText="大型密码框"
                         Margin="4"/>
        </WrapPanel>
    </GroupBox>

    <!-- 特殊样式 -->
    <GroupBox Header="特殊样式" Padding="15">
        <StackPanel Spacing="10">
            <!-- 透明样式 -->
            <StackPanel>
                <TextBlock Text="透明样式 (TransparentPasswordBoxStyle)" FontWeight="Bold" Margin="0,0,0,4"/>
                <PasswordBox Style="{StaticResource TransparentPasswordBoxStyle}"
                             ui:TextBoxHelper.PlaceholderText="透明背景，底部边框"
                             Width="250"
                             HorizontalAlignment="Left"/>
            </StackPanel>

            <!-- 圆角样式 -->
            <StackPanel>
                <TextBlock Text="圆角样式 (RoundedPasswordBoxStyle)" FontWeight="Bold" Margin="0,0,0,4"/>
                <PasswordBox Style="{StaticResource RoundedPasswordBoxStyle}"
                             ui:TextBoxHelper.PlaceholderText="圆角边框，增大内边距"
                             Width="250"
                             HorizontalAlignment="Left"/>
            </StackPanel>

            <!-- 安全增强样式 -->
            <StackPanel>
                <TextBlock Text="安全增强样式 (SecurePasswordBoxStyle)" FontWeight="Bold" Margin="0,0,0,4"/>
                <PasswordBox Style="{StaticResource SecurePasswordBoxStyle}"
                             ui:TextBoxHelper.PlaceholderText="安全增强，禁用复制粘贴"
                             Width="250"
                             HorizontalAlignment="Left"/>
            </StackPanel>
        </StackPanel>
    </GroupBox>

    <!-- 密码强度指示器 -->
    <GroupBox Header="密码强度指示器" Padding="15">
        <StackPanel>
            <TextBlock Text="输入密码查看强度指示：" FontWeight="Bold" Margin="0,0,0,8"/>
            <Grid Width="300" HorizontalAlignment="Left">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <PasswordBox Grid.Row="0"
                             Style="{StaticResource PasswordBoxStyle}"
                             ui:TextBoxHelper.PlaceholderText="输入密码查看强度"
                             x:Name="StrengthPasswordBox"/>
                
                <ProgressBar Grid.Row="1"
                             Height="4"
                             Margin="0,4,0,2"
                             Value="0"
                             Maximum="100"/>
                
                <TextBlock Grid.Row="2"
                           Text="请输入密码"
                           FontSize="10"
                           Foreground="Gray"/>
            </Grid>
        </StackPanel>
    </GroupBox>

    <!-- 显示/隐藏切换 -->
    <GroupBox Header="显示/隐藏密码切换" Padding="15">
        <StackPanel>
            <TextBlock Text="完整的显示/隐藏切换实现：" FontWeight="Bold" Margin="0,0,0,8"/>
            <Grid Width="300" HorizontalAlignment="Left">
                <!-- 密码框（隐藏状态） -->
                <PasswordBox Style="{StaticResource PasswordBoxStyle}"
                             PlaceholderText="可切换显示的密码"
                             attachedProps:PasswordBoxProperties.EnablePasswordBinding="True"
                             attachedProps:PasswordBoxProperties.BoundPassword="{Binding TogglePassword, UpdateSourceTrigger=PropertyChanged}"
                             Visibility="{Binding IsPasswordVisible, Converter={StaticResource InverseBooleanToVisibilityConverter}}"/>

                <!-- 文本框（显示状态） -->
                <ui:TextBox Style="{StaticResource PasswordDisplayTextBoxStyle}"
                            Text="{Binding TogglePassword, UpdateSourceTrigger=PropertyChanged}"
                            PlaceholderText="可显示密码"
                            Visibility="{Binding IsPasswordVisible, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                <!-- 切换按钮 -->
                <ui:Button Content="{Binding IsPasswordVisible, Converter={StaticResource BooleanToStringConverter}, ConverterParameter='🙈|👁️'}"
                           Command="{Binding TogglePasswordVisibilityCommand}"
                           HorizontalAlignment="Right"
                           VerticalAlignment="Center"
                           Background="Transparent"
                           BorderThickness="0"
                           Padding="4"
                           Margin="0,0,8,0"
                           ToolTip="{Binding IsPasswordVisible, Converter={StaticResource BooleanToStringConverter}, ConverterParameter='隐藏密码|显示密码'}"/>
            </Grid>
        </StackPanel>
    </GroupBox>

</StackPanel>
