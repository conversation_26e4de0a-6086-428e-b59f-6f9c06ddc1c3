# WPF-UI Flyout 控件使用指南

## 概述

Flyout 是 WPF-UI 提供的一个独立浮出面板控件，用于显示临时内容、菜单、通知等。与传统的 ToolTip 或 ContextMenu 不同，Flyout 是一个完全独立的控件，不作为附加属性使用。

## 核心特性

### ✨ 主要特点
- **独立控件**：不依附于特定控件，可独立存在
- **灵活定位**：支持多种显示位置（Top、Bottom、Left、Right）
- **任意内容**：可包含任何 WPF 内容（文本、图片、表单等）
- **事件支持**：提供 Opened 和 Closed 事件
- **主题适配**：自动适配浅色/深色主题

### 🎯 适用场景
- 上下文菜单和操作面板
- 通知和消息提示
- 快速设置和配置面板
- 用户信息和状态显示
- 临时表单和输入界面

## 基础用法

### 1. 简单的 Flyout

```xaml
<!-- 触发按钮 -->
<ui:Button Content="显示 Flyout" Command="{Binding ShowFlyoutCommand}"/>

<!-- Flyout 控件 -->
<ui:Flyout IsOpen="{Binding IsFlyoutOpen}" Placement="Bottom">
    <StackPanel Margin="16">
        <TextBlock Text="这是一个简单的 Flyout" FontWeight="Medium"/>
        <TextBlock Text="可以包含任何内容" TextWrapping="Wrap"/>
        <Button Content="关闭" Command="{Binding CloseFlyoutCommand}"/>
    </StackPanel>
</ui:Flyout>
```

### 2. ViewModel 控制

```csharp
public class MyViewModel : ObservableObject
{
    [ObservableProperty]
    private bool isFlyoutOpen = false;

    [RelayCommand]
    private void ShowFlyout()
    {
        IsFlyoutOpen = true;
    }

    [RelayCommand]
    private void CloseFlyout()
    {
        IsFlyoutOpen = false;
    }
}
```

## 高级用法

### 1. 菜单类型的 Flyout

```xaml
<ui:Flyout IsOpen="{Binding IsMenuOpen}">
    <StackPanel Margin="8" MinWidth="150">
        <ui:Button Content="新建" 
                   Command="{Binding NewCommand}"
                   HorizontalAlignment="Stretch"
                   HorizontalContentAlignment="Left"
                   Appearance="Transparent"/>
        <ui:Button Content="编辑" 
                   Command="{Binding EditCommand}"
                   HorizontalAlignment="Stretch"
                   HorizontalContentAlignment="Left"
                   Appearance="Transparent"/>
        <Separator Margin="0,4"/>
        <ui:Button Content="删除" 
                   Command="{Binding DeleteCommand}"
                   HorizontalAlignment="Stretch"
                   HorizontalContentAlignment="Left"
                   Appearance="Transparent"/>
    </StackPanel>
</ui:Flyout>
```

### 2. 通知类型的 Flyout

```xaml
<ui:Flyout IsOpen="{Binding ShowNotification}" Placement="Top">
    <Grid Margin="16">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="Auto"/>
        </Grid.ColumnDefinitions>
        
        <ui:SymbolIcon Grid.Column="0"
                       Symbol="CheckmarkCircle24" 
                       FontSize="24" 
                       Foreground="Green"
                       Margin="0,0,12,0"/>
        
        <StackPanel Grid.Column="1">
            <TextBlock Text="操作成功" FontWeight="Bold"/>
            <TextBlock Text="您的操作已成功完成。"/>
        </StackPanel>
        
        <ui:Button Grid.Column="2"
                   Content="✕"
                   Command="{Binding CloseNotificationCommand}"
                   Appearance="Transparent"/>
    </Grid>
</ui:Flyout>
```

### 3. 表单类型的 Flyout

```xaml
<ui:Flyout IsOpen="{Binding ShowSettings}">
    <StackPanel Margin="16" MinWidth="250">
        <TextBlock Text="快速设置" FontWeight="Bold" FontSize="16" Margin="0,0,0,12"/>
        
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <TextBlock Grid.Row="0" Grid.Column="0" Text="主题:" VerticalAlignment="Center" Margin="0,0,8,8"/>
            <ComboBox Grid.Row="0" Grid.Column="1" 
                      SelectedItem="{Binding SelectedTheme}"
                      ItemsSource="{Binding Themes}"
                      Margin="0,0,0,8"/>
            
            <CheckBox Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2"
                      Content="启用通知" 
                      IsChecked="{Binding EnableNotifications}"
                      Margin="0,0,0,8"/>
            
            <CheckBox Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2"
                      Content="自动保存" 
                      IsChecked="{Binding AutoSave}"
                      Margin="0,0,0,12"/>
        </Grid>
        
        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
            <Button Content="取消" Command="{Binding CancelCommand}" Margin="0,0,8,0"/>
            <Button Content="保存" Command="{Binding SaveCommand}"/>
        </StackPanel>
    </StackPanel>
</ui:Flyout>
```

## 属性说明

### 核心属性

| 属性 | 类型 | 说明 |
|------|------|------|
| `IsOpen` | bool | 控制 Flyout 的显示/隐藏状态 |
| `Placement` | PlacementMode | 设置显示位置（Top、Bottom、Left、Right） |
| `Content` | object | Flyout 的内容（继承自 ContentControl） |

### 事件

| 事件 | 说明 |
|------|------|
| `Opened` | Flyout 打开时触发 |
| `Closed` | Flyout 关闭时触发 |

## 最佳实践

### 1. 控制显示时机
```csharp
// 推荐：通过 ViewModel 属性控制
[ObservableProperty]
private bool isFlyoutOpen = false;

// 避免：直接在代码后台操作
// flyout.IsOpen = true; // 不推荐
```

### 2. 合理设置内容大小
```xaml
<!-- 推荐：设置合理的最小宽度 -->
<StackPanel Margin="16" MinWidth="200">
    <!-- 内容 -->
</StackPanel>

<!-- 避免：内容过小或过大 -->
```

### 3. 提供关闭方式
```xaml
<!-- 推荐：提供明确的关闭按钮 -->
<Button Content="关闭" Command="{Binding CloseFlyoutCommand}"/>

<!-- 或者：点击外部区域关闭（通过事件处理） -->
```

### 4. 适配不同主题
```xaml
<!-- 推荐：使用动态资源适配主题 -->
<Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
        BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}">
    <!-- 内容 -->
</Border>
```

## 与其他控件的区别

### vs ToolTip
- **ToolTip**：简单的文本提示，自动显示/隐藏
- **Flyout**：复杂内容面板，手动控制显示/隐藏

### vs ContextMenu
- **ContextMenu**：右键菜单，系统级控件
- **Flyout**：自定义面板，更灵活的样式和内容

### vs Popup
- **Popup**：WPF 原生控件，样式简单
- **Flyout**：WPF-UI 控件，现代化样式，主题适配

## 常见问题

### Q: Flyout 不显示怎么办？
A: 检查以下几点：
1. `IsOpen` 属性是否正确绑定
2. Flyout 是否在可视化树中
3. 父容器是否有足够空间

### Q: 如何控制 Flyout 的位置？
A: 使用 `Placement` 属性设置相对位置，Flyout 会自动选择最佳显示位置。

### Q: 可以同时显示多个 Flyout 吗？
A: 可以，但建议同时只显示一个，避免界面混乱。

## 示例项目

完整的示例代码请参考：
- `Views/LayoutControls/FlyoutView.xaml`
- `ViewModels/LayoutControls/FlyoutViewModel.cs`
- `CodeExamples/LayoutControls/Flyout/`

这些示例展示了 Flyout 控件的各种用法和最佳实践。
