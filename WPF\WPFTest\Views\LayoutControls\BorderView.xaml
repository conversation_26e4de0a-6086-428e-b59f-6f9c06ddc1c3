<UserControl x:Class="WPFTest.Views.LayoutControls.BorderView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:codeExample="clr-namespace:Zylo.WPF.Controls.CodeExample;assembly=Zylo.WPF"
             xmlns:mvvm="http://prismlibrary.com/"
             xmlns:layoutControls="clr-namespace:WPFTest.ViewModels.LayoutControls"
             mc:Ignorable="d"
             d:DesignHeight="800" d:DesignWidth="1200"
             mvvm:ViewModelLocator.AutoWireViewModel="True"
             d:DataContext="{d:DesignInstance layoutControls:BorderViewModel}">

    <UserControl.Resources>
        <!-- 🎨 所有样式来自 Zylo.WPF 项目 -->
        <!-- Border 样式已在 Zylo.WPF/Resources/Border/ 目录下定义 -->
        <!-- 可用样式：BorderBaseStyle, ModernBorderStyle, RoundedBorderStyle -->
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" 
                  HorizontalScrollBarVisibility="Disabled"
                  Padding="24">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>    <!-- 标题区域 -->
                <RowDefinition Height="*"/>       <!-- 主要内容 -->
                <RowDefinition Height="Auto"/>    <!-- 底部操作栏 -->
            </Grid.RowDefinitions>

            <!-- 标题区域 -->
            <StackPanel Grid.Row="0" Margin="0,0,0,20">
                <TextBlock Text="🎨 Border 控件展示"
                           FontSize="32"
                           FontWeight="Bold"
                           Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                           Margin="0,0,0,8"/>
                <TextBlock Text="展示 WPF-UI 增强的 Border 控件的各种样式和交互效果" 
                           FontSize="16" 
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                           TextWrapping="Wrap"/>
                
                <!-- 状态显示 -->
                <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                        BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Padding="16,12"
                        Margin="0,16,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <ui:SymbolIcon Symbol="Info24" 
                                       FontSize="20" 
                                       Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                       VerticalAlignment="Center"
                                       Margin="0,0,12,0"/>
                        <TextBlock Grid.Column="1" 
                                   Text="{Binding StatusMessage}" 
                                   FontSize="14"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                   VerticalAlignment="Center"
                                   TextWrapping="Wrap"/>
                    </Grid>
                </Border>
            </StackPanel>

            <!-- 主要内容区域 -->
            <Border Grid.Row="1" Margin="0,0,0,20">
                <StackPanel>

                    <!-- 基础功能展示 -->
                    <ui:CardExpander Header="🎯 基础功能" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 Border 的基础功能和用法"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>
                            
                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="基础 Border 示例：" FontWeight="Medium" Margin="0,0,0,16"/>
                                    <WrapPanel>
                                        <!-- 标准 Border -->
                                        <Border Background="{DynamicResource ControlFillColorDefaultBrush}"
                                                BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                                BorderThickness="1"
                                                CornerRadius="4"
                                                Padding="16"
                                                Margin="8"
                                                Cursor="Hand">
                                            <TextBlock Text="标准 Border" 
                                                       HorizontalAlignment="Center"
                                                       VerticalAlignment="Center"/>
                                        </Border>

                                        <!-- 圆角 Border -->
                                        <Border Background="{DynamicResource AccentFillColorDefaultBrush}"
                                                BorderBrush="{DynamicResource AccentStrokeColorDefaultBrush}"
                                                BorderThickness="2"
                                                CornerRadius="12"
                                                Padding="16"
                                                Margin="8"
                                                Cursor="Hand">
                                            <TextBlock Text="圆角 Border" 
                                                       Foreground="White"
                                                       HorizontalAlignment="Center"
                                                       VerticalAlignment="Center"/>
                                        </Border>

                                        <!-- 阴影 Border -->
                                        <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                                                BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                                BorderThickness="1"
                                                CornerRadius="8"
                                                Padding="16"
                                                Margin="8"
                                                Cursor="Hand">
                                            <Border.Effect>
                                                <DropShadowEffect Color="Black" 
                                                                  Opacity="0.1" 
                                                                  ShadowDepth="4" 
                                                                  BlurRadius="8"/>
                                            </Border.Effect>
                                            <TextBlock Text="阴影 Border" 
                                                       HorizontalAlignment="Center"
                                                       VerticalAlignment="Center"/>
                                        </Border>
                                    </WrapPanel>
                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="基础功能代码示例"
                                Language="XAML"
                                Description="展示 Border 的基础用法"
                                ShowTabs="True"
                                XamlCode="{Binding BasicXamlExample}"
                                CSharpCode="{Binding BasicCSharpExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 高级功能展示 -->
                    <ui:CardExpander Header="⚡ 高级功能" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 Border 的高级功能和自定义样式"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>
                            
                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="高级 Border 示例：" FontWeight="Medium" Margin="0,0,0,16"/>
                                    <WrapPanel>
                                        <!-- 渐变背景 Border -->
                                        <Border BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                                BorderThickness="1"
                                                CornerRadius="8"
                                                Padding="16"
                                                Margin="8"
                                                Cursor="Hand">
                                            <Border.Background>
                                                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                                    <GradientStop Color="#FF6B73FF" Offset="0"/>
                                                    <GradientStop Color="#FF9DFFAD" Offset="1"/>
                                                </LinearGradientBrush>
                                            </Border.Background>
                                            <TextBlock Text="渐变背景" 
                                                       Foreground="White"
                                                       HorizontalAlignment="Center"
                                                       VerticalAlignment="Center"/>
                                        </Border>

                                        <!-- 粗边框 Border -->
                                        <Border Background="{DynamicResource ControlFillColorDefaultBrush}"
                                                BorderBrush="{DynamicResource AccentFillColorDefaultBrush}"
                                                BorderThickness="4"
                                                CornerRadius="4"
                                                Padding="16"
                                                Margin="8"
                                                Cursor="Hand">
                                            <TextBlock Text="粗边框"
                                                       HorizontalAlignment="Center"
                                                       VerticalAlignment="Center"/>
                                        </Border>

                                        <!-- 动画 Border -->
                                        <Border Background="{DynamicResource ControlFillColorDefaultBrush}"
                                                BorderBrush="{DynamicResource AccentFillColorDefaultBrush}"
                                                BorderThickness="2"
                                                CornerRadius="8"
                                                Padding="16"
                                                Margin="8"
                                                Cursor="Hand"
                                                x:Name="AnimatedBorder">
                                            <Border.Triggers>
                                                <EventTrigger RoutedEvent="MouseEnter">
                                                    <BeginStoryboard>
                                                        <Storyboard>
                                                            <DoubleAnimation Storyboard.TargetProperty="(Border.RenderTransform).(ScaleTransform.ScaleX)"
                                                                             To="1.1" Duration="0:0:0.2"/>
                                                            <DoubleAnimation Storyboard.TargetProperty="(Border.RenderTransform).(ScaleTransform.ScaleY)"
                                                                             To="1.1" Duration="0:0:0.2"/>
                                                        </Storyboard>
                                                    </BeginStoryboard>
                                                </EventTrigger>
                                                <EventTrigger RoutedEvent="MouseLeave">
                                                    <BeginStoryboard>
                                                        <Storyboard>
                                                            <DoubleAnimation Storyboard.TargetProperty="(Border.RenderTransform).(ScaleTransform.ScaleX)"
                                                                             To="1.0" Duration="0:0:0.2"/>
                                                            <DoubleAnimation Storyboard.TargetProperty="(Border.RenderTransform).(ScaleTransform.ScaleY)"
                                                                             To="1.0" Duration="0:0:0.2"/>
                                                        </Storyboard>
                                                    </BeginStoryboard>
                                                </EventTrigger>
                                            </Border.Triggers>
                                            <Border.RenderTransform>
                                                <ScaleTransform/>
                                            </Border.RenderTransform>
                                            <Border.RenderTransformOrigin>
                                                <Point X="0.5" Y="0.5"/>
                                            </Border.RenderTransformOrigin>
                                            <TextBlock Text="动画 Border" 
                                                       HorizontalAlignment="Center"
                                                       VerticalAlignment="Center"/>
                                        </Border>
                                    </WrapPanel>
                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="高级功能代码示例"
                                Language="XAML"
                                Description="展示 Border 的高级用法和自定义样式"
                                ShowTabs="True"
                                XamlCode="{Binding AdvancedXamlExample}"
                                CSharpCode="{Binding AdvancedCSharpExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 样式展示 -->
                    <ui:CardExpander Header="🎨 样式展示" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示来自 Zylo.WPF 的各种 Border 样式"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>
                            
                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="样式展示示例：" FontWeight="Medium" Margin="0,0,0,16"/>
                                    <WrapPanel>
                                        <!-- 这里将添加不同样式的 Border 示例 -->
                                        <Border Background="{DynamicResource ControlFillColorDefaultBrush}"
                                                BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                                BorderThickness="1"
                                                CornerRadius="4"
                                                Padding="16"
                                                Margin="8">
                                            <TextBlock Text="默认样式" 
                                                       HorizontalAlignment="Center"
                                                       VerticalAlignment="Center"/>
                                        </Border>
                                    </WrapPanel>
                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="样式代码示例"
                                Language="XAML"
                                Description="展示如何使用 Zylo.WPF 中的 Border 样式"
                                ShowTabs="True"
                                XamlCode="{Binding StylesXamlExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                </StackPanel>
            </Border>

            <!-- 底部操作栏 -->
            <Border Grid.Row="2" 
                    Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Padding="16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 统计信息 -->
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock Text="{Binding InteractionCount, StringFormat='交互次数: {0}'}" 
                                   FontSize="14" 
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   Margin="0,0,20,0"/>
                        <TextBlock Text="{Binding LastAction, StringFormat='最后操作: {0}'}" 
                                   FontSize="14" 
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                    </StackPanel>

                    <!-- 操作按钮 -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <ui:Button Content="重置计数" 
                                   Command="{Binding ResetCountCommand}"
                                   Appearance="Secondary"
                                   Margin="0,0,8,0"/>
                        <ui:Button Content="清除状态" 
                                   Command="{Binding ClearStatusCommand}"
                                   Appearance="Secondary"/>
                    </StackPanel>
                </Grid>
            </Border>

        </Grid>
    </ScrollViewer>
</UserControl>
