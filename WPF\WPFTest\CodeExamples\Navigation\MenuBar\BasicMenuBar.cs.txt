// 基础 MenuBar ViewModel 实现
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Zylo.WPF.Models.Navigation;

namespace WPFTest.ViewModels.Navigation
{
    /// <summary>
    /// 基础 MenuBar 示例 ViewModel
    /// </summary>
    public partial class BasicMenuBarViewModel : ObservableObject
    {
        #region 属性

        /// <summary>
        /// 基础菜单项集合
        /// </summary>
        [ObservableProperty]
        public partial ObservableCollection<MenuItemModel> BasicMenuItems { get; set; } = new();

        /// <summary>
        /// 简化菜单项集合
        /// </summary>
        [ObservableProperty]
        public partial ObservableCollection<MenuItemModel> SimpleMenuItems { get; set; } = new();

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        public partial string StatusMessage { get; set; } = "准备就绪";

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化 BasicMenuBarViewModel
        /// </summary>
        public BasicMenuBarViewModel()
        {
            InitializeMenuItems();
        }

        #endregion

        #region 命令

        /// <summary>
        /// 菜单项点击命令
        /// </summary>
        /// <param name="parameter">菜单项参数</param>
        [RelayCommand]
        private void MenuItemClick(string? parameter)
        {
            StatusMessage = $"点击了菜单项: {parameter}";
            Debug.WriteLine($"MenuBar 菜单项点击: {parameter}");
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化菜单项
        /// </summary>
        private void InitializeMenuItems()
        {
            InitializeBasicMenuItems();
            InitializeSimpleMenuItems();
        }

        /// <summary>
        /// 初始化基础菜单项
        /// </summary>
        private void InitializeBasicMenuItems()
        {
            // 文件菜单
            var fileMenu = new MenuItemModel("文件(_F)");
            fileMenu.AddChild(new MenuItemModel("新建(_N)", MenuItemClickCommand, "新建") 
            { 
                InputGestureText = "Ctrl+N" 
            });
            fileMenu.AddChild(new MenuItemModel("打开(_O)", MenuItemClickCommand, "打开") 
            { 
                InputGestureText = "Ctrl+O" 
            });
            fileMenu.AddChild(new MenuItemModel("保存(_S)", MenuItemClickCommand, "保存") 
            { 
                InputGestureText = "Ctrl+S" 
            });
            fileMenu.AddChild(new MenuItemModel("另存为(_A)", MenuItemClickCommand, "另存为") 
            { 
                InputGestureText = "Ctrl+Shift+S" 
            });
            fileMenu.AddChild(new MenuItemModel { IsSeparator = true });
            fileMenu.AddChild(new MenuItemModel("退出(_X)", MenuItemClickCommand, "退出") 
            { 
                InputGestureText = "Alt+F4" 
            });

            // 编辑菜单
            var editMenu = new MenuItemModel("编辑(_E)");
            editMenu.AddChild(new MenuItemModel("撤销(_U)", MenuItemClickCommand, "撤销") 
            { 
                InputGestureText = "Ctrl+Z" 
            });
            editMenu.AddChild(new MenuItemModel("重做(_R)", MenuItemClickCommand, "重做") 
            { 
                InputGestureText = "Ctrl+Y" 
            });
            editMenu.AddChild(new MenuItemModel { IsSeparator = true });
            editMenu.AddChild(new MenuItemModel("剪切(_T)", MenuItemClickCommand, "剪切") 
            { 
                InputGestureText = "Ctrl+X" 
            });
            editMenu.AddChild(new MenuItemModel("复制(_C)", MenuItemClickCommand, "复制") 
            { 
                InputGestureText = "Ctrl+C" 
            });
            editMenu.AddChild(new MenuItemModel("粘贴(_P)", MenuItemClickCommand, "粘贴") 
            { 
                InputGestureText = "Ctrl+V" 
            });

            // 帮助菜单
            var helpMenu = new MenuItemModel("帮助(_H)");
            helpMenu.AddChild(new MenuItemModel("帮助主题(_H)", MenuItemClickCommand, "帮助主题") 
            { 
                InputGestureText = "F1" 
            });
            helpMenu.AddChild(new MenuItemModel { IsSeparator = true });
            helpMenu.AddChild(new MenuItemModel("关于(_A)", MenuItemClickCommand, "关于"));

            BasicMenuItems.Add(fileMenu);
            BasicMenuItems.Add(editMenu);
            BasicMenuItems.Add(helpMenu);
        }

        /// <summary>
        /// 初始化简化菜单项
        /// </summary>
        private void InitializeSimpleMenuItems()
        {
            // 文件菜单
            var fileMenu = new MenuItemModel("文件(_F)");
            fileMenu.AddChild(new MenuItemModel("新建(_N)", MenuItemClickCommand, "新建"));
            fileMenu.AddChild(new MenuItemModel("打开(_O)", MenuItemClickCommand, "打开"));
            fileMenu.AddChild(new MenuItemModel("保存(_S)", MenuItemClickCommand, "保存"));

            // 帮助菜单
            var helpMenu = new MenuItemModel("帮助(_H)");
            helpMenu.AddChild(new MenuItemModel("关于(_A)", MenuItemClickCommand, "关于"));

            SimpleMenuItems.Add(fileMenu);
            SimpleMenuItems.Add(helpMenu);
        }

        #endregion
    }
}
