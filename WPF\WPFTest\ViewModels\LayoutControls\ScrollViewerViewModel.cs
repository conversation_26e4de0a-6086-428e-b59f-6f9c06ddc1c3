using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Windows;
using System.IO;
using System.Reflection;
using System.Collections.ObjectModel;
using System.ComponentModel;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels.LayoutControls
{
    /// <summary>
    /// ScrollViewer 页面的 ViewModel，演示 ScrollViewer 控件的各种功能
    /// </summary>
    public partial class ScrollViewerViewModel : ObservableObject
    {
        private readonly YLoggerInstance _logger = YLogger.For<ScrollViewerViewModel>();

        #region 属性

        /// <summary>
        /// 交互次数
        /// </summary>
        [ObservableProperty]
        private int interactionCount = 0;

        /// <summary>
        /// 最后执行的操作
        /// </summary>
        [ObservableProperty]
        private string lastAction = "无操作";

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        private string statusMessage = "欢迎使用 ScrollViewer 示例库！";

        /// <summary>
        /// 显示垂直滚动条
        /// </summary>
        [ObservableProperty]
        private bool showVerticalScrollBar = true;

        /// <summary>
        /// 显示水平滚动条
        /// </summary>
        [ObservableProperty]
        private bool showHorizontalScrollBar = true;

        /// <summary>
        /// 启用滚动条预览
        /// </summary>
        [ObservableProperty]
        private bool enableScrollBarPreview = false;

        /// <summary>
        /// 垂直偏移量
        /// </summary>
        [ObservableProperty]
        private double verticalOffset = 0.0;

        /// <summary>
        /// 水平偏移量
        /// </summary>
        [ObservableProperty]
        private double horizontalOffset = 0.0;

        /// <summary>
        /// 可视区域高度
        /// </summary>
        [ObservableProperty]
        private double viewportHeight = 0.0;

        /// <summary>
        /// 内容总高度
        /// </summary>
        [ObservableProperty]
        private double extentHeight = 0.0;

        #region 代码示例属性

        /// <summary>
        /// 基础功能 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 基础功能 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级功能 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级功能 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 样式 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string StylesXamlExample { get; set; } = string.Empty;

        #endregion

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化 ScrollViewerViewModel
        /// </summary>
        public ScrollViewerViewModel()
        {
            try
            {
                _logger.Info("🚀 ScrollViewer 页面 ViewModel 开始初始化");

                StatusMessage = "ScrollViewer 示例库已加载，开始体验各种滚动功能！";
                InitializeCodeExamples();
                InitializeScrollData();

                _logger.Info("✅ ScrollViewer 页面 ViewModel 初始化完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ ScrollViewer 页面 ViewModel 初始化失败: {ex.Message}");
                StatusMessage = "初始化失败，请检查日志";
            }
        }

        #endregion

        #region 命令

        /// <summary>
        /// 通用交互命令
        /// </summary>
        /// <param name="parameter">操作参数</param>
        [RelayCommand]
        private void HandleInteraction(string? parameter)
        {
            try
            {
                InteractionCount++;
                LastAction = parameter ?? "未知操作";
                
                _logger.Info($"🎯 用户交互操作: {parameter}");
                _logger.Info($"🔢 当前交互次数: {InteractionCount}");

                // 根据不同的操作显示不同的状态消息
                StatusMessage = parameter switch
                {
                    "垂直滚动测试" => "🎯 测试了垂直滚动功能",
                    "水平滚动测试" => "🎯 测试了水平滚动功能",
                    "双向滚动测试" => "🎯 测试了双向滚动功能",
                    _ => $"🎯 执行了操作: {parameter}"
                };
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 处理用户交互失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用滚动设置命令
        /// </summary>
        [RelayCommand]
        private void ApplyScrollSettings()
        {
            try
            {
                InteractionCount++;
                LastAction = "应用滚动设置";
                
                _logger.Info($"应用滚动设置 - 垂直: {ShowVerticalScrollBar}, 水平: {ShowHorizontalScrollBar}, 预览: {EnableScrollBarPreview}");
                StatusMessage = $"🎯 已应用滚动设置 - 垂直滚动条: {(ShowVerticalScrollBar ? "显示" : "隐藏")}, 水平滚动条: {(ShowHorizontalScrollBar ? "显示" : "隐藏")}";
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 应用滚动设置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 滚动到顶部命令
        /// </summary>
        [RelayCommand]
        private void ScrollToTop()
        {
            try
            {
                InteractionCount++;
                LastAction = "滚动到顶部";
                VerticalOffset = 0.0;
                
                _logger.Info("滚动到顶部");
                StatusMessage = "🎯 已滚动到顶部";
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 滚动到顶部失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 滚动到底部命令
        /// </summary>
        [RelayCommand]
        private void ScrollToBottom()
        {
            try
            {
                InteractionCount++;
                LastAction = "滚动到底部";
                VerticalOffset = ExtentHeight - ViewportHeight;
                
                _logger.Info("滚动到底部");
                StatusMessage = "🎯 已滚动到底部";
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 滚动到底部失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 滚动到左侧命令
        /// </summary>
        [RelayCommand]
        private void ScrollToLeft()
        {
            try
            {
                InteractionCount++;
                LastAction = "滚动到左侧";
                HorizontalOffset = 0.0;
                
                _logger.Info("滚动到左侧");
                StatusMessage = "🎯 已滚动到左侧";
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 滚动到左侧失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 滚动到右侧命令
        /// </summary>
        [RelayCommand]
        private void ScrollToRight()
        {
            try
            {
                InteractionCount++;
                LastAction = "滚动到右侧";
                HorizontalOffset = 800.0; // 假设内容宽度
                
                _logger.Info("滚动到右侧");
                StatusMessage = "🎯 已滚动到右侧";
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 滚动到右侧失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 重置计数命令
        /// </summary>
        [RelayCommand]
        private void ResetCount()
        {
            InteractionCount = 0;
            StatusMessage = "计数已重置";
            _logger.Info("重置了交互计数");
        }

        /// <summary>
        /// 清除状态命令
        /// </summary>
        [RelayCommand]
        private void ClearStatus()
        {
            LastAction = "无操作";
            StatusMessage = "状态已清除";
            _logger.Info("清除了状态信息");
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化滚动数据
        /// </summary>
        private void InitializeScrollData()
        {
            // 模拟滚动数据
            VerticalOffset = 0.0;
            HorizontalOffset = 0.0;
            ViewportHeight = 200.0;
            ExtentHeight = 400.0;
        }

        /// <summary>
        /// 初始化代码示例
        /// </summary>
        private void InitializeCodeExamples()
        {
            try
            {
                // 获取代码示例文件的基础路径
                var baseDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
                var codeExamplesPath = Path.Combine(baseDirectory, "CodeExamples", "LayoutControls", "ScrollViewer");

                _logger.Info($"基础目录: {baseDirectory}");
                _logger.Info($"代码示例路径: {codeExamplesPath}");
                _logger.Info($"目录是否存在: {Directory.Exists(codeExamplesPath)}");

                // 读取各种示例文件
                BasicXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.xaml.txt"));
                BasicCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.cs.txt"));
                AdvancedXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Advanced.xaml.txt"));
                AdvancedCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Advanced.cs.txt"));
                StylesXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Styles.xaml.txt"));

                _logger.Info("ScrollViewer 代码示例加载完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"加载 ScrollViewer 代码示例失败: {ex.Message}");
                SetDefaultCodeExamples();
            }
        }

        /// <summary>
        /// 读取代码示例文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件内容</returns>
        private string ReadCodeExampleFile(string filePath)
        {
            _logger.Info($"尝试读取文件: {filePath}");
            
            if (File.Exists(filePath))
            {
                var content = File.ReadAllText(filePath);
                _logger.Info($"成功读取文件，内容长度: {content.Length}");
                return content;
            }
            else
            {
                _logger.Warning($"代码示例文件不存在: {filePath}");
                return $"// 代码示例文件不存在: {Path.GetFileName(filePath)}\n// 完整路径: {filePath}";
            }
        }

        /// <summary>
        /// 设置默认代码示例（当文件读取失败时使用）
        /// </summary>
        private void SetDefaultCodeExamples()
        {
            BasicXamlExample = @"<!-- ScrollViewer 基础示例 -->
<ScrollViewer Height=""200""
              VerticalScrollBarVisibility=""Auto""
              HorizontalScrollBarVisibility=""Disabled"">
    <StackPanel Margin=""16"">
        <TextBlock Text=""这是一个垂直滚动的示例。"" FontWeight=""Bold""/>
        <TextBlock Text=""当内容超过容器高度时，会自动显示垂直滚动条。""/>
        <!-- 更多内容... -->
    </StackPanel>
</ScrollViewer>";

            BasicCSharpExample = @"// ScrollViewer C# 基础示例
using System.Windows.Controls;

var scrollViewer = new ScrollViewer
{
    Height = 200,
    VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
    HorizontalScrollBarVisibility = ScrollBarVisibility.Disabled
};

var content = new StackPanel();
content.Children.Add(new TextBlock { Text = ""滚动内容"" });
scrollViewer.Content = content;";

            AdvancedXamlExample = @"<!-- ScrollViewer 高级示例 -->
<ScrollViewer Height=""200"" Width=""400""
              VerticalScrollBarVisibility=""Auto""
              HorizontalScrollBarVisibility=""Auto"">
    <Grid>
        <!-- 大型内容网格 -->
    </Grid>
</ScrollViewer>";

            AdvancedCSharpExample = @"// ScrollViewer 高级 C# 示例
// 滚动控制方法
scrollViewer.ScrollToTop();
scrollViewer.ScrollToBottom();
scrollViewer.ScrollToLeftEnd();
scrollViewer.ScrollToRightEnd();";

            StylesXamlExample = @"<!-- ScrollViewer 样式示例 -->
<ScrollViewer Style=""{StaticResource ModernScrollViewerStyle}"">
    <!-- 内容 -->
</ScrollViewer>";
        }

        #endregion
    }
}
