using System.Windows.Media;

namespace Zylo.WPF.Comman.FontKeys
{
    /// <summary>
    /// 字体资源管理器
    /// </summary>
    public static class FontManager
    {
        /// <summary>
        /// 图标字体 - 默认路径
        /// </summary>
        public static readonly FontFamily IconFont = new FontFamily("/Zylo.WPF;component/Assets/Font/#iconfont");

        /// <summary>
        /// 获取字体资源路径 - 默认路径
        /// </summary>
        public const string IconFontPath = "/Zylo.WPF;component/Assets/Font/#iconfont";

        /// <summary>
        /// 测试用的字体路径列表
        /// 用于测试不同的字体名称和路径组合
        /// </summary>
        public static readonly string[] TestFontPaths = new[]
        {
            "/Zylo.WPF;component/Assets/Font/#iconfont",
            "/Zylo.WPF;component/Assets/Font/iconfont.ttf#iconfont",
            "/Zylo.WPF;component/Assets/Font/iconfont.ttf#IconFont",
            "/Zylo.WPF;component/Assets/Font/iconfont.ttf#icon-font",
            "/Zylo.WPF;component/Assets/Font/iconfont.ttf#Iconfont",
            "pack://application:,,,/Zylo.WPF;component/Assets/Font/#iconfont",
            "pack://application:,,,/Zylo.WPF;component/Assets/Font/iconfont.ttf#iconfont",
            "pack://application:,,,/Zylo.WPF;component/Assets/Font/iconfont.ttf#IconFont"
        };

        /// <summary>
        /// 获取系统中所有可用的字体族
        /// 用于调试字体加载问题
        /// </summary>
        public static IEnumerable<FontFamily> GetSystemFonts()
        {
            return Fonts.SystemFontFamilies;
        }

        /// <summary>
        /// 检查字体是否可用
        /// </summary>
        /// <param name="fontPath">字体路径</param>
        /// <returns>字体是否可用</returns>
        public static bool IsFontAvailable(string fontPath)
        {
            try
            {
                var fontFamily = new FontFamily(fontPath);
                // 尝试获取字体的字形信息来验证字体是否有效
                var typefaces = fontFamily.GetTypefaces();
                return typefaces.Any();
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取字体的详细信息
        /// </summary>
        /// <param name="fontPath">字体路径</param>
        /// <returns>字体信息</returns>
        public static string GetFontInfo(string fontPath)
        {
            try
            {
                var fontFamily = new FontFamily(fontPath);
                var typefaces = fontFamily.GetTypefaces();

                var info = $"字体路径: {fontPath}\n";
                info += $"字体族名称: {fontFamily.Source}\n";
                info += $"可用字形数量: {typefaces.Count()}\n";

                foreach (var typeface in typefaces.Take(3)) // 只显示前3个字形
                {
                    typeface.TryGetGlyphTypeface(out var glyphTypeface);
                    if (glyphTypeface != null)
                    {
                        info += $"字形名称: {glyphTypeface.FamilyNames.Values.FirstOrDefault()}\n";
                    }
                }

                return info;
            }
            catch (Exception ex)
            {
                return $"字体路径: {fontPath}\n错误: {ex.Message}";
            }
        }
    }
}