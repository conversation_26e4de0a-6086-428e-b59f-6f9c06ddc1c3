<UserControl x:Class="WPFTest.Views.InputControls.TextBoxPageView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:codeExample="clr-namespace:Zylo.WPF.Controls.CodeExample;assembly=Zylo.WPF"
             xmlns:input="clr-namespace:Zylo.WPF.Controls.Input;assembly=Zylo.WPF"
             xmlns:mvvm="http://prismlibrary.com/"
             xmlns:inputControls="clr-namespace:WPFTest.ViewModels.InputControls"
             mc:Ignorable="d"
             d:DesignHeight="2500" d:DesignWidth="1200"
             mvvm:ViewModelLocator.AutoWireViewModel="True"
             d:DataContext="{d:DesignInstance inputControls:TextBoxPageViewModel}">

    <UserControl.Resources>
        <!-- 🎨 所有样式来自 Zylo.WPF 项目 -->
        <!-- TextBox 样式已在 Zylo.WPF/Resources/TextBox/ 目录下定义 -->
        <!-- 可用样式：TextBoxStyle, SmallTextBoxStyle, LargeTextBoxStyle, TransparentTextBoxStyle, RoundedTextBoxStyle, MultilineTextBoxStyle, SearchTextBoxStyle, ErrorTextBoxStyle, SuccessTextBoxStyle -->
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" 
                  HorizontalScrollBarVisibility="Disabled"
                  Padding="24">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>    <!-- 标题区域 -->
                <RowDefinition Height="*"/>       <!-- 主要内容 -->
                <RowDefinition Height="Auto"/>    <!-- 底部操作栏 -->
            </Grid.RowDefinitions>

            <!-- 标题区域 -->
            <StackPanel Grid.Row="0" Margin="0,0,0,24">
                <TextBlock Text="🎨 TextBox 控件展示"
                           FontSize="32"
                           FontWeight="Bold"
                           Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                           Margin="0,0,0,8"/>
                <TextBlock Text="展示 TextBox 控件的各种样式和交互效果，包括基础功能、高级特性和自定义组合控件"
                           FontSize="16"
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                           TextWrapping="Wrap"
                           Margin="0,0,0,12"/>

                <!-- 功能亮点 -->
                <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                    <Border Background="{DynamicResource SystemAccentColorSecondaryBrush}"
                            CornerRadius="12" Padding="8,4" Margin="0,0,8,0">
                        <TextBlock Text="✨ 多种样式" FontSize="12" Foreground="White"/>
                    </Border>
                    <Border Background="{DynamicResource SystemAccentColorSecondaryBrush}"
                            CornerRadius="12" Padding="8,4" Margin="0,0,8,0">
                        <TextBlock Text="🔧 可控参数" FontSize="12" Foreground="White"/>
                    </Border>
                    <Border Background="{DynamicResource SystemAccentColorSecondaryBrush}"
                            CornerRadius="12" Padding="8,4" Margin="0,0,8,0">
                        <TextBlock Text="🎯 组合控件" FontSize="12" Foreground="White"/>
                    </Border>
                </StackPanel>
                
                <!-- 状态显示 -->
                <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                        BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Padding="16,12"
                        Margin="0,16,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <ui:SymbolIcon Symbol="Info24" 
                                       FontSize="20" 
                                       Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                       VerticalAlignment="Center"
                                       Margin="0,0,12,0"/>
                        <TextBlock Grid.Column="1" 
                                   Text="{Binding StatusMessage}" 
                                   FontSize="14"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                   VerticalAlignment="Center"
                                   TextWrapping="Wrap"/>
                    </Grid>
                </Border>
            </StackPanel>

            <!-- 主要内容区域 -->
            <Border Grid.Row="1" Margin="0,0,0,20">
                <StackPanel>

                    <!-- 基础功能展示 -->
                    <ui:CardExpander Header="🎯 基础功能" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 TextBox 的基础功能和用法"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 控件展示区域 -->
                            <WrapPanel Margin="0,0,0,16">
                                <!-- 标准 TextBox -->
                                <StackPanel Margin="8">
                                    <TextBlock Text="标准 TextBox" FontWeight="Bold" Margin="0,0,0,4"/>
                                    <ui:TextBox Style="{StaticResource TextBoxStyle}"
                                                Text="{Binding StandardTextValue, UpdateSourceTrigger=PropertyChanged}"
                                                PlaceholderText="请输入文本..."/>
                                </StackPanel>

                                <!-- 小型 TextBox -->
                                <StackPanel Margin="8">
                                    <TextBlock Text="小型 TextBox" FontWeight="Bold" Margin="0,0,0,4"/>
                                    <ui:TextBox Style="{StaticResource SmallTextBoxStyle}"
                                                Text="{Binding SmallTextValue, UpdateSourceTrigger=PropertyChanged}"
                                                PlaceholderText="小型输入框"/>
                                </StackPanel>

                                <!-- 大型 TextBox -->
                                <StackPanel Margin="8">
                                    <TextBlock Text="大型 TextBox" FontWeight="Bold" Margin="0,0,0,4"/>
                                    <ui:TextBox Style="{StaticResource LargeTextBoxStyle}"
                                                Text="{Binding LargeTextValue, UpdateSourceTrigger=PropertyChanged}"
                                                PlaceholderText="大型输入框"/>
                                </StackPanel>
                            </WrapPanel>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="基础功能代码示例"
                                Language="XAML"
                                Description="展示 TextBox 的基础用法"
                                ShowTabs="True"
                                XamlCode="{Binding BasicXamlExample}"
                                CSharpCode="{Binding BasicCSharpExample}"
                                IsExpanded="False"
                                Margin="0,16,0,0"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 高级功能展示 -->
                    <ui:CardExpander Header="⚡ 高级功能" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 TextBox 的高级功能和自定义样式"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 高级控件展示区域 -->
                            <StackPanel>
                                <!-- 多行文本框 -->
                                <GroupBox Header="多行文本框" Padding="15" Margin="0,0,0,16">
                                    <StackPanel>
                                        <TextBlock Text="支持多行输入、自动换行和滚动条的文本框"
                                                   FontSize="12"
                                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                   Margin="0,0,0,8"/>
                                        <ui:TextBox Style="{StaticResource MultilineTextBoxStyle}"
                                                    Text="{Binding MultilineTextValue, UpdateSourceTrigger=PropertyChanged}"
                                                    PlaceholderText="这是一个多行文本框示例&#x0a;您可以在这里输入多行文本&#x0a;支持换行和自动滚动&#x0a;&#x0a;试试输入更多内容来测试滚动功能..."
                                                    HorizontalAlignment="Left"/>

                                        <!-- 多行文本框操作按钮 -->
                                        <StackPanel Orientation="Horizontal" Margin="0,8,0,0">
                                            <ui:Button Content="清空内容"
                                                       Command="{Binding ClearMultilineTextCommand}"
                                                       Appearance="Secondary"
                                                       Margin="0,0,8,0"/>
                                            <ui:Button Content="添加示例文本"
                                                       Command="{Binding AddSampleTextCommand}"
                                                       Appearance="Secondary"/>
                                        </StackPanel>
                                    </StackPanel>
                                </GroupBox>

                                <!-- 特殊样式 -->
                                <GroupBox Header="特殊样式文本框" Padding="15" Margin="0,0,0,16">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <Border Grid.Column="0" Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                                CornerRadius="8" Padding="12" Margin="0,0,4,0">
                                            <StackPanel>
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                                                    <TextBlock Text="🔍" FontSize="14" Margin="0,0,4,0"/>
                                                    <TextBlock Text="透明样式" FontWeight="Bold"/>
                                                </StackPanel>
                                                <TextBlock Text="底部边框，透明背景"
                                                           FontSize="11"
                                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                           Margin="0,0,0,8"/>
                                                <ui:TextBox Style="{StaticResource TransparentTextBoxStyle}"
                                                            Text="{Binding TransparentTextValue, UpdateSourceTrigger=PropertyChanged}"
                                                            PlaceholderText="透明背景"/>
                                            </StackPanel>
                                        </Border>

                                        <Border Grid.Column="1" Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                                CornerRadius="8" Padding="12" Margin="4,0">
                                            <StackPanel>
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                                                    <TextBlock Text="🎨" FontSize="14" Margin="0,0,4,0"/>
                                                    <TextBlock Text="圆角样式" FontWeight="Bold"/>
                                                </StackPanel>
                                                <TextBlock Text="圆角边框，增大内边距"
                                                           FontSize="11"
                                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                           Margin="0,0,0,8"/>
                                                <ui:TextBox Style="{StaticResource RoundedTextBoxStyle}"
                                                            Text="{Binding RoundedTextValue, UpdateSourceTrigger=PropertyChanged}"
                                                            PlaceholderText="圆角边框"/>
                                            </StackPanel>
                                        </Border>

                                        <Border Grid.Column="2" Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                                CornerRadius="8" Padding="12" Margin="4,0,0,0">
                                            <StackPanel>
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                                                    <TextBlock Text="🔍" FontSize="14" Margin="0,0,4,0"/>
                                                    <TextBlock Text="搜索框样式" FontWeight="Bold"/>
                                                </StackPanel>
                                                <TextBlock Text="搜索专用样式，带边框和聚焦效果"
                                                           FontSize="11"
                                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                           Margin="0,0,0,8"/>
                                                <ui:TextBox Style="{StaticResource SearchTextBoxStyle}"
                                                            Text="{Binding SearchTextValue, UpdateSourceTrigger=PropertyChanged}"
                                                            PlaceholderText="🔍 搜索内容..."/>
                                            </StackPanel>
                                        </Border>
                                    </Grid>
                                </GroupBox>
                            </StackPanel>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="高级功能代码示例"
                                Language="XAML"
                                Description="展示 TextBox 的高级用法和自定义样式"
                                ShowTabs="True"
                                XamlCode="{Binding AdvancedXamlExample}"
                                CSharpCode="{Binding AdvancedCSharpExample}"
                                IsExpanded="False"
                                Margin="0,16,0,0"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 组合控件展示 -->
                    <ui:CardExpander Header="🔗 组合控件" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 TextBlock + TextBox 组合控件的现代化设计"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 传统样式组合控件 -->
                            <GroupBox Header="传统样式组合控件" Padding="16" Margin="0,0,0,16">
                                <StackPanel>
                                    <!-- 第一行：用户名和邮箱 -->
                                    <Grid Margin="0,0,0,12">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="16"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- 用户名输入 -->
                                        <Border Grid.Column="0" Style="{StaticResource LabelTextBoxContainerStyle}">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="70"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>
                                                <TextBlock Grid.Column="0" Text="用户名:" Style="{StaticResource LabelTextStyle}"/>
                                                <ui:TextBox Grid.Column="1" Style="{StaticResource LabelTextBoxInputStyle}"
                                                            Text="{Binding UsernameValue, UpdateSourceTrigger=PropertyChanged}"
                                                            PlaceholderText="请输入用户名"/>
                                            </Grid>
                                        </Border>

                                        <!-- 邮箱输入 -->
                                        <Border Grid.Column="2" Style="{StaticResource LabelTextBoxContainerStyle}">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="70"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>
                                                <TextBlock Grid.Column="0" Text="邮箱:" Style="{StaticResource LabelTextStyle}"/>
                                                <ui:TextBox Grid.Column="1" Style="{StaticResource LabelTextBoxInputStyle}"
                                                            Text="{Binding EmailValue, UpdateSourceTrigger=PropertyChanged}"
                                                            PlaceholderText="<EMAIL>"/>
                                            </Grid>
                                        </Border>
                                    </Grid>

                                    <!-- 第二行：个人描述 -->
                                    <Border Style="{StaticResource LabelTextBoxContainerStyle}">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="70"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Grid.Column="0" Text="个人描述:" Style="{StaticResource LabelTextStyle}" VerticalAlignment="Top"/>
                                            <ui:TextBox Grid.Column="1" Style="{StaticResource LabelTextBoxInputStyle}"
                                                        Text="{Binding DescriptionValue, UpdateSourceTrigger=PropertyChanged}"
                                                        PlaceholderText="请输入个人描述信息..."
                                                        TextWrapping="Wrap"
                                                        AcceptsReturn="True"
                                                        VerticalContentAlignment="Top"
                                                        MinHeight="50"/>
                                        </Grid>
                                    </Border>
                                </StackPanel>
                            </GroupBox>

                            <!-- 现代化样式组合控件 -->
                            <GroupBox Header="现代化样式组合控件" Padding="16" Margin="0,0,0,16">
                                <StackPanel>
                                    <!-- 第一行：姓名和公司 -->
                                    <Grid Margin="0,0,0,16">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="16"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- 姓名输入 -->
                                        <Border Grid.Column="0" Style="{StaticResource ModernLabelTextBoxStyle}">
                                            <StackPanel>
                                                <TextBlock Text="姓名" Style="{StaticResource ModernLabelStyle}"/>
                                                <ui:TextBox Style="{StaticResource ModernInputStyle}"
                                                            Text="{Binding FullNameValue, UpdateSourceTrigger=PropertyChanged}"
                                                            PlaceholderText="请输入您的全名"/>
                                            </StackPanel>
                                        </Border>

                                        <!-- 公司输入 -->
                                        <Border Grid.Column="2" Style="{StaticResource ModernLabelTextBoxStyle}">
                                            <StackPanel>
                                                <TextBlock Text="公司/组织" Style="{StaticResource ModernLabelStyle}"/>
                                                <ui:TextBox Style="{StaticResource ModernInputStyle}"
                                                            Text="{Binding CompanyValue, UpdateSourceTrigger=PropertyChanged}"
                                                            PlaceholderText="请输入公司或组织名称"/>
                                            </StackPanel>
                                        </Border>
                                    </Grid>

                                    <!-- 第二行：联系方式 -->
                                    <Border Style="{StaticResource ModernLabelTextBoxStyle}" Margin="0,0,0,16">
                                        <StackPanel>
                                            <TextBlock Text="联系电话" Style="{StaticResource ModernLabelStyle}"/>
                                            <ui:TextBox Style="{StaticResource ModernInputStyle}"
                                                        Text="{Binding PhoneValue, UpdateSourceTrigger=PropertyChanged}"
                                                        PlaceholderText="+86 138 0000 0000"/>
                                        </StackPanel>
                                    </Border>

                                    <!-- 第三行：地址信息 -->
                                    <Border Style="{StaticResource ModernLabelTextBoxStyle}">
                                        <StackPanel>
                                            <TextBlock Text="详细地址" Style="{StaticResource ModernLabelStyle}"/>
                                            <ui:TextBox Style="{StaticResource ModernInputStyle}"
                                                        Text="{Binding AddressValue, UpdateSourceTrigger=PropertyChanged}"
                                                        PlaceholderText="请输入详细地址信息，包括省市区街道等"
                                                        TextWrapping="Wrap"
                                                        AcceptsReturn="True"
                                                        VerticalContentAlignment="Top"
                                                        MinHeight="60"/>
                                        </StackPanel>
                                    </Border>
                                </StackPanel>
                            </GroupBox>

                            <!-- 自定义控件展示 -->
                            <GroupBox Header="LabelTextBox 自定义控件 - 可控参数展示" Padding="15" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="一个控件，多种配置 - 展示 LabelTextBox 的强大可控参数"
                                               FontSize="14"
                                               FontWeight="SemiBold"
                                               Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                               Margin="0,0,0,16"/>

                                    <!-- 参数对比展示 -->
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- 左列：宽度参数展示 -->
                                        <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                            <TextBlock Text="🎯 标签宽度参数 (LabelWidth)" FontWeight="Bold" Margin="0,0,0,8"/>

                                            <!-- 宽度对比 - 优化协调性 -->
                                            <Border Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                                    Padding="12" CornerRadius="6" Margin="0,0,0,8">
                                                <StackPanel>
                                                    <TextBlock Text="LabelWidth=&quot;50&quot;" FontFamily="Consolas" FontSize="11"
                                                               Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                                               Margin="0,0,0,6"/>
                                                    <input:LabelTextBox LabelText="短:"
                                                                        LabelWidth="40"
                                                                        LabelFontSize="14"
                                                                        LabelFontWeight="Medium"
                                                                        Text="{Binding CustomShortLabelValue, UpdateSourceTrigger=PropertyChanged}"
                                                                        PlaceholderText="40px宽度"/>
                                                </StackPanel>
                                            </Border>

                                            <Border Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                                    Padding="12" CornerRadius="6" Margin="0,0,0,8">
                                                <StackPanel>
                                                    <TextBlock Text="LabelWidth=&quot;90&quot;" FontFamily="Consolas" FontSize="11"
                                                               Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                                               Margin="0,0,0,6"/>
                                                    <input:LabelTextBox LabelText="中等长度:"
                                                                        LabelWidth="80"
                                                                        LabelFontSize="14"
                                                                        LabelFontWeight="Medium"
                                                                        Text="{Binding CustomMediumLabelValue, UpdateSourceTrigger=PropertyChanged}"
                                                                        PlaceholderText="80px宽度"/>
                                                </StackPanel>
                                            </Border>

                                            <Border Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                                    Padding="12" CornerRadius="6" Margin="0,0,0,8">
                                                <StackPanel>
                                                    <TextBlock Text="LabelWidth=&quot;130&quot; + 自动换行" FontFamily="Consolas" FontSize="11"
                                                               Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                                               Margin="0,0,0,6"/>
                                                    <input:LabelTextBox LabelText="这是一个很长的标签文本，会自动换行:"
                                                                        LabelWidth="120"
                                                                        LabelFontSize="14"
                                                                        LabelFontWeight="Medium"
                                                                        Text="{Binding CustomLongLabelValue, UpdateSourceTrigger=PropertyChanged}"
                                                                        PlaceholderText="120px宽度，自动换行"/>
                                                </StackPanel>
                                            </Border>
                                        </StackPanel>

                                        <!-- 右列：样式和功能参数展示 -->
                                        <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                            <TextBlock Text="🎨 样式和功能参数" FontWeight="Bold" Margin="0,0,0,8"/>

                                            <!-- 基础样式 - 优化协调性 -->
                                            <Border Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                                    Padding="12" CornerRadius="6" Margin="0,0,0,8">
                                                <StackPanel>
                                                    <TextBlock Text="默认样式 + PlaceholderText" FontFamily="Consolas" FontSize="11"
                                                               Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                                               Margin="0,0,0,6"/>
                                                    <input:LabelTextBox LabelText="用户名:"
                                                                        LabelWidth="60"
                                                                        LabelFontSize="14"
                                                                        LabelFontWeight="Medium"
                                                                        Text="{Binding CustomUsernameValue, UpdateSourceTrigger=PropertyChanged}"
                                                                        PlaceholderText="占位符文本提示"/>
                                                </StackPanel>
                                            </Border>

                                            <!-- 现代化样式 -->
                                            <Border Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                                    Padding="12" CornerRadius="6" Margin="0,0,0,8">
                                                <StackPanel>
                                                    <TextBlock Text="Style=&quot;ModernLabelTextBoxControlStyle&quot;" FontFamily="Consolas" FontSize="11"
                                                               Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                                               Margin="0,0,0,6"/>
                                                    <input:LabelTextBox Style="{StaticResource ModernLabelTextBoxControlStyle}"
                                                                        LabelText="现代化样式"
                                                                        Text="{Binding CustomFullNameValue, UpdateSourceTrigger=PropertyChanged}"
                                                                        PlaceholderText="卡片样式+阴影"/>
                                                </StackPanel>
                                            </Border>

                                            <!-- 紧凑样式 -->
                                            <Border Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                                    Padding="12" CornerRadius="6" Margin="0,0,0,8">
                                                <StackPanel>
                                                    <TextBlock Text="Style=&quot;CompactLabelTextBoxControlStyle&quot;" FontFamily="Consolas" FontSize="11"
                                                               Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                                               Margin="0,0,0,6"/>
                                                    <input:LabelTextBox Style="{StaticResource CompactLabelTextBoxControlStyle}"
                                                                        LabelText="紧凑样式:"
                                                                        LabelWidth="70"
                                                                        LabelFontSize="14"
                                                                        LabelFontWeight="Medium"
                                                                        Text="{Binding CustomCompanyValue, UpdateSourceTrigger=PropertyChanged}"
                                                                        PlaceholderText="底部边框样式"/>
                                                </StackPanel>
                                            </Border>
                                        </StackPanel>
                                    </Grid>

                                    <!-- 多行功能展示 - 优化协调性 -->
                                    <TextBlock Text="📝 多行功能参数 (IsMultiline + TextBoxMinHeight)" FontWeight="Bold" Margin="0,16,0,8"/>
                                    <Border Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                            Padding="16" CornerRadius="6" Margin="0,0,0,8">
                                        <StackPanel>
                                            <TextBlock Text="IsMultiline=&quot;True&quot; TextBoxMinHeight=&quot;70&quot;" FontFamily="Consolas" FontSize="11"
                                                       Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                                       Margin="0,0,0,8"/>
                                            <input:LabelTextBox LabelText="多行描述:"
                                                                LabelWidth="80"
                                                                LabelFontSize="14"
                                                                LabelFontWeight="Medium"
                                                                Text="{Binding CustomDescriptionValue, UpdateSourceTrigger=PropertyChanged}"
                                                                PlaceholderText="支持多行输入&#x0a;自动换行&#x0a;滚动条显示"
                                                                IsMultiline="True"
                                                                TextBoxMinHeight="70"/>
                                        </StackPanel>
                                    </Border>

                                    <!-- 参数总结 -->
                                    <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                                            BorderBrush="{DynamicResource SystemAccentColorPrimaryBrush}"
                                            BorderThickness="2"
                                            Padding="16" CornerRadius="8" Margin="0,16,0,0">
                                        <StackPanel>
                                            <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                                <TextBlock Text="💡" FontSize="16" Margin="0,0,8,0"/>
                                                <TextBlock Text="可控参数总结" FontWeight="Bold" FontSize="15"
                                                           Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                                            </StackPanel>

                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>

                                                <!-- 左列参数 -->
                                                <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                                    <TextBlock Text="🎯 LabelWidth: 精确控制标签宽度 (30-200px)" FontSize="12" Margin="0,0,0,4"/>
                                                    <TextBlock Text="📝 LabelText: 标签文本内容，支持多行自动换行" FontSize="12" Margin="0,0,0,4"/>
                                                    <TextBlock Text="💬 PlaceholderText: 占位符提示文本" FontSize="12" Margin="0,0,0,4"/>
                                                    <TextBlock Text="📏 TextBoxMinHeight: 自定义输入框最小高度" FontSize="12" Margin="0,0,0,4"/>
                                                </StackPanel>

                                                <!-- 右列参数 -->
                                                <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                                    <TextBlock Text="📄 IsMultiline: 启用多行输入模式" FontSize="12" Margin="0,0,0,4"/>
                                                    <TextBlock Text="🎨 Style: 三种预设样式 (默认/现代化/紧凑)" FontSize="12" Margin="0,0,0,4"/>
                                                    <TextBlock Text="🔧 其他: IsReadOnly, MaxLength, LabelFontSize" FontSize="12" Margin="0,0,0,4"/>
                                                    <TextBlock Text="✨ 完全可定制的组合控件解决方案" FontSize="12" FontWeight="Medium"
                                                               Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                                                </StackPanel>
                                            </Grid>
                                        </StackPanel>
                                    </Border>
                                </StackPanel>
                            </GroupBox>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="组合控件代码示例"
                                Language="XAML"
                                Description="展示 TextBlock + TextBox 组合控件的实现方式"
                                ShowTabs="True"
                                XamlCode="{Binding CombinedXamlExample}"
                                CSharpCode="{Binding CombinedCSharpExample}"
                                IsExpanded="False"
                                Margin="0,16,0,0"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 样式展示 -->
                    <ui:CardExpander Header="🎨 样式展示" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示来自 Zylo.WPF 的各种 TextBox 样式"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 样式展示区域 -->
                            <StackPanel>
                                <!-- 状态样式 -->
                                <WrapPanel>
                                    <StackPanel Margin="8">
                                        <TextBlock Text="错误状态" FontWeight="Bold" Margin="0,0,0,4"/>
                                        <ui:TextBox Style="{StaticResource ErrorTextBoxStyle}"
                                                    Text="{Binding ErrorTextValue, UpdateSourceTrigger=PropertyChanged}"
                                                    PlaceholderText="错误状态"/>
                                    </StackPanel>

                                    <StackPanel Margin="8">
                                        <TextBlock Text="成功状态" FontWeight="Bold" Margin="0,0,0,4"/>
                                        <ui:TextBox Style="{StaticResource SuccessTextBoxStyle}"
                                                    Text="{Binding SuccessTextValue, UpdateSourceTrigger=PropertyChanged}"
                                                    PlaceholderText="成功状态"/>
                                    </StackPanel>
                                </WrapPanel>

                                <!-- 特殊功能 -->
                                <WrapPanel>
                                    <StackPanel Margin="8">
                                        <TextBlock Text="只读文本框" FontWeight="Bold" Margin="0,0,0,4"/>
                                        <ui:TextBox Style="{StaticResource TextBoxStyle}"
                                                    Text="只读内容，无法编辑"
                                                    IsReadOnly="True"/>
                                    </StackPanel>

                                    <StackPanel Margin="8">
                                        <TextBlock Text="禁用文本框" FontWeight="Bold" Margin="0,0,0,4"/>
                                        <ui:TextBox Style="{StaticResource TextBoxStyle}"
                                                    Text="禁用状态"
                                                    IsEnabled="False"/>
                                    </StackPanel>
                                </WrapPanel>
                            </StackPanel>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="样式代码示例"
                                Language="XAML"
                                Description="展示如何使用 Zylo.WPF 中的 TextBox 样式"
                                ShowTabs="True"
                                XamlCode="{Binding StylesXamlExample}"
                                IsExpanded="False"
                                Margin="0,16,0,0"/>
                        </StackPanel>
                    </ui:CardExpander>

                </StackPanel>
            </Border>

            <!-- 底部操作栏 -->
            <Border Grid.Row="2" 
                    Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Padding="16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 统计信息 -->
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock Text="{Binding InteractionCount, StringFormat='交互次数: {0}'}" 
                                   FontSize="14" 
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   Margin="0,0,20,0"/>
                        <TextBlock Text="{Binding LastAction, StringFormat='最后操作: {0}'}" 
                                   FontSize="14" 
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                    </StackPanel>

                    <!-- 操作按钮 -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <ui:Button Content="重置计数" 
                                   Command="{Binding ResetCountCommand}"
                                   Appearance="Secondary"
                                   Margin="0,0,8,0"/>
                        <ui:Button Content="清除状态" 
                                   Command="{Binding ClearStatusCommand}"
                                   Appearance="Secondary"/>
                    </StackPanel>
                </Grid>
            </Border>

        </Grid>
    </ScrollViewer>
</UserControl>
