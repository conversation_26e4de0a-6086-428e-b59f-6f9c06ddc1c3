<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <!-- DatePicker 中文格式样式 -->
    <Style x:Key="ChineseDatePickerStyle" TargetType="DatePicker">
        <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontFamily" Value="{DynamicResource ContentControlThemeFontFamily}"/>
        <Setter Property="HorizontalContentAlignment" Value="Left"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="IsTodayHighlighted" Value="True"/>
        <Setter Property="FirstDayOfWeek" Value="Monday"/>
        <Setter Property="SelectedDateFormat" Value="Long"/>

        <!-- 设置中文文化信息 -->
        <Setter Property="Language" Value="zh-CN"/>

        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorSecondaryBrush}"/>
            </Trigger>
            <Trigger Property="IsKeyboardFocused" Value="True">
                <Setter Property="BorderBrush" Value="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                <Setter Property="Background" Value="{DynamicResource ControlFillColorInputActiveBrush}"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Opacity" Value="0.5"/>
                <Setter Property="Foreground" Value="{DynamicResource TextFillColorDisabledBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- CalendarDatePicker 基础样式 -->
    <Style x:Key="CalendarDatePickerBaseStyle" TargetType="ui:CalendarDatePicker">
        <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="4"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontFamily" Value="{DynamicResource ContentControlThemeFontFamily}"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="MinWidth" Value="120"/>
        <Setter Property="MinHeight" Value="32"/>
        <Setter Property="HorizontalContentAlignment" Value="Left"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="IsTodayHighlighted" Value="True"/>
        <Setter Property="FirstDayOfWeek" Value="Monday"/>

        <!-- 添加鼠标悬停和按下效果 -->
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorSecondaryBrush}"/>
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="{DynamicResource ControlFillColorTertiaryBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorTertiaryBrush}"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Opacity" Value="0.5"/>
                <Setter Property="Foreground" Value="{DynamicResource TextFillColorDisabledBrush}"/>
            </Trigger>
            <Trigger Property="IsCalendarOpen" Value="True">
                <Setter Property="BorderBrush" Value="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                <Setter Property="Background" Value="{DynamicResource ControlFillColorInputActiveBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- CalendarDatePicker 标准样式 -->
    <Style x:Key="CalendarDatePickerStyle" TargetType="ui:CalendarDatePicker" BasedOn="{StaticResource CalendarDatePickerBaseStyle}">
        <!-- 标准样式的特定设置 -->
        <Setter Property="Width" Value="160"/>
        <Setter Property="Height" Value="36"/>
    </Style>

    <!-- CalendarDatePicker 小型样式 -->
    <Style x:Key="SmallCalendarDatePickerStyle" TargetType="ui:CalendarDatePicker" BasedOn="{StaticResource CalendarDatePickerBaseStyle}">
        <Setter Property="Padding" Value="6"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="MinWidth" Value="100"/>
        <Setter Property="MinHeight" Value="28"/>
        <Setter Property="Width" Value="140"/>
        <Setter Property="Height" Value="32"/>
    </Style>

    <!-- CalendarDatePicker 大型样式 -->
    <Style x:Key="LargeCalendarDatePickerStyle" TargetType="ui:CalendarDatePicker" BasedOn="{StaticResource CalendarDatePickerBaseStyle}">
        <Setter Property="Padding" Value="12"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="MinWidth" Value="140"/>
        <Setter Property="MinHeight" Value="40"/>
        <Setter Property="Width" Value="180"/>
        <Setter Property="Height" Value="44"/>
    </Style>

    <!-- CalendarDatePicker 透明样式 -->
    <Style x:Key="TransparentCalendarDatePickerStyle" TargetType="ui:CalendarDatePicker" BasedOn="{StaticResource CalendarDatePickerBaseStyle}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- CalendarDatePicker 紧凑样式 -->
    <Style x:Key="CompactCalendarDatePickerStyle" TargetType="ui:CalendarDatePicker" BasedOn="{StaticResource CalendarDatePickerBaseStyle}">
        <Setter Property="Padding" Value="4"/>
        <Setter Property="FontSize" Value="11"/>
        <Setter Property="MinWidth" Value="80"/>
        <Setter Property="MinHeight" Value="24"/>
        <Setter Property="Width" Value="120"/>
        <Setter Property="Height" Value="28"/>
        <Setter Property="CornerRadius" Value="2"/>
    </Style>

    <!-- CalendarDatePicker 强调样式 -->
    <Style x:Key="AccentCalendarDatePickerStyle" TargetType="ui:CalendarDatePicker" BasedOn="{StaticResource CalendarDatePickerBaseStyle}">
        <Setter Property="Background" Value="{DynamicResource SystemAccentColorSecondaryBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource SystemAccentColorPrimaryBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource SystemAccentColorPrimaryBrush}"/>
        <Setter Property="BorderThickness" Value="2"/>
        
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{DynamicResource SystemAccentColorTertiaryBrush}"/>
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                <Setter Property="Foreground" Value="{DynamicResource TextOnAccentFillColorPrimaryBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- CalendarDatePicker 圆角样式 -->
    <Style x:Key="RoundedCalendarDatePickerStyle" TargetType="ui:CalendarDatePicker" BasedOn="{StaticResource CalendarDatePickerBaseStyle}">
        <Setter Property="CornerRadius" Value="16"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="MinHeight" Value="36"/>
        <Setter Property="Height" Value="36"/>
    </Style>

</ResourceDictionary>
