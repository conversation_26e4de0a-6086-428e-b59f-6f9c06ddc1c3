<UserControl x:Class="WPFTest.Views.SQLite.SqliteDbManagerView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:local="clr-namespace:WPFTest.Views.SQLite"
             xmlns:models="clr-namespace:WPFTest.Models.SQLite"
             xmlns:system="clr-namespace:System;assembly=mscorlib"
             mc:Ignorable="d"
             d:DesignHeight="800" d:DesignWidth="1200">

    <UserControl.Resources>
        <!-- 连接状态转换器 -->
        <local:ConnectionStatusToColorConverter x:Key="ConnectionStatusToColorConverter"/>
        <local:ConnectionStatusToIconConverter x:Key="ConnectionStatusToIconConverter"/>

        <!-- 文件大小转换器 -->
        <local:FileSizeConverter x:Key="FileSizeConverter"/>
        
        <!-- 数据模板 -->
        <DataTemplate x:Key="DatabaseFileTemplate" DataType="{x:Type models:SqliteDbFileModel}">
            <Border Background="{DynamicResource ControlFillColorDefaultBrush}" 
                    BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}" 
                    BorderThickness="1" 
                    CornerRadius="6" 
                    Padding="12" 
                    Margin="4">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- 连接状态图标 -->
                    <Border Grid.Column="0" 
                            Background="{Binding ConnectionStatus, Converter={StaticResource ConnectionStatusToColorConverter}}"
                            CornerRadius="12" 
                            Width="24" 
                            Height="24" 
                            Margin="0,0,12,0">
                        <TextBlock Text="{Binding ConnectionStatus, Converter={StaticResource ConnectionStatusToIconConverter}}" 
                                   HorizontalAlignment="Center" 
                                   VerticalAlignment="Center" 
                                   FontSize="12"/>
                    </Border>
                    
                    <!-- 数据库信息 -->
                    <StackPanel Grid.Column="1">
                        <TextBlock Text="{Binding DisplayTitle}" 
                                   FontWeight="SemiBold" 
                                   FontSize="14" 
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                        <TextBlock Text="{Binding FilePath}" 
                                   FontSize="12" 
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}" 
                                   TextTrimming="CharacterEllipsis"/>
                        <StackPanel Orientation="Horizontal" Margin="0,4,0,0">
                            <TextBlock Text="{Binding FileSizeText}" 
                                       FontSize="11" 
                                       Foreground="{DynamicResource TextFillColorTertiaryBrush}" 
                                       Margin="0,0,12,0"/>
                            <TextBlock Text="{Binding TableCount, StringFormat='表: {0}'}" 
                                       FontSize="11" 
                                       Foreground="{DynamicResource TextFillColorTertiaryBrush}" 
                                       Margin="0,0,12,0"/>
                            <TextBlock Text="{Binding ModifiedDate, StringFormat='修改: {0:yyyy-MM-dd}'}" 
                                       FontSize="11" 
                                       Foreground="{DynamicResource TextFillColorTertiaryBrush}"/>
                        </StackPanel>
                    </StackPanel>
                    
                    <!-- 操作按钮 -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal">
                        <ui:Button Icon="{ui:SymbolIcon DatabaseLink24}" 
                                   ToolTip="测试连接" 
                                   Appearance="Secondary" 
                                   Margin="4,0"/>
                        <ui:Button Icon="{ui:SymbolIcon Edit24}" 
                                   ToolTip="编辑" 
                                   Appearance="Secondary" 
                                   Margin="4,0"/>
                        <ui:Button Icon="{ui:SymbolIcon Delete24}" 
                                   ToolTip="删除" 
                                   Appearance="Danger" 
                                   Margin="4,0"/>
                    </StackPanel>
                </Grid>
            </Border>
        </DataTemplate>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" 
                Background="{DynamicResource ControlFillColorDefaultBrush}" 
                BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}" 
                BorderThickness="1" 
                CornerRadius="8" 
                Padding="16" 
                Margin="0,0,0,12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0">
                    <TextBlock Text="SQLite 数据库管理器" 
                               FontSize="20" 
                               FontWeight="SemiBold" 
                               Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                    <TextBlock Text="管理和监控 SQLite 数据库文件" 
                               FontSize="14" 
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}" 
                               Margin="0,4,0,0"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <ui:Button Content="刷新" 
                               Icon="{ui:SymbolIcon ArrowClockwise24}" 
                               Command="{Binding RefreshCommand}" 
                               Appearance="Secondary" 
                               Margin="0,0,8,0"/>
                    <ui:Button Content="设置" 
                               Icon="{ui:SymbolIcon Settings24}" 
                               Appearance="Secondary"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 工具栏 -->
        <Border Grid.Row="1" 
                Background="{DynamicResource ControlFillColorDefaultBrush}" 
                BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}" 
                BorderThickness="1" 
                CornerRadius="8" 
                Padding="16" 
                Margin="0,0,0,12">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- 文件夹选择 -->
                <Grid Grid.Row="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" 
                               Text="当前文件夹:" 
                               VerticalAlignment="Center" 
                               Margin="0,0,8,0"/>
                    
                    <ui:TextBox Grid.Column="1" 
                                Text="{Binding CurrentFolderPath}" 
                                PlaceholderText="选择 SQLite 数据库文件夹..." 
                                IsReadOnly="True" 
                                Margin="0,0,8,0"/>
                    
                    <ui:Button Grid.Column="2" 
                               Content="浏览" 
                               Icon="{ui:SymbolIcon FolderOpen24}" 
                               Command="{Binding SelectFolderCommand}" 
                               Margin="0,0,8,0"/>
                    
                    <ui:Button Grid.Column="3" 
                               Content="扫描" 
                               Icon="{ui:SymbolIcon Search24}" 
                               Command="{Binding ScanFolderCommand}" 
                               Appearance="Primary"/>
                </Grid>
                
                <!-- 操作按钮和选项 -->
                <Grid Grid.Row="1" Margin="0,12,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <ui:Button Content="添加文件" 
                                   Icon="{ui:SymbolIcon Add24}" 
                                   Command="{Binding AddDatabaseFileCommand}" 
                                   Margin="0,0,8,0"/>
                        <ui:Button Content="批量测试" 
                                   Icon="{ui:SymbolIcon DatabaseLink24}" 
                                   Command="{Binding TestAllConnectionsCommand}" 
                                   Margin="0,0,8,0"/>
                        <ui:Button Content="删除选中" 
                                   Icon="{ui:SymbolIcon Delete24}" 
                                   Command="{Binding DeleteCommand}" 
                                   Appearance="Danger" 
                                   Margin="0,0,8,0"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <CheckBox Content="包含子文件夹" 
                                  IsChecked="{Binding IncludeSubfolders}" 
                                  Margin="0,0,16,0"/>
                        <CheckBox Content="自动测试连接" 
                                  IsChecked="{Binding AutoTestConnection}"/>
                    </StackPanel>
                </Grid>
            </Grid>
        </Border>

        <!-- 主内容区域 -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- 数据库列表 -->
            <Border Grid.Column="0" 
                    Background="{DynamicResource ControlFillColorDefaultBrush}" 
                    BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}" 
                    BorderThickness="1" 
                    CornerRadius="8" 
                    Padding="16">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- 列表标题和搜索 -->
                    <Grid Grid.Row="0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Column="0" 
                                   Text="数据库文件列表" 
                                   FontSize="16" 
                                   FontWeight="SemiBold" 
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                        
                        <ui:TextBox Grid.Column="1" 
                                    Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}" 
                                    PlaceholderText="搜索数据库..." 
                                    Icon="{ui:SymbolIcon Search24}" 
                                    Width="200"/>
                    </Grid>
                    
                    <!-- 连接状态统计 -->
                    <TextBlock Grid.Row="1" 
                               Text="{Binding ConnectionStatusSummary}" 
                               FontSize="12" 
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}" 
                               Margin="0,8,0,12"/>
                    
                    <!-- 数据库列表 -->
                    <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
                        <ItemsControl ItemsSource="{Binding FilteredDatabaseFiles}" 
                                      ItemTemplate="{StaticResource DatabaseFileTemplate}">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <StackPanel/>
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                        </ItemsControl>
                    </ScrollViewer>
                </Grid>
            </Border>
            
            <!-- 分隔符 -->
            <GridSplitter Grid.Column="1" 
                          Width="8" 
                          HorizontalAlignment="Center" 
                          VerticalAlignment="Stretch" 
                          Background="Transparent" 
                          Margin="4,0"/>
            
            <!-- 详细信息面板 -->
            <Border Grid.Column="2" 
                    Background="{DynamicResource ControlFillColorDefaultBrush}" 
                    BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}" 
                    BorderThickness="1" 
                    CornerRadius="8" 
                    Padding="16">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <!-- 详细信息标题 -->
                    <TextBlock Grid.Row="0" 
                               Text="数据库详细信息" 
                               FontSize="16" 
                               FontWeight="SemiBold" 
                               Foreground="{DynamicResource TextFillColorPrimaryBrush}" 
                               Margin="0,0,0,16"/>
                    
                    <!-- 详细信息内容 -->
                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <StackPanel DataContext="{Binding SelectedDatabaseFile}">
                            <!-- 这里将显示选中数据库的详细信息 -->
                            <TextBlock Text="选择一个数据库文件查看详细信息" 
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}" 
                                       HorizontalAlignment="Center" 
                                       VerticalAlignment="Center" 
                                       Margin="0,50,0,0">
                                <TextBlock.Style>
                                    <Style TargetType="TextBlock">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding}" Value="{x:Null}">
                                                <Setter Property="Visibility" Value="Visible"/>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding}" Value="{x:Static system:DBNull.Value}">
                                                <Setter Property="Visibility" Value="Visible"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                        <Setter Property="Visibility" Value="Collapsed"/>
                                    </Style>
                                </TextBlock.Style>
                            </TextBlock>
                        </StackPanel>
                    </ScrollViewer>
                    
                    <!-- 操作按钮 -->
                    <StackPanel Grid.Row="2" Orientation="Vertical" Margin="0,16,0,0">
                        <ui:Button Content="测试连接" 
                                   Icon="{ui:SymbolIcon DatabaseLink24}" 
                                   Command="{Binding TestConnectionCommand}" 
                                   HorizontalAlignment="Stretch" 
                                   Margin="0,0,0,8"/>
                        <ui:Button Content="编辑信息" 
                                   Icon="{ui:SymbolIcon Edit24}" 
                                   Command="{Binding EditDatabaseFileCommand}" 
                                   HorizontalAlignment="Stretch" 
                                   Margin="0,0,0,8"/>
                        <ui:Button Content="刷新信息" 
                                   Icon="{ui:SymbolIcon ArrowClockwise24}" 
                                   HorizontalAlignment="Stretch"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>

        <!-- 状态栏 -->
        <Border Grid.Row="3" 
                Background="{DynamicResource ControlFillColorDefaultBrush}" 
                BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}" 
                BorderThickness="1" 
                CornerRadius="8" 
                Padding="16" 
                Margin="0,12,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" 
                           Text="{Binding StatusMessage}" 
                           VerticalAlignment="Center" 
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <ui:ProgressRing IsIndeterminate="{Binding IsLoading}" 
                                     Width="16" 
                                     Height="16" 
                                     Margin="0,0,8,0">
                        <ui:ProgressRing.Style>
                            <Style TargetType="ui:ProgressRing">
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsLoading}" Value="False">
                                        <Setter Property="Visibility" Value="Collapsed"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </ui:ProgressRing.Style>
                    </ui:ProgressRing>
                    
                    <TextBlock Text="{Binding DatabaseFiles.Count, StringFormat='共 {0} 个数据库'}" 
                               VerticalAlignment="Center" 
                               Foreground="{DynamicResource TextFillColorTertiaryBrush}"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>
