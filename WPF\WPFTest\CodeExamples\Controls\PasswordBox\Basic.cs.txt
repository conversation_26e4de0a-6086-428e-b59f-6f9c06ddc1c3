// PasswordBox C# 基础用法示例
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Windows.Media;

namespace WPFTest.ViewModels.InputControls
{
    public partial class PasswordBoxPageViewModel : ObservableObject
    {
        /// <summary>
        /// 基础密码（支持数据绑定）
        /// </summary>
        [ObservableProperty]
        private string basicPassword = string.Empty;

        /// <summary>
        /// 确认密码（支持数据绑定）
        /// </summary>
        [ObservableProperty]
        private string confirmPassword = string.Empty;

        /// <summary>
        /// 切换显示的密码
        /// </summary>
        [ObservableProperty]
        private string togglePassword = string.Empty;

        /// <summary>
        /// 密码是否可见
        /// </summary>
        [ObservableProperty]
        private bool isPasswordVisible = false;

        /// <summary>
        /// 密码匹配消息
        /// </summary>
        public string PasswordMatchMessage =>
            string.IsNullOrEmpty(BasicPassword) || string.IsNullOrEmpty(ConfirmPassword)
                ? "请输入密码"
                : BasicPassword == ConfirmPassword
                    ? "✅ 密码匹配"
                    : "❌ 密码不匹配";

        /// <summary>
        /// 密码匹配颜色
        /// </summary>
        public Brush PasswordMatchColor =>
            string.IsNullOrEmpty(BasicPassword) || string.IsNullOrEmpty(ConfirmPassword)
                ? Brushes.Gray
                : BasicPassword == ConfirmPassword
                    ? Brushes.Green
                    : Brushes.Red;

        /// <summary>
        /// 密码是否匹配
        /// </summary>
        public bool PasswordsMatch => !string.IsNullOrEmpty(BasicPassword) && BasicPassword == ConfirmPassword;

        /// <summary>
        /// 切换密码可见性命令
        /// </summary>
        [RelayCommand]
        private void TogglePasswordVisibility()
        {
            IsPasswordVisible = !IsPasswordVisible;
        }

        /// <summary>
        /// 密码哈希加密（使用 SHA-256）
        /// </summary>
        /// <param name="password">原始密码</param>
        /// <returns>哈希值</returns>
        private string HashPassword(string password)
        {
            using var sha256 = SHA256.Create();
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password + "ZyloSalt2024")); // 添加盐值
            return Convert.ToBase64String(hashedBytes);
        }

        /// <summary>
        /// 验证密码哈希
        /// </summary>
        /// <param name="password">原始密码</param>
        /// <param name="hash">存储的哈希值</param>
        /// <returns>是否匹配</returns>
        private bool VerifyPassword(string password, string hash)
        {
            var computedHash = HashPassword(password);
            return computedHash == hash;
        }

        /// <summary>
        /// 密码强度值 (0-100)
        /// </summary>
        [ObservableProperty]
        private double passwordStrength = 0;

        /// <summary>
        /// 密码强度文本
        /// </summary>
        [ObservableProperty]
        private string passwordStrengthText = "请输入密码";

        /// <summary>
        /// 密码强度颜色
        /// </summary>
        [ObservableProperty]
        private Brush passwordStrengthColor = Brushes.Gray;

        /// <summary>
        /// PasswordBox 交互命令
        /// </summary>
        [RelayCommand]
        private void HandlePasswordBoxInteraction(string parameter)
        {
            var message = parameter switch
            {
                "基础密码输入" => "🔒 输入了基础密码",
                "切换密码显示" => "👁️ 切换了密码显示状态",
                "密码强度检测" => "💪 检测了密码强度",
                "清除密码" => "🗑️ 清除了所有密码",
                _ => $"🔘 执行了操作: {parameter}"
            };

            StatusMessage = message;
            InteractionCount++;
        }

        /// <summary>
        /// 切换密码可见性命令
        /// </summary>
        [RelayCommand]
        private void TogglePasswordVisibility()
        {
            IsPasswordVisible = !IsPasswordVisible;
            HandlePasswordBoxInteraction("切换密码显示");
        }

        /// <summary>
        /// 清除所有密码命令
        /// </summary>
        [RelayCommand]
        private void ClearAllPasswords()
        {
            BasicPassword = string.Empty;
            ConfirmPassword = string.Empty;
            HandlePasswordBoxInteraction("清除密码");
        }

        /// <summary>
        /// 验证密码匹配
        /// </summary>
        /// <returns>密码是否匹配</returns>
        public bool ValidatePasswordMatch()
        {
            return !string.IsNullOrEmpty(BasicPassword) && 
                   BasicPassword == ConfirmPassword;
        }

        /// <summary>
        /// 计算密码强度
        /// </summary>
        /// <param name="password">密码</param>
        /// <returns>强度值 (0-100)</returns>
        private static double CalculatePasswordStrength(string password)
        {
            if (string.IsNullOrEmpty(password)) return 0;

            double strength = 0;

            // 长度评分 (最多40分)
            strength += Math.Min(password.Length * 4, 40);

            // 包含小写字母 (10分)
            if (Regex.IsMatch(password, @"[a-z]")) strength += 10;

            // 包含大写字母 (10分)
            if (Regex.IsMatch(password, @"[A-Z]")) strength += 10;

            // 包含数字 (10分)
            if (Regex.IsMatch(password, @"\d")) strength += 10;

            // 包含特殊字符 (15分)
            if (Regex.IsMatch(password, @"[!@#$%^&*(),.?""':;{}|<>]")) strength += 15;

            // 字符种类多样性 (5分)
            var uniqueChars = password.Distinct().Count();
            if (uniqueChars > password.Length * 0.7) strength += 5;

            return Math.Min(strength, 100);
        }

        /// <summary>
        /// 更新密码强度显示
        /// </summary>
        /// <param name="password">密码</param>
        private void UpdatePasswordStrength(string password)
        {
            if (string.IsNullOrEmpty(password))
            {
                PasswordStrength = 0;
                PasswordStrengthText = "请输入密码";
                PasswordStrengthColor = Brushes.Gray;
                return;
            }

            var strength = CalculatePasswordStrength(password);
            PasswordStrength = strength;

            (PasswordStrengthText, PasswordStrengthColor) = strength switch
            {
                < 25 => ("弱", Brushes.Red),
                < 50 => ("一般", Brushes.Orange),
                < 75 => ("良好", Brushes.Yellow),
                _ => ("强", Brushes.Green)
            };

            HandlePasswordBoxInteraction("密码强度检测");
        }

        /// <summary>
        /// 密码安全性检查
        /// </summary>
        /// <param name="password">密码</param>
        /// <returns>安全性检查结果</returns>
        public (bool IsValid, string Message) ValidatePasswordSecurity(string password)
        {
            if (string.IsNullOrEmpty(password))
                return (false, "密码不能为空");

            if (password.Length < 8)
                return (false, "密码长度至少8位");

            if (!Regex.IsMatch(password, @"[a-z]"))
                return (false, "密码必须包含小写字母");

            if (!Regex.IsMatch(password, @"[A-Z]"))
                return (false, "密码必须包含大写字母");

            if (!Regex.IsMatch(password, @"\d"))
                return (false, "密码必须包含数字");

            if (!Regex.IsMatch(password, @"[!@#$%^&*(),.?""':;{}|<>]"))
                return (false, "密码必须包含特殊字符");

            return (true, "密码符合安全要求");
        }
    }
}
