<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:controls="clr-namespace:Zylo.WPF.Controls.Icon">

    <!-- ZyloIcon 默认样式 -->
    <Style TargetType="{x:Type controls:ZyloIcon}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type controls:ZyloIcon}">
                    <TextBlock Text="{TemplateBinding Glyph}"
                               FontFamily="{TemplateBinding FontFamily}"
                               FontSize="{TemplateBinding FontSize}"
                               Foreground="{TemplateBinding Foreground}"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"
                               TextAlignment="Center" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        
        <!-- 默认属性 -->
        <Setter Property="Width" Value="20" />
        <Setter Property="Height" Value="20" />
        <Setter Property="FontSize" Value="20" />
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}" />
        <Setter Property="FontFamily" Value="/Zylo.WPF;component/Assets/Font/#iconfont" />
        
        <!-- 焦点样式 -->
        <Setter Property="Focusable" Value="False" />
        <Setter Property="IsTabStop" Value="False" />
    </Style>

</ResourceDictionary>
