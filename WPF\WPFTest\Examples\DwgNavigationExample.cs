using Prism.Regions;
using System;
using System.IO;

namespace WPFTest.Examples;

/// <summary>
/// DWG管理页面导航示例
/// </summary>
/// <remarks>
/// 演示如何使用导航参数来控制DwgManagerTabViewModel的初始状态
/// </remarks>
public class DwgNavigationExample
{
    private readonly IRegionManager _regionManager;

    public DwgNavigationExample(IRegionManager regionManager)
    {
        _regionManager = regionManager ?? throw new ArgumentNullException(nameof(regionManager));
    }

    /// <summary>
    /// 示例1: 基本导航（无参数）
    /// </summary>
    public void BasicNavigation()
    {
        // 简单导航到DWG管理页面，使用默认设置
        _regionManager.RequestNavigate("ContentRegion", "DwgManagerTabView");
    }

    /// <summary>
    /// 示例2: 指定DWG文件夹路径
    /// </summary>
    /// <param name="projectPath">项目路径</param>
    public void NavigateWithCustomPath(string projectPath)
    {
        var dwgPath = Path.Combine(projectPath, "DWG");
        
        if (!Directory.Exists(dwgPath))
        {
            throw new DirectoryNotFoundException($"DWG文件夹不存在: {dwgPath}");
        }

        var parameters = new NavigationParameters();
        parameters.Add("DwgFolderPath", dwgPath);

        _regionManager.RequestNavigate("ContentRegion", "DwgManagerTabView", parameters);
    }

    /// <summary>
    /// 示例3: 设置默认选择项
    /// </summary>
    /// <param name="dwgFolderPath">DWG文件夹路径</param>
    /// <param name="profession">专业名称</param>
    /// <param name="fileType">文件类型</param>
    public void NavigateWithDefaults(string dwgFolderPath, string profession, string fileType)
    {
        var parameters = new NavigationParameters();
        parameters.Add("DwgFolderPath", dwgFolderPath);
        parameters.Add("SelectedProfession", profession);
        parameters.Add("SelectedFileType", fileType);
        parameters.Add("AutoLoad", true);

        _regionManager.RequestNavigate("ContentRegion", "DwgManagerTabView", parameters);
    }

    /// <summary>
    /// 示例4: 强制创建新实例
    /// </summary>
    /// <param name="dwgFolderPath">DWG文件夹路径</param>
    public void NavigateWithNewInstance(string dwgFolderPath)
    {
        var parameters = new NavigationParameters();
        parameters.Add("DwgFolderPath", dwgFolderPath);
        parameters.Add("ForceNewInstance", true);

        _regionManager.RequestNavigate("ContentRegion", "DwgManagerTabView", parameters);
    }

    /// <summary>
    /// 示例5: 项目切换场景（带回调）
    /// </summary>
    /// <param name="projectInfo">项目信息</param>
    public void SwitchProject(ProjectInfo projectInfo)
    {
        var parameters = new NavigationParameters();
        parameters.Add("DwgFolderPath", projectInfo.DwgFolderPath);
        parameters.Add("SelectedProfession", projectInfo.DefaultProfession);
        parameters.Add("ForceNewInstance", true); // 确保清洁状态

        _regionManager.RequestNavigate("ContentRegion", "DwgManagerTabView",
            navigationResult =>
            {
                if (navigationResult.Result.HasValue && navigationResult.Result.Value)
                {
                    Console.WriteLine($"✅ 成功切换到项目: {projectInfo.Name}");
                }
                else
                {
                    Console.WriteLine($"❌ 项目切换失败: {navigationResult.Error?.Message}");
                }
            }, parameters);
    }

    /// <summary>
    /// 示例5B: 项目切换场景（简化版，无回调）
    /// </summary>
    /// <param name="projectInfo">项目信息</param>
    public void SwitchProjectSimple(ProjectInfo projectInfo)
    {
        var parameters = new NavigationParameters();
        parameters.Add("DwgFolderPath", projectInfo.DwgFolderPath);
        parameters.Add("SelectedProfession", projectInfo.DefaultProfession);
        parameters.Add("ForceNewInstance", true);

        _regionManager.RequestNavigate("ContentRegion", "DwgManagerTabView", parameters);
    }

    /// <summary>
    /// 示例6: 快速访问建筑专业
    /// </summary>
    public void QuickAccessArchitecture()
    {
        var parameters = new NavigationParameters();
        parameters.Add("SelectedProfession", "建筑");
        parameters.Add("SelectedFileType", "出图");
        parameters.Add("AutoLoad", true);

        _regionManager.RequestNavigate("ContentRegion", "DwgManagerTabView", parameters);
    }

    /// <summary>
    /// 示例7: 快速访问结构专业
    /// </summary>
    public void QuickAccessStructure()
    {
        var parameters = new NavigationParameters();
        parameters.Add("SelectedProfession", "结构");
        parameters.Add("SelectedFileType", "出图");
        parameters.Add("AutoLoad", true);

        _regionManager.RequestNavigate("ContentRegion", "DwgManagerTabView", parameters);
    }

    /// <summary>
    /// 示例8: 从搜索结果导航
    /// </summary>
    /// <param name="searchResult">搜索结果</param>
    public void NavigateFromSearchResult(SearchResult searchResult)
    {
        var parameters = new NavigationParameters();
        parameters.Add("DwgFolderPath", searchResult.ProjectPath);
        parameters.Add("SelectedProfession", searchResult.Profession);
        parameters.Add("SelectedFileType", searchResult.FileType);

        _regionManager.RequestNavigate("ContentRegion", "DwgManagerTabView", parameters);
    }
}

/// <summary>
/// 项目信息类
/// </summary>
public class ProjectInfo
{
    public string Name { get; set; } = string.Empty;
    public string DwgFolderPath { get; set; } = string.Empty;
    public string DefaultProfession { get; set; } = "建筑";
}

/// <summary>
/// 搜索结果类
/// </summary>
public class SearchResult
{
    public string ProjectPath { get; set; } = string.Empty;
    public string Profession { get; set; } = string.Empty;
    public string FileType { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
}
