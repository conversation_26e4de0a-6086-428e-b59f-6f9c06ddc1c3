using System;
using System.Globalization;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Media;
using Zylo.WPF.Helpers;

namespace Zylo.WPF.Converters
{
    /// <summary>
    /// 布尔值转选择模式转换器
    /// </summary>
    public class BoolToSelectionModeConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isMultiSelect)
            {
                return isMultiSelect ? SelectionMode.Extended : SelectionMode.Single;
            }
            return SelectionMode.Single;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is SelectionMode mode)
            {
                return mode == SelectionMode.Extended || mode == SelectionMode.Multiple;
            }
            return false;
        }
    }

    /// <summary>
    /// 布尔值转选择模式文本转换器
    /// </summary>
    public class BoolToSelectionModeTextConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isMultiSelect)
            {
                return isMultiSelect ? "多选模式" : "单选模式";
            }
            return "单选模式";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 布尔值转边框画刷转换器 - 使用主题资源
    /// </summary>
    public class BoolToBorderBrushConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isSelected)
            {
                return ThemeColorHelper.GetSelectionBorderBrush(isSelected);
            }
            return ThemeColorHelper.GetSelectionBorderBrush(false);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 布尔值转启用文本转换器
    /// </summary>
    public class BoolToEnabledTextConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isEnabled)
            {
                return isEnabled ? "启用" : "禁用";
            }
            return "禁用";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 布尔值转可见性转换器
    /// </summary>
    public class BoolToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? Visibility.Visible : Visibility.Collapsed;
            }
            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Visibility visibility)
            {
                return visibility == Visibility.Visible;
            }
            return false;
        }
    }



    /// <summary>
    /// 分类转颜色转换器 - 使用主题资源，更通用
    /// </summary>
    public class CategoryToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string category)
            {
                return ThemeColorHelper.GetCategoryBrush(category);
            }
            return ThemeColorHelper.GetCategoryBrush(string.Empty);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 通用字符串转主题颜色转换器
    /// </summary>
    public class StringToThemeColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string text)
            {
                return ThemeColorHelper.GetThemeColorFromString(text);
            }
            return ThemeColorHelper.GetThemeColorFromString(string.Empty);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 多值转换器：选择状态和模式转文本
    /// </summary>
    public class SelectionStatusMultiConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values.Length >= 3 &&
                values[0] is bool isMultiSelect &&
                values[1] is int selectedCount &&
                values[2] is string selectedItemName)
            {
                if (isMultiSelect)
                {
                    return $"多选模式：已选择 {selectedCount} 个项目";
                }
                else
                {
                    return string.IsNullOrEmpty(selectedItemName)
                        ? "单选模式：未选择项目"
                        : $"单选模式：选中 '{selectedItemName}'";
                }
            }
            return "未知状态";
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
