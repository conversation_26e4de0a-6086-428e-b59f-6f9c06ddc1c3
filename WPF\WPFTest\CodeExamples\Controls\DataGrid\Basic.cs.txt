// DataGrid C# 基础用法示例
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;

namespace WPFTest.ViewModels.List
{
    public partial class DataGridPageViewModel : ObservableObject
    {
        /// <summary>
        /// 员工数据集合
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<EmployeeData> employeeData = new();

        /// <summary>
        /// 选中的员工
        /// </summary>
        [ObservableProperty]
        private EmployeeData? selectedEmployee;

        /// <summary>
        /// DataGrid 交互命令
        /// </summary>
        [RelayCommand]
        private void HandleDataGridInteraction(string parameter)
        {
            var message = parameter switch
            {
                "选择员工" => $"🎯 选择了员工: {SelectedEmployee?.Name ?? "无"}",
                "排序数据" => "📊 数据已排序",
                "筛选数据" => "🔍 数据已筛选",
                _ => $"🔘 执行了操作: {parameter}"
            };

            StatusMessage = message;
            InteractionCount++;
        }

        /// <summary>
        /// 初始化员工数据
        /// </summary>
        private void InitializeEmployeeData()
        {
            var employees = new[]
            {
                new EmployeeData { Id = 1, Name = "张三", Department = "技术部", Position = "高级工程师", Salary = 12000, HireDate = new DateTime(2020, 3, 15) },
                new EmployeeData { Id = 2, Name = "李四", Department = "产品部", Position = "产品经理", Salary = 15000, HireDate = new DateTime(2019, 8, 22) },
                new EmployeeData { Id = 3, Name = "王五", Department = "设计部", Position = "UI设计师", Salary = 10000, HireDate = new DateTime(2021, 1, 10) }
            };

            EmployeeData = new ObservableCollection<EmployeeData>(employees);
        }
    }

    /// <summary>
    /// 员工数据模型
    /// </summary>
    public partial class EmployeeData : ObservableObject
    {
        [ObservableProperty]
        private int id;

        [ObservableProperty]
        private string name = string.Empty;

        [ObservableProperty]
        private string department = string.Empty;

        [ObservableProperty]
        private string position = string.Empty;

        [ObservableProperty]
        private decimal salary;

        [ObservableProperty]
        private DateTime hireDate;

        public override string ToString() => $"{Name} ({Department})";
    }
}
