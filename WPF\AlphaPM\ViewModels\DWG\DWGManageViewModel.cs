﻿using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace AlphaPM.ViewModels.DWG;

/// <summary>
/// DWG管理视图模型 - 三列布局设计
/// 左侧：TreeView 专业分类
/// 中间：ListBox 文件类型
/// 右侧：DataView 文件详情
/// </summary>
public partial class DWGManageViewModel : ObservableObject
{
    #region 属性

    /// <summary>
    /// TreeView 数据源 - 专业分类树
    /// </summary>
    [ObservableProperty]
    [Description("专业分类树形数据")]
    public partial ObservableCollection<TreeViewItemModel> TreeViewItems { get; set; } = new();

    /// <summary>
    /// 选中的树节点
    /// </summary>
    [ObservableProperty]
    [Description("选中的专业分类")]
    public partial TreeViewItemModel? SelectedTreeItem { get; set; }

    /// <summary>
    /// ListBox 数据源 - 文件类型列表
    /// </summary>
    [ObservableProperty]
    [Description("文件类型列表")]
    public partial ObservableCollection<FileTypeItemModel> FileTypeItems { get; set; } = new();

    /// <summary>
    /// 选中的文件类型
    /// </summary>
    [ObservableProperty]
    [Description("选中的文件类型")]
    public partial FileTypeItemModel? SelectedFileType { get; set; }

    /// <summary>
    /// DataGrid 数据源 - 文件详情列表
    /// </summary>
    [ObservableProperty]
    [Description("文件详情列表")]
    public partial ObservableCollection<FileItemModel> FileItems { get; set; } = new();

    /// <summary>
    /// 选中的文件
    /// </summary>
    [ObservableProperty]
    [Description("选中的文件")]
    public partial FileItemModel? SelectedFile { get; set; }

    /// <summary>
    /// 搜索关键词
    /// </summary>
    [ObservableProperty]
    [Description("搜索关键词")]
    public partial string SearchKeyword { get; set; } = string.Empty;

    /// <summary>
    /// 状态消息
    /// </summary>
    [ObservableProperty]
    [Description("状态消息")]
    public partial string StatusMessage { get; set; } = "就绪";

    /// <summary>
    /// 文件总数
    /// </summary>
    [ObservableProperty]
    [Description("文件总数")]
    public partial int TotalFiles { get; set; }

    /// <summary>
    /// 已选择文件数
    /// </summary>
    [ObservableProperty]
    [Description("已选择文件数")]
    public partial int SelectedFiles { get; set; }

    #endregion

    #region 命令

    /// <summary>
    /// 刷新命令
    /// </summary>
    [RelayCommand]
    private void Refresh()
    {
        LoadData();
        StatusMessage = "数据已刷新";
    }

    /// <summary>
    /// 新建文件命令
    /// </summary>
    [RelayCommand]
    private void CreateNew()
    {
        StatusMessage = "创建新文件功能待实现";
    }

    /// <summary>
    /// 打开文件命令
    /// </summary>
    [RelayCommand]
    private void OpenFile(FileItemModel? file)
    {
        if (file != null)
        {
            StatusMessage = $"打开文件: {file.FileName}";
        }
    }

    /// <summary>
    /// 删除文件命令
    /// </summary>
    [RelayCommand]
    private void DeleteFile(FileItemModel? file)
    {
        if (file != null)
        {
            FileItems.Remove(file);
            TotalFiles = FileItems.Count;
            StatusMessage = $"已删除文件: {file.FileName}";
        }
    }

    #endregion

    #region 构造函数

    public DWGManageViewModel()
    {
        LoadData();

        // 监听属性变化
        PropertyChanged += OnPropertyChanged;
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 加载数据
    /// </summary>
    private void LoadData()
    {
        LoadTreeViewData();
        LoadFileTypeData();
        LoadFileData();
    }

    /// <summary>
    /// 加载树形数据
    /// </summary>
    private void LoadTreeViewData()
    {
        TreeViewItems.Clear();

        // 示例数据
        var rootItem = new TreeViewItemModel
        {
            Name = "DWG项目",
            Children = new ObservableCollection<TreeViewItemModel>
            {
                new() { Name = "建筑专业", IsExpanded = true },
                new() { Name = "结构专业" },
                new() { Name = "机电专业" },
                new() { Name = "装饰专业" }
            }
        };

        TreeViewItems.Add(rootItem);
    }

    /// <summary>
    /// 加载文件类型数据
    /// </summary>
    private void LoadFileTypeData()
    {
        FileTypeItems.Clear();

        // 示例数据
        var fileTypes = new[]
        {
            "平面图", "立面图", "剖面图", "详图", "节点图", "大样图"
        };

        foreach (var type in fileTypes)
        {
            FileTypeItems.Add(new FileTypeItemModel { Name = type });
        }

        if (FileTypeItems.Count > 0)
        {
            SelectedFileType = FileTypeItems[0];
        }
    }

    /// <summary>
    /// 加载文件数据
    /// </summary>
    private void LoadFileData()
    {
        FileItems.Clear();

        // 示例数据
        var files = new[]
        {
            new FileItemModel { FileName = "A01-平面图.dwg", FileSize = "2.5MB", ModifiedTime = DateTime.Now.AddDays(-1) },
            new FileItemModel { FileName = "A02-立面图.dwg", FileSize = "1.8MB", ModifiedTime = DateTime.Now.AddDays(-2) },
            new FileItemModel { FileName = "A03-剖面图.dwg", FileSize = "3.2MB", ModifiedTime = DateTime.Now.AddDays(-3) },
            new FileItemModel { FileName = "A04-详图.dwg", FileSize = "1.2MB", ModifiedTime = DateTime.Now.AddDays(-4) }
        };

        foreach (var file in files)
        {
            FileItems.Add(file);
        }

        TotalFiles = FileItems.Count;
    }

    /// <summary>
    /// 属性变化处理
    /// </summary>
    private void OnPropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        switch (e.PropertyName)
        {
            case nameof(SelectedTreeItem):
                OnTreeItemChanged();
                break;
            case nameof(SelectedFileType):
                OnFileTypeChanged();
                break;
            case nameof(SearchKeyword):
                OnSearchKeywordChanged();
                break;
        }
    }

    /// <summary>
    /// 树节点选择变化
    /// </summary>
    private void OnTreeItemChanged()
    {
        if (SelectedTreeItem != null)
        {
            StatusMessage = $"选择专业: {SelectedTreeItem.Name}";
            // 根据选择的专业加载对应的文件类型和文件
        }
    }

    /// <summary>
    /// 文件类型选择变化
    /// </summary>
    private void OnFileTypeChanged()
    {
        if (SelectedFileType != null)
        {
            StatusMessage = $"选择类型: {SelectedFileType.Name}";
            // 根据选择的文件类型过滤文件列表
        }
    }

    /// <summary>
    /// 搜索关键词变化
    /// </summary>
    private void OnSearchKeywordChanged()
    {
        // 实现搜索过滤逻辑
        StatusMessage = string.IsNullOrEmpty(SearchKeyword) ? "就绪" : $"搜索: {SearchKeyword}";
    }

    #endregion
}

#region 数据模型

/// <summary>
/// 树形节点模型
/// </summary>
public partial class TreeViewItemModel : ObservableObject
{
    [ObservableProperty]
    public partial string Name { get; set; } = string.Empty;

    [ObservableProperty]
    public partial bool IsExpanded { get; set; }

    [ObservableProperty]
    public partial bool IsSelected { get; set; }

    public ObservableCollection<TreeViewItemModel> Children { get; set; } = new();
}

/// <summary>
/// 文件类型模型
/// </summary>
public partial class FileTypeItemModel : ObservableObject
{
    [ObservableProperty]
    public partial string Name { get; set; } = string.Empty;

    [ObservableProperty]
    public partial string Description { get; set; } = string.Empty;

    [ObservableProperty]
    public partial bool IsEnabled { get; set; } = true;
}

/// <summary>
/// 文件项模型
/// </summary>
public partial class FileItemModel : ObservableObject
{
    [ObservableProperty]
    public partial string FileName { get; set; } = string.Empty;

    [ObservableProperty]
    public partial string FileSize { get; set; } = string.Empty;

    [ObservableProperty]
    public partial DateTime ModifiedTime { get; set; }

    [ObservableProperty]
    public partial string FilePath { get; set; } = string.Empty;

    [ObservableProperty]
    public partial bool IsSelected { get; set; }
}

#endregion