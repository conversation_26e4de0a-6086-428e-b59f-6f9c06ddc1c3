# .NET Core / .NET 5+ / .NET 6+ / .NET 8+
bin/
obj/
out/

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Ww][Ii][Nn]32/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/
[Ll]ogs/

# Visual Studio 2015/2017/2019/2022 cache/options directory
.vs/

# Visual Studio 2017 auto generated files
Generated\ Files/

# MSTest test Results
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*

# NUnit

*.VisualState.xml
TestResult.xml
nunit-*.xml

# .NET Core
project.lock.json
project.fragment.lock.json
artifacts/

# Files built by Visual Studio
*_i.c
*_p.c
*_h.h
*.ilk
*.meta
*.obj
*.iobj
*.pch
*.pdb
*.ipdb
*.pgc
*.pgd
*.rsp
*.sbr
*.tlb
*.tli
*.tlh
*.tmp
*.tmp_proj
*_wpftmp.csproj
*.log
*.vspscc
*.vssscc
.builds
*.pidb
*.svclog
*.scc

# Visual C++ cache files
ipch/
*.aps
*.ncb
*.opendb
*.opensdf
*.sdf
*.cachefile
*.VC.db
*.VC.VC.opendb

# Visual Studio profiler
*.psess
*.vsp
*.vspx
*.sap

# ReSharper is a .NET coding add-in
_ReSharper*/
*.[Rr]e[Ss]harper
*.DotSettings.user

# Visual Studio cache files
*.[Cc]ache
!?*.[Cc]ache/

# Others
ClientBin/
~$*
*~
*.dbmdl
*.dbproj.schemaview
*.jfm
*.pfx
*.publishsettings
orleans.codegen.cs

# SQL Server files
*.mdf
*.ldf
*.ndf

# Node.js Tools for Visual Studio
.ntvs_analysis.dat
node_modules/

# JetBrains Rider
.idea/
*.sln.iml

# User-specific files (MonoDevelop/Xamarin Studio)
*.userprefs

# Zylo 项目特定忽略
*.DotSettings.user
packages/
TestResults/

# 临时文件
*.tmp
*.temp
*~

# 日志文件
*.log
logs/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 配置文件中的敏感信息
appsettings.local.json
appsettings.*.local.json

.fake