using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Windows;
using System.IO;
using System.Reflection;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Text;
using System.Text.Json;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels.List
{
    /// <summary>
    /// DataGrid 页面的 ViewModel，演示 DataGrid 控件的各种功能
    /// </summary>
    public partial class DataGridPageViewModel : ObservableObject
    {
        private readonly YLoggerInstance _logger = YLogger.For<DataGridPageViewModel>();

        #region 属性

        /// <summary>
        /// 交互次数
        /// </summary>
        [ObservableProperty]
        private int interactionCount = 0;

        /// <summary>
        /// 最后执行的操作
        /// </summary>
        [ObservableProperty]
        private string lastAction = "无操作";

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        private string statusMessage = "欢迎使用 DataGrid 示例库！";

        /// <summary>
        /// 员工数据集合
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<EmployeeData> employeeData = new();

        /// <summary>
        /// 选中的员工
        /// </summary>
        [ObservableProperty]
        private EmployeeData? selectedEmployee;

        /// <summary>
        /// 产品数据集合（用于高级功能展示）
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<ProductData> productData = new();

        /// <summary>
        /// 选中的产品
        /// </summary>
        [ObservableProperty]
        private ProductData? selectedProduct;

        /// <summary>
        /// 是否启用编辑模式
        /// </summary>
        [ObservableProperty]
        private bool isEditModeEnabled = false;

        /// <summary>
        /// 部门选项列表（用于ComboBox）
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<string> departmentOptions = new();

        /// <summary>
        /// 职位选项列表（用于ComboBox）
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<string> positionOptions = new();

        /// <summary>
        /// 员工状态选项列表
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<EmployeeStatus> statusOptions = new();

        /// <summary>
        /// 筛选文本
        /// </summary>
        [ObservableProperty]
        private string filterText = string.Empty;

        /// <summary>
        /// 筛选后的员工数据视图
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<EmployeeData> filteredEmployeeData = new();

        /// <summary>
        /// 选中的部门筛选
        /// </summary>
        [ObservableProperty]
        private string? selectedDepartmentFilter;

        /// <summary>
        /// 选中的状态筛选
        /// </summary>
        [ObservableProperty]
        private EmployeeStatus? selectedStatusFilter;

        #region 代码示例属性

        /// <summary>
        /// 基础功能 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 基础功能 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级功能 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级功能 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 样式 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string StylesXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 数据绑定 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string DataBindingXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 数据绑定 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string DataBindingCSharpExample { get; set; } = string.Empty;

        #endregion

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化 DataGridPageViewModel
        /// </summary>
        public DataGridPageViewModel()
        {
            try
            {
                _logger.Info("🚀 DataGrid 页面 ViewModel 开始初始化");

                StatusMessage = "DataGrid 示例库已加载，开始体验各种功能！";
                InitializeData();
                InitializeCodeExamples();

                PropertyChanged += OnPropertyChanged;

                _logger.Info("✅ DataGrid 页面 ViewModel 初始化完成");
                _logger.Info($"📊 初始状态 - 员工数据: {EmployeeData.Count}, 产品数据: {ProductData.Count}");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ DataGrid 页面 ViewModel 初始化失败: {ex.Message}");
                StatusMessage = "初始化失败，请检查日志";
            }
        }

        #endregion

        #region 命令

        /// <summary>
        /// 通用交互命令
        /// </summary>
        /// <param name="parameter">操作参数</param>
        [RelayCommand]
        private void HandleInteraction(string? parameter)
        {
            try
            {
                InteractionCount++;
                var action = parameter ?? "未知操作";
                LastAction = action;

                _logger.Info($"🎯 用户交互操作: {action}");
                _logger.Info($"🔢 当前交互次数: {InteractionCount}");

                // 根据不同的操作显示不同的状态消息
                StatusMessage = parameter switch
                {
                    "选择员工" => $"🎯 选择了员工: {SelectedEmployee?.Name ?? "无"}",
                    "选择产品" => $"🎯 选择了产品: {SelectedProduct?.Name ?? "无"}",
                    "编辑模式" => $"✏️ 编辑模式: {(IsEditModeEnabled ? "已启用" : "已禁用")}",
                    "筛选数据" => $"🔍 筛选条件: {FilterText}",
                    _ => $"🎯 执行了操作: {parameter}"
                };
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 处理用户交互失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 添加员工命令
        /// </summary>
        [RelayCommand]
        private void AddEmployee()
        {
            try
            {
                var newEmployee = new EmployeeData
                {
                    Id = EmployeeData.Count + 1,
                    Name = $"新员工 {EmployeeData.Count + 1}",
                    Department = DepartmentOptions.FirstOrDefault() ?? "技术部",
                    Position = PositionOptions.FirstOrDefault() ?? "工程师",
                    Salary = 8000,
                    HireDate = DateTime.Now,
                    Status = EmployeeStatus.Active,
                    IsFullTime = true,
                    WorkExperience = 0,
                    PerformanceRating = 3.0
                };

                EmployeeData.Insert(0, newEmployee);
                SelectedEmployee = newEmployee;

                // 自动更新筛选视图
                ApplyFilter();

                HandleInteraction("添加员工");
                StatusMessage = $"✅ 已添加新员工: {newEmployee.Name}";
                _logger.Info($"➕ 添加了新员工: {newEmployee.Name}");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 添加员工失败: {ex.Message}");
                StatusMessage = $"❌ 添加员工失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 删除选中员工命令
        /// </summary>
        [RelayCommand]
        private void DeleteSelectedEmployee()
        {
            try
            {
                if (SelectedEmployee != null)
                {
                    var employeeName = SelectedEmployee.Name;
                    EmployeeData.Remove(SelectedEmployee);
                    SelectedEmployee = null;

                    // 自动更新筛选视图
                    ApplyFilter();

                    HandleInteraction("删除员工");
                    StatusMessage = $"✅ 已删除员工: {employeeName}";
                    _logger.Info($"🗑️ 删除了员工: {employeeName}");
                }
                else
                {
                    StatusMessage = "⚠️ 请先选择要删除的员工";
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 删除员工失败: {ex.Message}");
                StatusMessage = $"❌ 删除员工失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 切换编辑模式命令
        /// </summary>
        [RelayCommand]
        private void ToggleEditMode()
        {
            IsEditModeEnabled = !IsEditModeEnabled;
            HandleInteraction("编辑模式");
        }

        /// <summary>
        /// 保存所有更改命令
        /// </summary>
        [RelayCommand]
        private void SaveAllChanges()
        {
            try
            {
                // 验证所有数据
                var invalidEmployees = EmployeeData.Where(e => !e.IsValid()).ToList();
                if (invalidEmployees.Any())
                {
                    StatusMessage = $"❌ 发现 {invalidEmployees.Count} 个员工数据无效，请检查后再保存";
                    return;
                }

                // 创建DATA文件夹
                var dataFolderPath = CreateDataFolder();

                // 保存为JSON文件
                var fileName = $"员工数据_{DateTime.Now:yyyyMMdd_HHmmss}.json";
                var filePath = Path.Combine(dataFolderPath, fileName);

                // 创建保存数据对象
                var saveData = new
                {
                    SaveInfo = new
                    {
                        Timestamp = DateTime.Now,
                        Version = "1.0",
                        TotalCount = EmployeeData.Count,
                        Application = "WPF DataGrid Demo"
                    },
                    Employees = EmployeeData.Select(emp => new
                    {
                        emp.Id,
                        emp.Name,
                        emp.Department,
                        emp.Position,
                        emp.Salary,
                        HireDate = emp.HireDate.ToString("yyyy-MM-dd"),
                        Status = emp.Status.ToString(),
                        emp.IsFullTime,
                        emp.WorkExperience,
                        emp.PerformanceRating
                    }).ToArray()
                };

                // 序列化为JSON并保存
                var jsonOptions = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };

                var jsonContent = JsonSerializer.Serialize(saveData, jsonOptions);
                File.WriteAllText(filePath, jsonContent, Encoding.UTF8);

                var savedCount = EmployeeData.Count;
                HandleInteraction("保存更改");
                StatusMessage = $"✅ 已保存 {savedCount} 条员工数据到 {fileName}";
                _logger.Info($"💾 保存了 {savedCount} 条员工数据到文件: {filePath}");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 保存更改失败: {ex.Message}");
                StatusMessage = $"❌ 保存失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 重置数据命令
        /// </summary>
        [RelayCommand]
        private void ResetData()
        {
            try
            {
                InitializeData();
                SelectedEmployee = null;
                HandleInteraction("重置数据");
                StatusMessage = "🔄 数据已重置为初始状态";
                _logger.Info("🔄 重置了所有数据");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 重置数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 导出数据命令
        /// </summary>
        [RelayCommand]
        private void ExportData()
        {
            try
            {
                // 导出当前筛选的数据
                var dataToExport = FilteredEmployeeData.Any() ? FilteredEmployeeData : EmployeeData;

                // 创建DATA文件夹
                var dataFolderPath = CreateDataFolder();

                // 同时导出JSON和CSV两种格式
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");

                // 导出JSON格式
                var jsonFileName = $"导出数据_{timestamp}.json";
                var jsonFilePath = Path.Combine(dataFolderPath, jsonFileName);
                var jsonContent = GenerateJsonContent(dataToExport);
                File.WriteAllText(jsonFilePath, jsonContent, Encoding.UTF8);

                // 导出CSV格式
                var csvFileName = $"导出数据_{timestamp}.csv";
                var csvFilePath = Path.Combine(dataFolderPath, csvFileName);
                var csvContent = GenerateCsvContent(dataToExport);
                File.WriteAllText(csvFilePath, csvContent, Encoding.UTF8);

                HandleInteraction("导出数据");
                StatusMessage = $"📤 已导出 {dataToExport.Count} 条员工数据 (JSON + CSV格式)";
                _logger.Info($"📤 导出了 {dataToExport.Count} 条员工数据到: {jsonFileName} 和 {csvFileName}");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 导出数据失败: {ex.Message}");
                StatusMessage = $"❌ 导出数据失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 创建DATA文件夹
        /// </summary>
        private string CreateDataFolder()
        {
            try
            {
                // 获取应用程序目录
                var appDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
                var dataFolderPath = Path.Combine(appDirectory ?? Environment.CurrentDirectory, "DATA");

                // 创建文件夹（如果不存在）
                if (!Directory.Exists(dataFolderPath))
                {
                    Directory.CreateDirectory(dataFolderPath);
                    _logger.Info($"📁 创建了DATA文件夹: {dataFolderPath}");
                }

                return dataFolderPath;
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 创建DATA文件夹失败: {ex.Message}");
                // 如果创建失败，使用当前目录
                return Environment.CurrentDirectory;
            }
        }

        /// <summary>
        /// 生成JSON内容
        /// </summary>
        private string GenerateJsonContent(IEnumerable<EmployeeData> employees)
        {
            try
            {
                var exportData = new
                {
                    ExportInfo = new
                    {
                        Timestamp = DateTime.Now,
                        ExportType = "Filtered Data Export",
                        TotalCount = employees.Count(),
                        Application = "WPF DataGrid Demo",
                        Version = "1.0"
                    },
                    Employees = employees.Select(emp => new
                    {
                        emp.Id,
                        emp.Name,
                        emp.Department,
                        emp.Position,
                        emp.Salary,
                        HireDate = emp.HireDate.ToString("yyyy-MM-dd"),
                        Status = emp.Status.ToString(),
                        emp.IsFullTime,
                        emp.WorkExperience,
                        emp.PerformanceRating
                    }).ToArray()
                };

                var jsonOptions = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };

                return JsonSerializer.Serialize(exportData, jsonOptions);
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 生成JSON内容失败: {ex.Message}");
                return "{}";
            }
        }

        /// <summary>
        /// 生成CSV内容
        /// </summary>
        private string GenerateCsvContent(IEnumerable<EmployeeData> employees)
        {
            var csv = new StringBuilder();
            csv.AppendLine("ID,姓名,部门,职位,薪资,入职日期,状态,是否全职,工作经验,绩效评分");

            foreach (var emp in employees)
            {
                csv.AppendLine($"{emp.Id},{emp.Name},{emp.Department},{emp.Position}," +
                              $"{emp.Salary},{emp.HireDate:yyyy-MM-dd},{emp.Status}," +
                              $"{emp.IsFullTime},{emp.WorkExperience},{emp.PerformanceRating}");
            }

            return csv.ToString();
        }

        /// <summary>
        /// 批量更新薪资命令
        /// </summary>
        [RelayCommand]
        private void BatchUpdateSalary()
        {
            try
            {
                foreach (var employee in EmployeeData)
                {
                    // 根据绩效评分调整薪资
                    var adjustmentFactor = employee.PerformanceRating / 5.0;
                    employee.Salary = employee.Salary * (decimal)(1 + adjustmentFactor * 0.1);
                }

                HandleInteraction("批量更新薪资");
                StatusMessage = "💰 已根据绩效评分批量调整薪资";
                _logger.Info("💰 批量更新了员工薪资");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 批量更新薪资失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用筛选命令
        /// </summary>
        [RelayCommand]
        private void ApplyFilter()
        {
            try
            {
                var filtered = EmployeeData.AsEnumerable();

                // 文本筛选
                if (!string.IsNullOrWhiteSpace(FilterText))
                {
                    filtered = filtered.Where(e =>
                        e.Name.Contains(FilterText, StringComparison.OrdinalIgnoreCase) ||
                        e.Department.Contains(FilterText, StringComparison.OrdinalIgnoreCase) ||
                        e.Position.Contains(FilterText, StringComparison.OrdinalIgnoreCase));
                }

                // 部门筛选
                if (!string.IsNullOrEmpty(SelectedDepartmentFilter))
                {
                    filtered = filtered.Where(e => e.Department == SelectedDepartmentFilter);
                }

                // 状态筛选
                if (SelectedStatusFilter.HasValue)
                {
                    filtered = filtered.Where(e => e.Status == SelectedStatusFilter.Value);
                }

                FilteredEmployeeData = new ObservableCollection<EmployeeData>(filtered);
                HandleInteraction("应用筛选");
                StatusMessage = $"🔍 筛选结果: {FilteredEmployeeData.Count} / {EmployeeData.Count} 条记录";
                _logger.Info($"🔍 应用筛选，显示 {FilteredEmployeeData.Count} 条记录");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 应用筛选失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清除筛选命令
        /// </summary>
        [RelayCommand]
        private void ClearFilter()
        {
            try
            {
                FilterText = string.Empty;
                SelectedDepartmentFilter = null;
                SelectedStatusFilter = null;
                FilteredEmployeeData = new ObservableCollection<EmployeeData>(EmployeeData);
                HandleInteraction("清除筛选");
                StatusMessage = $"🔄 已清除筛选，显示全部 {EmployeeData.Count} 条记录";
                _logger.Info("🔄 清除了所有筛选条件");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 清除筛选失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证所有数据命令
        /// </summary>
        [RelayCommand]
        private void ValidateAllData()
        {
            try
            {
                var invalidEmployees = EmployeeData.Where(e => !e.IsValid()).ToList();

                if (invalidEmployees.Count == 0)
                {
                    StatusMessage = "✅ 所有数据验证通过";
                    _logger.Info("✅ 所有员工数据验证通过");
                }
                else
                {
                    StatusMessage = $"❌ 发现 {invalidEmployees.Count} 条无效数据";
                    _logger.Warning($"❌ 发现 {invalidEmployees.Count} 条无效员工数据");
                }

                HandleInteraction("验证数据");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 验证数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 重置计数命令
        /// </summary>
        [RelayCommand]
        private void ResetCount()
        {
            InteractionCount = 0;
            StatusMessage = "计数已重置";
            _logger.Info("重置了交互计数");
        }

        /// <summary>
        /// 清除状态命令
        /// </summary>
        [RelayCommand]
        private void ClearStatus()
        {
            LastAction = "无操作";
            StatusMessage = "状态已清除";
            _logger.Info("清除了状态信息");
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            try
            {
                // 初始化选项数据
                DepartmentOptions = new ObservableCollection<string>
                {
                    "技术部", "产品部", "设计部", "市场部", "人事部", "财务部", "运营部"
                };

                PositionOptions = new ObservableCollection<string>
                {
                    "高级工程师", "产品经理", "UI设计师", "前端工程师", "后端工程师",
                    "市场专员", "HR专员", "财务专员", "运营专员", "项目经理", "架构师"
                };

                StatusOptions = new ObservableCollection<EmployeeStatus>
                {
                    EmployeeStatus.Active,
                    EmployeeStatus.Inactive,
                    EmployeeStatus.OnLeave,
                    EmployeeStatus.Terminated
                };

                // 初始化员工数据
                var employees = new[]
                {
                    new EmployeeData { Id = 1, Name = "张三", Department = "技术部", Position = "高级工程师", Salary = 12000, HireDate = new DateTime(2020, 3, 15), Status = EmployeeStatus.Active, IsFullTime = true, WorkExperience = 5, PerformanceRating = 4.5 },
                    new EmployeeData { Id = 2, Name = "李四", Department = "产品部", Position = "产品经理", Salary = 15000, HireDate = new DateTime(2019, 8, 22), Status = EmployeeStatus.Active, IsFullTime = true, WorkExperience = 7, PerformanceRating = 4.8 },
                    new EmployeeData { Id = 3, Name = "王五", Department = "设计部", Position = "UI设计师", Salary = 10000, HireDate = new DateTime(2021, 1, 10), Status = EmployeeStatus.Active, IsFullTime = false, WorkExperience = 3, PerformanceRating = 4.2 },
                    new EmployeeData { Id = 4, Name = "赵六", Department = "技术部", Position = "前端工程师", Salary = 11000, HireDate = new DateTime(2020, 11, 5), Status = EmployeeStatus.OnLeave, IsFullTime = true, WorkExperience = 4, PerformanceRating = 4.0 },
                    new EmployeeData { Id = 5, Name = "钱七", Department = "市场部", Position = "市场专员", Salary = 8000, HireDate = new DateTime(2022, 2, 18), Status = EmployeeStatus.Active, IsFullTime = true, WorkExperience = 2, PerformanceRating = 3.8 },
                    new EmployeeData { Id = 6, Name = "孙八", Department = "人事部", Position = "HR专员", Salary = 7500, HireDate = new DateTime(2021, 9, 12), Status = EmployeeStatus.Inactive, IsFullTime = false, WorkExperience = 3, PerformanceRating = 4.1 }
                };

                EmployeeData = new ObservableCollection<EmployeeData>(employees);
                FilteredEmployeeData = new ObservableCollection<EmployeeData>(employees);

                // 初始化产品数据
                var products = new[]
                {
                    new ProductData { Id = 1, Name = "笔记本电脑", Category = "电子产品", Price = 5999, Stock = 50, IsActive = true },
                    new ProductData { Id = 2, Name = "无线鼠标", Category = "电子产品", Price = 199, Stock = 200, IsActive = true },
                    new ProductData { Id = 3, Name = "机械键盘", Category = "电子产品", Price = 599, Stock = 80, IsActive = true },
                    new ProductData { Id = 4, Name = "显示器", Category = "电子产品", Price = 1299, Stock = 30, IsActive = false },
                    new ProductData { Id = 5, Name = "办公椅", Category = "办公用品", Price = 899, Stock = 25, IsActive = true }
                };

                ProductData = new ObservableCollection<ProductData>(products);

                _logger.Info($"📊 数据初始化完成 - 员工: {EmployeeData.Count}, 产品: {ProductData.Count}");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 数据初始化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 属性变化处理
        /// </summary>
        private void OnPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            switch (e.PropertyName)
            {
                case nameof(SelectedEmployee):
                    if (SelectedEmployee != null)
                        HandleInteraction("选择员工");
                    break;
                case nameof(SelectedProduct):
                    if (SelectedProduct != null)
                        HandleInteraction("选择产品");
                    break;
                case nameof(FilterText):
                case nameof(SelectedDepartmentFilter):
                case nameof(SelectedStatusFilter):
                    // 自动应用筛选
                    ApplyFilter();
                    break;
            }
        }

        /// <summary>
        /// 初始化代码示例
        /// </summary>
        private void InitializeCodeExamples()
        {
            try
            {
                // 获取代码示例文件的基础路径
                var baseDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
                var codeExamplesPath = Path.Combine(baseDirectory, "CodeExamples", "Controls", "DataGrid");

                _logger.Info($"基础目录: {baseDirectory}");
                _logger.Info($"代码示例路径: {codeExamplesPath}");

                // 读取各种示例文件
                BasicXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.xaml.txt"));
                BasicCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.cs.txt"));
                AdvancedXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Advanced.xaml.txt"));
                AdvancedCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Advanced.cs.txt"));
                StylesXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Styles.xaml.txt"));
                DataBindingXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "DataBinding.xaml.txt"));
                DataBindingCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "DataBinding.cs.txt"));

                _logger.Info("DataGrid 代码示例加载完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"加载 DataGrid 代码示例失败: {ex.Message}");
                SetDefaultCodeExamples();
            }
        }

        /// <summary>
        /// 读取代码示例文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件内容</returns>
        private string ReadCodeExampleFile(string filePath)
        {
            if (File.Exists(filePath))
            {
                var content = File.ReadAllText(filePath);
                _logger.Info($"成功读取文件: {Path.GetFileName(filePath)}, 长度: {content.Length}");
                return content;
            }
            else
            {
                _logger.Warning($"代码示例文件不存在: {filePath}");
                return $"// 代码示例文件不存在: {Path.GetFileName(filePath)}\n// 完整路径: {filePath}";
            }
        }

        /// <summary>
        /// 设置默认代码示例（当文件读取失败时使用）
        /// </summary>
        private void SetDefaultCodeExamples()
        {
            BasicXamlExample = "<!-- DataGrid 基础示例 -->\n<!-- 在这里添加基础用法示例 -->";
            BasicCSharpExample = "// DataGrid C# 基础示例\n// 在这里添加 C# 代码示例";
            AdvancedXamlExample = "<!-- DataGrid 高级示例 -->\n<!-- 在这里添加高级用法示例 -->";
            AdvancedCSharpExample = "// DataGrid C# 高级示例\n// 在这里添加高级 C# 代码示例";
            StylesXamlExample = "<!-- DataGrid 样式示例 -->\n<!-- 在这里添加样式使用示例 -->";
            DataBindingXamlExample = "<!-- DataGrid 数据绑定示例 -->\n<!-- 在这里添加数据绑定示例 -->";
            DataBindingCSharpExample = "// DataGrid 数据绑定 C# 示例\n// 在这里添加数据绑定 C# 代码示例";
        }

        #endregion
    }

    #region 数据模型

    /// <summary>
    /// 员工数据模型 - 支持数据验证和变更跟踪
    /// </summary>
    public partial class EmployeeData : ObservableObject, IDataErrorInfo
    {
        [ObservableProperty]
        [Description("员工ID")]
        public partial int Id { get; set; }

        [ObservableProperty]
        [Description("员工姓名")]
        public partial string Name { get; set; } = string.Empty;

        [ObservableProperty]
        [Description("部门")]
        public partial string Department { get; set; } = string.Empty;

        [ObservableProperty]
        [Description("职位")]
        public partial string Position { get; set; } = string.Empty;

        [ObservableProperty]
        [Description("薪资")]
        public partial decimal Salary { get; set; }

        [ObservableProperty]
        [Description("入职日期")]
        public partial DateTime HireDate { get; set; }

        [ObservableProperty]
        [Description("员工状态")]
        public partial EmployeeStatus Status { get; set; } = EmployeeStatus.Active;

        [ObservableProperty]
        [Description("是否全职")]
        public partial bool IsFullTime { get; set; } = true;

        [ObservableProperty]
        [Description("工作经验（年）")]
        public partial int WorkExperience { get; set; } = 0;

        [ObservableProperty]
        [Description("绩效评分")]
        public partial double PerformanceRating { get; set; } = 5.0;



        public override string ToString() => $"{Name} ({Department})";

        #region IDataErrorInfo 实现

        public string Error => string.Empty;

        public string this[string columnName]
        {
            get
            {
                return columnName switch
                {
                    nameof(Name) => ValidateName(),
                    nameof(Salary) => ValidateSalary(),
                    nameof(WorkExperience) => ValidateWorkExperience(),
                    nameof(PerformanceRating) => ValidatePerformanceRating(),
                    nameof(HireDate) => ValidateHireDate(),
                    _ => string.Empty
                };
            }
        }

        private string ValidateName()
        {
            if (string.IsNullOrWhiteSpace(Name))
                return "姓名不能为空";
            if (Name.Length < 2)
                return "姓名至少需要2个字符";
            if (Name.Length > 20)
                return "姓名不能超过20个字符";
            return string.Empty;
        }

        private string ValidateSalary()
        {
            if (Salary <= 0)
                return "薪资必须大于0";
            if (Salary > 1000000)
                return "薪资不能超过100万";
            return string.Empty;
        }

        private string ValidateWorkExperience()
        {
            if (WorkExperience < 0)
                return "工作经验不能为负数";
            if (WorkExperience > 50)
                return "工作经验不能超过50年";
            return string.Empty;
        }

        private string ValidatePerformanceRating()
        {
            if (PerformanceRating < 0)
                return "绩效评分不能为负数";
            if (PerformanceRating > 5)
                return "绩效评分不能超过5分";
            return string.Empty;
        }

        private string ValidateHireDate()
        {
            if (HireDate > DateTime.Now)
                return "入职日期不能是未来日期";
            if (HireDate < new DateTime(1950, 1, 1))
                return "入职日期不能早于1950年";
            return string.Empty;
        }

        #endregion

        /// <summary>
        /// 验证所有数据
        /// </summary>
        public bool IsValid()
        {
            var properties = new[] { nameof(Name), nameof(Salary), nameof(WorkExperience),
                                   nameof(PerformanceRating), nameof(HireDate) };
            return properties.All(prop => string.IsNullOrEmpty(this[prop]));
        }
    }

    /// <summary>
    /// 员工状态枚举
    /// </summary>
    public enum EmployeeStatus
    {
        Active = 0,
        Inactive = 1,
        OnLeave = 2,
        Terminated = 3
    }

    /// <summary>
    /// 产品数据模型
    /// </summary>
    public partial class ProductData : ObservableObject
    {
        [ObservableProperty]
        private int id;

        [ObservableProperty]
        private string name = string.Empty;

        [ObservableProperty]
        private string category = string.Empty;

        [ObservableProperty]
        private decimal price;

        [ObservableProperty]
        private int stock;

        [ObservableProperty]
        private bool isActive;

        public override string ToString() => $"{Name} ({Category})";
    }

    #endregion
}
