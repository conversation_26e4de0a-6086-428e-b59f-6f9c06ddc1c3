using WPFTest.Models.DWG;

namespace WPFTest.Services.DWG;

/// <summary>
/// DWG 文件夹服务接口 - 智能文件夹管理和路径扫描系统
/// </summary>
/// <remarks>
/// 🎯 设计目的：
/// - 提供DWG项目文件夹的完整管理功能
/// - 支持智能路径扫描和自动文件夹创建
/// - 支持专业文件夹模板的标准化管理
/// - 提供基础CRUD操作（增删改查）
/// - 使用YData进行数据持久化
///
/// 📋 核心接口方法：
///
/// 🔧 数据库管理：
/// - InitializeDatabaseAsync(): 初始化数据库和默认专业文件夹
///
/// 🔍 查询操作：
/// - GetAllFoldersAsync(): 获取所有文件夹（支持过滤禁用项）
/// - GetFolderByIdAsync(): 根据ID获取特定文件夹
/// - GetFolderByNameAsync(): 根据名称查找文件夹（用于重复检查）
///
/// ✏️ 增删改操作：
/// - AddFolderAsync(): 添加新文件夹到数据库
/// - UpdateFolderAsync(): 更新现有文件夹信息
/// - DeleteFolderAsync(): 删除指定文件夹
///
/// 🔍 智能扫描功能：
/// - ScanAndManageFoldersAsync(): 核心扫描方法，自动管理文件夹
/// - CreateDefaultFoldersAtPathAsync(): 在指定路径创建默认专业文件夹
/// - ScanExistingFoldersAsync(): 扫描现有文件夹并纳入管理
///
/// 📊 批量操作：
/// - UpdateSortOrdersAsync(): 批量更新文件夹排序权重
///
/// 🔄 事件通知：
/// - FolderChanged: 文件夹数据变更事件
/// - ScanCompleted: 扫描操作完成事件
///
/// 💡 使用场景：
/// 1. 项目初始化：自动创建标准专业文件夹结构
/// 2. 现有项目导入：扫描并管理现有文件夹
/// 3. 文件夹管理：增删改查文件夹信息
/// 4. 排序管理：调整文件夹显示顺序
///
/// 🎨 设计模式：
/// - 接口隔离：清晰的方法职责分离
/// - 异步优先：所有操作都采用异步模式
/// - 事件驱动：通过事件通知外部组件
/// - 依赖注入：支持IoC容器管理
/// </remarks>
public interface IDwgFolderService : IDisposable
{
    #region 数据库初始化

    /// <summary>
    /// 初始化数据库
    /// </summary>
    /// <returns>初始化是否成功</returns>
    /// <remarks>
    /// 功能：
    /// - 创建数据库表结构
    /// - 插入默认文件夹数据（如果数据库为空）
    /// - 确保服务可以正常工作
    /// </remarks>
    Task<bool> InitializeDatabaseAsync();

    #endregion

    #region 查询操作

    /// <summary>
    /// 获取所有文件夹
    /// </summary>
    /// <param name="includeDisabled">是否包含禁用的文件夹</param>
    /// <returns>文件夹列表，按排序权重和名称排序</returns>
    /// <remarks>
    /// 查询逻辑：
    /// - includeDisabled = false：只返回启用的文件夹
    /// - includeDisabled = true：返回所有文件夹（包括禁用的）
    /// - 结果按 SortOrder 升序，然后按 Name 升序排列
    /// </remarks>
    Task<List<DwgFolderModel>> GetAllFoldersAsync(bool includeDisabled = false);

    /// <summary>
    /// 根据ID获取文件夹
    /// </summary>
    /// <param name="id">文件夹ID</param>
    /// <returns>文件夹模型，如果不存在返回 null</returns>
    /// <remarks>
    /// 用途：
    /// - 编辑文件夹时获取详细信息
    /// - 删除前确认文件夹存在
    /// - 更新操作的前置检查
    /// </remarks>
    Task<DwgFolderModel?> GetFolderByIdAsync(int id);

    /// <summary>
    /// 根据名称获取文件夹
    /// </summary>
    /// <param name="name">文件夹名称</param>
    /// <returns>文件夹模型，如果不存在返回 null</returns>
    /// <remarks>
    /// 用途：
    /// - 检查文件夹名称是否重复
    /// - 按名称查找特定文件夹
    /// - 扫描时检查文件夹是否已存在
    /// </remarks>
    Task<DwgFolderModel?> GetFolderByNameAsync(string name);

    #endregion

    #region 增删改操作

    /// <summary>
    /// 添加文件夹
    /// </summary>
    /// <param name="folder">文件夹模型</param>
    /// <returns>添加是否成功</returns>
    /// <remarks>
    /// 添加逻辑：
    /// - 验证文件夹数据的有效性
    /// - 插入到数据库并获取自增ID
    /// - 触发 FolderChanged 事件
    /// - 记录操作日志
    /// </remarks>
    Task<bool> AddFolderAsync(DwgFolderModel folder);

    /// <summary>
    /// 更新文件夹
    /// </summary>
    /// <param name="folder">文件夹模型</param>
    /// <returns>更新是否成功</returns>
    /// <remarks>
    /// 更新逻辑：
    /// - 根据 ID 更新文件夹信息
    /// - 自动更新 UpdatedAt 时间戳
    /// - 触发 FolderChanged 事件
    /// - 记录操作日志
    /// </remarks>
    Task<bool> UpdateFolderAsync(DwgFolderModel folder);

    /// <summary>
    /// 删除文件夹
    /// </summary>
    /// <param name="id">文件夹ID</param>
    /// <returns>删除是否成功</returns>
    /// <remarks>
    /// 删除逻辑：
    /// - 先查询文件夹是否存在
    /// - 执行物理删除操作
    /// - 触发 FolderChanged 事件
    /// - 记录操作日志
    /// </remarks>
    Task<bool> DeleteFolderAsync(int id);

    /// <summary>
    /// 批量更新排序
    /// </summary>
    /// <param name="folders">文件夹列表</param>
    /// <returns>更新是否成功</returns>
    /// <remarks>
    /// 排序逻辑：
    /// - 按列表顺序设置 SortOrder（0, 1, 2...）
    /// - 批量更新所有文件夹的排序权重
    /// - 为每个文件夹触发 SortOrderChanged 事件
    /// - 支持拖拽排序功能
    /// </remarks>
    Task<bool> UpdateSortOrderAsync(List<DwgFolderModel> folders);

    #endregion

    #region 特殊功能 - 路径扫描和自动创建

    /// <summary>
    /// 扫描指定路径并自动管理文件夹（核心功能）
    /// </summary>
    /// <param name="basePath">基础路径</param>
    /// <returns>扫描结果，包含创建和发现的文件夹信息</returns>
    /// <remarks>
    /// 核心业务逻辑：
    /// 1. 检查指定路径是否存在
    /// 2. 调用 CreateDefaultFoldersAtPathAsync 创建默认文件夹
    /// 3. 调用 ScanExistingFoldersAsync 扫描现有文件夹
    /// 4. 返回完整的扫描结果
    /// 5. 触发 ScanCompleted 事件
    ///
    /// 使用场景：
    /// - 用户选择新的 DWG 工作目录
    /// - 项目初始化时自动设置文件夹结构
    /// - 导入现有项目时发现和管理文件夹
    /// </remarks>
    Task<DwgFolderScanResult> ScanAndManageFoldersAsync(string basePath);

    /// <summary>
    /// 在指定路径创建默认文件夹
    /// </summary>
    /// <param name="basePath">基础路径</param>
    /// <returns>成功创建的文件夹列表</returns>
    /// <remarks>
    /// 创建逻辑：
    /// 1. 获取默认文件夹模板（结构、建筑、暖通等）
    /// 2. 检查每个文件夹是否已存在于指定路径
    /// 3. 如果不存在，则创建物理文件夹
    /// 4. 将创建的文件夹添加到数据库管理
    /// 5. 记录创建日志
    ///
    /// 默认文件夹包括：
    /// - 🏗️ 结构
    /// - 🏢 建筑
    /// - 🌡️ 暖通
    /// - 💧 给排水
    /// - ⚡ 电气
    /// - 🌳 园林
    /// </remarks>
    Task<List<DwgFolderModel>> CreateDefaultFoldersAtPathAsync(string basePath);

    /// <summary>
    /// 扫描路径中的现有文件夹并添加到管理
    /// </summary>
    /// <param name="basePath">基础路径</param>
    /// <returns>发现并添加的文件夹列表</returns>
    /// <remarks>
    /// 扫描逻辑：
    /// 1. 获取指定路径下的所有子文件夹
    /// 2. 检查每个文件夹是否已在数据库中管理
    /// 3. 对于未管理的文件夹：
    ///    - 根据文件夹名称自动检测图标
    ///    - 创建 DwgFolderModel 并添加到数据库
    ///    - 记录发现日志
    ///
    /// 图标检测规则：
    /// - 包含"建筑"关键词 → 🏢
    /// - 包含"结构"关键词 → 🏗️
    /// - 包含"暖通"关键词 → 🌡️
    /// - 其他情况 → 📁
    /// </remarks>
    Task<List<DwgFolderModel>> ScanExistingFoldersAsync(string basePath);

    #endregion

    #region 事件通知

    /// <summary>
    /// 文件夹变更事件
    /// </summary>
    /// <remarks>
    /// 触发时机：
    /// - 添加文件夹时：ChangeType = Added
    /// - 更新文件夹时：ChangeType = Updated
    /// - 删除文件夹时：ChangeType = Deleted
    /// - 排序变更时：ChangeType = SortOrderChanged
    ///
    /// 用途：
    /// - UI 实时更新文件夹列表
    /// - 缓存失效通知
    /// - 审计日志记录
    /// </remarks>
    event EventHandler<DwgFolderChangedEventArgs>? FolderChanged;

    /// <summary>
    /// 扫描完成事件
    /// </summary>
    /// <remarks>
    /// 触发时机：
    /// - ScanAndManageFoldersAsync 完成时
    ///
    /// 事件数据：
    /// - 扫描的基础路径
    /// - 创建的文件夹数量
    /// - 发现的文件夹数量
    /// - 扫描是否成功
    /// - 错误信息（如果有）
    ///
    /// 用途：
    /// - UI 显示扫描结果
    /// - 进度条更新
    /// - 用户通知
    /// </remarks>
    event EventHandler<DwgFolderScanCompletedEventArgs>? ScanCompleted;

    #endregion
}

#region 数据传输对象

/// <summary>
/// 文件夹扫描结果
/// </summary>
/// <remarks>
/// 用途：封装 ScanAndManageFoldersAsync 方法的执行结果
/// 包含创建和发现的文件夹信息，以及操作状态
/// </remarks>
public class DwgFolderScanResult
{
    /// <summary>
    /// 扫描的基础路径
    /// </summary>
    /// <remarks>
    /// 用户提供的扫描目标路径
    /// 例如：D:\Projects\DWG
    /// </remarks>
    public string BasePath { get; set; } = string.Empty;

    /// <summary>
    /// 创建的默认文件夹
    /// </summary>
    /// <remarks>
    /// 在指定路径下新创建的默认文件夹列表
    /// 包括：结构、建筑、暖通、给排水、电气、园林等
    /// </remarks>
    public List<DwgFolderModel> CreatedFolders { get; set; } = new();

    /// <summary>
    /// 发现的现有文件夹
    /// </summary>
    /// <remarks>
    /// 在指定路径下发现的已存在文件夹列表
    /// 这些文件夹之前不在数据库管理中，现在被添加进来
    /// </remarks>
    public List<DwgFolderModel> DiscoveredFolders { get; set; } = new();

    /// <summary>
    /// 总共管理的文件夹数量
    /// </summary>
    /// <remarks>
    /// 计算属性：CreatedFolders.Count + DiscoveredFolders.Count
    /// 表示本次扫描操作总共纳入管理的文件夹数量
    /// </remarks>
    public int TotalManagedFolders => CreatedFolders.Count + DiscoveredFolders.Count;

    /// <summary>
    /// 是否成功
    /// </summary>
    /// <remarks>
    /// 扫描操作的整体状态
    /// - true：扫描成功完成
    /// - false：扫描过程中出现错误
    /// </remarks>
    public bool IsSuccess { get; set; } = true;

    /// <summary>
    /// 错误信息
    /// </summary>
    /// <remarks>
    /// 当 IsSuccess = false 时，包含具体的错误描述
    /// 例如：路径不存在、权限不足、数据库操作失败等
    /// </remarks>
    public string ErrorMessage { get; set; } = string.Empty;
}

/// <summary>
/// 文件夹变更事件参数
/// </summary>
/// <remarks>
/// 用途：当文件夹发生增删改操作时，通过事件传递变更信息
/// 订阅者可以根据变更类型执行相应的处理逻辑
/// </remarks>
public class DwgFolderChangedEventArgs : EventArgs
{
    /// <summary>
    /// 变更类型
    /// </summary>
    /// <remarks>
    /// 指示文件夹发生了什么类型的变更
    /// 用于订阅者判断如何处理这个事件
    /// </remarks>
    public DwgFolderChangeType ChangeType { get; set; }

    /// <summary>
    /// 发生变更的文件夹
    /// </summary>
    /// <remarks>
    /// 包含文件夹的完整信息
    /// 对于删除操作，这是删除前的文件夹信息
    /// </remarks>
    public DwgFolderModel Folder { get; set; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="changeType">变更类型</param>
    /// <param name="folder">文件夹模型</param>
    public DwgFolderChangedEventArgs(DwgFolderChangeType changeType, DwgFolderModel folder)
    {
        ChangeType = changeType;
        Folder = folder;
    }
}

/// <summary>
/// 扫描完成事件参数
/// </summary>
/// <remarks>
/// 用途：当路径扫描操作完成时，通过事件传递扫描结果
/// 包含创建和发现的文件夹信息，以及操作状态
/// </remarks>
public class DwgFolderScanCompletedEventArgs : EventArgs
{
    /// <summary>
    /// 扫描结果
    /// </summary>
    /// <remarks>
    /// 包含完整的扫描操作结果
    /// 包括创建的文件夹、发现的文件夹、成功状态等
    /// </remarks>
    public DwgFolderScanResult ScanResult { get; set; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="scanResult">扫描结果</param>
    public DwgFolderScanCompletedEventArgs(DwgFolderScanResult scanResult)
    {
        ScanResult = scanResult;
    }
}

#endregion

#region 枚举定义

/// <summary>
/// 文件夹变更类型枚举
/// </summary>
/// <remarks>
/// 定义文件夹可能发生的各种变更操作类型
/// 用于事件通知和日志记录
/// </remarks>
public enum DwgFolderChangeType
{
    /// <summary>
    /// 添加文件夹
    /// </summary>
    /// <remarks>
    /// 触发时机：调用 AddFolderAsync 成功时
    /// </remarks>
    Added,

    /// <summary>
    /// 更新文件夹
    /// </summary>
    /// <remarks>
    /// 触发时机：调用 UpdateFolderAsync 成功时
    /// </remarks>
    Updated,

    /// <summary>
    /// 删除文件夹
    /// </summary>
    /// <remarks>
    /// 触发时机：调用 DeleteFolderAsync 成功时
    /// </remarks>
    Deleted,

    /// <summary>
    /// 排序变更
    /// </summary>
    /// <remarks>
    /// 触发时机：调用 UpdateSortOrderAsync 成功时
    /// 每个文件夹都会触发一次此事件
    /// </remarks>
    SortOrderChanged
}

#endregion
