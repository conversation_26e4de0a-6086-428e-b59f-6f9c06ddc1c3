using System.Collections.ObjectModel;
using System.IO;
using System.Windows;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using GongSolutions.Wpf.DragDrop;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels.DragDrop
{
    /// <summary>
    /// 跨控件拖拽示例 ViewModel
    /// 展示 gong-wpf-dragdrop 库在不同控件间的拖拽应用
    /// 
    /// 核心功能：
    /// 1. ListView 与 TreeView 间的拖拽
    /// 2. DataGrid 与 ListBox 间的拖拽
    /// 3. 自定义控件间的拖拽
    /// 4. 不同数据类型间的转换
    /// 5. 复杂的拖拽验证规则
    /// </summary>
    public partial class CrossControlDragExampleViewModel : ObservableObject, IDragSource, IDropTarget
    {
        #region 字段

        private readonly YLoggerInstance _logger = YLogger.ForSilent<CrossControlDragExampleViewModel>();

        #endregion

        #region 属性

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        private string statusMessage = "欢迎使用跨控件拖拽示例！";

        /// <summary>
        /// 交互次数
        /// </summary>
        [ObservableProperty]
        private int interactionCount = 0;

        /// <summary>
        /// 最后操作
        /// </summary>
        [ObservableProperty]
        private string lastAction = "无";

        /// <summary>
        /// ListView 数据源
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<CrossDragItem> listViewItems = new();

        /// <summary>
        /// TreeView 数据源
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<CrossDragTreeItem> treeViewItems = new();

        /// <summary>
        /// DataGrid 数据源
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<CrossDragDataItem> dataGridItems = new();

        /// <summary>
        /// ListBox 数据源
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<CrossDragSimpleItem> listBoxItems = new();

        /// <summary>
        /// 回收站数据源
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<object> recycleItems = new();

        /// <summary>
        /// 选中的 ListView 项目
        /// </summary>
        [ObservableProperty]
        private CrossDragItem? selectedListViewItem;

        /// <summary>
        /// 选中的 DataGrid 项目
        /// </summary>
        [ObservableProperty]
        private CrossDragDataItem? selectedDataGridItem;

        #endregion

        #region 构造函数

        public CrossControlDragExampleViewModel()
        {
            _logger.Info("🔄 CrossControlDragExampleViewModel 初始化开始");
            
            InitializeData();
            
            _logger.Info("✅ CrossControlDragExampleViewModel 初始化完成");
        }

        #endregion

        #region 命令

        /// <summary>
        /// 重置数据命令
        /// </summary>
        [RelayCommand]
        private void ResetData()
        {
            InitializeData();
            InteractionCount++;
            LastAction = "重置数据";
            StatusMessage = "🔄 数据已重置";
            _logger.Info("重置跨控件拖拽数据");
        }

        /// <summary>
        /// 清空回收站命令
        /// </summary>
        [RelayCommand]
        private void ClearRecycle()
        {
            RecycleItems.Clear();
            InteractionCount++;
            LastAction = "清空回收站";
            StatusMessage = "🗑️ 回收站已清空";
            _logger.Info("清空回收站");
        }

        /// <summary>
        /// 添加 ListView 项目命令
        /// </summary>
        [RelayCommand]
        private void AddListViewItem()
        {
            var newItem = new CrossDragItem
            {
                Id = ListViewItems.Count + 1,
                Name = $"ListView 项目 {ListViewItems.Count + 1}",
                Type = "ListView",
                Description = "从 ListView 创建的项目",
                Priority = Random.Shared.Next(1, 6)
            };
            
            ListViewItems.Add(newItem);
            InteractionCount++;
            LastAction = "添加 ListView 项目";
            StatusMessage = $"➕ 添加了新的 ListView 项目: {newItem.Name}";
            _logger.Info($"添加 ListView 项目: {newItem.Name}");
        }

        /// <summary>
        /// 添加 DataGrid 项目命令
        /// </summary>
        [RelayCommand]
        private void AddDataGridItem()
        {
            var newItem = new CrossDragDataItem
            {
                Id = DataGridItems.Count + 1,
                Name = $"DataGrid 项目 {DataGridItems.Count + 1}",
                Category = "数据",
                Value = Random.Shared.Next(100, 1000),
                IsActive = true,
                CreatedDate = DateTime.Now
            };
            
            DataGridItems.Add(newItem);
            InteractionCount++;
            LastAction = "添加 DataGrid 项目";
            StatusMessage = $"➕ 添加了新的 DataGrid 项目: {newItem.Name}";
            _logger.Info($"添加 DataGrid 项目: {newItem.Name}");
        }

        #endregion

        #region IDragSource 实现

        public void StartDrag(IDragInfo dragInfo)
        {
            try
            {
                if (dragInfo.SourceItem != null)
                {
                    dragInfo.Data = dragInfo.SourceItem;
                    dragInfo.Effects = DragDropEffects.Move;
                    
                    InteractionCount++;
                    LastAction = $"开始跨控件拖拽: {GetItemName(dragInfo.SourceItem)}";
                    StatusMessage = $"🎯 开始拖拽: {GetItemName(dragInfo.SourceItem)}";
                    _logger.Info($"开始跨控件拖拽: {GetItemName(dragInfo.SourceItem)}");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"开始拖拽失败: {ex.Message}");
                StatusMessage = $"❌ 拖拽失败: {ex.Message}";
            }
        }

        public bool CanStartDrag(IDragInfo dragInfo)
        {
            return dragInfo.SourceItem != null;
        }

        public void Dropped(IDropInfo dropInfo)
        {
            try
            {
                if (dropInfo.Data != null)
                {
                    InteractionCount++;
                    LastAction = $"拖拽完成: {GetItemName(dropInfo.Data)}";
                    StatusMessage = $"✅ 拖拽完成: {GetItemName(dropInfo.Data)}";
                    _logger.Info($"跨控件拖拽完成: {GetItemName(dropInfo.Data)}");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"拖拽完成处理失败: {ex.Message}");
            }
        }

        public void DragDropOperationFinished(DragDropEffects operationResult, IDragInfo dragInfo)
        {
            try
            {
                if (dragInfo.Data != null)
                {
                    _logger.Info($"跨控件拖拽操作完成: {GetItemName(dragInfo.Data)}, 结果: {operationResult}");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"拖拽操作完成处理失败: {ex.Message}");
            }
        }

        public bool TryCatchOccurredException(Exception exception)
        {
            _logger.Error($"跨控件拖拽过程中发生异常: {exception.Message}");
            StatusMessage = $"❌ 拖拽异常: {exception.Message}";
            
            InteractionCount++;
            LastAction = "拖拽异常";
            
            return true;
        }

        public void DragCancelled()
        {
            InteractionCount++;
            LastAction = "拖拽被取消";
            StatusMessage = "⚠️ 拖拽操作被取消";
            _logger.Info("跨控件拖拽操作被取消");
        }

        #endregion

        #region IDropTarget 实现

        public void DragOver(IDropInfo dropInfo)
        {
            try
            {
                if (dropInfo.Data != null && IsValidCrossControlDrop(dropInfo))
                {
                    dropInfo.DropTargetAdorner = DropTargetAdorners.Highlight;
                    dropInfo.Effects = DragDropEffects.Move;
                }
                else
                {
                    dropInfo.Effects = DragDropEffects.None;
                }
            }
            catch (Exception ex)
            {
                dropInfo.Effects = DragDropEffects.None;
                _logger.Error($"跨控件拖拽悬停处理失败: {ex.Message}");
            }
        }

        public void Drop(IDropInfo dropInfo)
        {
            try
            {
                if (dropInfo.Data == null || !IsValidCrossControlDrop(dropInfo))
                {
                    StatusMessage = "⚠️ 放置失败：不支持的拖拽操作";
                    return;
                }

                var sourceData = dropInfo.Data;
                var targetCollection = dropInfo.TargetCollection;

                // 根据目标集合执行不同的转换和放置逻辑
                if (targetCollection == ListViewItems)
                {
                    HandleDropToListView(sourceData, dropInfo);
                }
                else if (targetCollection == DataGridItems)
                {
                    HandleDropToDataGrid(sourceData, dropInfo);
                }
                else if (targetCollection == ListBoxItems)
                {
                    HandleDropToListBox(sourceData, dropInfo);
                }
                else if (targetCollection == RecycleItems)
                {
                    HandleDropToRecycle(sourceData, dropInfo);
                }

                // 从源集合中移除
                RemoveFromSourceCollection(sourceData, dropInfo.DragInfo?.SourceCollection);

                InteractionCount++;
                LastAction = $"跨控件放置: {GetItemName(sourceData)}";
                StatusMessage = $"✅ 跨控件放置完成: {GetItemName(sourceData)}";
                _logger.Info($"跨控件放置完成: {GetItemName(sourceData)}");
            }
            catch (Exception ex)
            {
                _logger.Error($"跨控件放置操作失败: {ex.Message}");
                StatusMessage = $"❌ 放置失败: {ex.Message}";
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            // 清空现有数据
            ListViewItems.Clear();
            TreeViewItems.Clear();
            DataGridItems.Clear();
            ListBoxItems.Clear();
            RecycleItems.Clear();

            // 初始化 ListView 数据
            for (int i = 1; i <= 3; i++)
            {
                ListViewItems.Add(new CrossDragItem
                {
                    Id = i,
                    Name = $"ListView 项目 {i}",
                    Type = "ListView",
                    Description = $"这是第 {i} 个 ListView 项目",
                    Priority = i
                });
            }

            // 初始化 TreeView 数据
            var rootNode = new CrossDragTreeItem
            {
                Id = "ROOT",
                Name = "根节点",
                Type = "Folder",
                IsExpanded = true
            };

            for (int i = 1; i <= 2; i++)
            {
                rootNode.Children.Add(new CrossDragTreeItem
                {
                    Id = $"CHILD_{i}",
                    Name = $"子节点 {i}",
                    Type = "Item",
                    IsExpanded = false
                });
            }

            TreeViewItems.Add(rootNode);

            // 初始化 DataGrid 数据
            for (int i = 1; i <= 3; i++)
            {
                DataGridItems.Add(new CrossDragDataItem
                {
                    Id = i,
                    Name = $"数据项 {i}",
                    Category = i % 2 == 0 ? "偶数" : "奇数",
                    Value = i * 100,
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-i)
                });
            }

            // 初始化 ListBox 数据
            for (int i = 1; i <= 2; i++)
            {
                ListBoxItems.Add(new CrossDragSimpleItem
                {
                    Id = i,
                    Text = $"简单项目 {i}",
                    Color = i % 2 == 0 ? "蓝色" : "红色"
                });
            }

            _logger.Info("跨控件拖拽数据初始化完成");
        }

        /// <summary>
        /// 获取项目名称
        /// </summary>
        private string GetItemName(object item)
        {
            return item switch
            {
                CrossDragItem listItem => listItem.Name,
                CrossDragTreeItem treeItem => treeItem.Name,
                CrossDragDataItem dataItem => dataItem.Name,
                CrossDragSimpleItem simpleItem => simpleItem.Text,
                _ => item?.ToString() ?? "未知项目"
            };
        }

        /// <summary>
        /// 验证跨控件拖拽是否有效
        /// </summary>
        private bool IsValidCrossControlDrop(IDropInfo dropInfo)
        {
            // 基础验证
            if (dropInfo.Data == null || dropInfo.TargetCollection == null)
                return false;

            // 不允许拖拽到同一个集合
            if (dropInfo.DragInfo?.SourceCollection == dropInfo.TargetCollection)
                return false;

            // 回收站可以接受任何类型
            if (dropInfo.TargetCollection == RecycleItems)
                return true;

            // 其他验证规则可以在这里添加
            return true;
        }

        /// <summary>
        /// 处理放置到 ListView
        /// </summary>
        private void HandleDropToListView(object sourceData, IDropInfo dropInfo)
        {
            var newItem = ConvertToListViewItem(sourceData);
            if (newItem != null)
            {
                ListViewItems.Add(newItem);
            }
        }

        /// <summary>
        /// 处理放置到 DataGrid
        /// </summary>
        private void HandleDropToDataGrid(object sourceData, IDropInfo dropInfo)
        {
            var newItem = ConvertToDataGridItem(sourceData);
            if (newItem != null)
            {
                DataGridItems.Add(newItem);
            }
        }

        /// <summary>
        /// 处理放置到 ListBox
        /// </summary>
        private void HandleDropToListBox(object sourceData, IDropInfo dropInfo)
        {
            var newItem = ConvertToListBoxItem(sourceData);
            if (newItem != null)
            {
                ListBoxItems.Add(newItem);
            }
        }

        /// <summary>
        /// 处理放置到回收站
        /// </summary>
        private void HandleDropToRecycle(object sourceData, IDropInfo dropInfo)
        {
            RecycleItems.Add(sourceData);
        }

        /// <summary>
        /// 从源集合中移除项目
        /// </summary>
        private void RemoveFromSourceCollection(object item, object sourceCollection)
        {
            if (sourceCollection == ListViewItems && item is CrossDragItem listItem)
            {
                ListViewItems.Remove(listItem);
            }
            else if (sourceCollection == DataGridItems && item is CrossDragDataItem dataItem)
            {
                DataGridItems.Remove(dataItem);
            }
            else if (sourceCollection == ListBoxItems && item is CrossDragSimpleItem simpleItem)
            {
                ListBoxItems.Remove(simpleItem);
            }
        }

        /// <summary>
        /// 转换为 ListView 项目
        /// </summary>
        private CrossDragItem? ConvertToListViewItem(object sourceData)
        {
            return sourceData switch
            {
                CrossDragItem item => item,
                CrossDragDataItem dataItem => new CrossDragItem
                {
                    Id = dataItem.Id,
                    Name = dataItem.Name,
                    Type = "从DataGrid转换",
                    Description = $"转换自DataGrid: {dataItem.Category}",
                    Priority = (int)(dataItem.Value / 100)
                },
                CrossDragSimpleItem simpleItem => new CrossDragItem
                {
                    Id = simpleItem.Id,
                    Name = simpleItem.Text,
                    Type = "从ListBox转换",
                    Description = $"转换自ListBox: {simpleItem.Color}",
                    Priority = 1
                },
                _ => null
            };
        }

        /// <summary>
        /// 转换为 DataGrid 项目
        /// </summary>
        private CrossDragDataItem? ConvertToDataGridItem(object sourceData)
        {
            return sourceData switch
            {
                CrossDragDataItem item => item,
                CrossDragItem listItem => new CrossDragDataItem
                {
                    Id = listItem.Id,
                    Name = listItem.Name,
                    Category = listItem.Type,
                    Value = listItem.Priority * 100,
                    IsActive = true,
                    CreatedDate = DateTime.Now
                },
                CrossDragSimpleItem simpleItem => new CrossDragDataItem
                {
                    Id = simpleItem.Id,
                    Name = simpleItem.Text,
                    Category = simpleItem.Color,
                    Value = 100,
                    IsActive = true,
                    CreatedDate = DateTime.Now
                },
                _ => null
            };
        }

        /// <summary>
        /// 转换为 ListBox 项目
        /// </summary>
        private CrossDragSimpleItem? ConvertToListBoxItem(object sourceData)
        {
            return sourceData switch
            {
                CrossDragSimpleItem item => item,
                CrossDragItem listItem => new CrossDragSimpleItem
                {
                    Id = listItem.Id,
                    Text = listItem.Name,
                    Color = listItem.Type
                },
                CrossDragDataItem dataItem => new CrossDragSimpleItem
                {
                    Id = dataItem.Id,
                    Text = dataItem.Name,
                    Color = dataItem.Category
                },
                _ => null
            };
        }

        #endregion
    }

    /// <summary>
    /// 跨拖拽项目数据模型
    /// </summary>
    public partial class CrossDragItem : ObservableObject
    {
        [ObservableProperty]
        private int id;

        [ObservableProperty]
        private string name = string.Empty;

        [ObservableProperty]
        private string type = string.Empty;

        [ObservableProperty]
        private string description = string.Empty;

        [ObservableProperty]
        private int priority;

        public override string ToString() => $"{Name} ({Type})";
    }

    /// <summary>
    /// 跨拖拽树项目数据模型
    /// </summary>
    public partial class CrossDragTreeItem : ObservableObject
    {
        [ObservableProperty]
        private string id = string.Empty;

        [ObservableProperty]
        private string name = string.Empty;

        [ObservableProperty]
        private string type = string.Empty;

        [ObservableProperty]
        private bool isExpanded = false;

        [ObservableProperty]
        private ObservableCollection<CrossDragTreeItem> children = new();

        public override string ToString() => $"{Name} ({Type})";
    }

    /// <summary>
    /// 跨拖拽数据项目模型
    /// </summary>
    public partial class CrossDragDataItem : ObservableObject
    {
        [ObservableProperty]
        private int id;

        [ObservableProperty]
        private string name = string.Empty;

        [ObservableProperty]
        private string category = string.Empty;

        [ObservableProperty]
        private double value;

        [ObservableProperty]
        private bool isActive;

        [ObservableProperty]
        private DateTime createdDate;

        public override string ToString() => $"{Name} ({Category})";
    }

    /// <summary>
    /// 跨拖拽简单项目模型
    /// </summary>
    public partial class CrossDragSimpleItem : ObservableObject
    {
        [ObservableProperty]
        private int id;

        [ObservableProperty]
        private string text = string.Empty;

        [ObservableProperty]
        private string color = string.Empty;

        public override string ToString() => $"{Text} ({Color})";
    }
}
