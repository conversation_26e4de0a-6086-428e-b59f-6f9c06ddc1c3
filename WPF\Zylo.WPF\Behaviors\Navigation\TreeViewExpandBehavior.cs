using System.Collections;
using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Xaml.Behaviors;

namespace Zylo.WPF.Behaviors.Navigation
{
    /// <summary>
    /// TreeView展开/折叠同步Behavior
    /// 确保数据对象的IsExpanded属性与TreeViewItem的IsExpanded属性同步
    /// </summary>
    public class TreeViewExpandBehavior : Behavior<TreeView>
    {
        #region TreeData 依赖属性

        /// <summary>
        /// TreeData依赖属性
        /// </summary>
        public static readonly DependencyProperty TreeDataProperty =
            DependencyProperty.Register(
                nameof(TreeData),
                typeof(IEnumerable),
                typeof(TreeViewExpandBehavior),
                new PropertyMetadata(null, OnTreeDataChanged));

        /// <summary>
        /// 树形数据源
        /// </summary>
        public IEnumerable TreeData
        {
            get => (IEnumerable)GetValue(TreeDataProperty);
            set => SetValue(TreeDataProperty, value);
        }

        private static void OnTreeDataChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is TreeViewExpandBehavior behavior)
            {
                behavior.OnTreeDataChanged(e.OldValue as IEnumerable, e.NewValue as IEnumerable);
            }
        }

        #endregion

        #region Behavior重写

        /// <summary>
        /// 附加到TreeView时
        /// </summary>
        protected override void OnAttached()
        {
            base.OnAttached();
            
            if (AssociatedObject != null)
            {
                // 监听TreeView的Loaded事件
                AssociatedObject.Loaded += OnTreeViewLoaded;
                
                // 如果TreeData已经设置，立即同步
                if (TreeData != null)
                {
                    SyncExpandedStates();
                }
            }
        }

        /// <summary>
        /// 从TreeView分离时
        /// </summary>
        protected override void OnDetaching()
        {
            if (AssociatedObject != null)
            {
                AssociatedObject.Loaded -= OnTreeViewLoaded;
            }
            
            base.OnDetaching();
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// TreeView加载完成时
        /// </summary>
        private void OnTreeViewLoaded(object sender, RoutedEventArgs e)
        {
            SyncExpandedStates();
        }

        /// <summary>
        /// TreeData变化时
        /// </summary>
        private void OnTreeDataChanged(IEnumerable oldValue, IEnumerable newValue)
        {
            // 如果TreeView已经加载，立即同步
            if (AssociatedObject?.IsLoaded == true)
            {
                SyncExpandedStates();
            }
        }

        /// <summary>
        /// 同步展开状态
        /// </summary>
        private void SyncExpandedStates()
        {
            if (AssociatedObject == null || TreeData == null)
                return;

            // 延迟执行，确保TreeView已经完全渲染
            AssociatedObject.Dispatcher.BeginInvoke(new Action(() =>
            {
                SyncExpandedStatesRecursive(TreeData);
            }), System.Windows.Threading.DispatcherPriority.Loaded);
        }

        /// <summary>
        /// 递归同步展开状态
        /// </summary>
        private void SyncExpandedStatesRecursive(IEnumerable items)
        {
            if (items == null) return;

            foreach (var item in items)
            {
                // 查找对应的TreeViewItem
                var container = AssociatedObject.ItemContainerGenerator.ContainerFromItem(item) as TreeViewItem;
                if (container != null)
                {
                    // 同步展开状态
                    if (item is IExpandable expandableItem)
                    {
                        container.IsExpanded = expandableItem.IsExpanded;
                        
                        // 递归处理子项
                        if (expandableItem.Children != null)
                        {
                            SyncExpandedStatesRecursive(expandableItem.Children);
                        }
                    }
                }
            }
        }

        #endregion
    }

    /// <summary>
    /// 可展开接口
    /// </summary>
    public interface IExpandable
    {
        /// <summary>
        /// 是否展开
        /// </summary>
        bool IsExpanded { get; set; }
        
        /// <summary>
        /// 子项集合
        /// </summary>
        IEnumerable Children { get; }
    }
}
