<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml">

    <!-- 🎨 Zylo.WPF 按钮样式库 -->
    <!-- 提供现代化的按钮样式，支持圆形和矩形按钮 -->

    <!-- 圆形按钮样式 -->
    <Style x:Key="CircleButtonStyle" TargetType="ui:Button">
        <Setter Property="Background" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextOnAccentFillColorPrimaryBrush}"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Width" Value="48"/>
        <Setter Property="Height" Value="48"/>
        <Setter Property="CornerRadius" Value="24"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Padding" Value="0"/>
        <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="{DynamicResource SystemAccentColor}"
                                  BlurRadius="4"
                                  ShadowDepth="1"
                                  Opacity="0.3"/>
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ui:Button">
                    <Border x:Name="ButtonBorder"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{TemplateBinding CornerRadius}"
                            Width="{TemplateBinding Width}"
                            Height="{TemplateBinding Height}"
                            Effect="{TemplateBinding Effect}">
                        <ContentPresenter x:Name="ContentPresenter"
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          Margin="{TemplateBinding Padding}"
                                          Content="{TemplateBinding Content}"
                                          ContentTemplate="{TemplateBinding ContentTemplate}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="ButtonBorder" Property="Background" Value="{DynamicResource AccentFillColorSecondaryBrush}"/>
                            <Setter Property="Effect">
                                <Setter.Value>
                                    <DropShadowEffect Color="{DynamicResource SystemAccentColor}"
                                                      BlurRadius="6"
                                                      ShadowDepth="2"
                                                      Opacity="0.4"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="ButtonBorder" Property="Background" Value="{DynamicResource AccentFillColorTertiaryBrush}"/>
                            <Setter Property="Effect">
                                <Setter.Value>
                                    <DropShadowEffect Color="{DynamicResource SystemAccentColor}"
                                                      BlurRadius="2"
                                                      ShadowDepth="0"
                                                      Opacity="0.2"/>
                                </Setter.Value>
                            </Setter>
                            <Setter Property="RenderTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="0.95" ScaleY="0.95"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="ButtonBorder" Property="Background" Value="{DynamicResource ControlFillColorDisabledBrush}"/>
                            <Setter Property="Foreground" Value="{DynamicResource TextFillColorDisabledBrush}"/>
                            <Setter Property="Effect" Value="{x:Null}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 小型圆形按钮样式 -->
    <Style x:Key="SmallCircleButtonStyle" TargetType="ui:Button" BasedOn="{StaticResource CircleButtonStyle}">
        <Setter Property="Width" Value="36"/>
        <Setter Property="Height" Value="36"/>
        <Setter Property="CornerRadius" Value="18"/>
        <Setter Property="FontSize" Value="14"/>
    </Style>

    <!-- 大型圆形按钮样式 -->
    <Style x:Key="LargeCircleButtonStyle" TargetType="ui:Button" BasedOn="{StaticResource CircleButtonStyle}">
        <Setter Property="Width" Value="56"/>
        <Setter Property="Height" Value="56"/>
        <Setter Property="CornerRadius" Value="28"/>
        <Setter Property="FontSize" Value="18"/>
    </Style>

    <!-- 透明圆形按钮样式 (带边框) -->
    <Style x:Key="TransparentCircleButtonStyle" TargetType="ui:Button" BasedOn="{StaticResource CircleButtonStyle}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="Foreground" Value="{DynamicResource AccentTextFillColorPrimaryBrush}"/>
        <Setter Property="Effect" Value="{x:Null}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ui:Button">
                    <Border x:Name="ButtonBorder"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{TemplateBinding CornerRadius}"
                            Width="{TemplateBinding Width}"
                            Height="{TemplateBinding Height}">
                        <ContentPresenter x:Name="ContentPresenter"
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          Margin="{TemplateBinding Padding}"
                                          Content="{TemplateBinding Content}"
                                          ContentTemplate="{TemplateBinding ContentTemplate}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="ButtonBorder" Property="Background" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
                            <Setter Property="Foreground" Value="{DynamicResource TextOnAccentFillColorPrimaryBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="ButtonBorder" Property="Background" Value="{DynamicResource AccentFillColorSecondaryBrush}"/>
                            <Setter Property="Foreground" Value="{DynamicResource TextOnAccentFillColorPrimaryBrush}"/>
                            <Setter Property="RenderTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="0.95" ScaleY="0.95"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="ButtonBorder" Property="Background" Value="Transparent"/>
                            <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
                            <Setter Property="Foreground" Value="{DynamicResource TextFillColorDisabledBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 无边框透明圆形按钮样式 -->
    <Style x:Key="BorderlessCircleButtonStyle" TargetType="ui:Button" BasedOn="{StaticResource CircleButtonStyle}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="Effect" Value="{x:Null}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ui:Button">
                    <Border x:Name="ButtonBorder"
                            Background="{TemplateBinding Background}"
                            BorderThickness="0"
                            CornerRadius="{TemplateBinding CornerRadius}"
                            Width="{TemplateBinding Width}"
                            Height="{TemplateBinding Height}">
                        <ContentPresenter x:Name="ContentPresenter"
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          Margin="{TemplateBinding Padding}"
                                          Content="{TemplateBinding Content}"
                                          ContentTemplate="{TemplateBinding ContentTemplate}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="ButtonBorder" Property="Background" Value="{DynamicResource SubtleFillColorSecondaryBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="ButtonBorder" Property="Background" Value="{DynamicResource SubtleFillColorTertiaryBrush}"/>
                            <Setter Property="RenderTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="0.95" ScaleY="0.95"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Foreground" Value="{DynamicResource TextFillColorDisabledBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 现代化按钮样式 -->
    <Style x:Key="ModernButtonStyle" TargetType="ui:Button">
        <Setter Property="Background" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextOnAccentFillColorPrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="MinHeight" Value="36"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="{DynamicResource SystemAccentColor}"
                                  BlurRadius="8"
                                  ShadowDepth="2"
                                  Opacity="0.3"/>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{DynamicResource AccentFillColorSecondaryBrush}"/>
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="{DynamicResource AccentFillColorTertiaryBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 渐变按钮样式 -->
    <Style x:Key="GradientButtonStyle" TargetType="ui:Button">
        <Setter Property="Background">
            <Setter.Value>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#667eea" Offset="0"/>
                    <GradientStop Color="#764ba2" Offset="1"/>
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="CornerRadius" Value="12"/>
        <Setter Property="Padding" Value="20,10"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="MinHeight" Value="40"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background">
                    <Setter.Value>
                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                            <GradientStop Color="#764ba2" Offset="0"/>
                            <GradientStop Color="#667eea" Offset="1"/>
                        </LinearGradientBrush>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

</ResourceDictionary>
