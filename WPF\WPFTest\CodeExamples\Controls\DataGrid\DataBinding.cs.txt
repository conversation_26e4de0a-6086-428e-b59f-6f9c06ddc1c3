// DataGrid 数据绑定 C# 示例
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using System.ComponentModel;

namespace WPFTest.ViewModels.List
{
    /// <summary>
    /// DataGrid 数据绑定示例 ViewModel
    /// </summary>
    public partial class DataGridPageViewModel : ObservableObject
    {
        #region 数据绑定属性

        /// <summary>
        /// 员工数据集合 - 使用 ObservableCollection 支持动态更新
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<EmployeeData> employeeData = new();

        /// <summary>
        /// 产品数据集合 - 支持增删改操作
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<ProductData> productData = new();

        /// <summary>
        /// 选中的员工 - 双向绑定
        /// </summary>
        [ObservableProperty]
        private EmployeeData? selectedEmployee;

        /// <summary>
        /// 选中的产品 - 双向绑定
        /// </summary>
        [ObservableProperty]
        private ProductData? selectedProduct;

        #endregion

        #region 构造函数

        public DataGridPageViewModel()
        {
            // 初始化数据
            InitializeData();
            
            // 监听属性变化
            PropertyChanged += OnPropertyChanged;
        }

        #endregion

        #region 数据操作命令

        /// <summary>
        /// 添加员工命令
        /// </summary>
        [RelayCommand]
        private void AddEmployee()
        {
            var newEmployee = new EmployeeData
            {
                Id = EmployeeData.Count + 1,
                Name = $"新员工 {EmployeeData.Count + 1}",
                Department = "新部门",
                Position = "新职位",
                Salary = 5000,
                HireDate = DateTime.Now
            };

            // 添加到集合，界面会自动更新
            EmployeeData.Insert(0, newEmployee);
            SelectedEmployee = newEmployee;
        }

        /// <summary>
        /// 删除选中员工命令
        /// </summary>
        [RelayCommand]
        private void DeleteSelectedEmployee()
        {
            if (SelectedEmployee != null)
            {
                // 从集合中移除，界面会自动更新
                EmployeeData.Remove(SelectedEmployee);
                SelectedEmployee = null;
            }
        }

        /// <summary>
        /// 添加产品命令
        /// </summary>
        [RelayCommand]
        private void AddProduct()
        {
            var newProduct = new ProductData
            {
                Id = ProductData.Count + 1,
                Name = $"新产品 {ProductData.Count + 1}",
                Category = "新分类",
                Price = 100,
                Stock = 10,
                IsActive = true
            };

            ProductData.Insert(0, newProduct);
            SelectedProduct = newProduct;
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 属性变化处理 - 实现数据绑定的响应
        /// </summary>
        private void OnPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            switch (e.PropertyName)
            {
                case nameof(SelectedEmployee):
                    // 选中员工变化时的处理
                    if (SelectedEmployee != null)
                    {
                        StatusMessage = $"选中员工: {SelectedEmployee.Name}";
                        InteractionCount++;
                    }
                    break;

                case nameof(SelectedProduct):
                    // 选中产品变化时的处理
                    if (SelectedProduct != null)
                    {
                        StatusMessage = $"选中产品: {SelectedProduct.Name}";
                        InteractionCount++;
                    }
                    break;
            }
        }

        #endregion

        #region 数据初始化

        /// <summary>
        /// 初始化示例数据
        /// </summary>
        private void InitializeData()
        {
            // 初始化员工数据
            var employees = new[]
            {
                new EmployeeData { Id = 1, Name = "张三", Department = "技术部", Position = "高级工程师", Salary = 12000, HireDate = new DateTime(2020, 3, 15) },
                new EmployeeData { Id = 2, Name = "李四", Department = "产品部", Position = "产品经理", Salary = 15000, HireDate = new DateTime(2019, 8, 22) },
                new EmployeeData { Id = 3, Name = "王五", Department = "设计部", Position = "UI设计师", Salary = 10000, HireDate = new DateTime(2021, 1, 10) },
                new EmployeeData { Id = 4, Name = "赵六", Department = "技术部", Position = "前端工程师", Salary = 11000, HireDate = new DateTime(2020, 11, 5) },
                new EmployeeData { Id = 5, Name = "钱七", Department = "市场部", Position = "市场专员", Salary = 8000, HireDate = new DateTime(2022, 2, 18) }
            };

            EmployeeData = new ObservableCollection<EmployeeData>(employees);

            // 初始化产品数据
            var products = new[]
            {
                new ProductData { Id = 1, Name = "笔记本电脑", Category = "电子产品", Price = 5999, Stock = 50, IsActive = true },
                new ProductData { Id = 2, Name = "无线鼠标", Category = "电子产品", Price = 199, Stock = 200, IsActive = true },
                new ProductData { Id = 3, Name = "机械键盘", Category = "电子产品", Price = 599, Stock = 80, IsActive = true },
                new ProductData { Id = 4, Name = "显示器", Category = "电子产品", Price = 1299, Stock = 30, IsActive = false }
            };

            ProductData = new ObservableCollection<ProductData>(products);
        }

        #endregion
    }

    #region 数据模型

    /// <summary>
    /// 员工数据模型 - 支持属性变化通知
    /// </summary>
    public partial class EmployeeData : ObservableObject
    {
        [ObservableProperty]
        private int id;

        [ObservableProperty]
        private string name = string.Empty;

        [ObservableProperty]
        private string department = string.Empty;

        [ObservableProperty]
        private string position = string.Empty;

        [ObservableProperty]
        private decimal salary;

        [ObservableProperty]
        private DateTime hireDate;

        public override string ToString() => $"{Name} ({Department})";
    }

    /// <summary>
    /// 产品数据模型 - 支持属性变化通知
    /// </summary>
    public partial class ProductData : ObservableObject
    {
        [ObservableProperty]
        private int id;

        [ObservableProperty]
        private string name = string.Empty;

        [ObservableProperty]
        private string category = string.Empty;

        [ObservableProperty]
        private decimal price;

        [ObservableProperty]
        private int stock;

        [ObservableProperty]
        private bool isActive;

        public override string ToString() => $"{Name} ({Category})";
    }

    #endregion
}
