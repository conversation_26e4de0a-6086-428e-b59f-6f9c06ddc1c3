// ListView 数据绑定 C# 代码示例
// 展示各种数据绑定技术的实现

using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows.Data;

/// <summary>
/// 数据绑定示例 ViewModel
/// </summary>
public partial class DataBindingListViewViewModel : ObservableObject
{
    #region 基础绑定属性

    /// <summary>
    /// 简单项集合
    /// </summary>
    [ObservableProperty]
    private ObservableCollection<string> simpleItems = new();

    /// <summary>
    /// 复杂数据项集合
    /// </summary>
    [ObservableProperty]
    private ObservableCollection<DataItem> dataItems = new();

    /// <summary>
    /// 选择的简单项
    /// </summary>
    [ObservableProperty]
    private string? selectedSimpleItem;

    /// <summary>
    /// 选择的数据项
    /// </summary>
    [ObservableProperty]
    private DataItem? selectedDataItem;

    #endregion

    #region 多选绑定属性

    /// <summary>
    /// 是否启用多选
    /// </summary>
    [ObservableProperty]
    private bool isMultiSelectEnabled = false;

    /// <summary>
    /// 选中的项集合
    /// </summary>
    [ObservableProperty]
    private ObservableCollection<DataItem> selectedItems = new();

    #endregion

    #region 筛选和排序属性

    /// <summary>
    /// 筛选文本
    /// </summary>
    [ObservableProperty]
    private string filterText = string.Empty;

    /// <summary>
    /// 选择的分类
    /// </summary>
    [ObservableProperty]
    private string? selectedCategory;

    /// <summary>
    /// 分类列表
    /// </summary>
    [ObservableProperty]
    private ObservableCollection<string> categories = new();

    /// <summary>
    /// 筛选后的项集合
    /// </summary>
    public ICollectionView FilteredItems { get; private set; }

    #endregion

    #region 大数据集属性

    /// <summary>
    /// 大数据集（用于虚拟化演示）
    /// </summary>
    [ObservableProperty]
    private ObservableCollection<DataItem> largeDataSet = new();

    #endregion

    /// <summary>
    /// 构造函数
    /// </summary>
    public DataBindingListViewViewModel()
    {
        InitializeData();
        SetupFiltering();
        PropertyChanged += OnPropertyChanged;
    }

    /// <summary>
    /// 初始化数据
    /// </summary>
    private void InitializeData()
    {
        // 初始化简单项
        SimpleItems.Clear();
        for (int i = 1; i <= 10; i++)
        {
            SimpleItems.Add($"简单项 {i}");
        }

        // 初始化复杂数据项
        DataItems.Clear();
        var sampleData = new[]
        {
            new DataItem { Id = 1, Name = "文档管理", Category = "办公", IsEnabled = true },
            new DataItem { Id = 2, Name = "图片编辑", Category = "媒体", IsEnabled = true },
            new DataItem { Id = 3, Name = "音乐播放", Category = "媒体", IsEnabled = false },
            new DataItem { Id = 4, Name = "任务管理", Category = "效率", IsEnabled = true },
            new DataItem { Id = 5, Name = "日历安排", Category = "效率", IsEnabled = true },
            new DataItem { Id = 6, Name = "邮件处理", Category = "通讯", IsEnabled = true },
            new DataItem { Id = 7, Name = "联系人", Category = "通讯", IsEnabled = false },
            new DataItem { Id = 8, Name = "设置配置", Category = "系统", IsEnabled = true }
        };

        foreach (var item in sampleData)
        {
            DataItems.Add(item);
        }

        // 初始化分类
        Categories.Clear();
        Categories.Add("全部");
        var uniqueCategories = sampleData.Select(x => x.Category).Distinct();
        foreach (var category in uniqueCategories)
        {
            Categories.Add(category);
        }
        SelectedCategory = "全部";

        // 初始化大数据集（虚拟化演示）
        InitializeLargeDataSet();
    }

    /// <summary>
    /// 初始化大数据集
    /// </summary>
    private void InitializeLargeDataSet()
    {
        LargeDataSet.Clear();
        for (int i = 1; i <= 10000; i++)
        {
            LargeDataSet.Add(new DataItem
            {
                Id = i,
                Name = $"大数据项 {i}",
                Category = $"分类 {i % 10}",
                IsEnabled = i % 3 == 0,
                CreatedDate = DateTime.Now.AddDays(-i)
            });
        }
    }

    /// <summary>
    /// 设置筛选功能
    /// </summary>
    private void SetupFiltering()
    {
        FilteredItems = CollectionViewSource.GetDefaultView(DataItems);
        FilteredItems.Filter = FilterPredicate;
    }

    /// <summary>
    /// 筛选谓词
    /// </summary>
    private bool FilterPredicate(object obj)
    {
        if (obj is not DataItem item)
            return false;

        // 文本筛选
        bool textMatch = string.IsNullOrEmpty(FilterText) ||
                        item.Name.Contains(FilterText, StringComparison.OrdinalIgnoreCase);

        // 分类筛选
        bool categoryMatch = SelectedCategory == "全部" ||
                           item.Category == SelectedCategory;

        return textMatch && categoryMatch;
    }

    /// <summary>
    /// 排序命令
    /// </summary>
    [RelayCommand]
    private void Sort()
    {
        FilteredItems.SortDescriptions.Clear();
        FilteredItems.SortDescriptions.Add(new SortDescription("Name", ListSortDirection.Ascending));
    }

    /// <summary>
    /// 添加项命令
    /// </summary>
    [RelayCommand]
    private void AddItem()
    {
        var newItem = new DataItem
        {
            Id = DataItems.Count + 1,
            Name = $"新项目 {DataItems.Count + 1}",
            Category = Categories.Skip(1).FirstOrDefault() ?? "默认",
            IsEnabled = true,
            CreatedDate = DateTime.Now
        };

        DataItems.Add(newItem);
        FilteredItems.Refresh(); // 刷新筛选视图
    }

    /// <summary>
    /// 删除选中项命令
    /// </summary>
    [RelayCommand]
    private void RemoveSelectedItem()
    {
        if (SelectedDataItem != null)
        {
            DataItems.Remove(SelectedDataItem);
            SelectedItems.Remove(SelectedDataItem);
            SelectedDataItem = null;
            FilteredItems.Refresh();
        }
    }

    /// <summary>
    /// 切换多选模式命令
    /// </summary>
    [RelayCommand]
    private void ToggleMultiSelect()
    {
        IsMultiSelectEnabled = !IsMultiSelectEnabled;
        if (!IsMultiSelectEnabled)
        {
            // 清除所有选择状态
            foreach (var item in DataItems)
            {
                item.IsSelected = false;
            }
            SelectedItems.Clear();
        }
    }

    /// <summary>
    /// 属性变化处理
    /// </summary>
    private void OnPropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        switch (e.PropertyName)
        {
            case nameof(FilterText):
            case nameof(SelectedCategory):
                // 刷新筛选
                FilteredItems.Refresh();
                break;

            case nameof(SelectedDataItem):
                HandleDataItemSelection();
                break;

            case nameof(IsMultiSelectEnabled):
                HandleMultiSelectModeChange();
                break;
        }
    }

    /// <summary>
    /// 处理数据项选择
    /// </summary>
    private void HandleDataItemSelection()
    {
        if (SelectedDataItem != null)
        {
            Console.WriteLine($"选择了数据项: {SelectedDataItem.Name}");

            // 多选模式下添加到选中集合
            if (IsMultiSelectEnabled)
            {
                SelectedDataItem.IsSelected = true;
                if (!SelectedItems.Contains(SelectedDataItem))
                {
                    SelectedItems.Add(SelectedDataItem);
                }
            }
        }
    }

    /// <summary>
    /// 处理多选模式变化
    /// </summary>
    private void HandleMultiSelectModeChange()
    {
        Console.WriteLine($"多选模式: {(IsMultiSelectEnabled ? "启用" : "禁用")}");

        // 监听数据项的选择状态变化
        foreach (var item in DataItems)
        {
            item.PropertyChanged -= OnDataItemPropertyChanged;
            if (IsMultiSelectEnabled)
            {
                item.PropertyChanged += OnDataItemPropertyChanged;
            }
        }
    }

    /// <summary>
    /// 数据项属性变化处理
    /// </summary>
    private void OnDataItemPropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(DataItem.IsSelected) && sender is DataItem item)
        {
            if (item.IsSelected && !SelectedItems.Contains(item))
            {
                SelectedItems.Add(item);
            }
            else if (!item.IsSelected && SelectedItems.Contains(item))
            {
                SelectedItems.Remove(item);
            }
        }
    }
}

/// <summary>
/// 数据项模型（支持选择状态）
/// </summary>
public partial class DataItem : ObservableObject
{
    [ObservableProperty]
    private int id;

    [ObservableProperty]
    private string name = string.Empty;

    [ObservableProperty]
    private string category = string.Empty;

    [ObservableProperty]
    private bool isEnabled = true;

    [ObservableProperty]
    private bool isSelected = false;

    [ObservableProperty]
    private DateTime createdDate = DateTime.Now;

    [ObservableProperty]
    private string icon = "📄";
}

/*
数据绑定关键概念：

1. ObservableCollection: 自动通知集合变化
2. ICollectionView: 提供筛选、排序、分组功能
3. PropertyChanged 事件: 监听属性变化
4. 双向绑定: 确保 UI 和数据同步
5. 筛选谓词: 定义筛选逻辑
6. 排序描述符: 定义排序规则
7. 虚拟化: 优化大数据集性能
8. 转换器: 处理数据格式转换

最佳实践：
- 使用 ObservableCollection 作为数据源
- 实现 INotifyPropertyChanged 接口
- 合理使用 ICollectionView 进行数据操作
- 对大数据集启用虚拟化
- 使用命令模式处理用户交互
*/
