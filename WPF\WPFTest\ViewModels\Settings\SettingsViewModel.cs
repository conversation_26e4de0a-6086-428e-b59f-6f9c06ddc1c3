using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Prism.Regions;
using Wpf.Ui.Controls;
using Zylo.WPF.Models.Navigation;
using Zylo.WPF.YPrism;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels
{
    /// <summary>
    /// 设置页面视图模型
    /// 管理设置导航和内容区域的显示
    /// </summary>
    public partial class SettingsViewModel : ViewModelBase
    {
        public readonly YLoggerInstance _logger = YLogger.ForSilent<SettingsViewModel>();
        #region 私有字段

        private readonly IRegionManager _regionManager;

        #endregion

        #region 构造函数

        public SettingsViewModel(IRegionManager regionManager)
        {
            _regionManager = regionManager;
            InitializeSettingsNavigation();
            
            // 默认显示主题设置
            CurrentSettingTitle = "主题设置";
        }

        #endregion

        #region 属性

        /// <summary>
        /// 设置导航项集合
        /// </summary>
        [ObservableProperty]
        public partial ObservableCollection<ZyloNavigationItemModel> SettingsNavigationItems { get; set; } = new();

        /// <summary>
        /// 当前设置页面标题
        /// </summary>
        [ObservableProperty]
        public partial string CurrentSettingTitle { get; set; } = "设置";

        #endregion

        #region 命令

        /// <summary>
        /// 导航到设置页面命令
        /// </summary>
        [RelayCommand]
        private void NavigateToSetting(ZyloNavigationItemModel item)
        {
            if (item == null || string.IsNullOrEmpty(item.NavigationTarget))
            {
                _logger.Debug("导航项为空或没有导航目标");
                return;
            }

            try
            {
                // 更新当前页面标题
                CurrentSettingTitle = item.Name;
                
                // 导航到具体设置页面
                _regionManager.RequestNavigate(PrismManager.SettingsRegionName, item.NavigationTarget);
                
                _logger.Debug($"导航到设置页面: {item.Name} -> {item.NavigationTarget}");
            }
            catch (Exception ex)
            {
                _logger.Error($"导航到设置页面失败: {ex.Message}");
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化设置导航数据
        /// </summary>
        private void InitializeSettingsNavigation()
        {
            SettingsNavigationItems = new ObservableCollection<ZyloNavigationItemModel>
            {
                // 外观设置分组
                new ZyloNavigationItemModel
                {
                    Number = "1",
                    Name = "外观设置",
                    WpfUiSymbol= SymbolRegular.ColorBackground24,
                    IsExpanded = true,
                 
                    Children = new ObservableCollection<ZyloNavigationItemModel>
                    {
                        new ZyloNavigationItemModel
                        {
                            Number = "1.1",
                            ParentNumber = "1",
                            Name = "主题设置",
                            NavigationTarget = "NewThemeSettingsView",
                            WpfUiSymbol = SymbolRegular.DarkTheme24,
                          
                            
                        },
                        new ZyloNavigationItemModel
                        {
                            Number = "1.2",
                            ParentNumber = "1",
                            Name = "颜色设置",
                            NavigationTarget = "ColorSettingsView",
                            WpfUiSymbol = SymbolRegular.Color24
                        },
                        new ZyloNavigationItemModel
                        {
                            Number = "1.3",
                            ParentNumber = "1",
                            Name = "字体设置",
                            NavigationTarget = "FontSettingsView",
                            WpfUiSymbol = SymbolRegular.TextFont24
                        }
                    }
                },
                
                // 系统设置分组
                new ZyloNavigationItemModel
                {
                    Number = "2",
                    Name = "系统设置",
                    WpfUiSymbol = SymbolRegular.Settings24,
                    IsExpanded = true,
                    Children = new ObservableCollection<ZyloNavigationItemModel>
                    {
                        new ZyloNavigationItemModel
                        {
                            Number = "2.1",
                            ParentNumber = "2",
                            Name = "语言设置",
                            NavigationTarget = "LanguageSettingsView",
                            WpfUiSymbol =  SymbolRegular.LocalLanguage24
                        },
                        new ZyloNavigationItemModel
                        {
                            Number = "2.2",
                            ParentNumber = "2",
                            Name = "启动设置",
                            NavigationTarget = "StartupSettingsView",
                            WpfUiSymbol = SymbolRegular.Rocket24
                        },
                        new ZyloNavigationItemModel
                        {
                            Number = "2.3",
                            ParentNumber = "2",
                            Name = "更新设置",
                            NavigationTarget = "UpdateSettingsView",
                            WpfUiSymbol =  SymbolRegular.ArrowSync24
                        }
                    }
                },
                
                // 开发设置分组
                new ZyloNavigationItemModel
                {
                    Number = "3",
                    Name = "开发设置",
                    IsExpanded = true,
                    WpfUiSymbol =  SymbolRegular.DeveloperBoard24,
                    Children = new ObservableCollection<ZyloNavigationItemModel>
                    {
                        new ZyloNavigationItemModel
                        {
                            Number = "3.1",
                            ParentNumber = "3",
                            Name = "调试选项",
                            NavigationTarget = "DebugSettingsView",
                            WpfUiSymbol = SymbolRegular.Bug24
                        },
                        new ZyloNavigationItemModel
                        {
                            Number = "3.2",
                            ParentNumber = "3",
                            Name = "日志设置",
                            NavigationTarget = "LogSettingsView",
                            WpfUiSymbol =  SymbolRegular.DocumentText24
                        }
                    }
                }
            };
        }

        #endregion
    }
}
