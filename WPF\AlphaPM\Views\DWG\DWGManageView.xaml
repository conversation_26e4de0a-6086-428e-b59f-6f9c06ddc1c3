﻿<UserControl
    d:DataContext="{d:DesignInstance dwg:DWGManageViewModel}"
    d:DesignHeight="800"
    d:DesignWidth="1400"
    mc:Ignorable="d"
    mvvm:ViewModelLocator.AutoWireViewModel="True"
    x:Class="AlphaPM.Views.DWG.DWGManageView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dwg="clr-namespace:AlphaPM.ViewModels.DWG"
    xmlns:local="clr-namespace:AlphaPM.Views.DWG"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:mvvm="http://prismlibrary.com/"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <UserControl.Resources>
        <!-- 容器样式 -->
        <Style x:Key="PanelContainerStyle" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource CardBackgroundBrush}" />
            <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="CornerRadius" Value="8" />
            <Setter Property="Margin" Value="4" />
            <Setter Property="Padding" Value="8" />
        </Style>

        <!-- 标题样式 -->
        <Style x:Key="PanelTitleStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14" />
            <Setter Property="FontWeight" Value="Medium" />
            <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}" />
            <Setter Property="Margin" Value="0,0,0,8" />
        </Style>
    </UserControl.Resources>

    <!-- 三列布局：TreeView | ListBox | DataView -->
    <Grid Margin="8">
        <Grid.ColumnDefinitions>
            <!-- 左侧 TreeView 区域 -->
            <ColumnDefinition Width="250" />
            <ColumnDefinition Width="4" />

            <!-- 中间 ListBox 区域 -->
            <ColumnDefinition Width="200" />
            <ColumnDefinition Width="4" />

            <!-- 右侧 DataView 区域 -->
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>

        <!-- 左侧：TreeView 专业文件夹树 -->
        <Border Grid.Column="0" Style="{StaticResource PanelContainerStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <!-- 标题 -->
                <TextBlock Grid.Row="0"
                          Text="📁 专业分类"
                          Style="{StaticResource PanelTitleStyle}" />

                <!-- TreeView 内容 -->
                <TreeView Grid.Row="1"
                         Background="Transparent"
                         BorderThickness="0"
                         ItemsSource="{Binding TreeViewItems}">
                    <TreeView.ItemTemplate>
                        <HierarchicalDataTemplate ItemsSource="{Binding Children}">
                            <StackPanel Orientation="Horizontal" Margin="2">
                                <ui:SymbolIcon Symbol="Folder24"
                                              FontSize="16"
                                              Margin="0,0,6,0"
                                              Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}" />
                                <TextBlock Text="{Binding Name}"
                                          VerticalAlignment="Center"
                                          Foreground="{DynamicResource TextFillColorPrimaryBrush}" />
                            </StackPanel>
                        </HierarchicalDataTemplate>
                    </TreeView.ItemTemplate>
                </TreeView>
            </Grid>
        </Border>

        <!-- 分隔线 -->
        <GridSplitter Grid.Column="1"
                     Width="4"
                     Background="{DynamicResource ControlStrokeColorDefaultBrush}"
                     HorizontalAlignment="Stretch" />

        <!-- 中间：ListBox 文件类型列表 -->
        <Border Grid.Column="2" Style="{StaticResource PanelContainerStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <!-- 标题 -->
                <TextBlock Grid.Row="0"
                          Text="📋 文件类型"
                          Style="{StaticResource PanelTitleStyle}" />

                <!-- 搜索框 -->
                <ui:TextBox Grid.Row="1"
                           PlaceholderText="搜索类型..."
                           Text="{Binding SearchKeyword, UpdateSourceTrigger=PropertyChanged}"
                           Margin="0,0,0,8">
                    <ui:TextBox.Icon>
                        <ui:SymbolIcon Symbol="Search24" />
                    </ui:TextBox.Icon>
                </ui:TextBox>

                <!-- ListBox 内容 -->
                <ListBox Grid.Row="2"
                        Background="Transparent"
                        BorderThickness="0"
                        ItemsSource="{Binding FileTypeItems}"
                        SelectedItem="{Binding SelectedFileType}">
                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <Border Background="Transparent"
                                   Padding="8,6"
                                   CornerRadius="4">
                                <StackPanel Orientation="Horizontal">
                                    <ui:SymbolIcon Symbol="Document24"
                                                  FontSize="16"
                                                  Margin="0,0,8,0"
                                                  Foreground="{DynamicResource AccentTextFillColorSecondaryBrush}" />
                                    <TextBlock Text="{Binding Name}"
                                              VerticalAlignment="Center"
                                              Foreground="{DynamicResource TextFillColorPrimaryBrush}" />
                                </StackPanel>
                            </Border>
                        </DataTemplate>
                    </ListBox.ItemTemplate>
                    <ListBox.ItemContainerStyle>
                        <Style TargetType="ListBoxItem">
                            <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                            <Setter Property="Padding" Value="0" />
                            <Setter Property="Margin" Value="0,1" />
                            <Style.Triggers>
                                <Trigger Property="IsSelected" Value="True">
                                    <Setter Property="Background" Value="{DynamicResource AccentFillColorDefaultBrush}" />
                                </Trigger>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}" />
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </ListBox.ItemContainerStyle>
                </ListBox>
            </Grid>
        </Border>

        <!-- 分隔线 -->
        <GridSplitter Grid.Column="3"
                     Width="4"
                     Background="{DynamicResource ControlStrokeColorDefaultBrush}"
                     HorizontalAlignment="Stretch" />

        <!-- 右侧：DataView 文件详细信息 -->
        <Border Grid.Column="4" Style="{StaticResource PanelContainerStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <!-- 标题和工具栏 -->
                <Grid Grid.Row="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0"
                              Text="📊 文件详情"
                              Style="{StaticResource PanelTitleStyle}" />

                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <ui:Button Content="刷新"
                                  Icon="{ui:SymbolIcon Refresh24}"
                                  Appearance="Secondary"
                                  Command="{Binding RefreshCommand}"
                                  Margin="4,0" />
                        <ui:Button Content="新建"
                                  Icon="{ui:SymbolIcon Add24}"
                                  Appearance="Primary"
                                  Command="{Binding CreateNewCommand}"
                                  Margin="4,0" />
                    </StackPanel>
                </Grid>

                <!-- 统计信息 -->
                <Border Grid.Row="1"
                       Background="{DynamicResource ControlFillColorDefaultBrush}"
                       CornerRadius="4"
                       Padding="12,8"
                       Margin="0,0,0,8">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="📈 统计："
                                  FontWeight="Medium"
                                  Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                  Margin="0,0,8,0" />
                        <TextBlock Text="{Binding TotalFiles, StringFormat='文件总数: {0}'}"
                                  Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                  Margin="0,0,16,0" />
                        <TextBlock Text="{Binding SelectedFiles, StringFormat='已选择: {0}'}"
                                  Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}" />
                    </StackPanel>
                </Border>

                <!-- DataGrid 文件列表 -->
                <DataGrid Grid.Row="2"
                         AutoGenerateColumns="False"
                         Background="Transparent"
                         BorderThickness="0"
                         CanUserAddRows="False"
                         CanUserDeleteRows="False"
                         GridLinesVisibility="Horizontal"
                         HeadersVisibility="Column"
                         ItemsSource="{Binding FileItems}"
                         SelectedItem="{Binding SelectedFile}"
                         RowHeight="32">
                    <DataGrid.Columns>
                        <DataGridTemplateColumn Header="📄 文件名" Width="*">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" Margin="8,4">
                                        <ui:SymbolIcon Symbol="Document24"
                                                      FontSize="16"
                                                      Margin="0,0,8,0"
                                                      Foreground="{DynamicResource AccentTextFillColorSecondaryBrush}" />
                                        <TextBlock Text="{Binding FileName}"
                                                  VerticalAlignment="Center"
                                                  Foreground="{DynamicResource TextFillColorPrimaryBrush}" />
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTextColumn Header="📏 大小"
                                           Binding="{Binding FileSize}"
                                           Width="80" />

                        <DataGridTextColumn Header="📅 修改时间"
                                           Binding="{Binding ModifiedTime, StringFormat='yyyy-MM-dd HH:mm'}"
                                           Width="140" />

                        <DataGridTemplateColumn Header="🔧 操作" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <ui:Button Content="打开"
                                                  Appearance="Secondary"
                                                  FontSize="10"
                                                  Padding="8,4"
                                                  Command="{Binding DataContext.OpenFileCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                  CommandParameter="{Binding}"
                                                  Margin="2" />
                                        <ui:Button Content="删除"
                                                  Appearance="Danger"
                                                  FontSize="10"
                                                  Padding="8,4"
                                                  Command="{Binding DataContext.DeleteFileCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                  CommandParameter="{Binding}"
                                                  Margin="2" />
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- 状态栏 -->
                <Border Grid.Row="3"
                       Background="{DynamicResource ControlFillColorDefaultBrush}"
                       CornerRadius="4"
                       Padding="12,6"
                       Margin="0,8,0,0">
                    <TextBlock Text="{Binding StatusMessage}"
                              Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                              FontSize="12" />
                </Border>
            </Grid>
        </Border>
    </Grid>
</UserControl>