using System;
using System.Windows.Controls;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Wpf.Ui.Controls;

namespace WPFTest.Examples
{
    /// <summary>
    /// 基础 InfoBadge 控件示例
    /// 使用 CommunityToolkit.MVVM 8.4.0 的 Partial Properties 语法
    /// </summary>
    public partial class BasicInfoBadgeExample : UserControl
    {
        public BasicInfoBadgeExample()
        {
            InitializeComponent();
            DataContext = new BasicInfoBadgeViewModel();
        }
    }

    /// <summary>
    /// 基础 InfoBadge 示例 ViewModel
    /// </summary>
    public partial class BasicInfoBadgeViewModel : ObservableObject
    {
        #region InfoBadge 属性

        /// <summary>
        /// 通知计数
        /// </summary>
        [ObservableProperty]
        public partial int NotificationCount { get; set; } = 5;

        /// <summary>
        /// 邮件计数
        /// </summary>
        [ObservableProperty]
        public partial int MailCount { get; set; } = 12;

        /// <summary>
        /// 消息计数
        /// </summary>
        [ObservableProperty]
        public partial int MessageCount { get; set; } = 8;

        /// <summary>
        /// InfoBadge 数值
        /// </summary>
        [ObservableProperty]
        public partial int BadgeValue { get; set; } = 3;

        /// <summary>
        /// InfoBadge 背景色
        /// </summary>
        [ObservableProperty]
        public partial string BadgeBackground { get; set; } = "#FF0078D4";

        /// <summary>
        /// InfoBadge 前景色
        /// </summary>
        [ObservableProperty]
        public partial string BadgeForeground { get; set; } = "White";

        /// <summary>
        /// 是否显示 InfoBadge
        /// </summary>
        [ObservableProperty]
        public partial bool ShowInfoBadge { get; set; } = true;

        /// <summary>
        /// InfoBadge 宽度
        /// </summary>
        [ObservableProperty]
        public partial double BadgeWidth { get; set; } = 20;

        /// <summary>
        /// InfoBadge 高度
        /// </summary>
        [ObservableProperty]
        public partial double BadgeHeight { get; set; } = 20;

        /// <summary>
        /// 用户在线状态
        /// </summary>
        [ObservableProperty]
        public partial bool IsUserOnline { get; set; } = true;

        /// <summary>
        /// 用户状态背景色
        /// </summary>
        [ObservableProperty]
        public partial string UserStatusBackground { get; set; } = "Green";

        #endregion

        #region 构造函数

        public BasicInfoBadgeViewModel()
        {
            // 初始化默认值
            InitializeDefaults();
        }

        #endregion

        #region 命令

        /// <summary>
        /// 增加通知计数命令
        /// </summary>
        [RelayCommand]
        private void IncreaseNotificationCount()
        {
            NotificationCount++;
        }

        /// <summary>
        /// 减少通知计数命令
        /// </summary>
        [RelayCommand]
        private void DecreaseNotificationCount()
        {
            if (NotificationCount > 0)
            {
                NotificationCount--;
            }
        }

        /// <summary>
        /// 重置通知计数命令
        /// </summary>
        [RelayCommand]
        private void ResetNotificationCount()
        {
            NotificationCount = 0;
        }

        /// <summary>
        /// 增加邮件计数命令
        /// </summary>
        [RelayCommand]
        private void IncreaseMailCount()
        {
            MailCount++;
        }

        /// <summary>
        /// 减少邮件计数命令
        /// </summary>
        [RelayCommand]
        private void DecreaseMailCount()
        {
            if (MailCount > 0)
            {
                MailCount--;
            }
        }

        /// <summary>
        /// 重置邮件计数命令
        /// </summary>
        [RelayCommand]
        private void ResetMailCount()
        {
            MailCount = 0;
        }

        /// <summary>
        /// 增加消息计数命令
        /// </summary>
        [RelayCommand]
        private void IncreaseMessageCount()
        {
            MessageCount++;
        }

        /// <summary>
        /// 减少消息计数命令
        /// </summary>
        [RelayCommand]
        private void DecreaseMessageCount()
        {
            if (MessageCount > 0)
            {
                MessageCount--;
            }
        }

        /// <summary>
        /// 重置消息计数命令
        /// </summary>
        [RelayCommand]
        private void ResetMessageCount()
        {
            MessageCount = 0;
        }

        /// <summary>
        /// 更改 InfoBadge 背景色命令
        /// </summary>
        [RelayCommand]
        private void ChangeBadgeBackground()
        {
            var colors = new[]
            {
                "#FF0078D4", // 蓝色
                "#FF107C10", // 绿色
                "#FFFF4343", // 红色
                "#FFFF8C00", // 橙色
                "#FF744DA9", // 紫色
                "#FF00BCF2", // 青色
                "#FF767676"  // 灰色
            };
            
            var random = new Random();
            BadgeBackground = colors[random.Next(colors.Length)];
        }

        /// <summary>
        /// 切换 InfoBadge 显示命令
        /// </summary>
        [RelayCommand]
        private void ToggleInfoBadgeVisibility()
        {
            ShowInfoBadge = !ShowInfoBadge;
        }

        /// <summary>
        /// 调整 InfoBadge 大小命令
        /// </summary>
        [RelayCommand]
        private void ResizeInfoBadge()
        {
            var sizes = new[]
            {
                (16.0, 16.0), // 小
                (20.0, 20.0), // 中
                (24.0, 24.0), // 大
                (28.0, 28.0)  // 特大
            };
            
            var random = new Random();
            var size = sizes[random.Next(sizes.Length)];
            BadgeWidth = size.Item1;
            BadgeHeight = size.Item2;
        }

        /// <summary>
        /// 切换用户在线状态命令
        /// </summary>
        [RelayCommand]
        private void ToggleUserOnlineStatus()
        {
            IsUserOnline = !IsUserOnline;
            UserStatusBackground = IsUserOnline ? "Green" : "Gray";
        }

        /// <summary>
        /// 模拟新消息到达命令
        /// </summary>
        [RelayCommand]
        private void SimulateNewMessage()
        {
            var random = new Random();
            
            // 随机增加各种计数
            NotificationCount += random.Next(1, 4);
            MailCount += random.Next(0, 3);
            MessageCount += random.Next(0, 2);
            
            // 随机更改背景色
            ChangeBadgeBackground();
        }

        /// <summary>
        /// 清除所有计数命令
        /// </summary>
        [RelayCommand]
        private void ClearAllCounts()
        {
            NotificationCount = 0;
            MailCount = 0;
            MessageCount = 0;
            BadgeValue = 0;
        }

        /// <summary>
        /// 设置预设值命令
        /// </summary>
        [RelayCommand]
        private void SetPresetValues()
        {
            NotificationCount = 5;
            MailCount = 12;
            MessageCount = 8;
            BadgeValue = 3;
            BadgeBackground = "#FF0078D4";
            BadgeForeground = "White";
            ShowInfoBadge = true;
            BadgeWidth = 20;
            BadgeHeight = 20;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化默认值
        /// </summary>
        private void InitializeDefaults()
        {
            // 设置默认的 InfoBadge 属性
            BadgeBackground = "#FF0078D4";
            BadgeForeground = "White";
            BadgeWidth = 20;
            BadgeHeight = 20;
            ShowInfoBadge = true;
            
            // 设置默认计数
            NotificationCount = 5;
            MailCount = 12;
            MessageCount = 8;
            BadgeValue = 3;
            
            // 设置用户状态
            IsUserOnline = true;
            UserStatusBackground = "Green";
        }

        #endregion
    }
}
