<UserControl x:Class="WPFTest.Examples.BasicInfoBadgeExample"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml">

    <StackPanel>

        <!-- 基础 InfoBadge 示例 -->
        <GroupBox Header="基础 InfoBadge">
            <StackPanel>
                
                <!-- 点状 InfoBadge -->
                <StackPanel Margin="0,0,0,16">
                    <TextBlock Text="点状 InfoBadge:" FontWeight="Medium" Margin="0,0,0,8"/>
                    <StackPanel Orientation="Horizontal">
                        <ui:Button Content="消息" Margin="0,0,8,0">
                            <ui:InfoBadge Background="Red" Width="8" Height="8"/>
                        </ui:Button>
                        <ui:Button Content="通知" Margin="0,0,8,0">
                            <ui:InfoBadge Background="Orange" Width="10" Height="10"/>
                        </ui:Button>
                        <ui:Button Content="邮件">
                            <ui:InfoBadge Background="Blue" Width="6" Height="6"/>
                        </ui:Button>
                    </StackPanel>
                </StackPanel>

                <!-- 数字 InfoBadge -->
                <StackPanel Margin="0,0,0,16">
                    <TextBlock Text="数字 InfoBadge:" FontWeight="Medium" Margin="0,0,0,8"/>
                    <StackPanel Orientation="Horizontal">
                        <ui:Button Content="收件箱" Margin="0,0,8,0">
                            <ui:InfoBadge Value="{Binding NotificationCount}" 
                                         Background="Red" 
                                         Foreground="White"/>
                        </ui:Button>
                        <ui:Button Content="购物车" Margin="0,0,8,0">
                            <ui:InfoBadge Value="5" 
                                         Background="Orange" 
                                         Foreground="White"/>
                        </ui:Button>
                        <ui:Button Content="待办事项">
                            <ui:InfoBadge Value="12" 
                                         Background="Green" 
                                         Foreground="White"/>
                        </ui:Button>
                    </StackPanel>
                </StackPanel>

                <!-- 图标 InfoBadge -->
                <StackPanel Margin="0,0,0,16">
                    <TextBlock Text="图标 InfoBadge:" FontWeight="Medium" Margin="0,0,0,8"/>
                    <StackPanel Orientation="Horizontal">
                        <ui:Button Content="警告" Margin="0,0,8,0">
                            <ui:InfoBadge Background="Orange">
                                <ui:SymbolIcon Symbol="Warning24" FontSize="12" Foreground="White"/>
                            </ui:InfoBadge>
                        </ui:Button>
                        <ui:Button Content="完成" Margin="0,0,8,0">
                            <ui:InfoBadge Background="Green">
                                <ui:SymbolIcon Symbol="Checkmark24" FontSize="12" Foreground="White"/>
                            </ui:InfoBadge>
                        </ui:Button>
                        <ui:Button Content="错误">
                            <ui:InfoBadge Background="Red">
                                <ui:SymbolIcon Symbol="Dismiss24" FontSize="12" Foreground="White"/>
                            </ui:InfoBadge>
                        </ui:Button>
                    </StackPanel>
                </StackPanel>

            </StackPanel>
        </GroupBox>

        <!-- 不同大小的 InfoBadge -->
        <GroupBox Header="不同大小" Margin="0,16,0,0">
            <StackPanel>
                
                <!-- 小尺寸 -->
                <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                    <TextBlock Text="小尺寸:" FontWeight="Medium" Width="80" VerticalAlignment="Center"/>
                    <ui:Button Content="按钮" Margin="0,0,8,0">
                        <ui:InfoBadge Value="3" Background="Red" Width="16" Height="16" FontSize="10"/>
                    </ui:Button>
                    <ui:Button Content="图标">
                        <ui:InfoBadge Background="Blue" Width="12" Height="12"/>
                    </ui:Button>
                </StackPanel>

                <!-- 中等尺寸 -->
                <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                    <TextBlock Text="中等尺寸:" FontWeight="Medium" Width="80" VerticalAlignment="Center"/>
                    <ui:Button Content="按钮" Margin="0,0,8,0">
                        <ui:InfoBadge Value="15" Background="Orange" Width="20" Height="20" FontSize="12"/>
                    </ui:Button>
                    <ui:Button Content="图标">
                        <ui:InfoBadge Background="Green" Width="16" Height="16"/>
                    </ui:Button>
                </StackPanel>

                <!-- 大尺寸 -->
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="大尺寸:" FontWeight="Medium" Width="80" VerticalAlignment="Center"/>
                    <ui:Button Content="按钮" Margin="0,0,8,0">
                        <ui:InfoBadge Value="99+" Background="Purple" Width="28" Height="28" FontSize="14"/>
                    </ui:Button>
                    <ui:Button Content="图标">
                        <ui:InfoBadge Background="Teal" Width="20" Height="20"/>
                    </ui:Button>
                </StackPanel>

            </StackPanel>
        </GroupBox>

        <!-- 不同颜色的 InfoBadge -->
        <GroupBox Header="不同颜色" Margin="0,16,0,0">
            <StackPanel>
                
                <StackPanel Orientation="Horizontal">
                    <ui:Button Content="蓝色" Margin="0,0,8,0">
                        <ui:InfoBadge Value="1" Background="#FF0078D4" Foreground="White"/>
                    </ui:Button>
                    <ui:Button Content="绿色" Margin="0,0,8,0">
                        <ui:InfoBadge Value="2" Background="#FF107C10" Foreground="White"/>
                    </ui:Button>
                    <ui:Button Content="红色" Margin="0,0,8,0">
                        <ui:InfoBadge Value="3" Background="#FFFF4343" Foreground="White"/>
                    </ui:Button>
                    <ui:Button Content="橙色" Margin="0,0,8,0">
                        <ui:InfoBadge Value="4" Background="#FFFF8C00" Foreground="White"/>
                    </ui:Button>
                    <ui:Button Content="紫色" Margin="0,0,8,0">
                        <ui:InfoBadge Value="5" Background="#FF744DA9" Foreground="White"/>
                    </ui:Button>
                    <ui:Button Content="青色">
                        <ui:InfoBadge Value="6" Background="#FF00BCF2" Foreground="White"/>
                    </ui:Button>
                </StackPanel>

            </StackPanel>
        </GroupBox>

        <!-- 实际应用场景 -->
        <GroupBox Header="实际应用" Margin="0,16,0,0">
            <StackPanel>
                
                <!-- 导航菜单 -->
                <StackPanel Margin="0,0,0,16">
                    <TextBlock Text="导航菜单:" FontWeight="Medium" Margin="0,0,0,8"/>
                    <StackPanel Orientation="Horizontal">
                        <ui:Button Content="首页" Icon="{ui:SymbolIcon Home24}" Margin="0,0,8,0"/>
                        <ui:Button Content="消息" Icon="{ui:SymbolIcon Mail24}" Margin="0,0,8,0">
                            <ui:InfoBadge Value="{Binding MailCount}" Background="Red" Foreground="White"/>
                        </ui:Button>
                        <ui:Button Content="通知" Icon="{ui:SymbolIcon Alert24}" Margin="0,0,8,0">
                            <ui:InfoBadge Value="{Binding NotificationCount}" Background="Orange" Foreground="White"/>
                        </ui:Button>
                        <ui:Button Content="设置" Icon="{ui:SymbolIcon Settings24}"/>
                    </StackPanel>
                </StackPanel>

                <!-- 状态指示 -->
                <StackPanel Margin="0,0,0,16">
                    <TextBlock Text="状态指示:" FontWeight="Medium" Margin="0,0,0,8"/>
                    <StackPanel Orientation="Horizontal">
                        <ui:Button Content="在线用户" Margin="0,0,8,0">
                            <ui:InfoBadge Background="Green" Width="8" Height="8"/>
                        </ui:Button>
                        <ui:Button Content="离线用户" Margin="0,0,8,0">
                            <ui:InfoBadge Background="Gray" Width="8" Height="8"/>
                        </ui:Button>
                        <ui:Button Content="忙碌用户">
                            <ui:InfoBadge Background="Orange" Width="8" Height="8"/>
                        </ui:Button>
                    </StackPanel>
                </StackPanel>

                <!-- 功能标记 -->
                <StackPanel>
                    <TextBlock Text="功能标记:" FontWeight="Medium" Margin="0,0,0,8"/>
                    <StackPanel Orientation="Horizontal">
                        <ui:Button Content="新功能" Margin="0,0,8,0">
                            <ui:InfoBadge Background="Blue">
                                <TextBlock Text="NEW" FontSize="8" Foreground="White" FontWeight="Bold"/>
                            </ui:InfoBadge>
                        </ui:Button>
                        <ui:Button Content="热门" Margin="0,0,8,0">
                            <ui:InfoBadge Background="Red">
                                <TextBlock Text="HOT" FontSize="8" Foreground="White" FontWeight="Bold"/>
                            </ui:InfoBadge>
                        </ui:Button>
                        <ui:Button Content="推荐">
                            <ui:InfoBadge Background="Gold">
                                <ui:SymbolIcon Symbol="Star24" FontSize="10" Foreground="White"/>
                            </ui:InfoBadge>
                        </ui:Button>
                    </StackPanel>
                </StackPanel>

            </StackPanel>
        </GroupBox>

    </StackPanel>
</UserControl>
