using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Reflection;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels.InputControls;

/// <summary>
/// AutoSuggestBox 控件示例页面的 ViewModel
/// 使用 CommunityToolkit.MVVM 8.4.0 的 Partial Properties 语法
/// </summary>
public partial class AutoSuggestBoxPageViewModel : ObservableObject
{
    private readonly YLoggerInstance _logger = YLogger.For<PasswordBoxPageViewModel>();

    public AutoSuggestBoxPageViewModel()
    {
        InitializeData();
        LoadCodeExamples();

        // 监听属性变化
        PropertyChanged += OnPropertyChanged;

        _logger.Info("AutoSuggestBoxPageViewModel 初始化完成");
    }

    #region 状态属性

    /// <summary>
    /// 交互次数
    /// </summary>
    [ObservableProperty]
    public partial int InteractionCount { get; set; } = 0;

    /// <summary>
    /// 最后执行的操作
    /// </summary>
    [ObservableProperty]
    public partial string LastAction { get; set; } = "无操作";

    /// <summary>
    /// 状态消息
    /// </summary>
    [ObservableProperty]
    public partial string StatusMessage { get; set; } = "欢迎使用 AutoSuggestBox 控件示例！";

    #endregion

    #region 搜索属性

    /// <summary>
    /// 城市搜索文本
    /// </summary>
    [ObservableProperty]
    public partial string SearchText { get; set; } = string.Empty;

    /// <summary>
    /// 编程语言搜索文本
    /// </summary>
    [ObservableProperty]
    public partial string LanguageSearchText { get; set; } = string.Empty;

    /// <summary>
    /// 国家搜索文本
    /// </summary>
    [ObservableProperty]
    public partial string CountrySearchText { get; set; } = string.Empty;

    /// <summary>
    /// 产品搜索文本
    /// </summary>
    [ObservableProperty]
    public partial string ProductSearchText { get; set; } = string.Empty;

    /// <summary>
    /// 用户搜索文本
    /// </summary>
    [ObservableProperty]
    public partial string UserSearchText { get; set; } = string.Empty;

    #endregion

    #region 建议列表属性

    /// <summary>
    /// 城市建议列表
    /// </summary>
    [ObservableProperty]
    public partial ObservableCollection<string> CitySuggestions { get; set; } = new();

    /// <summary>
    /// 编程语言建议列表
    /// </summary>
    [ObservableProperty]
    public partial ObservableCollection<string> LanguageSuggestions { get; set; } = new();

    /// <summary>
    /// 国家建议列表
    /// </summary>
    [ObservableProperty]
    public partial ObservableCollection<string> CountrySuggestions { get; set; } = new();

    /// <summary>
    /// 产品建议列表
    /// </summary>
    [ObservableProperty]
    public partial ObservableCollection<string> ProductSuggestions { get; set; } = new();

    /// <summary>
    /// 用户建议列表
    /// </summary>
    [ObservableProperty]
    public partial ObservableCollection<string> UserSuggestions { get; set; } = new();

    #endregion

    #region 控件属性

    /// <summary>
    /// 占位符文本
    /// </summary>
    [ObservableProperty]
    public partial string PlaceholderText { get; set; } = "请输入搜索内容...";

    /// <summary>
    /// 建议列表是否打开
    /// </summary>
    [ObservableProperty]
    public partial bool IsSuggestionListOpen { get; set; } = false;

    /// <summary>
    /// 建议列表最大高度
    /// </summary>
    [ObservableProperty]
    public partial double MaxSuggestionListHeight { get; set; } = 200;

    /// <summary>
    /// 选择时是否更新文本
    /// </summary>
    [ObservableProperty]
    public partial bool UpdateTextOnSelect { get; set; } = true;

    /// <summary>
    /// 是否启用清除按钮
    /// </summary>
    [ObservableProperty]
    public partial bool ClearButtonEnabled { get; set; } = true;

    #endregion

    #region 数据源

    private readonly List<string> _allCities = new()
    {
        "北京", "上海", "广州", "深圳", "杭州", "南京", "苏州", "成都", "重庆", "武汉",
        "西安", "天津", "青岛", "大连", "厦门", "宁波", "无锡", "佛山", "东莞", "长沙"
    };

    private readonly List<string> _allLanguages = new()
    {
        "C#", "Java", "Python", "JavaScript", "TypeScript", "C++", "C", "Go", "Rust", "Swift",
        "Kotlin", "PHP", "Ruby", "Scala", "F#", "VB.NET", "Dart", "R", "MATLAB", "SQL"
    };

    private readonly List<string> _allCountries = new()
    {
        "中国", "美国", "日本", "德国", "英国", "法国", "意大利", "加拿大", "澳大利亚", "韩国",
        "俄罗斯", "印度", "巴西", "墨西哥", "西班牙", "荷兰", "瑞士", "瑞典", "挪威", "丹麦"
    };


    private readonly List<string> _allProducts = new()
    {
        "笔记本电脑", "台式电脑", "平板电脑", "智能手机", "智能手表", "耳机", "音响", "键盘", "鼠标", "显示器",
        "打印机", "扫描仪", "路由器", "交换机", "硬盘", "内存条", "显卡", "主板", "电源", "机箱"
    };

    private readonly List<string> _allUsers = new()
    {
        "张三", "李四", "王五", "赵六", "钱七", "孙八", "周九", "吴十", "郑十一", "王十二",
        "冯十三", "陈十四", "褚十五", "卫十六", "蒋十七", "沈十八", "韩十九", "杨二十", "朱二十一", "秦二十二"
    };

    #endregion

    #region 代码示例属性

    /// <summary>
    /// 基础功能 XAML 示例
    /// </summary>
    [ObservableProperty]
    public partial string BasicAutoSuggestBoxXaml { get; set; } = string.Empty;

    /// <summary>
    /// 基础功能 C# 示例
    /// </summary>
    [ObservableProperty]
    public partial string BasicAutoSuggestBoxCs { get; set; } = string.Empty;

    /// <summary>
    /// 高级功能 XAML 示例
    /// </summary>
    [ObservableProperty]
    public partial string AdvancedAutoSuggestBoxXaml { get; set; } = string.Empty;

    /// <summary>
    /// 高级功能 C# 示例
    /// </summary>
    [ObservableProperty]
    public partial string AdvancedAutoSuggestBoxCs { get; set; } = string.Empty;

    /// <summary>
    /// 总结果数量
    /// </summary>
    public int TotalResultsCount => CitySuggestions.Count + LanguageSuggestions.Count + CountrySuggestions.Count +
                                    ProductSuggestions.Count + UserSuggestions.Count;

    #endregion


    #region 初始化方法

    private void InitializeData()
    {
        try
        {
            // 初始化建议列表为空
            CitySuggestions.Clear();
            LanguageSuggestions.Clear();
            CountrySuggestions.Clear();
            ProductSuggestions.Clear();
            UserSuggestions.Clear();

            // 不透露具体的搜索内容，让用户自己探索
            StatusMessage = "请输入关键词开始搜索...";
            _logger.Info("AutoSuggestBox 数据初始化完成");
        }
        catch (Exception ex)
        {
            StatusMessage = $"数据初始化失败: {ex.Message}";
            _logger.Error("AutoSuggestBox 数据初始化失败", ex);
        }
    }

    private void LoadCodeExamples()
    {
        try
        {
            var assembly = Assembly.GetExecutingAssembly();

            // 加载基础示例
            using var basicXamlStream =
                assembly.GetManifestResourceStream(
                    "WPFTest.CodeExamples.Controls.AutoSuggestBox.BasicAutoSuggestBox.xaml.txt");
            if (basicXamlStream != null)
            {
                using var reader = new StreamReader(basicXamlStream);
                BasicAutoSuggestBoxXaml = reader.ReadToEnd();
            }

            using var basicCsStream =
                assembly.GetManifestResourceStream(
                    "WPFTest.CodeExamples.Controls.AutoSuggestBox.BasicAutoSuggestBox.cs.txt");
            if (basicCsStream != null)
            {
                using var reader = new StreamReader(basicCsStream);
                BasicAutoSuggestBoxCs = reader.ReadToEnd();
            }

            // 加载高级示例
            using var advancedXamlStream =
                assembly.GetManifestResourceStream(
                    "WPFTest.CodeExamples.Controls.AutoSuggestBox.AdvancedAutoSuggestBox.xaml.txt");
            if (advancedXamlStream != null)
            {
                using var reader = new StreamReader(advancedXamlStream);
                AdvancedAutoSuggestBoxXaml = reader.ReadToEnd();
            }

            using var advancedCsStream =
                assembly.GetManifestResourceStream(
                    "WPFTest.CodeExamples.Controls.AutoSuggestBox.AdvancedAutoSuggestBox.cs.txt");
            if (advancedCsStream != null)
            {
                using var reader = new StreamReader(advancedCsStream);
                AdvancedAutoSuggestBoxCs = reader.ReadToEnd();
            }

            _logger.Info("AutoSuggestBox 代码示例加载完成");
        }
        catch (Exception ex)
        {
            _logger.Error("AutoSuggestBox 代码示例加载失败", ex);
        }
    }

    #endregion

    #region 搜索命令

    [RelayCommand]
    private void CitySearch(string? query)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(query))
            {
                CitySuggestions.Clear();
                return;
            }

            var suggestions = _allCities
                .Where(city => city.Contains(query, StringComparison.OrdinalIgnoreCase))
                .Take(10)
                .ToList();

            CitySuggestions.Clear();
            foreach (var suggestion in suggestions)
            {
                CitySuggestions.Add(suggestion);
            }

            InteractionCount++;
            StatusMessage = $"找到 {suggestions.Count} 个城市建议";
            _logger.Info($"城市搜索: {query}, 找到 {suggestions.Count} 个结果");
        }
        catch (Exception ex)
        {
            StatusMessage = $"城市搜索失败: {ex.Message}";
            _logger.Error("城市搜索失败", ex);
        }
    }

    [RelayCommand]
    private void LanguageSearch(string? query)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(query))
            {
                LanguageSuggestions.Clear();
                return;
            }

            var suggestions = _allLanguages
                .Where(lang => lang.Contains(query, StringComparison.OrdinalIgnoreCase))
                .Take(10)
                .ToList();

            LanguageSuggestions.Clear();
            foreach (var suggestion in suggestions)
            {
                LanguageSuggestions.Add(suggestion);
            }

            InteractionCount++;
            StatusMessage = $"找到 {suggestions.Count} 个编程语言建议";
            _logger.Info($"编程语言搜索: {query}, 找到 {suggestions.Count} 个结果");
        }
        catch (Exception ex)
        {
            StatusMessage = $"编程语言搜索失败: {ex.Message}";
            _logger.Error("编程语言搜索失败", ex);
        }
    }

    [RelayCommand]
    private void CountrySearch(string? query)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(query))
            {
                CountrySuggestions.Clear();
                return;
            }

            var suggestions = _allCountries
                .Where(country => country.Contains(query, StringComparison.OrdinalIgnoreCase))
                .Take(10)
                .ToList();

            CountrySuggestions.Clear();
            foreach (var suggestion in suggestions)
            {
                CountrySuggestions.Add(suggestion);
            }

            InteractionCount++;
            StatusMessage = $"找到 {suggestions.Count} 个国家建议";
            _logger.Info($"国家搜索: {query}, 找到 {suggestions.Count} 个结果");
        }
        catch (Exception ex)
        {
            StatusMessage = $"国家搜索失败: {ex.Message}";
            _logger.Error("国家搜索失败", ex);
        }
    }

    [RelayCommand]
    private void ProductSearch(string? query)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(query))
            {
                ProductSuggestions.Clear();
                return;
            }

            var suggestions = _allProducts
                .Where(product => product.Contains(query, StringComparison.OrdinalIgnoreCase))
                .Take(10)
                .ToList();

            ProductSuggestions.Clear();
            foreach (var suggestion in suggestions)
            {
                ProductSuggestions.Add(suggestion);
            }

            InteractionCount++;
            StatusMessage = $"找到 {suggestions.Count} 个产品建议";
            _logger.Info($"产品搜索: {query}, 找到 {suggestions.Count} 个结果");
        }
        catch (Exception ex)
        {
            StatusMessage = $"产品搜索失败: {ex.Message}";
            _logger.Error("产品搜索失败", ex);
        }
    }

    [RelayCommand]
    private void UserSearch(string? query)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(query))
            {
                UserSuggestions.Clear();
                return;
            }

            var suggestions = _allUsers
                .Where(user => user.Contains(query, StringComparison.OrdinalIgnoreCase))
                .Take(10)
                .ToList();

            UserSuggestions.Clear();
            foreach (var suggestion in suggestions)
            {
                UserSuggestions.Add(suggestion);
            }

            InteractionCount++;
            StatusMessage = $"找到 {suggestions.Count} 个用户建议";
            _logger.Info($"用户搜索: {query}, 找到 {suggestions.Count} 个结果");
        }
        catch (Exception ex)
        {
            StatusMessage = $"用户搜索失败: {ex.Message}";
            _logger.Error("用户搜索失败", ex);
        }
    }

    [RelayCommand]
    private void ClearStatus()
    {
        try
        {
            SearchText = string.Empty;
            LanguageSearchText = string.Empty;
            CountrySearchText = string.Empty;
            ProductSearchText = string.Empty;
            UserSearchText = string.Empty;

            CitySuggestions.Clear();
            LanguageSuggestions.Clear();
            CountrySuggestions.Clear();
            ProductSuggestions.Clear();
            UserSuggestions.Clear();

            StatusMessage = "所有搜索已清除";
            _logger.Info("所有搜索已清除");
        }
        catch (Exception ex)
        {
            StatusMessage = $"清除搜索失败: {ex.Message}";
            _logger.Error("清除搜索失败", ex);
        }
    }

    [RelayCommand]
    private void ResetCount()
    {
        try
        {
            var oldCount = InteractionCount;
            InteractionCount = 0;
            StatusMessage = "计数已重置";
            _logger.Info($"交互计数从 {oldCount} 重置为 0");
        }
        catch (Exception ex)
        {
            StatusMessage = $"重置计数失败: {ex.Message}";
            _logger.Error("重置计数失败", ex);
        }
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 属性变化处理
    /// </summary>
    private void OnPropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        try
        {
            _logger.Info($"属性变化触发: {e.PropertyName}");

            switch (e.PropertyName)
            {
                case nameof(SearchText):
                    CitySearchCommand.Execute(SearchText);
                    break;
                case nameof(LanguageSearchText):
                    LanguageSearchCommand.Execute(LanguageSearchText);
                    break;
                case nameof(CountrySearchText):
                    CountrySearchCommand.Execute(CountrySearchText);
                    break;
                case nameof(ProductSearchText):
                    ProductSearchCommand.Execute(ProductSearchText);
                    break;
                case nameof(UserSearchText):
                    UserSearchCommand.Execute(UserSearchText);
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"属性变化处理失败 - 属性: {e.PropertyName}", ex);
        }
    }

    #endregion
}