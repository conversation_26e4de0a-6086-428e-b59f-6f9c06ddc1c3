using System.Windows;
using Prism.Ioc;
using Prism.Modularity;
using Prism.Regions;
using WPFTest.Services;
using WPFTest.Services.DWG;
using Zylo.WPF;
using WPFTest.ViewModels;
using WPFTest.ViewModels.ButtonControls;
using WPFTest.Views.ButtonControls;
using WPFTest.ViewModels.MediaControls;
using WPFTest.Views.MediaControls;
using Zylo.WPF.Services;
using Zylo.WPF.YPrism;
using Zylo.YLog.Runtime;

namespace WPFTest;

/// <summary>
/// 🚀 WPFTest 应用程序 Bootstrapper - 基于 Zylo.WPF 框架
/// </summary>
/// <remarks>
/// 继承自 ZyloBootstrapper，自动获得通用功能：
/// - CommunityToolkit.Mvvm 服务自动注册（IMessenger 等）
/// - 容器访问方法（ZyloContainer.Resolve）
/// - 通用生命周期管理
///
/// 同时保留完整的 Prism 功能配置：
/// - 依赖注入容器配置
/// - 区域管理和导航
/// - 模块化系统
/// - 事件聚合器
/// - 对话框服务
/// - ViewModelLocator 配置
/// - 异常处理
/// </remarks>
public class AppBootstrapper : ZyloBootstrapper
{
    public readonly YLoggerInstance _logger = YLogger.ForSilent<AppBootstrapper>();
    #region 核心生命周期方法

    /// <summary>
    /// 创建应用程序的主窗口 (Shell)
    /// </summary>
    /// <returns>主窗口实例</returns>
    /// <remarks>
    /// Shell 是应用程序的主容器，通常包含菜单、工具栏、状态栏和主要的内容区域。
    /// Prism 会自动为 Shell 配置 RegionManager 和 ViewModel 绑定。
    /// </remarks>
    protected override DependencyObject CreateShell()
    {
       
        // 强制所有类输出Debug级别及以上（开发调试）
        YLogger.ForceDebugMode();
        //
        // _logger.Debug("🚀Debug");
        // _logger.InfoDetailed("详细信息");
        // _logger.Info("一般信息");
        // _logger.InfoSimple("简化信息");
        // _logger.Warning("警告信息");
        // _logger.Error("错误信息");
    
        
        
       _logger.Info("🏠 正在创建 WPFTest 应用程序 Shell...");
        var shell = ZyloContainer.Resolve<Views.MainView>();
       _logger.Info("✅ WPFTest Shell 创建完成");
        return shell;
    }

    /// <summary>
    /// 注册应用程序类型到依赖注入容器
    /// </summary>
    /// <param name="containerRegistry">容器注册器</param>
    /// <remarks>
    /// 在这里注册：
    /// - ViewModels（如果需要手动注册）
    /// - 服务接口和实现
    /// - 导航页面（利用 Prism 自动 View-ViewModel 绑定）
    /// - 对话框
    ///
    /// 注意：IMessenger 等 CommunityToolkit.Mvvm 服务已由 ZyloBootstrapper 自动注册。
    /// </remarks>
    protected override void RegisterTypes(IContainerRegistry containerRegistry)
    {
       _logger.Debug("📝 正在注册 WPFTest 应用程序类型...");

        // 🏠 注册主窗口和 ViewModel
        // containerRegistry.Register<Views.MainView>();
        // containerRegistry.Register<ViewModels.MainViewModel>();

        // 注册 MainViewModel 作为 IConfigureService 的实现
        containerRegistry.Register<IConfigureService, MainViewModel>();

        // 🗄️ 注册DWG相关服务（使用EF Core + SQLite）
        containerRegistry.RegisterSingleton<IDwgFileTypeService, DwgFileTypeService>(); // DWG文件类型服务
        containerRegistry.RegisterSingleton<IDwgFolderService, DwgFolderService>(); // DWG文件夹服务
        _logger.Debug("✅ DWG服务注册完成");

        // 🎯 注册UI相关服务
        containerRegistry.RegisterSingleton<Zylo.WPF.Services.ISnackbarService, Zylo.WPF.Services.SnackbarService>(); // 传统Snackbar服务（兼容性）
        containerRegistry.RegisterSingleton<Zylo.WPF.Services.IWindowSnackbarService, Zylo.WPF.Services.WindowSnackbarService>(); // 多窗口安全Snackbar服务
        _logger.Debug("✅ UI服务注册完成");


        containerRegistry.RegisterForNavigation<MainView, MainViewModel>();
       _logger.Debug("✅ MainView 和 MainViewModel 注册完成");

        // 🎯 注册导航页面（Prism 自动绑定：HomePageView -> HomePageViewModel）

        containerRegistry.RegisterForNavigation<ButtonPageView,ButtonPageViewModel>();
        containerRegistry.RegisterForNavigation<DropDownButtonPageView,DropDownButtonPageViewModel>();
        containerRegistry.RegisterForNavigation<SplitButtonPageView,SplitButtonPageViewModel>();
        containerRegistry.RegisterForNavigation<ToggleSwitchPageView,ToggleSwitchPageViewModel>();
        containerRegistry.RegisterForNavigation<HyperlinkButtonPageView,HyperlinkButtonPageViewModel>();

        // 📝 注册输入控件页面
        containerRegistry.RegisterForNavigation<Views.InputControls.TextBoxPageView, ViewModels.InputControls.TextBoxPageViewModel>();
        containerRegistry.RegisterForNavigation<Views.InputControls.PasswordBoxPageView, ViewModels.InputControls.PasswordBoxPageViewModel>();
        containerRegistry.RegisterForNavigation<Views.InputControls.NumberBoxPageView, ViewModels.InputControls.NumberBoxPageViewModel>();
        containerRegistry.RegisterForNavigation<Views.InputControls.AutoSuggestBoxPageView, ViewModels.InputControls.AutoSuggestBoxPageViewModel>();
        containerRegistry.RegisterForNavigation<Views.InputControls.CalendarDatePickerPageView, ViewModels.InputControls.CalendarDatePickerPageViewModel>();
        containerRegistry.RegisterForNavigation<Views.InputControls.TimePickerPageView, ViewModels.InputControls.TimePickerPageViewModel>();
        containerRegistry.RegisterForNavigation<Views.List.ListPageView, ViewModels.List.ListPageViewModel>();
        containerRegistry.RegisterForNavigation<Views.List.DataGridPageView, ViewModels.List.DataGridPageViewModel>();
        containerRegistry.RegisterForNavigation<Views.List.TreeViewPageView, ViewModels.List.TreeViewPageViewModel>();
        containerRegistry.RegisterForNavigation<Views.List.TabViewPageView, ViewModels.List.TabViewPageViewModel>();

        containerRegistry.RegisterForNavigation<IconTestPage>(); // 图标测试页面
       
        // 🏠 注册主菜单页面
        containerRegistry.RegisterForNavigation<HomePageView, HomePageViewModel>();

        // 🔧 注册新主题设置页面
        containerRegistry.RegisterForNavigation<NewThemeSettingsView, NewThemeSettingsViewModel>();

        // ⚙️ 注册设置页面
        containerRegistry.RegisterForNavigation<SettingsView, SettingsViewModel>();

        // 📋 注册子菜单页面
        containerRegistry.RegisterForNavigation<SubMenuView, SubMenuViewModel>();


        
        // 🚀 注册 NavigationControl 示例页面
        containerRegistry.RegisterForNavigation<Views.Navigation.NavigationBasicExample, ViewModels.Navigation.NavigationBasicExampleViewModel>();
        containerRegistry.RegisterForNavigation<Views.Navigation.NavigationAdvancedExample, ViewModels.Navigation.NavigationAdvancedExampleViewModel>();

        // 🍞 注册 BreadcrumbBar 示例页面
        containerRegistry.RegisterForNavigation<Views.Navigation.BreadcrumbBarView, ViewModels.Navigation.BreadcrumbBarViewModel>();

        // 📋 注册 MenuBar 示例页面
        containerRegistry.RegisterForNavigation<Views.Navigation.MenuBarPageView, ViewModels.Navigation.MenuBarPageViewModel>();

        // 🎨 注册布局容器控件页面
        containerRegistry.RegisterForNavigation<Views.LayoutControls.BorderView, ViewModels.LayoutControls.BorderViewModel>();
        containerRegistry.RegisterForNavigation<Views.LayoutControls.ExpanderView, ViewModels.LayoutControls.ExpanderViewModel>();
        containerRegistry.RegisterForNavigation<Views.LayoutControls.GroupBoxView, ViewModels.LayoutControls.GroupBoxViewModel>();
        containerRegistry.RegisterForNavigation<Views.LayoutControls.ScrollViewerView, ViewModels.LayoutControls.ScrollViewerViewModel>();
        containerRegistry.RegisterForNavigation<Views.LayoutControls.CardView, ViewModels.LayoutControls.CardViewModel>();
        containerRegistry.RegisterForNavigation<Views.LayoutControls.CardExpanderView, ViewModels.LayoutControls.CardExpanderViewModel>();
        containerRegistry.RegisterForNavigation<Views.LayoutControls.FlyoutView, ViewModels.LayoutControls.FlyoutViewModel>();

        // 📦 注册 MessageBox 示例页面
        containerRegistry.RegisterForNavigation<Views.NotificationControls.MessageBoxPageView, ViewModels.NotificationControls.MessageBoxPageViewModel>();

  



        // 📢 注册 InfoBarPageView 页面 - InfoBar 通知控件示例
        containerRegistry.RegisterForNavigation<Views.NotificationControls.InfoBarPageView, ViewModels.NotificationControls.InfoBarPageViewModel>();
        containerRegistry.RegisterForNavigation<Views.NotificationControls.SnackbarPageView, ViewModels.NotificationControls.SnackbarPageViewModel>();
        containerRegistry.RegisterForNavigation<Views.NotificationControls.ContentDialogPageView, ViewModels.NotificationControls.ContentDialogPageViewModel>();

        // 🪟 注册窗口控件页面
        containerRegistry.RegisterForNavigation<Views.WindowControls.PopupWindowPageView, ViewModels.WindowControls.PopupWindowPageViewModel>();

        // 🎨 注册媒体控件页面
        containerRegistry.RegisterForNavigation<Views.MediaControls.ImagePageView, ViewModels.MediaControls.ImagePageViewModel>();
        containerRegistry.RegisterForNavigation<Views.MediaControls.ProgressRingPageView, ViewModels.MediaControls.ProgressRingPageViewModel>();
        containerRegistry.RegisterForNavigation<Views.MediaControls.BadgePageView, ViewModels.MediaControls.BadgePageViewModel>();
        containerRegistry.RegisterForNavigation<Views.MediaControls.InfoBadgePageView, ViewModels.MediaControls.InfoBadgePageViewModel>();

        containerRegistry.RegisterForNavigation<Views.Navigation.BreadcrumbBarView, ViewModels.Navigation.BreadcrumbBarViewModel>();
        
        containerRegistry.RegisterForNavigation<Views.WindowControls.TitleBarPageView, ViewModels.WindowControls.TitleBarPageViewModel>();
        
        // 🎯 注册拖拽功能页面
        containerRegistry.RegisterForNavigation<Views.DragDrop.ListDragDropExample, ViewModels.DragDrop.ListDragDropExampleViewModel>();
        containerRegistry.RegisterForNavigation<Views.DragDrop.TreeDragDropExample, ViewModels.DragDrop.TreeDragDropExampleViewModel>();
        containerRegistry.RegisterForNavigation<Views.DragDrop.CrossControlDragExample, ViewModels.DragDrop.CrossControlDragExampleViewModel>();
        containerRegistry.RegisterForNavigation<Views.DragDrop.FileDragExample, ViewModels.DragDrop.FileDragExampleViewModel>();

        containerRegistry.RegisterForNavigation<Views.DragDrop.DwgManagerTabView, ViewModels.DragDrop.DwgManagerTabViewModel>();
        containerRegistry.RegisterForNavigation<Views.DWG.DwgFileTypeEditorView, ViewModels.DWG.DwgFileTypeEditorViewModel>();
        containerRegistry.RegisterForNavigation<Views.DWG.DwgFolderEditorView, ViewModels.DWG.DwgFolderEditorViewModel>();
       
        containerRegistry.RegisterForNavigation<Views.Pages.FileExplorerPage>();
        


       _logger.Debug("✅ 导航页面注册完成");

        // 🔧 注册应用程序服务
        containerRegistry.RegisterSingleton<IResourceVerificationService, ResourceVerificationService>(); // 资源验证服务
        containerRegistry.RegisterSingleton<IThemeManagementService, ThemeManagementService>(); // 主题管理服务
       _logger.Debug("✅ 应用程序服务注册完成");


        containerRegistry.RegisterForNavigation<ColorShowcaseView, ColorShowcaseViewModel>();

        // 💬 注册对话框
        // containerRegistry.RegisterDialog<UserDialog, UserDialogViewModel>();

       _logger.Debug("✅ WPFTest 类型注册完成");
    }

    /// <summary>
    /// 注册 Prism 框架必需的类型
    /// </summary>
    /// <param name="containerRegistry">容器注册器</param>
    /// <remarks>
    /// 这个方法由 Prism 框架调用，用于注册核心服务：
    /// - IEventAggregator: 事件聚合器
    /// - IRegionManager: 区域管理器
    /// - IDialogService: 对话框服务
    /// - IModuleManager: 模块管理器
    /// - IMessenger: 消息服务（由 ZyloBootstrapper 自动添加）
    /// - 等等...
    /// </remarks>
    protected override void RegisterRequiredTypes(IContainerRegistry containerRegistry)
    {
       _logger.Debug("🔧 正在注册 Prism 框架必需类型...");
        base.RegisterRequiredTypes(containerRegistry);
       _logger.Debug("✅ Prism 框架必需类型注册完成");
    }

    /// <summary>
    /// 注册框架异常类型
    /// </summary>
    /// <remarks>
    /// 注册不被视为根异常的异常类型，用于异常处理和调试。
    /// 这些异常通常是框架内部异常，不应该暴露给最终用户。
    /// </remarks>
    protected override void RegisterFrameworkExceptionTypes()
    {
       _logger.Debug("⚠️ 正在注册框架异常类型...");
        base.RegisterFrameworkExceptionTypes();

        // 可以添加自定义异常类型
        // ExceptionExtensions.RegisterFrameworkExceptionType(typeof(CustomFrameworkException));

       _logger.Debug("✅ 框架异常类型注册完成");
    }

    /// <summary>
    /// 配置 ViewModelLocator
    /// </summary>
    /// <remarks>
    /// 配置 Prism 的 ViewModelLocator，用于自动 View-ViewModel 绑定：
    /// - 设置 ViewModel 工厂
    /// - 配置命名约定
    /// - 设置 ViewModel 解析策略
    /// </remarks>
    protected override void ConfigureViewModelLocator()
    {
       _logger.Debug("🎯 正在配置 ViewModelLocator...");
        base.ConfigureViewModelLocator();

        // 可以自定义 ViewModelLocator 配置
        // ViewModelLocationProvider.SetDefaultViewModelFactory((view, type) => ZyloContainer.Resolve(type));

       _logger.Debug("✅ ViewModelLocator 配置完成");
    }

    /// <summary>
    /// 注册额外的 CommunityToolkit.Mvvm 服务
    /// </summary>
    /// <param name="containerRegistry">容器注册器</param>
    /// <remarks>
    /// 在这里可以注册项目特定的 CommunityToolkit.Mvvm 相关服务
    /// </remarks>
    protected override void RegisterAdditionalCommunityToolkitServices(IContainerRegistry containerRegistry)
    {
       _logger.Debug("🔧 正在注册额外的 CommunityToolkit.Mvvm 服务...");

        // 注册项目特定的服务
        // containerRegistry.RegisterSingleton<IFileService, FileService>();
        // containerRegistry.RegisterSingleton<IDialogService, CustomDialogService>();
        // containerRegistry.RegisterSingleton<IThemeService, ThemeService>();

       _logger.Debug("✅ 额外的 CommunityToolkit.Mvvm 服务注册完成");
    }

    #endregion

    #region 模块化系统配置

    /// <summary>
    /// 配置模块目录
    /// </summary>
    /// <param name="moduleCatalog">模块目录</param>
    /// <remarks>
    /// 在这里配置应用程序的模块：
    /// - 添加模块引用
    /// - 设置模块依赖关系
    /// - 配置模块加载策略（按需加载、延迟加载等）
    /// </remarks>
    protected override void ConfigureModuleCatalog(IModuleCatalog moduleCatalog)
    {
       _logger.Debug("📦 正在配置模块目录...");

        // 添加模块（示例）
        // moduleCatalog.AddModule<UserModule>();
        // moduleCatalog.AddModule<OrderModule>(InitializationMode.OnDemand);

        // 从目录加载模块
        // var directoryModuleCatalog = new DirectoryModuleCatalog() { ModulePath = @".\Modules" };
        // moduleCatalog.AddCatalog(directoryModuleCatalog);

       _logger.Debug("✅ 模块目录配置完成");
    }

    /// <summary>
    /// 初始化模块
    /// </summary>
    /// <remarks>
    /// 这个方法在所有配置完成后被调用，用于初始化已注册的模块。
    /// </remarks>
    protected override void InitializeModules()
    {
       _logger.Debug("🔄 正在初始化模块...");
        base.InitializeModules();
       _logger.Debug("✅ 模块初始化完成");
    }

    #endregion

    #region 区域管理配置

    /// <summary>
    /// 配置区域适配器映射
    /// </summary>
    /// <param name="regionAdapterMappings">区域适配器映射</param>
    /// <remarks>
    /// 区域适配器负责将 WPF 控件转换为 Prism 区域：
    /// - ContentControl -> ContentControlRegionAdapter
    /// - ItemsControl -> ItemsControlRegionAdapter
    /// - Selector -> SelectorRegionAdapter
    /// - TabControl -> TabControlRegionAdapter (自定义)
    /// </remarks>
    protected override void ConfigureRegionAdapterMappings(RegionAdapterMappings regionAdapterMappings)
    {
       _logger.Debug("🗺️ 正在配置区域适配器映射...");

        base.ConfigureRegionAdapterMappings(regionAdapterMappings);

        // 添加自定义区域适配器
        // regionAdapterMappings.RegisterMapping(typeof(StackPanel), ZyloContainer.Resolve<StackPanelRegionAdapter>());
        // regionAdapterMappings.RegisterMapping(typeof(Grid), ZyloContainer.Resolve<GridRegionAdapter>());

       _logger.Debug("✅ 区域适配器映射配置完成");
    }

    /// <summary>
    /// 配置默认区域行为
    /// </summary>
    /// <param name="regionBehaviors">区域行为工厂</param>
    /// <remarks>
    /// 区域行为定义了区域的默认功能：
    /// - AutoPopulateRegionBehavior: 自动填充区域
    /// - BindRegionContextToDependencyObjectBehavior: 绑定区域上下文
    /// - RegionActiveAwareBehavior: 区域激活感知
    /// - SyncRegionContextWithHostBehavior: 同步区域上下文
    /// - RegionManagerRegistrationBehavior: 区域管理器注册
    /// </remarks>
    protected override void ConfigureDefaultRegionBehaviors(IRegionBehaviorFactory regionBehaviors)
    {
       _logger.Debug("⚙️ 正在配置默认区域行为...");

        base.ConfigureDefaultRegionBehaviors(regionBehaviors);

        // 添加自定义区域行为
        // regionBehaviors.AddIfMissing("CustomBehavior", typeof(CustomRegionBehavior));

       _logger.Debug("✅ 默认区域行为配置完成");
    }

    #endregion

    #region Shell 初始化

    /// <summary>
    /// 初始化 Shell
    /// </summary>
    /// <param name="shell">Shell 实例</param>
    /// <remarks>
    /// 在这里执行 Shell 的最终初始化：
    /// - 显示主窗口
    /// - 设置窗口属性
    /// - 执行初始导航
    /// - 加载用户设置
    /// </remarks>
    protected override void InitializeShell(DependencyObject shell)
    {
       _logger.Debug("🚀 正在初始化 Shell...");

        if (shell is Window window)
        {
            // 设置窗口属性
            window.WindowStartupLocation = WindowStartupLocation.CenterScreen;

            // 显示主窗口
            window.Show();

            // 执行初始导航（可选）
            // var regionManager = ZyloContainer.Resolve<IRegionManager>();
            // regionManager.RequestNavigate("ContentRegion", "Page1View");
        }

       _logger.Debug("✅ Shell 初始化完成");
    }

    /// <summary>
    /// 应用程序初始化完成回调
    /// </summary>
    /// <remarks>
    /// 这是 Prism 初始化流程的最后一步，在这里可以：
    /// - 执行应用程序启动逻辑
    /// - 加载用户数据
    /// - 显示启动画面
    /// - 检查更新
    /// </remarks>
    protected override void OnInitialized()
    {
        base.OnInitialized();

       _logger.Debug("🎉 Prism 应用程序初始化完成！");
       _logger.Debug("📱 应用程序已准备就绪，可以开始使用所有 Prism 功能：");
       _logger.Debug("   ✅ 依赖注入容器");
       _logger.Debug("   ✅ 区域管理和导航");
       _logger.Debug("   ✅ 事件聚合器");
       _logger.Debug("   ✅ 对话框服务");
       _logger.Debug("   ✅ 模块化系统");
       _logger.Debug("   ✅ ViewModelLocator");
       _logger.Debug("   ✅ CommunityToolkit.Mvvm 服务");

        // 🔍 验证 Zylo.WPF 资源加载
        var verificationService = ZyloContainer.Resolve<IResourceVerificationService>();
        verificationService.VerifyZyloResources();

        // 🎨 加载保存的主题设置
        var themeService = ZyloContainer.Resolve<IThemeManagementService>();
        themeService.LoadSavedThemeSettings();

        // 🔥 预热 Snackbar 服务，避免第一次使用延迟
        try
        {
            var snackbarService = ZyloContainer.Resolve<Zylo.WPF.Services.ISnackbarService>();
            _logger.Debug("✅ Snackbar 服务预热完成");
        }
        catch (Exception ex)
        {
            _logger.Warning($"⚠️ Snackbar 服务预热失败: {ex.Message}");
        }

        // 🚀 执行配置服务的初始化（包括默认导航）
        var configureService = ZyloContainer.Resolve<IConfigureService>();
        configureService.Configure();
    }



    #endregion
}
