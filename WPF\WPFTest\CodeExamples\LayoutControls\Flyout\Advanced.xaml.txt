<!-- WPF-UI Flyout 高级功能 XAML 示例 -->

<!-- 1. 菜单类型的 Flyout -->
<ui:Button Content="操作菜单">
    <ui:Flyout x:Name="MenuFlyout" 
               Placement="Bottom"
               IsOpen="{Binding IsMenuFlyoutOpen}">
        <StackPanel Margin="8" MinWidth="150">
            <ui:Button Content="新建" 
                       Command="{Binding NewCommand}"
                       HorizontalAlignment="Stretch"
                       HorizontalContentAlignment="Left"
                       Appearance="Transparent"
                       Margin="0,0,0,4"/>
            <ui:Button Content="编辑" 
                       Command="{Binding EditCommand}"
                       HorizontalAlignment="Stretch"
                       HorizontalContentAlignment="Left"
                       Appearance="Transparent"
                       Margin="0,0,0,4"/>
            <ui:Button Content="删除" 
                       Command="{Binding DeleteCommand}"
                       HorizontalAlignment="Stretch"
                       HorizontalContentAlignment="Left"
                       Appearance="Transparent"
                       Margin="0,0,0,4"/>
            <Separator Margin="0,4,0,4"/>
            <ui:Button Content="设置" 
                       Command="{Binding SettingsCommand}"
                       HorizontalAlignment="Stretch"
                       HorizontalContentAlignment="Left"
                       Appearance="Transparent"/>
        </StackPanel>
    </ui:Flyout>
</ui:Button>

<!-- 2. 用户信息 Flyout -->
<ui:Button Content="用户菜单">
    <ui:Flyout x:Name="UserFlyout" 
               Placement="Bottom"
               IsOpen="{Binding IsUserFlyoutOpen}">
        <StackPanel Margin="16" MinWidth="200">
            <!-- 用户信息 -->
            <Grid Margin="0,0,0,12">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <Border Grid.Column="0" 
                        Background="{DynamicResource AccentFillColorDefaultBrush}"
                        Width="40" Height="40" 
                        CornerRadius="20"
                        Margin="0,0,12,0">
                    <TextBlock Text="U" 
                               Foreground="White" 
                               FontWeight="Bold"
                               HorizontalAlignment="Center" 
                               VerticalAlignment="Center"/>
                </Border>
                
                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                    <TextBlock Text="{Binding UserName}" FontWeight="Bold"/>
                    <TextBlock Text="{Binding UserEmail}" 
                               FontSize="12" 
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                </StackPanel>
            </Grid>
            
            <Separator Margin="0,0,0,8"/>
            
            <!-- 菜单项 -->
            <ui:Button Content="个人资料" 
                       Command="{Binding ProfileCommand}"
                       HorizontalAlignment="Stretch"
                       HorizontalContentAlignment="Left"
                       Appearance="Transparent"
                       Margin="0,0,0,4"/>
            <ui:Button Content="账户设置" 
                       Command="{Binding AccountCommand}"
                       HorizontalAlignment="Stretch"
                       HorizontalContentAlignment="Left"
                       Appearance="Transparent"
                       Margin="0,0,0,4"/>
            <ui:Button Content="帮助" 
                       Command="{Binding HelpCommand}"
                       HorizontalAlignment="Stretch"
                       HorizontalContentAlignment="Left"
                       Appearance="Transparent"
                       Margin="0,0,0,8"/>
            
            <Separator Margin="0,0,0,8"/>
            
            <ui:Button Content="退出登录" 
                       Command="{Binding LogoutCommand}"
                       HorizontalAlignment="Stretch"
                       HorizontalContentAlignment="Left"
                       Appearance="Transparent"/>
        </StackPanel>
    </ui:Flyout>
</ui:Button>

<!-- 3. 表单类型的 Flyout -->
<ui:Button Content="快速设置">
    <ui:Flyout x:Name="SettingsFlyout" 
               Placement="Bottom"
               IsOpen="{Binding IsSettingsFlyoutOpen}">
        <StackPanel Margin="16" MinWidth="250">
            <TextBlock Text="快速设置" 
                       FontWeight="Bold" 
                       FontSize="16"
                       Margin="0,0,0,12"/>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <TextBlock Grid.Row="0" Grid.Column="0" 
                           Text="主题:" 
                           VerticalAlignment="Center"
                           Margin="0,0,8,8"/>
                <ComboBox Grid.Row="0" Grid.Column="1" 
                          SelectedItem="{Binding SelectedTheme}"
                          ItemsSource="{Binding Themes}"
                          Margin="0,0,0,8"/>
                
                <TextBlock Grid.Row="1" Grid.Column="0" 
                           Text="语言:" 
                           VerticalAlignment="Center"
                           Margin="0,0,8,8"/>
                <ComboBox Grid.Row="1" Grid.Column="1" 
                          SelectedItem="{Binding SelectedLanguage}"
                          ItemsSource="{Binding Languages}"
                          Margin="0,0,0,8"/>
                
                <CheckBox Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2"
                          Content="启用通知" 
                          IsChecked="{Binding EnableNotifications}"
                          Margin="0,0,0,8"/>
                
                <CheckBox Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="2"
                          Content="自动保存" 
                          IsChecked="{Binding AutoSave}"
                          Margin="0,0,0,12"/>
            </Grid>
            
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="取消" 
                        Command="{Binding CancelCommand}"
                        Margin="0,0,8,0"/>
                <Button Content="保存" 
                        Command="{Binding SaveCommand}"/>
            </StackPanel>
        </StackPanel>
    </ui:Flyout>
</ui:Button>

<!-- 4. 通知类型的 Flyout -->
<ui:Button Content="通知">
    <ui:Flyout x:Name="NotificationFlyout" 
               Placement="Bottom"
               IsOpen="{Binding IsNotificationFlyoutOpen}">
        <Grid Margin="16" MinWidth="300">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <ui:SymbolIcon Grid.Column="0"
                           Symbol="{Binding NotificationIcon}" 
                           FontSize="24" 
                           Foreground="{Binding NotificationColor}"
                           Margin="0,0,12,0"
                           VerticalAlignment="Top"/>
            
            <StackPanel Grid.Column="1">
                <TextBlock Text="{Binding NotificationTitle}" 
                           FontWeight="Bold" 
                           Margin="0,0,0,4"/>
                <TextBlock Text="{Binding NotificationMessage}" 
                           TextWrapping="Wrap"
                           Margin="0,0,0,8"/>
                <TextBlock Text="{Binding NotificationTime}" 
                           FontSize="11"
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
            </StackPanel>
            
            <ui:Button Grid.Column="2"
                       Content="✕"
                       Command="{Binding CloseNotificationCommand}"
                       Appearance="Transparent"
                       Width="24" Height="24"
                       Padding="0"
                       VerticalAlignment="Top"/>
        </Grid>
    </ui:Flyout>
</ui:Button>
