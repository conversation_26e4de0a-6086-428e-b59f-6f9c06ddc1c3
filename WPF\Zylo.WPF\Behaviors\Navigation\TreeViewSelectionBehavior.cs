using System.Windows.Input;
using Microsoft.Xaml.Behaviors;
using Zylo.WPF.Models.Navigation;
using Zylo.YLog.Runtime;

namespace Zylo.WPF.Behaviors.Navigation;

/// <summary>
/// TreeView选中项行为 - 支持双向绑定SelectedItem和选中命令
/// </summary>
public class TreeViewSelectionBehavior : Behavior<TreeView>
{
    public readonly YLoggerInstance _logger = YLogger.ForSilent<TreeViewSelectionBehavior>();
    #region SelectedItem 依赖属性

    /// <summary>
    /// 选中项依赖属性
    /// </summary>
    public static readonly DependencyProperty SelectedItemProperty =
        DependencyProperty.Register(
            nameof(SelectedItem),
            typeof(object),
            typeof(TreeViewSelectionBehavior),
            new FrameworkPropertyMetadata(null, FrameworkPropertyMetadataOptions.BindsTwoWayByDefault, OnSelectedItemChanged));

    /// <summary>
    /// 选中项
    /// </summary>
    public object SelectedItem
    {
        get => GetValue(SelectedItemProperty);
        set => SetValue(SelectedItemProperty, value);
    }

    /// <summary>
    /// 选中项变化处理
    /// </summary>
    private static void OnSelectedItemChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is TreeViewSelectionBehavior behavior && behavior.AssociatedObject != null)
        {
            // 避免循环触发
            behavior._isUpdatingFromProperty = true;
            behavior.SelectTreeViewItem(e.NewValue);
            behavior._isUpdatingFromProperty = false;
        }
    }

    #endregion

    #region 选中命令相关属性

    /// <summary>
    /// 选择命令 - 当TreeView项被选中时执行的命令
    /// </summary>
    public static readonly DependencyProperty SelectionCommandProperty =
        DependencyProperty.Register(
            nameof(SelectionCommand),
            typeof(ICommand),
            typeof(TreeViewSelectionBehavior),
            new PropertyMetadata(null));

    /// <summary>
    /// 获取或设置选择命令
    /// </summary>
    public ICommand SelectionCommand
    {
        get => (ICommand)GetValue(SelectionCommandProperty);
        set => SetValue(SelectionCommandProperty, value);
    }

    /// <summary>
    /// 选择命令参数 - 传递给选择命令的参数
    /// </summary>
    public static readonly DependencyProperty SelectionCommandParameterProperty =
        DependencyProperty.Register(
            nameof(SelectionCommandParameter),
            typeof(object),
            typeof(TreeViewSelectionBehavior),
            new PropertyMetadata(null));

    /// <summary>
    /// 获取或设置选择命令参数
    /// </summary>
    public object SelectionCommandParameter
    {
        get => GetValue(SelectionCommandParameterProperty);
        set => SetValue(SelectionCommandParameterProperty, value);
    }

    /// <summary>
    /// 是否传递选中项作为命令参数
    /// </summary>
    public static readonly DependencyProperty PassSelectedItemAsParameterProperty =
        DependencyProperty.Register(
            nameof(PassSelectedItemAsParameter),
            typeof(bool),
            typeof(TreeViewSelectionBehavior),
            new PropertyMetadata(true));

    /// <summary>
    /// 获取或设置是否传递选中项作为命令参数
    /// </summary>
    public bool PassSelectedItemAsParameter
    {
        get => (bool)GetValue(PassSelectedItemAsParameterProperty);
        set => SetValue(PassSelectedItemAsParameterProperty, value);
    }

    #endregion

    #region 私有字段

    private bool _isUpdatingFromProperty = false;

    #endregion

    #region 行为生命周期

    /// <summary>
    /// 附加到TreeView时
    /// </summary>
    protected override void OnAttached()
    {
        base.OnAttached();
        AssociatedObject.SelectedItemChanged += OnTreeViewSelectedItemChanged;
        
        // 初始化选中项
        if (SelectedItem != null)
        {
            SelectTreeViewItem(SelectedItem);
        }
    }

    /// <summary>
    /// 从TreeView分离时
    /// </summary>
    protected override void OnDetaching()
    {
        if (AssociatedObject != null)
        {
            AssociatedObject.SelectedItemChanged -= OnTreeViewSelectedItemChanged;
        }
        base.OnDetaching();
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 选中指定的TreeViewItem
    /// </summary>
    /// <param name="item">要选中的数据项</param>
    private void SelectTreeViewItem(object item)
    {
        if (AssociatedObject == null || item == null) return;

        // 首先尝试直接查找
        var treeViewItem = FindTreeViewItem(AssociatedObject, item);
        if (treeViewItem != null)
        {
            treeViewItem.IsSelected = true;
            treeViewItem.BringIntoView();
            return;
        }

        // 如果没找到，可能是因为虚拟化或者父项未展开
        // 尝试展开路径并重新查找
        ExpandPathToItem(AssociatedObject, item);

        // 延迟一帧后重新查找
        AssociatedObject.Dispatcher.BeginInvoke(new Action(() =>
        {
            var delayedTreeViewItem = FindTreeViewItem(AssociatedObject, item);
            if (delayedTreeViewItem != null)
            {
                delayedTreeViewItem.IsSelected = true;
                delayedTreeViewItem.BringIntoView();
            }
        }), System.Windows.Threading.DispatcherPriority.Background);
    }

    /// <summary>
    /// 在TreeView中查找对应数据项的TreeViewItem
    /// </summary>
    /// <param name="parent">父容器</param>
    /// <param name="item">要查找的数据项</param>
    /// <returns>找到的TreeViewItem，如果没找到返回null</returns>
    private TreeViewItem FindTreeViewItem(ItemsControl parent, object item)
    {
        if (parent == null || item == null) return null;

        // 遍历所有子项
        for (int i = 0; i < parent.Items.Count; i++)
        {
            var dataItem = parent.Items[i];
            var container = parent.ItemContainerGenerator.ContainerFromIndex(i) as TreeViewItem;

            // 如果找到匹配的数据项
            if (ReferenceEquals(dataItem, item) || dataItem.Equals(item))
            {
                return container;
            }

            // 递归查找子项
            if (container != null)
            {
                var childItem = FindTreeViewItem(container, item);
                if (childItem != null)
                {
                    return childItem;
                }
            }
        }

        return null;
    }

    /// <summary>
    /// 展开到指定项的路径
    /// </summary>
    /// <param name="parent">父容器</param>
    /// <param name="targetItem">目标项</param>
    /// <returns>是否找到并展开了路径</returns>
    private bool ExpandPathToItem(ItemsControl parent, object targetItem)
    {
        if (parent == null || targetItem == null) return false;

        // 遍历所有子项
        for (int i = 0; i < parent.Items.Count; i++)
        {
            var dataItem = parent.Items[i];

            // 如果找到目标项
            if (ReferenceEquals(dataItem, targetItem) || dataItem.Equals(targetItem))
            {
                return true;
            }

            // 检查子项中是否包含目标项
            if (dataItem is ZyloNavigationItemModel navItem && ContainsItem(navItem, targetItem))
            {
                var container = parent.ItemContainerGenerator.ContainerFromIndex(i) as TreeViewItem;
                if (container != null)
                {
                    container.IsExpanded = true;
                    container.UpdateLayout(); // 强制更新布局

                    // 递归展开子项路径
                    if (ExpandPathToItem(container, targetItem))
                    {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /// <summary>
    /// 检查导航项是否包含指定的子项
    /// </summary>
    /// <param name="parent">父导航项</param>
    /// <param name="targetItem">目标项</param>
    /// <returns>是否包含</returns>
    private bool ContainsItem(ZyloNavigationItemModel parent, object targetItem)
    {
        if (parent?.Children == null) return false;

        foreach (var child in parent.Children)
        {
            if (ReferenceEquals(child, targetItem) || child.Equals(targetItem))
            {
                return true;
            }

            if (ContainsItem(child, targetItem))
            {
                return true;
            }
        }

        return false;
    }

    #endregion

    #region 事件处理

    /// <summary>
    /// TreeView选中项变化处理
    /// </summary>
    private void OnTreeViewSelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
    {
        // 避免循环触发
        if (_isUpdatingFromProperty) return;

        // 更新绑定的SelectedItem属性
        SelectedItem = e.NewValue;

        // 执行选中命令
        ExecuteSelectionCommand(e.NewValue);
    }

    /// <summary>
    /// 执行选中命令
    /// </summary>
    private void ExecuteSelectionCommand(object selectedItem)
    {
        _logger.InfoDetailed($"🌳 TreeView ExecuteSelectionCommand 被调用，参数: {selectedItem?.GetType().Name}");

        if (SelectionCommand == null)
        {
            _logger.Warning("❌ TreeView SelectionCommand 为空");
            return;
        }

        // Console.WriteLine($"🔧 TreeView PassSelectedItemAsParameter: {PassSelectedItemAsParameter}");

        // 确定命令参数
        object parameter = PassSelectedItemAsParameter ? selectedItem : SelectionCommandParameter;

        _logger.InfoDetailed($"🎯 TreeView 准备执行命令，参数类型: {parameter?.GetType().Name}");

        // 执行命令
        if (SelectionCommand.CanExecute(parameter))
        {
            _logger.InfoDetailed($"✅ TreeView 执行命令");
            SelectionCommand.Execute(parameter);
        }
        else
        {
            _logger.Error($"❌ TreeView 命令无法执行");
        }
    }

    #endregion
}
