<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
                    xmlns:input="clr-namespace:Zylo.WPF.Controls.Input">

    <!-- LabelTextBox 默认样式 -->
    <Style TargetType="{x:Type input:LabelTextBox}">
        <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="12"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="MinHeight" Value="40"/>
        <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type input:LabelTextBox}">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            Padding="{TemplateBinding Padding}"
                            CornerRadius="4">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="{Binding LabelWidth, RelativeSource={RelativeSource TemplatedParent}}"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- 标签 -->
                            <TextBlock Grid.Column="0"
                                       Text="{TemplateBinding LabelText}"
                                       FontSize="{TemplateBinding LabelFontSize}"
                                       FontWeight="{TemplateBinding LabelFontWeight}"
                                       Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                       VerticalAlignment="Center"
                                       TextWrapping="Wrap"
                                       Margin="0,0,12,0"/>

                            <!-- 输入框 -->
                            <ui:TextBox x:Name="PART_TextBox"
                                        Grid.Column="1"
                                        Text="{Binding Text, RelativeSource={RelativeSource TemplatedParent}, UpdateSourceTrigger=PropertyChanged}"
                                        PlaceholderText="{TemplateBinding PlaceholderText}"
                                        Background="Transparent"
                                        BorderThickness="0"
                                        Padding="0"
                                        Margin="0"
                                        MinHeight="{TemplateBinding TextBoxMinHeight}"
                                        IsReadOnly="{TemplateBinding IsReadOnly}"
                                        MaxLength="{TemplateBinding MaxLength}"
                                        VerticalContentAlignment="Center"
                                        HorizontalContentAlignment="Left"/>
                        </Grid>
                    </Border>

                    <!-- 触发器 -->
                    <ControlTemplate.Triggers>
                            <!-- 多行模式 -->
                            <Trigger Property="IsMultiline" Value="True">
                                <Setter TargetName="PART_TextBox" Property="TextWrapping" Value="Wrap"/>
                                <Setter TargetName="PART_TextBox" Property="AcceptsReturn" Value="True"/>
                                <Setter TargetName="PART_TextBox" Property="VerticalScrollBarVisibility" Value="Auto"/>
                                <Setter TargetName="PART_TextBox" Property="VerticalContentAlignment" Value="Top"/>
                                <Setter Property="MinHeight" Value="80"/>
                                <!-- 多行模式下标签顶部对齐 -->
                                <Setter TargetName="PART_TextBox" Property="Grid.Column" Value="1"/>
                            </Trigger>
                            
                            <!-- 鼠标悬停 -->
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorSecondaryBrush}"/>
                                <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}"/>
                            </Trigger>
                            
                            <!-- 禁用状态 -->
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Opacity" Value="0.5"/>
                                <Setter Property="Background" Value="{DynamicResource ControlFillColorDisabledBrush}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- LabelTextBox 现代化样式 -->
    <Style x:Key="ModernLabelTextBoxControlStyle" TargetType="{x:Type input:LabelTextBox}">
        <Setter Property="Background" Value="{DynamicResource CardBackgroundFillColorDefaultBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource CardStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="MinHeight" Value="60"/>
        <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="LabelFontSize" Value="13"/>
        <Setter Property="LabelFontWeight" Value="SemiBold"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" 
                                  Direction="270" 
                                  ShadowDepth="2" 
                                  BlurRadius="8" 
                                  Opacity="0.1"/>
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type input:LabelTextBox}">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            Padding="{TemplateBinding Padding}"
                            CornerRadius="8"
                            Effect="{TemplateBinding Effect}">
                        <StackPanel>
                            <!-- 标签 -->
                            <TextBlock Text="{TemplateBinding LabelText}"
                                       FontSize="{TemplateBinding LabelFontSize}"
                                       FontWeight="{TemplateBinding LabelFontWeight}"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       TextWrapping="Wrap"
                                       Margin="0,0,0,4"/>
                            
                            <!-- 输入框 -->
                            <ui:TextBox x:Name="PART_TextBox"
                                        Text="{Binding Text, RelativeSource={RelativeSource TemplatedParent}, UpdateSourceTrigger=PropertyChanged}"
                                        PlaceholderText="{TemplateBinding PlaceholderText}"
                                        Background="Transparent"
                                        BorderThickness="0"
                                        Padding="0"
                                        Margin="0"
                                        MinHeight="{TemplateBinding TextBoxMinHeight}"
                                        IsReadOnly="{TemplateBinding IsReadOnly}"
                                        MaxLength="{TemplateBinding MaxLength}"
                                        FontSize="15"
                                        FontWeight="Normal"
                                        VerticalContentAlignment="Center"
                                        HorizontalContentAlignment="Left"/>
                        </StackPanel>
                    </Border>

                    <!-- 触发器 -->
                    <ControlTemplate.Triggers>
                            <!-- 多行模式 -->
                            <Trigger Property="IsMultiline" Value="True">
                                <Setter TargetName="PART_TextBox" Property="TextWrapping" Value="Wrap"/>
                                <Setter TargetName="PART_TextBox" Property="AcceptsReturn" Value="True"/>
                                <Setter TargetName="PART_TextBox" Property="VerticalScrollBarVisibility" Value="Auto"/>
                                <Setter TargetName="PART_TextBox" Property="VerticalContentAlignment" Value="Top"/>
                                <Setter Property="MinHeight" Value="100"/>
                            </Trigger>
                            
                            <!-- 鼠标悬停 -->
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="BorderBrush" Value="{DynamicResource SystemAccentColorSecondaryBrush}"/>
                                <Setter Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="Black" 
                                                          Direction="270" 
                                                          ShadowDepth="4" 
                                                          BlurRadius="12" 
                                                          Opacity="0.15"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            
                            <!-- 禁用状态 -->
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Opacity" Value="0.5"/>
                                <Setter Property="Background" Value="{DynamicResource ControlFillColorDisabledBrush}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- LabelTextBox 紧凑样式 -->
    <Style x:Key="CompactLabelTextBoxControlStyle" TargetType="{x:Type input:LabelTextBox}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="0,0,0,1"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="Margin" Value="2"/>
        <Setter Property="MinHeight" Value="32"/>
        <Setter Property="LabelFontSize" Value="12"/>
        <Setter Property="LabelFontWeight" Value="Normal"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type input:LabelTextBox}">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            Padding="{TemplateBinding Padding}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="{Binding LabelWidth, RelativeSource={RelativeSource TemplatedParent}}"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- 标签 -->
                            <TextBlock Grid.Column="0"
                                       Text="{TemplateBinding LabelText}"
                                       FontSize="{TemplateBinding LabelFontSize}"
                                       FontWeight="{TemplateBinding LabelFontWeight}"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       VerticalAlignment="Center"
                                       TextWrapping="Wrap"
                                       Margin="0,0,8,0"/>
                            
                            <!-- 输入框 -->
                            <ui:TextBox x:Name="PART_TextBox"
                                        Grid.Column="1"
                                        Text="{Binding Text, RelativeSource={RelativeSource TemplatedParent}, UpdateSourceTrigger=PropertyChanged}"
                                        PlaceholderText="{TemplateBinding PlaceholderText}"
                                        Background="Transparent"
                                        BorderThickness="0"
                                        Padding="0"
                                        Margin="0"
                                        MinHeight="{TemplateBinding TextBoxMinHeight}"
                                        IsReadOnly="{TemplateBinding IsReadOnly}"
                                        MaxLength="{TemplateBinding MaxLength}"
                                        VerticalContentAlignment="Center"
                                        HorizontalContentAlignment="Left"/>
                        </Grid>
                    </Border>

                    <!-- 触发器 -->
                    <ControlTemplate.Triggers>
                            <!-- 聚焦状态 -->
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
