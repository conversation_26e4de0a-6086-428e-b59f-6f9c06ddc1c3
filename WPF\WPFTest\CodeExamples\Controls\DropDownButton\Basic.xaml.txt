<!-- DropDownButton 基础用法示例 -->
<StackPanel Margin="20" Spacing="15">
    
    <!-- 标准 DropDownButton -->
    <GroupBox Header="标准 DropDownButton" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <!-- 基础文件操作 -->
            <ui:DropDownButton Content="文件操作" 
                               Command="{Binding HandleInteractionCommand}"
                               CommandParameter="文件操作"
                               Margin="8">
                <ui:DropDownButton.Flyout>
                    <ContextMenu>
                        <MenuItem Header="新建" Command="{Binding HandleInteractionCommand}" CommandParameter="新建"/>
                        <MenuItem Header="打开" Command="{Binding HandleInteractionCommand}" CommandParameter="打开"/>
                        <MenuItem Header="保存" Command="{Binding HandleInteractionCommand}" CommandParameter="保存"/>
                        <Separator/>
                        <MenuItem Header="退出" Command="{Binding HandleInteractionCommand}" CommandParameter="退出"/>
                    </ContextMenu>
                </ui:DropDownButton.Flyout>
            </ui:DropDownButton>

            <!-- 带图标的编辑操作 -->
            <ui:DropDownButton Content="编辑" 
                               Command="{Binding HandleInteractionCommand}"
                               CommandParameter="编辑"
                               Margin="8">
                <ui:DropDownButton.Icon>
                    <ui:SymbolIcon Symbol="Edit24"/>
                </ui:DropDownButton.Icon>
                <ui:DropDownButton.Flyout>
                    <ContextMenu>
                        <MenuItem Header="复制" Command="{Binding HandleInteractionCommand}" CommandParameter="复制">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="Copy24"/>
                            </MenuItem.Icon>
                        </MenuItem>
                        <MenuItem Header="剪切" Command="{Binding HandleInteractionCommand}" CommandParameter="剪切">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="Cut24"/>
                            </MenuItem.Icon>
                        </MenuItem>
                        <MenuItem Header="粘贴" Command="{Binding HandleInteractionCommand}" CommandParameter="粘贴">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="ClipboardPaste24"/>
                            </MenuItem.Icon>
                        </MenuItem>
                    </ContextMenu>
                </ui:DropDownButton.Flyout>
            </ui:DropDownButton>

            <!-- 禁用状态 -->
            <ui:DropDownButton Content="禁用状态" 
                               IsEnabled="False"
                               Margin="8">
                <ui:DropDownButton.Flyout>
                    <ContextMenu>
                        <MenuItem Header="选项1"/>
                        <MenuItem Header="选项2"/>
                    </ContextMenu>
                </ui:DropDownButton.Flyout>
            </ui:DropDownButton>
        </WrapPanel>
    </GroupBox>

    <!-- 不同外观样式 -->
    <GroupBox Header="不同外观样式" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <ui:DropDownButton Content="Primary" 
                               Appearance="Primary"
                               Command="{Binding HandleInteractionCommand}"
                               CommandParameter="Primary"
                               Margin="8">
                <ui:DropDownButton.Flyout>
                    <ContextMenu>
                        <MenuItem Header="主要操作1" Command="{Binding HandleInteractionCommand}" CommandParameter="主要操作1"/>
                        <MenuItem Header="主要操作2" Command="{Binding HandleInteractionCommand}" CommandParameter="主要操作2"/>
                    </ContextMenu>
                </ui:DropDownButton.Flyout>
            </ui:DropDownButton>

            <ui:DropDownButton Content="Secondary" 
                               Appearance="Secondary"
                               Command="{Binding HandleInteractionCommand}"
                               CommandParameter="Secondary"
                               Margin="8">
                <ui:DropDownButton.Flyout>
                    <ContextMenu>
                        <MenuItem Header="次要操作1" Command="{Binding HandleInteractionCommand}" CommandParameter="次要操作1"/>
                        <MenuItem Header="次要操作2" Command="{Binding HandleInteractionCommand}" CommandParameter="次要操作2"/>
                    </ContextMenu>
                </ui:DropDownButton.Flyout>
            </ui:DropDownButton>

            <ui:DropDownButton Content="Success" 
                               Appearance="Success"
                               Command="{Binding HandleInteractionCommand}"
                               CommandParameter="Success"
                               Margin="8">
                <ui:DropDownButton.Flyout>
                    <ContextMenu>
                        <MenuItem Header="确认操作" Command="{Binding HandleInteractionCommand}" CommandParameter="确认操作"/>
                        <MenuItem Header="完成任务" Command="{Binding HandleInteractionCommand}" CommandParameter="完成任务"/>
                    </ContextMenu>
                </ui:DropDownButton.Flyout>
            </ui:DropDownButton>

            <ui:DropDownButton Content="Danger" 
                               Appearance="Danger"
                               Command="{Binding HandleInteractionCommand}"
                               CommandParameter="Danger"
                               Margin="8">
                <ui:DropDownButton.Flyout>
                    <ContextMenu>
                        <MenuItem Header="删除操作" Command="{Binding HandleInteractionCommand}" CommandParameter="删除操作"/>
                        <MenuItem Header="危险操作" Command="{Binding HandleInteractionCommand}" CommandParameter="危险操作"/>
                    </ContextMenu>
                </ui:DropDownButton.Flyout>
            </ui:DropDownButton>
        </WrapPanel>
    </GroupBox>

</StackPanel>
