using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows.Media;
using Wpf.Ui.Appearance;
using Wpf.Ui.Controls;

namespace AlphaPM.ViewModels.Settings;

/// <summary>
/// 主题设置页面视图模型
/// </summary>
public partial class ThemeSettingsViewModel : ObservableObject, INavigationAware
{
    public readonly YLoggerInstance _logger = YLogger.ForSimple<ThemeSettingsViewModel>();
    /// <summary>
    /// 区域管理器
    /// </summary>
    private readonly IRegionManager _regionManager;

    /// <summary>
    /// 主题管理服务
    /// </summary>
    private readonly IThemeManagementService _themeService;

    /// <summary>
    /// 页面标题
    /// </summary>
    [ObservableProperty] private string pageTitle = "主题设置";

    /// <summary>
    /// 页面描述
    /// </summary>
    [ObservableProperty] private string pageDescription = "自定义应用程序的外观和主题";

    /// <summary>
    /// 当前主题模式
    /// </summary>
    [ObservableProperty] private ApplicationTheme currentTheme = ApplicationTheme.Unknown;

    /// <summary>
    /// 当前强调色
    /// </summary>
    [ObservableProperty] private Color currentAccentColor = Colors.Blue;

    /// <summary>
    /// RGB 红色值 (0-255)
    /// </summary>
    [ObservableProperty] private int redValue = 0;

    /// <summary>
    /// RGB 绿色值 (0-255)
    /// </summary>
    [ObservableProperty] private int greenValue = 120;

    /// <summary>
    /// RGB 蓝色值 (0-255)
    /// </summary>
    [ObservableProperty] private int blueValue = 212;

    /// <summary>
    /// 当前 RGB 颜色画刷
    /// </summary>
    [ObservableProperty] private SolidColorBrush currentRgbColor = new(Color.FromRgb(0, 120, 212));

    /// <summary>
    /// 当前 RGB 颜色十六进制值
    /// </summary>
    [ObservableProperty] private string currentRgbColorHex = "#0078D4";

    /// <summary>
    /// 主题模式集合
    /// </summary>
    [ObservableProperty] private ObservableCollection<ThemeModeItem> themeModes = new();

    /// <summary>
    /// 强调色集合
    /// </summary>
    [ObservableProperty] private ObservableCollection<AccentColorItem> accentColors = new();

    public ThemeSettingsViewModel(IRegionManager regionManager, IThemeManagementService themeService)
    {
        _regionManager = regionManager;
        _themeService = themeService;
        InitializeThemeModes();
        InitializeAccentColors();
        LoadCurrentSettings();

        // 监听 RGB 值变化
        PropertyChanged += OnPropertyChanged;
    }

    /// <summary>
    /// 属性变化事件处理
    /// </summary>
    private void OnPropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(ThemeSettingsViewModel.RedValue) ||
            e.PropertyName == nameof(ThemeSettingsViewModel.GreenValue) ||
            e.PropertyName == nameof(ThemeSettingsViewModel.BlueValue))
        {
            UpdateRgbColor();
        }
    }

    /// <summary>
    /// 更新 RGB 颜色显示
    /// </summary>
    private void UpdateRgbColor()
    {
        try
        {
            // 确保值在有效范围内
            var r = Math.Max(0, Math.Min((int)255, (int)RedValue));
            var g = Math.Max(0, Math.Min((int)255, (int)GreenValue));
            var b = Math.Max(0, Math.Min((int)255, (int)BlueValue));

            // 更新颜色
            var color = Color.FromRgb((byte)r, (byte)g, (byte)b);
            CurrentRgbColor = new SolidColorBrush(color);
            CurrentRgbColorHex = $"#{r:X2}{g:X2}{b:X2}";
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ RGB 颜色更新失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 初始化主题模式集合
    /// </summary>
    private void InitializeThemeModes()
    {
        ThemeModes = new ObservableCollection<ThemeModeItem>
        {
            new()
            {
                Name = "浅色",
                Description = "明亮清爽的界面风格",
                Theme = ApplicationTheme.Light,
                Icon = SymbolRegular.WeatherSunny24
            },
            new()
            {
                Name = "深色",
                Description = "护眼舒适的暗色主题",
                Theme = ApplicationTheme.Dark,
                Icon = SymbolRegular.WeatherMoon24
            },
            new()
            {
                Name = "跟随系统",
                Description = "自动适应系统设置",
                Theme = ApplicationTheme.Unknown,
                Icon = SymbolRegular.Desktop24
            }
        };
    }

    /// <summary>
    /// 初始化强调色集合
    /// </summary>
    private void InitializeAccentColors()
    {
        AccentColors = new ObservableCollection<AccentColorItem>
        {
            new() { Name = "蓝色", Color = Color.FromRgb(0, 120, 215) },
            new() { Name = "紫色", Color = Color.FromRgb(135, 100, 184) },
            new() { Name = "绿色", Color = Color.FromRgb(16, 137, 62) },
            new() { Name = "橙色", Color = Color.FromRgb(255, 140, 0) },
            new() { Name = "红色", Color = Color.FromRgb(232, 17, 35) },
            new() { Name = "粉色", Color = Color.FromRgb(232, 17, 91) },
            new() { Name = "青色", Color = Color.FromRgb(0, 183, 195) },
            new() { Name = "黄色", Color = Color.FromRgb(255, 185, 0) },
            new() { Name = "灰色", Color = Color.FromRgb(107, 105, 106) },
            new() { Name = "棕色", Color = Color.FromRgb(135, 100, 55) }
        };
    }

    /// <summary>
    /// 加载当前设置
    /// </summary>
    private void LoadCurrentSettings()
    {
        // 从主题管理服务获取当前设置
        var settings = _themeService.GetCurrentThemeSettings();
        if (settings != null)
        {
            CurrentTheme = settings.Theme;
            if (settings.AccentColor != null)
            {
                CurrentAccentColor = Color.FromArgb(
                    settings.AccentColor.A,
                    settings.AccentColor.R,
                    settings.AccentColor.G,
                    settings.AccentColor.B);
            }
        }
        else
        {
            // 如果没有保存的设置，使用当前系统设置
            CurrentTheme = ApplicationThemeManager.GetAppTheme();
        }

        // 设置初始选中状态
        foreach (var themeMode in ThemeModes)
        {
            themeMode.IsSelected = themeMode.Theme == CurrentTheme;
        }
    }

    /// <summary>
    /// 更改主题命令
    /// </summary>
    /// <param name="theme"></param>
    [RelayCommand]
    private void ChangeTheme(ApplicationTheme theme)
    {
        CurrentTheme = theme;
        _themeService.ApplyTheme(theme);

        // 更新选中状态
        foreach (var themeMode in ThemeModes)
        {
            themeMode.IsSelected = themeMode.Theme == theme;
        }

        // 保存设置
        _themeService.SaveThemeSettings(CurrentTheme, CurrentAccentColor);
    }

    /// <summary>
    /// 更改强调色命令
    /// </summary>
    /// <param name="color"></param>
    [RelayCommand]
    private void ChangeAccentColor(Color color)
    {
        CurrentAccentColor = color;
        _themeService.ApplyAccentColor(color);

        // 保存设置
        _themeService.SaveThemeSettings(CurrentTheme, CurrentAccentColor);
    }

    /// <summary>
    /// 导航到颜色展示页面
    /// </summary>
    [RelayCommand]
    private void NavigateToColorShowcase()
    {
        _regionManager.RequestNavigate("ContentRegion", "ColorShowcaseView");
    }

    /// <summary>
    /// 选择自定义颜色命令
    /// </summary>
    /// <param name="colorHex">十六进制颜色值，如 "#0078D4"</param>
    [RelayCommand]
    private void SelectCustomColor(string colorHex)
    {
        try
        {
            // 解析十六进制颜色
            var color = (Color)ColorConverter.ConvertFromString(colorHex);

            // 应用颜色
            CurrentAccentColor = color;
            _themeService.ApplyAccentColor(color);

            // 同步更新 RGB 滑块值
            RedValue = color.R;
            GreenValue = color.G;
            BlueValue = color.B;
            UpdateRgbColor();

            // 保存设置
            _themeService.SaveThemeSettings(CurrentTheme, CurrentAccentColor);

            // 记录日志
            Console.WriteLine($"🎨 自定义颜色已选择: {colorHex}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 颜色解析失败: {colorHex}, 错误: {ex.Message}");
        }
    }

    /// <summary>
    /// 应用 RGB 颜色命令
    /// </summary>
    [RelayCommand]
    private void ApplyRgbColor()
    {
        try
        {
            // 创建颜色
            var color = Color.FromRgb((byte)RedValue, (byte)GreenValue, (byte)BlueValue);

            // 应用颜色
            CurrentAccentColor = color;
            _themeService.ApplyAccentColor(color);

            // 保存设置
            _themeService.SaveThemeSettings(CurrentTheme, CurrentAccentColor);

            // 记录日志
            Console.WriteLine($"🎨 RGB 颜色已应用: R={RedValue}, G={GreenValue}, B={BlueValue}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ RGB 颜色应用失败: {ex.Message}");
        }
    }


    public void OnNavigatedTo(NavigationContext navigationContext)
    {
        LoadCurrentSettings();
    }

    public bool IsNavigationTarget(NavigationContext navigationContext) => true;

    public void OnNavigatedFrom(NavigationContext navigationContext)
    {
        // 清理逻辑
    }
}

/// <summary>
/// 主题模式项
/// </summary>
public partial class ThemeModeItem : ObservableObject
{
    /// <summary>
    /// 主题名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 主题描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 主题
    /// </summary>
    public ApplicationTheme Theme { get; set; }

    /// <summary>
    /// 图标
    /// </summary>
    public SymbolRegular Icon { get; set; }

    /// <summary>
    /// 是否选中
    /// </summary>
    [ObservableProperty] private bool isSelected;
}

/// <summary>
/// 强调色项
/// </summary>
public class AccentColorItem
{
    /// <summary>
    /// 强调色名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 强调色
    /// </summary>
    public Color Color { get; set; }

    /// <summary>
    /// 强调色画刷
    /// </summary>
    public SolidColorBrush Brush => new(Color);
}