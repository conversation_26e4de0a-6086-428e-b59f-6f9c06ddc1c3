using System.Windows;
using System.Windows.Controls;
using Wpf.Ui.Controls;

namespace Zylo.WPF.Controls.Input
{
    /// <summary>
    /// TextBlock + TextBox 组合控件
    /// 提供标签和输入框的组合，支持自定义宽度、样式等
    /// </summary>
    public class LabelTextBox : Control
    {
        static LabelTextBox()
        {
            DefaultStyleKeyProperty.OverrideMetadata(typeof(LabelTextBox), new FrameworkPropertyMetadata(typeof(LabelTextBox)));
        }

        #region 依赖属性

        /// <summary>
        /// 标签文本
        /// </summary>
        public static readonly DependencyProperty LabelTextProperty =
            DependencyProperty.Register(nameof(LabelText), typeof(string), typeof(LabelTextBox), 
                new PropertyMetadata(string.Empty));

        /// <summary>
        /// 标签文本
        /// </summary>
        public string LabelText
        {
            get => (string)GetValue(LabelTextProperty);
            set => SetValue(LabelTextProperty, value);
        }

        /// <summary>
        /// 标签宽度
        /// </summary>
        public static readonly DependencyProperty LabelWidthProperty =
            DependencyProperty.Register(nameof(LabelWidth), typeof(double), typeof(LabelTextBox), 
                new PropertyMetadata(100.0));

        /// <summary>
        /// 标签宽度
        /// </summary>
        public double LabelWidth
        {
            get => (double)GetValue(LabelWidthProperty);
            set => SetValue(LabelWidthProperty, value);
        }

        /// <summary>
        /// 输入框文本
        /// </summary>
        public static readonly DependencyProperty TextProperty =
            DependencyProperty.Register(nameof(Text), typeof(string), typeof(LabelTextBox), 
                new FrameworkPropertyMetadata(string.Empty, FrameworkPropertyMetadataOptions.BindsTwoWayByDefault));

        /// <summary>
        /// 输入框文本
        /// </summary>
        public string Text
        {
            get => (string)GetValue(TextProperty);
            set => SetValue(TextProperty, value);
        }

        /// <summary>
        /// 占位符文本
        /// </summary>
        public static readonly DependencyProperty PlaceholderTextProperty =
            DependencyProperty.Register(nameof(PlaceholderText), typeof(string), typeof(LabelTextBox), 
                new PropertyMetadata(string.Empty));

        /// <summary>
        /// 占位符文本
        /// </summary>
        public string PlaceholderText
        {
            get => (string)GetValue(PlaceholderTextProperty);
            set => SetValue(PlaceholderTextProperty, value);
        }

        /// <summary>
        /// 是否为多行输入
        /// </summary>
        public static readonly DependencyProperty IsMultilineProperty =
            DependencyProperty.Register(nameof(IsMultiline), typeof(bool), typeof(LabelTextBox), 
                new PropertyMetadata(false));

        /// <summary>
        /// 是否为多行输入
        /// </summary>
        public bool IsMultiline
        {
            get => (bool)GetValue(IsMultilineProperty);
            set => SetValue(IsMultilineProperty, value);
        }

        /// <summary>
        /// 是否为现代化样式
        /// </summary>
        public static readonly DependencyProperty IsModernStyleProperty =
            DependencyProperty.Register(nameof(IsModernStyle), typeof(bool), typeof(LabelTextBox), 
                new PropertyMetadata(false));

        /// <summary>
        /// 是否为现代化样式
        /// </summary>
        public bool IsModernStyle
        {
            get => (bool)GetValue(IsModernStyleProperty);
            set => SetValue(IsModernStyleProperty, value);
        }

        /// <summary>
        /// 输入框最小高度
        /// </summary>
        public static readonly DependencyProperty TextBoxMinHeightProperty =
            DependencyProperty.Register(nameof(TextBoxMinHeight), typeof(double), typeof(LabelTextBox), 
                new PropertyMetadata(32.0));

        /// <summary>
        /// 输入框最小高度
        /// </summary>
        public double TextBoxMinHeight
        {
            get => (double)GetValue(TextBoxMinHeightProperty);
            set => SetValue(TextBoxMinHeightProperty, value);
        }

        /// <summary>
        /// 是否只读
        /// </summary>
        public static readonly DependencyProperty IsReadOnlyProperty =
            DependencyProperty.Register(nameof(IsReadOnly), typeof(bool), typeof(LabelTextBox), 
                new PropertyMetadata(false));

        /// <summary>
        /// 是否只读
        /// </summary>
        public bool IsReadOnly
        {
            get => (bool)GetValue(IsReadOnlyProperty);
            set => SetValue(IsReadOnlyProperty, value);
        }

        /// <summary>
        /// 最大长度
        /// </summary>
        public static readonly DependencyProperty MaxLengthProperty =
            DependencyProperty.Register(nameof(MaxLength), typeof(int), typeof(LabelTextBox), 
                new PropertyMetadata(0));

        /// <summary>
        /// 最大长度
        /// </summary>
        public int MaxLength
        {
            get => (int)GetValue(MaxLengthProperty);
            set => SetValue(MaxLengthProperty, value);
        }

        /// <summary>
        /// 标签字体大小
        /// </summary>
        public static readonly DependencyProperty LabelFontSizeProperty =
            DependencyProperty.Register(nameof(LabelFontSize), typeof(double), typeof(LabelTextBox), 
                new PropertyMetadata(14.0));

        /// <summary>
        /// 标签字体大小
        /// </summary>
        public double LabelFontSize
        {
            get => (double)GetValue(LabelFontSizeProperty);
            set => SetValue(LabelFontSizeProperty, value);
        }

        /// <summary>
        /// 标签字体粗细
        /// </summary>
        public static readonly DependencyProperty LabelFontWeightProperty =
            DependencyProperty.Register(nameof(LabelFontWeight), typeof(FontWeight), typeof(LabelTextBox), 
                new PropertyMetadata(FontWeights.Medium));

        /// <summary>
        /// 标签字体粗细
        /// </summary>
        public FontWeight LabelFontWeight
        {
            get => (FontWeight)GetValue(LabelFontWeightProperty);
            set => SetValue(LabelFontWeightProperty, value);
        }

        #endregion

        #region 内部控件引用

        private Wpf.Ui.Controls.TextBox? _textBox;

        #endregion

        #region 重写方法

        public override void OnApplyTemplate()
        {
            base.OnApplyTemplate();

            // 获取模板中的TextBox引用
            _textBox = GetTemplateChild("PART_TextBox") as Wpf.Ui.Controls.TextBox;
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 聚焦到输入框
        /// </summary>
        public void FocusTextBox()
        {
            _textBox?.Focus();
        }

        /// <summary>
        /// 选择所有文本
        /// </summary>
        public void SelectAllText()
        {
            _textBox?.SelectAll();
        }

        /// <summary>
        /// 清空文本
        /// </summary>
        public void ClearText()
        {
            Text = string.Empty;
        }

        #endregion
    }
}
