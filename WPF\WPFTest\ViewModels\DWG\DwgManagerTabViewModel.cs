using System.Collections;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Data;
using WinForms = System.Windows.Forms;
using Wpf.Ui.Controls;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using GongSolutions.Wpf.DragDrop;
using ImTools;
using Microsoft.Win32;
using WPFTest.Models.DragDrop;
using WPFTest.Models.DWG;
using WPFTest.Services.DWG;
using WPFTest.ViewModels.DWG;
using WPFTest.Views.DWG;
using Zylo.YIO.Core;
using Zylo.YLog.Runtime;
using DwgFileModel = WPFTest.Models.DWG.DwgFileModel;

namespace WPFTest.ViewModels.DragDrop;

/// <summary>
/// 简化版DWG管理系统ViewModel - 专注核心功能
/// </summary>
/// <remarks>
/// 核心功能：
/// - 专业分类显示（顶部Tab）
/// - 文件类型分类（左侧列表）
/// /// - 文件列表显示（右侧表格）
/// - 文件搜索过滤
/// - 基础文件管理
/// - 集成DwgFileTypeEditorViewModel和DwgFolderEditorViewModel
/// </remarks>
public partial class DwgManagerTabViewModel : ObservableObject, IDragSource, IDropTarget, INavigationAware
{
    private readonly IDwgFileTypeService iDwgFileTypeService;
    private readonly IDwgFolderService iDwgFolderService;
    private readonly YLoggerInstance _logger = YLogger.ForProduction<DwgManagerTabViewModel>();
    private readonly DwgFileDragService _dragService = new();

    /// <summary>
    /// DWG文件夹路径
    /// </summary>
    public string DwgFolderPath { get; private set; } = string.Empty;


    #region 核心数据属性

    /// <summary>
    /// 专业Tab列表 - 顶部显示
    /// </summary>
    [ObservableProperty]
    [Description("专业Tab列表")]
    public partial ObservableCollection<DwgFolderModel> LDwgFolderModels { get; set; } = new();

    /// <summary>
    /// 专业Tab列表-选中的专业
    /// </summary>
    [ObservableProperty]
    [Description("专业Tab列表-选中的专业")]
    public partial DwgFolderModel? TDwgFolderModel { get; set; }

    /// <summary>
    /// 文件类型列表 - 左侧显示
    /// </summary>
    [ObservableProperty]
    [Description("文件类型列表 - 左侧显示")]
    [NotifyPropertyChangedFor(nameof(CountFileType))]
    public partial ObservableCollection<DwgFileTypeModel> LDwgFileTypeModels { get; set; } = new();


    /// <summary>
    /// 文件类型列表 - 左侧显示-选中
    /// </summary>
    [ObservableProperty]
    [Description("文件类型列表 - 左侧显示-选中")]
    public partial DwgFileTypeModel? TDwgFileTypeModel { get; set; }


    /// <summary>
    /// DWG集合
    /// </summary>
    [ObservableProperty]
    [Description("DWG集合")]
    [NotifyPropertyChangedFor(nameof(CountFile))]
    public partial ObservableCollection<DwgFileModel> LDwgFileModels { get; set; } = new();


    /// <summary>
    /// DWG选中
    /// </summary>
    [ObservableProperty]
    [Description("DWG选中")]
    public partial DwgFileModel? TDwgFileModel { get; set; }

    #endregion

    #region 属性变化处理

    /// <summary>
    /// TDwgFolderModel 属性变化时的处理
    /// </summary>
    partial void OnTDwgFolderModelChanged(DwgFolderModel? value)
    {
        _logger.Debug($"🔄 专业选择变化: {value?.Name ?? "null"}");

        // 当选择不同专业文件夹时，重新加载该文件夹下的文件
        if (value != null)
        {
            LoadDwgFiles(value.Name);
            UpdateCurrentFiles();
        }
    }

    #endregion

    #region 私有字段-DWG

    /// <summary>
    /// 分组视图源
    /// </summary>
    public CollectionViewSource GroupedFilesView { get; private set; }

    /// <summary>
    /// 所有文件的内部存储
    /// </summary>
    private readonly List<DwgFileModel> _allFiles = new();

    /// <summary>
    /// 专业图标映射
    /// </summary>
    private readonly Dictionary<string, string> _professionIcons = new()
    {
        { "建筑", "🏢" },
        { "结构", "🏗️" },
        { "暖通", "🌡️" },
        { "给排水", "💧" },
        { "电气", "⚡" },
        { "园林", "🌳" },
        { "装修", "🎨" }
    };

    /// <summary>
    /// 搜索关键词
    /// </summary>
    [ObservableProperty]
    [Description("搜索关键词")]
    public partial string SearchKeyword { get; set; } = string.Empty;

    /// <summary>
    /// 状态消息
    /// </summary>
    [ObservableProperty]
    [Description("状态消息")]
    public partial string StatusMessage { get; set; } = "请选择DWG文件夹";

    #endregion

    #region 计算属性

    /// <summary>
    /// 当前类型文件统计
    /// </summary>
    public string CountFileType => $"当前: {LDwgFileTypeModels.Count} 个文件";

    /// <summary>
    /// 总DWG文件统计
    /// </summary>
    public string CountFile => $"总计: {LDwgFileModels?.Count ?? 0} 个文件";


    /// <summary>
    /// 是否有文件类型数据
    /// </summary>
    public bool HasFileTypeData => LDwgFileModels?.Count > 0;

    /// <summary>
    /// 是否有文件夹数据
    /// </summary>
    public bool HasFolderData => LDwgFolderModels.Count > 0;

    /// <summary>
    /// 文件类型统计文本
    /// </summary>
    public string FileTypeCountText => $"文件类型: {LDwgFileTypeModels?.Count ?? 0} 个";

    /// <summary>
    /// 文件夹统计文本
    /// </summary>
    public string FolderCountText => $"文件夹: {LDwgFolderModels?.Count ?? 0} 个";

    #endregion

    #region 构造函数

    /// <summary>
    /// 构造函数 - 按正确顺序初始化ViewModel
    /// </summary>
    /// <param name="iDwgFileTypeService">DWG文件类型服务</param>
    /// <param name="iDwgFolderService">DWG文件夹服务</param>
    /// <param name="dwgFolderPath">DWG文件夹路径，如果为空则使用默认路径</param>
    public DwgManagerTabViewModel(IDwgFileTypeService iDwgFileTypeService, IDwgFolderService iDwgFolderService)
    {
        this.iDwgFileTypeService = iDwgFileTypeService;
        this.iDwgFolderService = iDwgFolderService;

        // 设置DWG文件夹路径
        DwgFolderPath = Path.Combine(GetProjectRoot(), "Assets", "DWG");

        _logger.Info($"🚀 开始初始化DWG管理系统，文件夹路径: {DwgFolderPath}");

        // 确保模板文件存在
        EnsureTemplateFileExists();

        // 步骤1：初始化服务和数据（最重要，其他组件依赖这些数据）
        InitializeServicesAndData();

        // 步骤2：初始化视图组件（依赖于数据加载）
        InitializeViewComponents();


        // 步骤3：完成初始化设置
        CompleteInitialization();
        UpdateCurrentFiles();
        _logger.Info("✅ DwgManagerTabViewModel 初始化完成");
    }

    /// <summary>
    /// 步骤1：初始化服务和数据 - 这是最关键的步骤
    /// </summary>
    private async Task InitializeServicesAndData()
    {
        try
        {
            _logger.Debug("🔧 服务解析完成");


            // 1.加载文件类型服务
            await LoadFileType();

            // 2.动态扫描并加载文件夹（基于实际文件系统）
            await ScanAndLoadFolders();


            _logger.Info("✅ 服务和数据初始化完成，数据将异步加载");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 服务初始化失败: {ex.Message}");
            StatusMessage = "服务初始化失败";
            throw; // 重新抛出异常，因为没有服务就无法正常工作
        }
    }


    /// <summary>
    /// 步骤2：初始化视图组件 - 依赖于数据加载
    /// </summary>
    private void InitializeViewComponents()
    {
        // 确保集合已初始化
        if (LDwgFileModels == null)
        {
            LDwgFileModels = new ObservableCollection<DwgFileModel>();
        }

        // 初始化分组视图源 - 这是WPF中用于数据绑定的重要组件
        // CollectionViewSource 提供了分组、排序、过滤等功能
        GroupedFilesView = new CollectionViewSource { Source = LDwgFileModels };

        // 添加分组描述：按 DrawingTypeName 属性进行分组
        // 这意味着具有相同 DrawingTypeName 的文件会被归为一组显示
        // 例如：所有"建筑图"文件会显示在一个组中，所有"结构图"文件会显示在另一个组中
        GroupedFilesView.GroupDescriptions.Add(new PropertyGroupDescription("Category"));

        // 添加过滤器：排除"全部"类型的文件
        // 这是因为"全部"是一个特殊的虚拟分类，用于显示所有文件
        // 在分组视图中不应该显示这个虚拟分类
        GroupedFilesView.Filter += OnGroupedFilesFilter;

        _logger.Debug("✅ 视图组件初始化完成");
    }

    /// <summary>
    /// 分组文件过滤器 - 排除"全部"类型
    /// </summary>
    private void OnGroupedFilesFilter(object sender, FilterEventArgs e)
    {
        if (e.Item is DwgFileModel file)
        {
            // 只接受非"全部"类型的文件
            // "全部"是一个虚拟分类，不应该在分组视图中显示
            e.Accepted = file.Category != "全部";
        }
        else
        {
            // 对于非DwgFileModel类型的项目，默认接受
            e.Accepted = true;
        }
    }


    /// <summary>
    /// 属性变化事件处理器
    /// </summary>
    private void OnPropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        switch (e.PropertyName)
        {
            case nameof(TDwgFileTypeModel):
                _logger.Debug($"🔄 文件类型选择变化: {TDwgFileTypeModel?.ChineseName}");
                UpdateCurrentFiles();
                break;

            case nameof(SearchKeyword):
                _logger.Debug($"🔄 搜索关键词变化: '{SearchKeyword}'");
                UpdateCurrentFiles();
                break;
        }
    }

    /// <summary>
    /// 完成初始化设置 - 设置子组件的事件监听和初始化数据
    /// </summary>
    private void CompleteInitialization()
    {
        // 监听属性变化 - 当用户选择不同的专业、文件类型或搜索关键词时更新文件列表
        PropertyChanged += OnPropertyChanged;

        _logger.Debug("✅ 事件处理器初始化完成");

        // 监听文件类型编辑器的选择变化
        if (TDwgFileTypeModel != null)
        {
            TDwgFileTypeModel.PropertyChanged += OnFileTypeEditorPropertyChanged;
        }

        // 注意：TDwgFolderModel 属性变化已经通过 OnPropertyChanged 方法处理
        // 不需要额外监听 TDwgFolderModel.PropertyChanged 事件

        // 初始化数据-加载DWG
        InitializeData();
    }

    #endregion


    #region 初始化方法

    /// <summary>
    /// 初始化数据
    /// </summary>
    private void InitializeData()
    {
        try
        {
            if (Directory.Exists(DwgFolderPath))
            {
                // 如果有选中的专业文件夹，加载该文件夹下的文件
                if (TDwgFolderModel != null)
                {
                    LoadDwgFiles(TDwgFolderModel.Name);
                    _logger.Info($"✅ 初始化加载专业文件夹: {TDwgFolderModel.Name}");
                }
                else
                {
                    // 没有选中的文件夹，显示提示信息
                    StatusMessage = "请选择专业文件夹";
                    _logger.Info("📂 等待用户选择专业文件夹");
                }
            }
            else
            {
                StatusMessage = "DWG文件夹不存在";
                _logger.Warning($"⚠️ DWG文件夹不存在: {DwgFolderPath}");
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 初始化失败: {ex.Message}");
            StatusMessage = "初始化失败";
        }
    }


    /// <summary>
    /// 获取项目根目录路径
    /// </summary>
    /// <returns>
    /// 返回包含 WPFTest.csproj 文件的目录路径。
    /// 如果未找到项目文件，则返回当前工作目录。
    /// </returns>
    /// <remarks>
    /// 此方法通过向上遍历目录树来查找项目根目录，直到找到 WPFTest.csproj 文件为止。
    /// 这种方法确保无论代码在哪个子目录中运行，都能正确定位到项目根目录。
    ///
    /// 使用场景：
    /// - 需要访问项目根目录下的配置文件
    /// - 需要获取相对于项目根目录的资源路径
    /// - 确保路径解析的一致性，不受运行时工作目录影响
    /// </remarks>
    private string GetProjectRoot()
    {
        var currentDirectory = Directory.GetCurrentDirectory(); // 获取当前目录
        var directory = new DirectoryInfo(currentDirectory);

        // 向上遍历目录树，查找包含 WPFTest.csproj 的目录
        while (directory != null)
        {
            if (File.Exists(Path.Combine(directory.FullName, "WPFTest.csproj")))
            {
                return directory.FullName;
            }

            directory = directory.Parent;
        }

        // 如果未找到项目文件，返回当前工作目录
        return currentDirectory;
    }

    #endregion


    #region 右键菜单命令

    /// <summary>
    /// 打开文件命令
    /// </summary>
    [RelayCommand]
    private void OpenFile(DwgFileModel? fileModel)
    {
        try
        {
            if (fileModel == null)
            {
                StatusMessage = "❌ 请选择要打开的文件";
                return;
            }

            _logger.Info($"📂 打开文件: {fileModel.FileName}");
            var success = _dragService.TestFileOpen(fileModel);

            if (success)
            {
                StatusMessage = $"✅ 已打开文件: {fileModel.FileName}";
            }
            else
            {
                StatusMessage = $"❌ 文件打开失败: {fileModel.FileName}";
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 打开文件失败: {ex.Message}");
            StatusMessage = $"❌ 打开失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 重命名文件命令
    /// </summary>
    [RelayCommand]
    private async Task RenameFileAsync(DwgFileModel? fileModel)
    {
        try
        {
            if (fileModel == null)
            {
                StatusMessage = "❌ 请选择要重命名的文件";
                return;
            }

            _logger.Info($"📝 重命名文件: {fileModel.FileName}");

            // 弹出重命名对话框
            var newName = await ShowRenameDialogAsync(fileModel.FileName);
            if (string.IsNullOrEmpty(newName) || newName == fileModel.FileName)
            {
                return;
            }

            // 简单处理：检查是否有.dwg后缀，有就去除，最后统一加上
            if (newName.EndsWith(".dwg", StringComparison.OrdinalIgnoreCase))
            {
                newName = newName.Substring(0, newName.Length - 4);
                _logger.Info($"📎 去除.dwg后缀: {newName}");
            }

            newName += ".dwg";
            _logger.Info($"📎 添加.dwg后缀: {newName}");

            var directory = Path.GetDirectoryName(fileModel.FullPath);
            var newPath = Path.Combine(directory!, newName);

            if (File.Exists(newPath))
            {
                StatusMessage = "❌ 文件名已存在";
                return;
            }

            // 带重试机制的文件重命名
            await RenameFileWithRetryAsync(fileModel.FullPath, newPath, newName);
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 重命名文件失败: {ex.Message}");
            StatusMessage = $"❌ 重命名失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 复制文件命令
    /// </summary>
    [RelayCommand]
    private async Task CopyFileAsync(DwgFileModel? fileModel)
    {
        try
        {
            if (fileModel == null)
            {
                StatusMessage = "❌ 请选择要复制的文件";
                return;
            }

            _logger.Info($"📋 复制文件: {fileModel.FileName}");

            var directory = Path.GetDirectoryName(fileModel.FullPath);

            // 使用FullPath确保文件名和扩展名处理正确
            var fileName = Path.GetFileNameWithoutExtension(fileModel.FullPath);
            var extension = Path.GetExtension(fileModel.FullPath);

            var copyName = $"{fileName}_副本{extension}";
            var copyPath = Path.Combine(directory!, copyName);

            // 如果副本已存在，添加数字后缀
            // int counter = 1;
            // while (File.Exists(copyPath))
            // {
            //     copyName = $"{fileName}_副本{counter}{extension}";
            //     copyPath = Path.Combine(directory!, copyName);
            //     counter++;
            // }

            if (await CopyDwgFileAsync(fileModel, directory!, copyName))
            {
                // 刷新文件列表
                if (TDwgFolderModel != null)
                {
                    LoadDwgFiles(TDwgFolderModel.Name);
                    UpdateCurrentFiles();
                }

                StatusMessage = $"✅ 文件已复制: {copyName}";
                _logger.Info($"✅ 文件复制成功: {fileModel.FileName} -> {copyName}");
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 复制文件失败: {ex.Message}");
            StatusMessage = $"❌ 复制失败: {ex.Message}";
        }
    }


    /// <summary>
    /// 复制建筑专业
    /// </summary>
    [RelayCommand]
    private async Task CopyFileJSAsync(DwgFileModel? fileModel)
    {
        try
        {
            if (fileModel == null)
            {
                StatusMessage = "❌ 请选择要复制的文件";
                return;
            }

            _logger.Info($"📋 复制文件: {fileModel.FileName}");

            var directory = Path.GetDirectoryName(fileModel.FullPath);

            // 使用FullPath确保文件名和扩展名处理正确
            var fileName = Path.GetFileNameWithoutExtension(fileModel.FullPath);
            var extension = Path.GetExtension(fileModel.FullPath);


            var type = LDwgFileTypeModels.FindFirst(x => x.ChineseName == "建筑图").PrefixArray[0];

            var copyName = $"{type}_建筑底图{extension}";
            var copyPath = Path.Combine(directory!, copyName);

            if (YFileEs.FileExists(copyPath))
            {
                YFileEs.DeleteFile(copyPath);
            }

            if (await CopyDwgFileAsync(fileModel, directory!, copyName, overwrite: true))
            {
                // 刷新文件列表
                if (TDwgFolderModel != null)
                {
                    LoadDwgFiles(TDwgFolderModel.Name);
                    UpdateCurrentFiles();
                }

                StatusMessage = $"✅ 文件已复制: {copyName}";
                _logger.Info($"✅ 文件复制成功: {fileModel.FileName} -> {copyName}");
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 复制文件失败: {ex.Message}");
            StatusMessage = $"❌ 复制失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 删除文件命令
    /// </summary>
    [RelayCommand]
    private async Task DeleteFileAsync(DwgFileModel? fileModel)
    {
        try
        {
            if (fileModel == null)
            {
                StatusMessage = "❌ 请选择要删除的文件";
                return;
            }

            _logger.Info($"🗑️ 删除文件: {fileModel.FileName}");

            // 确认删除
            var result = await ShowDeleteConfirmationAsync(fileModel.FileName);
            if (!result)
            {
                return;
            }

            await Task.Run(() => File.Delete(fileModel.FullPath));

            // 刷新文件列表
            if (TDwgFolderModel != null)
            {
                LoadDwgFiles(TDwgFolderModel.Name);
                UpdateCurrentFiles();
            }

            StatusMessage = $"✅ 文件已删除: {fileModel.FileName}";
            _logger.Info($"✅ 文件删除成功: {fileModel.FileName}");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 删除文件失败: {ex.Message}");
            StatusMessage = $"❌ 删除失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 复制到桌面命令
    /// </summary>
    [RelayCommand]
    private async Task CopyToDesktopAsync(DwgFileModel? fileModel)
    {
        try
        {
            if (fileModel == null)
            {
                StatusMessage = "❌ 请选择要复制的文件";
                return;
            }

            _logger.Info($"🖥️ 复制文件到桌面: {fileModel.FileName}");

            var desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);

            // 使用FullPath确保文件名和扩展名处理正确
            var fileName = Path.GetFileNameWithoutExtension(fileModel.FullPath);
            var extension = Path.GetExtension(fileModel.FullPath);

            // 默认添加"_副本"后缀
            var copyName = $"{fileName}{extension}";
            var targetPath = Path.Combine(desktopPath, copyName);

            // 如果桌面已存在同名文件，添加数字后缀
            // int counter = 1;
            // while (File.Exists(targetPath))
            // {
            //     copyName = $"{fileName}{counter}{extension}";
            //     targetPath = Path.Combine(desktopPath, copyName);
            //     counter++;
            // }

            if (await CopyDwgFileAsync(fileModel, desktopPath, copyName))
            {
                StatusMessage = $"✅ 文件已复制到桌面: {Path.GetFileName(targetPath)}";
                _logger.Info($"✅ 文件复制到桌面成功: {targetPath}");
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 复制文件到桌面失败: {ex.Message}");
            StatusMessage = $"❌ 复制到桌面失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 复制文件路径命令
    /// </summary>
    [RelayCommand]
    private void CopyFilePath(DwgFileModel? fileModel)
    {
        try
        {
            if (fileModel == null)
            {
                StatusMessage = "❌ 请选择文件";
                return;
            }

            Clipboard.SetText(fileModel.FullPath);
            StatusMessage = $"📁 已复制路径: {fileModel.FileName}";
            _logger.Info($"📁 复制文件路径: {fileModel.FullPath}");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 复制文件路径失败: {ex.Message}");
            StatusMessage = $"❌ 复制路径失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 新建DWG文件命令
    /// </summary>
    [RelayCommand]
    private async Task CreateNewDwgAsync()
    {
        try
        {
            if (TDwgFolderModel == null)
            {
                StatusMessage = "❌ 请先选择专业文件夹";
                return;
            }

            _logger.Info("➕ 创建新DWG文件");

            // 获取模板文件路径
            var templatePath = Path.Combine(GetProjectRoot(), "Assets", "DWG", "Drawing1.dwg");
            if (!File.Exists(templatePath))
            {
                StatusMessage = "❌ 模板文件不存在";
                _logger.Error($"模板文件不存在: {templatePath}");
                return;
            }

            // 生成新文件名
            var targetDirectory = Path.Combine(DwgFolderPath, TDwgFolderModel.Name);
            var newFileName = await GenerateNewFileNameAsync(targetDirectory, "新建图纸", ".dwg");
            var newFilePath = Path.Combine(targetDirectory, newFileName);

            if (await CopyDwgFileAsync(new DwgFileModel { FullPath = templatePath }, targetDirectory, newFileName))
            {
                // 刷新文件列表
                LoadDwgFiles(TDwgFolderModel.Name);
                UpdateCurrentFiles();
                StatusMessage = $"✅ 已创建新文件: {newFileName}";
                _logger.Info($"✅ 创建新DWG文件成功: {newFilePath}");
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 创建新DWG文件失败: {ex.Message}");
            StatusMessage = $"❌ 创建失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 基于模板创建命令
    /// </summary>
    [RelayCommand]
    private async Task CreateFromTemplateAsync()
    {
        try
        {
            if (TDwgFolderModel == null)
            {
                StatusMessage = "❌ 请先选择专业文件夹";
                return;
            }

            _logger.Info("📄 基于模板创建文件");

            // 选择模板文件
            var templatePath = await SelectTemplateFileAsync();
            if (string.IsNullOrEmpty(templatePath))
            {
                return;
            }

            // 生成新文件名
            var targetDirectory = Path.Combine(DwgFolderPath, TDwgFolderModel.Name);
            var templateName = Path.GetFileNameWithoutExtension(templatePath);
            var newFileName = await GenerateNewFileNameAsync(targetDirectory, $"{templateName}_副本", ".dwg");
            var newFilePath = Path.Combine(targetDirectory, newFileName);

            if (await CopyDwgFileAsync(new DwgFileModel { FullPath = templatePath }, targetDirectory, newFileName))
            {
                // 刷新文件列表
                LoadDwgFiles(TDwgFolderModel.Name);
                UpdateCurrentFiles();
                StatusMessage = $"✅ 已基于模板创建: {newFileName}";
                _logger.Info($"✅ 基于模板创建文件成功: {newFilePath}");
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 基于模板创建失败: {ex.Message}");
            StatusMessage = $"❌ 创建失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 在资源管理器中显示命令
    /// </summary>
    [RelayCommand]
    private void ShowInExplorer(DwgFileModel? fileModel)
    {
        try
        {
            if (fileModel == null)
            {
                StatusMessage = "❌ 请选择文件";
                return;
            }

            _logger.Info($"🔍 在资源管理器中显示: {fileModel.FileName}");

            // 打开资源管理器并选中文件
            Process.Start("explorer.exe", $"/select,\"{fileModel.FullPath}\"");

            StatusMessage = $"✅ 已在资源管理器中显示: {fileModel.FileName}";
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 在资源管理器中显示失败: {ex.Message}");
            StatusMessage = $"❌ 显示失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 显示文件属性命令 - 使用WPF-UI美观对话框
    /// </summary>
    [RelayCommand]
    private async void ShowFileProperties(DwgFileModel? fileModel)
    {
        try
        {
            if (fileModel == null)
            {
                StatusMessage = "❌ 请选择文件";
                return;
            }

            _logger.Info($"ℹ️ 显示文件属性: {fileModel.FileName}");

            // 获取文件信息
            var fileInfo = new FileInfo(fileModel.FullPath);
            var sizeInKB = fileInfo.Length / 1024.0;
            var sizeText = sizeInKB > 1024
                ? $"{sizeInKB / 1024.0:F2} MB"
                : $"{sizeInKB:F2} KB";

            var properties = $"📄 文件名: {fileInfo.Name}\n\n" +
                             $"📏 大小: {sizeText} ({fileInfo.Length:N0} 字节)\n\n" +
                             $"📅 创建时间: {fileInfo.CreationTime:yyyy-MM-dd HH:mm:ss}\n\n" +
                             $"✏️ 修改时间: {fileInfo.LastWriteTime:yyyy-MM-dd HH:mm:ss}\n\n" +
                             $"📁 路径: {fileInfo.FullName}";

            // 使用WPF-UI的MessageBox
            var messageBox = new Wpf.Ui.Controls.MessageBox
            {
                Title = $"文件属性 - {fileInfo.Name}",
                Content = properties,
                PrimaryButtonText = "确定",
                WindowStartupLocation = WindowStartupLocation.CenterOwner
            };

            // 设置主窗口为父窗口
            if (Application.Current.MainWindow != null)
            {
                messageBox.Owner = Application.Current.MainWindow;
            }

            await messageBox.ShowDialogAsync();

            StatusMessage = $"✅ 已显示文件属性: {fileModel.FileName}";
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 显示文件属性失败: {ex.Message}");
            StatusMessage = $"❌ 显示属性失败: {ex.Message}";
        }
    }


    #region 辅助函数

    /// <summary>
    /// 通用DWG文件复制方法。
    /// </summary>
    /// <param name="fileModel">要复制的 DWG 文件模型。如果为 null，则操作无效。</param>
    /// <param name="targetDirectory">目标文件夹路径。</param>
    /// <param name="targetFileName">目标文件名（包含扩展名）。</param>
    /// <param name="overwrite">是否覆盖已存在的目标文件，默认为 false。</param>
    /// <returns>复制成功返回 true，失败返回 false。</returns>
    private async Task<bool> CopyDwgFileAsync(
        DwgFileModel? fileModel,
        string targetDirectory,
        string targetFileName,
        bool overwrite = true)
    {
        if (fileModel == null)
        {
            StatusMessage = "❌ 请选择要复制的文件";
            return false;
        }

        try
        {
            var targetPath = Path.Combine(targetDirectory, targetFileName);

            if (File.Exists(targetPath))
            {
                if (!overwrite)
                {
                    StatusMessage = "❌ 目标文件已存在";
                    return false;
                }

                File.Delete(targetPath);
            }

            await Task.Run(() => File.Copy(fileModel.FullPath, targetPath));
            return true;
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 复制文件失败: {ex.Message}");
            StatusMessage = $"❌ 复制失败: {ex.Message}";
            return false;
        }
    }

    #endregion

    #endregion

    #region 基础命令

    /// <summary>
    /// 加载DWG根文件夹命令
    /// </summary>
    [RelayCommand]
    private void LoadFolder()
    {
        var dialog = new OpenFolderDialog { Title = "选择DWG根文件夹" };
        if (dialog.ShowDialog() == true)
        {
            // 更新DWG文件夹路径
            DwgFolderPath = dialog.FolderName;

            // 重新扫描专业文件夹
            ScanAndLoadFolders();

            // 如果有专业文件夹，加载第一个
            if (LDwgFolderModels.Count > 0)
            {
                TDwgFolderModel = LDwgFolderModels.First();
                _logger.Info($"📂 手动选择根文件夹: {dialog.FolderName}，默认加载: {TDwgFolderModel.Name}");
            }
            else
            {
                StatusMessage = "所选文件夹中没有专业子文件夹";
                _logger.Warning($"⚠️ 所选文件夹中没有专业子文件夹: {dialog.FolderName}");
            }
        }
    }

    /// <summary>
    /// 刷新命令
    /// </summary>
    [RelayCommand]
    private void Refresh()
    {
        InitializeData();
        _logger.Info("🔄 手动刷新数据");
    }

    /// <summary>
    /// 选择文件夹命令
    /// </summary>
    [RelayCommand]
    private void SelectFolder(DwgFolderModel folder)
    {
        if (folder != null)
        {
            // 直接设置选中项，ListBox 会自动处理 SelectedItem 绑定
            TDwgFolderModel = folder;

            _logger.Info($"🔄 用户选择文件夹: {folder.Name}");
        }
    }


    /// <summary>
    /// 创建测试DWG文件命令 - 解决拖拽禁止图标问题
    /// 🔧 当前的"DWG"文件实际上是文本文件，需要创建真正的DWG文件用于测试
    /// </summary>
    [RelayCommand]
    private void CreateTestDwgFiles()
    {
        try
        {
            _logger.Info("🔧 开始创建测试DWG文件...");

            if (string.IsNullOrEmpty(DwgFolderPath))
            {
                StatusMessage = "❌ 请先选择DWG根文件夹";
                return;
            }

            var createdFiles = new List<string>();
            var testFiles = new[]
            {
                ("建筑", "A_建筑平面图.dwg"),
                ("结构", "B_结构平面图.dwg"),
                ("设备", "C_设备平面图.dwg"),
                ("电气", "D_电气平面图.dwg")
            };

            foreach (var (folder, fileName) in testFiles)
            {
                var folderPath = Path.Combine(DwgFolderPath, folder);
                Directory.CreateDirectory(folderPath);

                var filePath = Path.Combine(folderPath, fileName);

                // 创建一个最小的有效DWG文件
                CreateMinimalDwgFile(filePath, fileName);
                createdFiles.Add(filePath);

                _logger.Info($"✅ 创建测试文件: {filePath}");
            }

            StatusMessage = $"✅ 成功创建 {createdFiles.Count} 个测试DWG文件";
            _logger.Info($"🎉 测试DWG文件创建完成，共 {createdFiles.Count} 个文件");

            // 刷新文件列表
            ScanAndLoadFolders();
            if (LDwgFolderModels.Count > 0)
            {
                TDwgFolderModel = LDwgFolderModels.First();
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 创建测试DWG文件失败: {ex.Message}");
            StatusMessage = $"❌ 创建失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 创建最小的有效DWG文件
    /// 🎯 创建一个具有正确DWG文件头的最小文件，可以被正确识别为DWG格式
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="fileName">文件名</param>
    private void CreateMinimalDwgFile(string filePath, string fileName)
    {
        try
        {
            // 创建一个最小的DWG文件结构
            // 使用AutoCAD 2018格式 (AC1032)
            var dwgHeader = new byte[]
            {
                // DWG文件头 "AC1032"
                0x41, 0x43, 0x31, 0x30, 0x33, 0x32,
                // 版本信息和基本结构
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                // 填充数据以达到最小文件大小
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
            };

            // 写入文件
            File.WriteAllBytes(filePath, dwgHeader);

            // 设置文件属性
            File.SetCreationTime(filePath, DateTime.Now);
            File.SetLastWriteTime(filePath, DateTime.Now);

            _logger.Debug($"✅ 创建DWG文件: {filePath} ({dwgHeader.Length} 字节)");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 创建DWG文件失败: {filePath}, 错误: {ex.Message}");
            throw;
        }
    }

    #endregion

    #region 事件处理

    /// <summary>
    /// 文件类型列表 - 左侧显示-选中 变化事件处理
    /// </summary>
    /// <param name="value"></param>
    partial void OnTDwgFileTypeModelChanged(DwgFileTypeModel? value)
    {
        _logger.Info($"🔄 TDwgFileTypeModel 变化: {value?.ChineseName ?? "null"}");
    }

    /// <summary>
    /// 文件类型编辑器属性变化处理
    /// </summary>
    private void OnFileTypeEditorPropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(TDwgFileTypeModel))
        {
            // 当左侧选中文件类型时，更新右侧文件列表
            UpdateCurrentFiles();
            _logger.Debug($"🔄 文件类型选择变化，更新文件列表");
        }
    }

    #endregion

    #region 测试前缀功能

    /// <summary>
    /// 测试前缀处理命令
    /// </summary>
    [RelayCommand]
    private void TestPrefixProcessing()
    {
        try
        {
            // 检查是否选择了文件
            if (TDwgFileModel == null)
            {
                _logger.Warning("⚠️ 前缀测试失败: 请先选择一个DWG文件");
                StatusMessage = "❌ 请先选择一个DWG文件";
                return;
            }

            // 自动查找"底图"类型作为目标类型
            var targetFileType = LDwgFileTypeModels.FirstOrDefault(ft => ft.ChineseName == "底图");
            if (targetFileType == null)
            {
                _logger.Error("❌ 前缀测试失败: 未找到'底图'文件类型");
                StatusMessage = "❌ 未找到'底图'文件类型";
                return;
            }

            _logger.Info($"🧪 开始前缀处理测试: {TDwgFileModel.FileName} → {targetFileType.ChineseName}");

            // 执行前缀处理测试
            var originalName = TDwgFileModel.FileName;
            var targetType = targetFileType.ChineseName;

            // 1. 去除旧前缀
            _logger.Debug($"🔍 调用RemovePrefix方法，输入: {originalName}");
            var baseName = TDwgFileModel.RemovePrefix(LDwgFileTypeModels);
            _logger.Debug($"📝 RemovePrefix返回结果: {originalName} → {baseName}");

            // 验证是否正确移除了所有前缀
            if (baseName.Contains("_") || baseName.Contains("-"))
            {
                _logger.Warning($"⚠️ 警告：结果中仍包含分隔符，可能还有未移除的前缀: {baseName}");
            }

            // 2. 添加新前缀
            var targetPrefix = targetFileType.PrefixArray.Length > 0 ? targetFileType.PrefixArray[0] : "";
            var newName = string.IsNullOrEmpty(targetPrefix) ? baseName : $"{targetPrefix}_{baseName}";
            _logger.Debug($"📝 添加前缀: {baseName} → {newName} (前缀: {targetPrefix})");

            // 记录详细结果
            _logger.Info($"✅ 前缀处理测试完成:");
            _logger.Info($"   原文件名: {originalName}");
            _logger.Info($"   去除前缀后: {baseName}");
            _logger.Info($"   目标类型: {targetType}");
            _logger.Info($"   目标前缀: {targetPrefix}");
            _logger.Info($"   新文件名: {newName}");

            StatusMessage = $"✅ 测试完成: {originalName} → {newName}";
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 前缀处理测试失败: {ex.Message}");
            StatusMessage = $"❌ 测试失败: {ex.Message}";
        }
    }

    #endregion


    #region 辅助方法

    /// <summary>
    /// 加载文件类型服务
    /// </summary>
    private async Task LoadFileType()
    {
        await iDwgFileTypeService.InitializeDatabaseAsync(); // 确保数据库已初始化
        var types = await iDwgFileTypeService.GetAllFileTypesAsync(); // 获取所有类型（包括禁用的）

        // 动态添加文件类型（不清空现有的，只添加新发现的）
        foreach (var type in types)
        {
            // 检查是否已存在该文件类型
            var existingType = LDwgFileTypeModels.FirstOrDefault(t => t.EnglishName == type.EnglishName);


            LDwgFileTypeModels.Add(type);
        }

        // 设置默认选中第一个启用的文件类型
        if (LDwgFileTypeModels.Count > 0)
        {
            TDwgFileTypeModel = LDwgFileTypeModels.FirstOrDefault(ft => ft.IsEnabled) ?? LDwgFileTypeModels.First();
            _logger.Info($"🎯 默认选中文件类型: {TDwgFileTypeModel.ChineseName}");
        }
    }

    /// <summary>
    /// 动态扫描文件系统中的专业文件夹
    /// </summary>
    private async Task ScanAndLoadFolders()
    {
        await iDwgFolderService.InitializeDatabaseAsync(); // 确保数据库已初始化
        var types = await iDwgFolderService.GetAllFoldersAsync(); // 获取所有类型（包括禁用的）

        // 动态添加文件类型（不清空现有的，只添加新发现的）
        foreach (var type in types)
        {
            LDwgFolderModels.Add(type);
            YDirectoryEs.CreateDirectory(Path.Combine(DwgFolderPath, type.Name));
        }

        try
        {
            if (!Directory.Exists(DwgFolderPath))
            {
                _logger.Warning($"⚠️ DWG文件夹不存在: {DwgFolderPath}");
                return;
            }

            // 扫描文件系统中的专业文件夹
            var professionFolders = Directory.GetDirectories(DwgFolderPath)
                .Select(Path.GetFileName)
                .Where(name => !string.IsNullOrEmpty(name))
                .OrderBy(name => name)
                .ToList();

            _logger.Info($"🔍 文件系统中发现 {professionFolders.Count} 个专业文件夹: {string.Join(", ", professionFolders)}");

            // 动态添加文件夹模型（不清空现有的，只添加新发现的）
            foreach (var professionName in professionFolders)
            {
                // 检查是否已存在该文件夹
                var existingFolder = LDwgFolderModels.FirstOrDefault(f => f.Name == professionName);

                if (existingFolder == null)
                {
                    // 不存在则创建新的文件夹模型
                    var folderModel = new DwgFolderModel
                    {
                        Name = professionName,
                        Icon = _professionIcons.GetValueOrDefault(professionName, "📁"),
                        IsEnabled = true,
                        SortOrder = LDwgFolderModels.Count + 1,
                        IsDefault = false
                    };

                    // // 检查是否已存在该文件类型
                    // var existingType = LDwgFolderModels.FirstOrDefault(t => t.Name == type.EnglishName);
                    //
                    // 添加到集合尾部
                    LDwgFolderModels.Add(folderModel);

                    _logger.Debug($"📂 新增文件夹: {professionName}");
                }
                else
                {
                    _logger.Debug($"📂 文件夹已存在: {professionName}");
                }
            }

            _logger.Info($"✅ 动态加载了 {LDwgFolderModels.Count} 个专业文件夹");

            // 3.设置默认选中第一个文件夹
            if (LDwgFolderModels.Count > 0)
            {
                var firstFolder = LDwgFolderModels.First();
                TDwgFolderModel = firstFolder;
                _logger.Info($"🎯 默认选中文件夹: {firstFolder.Name}");
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 扫描文件夹失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 加载指定专业文件夹下的DWG文件
    /// </summary>
    /// <param name="folderName">专业文件夹名称，如"建筑"、"结构"等</param>
    private void LoadDwgFiles(string folderName)
    {
        _allFiles.Clear();

        if (string.IsNullOrEmpty(folderName))
        {
            _logger.Warning("⚠️ 文件夹名称为空");
            return;
        }

        // 构建专业文件夹的完整路径
        var professionPath = Path.Combine(DwgFolderPath, folderName);

        if (!Directory.Exists(professionPath))
        {
            _logger.Warning($"⚠️ 专业文件夹不存在: {professionPath}");
            StatusMessage = $"文件夹不存在: {folderName}";
            return;
        }

        _logger.Info($"📂 开始加载专业文件夹: {folderName}");

        // 扫描该专业文件夹下的所有DWG文件
        var files = Directory.GetFiles(professionPath, "*.dwg", SearchOption.TopDirectoryOnly);

        _logger.Info($"� 在 {folderName} 中发现 {files.Length} 个DWG文件");

        // 加载文件并分析类型
        foreach (var filePath in files)
        {
            var file = DwgFileModel.FromFilePath(filePath);
            // 🎯 根据文件名分析文件类型（按左边的文件类型分类）
            file.Category = AnalyzeFileTypeFromDatabase(file.FileName);
            _allFiles.Add(file);
        }

        _logger.Info($"✅ 成功加载 {_allFiles.Count} 个DWG文件");

        // 构建文件类型统计
        BuildFileTypes();

        // 更新状态信息
        StatusMessage = $"已加载 {_allFiles.Count} 个文件";
    }

    /// <summary>
    /// 加载指定文件夹的DWG文件
    /// </summary>
    private void LoadFolderFiles(string folderPath)
    {
        _logger.Info($"🔍 尝试加载文件夹: {folderPath}");

        // 先清空当前文件列表和UI显示
        _allFiles.Clear();
        BuildFileTypes(); // 清空文件类型列表
        UpdateCurrentFiles(); // 清空当前显示的文件列表

        if (string.IsNullOrEmpty(folderPath))
        {
            _logger.Warning($"⚠️ 文件夹路径为空");
            StatusMessage = "文件夹路径为空";
            return;
        }

        // 如果文件夹不存在，尝试创建
        if (!Directory.Exists(folderPath))
        {
            _logger.Warning($"⚠️ 文件夹路径不存在: {folderPath}");

            try
            {
                Directory.CreateDirectory(folderPath);
                _logger.Info($"📁 已创建文件夹: {folderPath}");
                StatusMessage = $"已创建文件夹: {Path.GetFileName(folderPath)}";
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 创建文件夹失败: {folderPath} - {ex.Message}");
                StatusMessage = $"创建文件夹失败: {ex.Message}";
                return;
            }
        }

        _logger.Info($"📂 开始加载文件夹: {folderPath}");

        // 加载该文件夹下的所有DWG文件
        var files = Directory.GetFiles(folderPath, "*.dwg", SearchOption.AllDirectories);
        _logger.Info($"🔍 在文件夹中发现 {files.Length} 个DWG文件");

        foreach (var filePath in files)
        {
            var file = DwgFileModel.FromFilePath(filePath);
            // 🎯 使用数据库中的文件类型，而不是硬编码
            file.Category = AnalyzeFileTypeFromDatabase(file.FileName);
            _allFiles.Add(file);
            _logger.Debug($"📄 加载文件: {file.FileName} -> {file.Category}");
        }

        _logger.Info($"✅ 成功加载 {_allFiles.Count} 个DWG文件");

        // 重新构建文件类型列表
        BuildFileTypes();

        // 更新文件列表显示
        UpdateCurrentFiles();

        // 更新状态消息
        StatusMessage = files.Length > 0
            ? $"已加载 {_allFiles.Count} 个文件"
            : $"文件夹为空: {Path.GetFileName(folderPath)}";
    }

    /// <summary>
    /// 构建文件类型列表
    /// </summary>
    private void BuildFileTypes()
    {
        // 注意：现在文件类型列表来自数据库 LDwgFileTypeModels
        // 这里不需要重新构建，只需要更新文件计数

        foreach (var fileType in LDwgFileTypeModels)
        {
            // 计算该类型的文件数量
            var count = _allFiles.Count(f => f.Category == fileType.ChineseName);
            // 这里可以添加文件计数的更新逻辑，如果 DwgFileTypeModel 有相关属性的话
        }

        // 注意：默认选中逻辑已在 InitializeServicesAndData 中处理
    }

    /// <summary>
    /// 基于数据库的文件类型分析 - 简化版，直接使用正则提取并匹配
    /// </summary>
    private string AnalyzeFileTypeFromDatabase(string fileName)
    {
        try
        {
            if (string.IsNullOrEmpty(fileName))
                return "其他";

            // 如果文件类型数据还未加载，返回默认值
            if (LDwgFileTypeModels == null || LDwgFileTypeModels.Count == 0)
            {
                _logger.Warning($"⚠️ 文件类型数据未加载，使用默认分析: {fileName}");
                return AnalyzeFileTypeFallback(fileName);
            }

            var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);

            // 使用正则表达式提取前缀
            var prefixPattern = @"^[A-Za-z]{1,2}(?=[_-])";
            var match = Regex.Match(fileNameWithoutExtension, prefixPattern);

            if (match.Success)
            {
                var extractedPrefix = match.Value.ToUpper();

                // 直接在文件类型中查找匹配的前缀
                foreach (var fileType in LDwgFileTypeModels.OrderBy(ft => ft.SortOrder))
                {
                    if (fileType.PrefixArray.Any(prefix =>
                            string.Equals(prefix.TrimEnd('_', '-').ToUpper(), extractedPrefix,
                                StringComparison.Ordinal)))
                    {
                        _logger.Debug($"🎯 前缀匹配: {fileName} → {fileType.ChineseName} (前缀: {extractedPrefix})");
                        return fileType.ChineseName;
                    }
                }
            }

            // 未匹配到前缀，返回"其他"
            _logger.Debug($"❓ 未匹配到文件类型: {fileName} → 其他");
            return "其他";
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 文件类型分析失败: {fileName} - {ex.Message}");
            return "其他";
        }
    }

    /// <summary>
    /// 备用文件类型分析 - 当数据库不可用时使用，基于defaultTypes配置
    /// </summary>
    private string AnalyzeFileTypeFallback(string fileName)
    {
        var name = fileName.ToUpper();
        var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(name);

        // 基于defaultTypes的前缀匹配（按优先级排序）

        // 出图 (B_, B-)
        if (fileNameWithoutExtension.StartsWith("B_") || fileNameWithoutExtension.StartsWith("B-"))
            return "出图";

        // 底图 (DT_, DT-, 底图_)
        if (fileNameWithoutExtension.StartsWith("DT_") || fileNameWithoutExtension.StartsWith("DT-") ||
            fileNameWithoutExtension.StartsWith("底图_"))
            return "底图";

        // 模板图 (MB_, MB-, 模板_)
        if (fileNameWithoutExtension.StartsWith("MB_") || fileNameWithoutExtension.StartsWith("MB-") ||
            fileNameWithoutExtension.StartsWith("模板_"))
            return "模板图";

        // 变更图 (BG_, BG-, 变更_)
        if (fileNameWithoutExtension.StartsWith("BG_") || fileNameWithoutExtension.StartsWith("BG-") ||
            fileNameWithoutExtension.StartsWith("变更_"))
            return "变更图";

        // 计算书 (J_, J-)
        if (fileNameWithoutExtension.StartsWith("J_") || fileNameWithoutExtension.StartsWith("J-"))
            return "计算书";

        // 建筑图 (JS_, JS-, JZ_, JZ-)
        if (fileNameWithoutExtension.StartsWith("JS_") || fileNameWithoutExtension.StartsWith("JS-") ||
            fileNameWithoutExtension.StartsWith("JZ_") || fileNameWithoutExtension.StartsWith("JZ-"))
            return "建筑图";

        // 绑定 (B_, B-) - 注意：这与出图有重复，需要根据上下文判断
        // 这里我们优先判断为出图，如果需要更精确的判断，可以加入文件名内容分析

        // 图框 (T_, T-)
        if (fileNameWithoutExtension.StartsWith("T_") || fileNameWithoutExtension.StartsWith("T-"))
            return "图框";

        // 扩展匹配：基于常见的CAD文件前缀
        // 结构图
        if (fileNameWithoutExtension.StartsWith("JG_") || fileNameWithoutExtension.StartsWith("JG-"))
            return "结构图";

        // 电气图
        if (fileNameWithoutExtension.StartsWith("DQ_") || fileNameWithoutExtension.StartsWith("DQ-") ||
            fileNameWithoutExtension.StartsWith("E_") || fileNameWithoutExtension.StartsWith("E-"))
            return "电气图";

        // 给排水图
        if (fileNameWithoutExtension.StartsWith("WS_") || fileNameWithoutExtension.StartsWith("WS-") ||
            fileNameWithoutExtension.StartsWith("GS_") || fileNameWithoutExtension.StartsWith("GS-"))
            return "给排水图";

        // 暖通图
        if (fileNameWithoutExtension.StartsWith("H_") || fileNameWithoutExtension.StartsWith("H-") ||
            fileNameWithoutExtension.StartsWith("NT_") || fileNameWithoutExtension.StartsWith("NT-"))
            return "暖通图";

        // 智能识别：根据文件名内容判断
        if (fileNameWithoutExtension.Contains("结构") || fileNameWithoutExtension.Contains("基础") ||
            fileNameWithoutExtension.Contains("梁") || fileNameWithoutExtension.Contains("柱"))
            return "结构图";

        if (fileNameWithoutExtension.Contains("建筑") || fileNameWithoutExtension.Contains("平面") ||
            fileNameWithoutExtension.Contains("立面"))
            return "建筑图";

        if (fileNameWithoutExtension.Contains("电气") || fileNameWithoutExtension.Contains("配电"))
            return "电气图";

        if (fileNameWithoutExtension.Contains("给水") || fileNameWithoutExtension.Contains("排水") ||
            fileNameWithoutExtension.Contains("污水"))
            return "给排水图";

        if (fileNameWithoutExtension.Contains("暖通") || fileNameWithoutExtension.Contains("空调"))
            return "暖通图";

        return "其他";
    }

    /// <summary>
    /// 获取类型图标
    /// </summary>
    private string GetTypeIcon(string typeName)
    {
        return typeName switch
        {
            "全部" => "📁",
            "出图" => "📋",
            "施工图" => "🏗️",
            "模板图" => "📐",
            "底图" => "🗺️",
            "变更" => "🔄",
            "计算书" => "📊",
            "图框" => "🖼️",
            "建筑图" => "🏢",
            "绑定" => "🔗",
            "结构图" => "🏗️",
            "电气图" => "⚡",
            "给排水图" => "💧",
            "暖通图" => "🌡️",
            "污水图" => "🚰",
            "雨水图" => "🌧️",
            "交通图" => "🚗",
            "其他" => "📄",
            _ => "📄"
        };
    }

    /// <summary>
    /// 更新当前文件列表
    /// </summary>
    private void UpdateCurrentFiles()
    {
        // 确保集合已初始化
        if (LDwgFileModels == null)
        {
            LDwgFileModels = new ObservableCollection<DwgFileModel>();
        }

        LDwgFileModels.Clear();

        // 将 _allFiles 转换为可枚举类型，用于后续的过滤操作。
        // 这样做是为了可以使用 LINQ 扩展方法对文件列表进行筛选和处理。
        var files = _allFiles.AsEnumerable();

        // 根据左侧文件类型编辑器的选择过滤
        if (TDwgFileTypeModel != null)
        {
            var selectedType = TDwgFileTypeModel;

            // 如果选择的是"其他"类型，显示没有匹配任何前缀的文件
            if (selectedType.EnglishName == "Other")
            {
                // 获取所有已定义的前缀
                var allPrefixes = LDwgFileTypeModels
                    .Where(ft => ft.EnglishName != "All" && ft.EnglishName != "Other")
                    .SelectMany(ft =>
                        ft.Prefixes?.Split(',', StringSplitOptions.RemoveEmptyEntries) ?? Array.Empty<string>())
                    .Select(p => p.Trim().ToUpper())
                    .ToArray();

                // 过滤出没有匹配任何前缀的文件
                files = files.Where(f => !allPrefixes.Any(prefix => f.FileName.ToUpper().StartsWith(prefix)));
                _logger.Debug($"🔍 显示其他类型文件: 不匹配任何前缀的文件");
            }
            else if (selectedType.EnglishName != "All")
            {
                // 普通类型按前缀过滤
                var prefixes = selectedType.Prefixes?.Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(p => p.Trim().ToUpper())
                    .ToArray() ?? Array.Empty<string>();

                if (prefixes.Length > 0)
                {
                    files = files.Where(f => prefixes.Any(prefix => f.FileName.ToUpper().StartsWith(prefix)));
                    _logger.Debug($"🔍 按文件类型过滤: {selectedType.ChineseName}, 前缀: {string.Join(",", prefixes)}");
                }
            }
            // 如果选择"全部"，不进行过滤
        }

        // 专业过滤（仅在显示所有专业文件时使用）
        // 如果当前是单个文件夹模式，跳过专业过滤
        var isShowingAllProfessions = _allFiles.Any() &&
                                      _allFiles.Select(f =>
                                              Path.GetDirectoryName(f.FullPath)?.Split(Path.DirectorySeparatorChar)
                                                  .LastOrDefault())
                                          .Distinct().Count() > 1;

        if (TDwgFolderModel != null && isShowingAllProfessions)
        {
            var beforeCount = files.Count();
            files = files.Where(f => Path.GetDirectoryName(f.FullPath)?.EndsWith(TDwgFolderModel.Name) == true);
            var afterCount = files.Count();
            _logger.Debug($"🔍 专业过滤: {TDwgFolderModel.Name}, 过滤前: {beforeCount}, 过滤后: {afterCount}");

            if (afterCount == 0 && beforeCount > 0)
            {
                // 如果过滤后没有文件，记录一些路径信息用于调试
                var samplePath = files.FirstOrDefault()?.FullPath ?? _allFiles.FirstOrDefault()?.FullPath;
                _logger.Debug($"🔍 专业过滤失败，示例路径: {samplePath}");
            }
        }
        else if (TDwgFolderModel != null)
        {
            _logger.Debug($"🔍 跳过专业过滤（单文件夹模式）: {TDwgFolderModel.Name}");
        }

        // 搜索过滤
        if (!string.IsNullOrWhiteSpace(SearchKeyword))
        {
            var keyword = SearchKeyword.ToLower();
            files = files.Where(f => f.FileName.ToLower().Contains(keyword));
        }

        foreach (var file in files)
        {
            LDwgFileModels.Add(file);
        }

        // 刷新分组视图
        if (GroupedFilesView?.View != null)
        {
            GroupedFilesView.View.Refresh();

            // 自定义分组排序，按照文件类型的SortOrder排序
            if (GroupedFilesView.View is ListCollectionView listView)
            {
                listView.CustomSort = new FileTypeGroupComparer(LDwgFileTypeModels);
            }
        }

        // 更新计算属性
        OnPropertyChanged(nameof(CountFile));
        OnPropertyChanged(nameof(CountFileType));

        _logger.Debug($"🔄 更新文件列表: {LDwgFileModels.Count} 个文件");
    }

    #endregion


    #region 5. IDropTarget接口实现 - 支持拖拽改前缀

    /// <summary>
    /// 拖拽悬停处理 - 验证是否可以放置到文件类型上
    /// </summary>
    public void DragOver(IDropInfo dropInfo)
    {
        try
        {
            // 检查是否拖拽到文件类型项上
            if (dropInfo.TargetItem is DwgFileTypeModel targetFileType)
            {
                // 检查拖拽数据是否为DWG文件
                if (dropInfo.Data is DataObject dataObject && dataObject.GetDataPresent(DataFormats.FileDrop))
                {
                    var files = dataObject.GetData(DataFormats.FileDrop) as string[];
                    if (files != null && files.Any(f =>
                            Path.GetExtension(f).Equals(".dwg", StringComparison.OrdinalIgnoreCase)))
                    {
                        // 不允许拖拽到"全部"类型
                        if (targetFileType.EnglishName == "All")
                        {
                            dropInfo.Effects = System.Windows.DragDropEffects.None;
                            return;
                        }

                        // 允许拖拽，显示高亮效果
                        dropInfo.DropTargetAdorner = DropTargetAdorners.Highlight;
                        dropInfo.Effects = System.Windows.DragDropEffects.Copy;

                        _logger.Debug($"🎯 允许拖拽到文件类型: {targetFileType.ChineseName}");
                        return;
                    }
                }
            }

            if (dropInfo.TargetItem is DwgFolderModel targetFolder)
            {
                // 检查拖拽数据是否为DWG文件
                if (dropInfo.Data is DataObject dataObject && dataObject.GetDataPresent(DataFormats.FileDrop))
                {
                    var files = dataObject.GetData(DataFormats.FileDrop) as string[];
                    if (files != null && files.Any(f =>
                            Path.GetExtension(f).Equals(".dwg", StringComparison.OrdinalIgnoreCase)))
                    {
                        // 允许拖拽，显示高亮效果
                        dropInfo.DropTargetAdorner = DropTargetAdorners.Highlight;
                        dropInfo.Effects = System.Windows.DragDropEffects.Copy;

                        var mass = $"允许拖拽到文件夹: {targetFolder.Name}";
                        _logger.Debug(mass);
                        StatusMessage = mass;
                        return;
                    }
                }
            }

            // 其他情况不允许拖拽
            dropInfo.Effects = System.Windows.DragDropEffects.None;
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 拖拽悬停检查失败: {ex.Message}");
            dropInfo.Effects = System.Windows.DragDropEffects.None;
        }
    }

    /// <summary>
    /// 拖拽放置处理 - 执行前缀更改操作
    /// </summary>
    public void Drop(IDropInfo dropInfo)
    {
        try
        {
            // 验证拖拽数据
            if (dropInfo.Data is not DataObject dataObject || !dataObject.GetDataPresent(DataFormats.FileDrop))
            {
                _logger.Warning("❌ 拖拽数据不包含文件");
                return;
            }

            var files = dataObject.GetData(DataFormats.FileDrop) as string[];
            if (files == null || files.Length == 0)
            {
                _logger.Warning("❌ 没有找到拖拽的文件");
                return;
            }

            // 过滤出DWG文件
            var dwgFiles = files.Where(f => Path.GetExtension(f).Equals(".dwg", StringComparison.OrdinalIgnoreCase))
                .ToArray();
            if (dwgFiles.Length == 0)
            {
                _logger.Warning("❌ 没有找到DWG文件");
                return;
            }

 

            // 验证拖拽目标
            switch (dropInfo.TargetItem)
            {
                case DwgFileTypeModel targetFileType:
                    _logger.Info($"🎯 开始处理拖拽: {dwgFiles.Length} 个DWG文件 → {targetFileType.ChineseName}");
                    // 执行前缀更改
                    ProcessPrefixChange(dwgFiles, targetFileType);
                    break;
                case DwgFolderModel targetFolder:
                    _logger.Info($"🎯 开始处理拖拽: {targetFolder} 成功识别文件夹");
                    // 调用批量移动方法
                    _ = MoveDwgFilesToFolderAsync(dwgFiles, targetFolder);
                    break;
                default:
                    _logger.Warning("❌ 拖拽目标不是文件类型或文件夹");
                    return;
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 拖拽放置处理失败: {ex.Message}");
            StatusMessage = $"❌ 拖拽失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 处理前缀更改操作 - 真正重命名文件
    /// </summary>
    private void ProcessPrefixChange(string[] filePaths, DwgFileTypeModel targetFileType)
    {
        try
        {
            var results = new List<string>();
            var successCount = 0;
            var failedCount = 0;
            var errors = new List<string>();

            foreach (var filePath in filePaths)
            {
                try
                {
                    var fileName = Path.GetFileNameWithoutExtension(filePath);
                    var directory = Path.GetDirectoryName(filePath);
                    var extension = Path.GetExtension(filePath);

                    // 创建临时文件模型用于前缀处理
                    var tempFileModel = new DwgFileModel { FileName = fileName };

                    // 1. 去除旧前缀
                    var baseName = tempFileModel.RemovePrefix(LDwgFileTypeModels);

                    // 2. 添加新前缀
                    var targetPrefix = targetFileType.PrefixArray.Length > 0 ? targetFileType.PrefixArray[0] : "";
                    var newFileName = string.IsNullOrEmpty(targetPrefix) ? baseName : $"{targetPrefix}_{baseName}";
                    var newFilePath = Path.Combine(directory!, newFileName + extension);

                    // 3. 检查目标文件是否已存在
                    if (File.Exists(newFilePath))
                    {
                        var error = $"目标文件已存在: {newFileName}{extension}";
                        errors.Add(error);
                        failedCount++;
                        _logger.Warning($"⚠️ {error}");
                        continue;
                    }

                    // 4. 检查文件是否被占用
                    if (IsFileInUse(filePath))
                    {
                        var error = $"文件被占用: {fileName}{extension}";
                        errors.Add(error);
                        failedCount++;
                        _logger.Warning($"⚠️ {error}");
                        continue;
                    }

                    // 5. 执行文件重命名
                    File.Move(filePath, newFilePath);

                    // 6. 更新 _allFiles 中对应文件的信息
                    var fileInList = _allFiles.FirstOrDefault(f => f.FullPath == filePath);
                    if (fileInList != null)
                    {
                        fileInList.FullPath = newFilePath;
                        fileInList.FileName = newFileName;
                        fileInList.Category = targetFileType.ChineseName; // 更新文件类型
                        _logger.Debug($"🔄 更新文件列表中的文件信息: {fileName} → {newFileName}");
                    }

                    results.Add($"{fileName}{extension} → {newFileName}{extension}");
                    successCount++;

                    _logger.Info($"✅ 文件重命名成功: {fileName}{extension} → {newFileName}{extension}");
                }
                catch (UnauthorizedAccessException)
                {
                    var error = $"权限不足: {Path.GetFileName(filePath)}";
                    errors.Add(error);
                    failedCount++;
                    _logger.Error($"❌ {error}");
                }
                catch (IOException ex)
                {
                    var error = $"IO错误: {Path.GetFileName(filePath)} - {ex.Message}";
                    errors.Add(error);
                    failedCount++;
                    _logger.Error($"❌ {error}");
                }
                catch (Exception ex)
                {
                    var error = $"重命名失败: {Path.GetFileName(filePath)} - {ex.Message}";
                    errors.Add(error);
                    failedCount++;
                    _logger.Error($"❌ {error}");
                }
            }

            // 更新状态消息
            if (failedCount == 0)
            {
                StatusMessage = $"✅ 成功重命名 {successCount} 个文件";
            }
            else if (successCount == 0)
            {
                StatusMessage = $"❌ 重命名失败: {string.Join("; ", errors.Take(2))}{(errors.Count > 2 ? "..." : "")}";
            }
            else
            {
                StatusMessage = $"⚠️ 部分成功: {successCount} 个成功, {failedCount} 个失败";
            }

            // 记录详细结果
            _logger.Info($"🎉 文件重命名完成: {successCount} 成功, {failedCount} 失败");
            foreach (var result in results)
            {
                _logger.Info($"   ✅ {result}");
            }

            foreach (var error in errors)
            {
                _logger.Warning($"   ❌ {error}");
            }

            // 如果有文件被重命名，刷新文件列表
            if (successCount > 0)
            {
                RefreshFileList();
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 前缀更改处理失败: {ex.Message}");
            StatusMessage = $"❌ 前缀更改失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 检查文件是否被占用
    /// </summary>
    private static bool IsFileInUse(string filePath)
    {
        try
        {
            using var stream = File.Open(filePath, FileMode.Open, FileAccess.ReadWrite, FileShare.None);
            return false;
        }
        catch (IOException)
        {
            return true;
        }
        catch
        {
            return true;
        }
    }

    /// <summary>
    /// 刷新文件列表 - 简单更新UI显示
    /// </summary>
    private void RefreshFileList()
    {
        try
        {
            // 直接更新UI显示，因为 _allFiles 中的数据已经在重命名时更新了
            UpdateCurrentFiles();

            _logger.Info("🔄 文件列表UI已刷新");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 刷新文件列表失败: {ex.Message}");
        }
    }

    #endregion

    #region IDragSource 接口实现 - 支持拖拽到外部

    /// <summary>
    /// 检查是否可以开始拖拽
    /// </summary>
    public bool CanStartDrag(IDragInfo dragInfo)
    {
        // 检查是否有选中的文件且文件存在
        return TDwgFileModel != null &&
               !string.IsNullOrEmpty(TDwgFileModel.FullPath) &&
               File.Exists(TDwgFileModel.FullPath);
    }

    /// <summary>
    /// 开始拖拽操作 - 使用拖拽服务
    /// </summary>
    public void StartDrag(IDragInfo dragInfo)
    {
        try
        {
            if (TDwgFileModel == null)
            {
                _logger.Warning("❌ 没有选中的文件");
                return;
            }

            _logger.Info($"🚀 开始拖拽: {TDwgFileModel.FileName}");

            // 使用拖拽服务创建标准拖拽数据
            dragInfo.Data = _dragService.CreateDragDataObject(TDwgFileModel);
            dragInfo.Effects = System.Windows.DragDropEffects.Copy | System.Windows.DragDropEffects.Move;

            StatusMessage = $"🚀 正在拖拽: {TDwgFileModel.FileName}";
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 拖拽启动失败: {ex.Message}");
            StatusMessage = $"❌ 拖拽失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 检查鼠标是否在应用程序外部
    /// </summary>
    private bool IsMouseOutsideApplication(System.Drawing.Point mousePosition)
    {
        try
        {
            var mainWindow = Application.Current.MainWindow;
            if (mainWindow == null) return true;

            // 获取窗口边界
            var windowBounds = new System.Drawing.Rectangle(
                (int)mainWindow.Left,
                (int)mainWindow.Top,
                (int)mainWindow.Width,
                (int)mainWindow.Height
            );

            // 检查鼠标是否在窗口外部
            var isOutside = !windowBounds.Contains(mousePosition);

            _logger.Debug($"🔍 鼠标位置: {mousePosition}, 窗口边界: {windowBounds}, 外部拖拽: {isOutside}");

            return isOutside;
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 检查鼠标位置失败: {ex.Message}");
            return false; // 默认为内部拖拽
        }
    }

    /// <summary>
    /// 启动原生拖拽操作 - 按照Windows标准CF_HDROP格式
    /// </summary>
    private void StartNativeDragOperation()
    {
        try
        {
            if (TDwgFileModel == null) return;

            // 使用拖拽服务
            var result = _dragService.StartNativeDrag(TDwgFileModel, Application.Current.MainWindow);
            HandleNativeDragResult(result);
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ Windows标准拖拽失败: {ex.Message}");
            StatusMessage = $"❌ 拖拽失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 拖拽操作完成回调 - 智能处理内部/外部拖拽结果
    /// </summary>
    public void DragDropOperationFinished(System.Windows.DragDropEffects operationResult, IDragInfo dragInfo)
    {
        try
        {
            var fileName = TDwgFileModel != null ? Path.GetFileName(TDwgFileModel.FullPath) : "未知文件";

            // 🎯 关键修复：检查当前鼠标位置判断是否为外部拖拽
            var currentMousePos = System.Windows.Forms.Control.MousePosition;
            var isOutsideApp = IsMouseOutsideApplication(currentMousePos);

            _logger.Info($"🔍 拖拽完成位置检查: 鼠标位置({currentMousePos.X}, {currentMousePos.Y}), 外部拖拽: {isOutsideApp}");

            // 🎯 如果鼠标在应用程序外部，强制使用Windows API拖拽
            if (isOutsideApp)
            {
                _logger.Info($"🔄 检测到外部拖拽，启动Windows底层API");

                Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                {
                    _logger.Info($"🚀 开始Windows底层API拖拽: {fileName}");
                    var success = _dragService.StartWindowsApiDrag(TDwgFileModel);

                    if (success)
                    {
                        _logger.Info($"✅ Windows底层API拖拽成功: {fileName}");
                        StatusMessage = $"✅ 外部拖拽完成: {fileName}";
                    }
                    else
                    {
                        _logger.Warning($"❌ Windows底层API拖拽失败: {fileName}");
                        StatusMessage = $"❌ 外部拖拽失败: {fileName}";
                    }
                }));
                return;
            }

            // 🎯 应用程序内部拖拽 - 处理GongSolutions的结果
            if (operationResult == System.Windows.DragDropEffects.None)
            {
                _logger.Warning($"⚠️ 内部拖拽无效果: {fileName}");
                StatusMessage = $"⚠️ 拖拽取消: {fileName}";
                return;
            }

            // 处理成功的内部拖拽
            var message = operationResult switch
            {
                System.Windows.DragDropEffects.Copy => $"✅ 文件已复制: {fileName}",
                System.Windows.DragDropEffects.Move => $"✅ 文件已移动: {fileName}",
                _ => $"✅ 拖拽完成: {fileName}"
            };

            _logger.Info($"✅ 内部拖拽成功: {fileName} - {operationResult}");
            StatusMessage = message;
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 拖拽完成处理失败: {ex.Message}");
            StatusMessage = "❌ 拖拽处理失败";
        }
    }

    /// <summary>
    /// 拖拽被取消
    /// </summary>
    public void DragCancelled()
    {
        var fileName = TDwgFileModel != null ? Path.GetFileName(TDwgFileModel.FullPath) : "未知文件";
        _logger.Info($"🚫 拖拽被取消: {fileName}");
        StatusMessage = $"🚫 拖拽取消: {fileName}";
    }

    /// <summary>
    /// 拖拽完成后的处理（IDragSource接口要求）
    /// </summary>
    public void Dropped(IDropInfo dropInfo)
    {
        // 这个方法在拖拽完成后被调用
        var fileName = TDwgFileModel != null ? Path.GetFileName(TDwgFileModel.FullPath) : "未知文件";
        _logger.Info($"📍 拖拽已放置到外部: {fileName}");
    }

    /// <summary>
    /// 处理拖拽过程中的异常（IDragSource接口要求）
    /// </summary>
    public bool TryCatchOccurredException(Exception exception)
    {
        // 记录异常并返回true表示已处理
        _logger.Error($"❌ 拖拽过程中发生异常: {exception.Message}");
        StatusMessage = $"❌ 拖拽异常: {exception.Message}";
        return true; // 表示异常已被处理
    }

    #endregion

    #region 原生WPF拖拽 - 支持拖拽到外部应用程序

    /// <summary>
    /// 使用原生WPF拖拽到外部应用程序
    /// </summary>
    [RelayCommand]
    private void StartNativeDrag()
    {
        try
        {
            if (TDwgFileModel == null)
            {
                _logger.Warning("❌ 没有选中的文件");
                StatusMessage = "❌ 请先选择一个文件";
                return;
            }

            StatusMessage = $"🚀 开始原生拖拽: {TDwgFileModel.FileName}";

            // 使用拖拽服务
            var result = _dragService.StartNativeDrag(TDwgFileModel, Application.Current.MainWindow);

            // 处理拖拽结果
            HandleNativeDragResult(result);
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 原生拖拽失败: {ex.Message}");
            StatusMessage = $"❌ 拖拽失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 处理原生拖拽结果
    /// </summary>
    private void HandleNativeDragResult(System.Windows.DragDropEffects result)
    {
        var fileName = TDwgFileModel != null ? Path.GetFileName(TDwgFileModel.FullPath) : "未知文件";

        var (message, logLevel) = result switch
        {
            System.Windows.DragDropEffects.Copy => ($"✅ 文件已复制到外部: {fileName}", "Info"),
            System.Windows.DragDropEffects.Move => ($"✅ 文件已移动到外部: {fileName}", "Info"),
            System.Windows.DragDropEffects.None => ($"⚠️ 拖拽取消: {fileName}", "Warning"),
            _ => ($"✅ 拖拽完成: {fileName}", "Info")
        };

        if (logLevel == "Warning")
            _logger.Warning(message);
        else
            _logger.Info(message);

        StatusMessage = message;
    }


    /// <summary>
    /// DataGrid双击打开文件命令
    /// </summary>
    [RelayCommand]
    private void OpenFileOnDoubleClick(DwgFileModel? fileModel)
    {
        try
        {
            if (fileModel == null)
            {
                _logger.Warning("❌ 双击打开文件失败: 文件模型为空");
                return;
            }

            _logger.Info($"🖱️ 双击打开文件: {fileModel.FileName}");

            // 使用拖拽服务打开文件
            var success = _dragService.TestFileOpen(fileModel);

            if (success)
            {
                StatusMessage = $"✅ 已打开文件: {fileModel.FileName}";
                _logger.Info($"✅ 文件打开成功: {fileModel.FullPath}");
            }
            else
            {
                StatusMessage = $"❌ 文件打开失败: {fileModel.FileName}";
                _logger.Warning($"❌ 文件打开失败: {fileModel.FullPath}");
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 双击打开文件异常: {ex.Message}");
            StatusMessage = $"❌ 打开失败: {ex.Message}";
        }
    }

    #endregion

    #region 右键菜单辅助方法

    /// <summary>
    /// 显示重命名对话框 - 使用WPF-UI美观对话框
    /// </summary>
    /// <param name="currentName">当前文件名</param>
    /// <returns>新文件名，如果取消则返回null</returns>
    private async Task<string?> ShowRenameDialogAsync(string currentName)
    {
        return await Application.Current.Dispatcher.InvokeAsync(() =>
        {
            var dialog = new RenameFileDialog(currentName);

            // 设置主窗口为父窗口
            if (Application.Current.MainWindow != null)
            {
                dialog.Owner = Application.Current.MainWindow;
            }

            var result = dialog.ShowDialog();
            return result == true ? dialog.NewFileName : null;
        });
    }

    /// <summary>
    /// 显示删除确认对话框 - 使用WPF-UI美观对话框
    /// </summary>
    /// <param name="fileName">文件名</param>
    /// <returns>是否确认删除</returns>
    private async Task<bool> ShowDeleteConfirmationAsync(string fileName)
    {
        var messageBox = new Wpf.Ui.Controls.MessageBox
        {
            Title = "确认删除",
            Content = $"确定要删除文件 '{fileName}' 吗？\n\n⚠️ 此操作不可撤销！",
            PrimaryButtonText = "删除",
            WindowStartupLocation = WindowStartupLocation.CenterOwner
        };


        // 设置主窗口为父窗口
        if (Application.Current.MainWindow != null)
        {
            messageBox.Owner = Application.Current.MainWindow;
        }

        var result = await messageBox.ShowDialogAsync();
        return result == Wpf.Ui.Controls.MessageBoxResult.Primary;
    }

    /// <summary>
    /// 生成新文件名（避免重复）
    /// </summary>
    /// <param name="directory">目标目录</param>
    /// <param name="baseName">基础文件名</param>
    /// <param name="extension">文件扩展名</param>
    /// <returns>不重复的文件名</returns>
    private async Task<string> GenerateNewFileNameAsync(string directory, string baseName, string extension)
    {
        return await Task.Run(() =>
        {
            var fileName = $"{baseName}{extension}";
            var fullPath = Path.Combine(directory, fileName);

            if (!File.Exists(fullPath))
            {
                return fileName;
            }

            int counter = 1;
            do
            {
                fileName = $"{baseName}_{counter}{extension}";
                fullPath = Path.Combine(directory, fileName);
                counter++;
            } while (File.Exists(fullPath));

            return fileName;
        });
    }

    /// <summary>
    /// 选择模板文件
    /// </summary>
    /// <returns>模板文件路径，如果取消则返回null</returns>
    private async Task<string?> SelectTemplateFileAsync()
    {
        return await Task.Run(() =>
        {
            var dialog = new OpenFileDialog
            {
                Title = "选择模板文件",
                Filter = "DWG文件 (*.dwg)|*.dwg|所有文件 (*.*)|*.*",
                InitialDirectory = Path.Combine(GetProjectRoot(), "Assets", "DWG")
            };

            return dialog.ShowDialog() == true ? dialog.FileName : null;
        });
    }

    /// <summary>
    /// 确保Assets/DWG/Drawing1.dwg模板文件存在
    /// </summary>
    private void EnsureTemplateFileExists()
    {
        try
        {
            var templatePath = Path.Combine(GetProjectRoot(), "Assets", "DWG", "Drawing1.dwg");
            var templateDir = Path.GetDirectoryName(templatePath);

            // 确保目录存在
            if (!Directory.Exists(templateDir))
            {
                Directory.CreateDirectory(templateDir!);
                _logger.Info($"📁 创建模板目录: {templateDir}");
            }

            // 如果模板文件不存在，创建一个空的DWG文件
            if (!File.Exists(templatePath))
            {
                // 创建一个最小的DWG文件内容（这里简化处理）
                var templateContent = CreateMinimalDwgContent();
                File.WriteAllBytes(templatePath, templateContent);
                _logger.Info($"📄 创建模板文件: {templatePath}");
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 确保模板文件存在失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 创建最小的DWG文件内容
    /// </summary>
    /// <returns>DWG文件字节数组</returns>
    private byte[] CreateMinimalDwgContent()
    {
        // 这里返回一个最小的DWG文件头
        // 实际项目中应该使用真正的DWG模板文件
        var header = new byte[]
        {
            0x41, 0x43, 0x31, 0x30, 0x31, 0x38, // AC1018 (AutoCAD 2004)
            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
        };

        // 创建一个简单的文件内容
        var content = new byte[1024];
        Array.Copy(header, content, header.Length);

        return content;
    }

    /// <summary>
    /// 带重试机制的文件重命名
    /// </summary>
    /// <param name="oldPath">原文件路径</param>
    /// <param name="newPath">新文件路径</param>
    /// <param name="newName">新文件名（用于显示）</param>
    private async Task RenameFileWithRetryAsync(string oldPath, string newPath, string newName)
    {
        const int maxRetries = 3;
        const int delayMs = 1000;

        for (int attempt = 1; attempt <= maxRetries; attempt++)
        {
            try
            {
                _logger.Info($"🔄 尝试重命名文件 (第{attempt}次): {Path.GetFileName(oldPath)} -> {newName}");

                // 尝试重命名
                File.Move(oldPath, newPath);

                // 成功后刷新文件列表
                if (TDwgFolderModel != null)
                {
                    LoadDwgFiles(TDwgFolderModel.Name);
                    UpdateCurrentFiles();
                }

                StatusMessage = $"✅ 文件已重命名: {newName}";
                _logger.Info($"✅ 文件重命名成功: {Path.GetFileName(oldPath)} -> {newName}");
                return;
            }
            catch (IOException ex) when (ex.Message.Contains("being used by another process"))
            {
                _logger.Warning($"⚠️ 文件被占用 (第{attempt}次尝试): {ex.Message}");

                if (attempt == maxRetries)
                {
                    // 最后一次尝试失败，显示详细错误信息
                    await ShowFileInUseErrorAsync(newName);
                    throw;
                }

                // 等待后重试
                StatusMessage = $"⏳ 文件被占用，{delayMs / 1000}秒后重试... (第{attempt}/{maxRetries}次)";
                await Task.Delay(delayMs);
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.Error($"❌ 权限不足: {ex.Message}");
                StatusMessage = "❌ 重命名失败: 权限不足，请检查文件权限";
                throw;
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 重命名失败: {ex.Message}");
                StatusMessage = $"❌ 重命名失败: {ex.Message}";
                throw;
            }
        }
    }

    /// <summary>
    /// 显示文件被占用的错误对话框
    /// </summary>
    /// <param name="fileName">文件名</param>
    private async Task ShowFileInUseErrorAsync(string fileName)
    {
        var messageBox = new Wpf.Ui.Controls.MessageBox
        {
            Title = "文件被占用",
            Content = $"无法重命名文件 '{fileName}'。\n\n" +
                      "可能的原因：\n" +
                      "• 文件在CAD软件中打开\n" +
                      "• 文件被Windows资源管理器预览\n" +
                      "• 文件被其他程序使用\n\n" +
                      "请关闭相关程序后重试。",
            PrimaryButtonText = "确定",
            WindowStartupLocation = WindowStartupLocation.CenterOwner
        };

        // 设置主窗口为父窗口
        if (Application.Current.MainWindow != null)
        {
            messageBox.Owner = Application.Current.MainWindow;
        }

        await messageBox.ShowDialogAsync();
    }

    #endregion

    #region 导航参数和生命周期管理

    /// <summary>
    /// 导航到此页面时调用 - 处理导航参数和初始化
    /// </summary>
    /// <param name="navigationContext">导航上下文，包含传递的参数</param>
    /// <remarks>
    /// 🎯 主要功能：
    /// - 接收并处理导航参数（如指定的DWG文件夹路径）
    /// - 根据参数设置初始状态
    /// - 执行页面特定的初始化逻辑
    ///
    /// 📋 支持的导航参数：
    /// - "DwgFolderPath": 指定DWG根文件夹路径
    /// - "SelectedProfession": 默认选中的专业文件夹
    /// - "SelectedFileType": 默认选中的文件类型
    /// - "AutoLoad": 是否自动加载文件列表
    /// </remarks>
    public void OnNavigatedTo(NavigationContext navigationContext)
    {
        try
        {
            _logger.Info("🧭 导航到DWG管理页面");

            // 1. 处理DWG文件夹路径参数
            if (navigationContext.Parameters.ContainsKey("DwgFolderPath"))
            {
                var folderPath = navigationContext.Parameters["DwgFolderPath"]?.ToString();
                if (!string.IsNullOrEmpty(folderPath) && Directory.Exists(folderPath))
                {
                    DwgFolderPath = folderPath;
                    _logger.Info($"📂 设置DWG文件夹路径: {folderPath}");

                    // 重新扫描文件夹
                    _ = Task.Run(async () => await ScanAndLoadFolders());
                }
            }

            // 2. 处理默认选中的专业文件夹
            string? targetProfessionName = null;

            // 优先使用导航参数
            if (navigationContext.Parameters.ContainsKey("SelectedProfession"))
            {
                targetProfessionName = navigationContext.Parameters["SelectedProfession"]?.ToString();
            }
            // 如果没有导航参数，尝试使用上次保存的状态
            else if (LastNavigationState.ContainsKey("SelectedProfession"))
            {
                targetProfessionName = LastNavigationState["SelectedProfession"];
                _logger.Debug("📋 使用上次保存的专业选择");
            }

            if (!string.IsNullOrEmpty(targetProfessionName) && targetProfessionName != "未选择")
            {
                // 延迟设置，等待文件夹加载完成
                _ = Task.Run(async () =>
                {
                    await Task.Delay(1000); // 等待文件夹加载
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        var targetFolder = LDwgFolderModels.FirstOrDefault(f => f.Name == targetProfessionName);
                        if (targetFolder != null)
                        {
                            TDwgFolderModel = targetFolder;
                            _logger.Info($"🎯 设置默认专业: {targetProfessionName}");
                        }
                    });
                });
            }

            // 3. 处理默认选中的文件类型
            string? targetFileTypeName = null;

            // 优先使用导航参数
            if (navigationContext.Parameters.ContainsKey("SelectedFileType"))
            {
                targetFileTypeName = navigationContext.Parameters["SelectedFileType"]?.ToString();
            }
            // 如果没有导航参数，尝试使用上次保存的状态
            else if (LastNavigationState.ContainsKey("SelectedFileType"))
            {
                targetFileTypeName = LastNavigationState["SelectedFileType"];
                _logger.Debug("📋 使用上次保存的文件类型选择");
            }

            if (!string.IsNullOrEmpty(targetFileTypeName) && targetFileTypeName != "未选择")
            {
                _ = Task.Run(async () =>
                {
                    await Task.Delay(1500); // 等待文件类型加载
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        var targetFileType = LDwgFileTypeModels.FirstOrDefault(ft =>
                            ft.ChineseName == targetFileTypeName || ft.EnglishName == targetFileTypeName);
                        if (targetFileType != null)
                        {
                            TDwgFileTypeModel = targetFileType;
                            _logger.Info($"🎯 设置默认文件类型: {targetFileTypeName}");
                        }
                    });
                });
            }

            // 4. 处理自动加载参数
            if (navigationContext.Parameters.ContainsKey("AutoLoad"))
            {
                var autoLoad = navigationContext.Parameters["AutoLoad"];
                if (autoLoad is bool shouldAutoLoad && shouldAutoLoad)
                {
                    _logger.Info("🚀 启用自动加载模式");
                    // 可以在这里添加自动加载逻辑
                }
            }

            // 5. 记录导航完成
            StatusMessage = "页面加载完成";
            _logger.Info("✅ DWG管理页面导航完成");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 导航到页面时发生错误: {ex.Message}");
            StatusMessage = $"页面加载失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 判断是否可以重用当前实例进行导航
    /// </summary>
    /// <param name="navigationContext">导航上下文</param>
    /// <returns>true表示可以重用当前实例，false表示需要创建新实例</returns>
    /// <remarks>
    /// 🎯 重用策略：
    /// - 如果导航参数中的DWG文件夹路径与当前相同，则重用
    /// - 如果是相同的页面类型且没有特殊参数，则重用
    /// - 其他情况创建新实例以确保状态清洁
    ///
    /// 💡 性能考虑：
    /// - 重用实例可以避免重复初始化，提高性能
    /// - 但需要确保状态正确更新
    /// </remarks>
    public bool IsNavigationTarget(NavigationContext navigationContext)
    {
        try
        {
            // 1. 检查DWG文件夹路径参数
            if (navigationContext.Parameters.ContainsKey("DwgFolderPath"))
            {
                var targetPath = navigationContext.Parameters["DwgFolderPath"]?.ToString();
                var currentPath = DwgFolderPath;

                // 如果路径相同，可以重用
                var canReuse = string.Equals(targetPath, currentPath, StringComparison.OrdinalIgnoreCase);
                _logger.Debug($"🔄 路径比较 - 目标: {targetPath}, 当前: {currentPath}, 可重用: {canReuse}");
                return canReuse;
            }

            // 2. 检查是否有特殊的重置参数
            if (navigationContext.Parameters.ContainsKey("ForceNewInstance"))
            {
                var forceNew = navigationContext.Parameters["ForceNewInstance"];
                if (forceNew is bool shouldForceNew && shouldForceNew)
                {
                    _logger.Debug("🆕 强制创建新实例");
                    return false;
                }
            }

            // 3. 默认情况下重用当前实例
            _logger.Debug("♻️ 重用当前页面实例");
            return true;
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 判断导航目标时发生错误: {ex.Message}");
            // 出错时创建新实例，确保安全
            return false;
        }
    }

    /// <summary>
    /// 从此页面导航离开时调用 - 清理资源和记录状态
    /// </summary>
    /// <param name="navigationContext">导航上下文</param>
    /// <remarks>
    /// 🎯 清理任务：
    /// - 记录用户的当前选择状态（用于日志和统计）
    /// - 取消正在进行的异步操作
    /// - 释放不必要的资源
    /// - 记录用户行为数据
    ///
    /// 📝 注意：
    /// - NavigationContext.Parameters 是只读的，不能修改
    /// - 状态保存可以通过其他方式实现（如静态变量、配置文件等）
    /// - 主要用于清理和统计记录
    /// </remarks>
    public void OnNavigatedFrom(NavigationContext navigationContext)
    {
        try
        {
            _logger.Info("🚪 正在离开DWG管理页面");

            // 1. 记录当前状态（用于日志和统计）
            var currentState = new
            {
                SelectedProfession = TDwgFolderModel?.Name ?? "未选择",
                SelectedFileType = TDwgFileTypeModel?.ChineseName ?? "未选择",
                SearchKeyword = SearchKeyword ?? "无",
                FileCount = LDwgFileModels?.Count ?? 0,
                FolderCount = LDwgFolderModels?.Count ?? 0,
                DwgFolderPath = DwgFolderPath
            };

            _logger.Info($"💾 当前页面状态记录: " +
                         $"专业={currentState.SelectedProfession}, " +
                         $"文件类型={currentState.SelectedFileType}, " +
                         $"搜索词='{currentState.SearchKeyword}', " +
                         $"文件数={currentState.FileCount}, " +
                         $"文件夹数={currentState.FolderCount}");

            // 2. 保存状态到静态缓存（可选，供下次导航时参考）
            SaveLastNavigationState(currentState.SelectedProfession,
                currentState.SelectedFileType,
                currentState.SearchKeyword);

            // 3. 取消正在进行的异步操作
            // 注意：这里可以添加CancellationToken的取消逻辑
            // 如果有正在进行的文件加载操作，应该在这里取消

            // 4. 清理临时资源
            StatusMessage = "页面已离开";

            // 5. 记录用户行为统计
            _logger.Info($"📊 页面使用统计 - 文件: {currentState.FileCount}, 文件夹: {currentState.FolderCount}");

            _logger.Info("✅ DWG管理页面离开完成");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 离开页面时发生错误: {ex.Message}");
        }
    }

    /// <summary>
    /// 保存最后的导航状态到静态缓存
    /// </summary>
    /// <param name="profession">专业名称</param>
    /// <param name="fileType">文件类型</param>
    /// <param name="searchKeyword">搜索关键词</param>
    private static void SaveLastNavigationState(string profession, string fileType, string searchKeyword)
    {
        try
        {
            // 使用静态字典保存状态，供下次导航时参考
            LastNavigationState["SelectedProfession"] = profession;
            LastNavigationState["SelectedFileType"] = fileType;
            LastNavigationState["SearchKeyword"] = searchKeyword;
            LastNavigationState["SaveTime"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }
        catch (Exception ex)
        {
            // 静默处理保存错误，不影响主流程
            var logger = YLogger.ForSilent<DwgManagerTabViewModel>();
            logger.Warning($"⚠️ 保存导航状态失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 静态字典用于保存最后的导航状态
    /// </summary>
    private static readonly Dictionary<string, string> LastNavigationState = new();

    #endregion

    #region 辅助方法

    /// <summary>
    /// 显示文件覆盖确认对话框
    /// </summary>
    /// <param name="fileName">文件名</param>
    /// <returns>是否覆盖</returns>
    private async Task<bool> ShowOverwriteDialogAsync(string fileName)
    {
        var messageBox = new Wpf.Ui.Controls.MessageBox
        {
            Title = "文件已存在",
            Content = $"目标文件夹已存在同名文件 '{fileName}'，是否覆盖？",
            PrimaryButtonText = "覆盖",
            SecondaryButtonText = "跳过",
            WindowStartupLocation = WindowStartupLocation.CenterOwner
        };
        if (Application.Current.MainWindow != null)
            messageBox.Owner = Application.Current.MainWindow;
        var result = await messageBox.ShowDialogAsync();
        return result == Wpf.Ui.Controls.MessageBoxResult.Primary;
    }

    /// <summary>
    /// 批量移动DWG文件到目标文件夹，支持覆盖提示
    /// </summary>
    /// <param name="filePaths">要移动的DWG文件路径数组</param>
    /// <param name="targetFolder">目标文件夹对象</param>
    private async Task MoveDwgFilesToFolderAsync(string[] filePaths, DwgFolderModel targetFolder)
    {
        var targetDir = Path.Combine(DwgFolderPath, targetFolder.Name);
        Directory.CreateDirectory(targetDir);
        foreach (var srcPath in filePaths)
        {
            var fileName = Path.GetFileName(srcPath);
            var destPath = Path.Combine(targetDir, fileName);
            try
            {
                if (File.Exists(destPath))
                {
                    bool overwrite = await ShowOverwriteDialogAsync(fileName);
                    if (!overwrite)
                    {
                        _logger.Info($"跳过已存在文件: {fileName}");
                        continue;
                    }
                    File.Delete(destPath);
                }
                File.Move(srcPath, destPath);
                _logger.Info($"文件已移动: {fileName}");
            }
            catch (Exception ex)
            {
                _logger.Error($"移动文件失败: {fileName} - {ex.Message}");
            }
        }
        // 刷新界面
        LoadDwgFiles(TDwgFolderModel.Name);
        UpdateCurrentFiles();
        StatusMessage = "文件移动完成";
    }

    #endregion
}

#region 全新拖拽架构数据结构

/// <summary>
/// 拖拽结果类型枚举
/// </summary>
public enum DragResultType
{
    /// <summary>
    /// 未知结果
    /// </summary>
    Unknown,

    /// <summary>
    /// 外部拖拽（拖拽到桌面、其他应用程序）
    /// </summary>
    ExternalDrop,

    /// <summary>
    /// 内部拖拽（拖拽到应用程序内部控件）
    /// </summary>
    InternalDrop,

    /// <summary>
    /// 拖拽被取消
    /// </summary>
    Cancelled
}

#endregion

/// <summary>
/// 文件类型分组排序比较器
/// </summary>
public partial class FileTypeGroupComparer : IComparer
{
    private readonly ObservableCollection<DwgFileTypeModel>? _fileTypes;
    private readonly YLoggerInstance _logger = YLogger.ForSilent<FileTypeGroupComparer>();

    public FileTypeGroupComparer(ObservableCollection<DwgFileTypeModel>? fileTypes)
    {
        _fileTypes = fileTypes;
    }

    public int Compare(object? x, object? y)
    {
        if (x is DwgFileModel fileX && y is DwgFileModel fileY)
        {
            // 获取文件类型的SortOrder
            var sortOrderX = GetSortOrder(fileX.Category);
            var sortOrderY = GetSortOrder(fileY.Category);

            // 先按SortOrder排序，再按文件名排序
            var result = sortOrderX.CompareTo(sortOrderY);
            return result != 0
                ? result
                : string.Compare(fileX.FileName, fileY.FileName, StringComparison.OrdinalIgnoreCase);
        }

        return 0;
    }

    private int GetSortOrder(string typeName)
    {
        if (_fileTypes == null) return 999;

        var fileType = _fileTypes.FirstOrDefault(ft => ft.ChineseName == typeName);
        return fileType?.SortOrder ?? 999; // 未找到的类型排在最后
    }
}

#region Windows拖拽接口和实现

/// <summary>
/// Windows IDropSource 接口
/// </summary>
[ComImport, Guid("00000121-0000-0000-C000-000000000046"), InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
public interface IDropSource
{
    [PreserveSig]
    int QueryContinueDrag([MarshalAs(UnmanagedType.Bool)] bool fEscapePressed, uint grfKeyState);

    [PreserveSig]
    int GiveFeedback(uint dwEffect);
}

#endregion