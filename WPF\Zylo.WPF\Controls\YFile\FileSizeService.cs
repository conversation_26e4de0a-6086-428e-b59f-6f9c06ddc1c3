using System.Globalization;

namespace Zylo.WPF.Controls.YFile;

/// <summary>
/// 文件大小格式化服务
/// </summary>
public static class FileSizeService
{
    /// <summary>
    /// 文件大小单位
    /// </summary>
    private static readonly string[] SizeUnits = { "B", "KB", "MB", "GB", "TB", "PB" };

    /// <summary>
    /// 格式化文件大小
    /// </summary>
    /// <param name="bytes">字节数</param>
    /// <param name="decimalPlaces">小数位数</param>
    /// <returns>格式化后的文件大小字符串</returns>
    public static string FormatFileSize(long bytes, int decimalPlaces = 1)
    {
        if (bytes == 0) 
            return "0 B";

        if (bytes < 0)
            return "未知大小";

        var unitIndex = 0;
        var size = (double)bytes;

        while (size >= 1024 && unitIndex < SizeUnits.Length - 1)
        {
            size /= 1024;
            unitIndex++;
        }

        // 对于字节，不显示小数
        if (unitIndex == 0)
            decimalPlaces = 0;

        var format = $"F{decimalPlaces}";
        return $"{size.ToString(format, CultureInfo.InvariantCulture)} {SizeUnits[unitIndex]}";
    }

    /// <summary>
    /// 格式化文件大小（带详细信息）
    /// </summary>
    /// <param name="bytes">字节数</param>
    /// <returns>格式化后的文件大小字符串（包含字节数）</returns>
    public static string FormatFileSizeDetailed(long bytes)
    {
        var formatted = FormatFileSize(bytes);
        
        if (bytes >= 1024)
        {
            return $"{formatted} ({bytes:N0} 字节)";
        }
        
        return formatted;
    }

    /// <summary>
    /// 解析文件大小字符串为字节数
    /// </summary>
    /// <param name="sizeString">文件大小字符串</param>
    /// <returns>字节数，解析失败返回-1</returns>
    public static long ParseFileSize(string sizeString)
    {
        if (string.IsNullOrWhiteSpace(sizeString))
            return -1;

        var parts = sizeString.Trim().Split(' ');
        if (parts.Length != 2)
            return -1;

        if (!double.TryParse(parts[0], NumberStyles.Float, CultureInfo.InvariantCulture, out var size))
            return -1;

        var unit = parts[1].ToUpper();
        var unitIndex = Array.IndexOf(SizeUnits, unit);
        
        if (unitIndex == -1)
            return -1;

        return (long)(size * Math.Pow(1024, unitIndex));
    }

    /// <summary>
    /// 获取文件大小的排序权重
    /// </summary>
    /// <param name="bytes">字节数</param>
    /// <returns>排序权重</returns>
    public static long GetSortWeight(long bytes)
    {
        return bytes;
    }

    /// <summary>
    /// 比较两个文件大小
    /// </summary>
    /// <param name="bytes1">第一个文件的字节数</param>
    /// <param name="bytes2">第二个文件的字节数</param>
    /// <returns>比较结果</returns>
    public static int CompareFileSize(long bytes1, long bytes2)
    {
        return bytes1.CompareTo(bytes2);
    }

    /// <summary>
    /// 获取文件大小类别
    /// </summary>
    /// <param name="bytes">字节数</param>
    /// <returns>文件大小类别</returns>
    public static FileSizeCategory GetSizeCategory(long bytes)
    {
        return bytes switch
        {
            0 => FileSizeCategory.Empty,
            < 1024 => FileSizeCategory.Tiny,           // < 1 KB
            < 1024 * 1024 => FileSizeCategory.Small,   // < 1 MB
            < 10 * 1024 * 1024 => FileSizeCategory.Medium, // < 10 MB
            < 100 * 1024 * 1024 => FileSizeCategory.Large, // < 100 MB
            < 1024 * 1024 * 1024 => FileSizeCategory.VeryLarge, // < 1 GB
            _ => FileSizeCategory.Huge                  // >= 1 GB
        };
    }

    /// <summary>
    /// 计算传输时间估算
    /// </summary>
    /// <param name="bytes">文件大小（字节）</param>
    /// <param name="speedBytesPerSecond">传输速度（字节/秒）</param>
    /// <returns>传输时间估算</returns>
    public static TimeSpan EstimateTransferTime(long bytes, long speedBytesPerSecond)
    {
        if (speedBytesPerSecond <= 0)
            return TimeSpan.MaxValue;

        var seconds = (double)bytes / speedBytesPerSecond;
        return TimeSpan.FromSeconds(seconds);
    }
}

/// <summary>
/// 文件大小类别枚举
/// </summary>
public enum FileSizeCategory
{
    Empty,      // 0 字节
    Tiny,       // < 1 KB
    Small,      // < 1 MB
    Medium,     // < 10 MB
    Large,      // < 100 MB
    VeryLarge,  // < 1 GB
    Huge        // >= 1 GB
}
