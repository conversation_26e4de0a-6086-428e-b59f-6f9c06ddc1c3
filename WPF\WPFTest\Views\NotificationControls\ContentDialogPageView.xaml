<UserControl x:Class="WPFTest.Views.NotificationControls.ContentDialogPageView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:codeExample="clr-namespace:Zylo.WPF.Controls.CodeExample;assembly=Zylo.WPF"
             xmlns:viewmodels="clr-namespace:WPFTest.ViewModels.NotificationControls"
             xmlns:mvvm="http://prismlibrary.com/"
             xmlns:converters="clr-namespace:Zylo.WPF.Converters;assembly=Zylo.WPF"
             d:DataContext="{d:DesignInstance Type=viewmodels:ContentDialogPageViewModel}"
             mc:Ignorable="d"
             mvvm:ViewModelLocator.AutoWireViewModel="True"
             d:DesignHeight="3200" d:DesignWidth="1000">

    <UserControl.Resources>
        <!-- 转换器 -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
        <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter" />
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" 
                  HorizontalScrollBarVisibility="Disabled"
                  Padding="0,0,12,0">
        <StackPanel Margin="16">
            
            <!-- 页面标题 -->
            <Grid Margin="0,0,0,24">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <ui:SymbolIcon Grid.Column="0" 
                               Symbol="WindowNew24" 
                               FontSize="32" 
                               Margin="0,0,16,0"
                               VerticalAlignment="Center"
                               Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                
                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                    <TextBlock Text="ContentDialog 对话框控件"
                               FontSize="28"
                               FontWeight="Bold"
                               Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                    <TextBlock Text="WPF-UI 现代化对话框控件，结合 CommunityToolkit.Mvvm 展示最佳实践"
                               FontSize="14"
                               Margin="0,4,0,0"
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                               TextWrapping="Wrap"/>
                </StackPanel>
            </Grid>

            <!-- 基础对话框功能 -->
            <ui:CardExpander Header="🎯 基础对话框功能" IsExpanded="True" Margin="0,0,0,16">
                <StackPanel>
                    <TextBlock Text="展示 ContentDialog 的基础功能和不同对话框类型"
                               FontSize="14"
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                               Margin="0,0,0,16"/>
                    
                    <!-- 对话框控制面板 -->
                    <ui:Card Padding="20" Margin="0,0,0,16">
                        <StackPanel>
                            <TextBlock Text="📋 对话框控制面板：" FontWeight="Medium" Margin="0,0,0,16"/>
                            
                            <!-- 对话框配置 -->
                            <Border Background="{DynamicResource LayerFillColorDefaultBrush}"
                                    CornerRadius="8"
                                    Padding="16"
                                    Margin="0,0,0,16">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="120"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="120"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <!-- 标题设置 -->
                                    <TextBlock Grid.Row="0" Grid.Column="0" 
                                               Text="对话框标题：" 
                                               VerticalAlignment="Center"/>
                                    <ui:TextBox Grid.Row="0" Grid.Column="1" 
                                                Text="{Binding DialogTitle, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                PlaceholderText="输入对话框标题"
                                                Margin="0,0,16,8"/>
                                    
                                    <!-- 内容设置 -->
                                    <TextBlock Grid.Row="0" Grid.Column="2" 
                                               Text="对话框内容：" 
                                               VerticalAlignment="Center"/>
                                    <ui:TextBox Grid.Row="0" Grid.Column="3" 
                                                Text="{Binding DialogContent, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                PlaceholderText="输入对话框内容"
                                                Margin="0,0,0,8"/>
                                    
                                    <!-- 按钮配置 -->
                                    <TextBlock Grid.Row="1" Grid.Column="0" 
                                               Text="主要按钮：" 
                                               VerticalAlignment="Center"/>
                                    <ui:TextBox Grid.Row="1" Grid.Column="1" 
                                                Text="{Binding PrimaryButtonText, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                PlaceholderText="主要按钮文本"
                                                Margin="0,0,16,8"/>
                                    
                                    <TextBlock Grid.Row="1" Grid.Column="2" 
                                               Text="次要按钮：" 
                                               VerticalAlignment="Center"/>
                                    <ui:TextBox Grid.Row="1" Grid.Column="3" 
                                                Text="{Binding SecondaryButtonText, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                PlaceholderText="次要按钮文本"
                                                Margin="0,0,0,8"/>
                                    
                                    <!-- 关闭按钮 -->
                                    <TextBlock Grid.Row="2" Grid.Column="0" 
                                               Text="关闭按钮：" 
                                               VerticalAlignment="Center"/>
                                    <ui:TextBox Grid.Row="2" Grid.Column="1" 
                                                Text="{Binding CloseButtonText, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                PlaceholderText="关闭按钮文本"
                                                Margin="0,0,16,0"/>
                                    
                                    <!-- 默认按钮设置 -->
                                    <TextBlock Grid.Row="2" Grid.Column="2" 
                                               Text="默认按钮：" 
                                               VerticalAlignment="Center"/>
                                    <ComboBox Grid.Row="2" Grid.Column="3" 
                                              SelectedItem="{Binding DefaultButton, Mode=TwoWay}"
                                              ItemsSource="{Binding AvailableDefaultButtons}"/>
                                </Grid>
                            </Border>
                            
                            <!-- 操作按钮 -->
                            <WrapPanel>
                                <ui:Button Content="信息对话框"
                                           Icon="{ui:SymbolIcon Info24}"
                                           Command="{Binding ShowInfoDialogCommand}"
                                           Margin="0,0,8,8"/>
                                <ui:Button Content="确认对话框"
                                           Icon="{ui:SymbolIcon QuestionCircle24}"
                                           Command="{Binding ShowConfirmDialogCommand}"
                                           Margin="0,0,8,8"/>
                                <ui:Button Content="警告对话框"
                                           Icon="{ui:SymbolIcon Warning24}"
                                           Command="{Binding ShowWarningDialogCommand}"
                                           Margin="0,0,8,8"/>
                                <ui:Button Content="错误对话框"
                                           Icon="{ui:SymbolIcon ErrorCircle24}"
                                           Command="{Binding ShowErrorDialogCommand}"
                                           Margin="0,0,8,8"/>
                                <ui:Button Content="自定义对话框"
                                           Icon="{ui:SymbolIcon Settings24}"
                                           Command="{Binding ShowCustomDialogCommand}"
                                           Appearance="Secondary"
                                           Margin="0,0,8,8"/>
                            </WrapPanel>
                            
                            <!-- 结果显示 -->
                            <Border Background="{DynamicResource LayerFillColorDefaultBrush}"
                                    CornerRadius="8"
                                    Padding="16"
                                    Margin="0,16,0,0"
                                    Visibility="{Binding HasDialogResult, Converter={StaticResource BooleanToVisibilityConverter}}">
                                <StackPanel>
                                    <TextBlock Text="📊 对话框结果：" FontWeight="Medium" Margin="0,0,0,8"/>
                                    <TextBlock Text="{Binding DialogResultText}" 
                                               FontSize="12"
                                               Foreground="{Binding DialogResultBrush}"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </ui:Card>

                    <!-- 基础代码示例 -->
                    <codeExample:CodeExampleControl
                        Title="ContentDialog 基础用法"
                        Language="XAML"
                        Description="展示 ContentDialog 的基本 XAML 配置和 CommunityToolkit.Mvvm 集成"
                        ShowTabs="True"
                        XamlCode="{Binding BasicXamlExample}"
                        CSharpCode="{Binding BasicCSharpExample}"/>
                </StackPanel>
            </ui:CardExpander>

            <!-- CommunityToolkit.Mvvm 深度集成 -->
            <ui:CardExpander Header="🚀 CommunityToolkit.Mvvm 深度集成" IsExpanded="True" Margin="0,0,0,16">
                <StackPanel>
                    <TextBlock Text="展示如何使用 CommunityToolkit.Mvvm 的现代化特性来处理对话框交互"
                               FontSize="14"
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                               Margin="0,0,0,16"/>
                    
                    <!-- MVVM 特性演示 -->
                    <ui:Card Padding="20" Margin="0,0,0,16">
                        <StackPanel>
                            <TextBlock Text="⚡ MVVM 特性演示：" FontWeight="Medium" Margin="0,0,0,16"/>
                            
                            <!-- 异步命令演示 -->
                            <Border Background="{DynamicResource LayerFillColorDefaultBrush}"
                                    CornerRadius="8"
                                    Padding="16"
                                    Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="🔄 异步命令演示：" FontWeight="Medium" Margin="0,0,0,12"/>
                                    
                                    <WrapPanel>
                                        <ui:Button Content="异步加载对话框"
                                                   Icon="{ui:SymbolIcon ArrowDownload24}"
                                                   Command="{Binding ShowAsyncLoadingDialogCommand}"
                                                   Margin="0,0,8,8"/>
                                        <ui:Button Content="异步保存对话框"
                                                   Icon="{ui:SymbolIcon Save24}"
                                                   Command="{Binding ShowAsyncSaveDialogCommand}"
                                                   Margin="0,0,8,8"/>
                                        <ui:Button Content="取消异步操作"
                                                   Icon="{ui:SymbolIcon Dismiss24}"
                                                   Command="{Binding CancelAsyncOperationCommand}"
                                                   Appearance="Danger"
                                                   Margin="0,0,8,8"/>
                                    </WrapPanel>
                                    
                                    <!-- 进度显示 -->
                                    <StackPanel Margin="0,12,0,0" 
                                                Visibility="{Binding IsAsyncOperationRunning, Converter={StaticResource BooleanToVisibilityConverter}}">
                                        <TextBlock Text="{Binding AsyncOperationStatus}" 
                                                   FontSize="12"
                                                   Margin="0,0,0,8"/>
                                        <ProgressBar Value="{Binding AsyncOperationProgress}" 
                                                     Maximum="100" 
                                                     Height="6"
                                                     IsIndeterminate="{Binding IsAsyncOperationIndeterminate}"/>
                                    </StackPanel>
                                </StackPanel>
                            </Border>
                            
                            <!-- 数据验证演示 -->
                            <Border Background="{DynamicResource LayerFillColorDefaultBrush}"
                                    CornerRadius="8"
                                    Padding="16"
                                    Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="✅ 数据验证演示：" FontWeight="Medium" Margin="0,0,0,12"/>
                                    
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <StackPanel Grid.Column="0">
                                            <ui:TextBox Text="{Binding ValidationInput, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                        PlaceholderText="输入要验证的内容（至少3个字符）"
                                                        Margin="0,0,8,8"/>
                                            <TextBlock Text="{Binding ValidationErrors}" 
                                                       Foreground="Red"
                                                       FontSize="12"
                                                       Visibility="{Binding HasValidationErrors, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                        </StackPanel>
                                        
                                        <ui:Button Grid.Column="1"
                                                   Content="验证并显示对话框"
                                                   Icon="{ui:SymbolIcon CheckmarkCircle24}"
                                                   Command="{Binding ShowValidationDialogCommand}"
                                                   VerticalAlignment="Top"/>
                                    </Grid>
                                </StackPanel>
                            </Border>
                            
                            <!-- 消息传递演示 -->
                            <Border Background="{DynamicResource LayerFillColorDefaultBrush}"
                                    CornerRadius="8"
                                    Padding="16">
                                <StackPanel>
                                    <TextBlock Text="📡 消息传递演示：" FontWeight="Medium" Margin="0,0,0,12"/>
                                    
                                    <WrapPanel>
                                        <ui:Button Content="发送全局消息"
                                                   Icon="{ui:SymbolIcon Send24}"
                                                   Command="{Binding SendGlobalMessageCommand}"
                                                   Margin="0,0,8,8"/>
                                        <ui:Button Content="订阅消息对话框"
                                                   Icon="{ui:SymbolIcon MailRead24}"
                                                   Command="{Binding ShowMessageSubscriptionDialogCommand}"
                                                   Margin="0,0,8,8"/>
                                    </WrapPanel>
                                    
                                    <TextBlock Text="{Binding LastReceivedMessage}" 
                                               FontSize="12"
                                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                               Margin="0,8,0,0"
                                               Visibility="{Binding HasReceivedMessage, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </ui:Card>

                    <!-- MVVM 集成代码示例 -->
                    <codeExample:CodeExampleControl
                        Title="CommunityToolkit.Mvvm 深度集成"
                        Language="C#"
                        Description="展示异步命令、数据验证、消息传递等现代化 MVVM 特性"
                        ShowTabs="True"
                        XamlCode="{Binding MvvmXamlExample}"
                        CSharpCode="{Binding MvvmCSharpExample}"/>
                </StackPanel>
            </ui:CardExpander>

            <!-- 高级对话框功能 -->
            <ui:CardExpander Header="⚡ 高级对话框功能" IsExpanded="True" Margin="0,0,0,16">
                <StackPanel>
                    <TextBlock Text="展示 ContentDialog 的高级功能和自定义扩展能力"
                               FontSize="14"
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                               Margin="0,0,0,16"/>
                    
                    <!-- 高级功能演示 -->
                    <ui:Card Padding="20" Margin="0,0,0,16">
                        <StackPanel>
                            <TextBlock Text="🎨 高级功能演示：" FontWeight="Medium" Margin="0,0,0,16"/>
                            
                            <!-- 自定义内容对话框 -->
                            <Border Background="{DynamicResource LayerFillColorDefaultBrush}"
                                    CornerRadius="8"
                                    Padding="16"
                                    Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="🎭 自定义内容对话框：" FontWeight="Medium" Margin="0,0,0,12"/>
                                    
                                    <WrapPanel>
                                        <ui:Button Content="表单输入对话框"
                                                   Icon="{ui:SymbolIcon Form24}"
                                                   Command="{Binding ShowFormDialogCommand}"
                                                   Margin="0,0,8,8"/>
                                        <ui:Button Content="进度条对话框"
                                                   Icon="{ui:SymbolIcon Timer24}"
                                                   Command="{Binding ShowProgressDialogCommand}"
                                                   Margin="0,0,8,8"/>
                                        <ui:Button Content="列表选择对话框"
                                                   Icon="{ui:SymbolIcon List24}"
                                                   Command="{Binding ShowListSelectionDialogCommand}"
                                                   Margin="0,0,8,8"/>
                                        <ui:Button Content="图片预览对话框"
                                                   Icon="{ui:SymbolIcon Image24}"
                                                   Command="{Binding ShowImagePreviewDialogCommand}"
                                                   Margin="0,0,8,8"/>
                                    </WrapPanel>
                                </StackPanel>
                            </Border>
                            
                            <!-- 对话框链式调用 -->
                            <Border Background="{DynamicResource LayerFillColorDefaultBrush}"
                                    CornerRadius="8"
                                    Padding="16"
                                    Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="🔗 对话框链式调用：" FontWeight="Medium" Margin="0,0,0,12"/>
                                    
                                    <WrapPanel>
                                        <ui:Button Content="向导式对话框"
                                                   Icon="{ui:SymbolIcon Navigation24}"
                                                   Command="{Binding ShowWizardDialogCommand}"
                                                   Margin="0,0,8,8"/>
                                        <ui:Button Content="确认删除流程"
                                                   Icon="{ui:SymbolIcon Delete24}"
                                                   Command="{Binding ShowDeleteConfirmationFlowCommand}"
                                                   Margin="0,0,8,8"/>
                                        <ui:Button Content="设置配置流程"
                                                   Icon="{ui:SymbolIcon Settings24}"
                                                   Command="{Binding ShowSettingsFlowCommand}"
                                                   Margin="0,0,8,8"/>
                                    </WrapPanel>
                                </StackPanel>
                            </Border>
                            
                            <!-- 状态显示 -->
                            <Border Background="{DynamicResource LayerFillColorDefaultBrush}"
                                    CornerRadius="8"
                                    Padding="16">
                                <StackPanel>
                                    <TextBlock Text="📊 操作状态：" FontWeight="Medium" Margin="0,0,0,8"/>
                                    <TextBlock Text="{Binding OperationStatus}" 
                                               FontSize="12"
                                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </ui:Card>

                    <!-- 高级功能代码示例 -->
                    <codeExample:CodeExampleControl
                        Title="高级对话框功能实现"
                        Language="C#"
                        Description="展示自定义内容、链式调用、复杂交互等高级功能"
                        ShowTabs="True"
                        XamlCode="{Binding AdvancedXamlExample}"
                        CSharpCode="{Binding AdvancedCSharpExample}"/>
                </StackPanel>
            </ui:CardExpander>

        </StackPanel>
    </ScrollViewer>
</UserControl>
