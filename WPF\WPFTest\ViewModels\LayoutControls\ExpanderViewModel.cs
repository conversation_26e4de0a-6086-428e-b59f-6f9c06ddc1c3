using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Windows;
using System.IO;
using System.Reflection;
using System.Collections.ObjectModel;
using System.ComponentModel;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels.LayoutControls
{
    /// <summary>
    /// Expander 页面的 ViewModel，演示 Expander 控件的各种功能
    /// </summary>
    public partial class ExpanderViewModel : ObservableObject
    {
        private readonly YLoggerInstance _logger = YLogger.For<ExpanderViewModel>();

        #region 属性

        /// <summary>
        /// 交互次数
        /// </summary>
        [ObservableProperty]
        private int interactionCount = 0;

        /// <summary>
        /// 最后执行的操作
        /// </summary>
        [ObservableProperty]
        private string lastAction = "无操作";

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        private string statusMessage = "欢迎使用 Expander 示例库！";

        #region 代码示例属性

        /// <summary>
        /// 基础功能 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 基础功能 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级功能 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级功能 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 样式 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string StylesXamlExample { get; set; } = string.Empty;

        #endregion

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化 ExpanderViewModel
        /// </summary>
        public ExpanderViewModel()
        {
            try
            {
                _logger.Info("🚀 Expander 页面 ViewModel 开始初始化");

                StatusMessage = "Expander 示例库已加载，开始体验各种功能！";
                InitializeCodeExamples();

                _logger.Info("✅ Expander 页面 ViewModel 初始化完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ Expander 页面 ViewModel 初始化失败: {ex.Message}");
                StatusMessage = "初始化失败，请检查日志";
            }
        }

        #endregion

        #region 命令

        /// <summary>
        /// 通用交互命令
        /// </summary>
        /// <param name="parameter">操作参数</param>
        [RelayCommand]
        private void HandleInteraction(string? parameter)
        {
            try
            {
                InteractionCount++;
                LastAction = parameter ?? "未知操作";
                
                _logger.Info($"🎯 用户交互操作: {parameter}");
                _logger.Info($"🔢 当前交互次数: {InteractionCount}");

                // 根据不同的操作显示不同的状态消息
                StatusMessage = parameter switch
                {
                    "标准Expander按钮" => "🎯 点击了标准 Expander 中的按钮",
                    "子级Expander1" => "🎯 点击了子级 Expander 1 中的按钮",
                    "自定义样式左侧" => "🎯 点击了自定义样式 Expander 左侧按钮",
                    "自定义样式右侧" => "🎯 点击了自定义样式 Expander 右侧按钮",
                    _ => $"🎯 执行了操作: {parameter}"
                };
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 处理用户交互失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 重置计数命令
        /// </summary>
        [RelayCommand]
        private void ResetCount()
        {
            InteractionCount = 0;
            StatusMessage = "计数已重置";
            _logger.Info("重置了交互计数");
        }

        /// <summary>
        /// 清除状态命令
        /// </summary>
        [RelayCommand]
        private void ClearStatus()
        {
            LastAction = "无操作";
            StatusMessage = "状态已清除";
            _logger.Info("清除了状态信息");
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化代码示例
        /// </summary>
        private void InitializeCodeExamples()
        {
            try
            {
                // 获取代码示例文件的基础路径
                var baseDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
                var codeExamplesPath = Path.Combine(baseDirectory, "CodeExamples", "LayoutControls", "Expander");

                _logger.Info($"基础目录: {baseDirectory}");
                _logger.Info($"代码示例路径: {codeExamplesPath}");
                _logger.Info($"目录是否存在: {Directory.Exists(codeExamplesPath)}");

                // 读取各种示例文件
                BasicXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.xaml.txt"));
                BasicCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.cs.txt"));
                AdvancedXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Advanced.xaml.txt"));
                AdvancedCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Advanced.cs.txt"));
                StylesXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Styles.xaml.txt"));

                _logger.Info("Expander 代码示例加载完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"加载 Expander 代码示例失败: {ex.Message}");
                SetDefaultCodeExamples();
            }
        }

        /// <summary>
        /// 读取代码示例文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件内容</returns>
        private string ReadCodeExampleFile(string filePath)
        {
            _logger.Info($"尝试读取文件: {filePath}");
            
            if (File.Exists(filePath))
            {
                var content = File.ReadAllText(filePath);
                _logger.Info($"成功读取文件，内容长度: {content.Length}");
                return content;
            }
            else
            {
                _logger.Warning($"代码示例文件不存在: {filePath}");
                return $"// 代码示例文件不存在: {Path.GetFileName(filePath)}\n// 完整路径: {filePath}";
            }
        }

        /// <summary>
        /// 设置默认代码示例（当文件读取失败时使用）
        /// </summary>
        private void SetDefaultCodeExamples()
        {
            BasicXamlExample = @"<!-- Expander 基础示例 -->
<Expander Header=""标准 Expander"" 
          IsExpanded=""False""
          Background=""{DynamicResource ControlFillColorDefaultBrush}""
          BorderBrush=""{DynamicResource ControlStrokeColorDefaultBrush}""
          BorderThickness=""1"">
    <StackPanel Padding=""16"">
        <TextBlock Text=""这是 Expander 的内容区域。""/>
        <Button Content=""示例按钮"" HorizontalAlignment=""Left""/>
    </StackPanel>
</Expander>";

            BasicCSharpExample = @"// Expander C# 基础示例
// Expander 是展开/折叠控件，用于节省界面空间

using System.Windows;
using System.Windows.Controls;

namespace WPFTest.Examples.LayoutControls
{
    public class ExpanderBasicExample
    {
        /// <summary>
        /// 创建基础 Expander 控件
        /// </summary>
        public static Expander CreateBasicExpander()
        {
            var expander = new Expander
            {
                Header = ""基础 Expander"",
                IsExpanded = false,
                Margin = new Thickness(8),
                Padding = new Thickness(4)
            };

            // 创建内容
            var content = new StackPanel
            {
                Margin = new Thickness(16)
            };

            content.Children.Add(new TextBlock
            {
                Text = ""这是 Expander 的内容区域。"",
                Margin = new Thickness(0, 0, 0, 8)
            });

            content.Children.Add(new Button
            {
                Content = ""示例按钮"",
                HorizontalAlignment = HorizontalAlignment.Left
            });

            expander.Content = content;
            return expander;
        }
    }
}";

            AdvancedXamlExample = @"<!-- Expander 高级示例 -->
<!-- 嵌套 Expander -->
<Expander Header=""嵌套 Expander 示例"" IsExpanded=""False"">
    <StackPanel Padding=""16"">
        <TextBlock Text=""这是父级 Expander 的内容。""/>
        
        <Expander Header=""子级 Expander"" IsExpanded=""False"" Margin=""0,8,0,0"">
            <StackPanel Padding=""12"">
                <TextBlock Text=""这是子级 Expander。""/>
                <Button Content=""子级按钮"" HorizontalAlignment=""Left""/>
            </StackPanel>
        </Expander>
    </StackPanel>
</Expander>";

            AdvancedCSharpExample = @"// Expander 高级 C# 示例
// 展示如何通过代码创建复杂的 Expander 结构

using System.Windows;
using System.Windows.Controls;

namespace WPFTest.Examples.LayoutControls
{
    public class ExpanderAdvancedExample
    {
        /// <summary>
        /// 创建嵌套 Expander
        /// </summary>
        public static Expander CreateNestedExpander()
        {
            var parentExpander = new Expander
            {
                Header = ""嵌套 Expander 示例"",
                IsExpanded = false,
                Margin = new Thickness(8)
            };

            var parentContent = new StackPanel
            {
                Margin = new Thickness(16)
            };

            parentContent.Children.Add(new TextBlock
            {
                Text = ""这是父级 Expander 的内容。"",
                Margin = new Thickness(0, 0, 0, 12)
            });

            // 创建子级 Expander
            var childExpander = new Expander
            {
                Header = ""子级 Expander"",
                IsExpanded = false,
                Margin = new Thickness(0, 8, 0, 0)
            };

            var childContent = new StackPanel
            {
                Margin = new Thickness(12)
            };

            childContent.Children.Add(new TextBlock
            {
                Text = ""这是子级 Expander。""
            });

            childContent.Children.Add(new Button
            {
                Content = ""子级按钮"",
                HorizontalAlignment = HorizontalAlignment.Left,
                Margin = new Thickness(0, 8, 0, 0)
            });

            childExpander.Content = childContent;
            parentContent.Children.Add(childExpander);
            parentExpander.Content = parentContent;

            return parentExpander;
        }
    }
}";

            StylesXamlExample = @"<!-- Expander 样式示例 -->
<!-- 在 Zylo.WPF 中定义的 Expander 样式 -->

<!-- 基础样式 -->
<Style x:Key=""ExpanderBaseStyle"" TargetType=""Expander"">
    <Setter Property=""Background"" Value=""{DynamicResource ControlFillColorDefaultBrush}""/>
    <Setter Property=""BorderBrush"" Value=""{DynamicResource ControlStrokeColorDefaultBrush}""/>
    <Setter Property=""BorderThickness"" Value=""1""/>
    <Setter Property=""Padding"" Value=""8""/>
    <Setter Property=""Margin"" Value=""4""/>
</Style>

<!-- 现代化样式 -->
<Style x:Key=""ModernExpanderStyle"" TargetType=""Expander"" BasedOn=""{StaticResource ExpanderBaseStyle}"">
    <Setter Property=""Background"" Value=""{DynamicResource CardBackgroundFillColorDefaultBrush}""/>
    <Setter Property=""Effect"">
        <Setter.Value>
            <DropShadowEffect Color=""Black"" Opacity=""0.1"" ShadowDepth=""4"" BlurRadius=""8""/>
        </Setter.Value>
    </Setter>
</Style>";
        }

        #endregion
    }
}
