<!-- TextBlock + TextBox 组合控件示例 -->
<StackPanel Margin="20">

    <!-- 引用命名空间 -->
    <!-- xmlns:input="clr-namespace:Zylo.WPF.Controls.Input;assembly=Zylo.WPF" -->

    <!-- 传统样式组合控件 (优化后的紧凑布局) -->
    <GroupBox Header="传统样式组合控件" Padding="16" Margin="0,0,0,16">
        <StackPanel>
            <!-- 第一行：用户名和邮箱 -->
            <Grid Margin="0,0,0,12">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="16"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 用户名输入 -->
                <Border Grid.Column="0" Style="{StaticResource LabelTextBoxContainerStyle}">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="70"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="用户名:" Style="{StaticResource LabelTextStyle}"/>
                        <ui:TextBox Grid.Column="1" Style="{StaticResource LabelTextBoxInputStyle}"
                                    Text="{Binding UsernameValue, UpdateSourceTrigger=PropertyChanged}"
                                    PlaceholderText="请输入用户名"/>
                    </Grid>
                </Border>

                <!-- 邮箱输入 -->
                <Border Grid.Column="2" Style="{StaticResource LabelTextBoxContainerStyle}">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="70"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="邮箱:" Style="{StaticResource LabelTextStyle}"/>
                        <ui:TextBox Grid.Column="1" Style="{StaticResource LabelTextBoxInputStyle}"
                                    Text="{Binding EmailValue, UpdateSourceTrigger=PropertyChanged}"
                                    PlaceholderText="<EMAIL>"/>
                    </Grid>
                </Border>
            </Grid>

            <!-- 第二行：个人描述 -->
            <Border Style="{StaticResource LabelTextBoxContainerStyle}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="70"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="个人描述:" Style="{StaticResource LabelTextStyle}" VerticalAlignment="Top"/>
                    <ui:TextBox Grid.Column="1" Style="{StaticResource LabelTextBoxInputStyle}"
                                Text="{Binding DescriptionValue, UpdateSourceTrigger=PropertyChanged}"
                                PlaceholderText="请输入个人描述信息..."
                                TextWrapping="Wrap"
                                AcceptsReturn="True"
                                VerticalContentAlignment="Top"
                                MinHeight="50"/>
                </Grid>
            </Border>
        </StackPanel>
    </GroupBox>

    <!-- 现代化样式组合控件 (优化后的完整表单) -->
    <GroupBox Header="现代化样式组合控件" Padding="16" Margin="0,0,0,16">
        <StackPanel>
            <!-- 第一行：姓名和公司 -->
            <Grid Margin="0,0,0,16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="16"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 姓名输入 -->
                <Border Grid.Column="0" Style="{StaticResource ModernLabelTextBoxStyle}">
                    <StackPanel>
                        <TextBlock Text="姓名" Style="{StaticResource ModernLabelStyle}"/>
                        <ui:TextBox Style="{StaticResource ModernInputStyle}"
                                    Text="{Binding FullNameValue, UpdateSourceTrigger=PropertyChanged}"
                                    PlaceholderText="请输入您的全名"/>
                    </StackPanel>
                </Border>

                <!-- 公司输入 -->
                <Border Grid.Column="2" Style="{StaticResource ModernLabelTextBoxStyle}">
                    <StackPanel>
                        <TextBlock Text="公司/组织" Style="{StaticResource ModernLabelStyle}"/>
                        <ui:TextBox Style="{StaticResource ModernInputStyle}"
                                    Text="{Binding CompanyValue, UpdateSourceTrigger=PropertyChanged}"
                                    PlaceholderText="请输入公司或组织名称"/>
                    </StackPanel>
                </Border>
            </Grid>

            <!-- 第二行：联系方式 -->
            <Border Style="{StaticResource ModernLabelTextBoxStyle}" Margin="0,0,0,16">
                <StackPanel>
                    <TextBlock Text="联系电话" Style="{StaticResource ModernLabelStyle}"/>
                    <ui:TextBox Style="{StaticResource ModernInputStyle}"
                                Text="{Binding PhoneValue, UpdateSourceTrigger=PropertyChanged}"
                                PlaceholderText="+86 138 0000 0000"/>
                </StackPanel>
            </Border>

            <!-- 第三行：地址信息 -->
            <Border Style="{StaticResource ModernLabelTextBoxStyle}">
                <StackPanel>
                    <TextBlock Text="详细地址" Style="{StaticResource ModernLabelStyle}"/>
                    <ui:TextBox Style="{StaticResource ModernInputStyle}"
                                Text="{Binding AddressValue, UpdateSourceTrigger=PropertyChanged}"
                                PlaceholderText="请输入详细地址信息，包括省市区街道等"
                                TextWrapping="Wrap"
                                AcceptsReturn="True"
                                VerticalContentAlignment="Top"
                                MinHeight="60"/>
                </StackPanel>
            </Border>
        </StackPanel>
    </GroupBox>

    <!-- 自定义宽度示例 -->
    <GroupBox Header="自定义标签宽度" Padding="15">
        <StackPanel Spacing="12">
            
            <!-- 短标签 -->
            <Border Style="{StaticResource LabelTextBoxContainerStyle}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="60"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" 
                               Text="ID:" 
                               Style="{StaticResource LabelTextStyle}"/>
                    <ui:TextBox Grid.Column="1"
                                Style="{StaticResource LabelTextBoxInputStyle}"
                                PlaceholderText="用户ID"/>
                </Grid>
            </Border>

            <!-- 长标签 -->
            <Border Style="{StaticResource LabelTextBoxContainerStyle}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="150"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" 
                               Text="详细联系方式:" 
                               Style="{StaticResource LabelTextStyle}"/>
                    <ui:TextBox Grid.Column="1"
                                Style="{StaticResource LabelTextBoxInputStyle}"
                                PlaceholderText="包括电话、邮箱等"/>
                </Grid>
            </Border>

            <!-- 多行标签 -->
            <Border Style="{StaticResource LabelTextBoxContainerStyle}" MinHeight="80">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="120"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" 
                               Text="这是一个很长的多行标签文本，用来演示文本换行效果" 
                               Style="{StaticResource LabelTextStyle}"/>
                    <ui:TextBox Grid.Column="1"
                                Style="{StaticResource LabelTextBoxInputStyle}"
                                PlaceholderText="对应的输入框"
                                VerticalContentAlignment="Top"/>
                </Grid>
            </Border>
            
        </StackPanel>
    </GroupBox>

    <!-- LabelTextBox 自定义控件 - 可控参数展示 -->
    <GroupBox Header="LabelTextBox 自定义控件 - 可控参数展示" Padding="16" Margin="0,0,0,16">
        <StackPanel>
            <!-- 参数展示区域 -->
            <Grid Margin="0,0,0,16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="16"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 左列：宽度参数展示 -->
                <StackPanel Grid.Column="0">
                    <TextBlock Text="🎯 标签宽度参数 (LabelWidth)" FontWeight="Bold" Margin="0,0,0,8"/>

                    <!-- 宽度对比 -->
                    <Border Background="{DynamicResource ControlFillColorSecondaryBrush}"
                            Padding="12" CornerRadius="6" Margin="0,0,0,8">
                        <StackPanel>
                            <TextBlock Text="LabelWidth=&quot;40&quot;" FontFamily="Consolas" FontSize="11"
                                       Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                       Margin="0,0,0,6"/>
                            <input:LabelTextBox LabelText="短:"
                                                LabelWidth="40"
                                                LabelFontSize="14"
                                                LabelFontWeight="Medium"
                                                Text="{Binding CustomShortLabelValue, UpdateSourceTrigger=PropertyChanged}"
                                                PlaceholderText="40px宽度"/>
                        </StackPanel>
                    </Border>

                    <Border Background="{DynamicResource ControlFillColorSecondaryBrush}"
                            Padding="12" CornerRadius="6" Margin="0,0,0,8">
                        <StackPanel>
                            <TextBlock Text="LabelWidth=&quot;80&quot;" FontFamily="Consolas" FontSize="11"
                                       Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                       Margin="0,0,0,6"/>
                            <input:LabelTextBox LabelText="中等长度:"
                                                LabelWidth="80"
                                                LabelFontSize="14"
                                                LabelFontWeight="Medium"
                                                Text="{Binding CustomMediumLabelValue, UpdateSourceTrigger=PropertyChanged}"
                                                PlaceholderText="80px宽度"/>
                        </StackPanel>
                    </Border>
                </StackPanel>

                <!-- 右列：样式和功能参数展示 -->
                <StackPanel Grid.Column="2">
                    <TextBlock Text="🎨 样式和功能参数" FontWeight="Bold" Margin="0,0,0,8"/>

                    <!-- 基础样式 -->
                    <Border Background="{DynamicResource ControlFillColorSecondaryBrush}"
                            Padding="12" CornerRadius="6" Margin="0,0,0,8">
                        <StackPanel>
                            <TextBlock Text="默认样式 + PlaceholderText" FontFamily="Consolas" FontSize="11"
                                       Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                       Margin="0,0,0,6"/>
                            <input:LabelTextBox LabelText="用户名:"
                                                LabelWidth="60"
                                                LabelFontSize="14"
                                                LabelFontWeight="Medium"
                                                Text="{Binding CustomUsernameValue, UpdateSourceTrigger=PropertyChanged}"
                                                PlaceholderText="占位符文本提示"/>
                        </StackPanel>
                    </Border>

                    <!-- 现代化样式 -->
                    <Border Background="{DynamicResource ControlFillColorSecondaryBrush}"
                            Padding="12" CornerRadius="6" Margin="0,0,0,8">
                        <StackPanel>
                            <TextBlock Text="Style=&quot;ModernLabelTextBoxControlStyle&quot;" FontFamily="Consolas" FontSize="11"
                                       Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                       Margin="0,0,0,6"/>
                            <input:LabelTextBox Style="{StaticResource ModernLabelTextBoxControlStyle}"
                                                LabelText="现代化样式"
                                                Text="{Binding CustomFullNameValue, UpdateSourceTrigger=PropertyChanged}"
                                                PlaceholderText="卡片样式+阴影"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Grid>

            <!-- 多行功能展示 -->
            <TextBlock Text="📝 多行功能参数 (IsMultiline + TextBoxMinHeight)" FontWeight="Bold" Margin="0,16,0,8"/>
            <Border Background="{DynamicResource ControlFillColorSecondaryBrush}"
                    Padding="16" CornerRadius="6" Margin="0,0,0,8">
                <StackPanel>
                    <TextBlock Text="IsMultiline=&quot;True&quot; TextBoxMinHeight=&quot;70&quot;" FontFamily="Consolas" FontSize="11"
                               Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                               Margin="0,0,0,8"/>
                    <input:LabelTextBox LabelText="多行描述:"
                                        LabelWidth="80"
                                        LabelFontSize="14"
                                        LabelFontWeight="Medium"
                                        Text="{Binding CustomDescriptionValue, UpdateSourceTrigger=PropertyChanged}"
                                        PlaceholderText="支持多行输入&#x0a;自动换行&#x0a;滚动条显示"
                                        IsMultiline="True"
                                        TextBoxMinHeight="70"/>
                </StackPanel>
            </Border>
        </StackPanel>
    </GroupBox>

</StackPanel>
