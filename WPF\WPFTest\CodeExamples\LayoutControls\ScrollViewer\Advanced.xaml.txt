<!-- <PERSON><PERSON>Viewer 高级用法示例 -->
<StackPanel Margin="20">

    <!-- 双向滚动示例 -->
    <TextBlock Text="双向滚动示例" FontWeight="Bold" Margin="0,0,0,8"/>
    <Border BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
            BorderThickness="1"
            CornerRadius="4"
            Margin="0,0,0,16">
        <ScrollViewer Height="200"
                      Width="400"
                      VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Auto"
                      Background="{DynamicResource ControlFillColorDefaultBrush}"
                      HorizontalAlignment="Left">
            <Grid Margin="16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="200"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="40"/>
                    <RowDefinition Height="40"/>
                    <RowDefinition Height="40"/>
                    <RowDefinition Height="40"/>
                    <RowDefinition Height="40"/>
                    <RowDefinition Height="40"/>
                    <RowDefinition Height="40"/>
                    <RowDefinition Height="40"/>
                </Grid.RowDefinitions>
                
                <!-- 创建表格数据 -->
                <Border Grid.Row="0" Grid.Column="0" Background="{DynamicResource AccentFillColorDefaultBrush}" Margin="2">
                    <TextBlock Text="A1" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                <Border Grid.Row="0" Grid.Column="1" Background="{DynamicResource AccentFillColorSecondaryBrush}" Margin="2">
                    <TextBlock Text="B1" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                <Border Grid.Row="0" Grid.Column="2" Background="{DynamicResource AccentFillColorTertiaryBrush}" Margin="2">
                    <TextBlock Text="C1" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                <Border Grid.Row="0" Grid.Column="3" Background="{DynamicResource AccentFillColorDefaultBrush}" Margin="2">
                    <TextBlock Text="D1" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                
                <!-- 继续添加更多行和列的数据... -->
                <!-- 这里省略了重复的代码，实际使用时需要填充完整的表格 -->
            </Grid>
        </ScrollViewer>
    </Border>

    <!-- 虚拟化滚动示例 -->
    <TextBlock Text="虚拟化滚动示例" FontWeight="Bold" Margin="0,0,0,8"/>
    <Border BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
            BorderThickness="1"
            CornerRadius="4"
            Margin="0,0,0,16">
        <ScrollViewer Height="200"
                      VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Disabled"
                      Background="{DynamicResource ControlFillColorDefaultBrush}">
            <ItemsControl>
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <VirtualizingStackPanel VirtualizationMode="Recycling"/>
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Border Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                Margin="8,4"
                                Padding="12,8"
                                CornerRadius="4">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="项目 " FontWeight="Bold"/>
                                <TextBlock Text="{Binding}"/>
                                <TextBlock Text=" - 这是一个虚拟化的列表项" Margin="8,0,0,0"/>
                            </StackPanel>
                        </Border>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
                <!-- 这里需要绑定到一个大型数据集合 -->
            </ItemsControl>
        </ScrollViewer>
    </Border>

    <!-- 自定义滚动行为 -->
    <TextBlock Text="自定义滚动行为" FontWeight="Bold" Margin="0,0,0,8"/>
    <Border BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
            BorderThickness="1"
            CornerRadius="4"
            Margin="0,0,0,16">
        <ScrollViewer Height="150"
                      VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Disabled"
                      Background="{DynamicResource ControlFillColorDefaultBrush}"
                      CanContentScroll="True"
                      IsDeferredScrollingEnabled="True"
                      PanningMode="VerticalOnly">
            <StackPanel Margin="16">
                <TextBlock Text="自定义滚动行为示例" FontWeight="Bold" Margin="0,0,0,8"/>
                <TextBlock Text="• CanContentScroll: 启用逻辑滚动" Margin="0,0,0,4"/>
                <TextBlock Text="• IsDeferredScrollingEnabled: 延迟滚动" Margin="0,0,0,4"/>
                <TextBlock Text="• PanningMode: 触摸平移模式" Margin="0,0,0,4"/>
                <TextBlock Text="内容 1" Margin="0,8,0,4"/>
                <TextBlock Text="内容 2" Margin="0,0,0,4"/>
                <TextBlock Text="内容 3" Margin="0,0,0,4"/>
                <TextBlock Text="内容 4" Margin="0,0,0,4"/>
                <TextBlock Text="内容 5" Margin="0,0,0,4"/>
                <TextBlock Text="内容 6" Margin="0,0,0,4"/>
                <TextBlock Text="内容 7" Margin="0,0,0,4"/>
                <TextBlock Text="内容 8"/>
            </StackPanel>
        </ScrollViewer>
    </Border>

    <!-- 滚动事件处理 -->
    <TextBlock Text="滚动事件处理" FontWeight="Bold" Margin="0,0,0,8"/>
    <Border BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
            BorderThickness="1"
            CornerRadius="4"
            Margin="0,0,0,16">
        <ScrollViewer Height="120"
                      VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Disabled"
                      Background="{DynamicResource ControlFillColorDefaultBrush}"
                      ScrollChanged="OnScrollChanged">
            <StackPanel Margin="16">
                <TextBlock Text="滚动事件处理示例" FontWeight="Bold" Margin="0,0,0,8"/>
                <TextBlock Text="当滚动位置改变时，会触发 ScrollChanged 事件。" Margin="0,0,0,4"/>
                <TextBlock Text="可以在事件处理程序中获取滚动信息：" Margin="0,0,0,4"/>
                <TextBlock Text="• VerticalOffset: 垂直偏移量" Margin="0,0,0,2"/>
                <TextBlock Text="• HorizontalOffset: 水平偏移量" Margin="0,0,0,2"/>
                <TextBlock Text="• ViewportHeight: 可视区域高度" Margin="0,0,0,2"/>
                <TextBlock Text="• ExtentHeight: 内容总高度" Margin="0,0,0,2"/>
                <TextBlock Text="更多内容 1" Margin="0,8,0,4"/>
                <TextBlock Text="更多内容 2" Margin="0,0,0,4"/>
                <TextBlock Text="更多内容 3"/>
            </StackPanel>
        </ScrollViewer>
    </Border>

    <!-- 滚动到指定元素 -->
    <TextBlock Text="滚动到指定元素" FontWeight="Bold" Margin="0,0,0,8"/>
    <Grid Margin="0,0,0,16">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="Auto"/>
        </Grid.ColumnDefinitions>
        
        <Border Grid.Column="0"
                BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                BorderThickness="1"
                CornerRadius="4">
            <ScrollViewer x:Name="TargetScrollViewer"
                          Height="150"
                          VerticalScrollBarVisibility="Auto"
                          HorizontalScrollBarVisibility="Disabled"
                          Background="{DynamicResource ControlFillColorDefaultBrush}">
                <StackPanel Margin="16">
                    <TextBlock Text="滚动到指定元素示例" FontWeight="Bold" Margin="0,0,0,8"/>
                    <TextBlock Text="元素 1" Margin="0,0,0,4"/>
                    <TextBlock Text="元素 2" Margin="0,0,0,4"/>
                    <TextBlock Text="元素 3" Margin="0,0,0,4"/>
                    <TextBlock x:Name="TargetElement" 
                               Text="🎯 目标元素" 
                               Background="{DynamicResource AccentFillColorDefaultBrush}"
                               Foreground="White"
                               Padding="8,4"
                               Margin="0,0,0,4"/>
                    <TextBlock Text="元素 5" Margin="0,0,0,4"/>
                    <TextBlock Text="元素 6" Margin="0,0,0,4"/>
                    <TextBlock Text="元素 7" Margin="0,0,0,4"/>
                    <TextBlock Text="元素 8" Margin="0,0,0,4"/>
                    <TextBlock Text="元素 9" Margin="0,0,0,4"/>
                    <TextBlock Text="元素 10"/>
                </StackPanel>
            </ScrollViewer>
        </Border>
        
        <StackPanel Grid.Column="1" Margin="16,0,0,0" VerticalAlignment="Center">
            <Button Content="滚动到目标" 
                    Click="ScrollToTarget"
                    Margin="0,0,0,8"/>
            <Button Content="滚动到顶部" 
                    Click="ScrollToTop"
                    Margin="0,0,0,8"/>
            <Button Content="滚动到底部" 
                    Click="ScrollToBottom"/>
        </StackPanel>
    </Grid>

    <!-- 平滑滚动动画 -->
    <TextBlock Text="平滑滚动动画" FontWeight="Bold" Margin="0,0,0,8"/>
    <Border BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
            BorderThickness="1"
            CornerRadius="4"
            Margin="0,0,0,16">
        <ScrollViewer Height="120"
                      VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Disabled"
                      Background="{DynamicResource ControlFillColorDefaultBrush}">
            <ScrollViewer.Resources>
                <!-- 自定义滚动动画 -->
                <Storyboard x:Key="SmoothScrollAnimation">
                    <DoubleAnimation Storyboard.TargetProperty="VerticalOffset"
                                     Duration="0:0:0.5"
                                     EasingFunction="{StaticResource QuadraticEase}"/>
                </Storyboard>
            </ScrollViewer.Resources>
            <StackPanel Margin="16">
                <TextBlock Text="平滑滚动动画示例" FontWeight="Bold" Margin="0,0,0,8"/>
                <TextBlock Text="使用动画实现平滑的滚动效果。" Margin="0,0,0,4"/>
                <TextBlock Text="可以自定义动画的持续时间和缓动函数。" Margin="0,0,0,4"/>
                <TextBlock Text="动画内容 1" Margin="0,8,0,4"/>
                <TextBlock Text="动画内容 2" Margin="0,0,0,4"/>
                <TextBlock Text="动画内容 3" Margin="0,0,0,4"/>
                <TextBlock Text="动画内容 4" Margin="0,0,0,4"/>
                <TextBlock Text="动画内容 5" Margin="0,0,0,4"/>
                <TextBlock Text="动画内容 6"/>
            </StackPanel>
        </ScrollViewer>
    </Border>

    <!-- 响应式滚动 -->
    <TextBlock Text="响应式滚动" FontWeight="Bold" Margin="0,0,0,8"/>
    <Border BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
            BorderThickness="1"
            CornerRadius="4">
        <ScrollViewer VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Auto"
                      Background="{DynamicResource ControlFillColorDefaultBrush}">
            <ScrollViewer.Style>
                <Style TargetType="ScrollViewer">
                    <Setter Property="Height" Value="150"/>
                    <Style.Triggers>
                        <!-- 响应式高度调整 -->
                        <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType=Window}, Path=ActualWidth}" Value="800">
                            <Setter Property="Height" Value="200"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </ScrollViewer.Style>
            <StackPanel Margin="16">
                <TextBlock Text="响应式滚动示例" FontWeight="Bold" Margin="0,0,0,8"/>
                <TextBlock Text="根据窗口大小自动调整滚动区域的尺寸。" Margin="0,0,0,4"/>
                <TextBlock Text="使用触发器实现响应式布局。" Margin="0,0,0,4"/>
                <TextBlock Text="响应式内容 1" Margin="0,8,0,4"/>
                <TextBlock Text="响应式内容 2" Margin="0,0,0,4"/>
                <TextBlock Text="响应式内容 3" Margin="0,0,0,4"/>
                <TextBlock Text="响应式内容 4" Margin="0,0,0,4"/>
                <TextBlock Text="响应式内容 5"/>
            </StackPanel>
        </ScrollViewer>
    </Border>

</StackPanel>
