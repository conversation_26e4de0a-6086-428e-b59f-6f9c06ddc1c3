using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Windows;
using System.IO;
using System.Reflection;
using System.Collections.ObjectModel;
using System.ComponentModel;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels.LayoutControls
{
    /// <summary>
    /// CardExpander 页面的 ViewModel，演示 WPF-UI CardExpander 控件的各种功能
    /// </summary>
    public partial class CardExpanderViewModel : ObservableObject
    {
        private readonly YLoggerInstance _logger = YLogger.For<CardExpanderViewModel>();

        #region 属性

        /// <summary>
        /// 交互次数
        /// </summary>
        [ObservableProperty]
        private int interactionCount = 0;

        /// <summary>
        /// 最后执行的操作
        /// </summary>
        [ObservableProperty]
        private string lastAction = "无操作";

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        private string statusMessage = "欢迎使用 WPF-UI CardExpander 示例库！";

        #region 代码示例属性

        /// <summary>
        /// 基础功能 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 基础功能 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级功能 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级功能 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 样式 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string StylesXamlExample { get; set; } = string.Empty;

        #endregion

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化 CardExpanderViewModel
        /// </summary>
        public CardExpanderViewModel()
        {
            try
            {
                _logger.Info("🚀 CardExpander 页面 ViewModel 开始初始化");

                StatusMessage = "WPF-UI CardExpander 示例库已加载，开始体验可展开卡片设计！";
                InitializeCodeExamples();

                _logger.Info("✅ CardExpander 页面 ViewModel 初始化完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ CardExpander 页面 ViewModel 初始化失败: {ex.Message}");
                StatusMessage = "初始化失败，请检查日志";
            }
        }

        #endregion

        #region 命令

        /// <summary>
        /// 通用交互命令
        /// </summary>
        /// <param name="parameter">操作参数</param>
        [RelayCommand]
        private void HandleInteraction(string? parameter)
        {
            try
            {
                InteractionCount++;
                LastAction = parameter ?? "未知操作";
                
                _logger.Info($"🎯 用户交互操作: {parameter}");
                _logger.Info($"🔢 当前交互次数: {InteractionCount}");

                // 根据不同的操作显示不同的状态消息
                StatusMessage = parameter switch
                {
                    "简单CardExpander按钮" => "🎯 点击了简单 CardExpander 中的按钮",
                    "配置操作" => "🎯 执行了配置操作",
                    "重置操作" => "🎯 执行了重置操作",
                    "确认通知" => "🎯 确认了系统通知",
                    "新建项目" => "🎯 创建了新项目",
                    "导入数据" => "🎯 导入了数据",
                    "导出报告" => "🎯 导出了报告",
                    "系统设置" => "🎯 打开了系统设置",
                    "高级选项" => "🎯 访问了高级选项",
                    "子操作A" => "🎯 执行了子操作A",
                    "应用设置" => "🎯 应用了设置",
                    "刷新状态" => "🎯 刷新了系统状态",
                    "保存设置" => "🎯 保存了用户设置",
                    "重置设置" => "🎯 重置了用户设置",
                    _ => $"🎯 执行了操作: {parameter}"
                };
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 处理用户交互失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 重置计数命令
        /// </summary>
        [RelayCommand]
        private void ResetCount()
        {
            InteractionCount = 0;
            StatusMessage = "计数已重置";
            _logger.Info("重置了交互计数");
        }

        /// <summary>
        /// 清除状态命令
        /// </summary>
        [RelayCommand]
        private void ClearStatus()
        {
            LastAction = "无操作";
            StatusMessage = "状态已清除";
            _logger.Info("清除了状态信息");
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化代码示例
        /// </summary>
        private void InitializeCodeExamples()
        {
            try
            {
                // 获取代码示例文件的基础路径
                var baseDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
                var codeExamplesPath = Path.Combine(baseDirectory, "CodeExamples", "LayoutControls", "CardExpander");

                _logger.Info($"基础目录: {baseDirectory}");
                _logger.Info($"代码示例路径: {codeExamplesPath}");
                _logger.Info($"目录是否存在: {Directory.Exists(codeExamplesPath)}");

                // 读取各种示例文件
                BasicXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.xaml.txt"));
                BasicCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.cs.txt"));
                AdvancedXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Advanced.xaml.txt"));
                AdvancedCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Advanced.cs.txt"));
                StylesXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Styles.xaml.txt"));

                _logger.Info("CardExpander 代码示例加载完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"加载 CardExpander 代码示例失败: {ex.Message}");
                SetDefaultCodeExamples();
            }
        }

        /// <summary>
        /// 读取代码示例文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件内容</returns>
        private string ReadCodeExampleFile(string filePath)
        {
            _logger.Info($"尝试读取文件: {filePath}");
            
            if (File.Exists(filePath))
            {
                var content = File.ReadAllText(filePath);
                _logger.Info($"成功读取文件，内容长度: {content.Length}");
                return content;
            }
            else
            {
                _logger.Warning($"代码示例文件不存在: {filePath}");
                return $"// 代码示例文件不存在: {Path.GetFileName(filePath)}\n// 完整路径: {filePath}";
            }
        }

        /// <summary>
        /// 设置默认代码示例（当文件读取失败时使用）
        /// </summary>
        private void SetDefaultCodeExamples()
        {
            BasicXamlExample = @"<!-- WPF-UI CardExpander 基础示例 -->
<ui:CardExpander Header=""简单的可展开卡片"" 
                 IsExpanded=""True"">
    <StackPanel Margin=""16"">
        <TextBlock Text=""这是一个简单的 CardExpander 示例。"" 
                   FontWeight=""Medium"" 
                   Margin=""0,0,0,8""/>
        <TextBlock Text=""CardExpander 结合了 Card 的现代化样式和 Expander 的可展开功能。"" 
                   TextWrapping=""Wrap""
                   Margin=""0,0,0,12""/>
        <Button Content=""卡片内按钮"" 
                HorizontalAlignment=""Left""/>
    </StackPanel>
</ui:CardExpander>";

            BasicCSharpExample = @"// WPF-UI CardExpander C# 基础示例
using Wpf.Ui.Controls;

// 创建基础 CardExpander
var cardExpander = new CardExpander
{
    Header = ""简单的可展开卡片"",
    IsExpanded = true,
    Margin = new Thickness(8)
};

var content = new StackPanel
{
    Margin = new Thickness(16)
};

content.Children.Add(new TextBlock 
{ 
    Text = ""这是一个简单的 CardExpander 示例。"",
    FontWeight = FontWeights.Medium,
    Margin = new Thickness(0, 0, 0, 8)
});

content.Children.Add(new TextBlock 
{ 
    Text = ""CardExpander 结合了 Card 的现代化样式和 Expander 的可展开功能。"",
    TextWrapping = TextWrapping.Wrap,
    Margin = new Thickness(0, 0, 0, 12)
});

content.Children.Add(new Button 
{ 
    Content = ""卡片内按钮"",
    HorizontalAlignment = HorizontalAlignment.Left
});

cardExpander.Content = content;";

            AdvancedXamlExample = @"<!-- WPF-UI CardExpander 高级示例 -->
<!-- 嵌套的 CardExpander -->
<ui:CardExpander Header=""嵌套展开卡片"" 
                 IsExpanded=""True"">
    <StackPanel Margin=""16"">
        <TextBlock Text=""这是一个包含子 CardExpander 的父级容器。"" 
                   Margin=""0,0,0,16""/>
        
        <!-- 子 CardExpander 1 -->
        <ui:CardExpander Header=""子展开卡片 A"" 
                         IsExpanded=""False""
                         Background=""{DynamicResource ControlFillColorSecondaryBrush}"">
            <StackPanel Margin=""12"">
                <TextBlock Text=""这是第一个子 CardExpander 的内容。"" 
                           Margin=""0,0,0,8""/>
                <Button Content=""子操作 A"" HorizontalAlignment=""Left""/>
            </StackPanel>
        </ui:CardExpander>
        
        <!-- 子 CardExpander 2 -->
        <ui:CardExpander Header=""子展开卡片 B"" 
                         IsExpanded=""False""
                         Background=""{DynamicResource ControlFillColorSecondaryBrush}"">
            <StackPanel Margin=""12"">
                <TextBlock Text=""这是第二个子 CardExpander 的内容。"" 
                           Margin=""0,0,0,8""/>
                <Button Content=""子操作 B"" HorizontalAlignment=""Left""/>
            </StackPanel>
        </ui:CardExpander>
    </StackPanel>
</ui:CardExpander>";

            AdvancedCSharpExample = @"// WPF-UI CardExpander 高级 C# 示例
// 创建嵌套的 CardExpander
public static CardExpander CreateNestedCardExpander()
{
    var parentExpander = new CardExpander
    {
        Header = ""嵌套展开卡片"",
        IsExpanded = true,
        Margin = new Thickness(8)
    };
    
    var parentContent = new StackPanel
    {
        Margin = new Thickness(16)
    };
    
    parentContent.Children.Add(new TextBlock
    {
        Text = ""这是一个包含子 CardExpander 的父级容器。"",
        Margin = new Thickness(0, 0, 0, 16)
    });
    
    // 创建子 CardExpander
    var childExpander1 = new CardExpander
    {
        Header = ""子展开卡片 A"",
        IsExpanded = false,
        Margin = new Thickness(0, 0, 0, 8)
    };
    
    var childContent1 = new StackPanel
    {
        Margin = new Thickness(12)
    };
    
    childContent1.Children.Add(new TextBlock
    {
        Text = ""这是第一个子 CardExpander 的内容。"",
        Margin = new Thickness(0, 0, 0, 8)
    });
    
    childContent1.Children.Add(new Button
    {
        Content = ""子操作 A"",
        HorizontalAlignment = HorizontalAlignment.Left
    });
    
    childExpander1.Content = childContent1;
    parentContent.Children.Add(childExpander1);
    
    parentExpander.Content = parentContent;
    return parentExpander;
}";

            StylesXamlExample = @"<!-- WPF-UI CardExpander 样式示例 -->
<!-- 成功样式 -->
<ui:CardExpander Header=""成功状态"" 
                 IsExpanded=""False""
                 Background=""{DynamicResource SystemFillColorSuccessBrush}"">
    <StackPanel Margin=""16"">
        <TextBlock Text=""操作已成功完成！"" Foreground=""White""/>
    </StackPanel>
</ui:CardExpander>

<!-- 警告样式 -->
<ui:CardExpander Header=""警告信息"" 
                 IsExpanded=""False""
                 Background=""{DynamicResource SystemFillColorCautionBrush}"">
    <StackPanel Margin=""16"">
        <TextBlock Text=""请注意以下事项"" Foreground=""White""/>
    </StackPanel>
</ui:CardExpander>

<!-- 错误样式 -->
<ui:CardExpander Header=""错误信息"" 
                 IsExpanded=""False""
                 Background=""{DynamicResource SystemFillColorCriticalBrush}"">
    <StackPanel Margin=""16"">
        <TextBlock Text=""发生了严重错误！"" Foreground=""White""/>
    </StackPanel>
</ui:CardExpander>

<!-- 强调样式 -->
<ui:CardExpander Header=""重要通知"" 
                 IsExpanded=""False""
                 Background=""{DynamicResource AccentFillColorDefaultBrush}"">
    <StackPanel Margin=""16"">
        <TextBlock Text=""系统更新可用"" Foreground=""White""/>
    </StackPanel>
</ui:CardExpander>

<!-- 透明样式 -->
<ui:CardExpander Header=""透明卡片"" 
                 IsExpanded=""False""
                 Background=""Transparent""
                 BorderBrush=""{DynamicResource ControlStrokeColorDefaultBrush}""
                 BorderThickness=""1"">
    <StackPanel Margin=""16"">
        <TextBlock Text=""这是一个透明背景的 CardExpander。""/>
    </StackPanel>
</ui:CardExpander>";
        }

        #endregion
    }
}
