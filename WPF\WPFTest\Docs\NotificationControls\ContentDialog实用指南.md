# ContentDialog 实用指南

> 🎉 **即开即用** - 所有基础配置已完成，无需额外设置，可直接使用所有功能！

## 📋 当前可用功能

### ✅ **完全可用的功能**
- **基础对话框**：信息、确认、警告、错误、自定义对话框
- **CommunityToolkit.Mvvm 集成**：异步命令、数据验证、消息传递
- **CodeExampleControl 展示**：完整的代码示例和语法高亮

### ⚠️ **暂不可用的功能**
- **高级对话框功能**：表单输入、进度条、列表选择、图片预览等（需要进一步开发）

---

## 🚀 快速开始

> ✅ **无需配置** - 主窗口和所有必要设置已完成，直接复制代码即可使用！

### **1. 基础对话框使用**

```csharp
// 信息对话框
[RelayCommand]
private async Task ShowInfoDialog()
{
    var dialog = new ContentDialog
    {
        Title = "信息提示",
        Content = "这是一个信息类型的对话框",
        PrimaryButtonText = "知道了",
        DefaultButton = ContentDialogButton.Primary
    };

    SetupContentDialog(dialog); // 必须调用
    var result = await dialog.ShowAsync();
}

// 确认对话框
[RelayCommand]
private async Task ShowConfirmDialog()
{
    var dialog = new ContentDialog
    {
        Title = "确认操作",
        Content = "您确定要执行此操作吗？",
        PrimaryButtonText = "确定",
        SecondaryButtonText = "取消",
        DefaultButton = ContentDialogButton.Primary
    };

    SetupContentDialog(dialog);
    var result = await dialog.ShowAsync();
    
    if (result == ContentDialogResult.Primary)
    {
        // 用户点击了确定
    }
}
```

### **2. 必需的设置方法**

```csharp
/// <summary>
/// 设置对话框的 ContentPresenter - 必须调用！
/// </summary>
private void SetupContentDialog(ContentDialog dialog)
{
    if (Application.Current.MainWindow != null)
    {
        var contentPresenter = Application.Current.MainWindow.FindName("RootContentDialogPresenter") as ContentPresenter;
        if (contentPresenter != null)
        {
#pragma warning disable CS0618 // 类型或成员已过时
            dialog.DialogHost = contentPresenter;
#pragma warning restore CS0618 // 类型或成员已过时
        }
    }
}
```

### **3. 主窗口配置**

✅ **已完成配置** - `MainView.xaml` 中已经包含了必要的配置：

```xml
<Grid>
    <!-- ContentDialog 支持 - 已配置 -->
    <ContentPresenter x:Name="RootContentDialogPresenter" Grid.RowSpan="2" Panel.ZIndex="999"/>

    <!-- 其他内容... -->
</Grid>
```

**无需额外操作** - 主窗口已经支持 ContentDialog，可以直接使用。

---

## 💻 CommunityToolkit.Mvvm 集成

### **异步命令示例**

```csharp
public partial class MyViewModel : ObservableObject
{
    [ObservableProperty]
    private bool isAsyncOperationRunning = false;

    [ObservableProperty]
    private string asyncOperationStatus = string.Empty;

    [RelayCommand]
    private async Task ShowAsyncDialog()
    {
        var dialog = new ContentDialog
        {
            Title = "异步操作",
            Content = "正在处理，请稍候...",
            PrimaryButtonText = "后台运行",
            SecondaryButtonText = "取消"
        };

        SetupContentDialog(dialog);

        // 启动异步操作
        var cancellationTokenSource = new CancellationTokenSource();
        var task = DoAsyncWork(cancellationTokenSource.Token);

        var result = await dialog.ShowAsync();
        
        if (result == ContentDialogResult.Secondary)
        {
            cancellationTokenSource.Cancel();
        }
    }

    private async Task DoAsyncWork(CancellationToken cancellationToken)
    {
        IsAsyncOperationRunning = true;
        try
        {
            for (int i = 0; i <= 100; i += 10)
            {
                cancellationToken.ThrowIfCancellationRequested();
                AsyncOperationStatus = $"进度: {i}%";
                await Task.Delay(500, cancellationToken);
            }
        }
        finally
        {
            IsAsyncOperationRunning = false;
        }
    }
}
```

### **数据验证集成**

```csharp
public partial class ValidationViewModel : ObservableValidator
{
    [ObservableProperty]
    [NotifyDataErrorInfo]
    [Required(ErrorMessage = "输入内容不能为空")]
    [MinLength(3, ErrorMessage = "至少需要3个字符")]
    private string validationInput = string.Empty;

    [RelayCommand]
    private async Task ShowValidationDialog()
    {
        ValidateAllProperties();

        if (HasErrors)
        {
            var errors = GetErrors(nameof(ValidationInput))
                .Cast<System.ComponentModel.DataAnnotations.ValidationResult>()
                .Select(vr => vr.ErrorMessage ?? "未知错误")
                .ToList();

            var dialog = new ContentDialog
            {
                Title = "验证失败",
                Content = $"输入数据不符合要求：\n{string.Join(", ", errors)}",
                PrimaryButtonText = "知道了"
            };

            SetupContentDialog(dialog);
            await dialog.ShowAsync();
        }
        else
        {
            var dialog = new ContentDialog
            {
                Title = "验证成功",
                Content = $"输入验证通过：{ValidationInput}",
                PrimaryButtonText = "确定"
            };

            SetupContentDialog(dialog);
            await dialog.ShowAsync();
        }
    }
}
```

### **消息传递示例**

```csharp
public partial class MessagingViewModel : ObservableObject, IRecipient<string>
{
    [ObservableProperty]
    private string lastReceivedMessage = string.Empty;

    public MessagingViewModel()
    {
        // 注册消息接收
        WeakReferenceMessenger.Default.Register<string>(this);
    }

    [RelayCommand]
    private void SendGlobalMessage()
    {
        var message = $"全局消息 - {DateTime.Now:HH:mm:ss}";
        WeakReferenceMessenger.Default.Send(message);
    }

    [RelayCommand]
    private async Task ShowMessageDialog()
    {
        var dialog = new ContentDialog
        {
            Title = "消息订阅",
            Content = $"最后接收到的消息：{LastReceivedMessage}",
            PrimaryButtonText = "继续订阅",
            SecondaryButtonText = "取消订阅"
        };

        SetupContentDialog(dialog);
        var result = await dialog.ShowAsync();
        
        if (result == ContentDialogResult.Secondary)
        {
            WeakReferenceMessenger.Default.Unregister<string>(this);
        }
    }

    // 实现 IRecipient<string> 接口
    public void Receive(string message)
    {
        LastReceivedMessage = message;
    }
}
```

---

## 🎨 XAML 配置

### **基础 XAML 绑定**

```xml
<StackPanel>
    <!-- 基础对话框按钮 -->
    <ui:Button Content="信息对话框"
               Icon="{ui:SymbolIcon Info24}"
               Command="{Binding ShowInfoDialogCommand}"
               Margin="0,0,8,8"/>
    
    <ui:Button Content="确认对话框"
               Icon="{ui:SymbolIcon QuestionCircle24}"
               Command="{Binding ShowConfirmDialogCommand}"
               Margin="0,0,8,8"/>
    
    <!-- 异步操作演示 -->
    <ui:Button Content="异步加载对话框"
               Icon="{ui:SymbolIcon ArrowDownload24}"
               Command="{Binding ShowAsyncLoadingDialogCommand}"
               Margin="0,0,8,8"/>
    
    <!-- 进度显示 -->
    <StackPanel Visibility="{Binding IsAsyncOperationRunning, 
                            Converter={StaticResource BooleanToVisibilityConverter}}">
        <TextBlock Text="{Binding AsyncOperationStatus}"/>
        <ProgressBar Value="{Binding AsyncOperationProgress}" Maximum="100"/>
    </StackPanel>
    
    <!-- 数据验证 -->
    <ui:TextBox Text="{Binding ValidationInput, Mode=TwoWay, 
                        UpdateSourceTrigger=PropertyChanged}"
                PlaceholderText="输入要验证的内容"/>
    <ui:Button Content="验证并显示对话框"
               Command="{Binding ShowValidationDialogCommand}"/>
</StackPanel>
```

---

## ⚠️ 重要注意事项

### **1. 必须调用 SetupContentDialog**
```csharp
// ❌ 错误 - 会抛出 DialogHost was not set 异常
var result = await dialog.ShowAsync();

// ✅ 正确 - 必须先设置 DialogHost
SetupContentDialog(dialog);
var result = await dialog.ShowAsync();
```

### **2. 主窗口必须有 ContentPresenter**
```xml
<!-- 必须在主窗口中添加 -->
<ContentPresenter x:Name="RootContentDialogPresenter" Grid.RowSpan="2" Panel.ZIndex="999"/>
```

### **3. 数据验证类型冲突**
```csharp
// ❌ 错误 - ValidationResult 类型不明确
.Cast<ValidationResult>()

// ✅ 正确 - 使用完全限定名称
.Cast<System.ComponentModel.DataAnnotations.ValidationResult>()
```

### **4. 异步操作的正确处理**
```csharp
// ✅ 正确的异步操作模式
[RelayCommand]
private async Task ShowAsyncDialog()
{
    var cancellationTokenSource = new CancellationTokenSource();
    var task = DoAsyncWork(cancellationTokenSource.Token);
    
    var result = await dialog.ShowAsync();
    
    if (result == ContentDialogResult.Secondary)
    {
        cancellationTokenSource.Cancel(); // 用户取消时停止操作
    }
}
```

---

## 📝 最佳实践

### **1. 统一的对话框创建模式**
```csharp
private async Task<ContentDialogResult> ShowDialogAsync(string title, string content, 
    string primaryText = "确定", string secondaryText = "取消")
{
    var dialog = new ContentDialog
    {
        Title = title,
        Content = content,
        PrimaryButtonText = primaryText,
        SecondaryButtonText = secondaryText,
        DefaultButton = ContentDialogButton.Primary
    };

    SetupContentDialog(dialog);
    return await dialog.ShowAsync();
}
```

### **2. 结果处理的标准方法**
```csharp
private void HandleDialogResult(ContentDialogResult result, string dialogType)
{
    var resultText = result switch
    {
        ContentDialogResult.Primary => "用户点击了主要按钮",
        ContentDialogResult.Secondary => "用户点击了次要按钮",
        ContentDialogResult.None => "用户点击了关闭按钮",
        _ => "未知结果"
    };
    
    UpdateStatus($"{dialogType}: {resultText}");
}
```

### **3. 错误处理**
```csharp
[RelayCommand]
private async Task ShowDialogSafely()
{
    try
    {
        var dialog = new ContentDialog { /* 配置 */ };
        SetupContentDialog(dialog);
        var result = await dialog.ShowAsync();
        HandleDialogResult(result, "示例对话框");
    }
    catch (Exception ex)
    {
        // 记录错误或显示错误消息
        _logger.Error($"显示对话框失败: {ex.Message}");
    }
}
```

---

## 🔗 相关文档

- [CommunityToolkit.Mvvm 完整指南](./CommunityToolkit-MVVM-Complete-Guide.md)
- [CodeExampleControl 开发文档](./CodeExampleControl开发文档.md)
- [WPF 依赖属性深度解析](./WPF依赖属性深度解析.md)

---

*📅 最后更新：2025年1月*  
*🔗 版本：v1.0.0 - 基础功能完整版*  
*⚠️ 注意：高级对话框功能待后续开发*
