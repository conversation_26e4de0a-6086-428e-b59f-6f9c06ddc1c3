<!-- NumberBox 基础用法示例 -->
<StackPanel Margin="20">

    <!-- 标准 NumberBox -->
    <GroupBox Header="标准 NumberBox" Padding="15" Margin="0,0,0,15">
        <StackPanel>
            <TextBlock Text="基础数字输入：" FontWeight="Bold"/>
            <ui:NumberBox Value="123.45"
                          PlaceholderText="请输入数值"
                          Margin="0,0,0,8"/>
            
            <!-- 带范围限制 -->
            <TextBlock Text="带范围限制（0-100）：" FontWeight="Bold"/>
            <ui:NumberBox Value="50"
                          PlaceholderText="0-100之间的数值"
                          Minimum="0"
                          Maximum="100"
                          Margin="0,0,0,8"/>
            
            <!-- 带步进值 -->
            <TextBlock Text="带步进值（步进0.5）：" FontWeight="Bold"/>
            <ui:NumberBox Value="10"
                          PlaceholderText="步进值为0.5"
                          SmallChange="0.5"/>
        </StackPanel>
    </GroupBox>

    <!-- 不同精度 -->
    <GroupBox Header="不同精度设置" Padding="15">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                <TextBlock Text="整数" FontWeight="Bold" Margin="0,0,0,4"/>
                <ui:NumberBox Value="42"
                              PlaceholderText="整数"/>
            </StackPanel>

            <StackPanel Grid.Column="1" Margin="4,0,4,0">
                <TextBlock Text="小数" FontWeight="Bold" Margin="0,0,0,4"/>
                <ui:NumberBox Value="3.14"
                              PlaceholderText="小数"/>
            </StackPanel>

            <StackPanel Grid.Column="2" Margin="8,0,0,0">
                <TextBlock Text="高精度小数" FontWeight="Bold" Margin="0,0,0,4"/>
                <ui:NumberBox Value="2.7183"
                              PlaceholderText="高精度小数"/>
            </StackPanel>
        </Grid>
    </GroupBox>

    <!-- 数据绑定示例 -->
    <GroupBox Header="数据绑定示例" Padding="15">
        <StackPanel>
            <TextBlock Text="绑定到 ViewModel 属性：" FontWeight="Bold" Margin="0,0,0,8"/>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="16"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <!-- 输入控件 -->
                <StackPanel Grid.Column="0">
                    <TextBlock Text="数值输入" FontSize="12" Margin="0,0,0,2"/>
                    <ui:NumberBox Value="{Binding BasicValue, UpdateSourceTrigger=PropertyChanged}"
                                  Minimum="{Binding MinimumValue}"
                                  Maximum="{Binding MaximumValue}"
                                  SmallChange="{Binding StepValue}"
                                  PlaceholderText="绑定的数值"/>
                </StackPanel>
                
                <!-- 设置控件 -->
                <StackPanel Grid.Column="2">
                    <TextBlock Text="范围设置" FontSize="12" Margin="0,0,0,2"/>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <StackPanel Grid.Column="0" Margin="0,0,2,0">
                            <TextBlock Text="最小值" FontSize="10" Margin="0,0,0,1"/>
                            <ui:NumberBox Value="{Binding MinimumValue}"
                                          FontSize="10"
                                          Height="24"/>
                        </StackPanel>
                        
                        <StackPanel Grid.Column="1" Margin="2,0,0,0">
                            <TextBlock Text="最大值" FontSize="10" Margin="0,0,0,1"/>
                            <ui:NumberBox Value="{Binding MaximumValue}"
                                          FontSize="10"
                                          Height="24"/>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </Grid>
        </StackPanel>
    </GroupBox>

    <!-- 验证和反馈 -->
    <GroupBox Header="验证和反馈" Padding="15">
        <StackPanel>
            <TextBlock Text="带验证的数字输入：" FontWeight="Bold" Margin="0,0,0,8"/>
            
            <ui:NumberBox Value="{Binding ValidatedValue, UpdateSourceTrigger=PropertyChanged}"
                          Minimum="1"
                          Maximum="999"
                          PlaceholderText="1-999之间的数值"
                          Margin="0,0,0,8"/>
            
            <!-- 验证结果显示 -->
            <Border Background="{DynamicResource ControlFillColorTertiaryBrush}"
                    CornerRadius="4"
                    Padding="8"
                    Visibility="{Binding ValidationMessage, Converter={StaticResource StringToVisibilityConverter}}">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="📊" FontSize="12" Margin="0,0,4,0"/>
                    <TextBlock Text="{Binding ValidationMessage}"
                               FontSize="11"
                               Foreground="{Binding ValidationMessageColor}"/>
                </StackPanel>
            </Border>
        </StackPanel>
    </GroupBox>

</StackPanel>
