<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 🔄 通用过渡动画 - 不依赖特定元素名称 -->
    
    <!-- ========================================= -->
    <!-- 📱 淡入淡出动画 -->
    <!-- ========================================= -->
    
    <!-- 淡入动画 -->
    <Storyboard x:Key="ZyloFadeInAnimation">
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="0" To="1" Duration="0:0:0.4">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="1"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 淡出动画 -->
    <Storyboard x:Key="ZyloFadeOutAnimation">
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="1" To="0" Duration="0:0:0.4">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="1"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 快速淡入动画 -->
    <Storyboard x:Key="ZyloFadeInFastAnimation">
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="0" To="1" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="1"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 快速淡出动画 -->
    <Storyboard x:Key="ZyloFadeOutFastAnimation">
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="1" To="0" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="1"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- ========================================= -->
    <!-- 🔄 滑动动画 -->
    <!-- ========================================= -->
    
    <!-- 从左滑入 -->
    <Storyboard x:Key="ZyloSlideInFromLeftAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                       From="-100" To="0" Duration="0:0:0.5">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="0" To="1" Duration="0:0:0.5"/>
    </Storyboard>

    <!-- 从右滑入 -->
    <Storyboard x:Key="ZyloSlideInFromRightAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                       From="100" To="0" Duration="0:0:0.5">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="0" To="1" Duration="0:0:0.5"/>
    </Storyboard>

    <!-- 从上滑入 -->
    <Storyboard x:Key="ZyloSlideInFromTopAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                       From="-50" To="0" Duration="0:0:0.5">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="0" To="1" Duration="0:0:0.5"/>
    </Storyboard>

    <!-- 从下滑入 -->
    <Storyboard x:Key="ZyloSlideInFromBottomAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                       From="50" To="0" Duration="0:0:0.5">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="0" To="1" Duration="0:0:0.5"/>
    </Storyboard>

    <!-- ========================================= -->
    <!-- 📏 缩放动画 -->
    <!-- ========================================= -->
    
    <!-- 缩放进入动画 -->
    <Storyboard x:Key="ZyloScaleInAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                       From="0.8" To="1" Duration="0:0:0.4">
            <DoubleAnimation.EasingFunction>
                <BackEase EasingMode="EaseOut" Amplitude="0.3"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                       From="0.8" To="1" Duration="0:0:0.4">
            <DoubleAnimation.EasingFunction>
                <BackEase EasingMode="EaseOut" Amplitude="0.3"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="0" To="1" Duration="0:0:0.4"/>
    </Storyboard>

    <!-- 缩放退出动画 -->
    <Storyboard x:Key="ZyloScaleOutAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                       From="1" To="0.8" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseIn" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                       From="1" To="0.8" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseIn" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="1" To="0" Duration="0:0:0.3"/>
    </Storyboard>

    <!-- ========================================= -->
    <!-- 🌀 旋转动画 -->
    <!-- ========================================= -->

    <!-- 顺时针旋转 360 度 -->
    <Storyboard x:Key="ZyloRotateClockwiseAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(RotateTransform.Angle)"
                       From="0" To="360" Duration="0:0:1">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseInOut" Power="1"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 逆时针旋转 360 度 -->
    <Storyboard x:Key="ZyloRotateCounterClockwiseAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(RotateTransform.Angle)"
                       From="0" To="-360" Duration="0:0:1">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseInOut" Power="1"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 无限旋转动画 - 加载指示器 -->
    <Storyboard x:Key="ZyloSpinAnimation" RepeatBehavior="Forever">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(RotateTransform.Angle)"
                       From="0" To="360" Duration="0:0:1"/>
    </Storyboard>

    <!-- 快速无限旋转 -->
    <Storyboard x:Key="ZyloSpinFastAnimation" RepeatBehavior="Forever">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(RotateTransform.Angle)"
                       From="0" To="360" Duration="0:0:0.5"/>
    </Storyboard>

    <!-- 慢速无限旋转 -->
    <Storyboard x:Key="ZyloSpinSlowAnimation" RepeatBehavior="Forever">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(RotateTransform.Angle)"
                       From="0" To="360" Duration="0:0:2"/>
    </Storyboard>

    <!-- ========================================= -->
    <!-- 💫 弹跳动画 -->
    <!-- ========================================= -->

    <!-- 弹跳进入动画 -->
    <Storyboard x:Key="ZyloBounceInAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                       From="0.3" To="1" Duration="0:0:0.6">
            <DoubleAnimation.EasingFunction>
                <BounceEase EasingMode="EaseOut" Bounces="2" Bounciness="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                       From="0.3" To="1" Duration="0:0:0.6">
            <DoubleAnimation.EasingFunction>
                <BounceEase EasingMode="EaseOut" Bounces="2" Bounciness="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="0" To="1" Duration="0:0:0.3"/>
    </Storyboard>

    <!-- 弹跳退出动画 -->
    <Storyboard x:Key="ZyloBounceOutAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                       From="1" To="0.3" Duration="0:0:0.4">
            <DoubleAnimation.EasingFunction>
                <BounceEase EasingMode="EaseIn" Bounces="2" Bounciness="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                       From="1" To="0.3" Duration="0:0:0.4">
            <DoubleAnimation.EasingFunction>
                <BounceEase EasingMode="EaseIn" Bounces="2" Bounciness="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="1" To="0" Duration="0:0:0.4"/>
    </Storyboard>

    <!-- 垂直弹跳动画 -->
    <Storyboard x:Key="ZyloBounceVerticalAnimation" RepeatBehavior="Forever">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                       From="0" To="-10" Duration="0:0:0.5"
                       AutoReverse="True">
            <DoubleAnimation.EasingFunction>
                <SineEase EasingMode="EaseInOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- ========================================= -->
    <!-- 🌊 摇摆动画 -->
    <!-- ========================================= -->

    <!-- 左右摇摆 -->
    <Storyboard x:Key="ZyloShakeHorizontalAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                       From="0" To="5" Duration="0:0:0.1"
                       AutoReverse="True" RepeatBehavior="3x">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseInOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 上下摇摆 -->
    <Storyboard x:Key="ZyloShakeVerticalAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                       From="0" To="5" Duration="0:0:0.1"
                       AutoReverse="True" RepeatBehavior="3x">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseInOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 旋转摇摆 -->
    <Storyboard x:Key="ZyloWobbleAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(RotateTransform.Angle)"
                       From="0" To="15" Duration="0:0:0.15"
                       AutoReverse="True" RepeatBehavior="4x">
            <DoubleAnimation.EasingFunction>
                <SineEase EasingMode="EaseInOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

</ResourceDictionary>
