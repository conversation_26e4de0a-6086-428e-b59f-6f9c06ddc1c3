using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls.Primitives;
using System.Windows.Input;
using Zylo.WPF.Helpers;
using Zylo.WPF.Models.Navigation;
using Zylo.YLog.Runtime;

namespace Zylo.WPF.Controls.Navigation;

/// <summary>
/// NavigationControl - 高级导航控件
/// </summary>
/// <remarks>
/// <para><strong>功能特性：</strong></para>
/// <list type="bullet">
///   <item><description>🎯 <strong>双模式显示</strong>：ListView（左侧48px）+ TreeView（右侧可配置宽度）</description></item>
///   <item><description>🔍 <strong>智能搜索</strong>：实时搜索过滤，支持高亮显示</description></item>
///   <item><description>📱 <strong>响应式布局</strong>：左右互斥显示，各自占满可用空间</description></item>
///   <item><description>⚡ <strong>高性能绑定</strong>：使用 CommunityToolkit.Mvvm 优化</description></item>
///   <item><description>🎨 <strong>WPF-UI 主题</strong>：完美适配 WPF-UI 设计系统</description></item>
/// </list>
///
/// <para><strong>使用示例：</strong></para>
/// <code>
/// &lt;controls:NavigationControl
///     TopNavigationItems="{Binding TopItems}"
///     BottomNavigationItems="{Binding BottomItems}"
///     RightColumnWidth="600"
///     NavigationItemSelectedCommand="{Binding NavigateCommand}" /&gt;
/// </code>
///
/// <para><strong>架构设计：</strong></para>
/// <list type="number">
///   <item><description>顶层 Grid 宽度由 NavigationControlWidth 控制</description></item>
///   <item><description>左边模式：宽度=48px，显示 ListView</description></item>
///   <item><description>右边模式：宽度=RightColumnWidth，显示 TreeView</description></item>
///   <item><description>通过 Visibility 控制左右互斥显示</description></item>
/// </list>
/// </remarks>
[ObservableObject]
public partial class NavigationControl : UserControl
{
    #region 私有字段 - 内部状态管理
    // ================================================================
    // 私有字段：用于管理控件内部状态和防止事件循环
    // ================================================================

    /// <summary>
    /// 防止互斥选中时的事件循环标志
    /// </summary>
    /// <remarks>
    /// 当一个 ListView 选中时清除另一个 ListView 的选择，
    /// 需要使用此标志防止无限循环的 SelectionChanged 事件
    /// </remarks>
    private bool _isUpdatingSelection = false;

    #endregion

    #region 依赖属性 - 核心数据绑定
    // ================================================================
    // 核心数据绑定属性：ItemsSource, SelectedItem, SearchText 等
    // 这些属性提供与外部 ViewModel 的标准双向绑定能力
    // ================================================================

    /// <summary>
    /// 主要数据源依赖属性
    /// </summary>
    /// <remarks>
    /// <para>类似于 TreeView 的 ItemsSource，用于绑定导航数据源</para>
    /// <para><strong>支持的数据类型：</strong></para>
    /// <list type="bullet">
    ///   <item><description>ObservableCollection&lt;ZyloNavigationItemModel&gt;</description></item>
    ///   <item><description>List&lt;ZyloNavigationItemModel&gt;</description></item>
    ///   <item><description>任何实现 IEnumerable 的集合</description></item>
    /// </list>
    /// <para><strong>使用示例：</strong></para>
    /// <code>ItemsSource="{Binding NavigationItems}"</code>
    /// </remarks>
    public static readonly DependencyProperty ItemsSourceProperty =
        DependencyProperty.Register(nameof(ItemsSource), typeof(System.Collections.IEnumerable), typeof(NavigationControl),
            new PropertyMetadata(null, OnItemsSourceChanged));

    /// <summary>
    /// 获取或设置主要数据源
    /// </summary>
    /// <value>包含导航项的可枚举集合</value>
    public System.Collections.IEnumerable ItemsSource
    {
        get => (System.Collections.IEnumerable)GetValue(ItemsSourceProperty);
        set => SetValue(ItemsSourceProperty, value);
    }

    /// <summary>
    /// 选中项依赖属性
    /// </summary>
    /// <remarks>
    /// <para>类似于 TreeView 的 SelectedItem，支持双向绑定</para>
    /// <para><strong>绑定特性：</strong></para>
    /// <list type="bullet">
    ///   <item><description>自动双向绑定（BindsTwoWayByDefault）</description></item>
    ///   <item><description>支持 PropertyChanged 通知</description></item>
    ///   <item><description>触发 SelectionChanged 路由事件</description></item>
    /// </list>
    /// <para><strong>使用示例：</strong></para>
    /// <code>SelectedItem="{Binding CurrentItem}"</code>
    /// </remarks>
    public static readonly DependencyProperty SelectedItemProperty =
        DependencyProperty.Register(nameof(SelectedItem), typeof(object), typeof(NavigationControl),
            new FrameworkPropertyMetadata(null, FrameworkPropertyMetadataOptions.BindsTwoWayByDefault, OnSelectedItemChanged));

    /// <summary>
    /// 获取或设置当前选中项
    /// </summary>
    /// <value>当前选中的导航项对象</value>
    public object SelectedItem
    {
        get => GetValue(SelectedItemProperty);
        set => SetValue(SelectedItemProperty, value);
    }

    /// <summary>
    /// 搜索文本依赖属性
    /// </summary>
    /// <remarks>
    /// <para>用于实时搜索过滤导航项</para>
    /// <para><strong>搜索特性：</strong></para>
    /// <list type="bullet">
    ///   <item><description>实时过滤：输入时立即更新显示结果</description></item>
    ///   <item><description>大小写不敏感：自动忽略大小写差异</description></item>
    ///   <item><description>支持部分匹配：包含搜索文本即可匹配</description></item>
    ///   <item><description>自动清空：切换到 ListView 模式时自动清空</description></item>
    /// </list>
    /// <para><strong>使用示例：</strong></para>
    /// <code>SearchText="{Binding SearchKeyword}"</code>
    /// </remarks>
    public static readonly DependencyProperty SearchTextProperty =
        DependencyProperty.Register(nameof(SearchText), typeof(string), typeof(NavigationControl),
            new FrameworkPropertyMetadata(string.Empty, FrameworkPropertyMetadataOptions.BindsTwoWayByDefault, OnSearchTextChanged));

    /// <summary>
    /// 获取或设置搜索文本
    /// </summary>
    /// <value>用于过滤导航项的搜索关键词</value>
    public string SearchText
    {
        get => (string)GetValue(SearchTextProperty);
        set => SetValue(SearchTextProperty, value);
    }

    /// <summary>
    /// 展开状态依赖属性
    /// </summary>
    /// <remarks>
    /// <para>控制 TreeView 的展开/折叠状态</para>
    /// <para><strong>展开行为：</strong></para>
    /// <list type="bullet">
    ///   <item><description>true：展开所有树节点</description></item>
    ///   <item><description>false：折叠所有树节点</description></item>
    ///   <item><description>支持双向绑定，可从外部控制</description></item>
    /// </list>
    /// <para><strong>使用示例：</strong></para>
    /// <code>IsExpanded="{Binding IsTreeExpanded}"</code>
    /// </remarks>
    public static readonly DependencyProperty IsExpandedProperty =
        DependencyProperty.Register(nameof(IsExpanded), typeof(bool), typeof(NavigationControl),
            new FrameworkPropertyMetadata(false, FrameworkPropertyMetadataOptions.BindsTwoWayByDefault, OnIsExpandedChanged));

    /// <summary>
    /// 获取或设置树形结构的展开状态
    /// </summary>
    /// <value>true 表示展开，false 表示折叠</value>
    public bool IsExpanded
    {
        get => (bool)GetValue(IsExpandedProperty);
        set => SetValue(IsExpandedProperty, value);
    }

    /// <summary>
    /// 顶部导航项依赖属性
    /// </summary>
    /// <remarks>
    /// <para>用于绑定显示在顶部区域的导航项（上方ListBox和上方TreeView共享）</para>
    /// <para><strong>显示位置：</strong></para>
    /// <list type="bullet">
    ///   <item><description>ListView 模式：显示在左侧上方列表</description></item>
    ///   <item><description>TreeView 模式：显示在右侧上方树形结构</description></item>
    /// </list>
    /// <para><strong>数据绑定：</strong></para>
    /// <list type="bullet">
    ///   <item><description>支持 ObservableCollection 自动更新</description></item>
    ///   <item><description>支持层级结构（Children 属性）</description></item>
    ///   <item><description>支持图标、文本、命令等完整功能</description></item>
    /// </list>
    /// <para><strong>使用示例：</strong></para>
    /// <code>TopNavigationItems="{Binding MainMenuItems}"</code>
    /// </remarks>
    public static readonly DependencyProperty TopNavigationItemsProperty =
        YDependencyPropertyHelper.Register<ObservableCollection<ZyloNavigationItemModel>, NavigationControl>(
            nameof(TopNavigationItems),
            propertyChangedCallback: OnTopNavigationItemsChanged);

    /// <summary>
    /// 获取或设置顶部导航项集合
    /// </summary>
    /// <value>包含顶部导航项的可观察集合</value>
    public ObservableCollection<ZyloNavigationItemModel> TopNavigationItems
    {
        get => (ObservableCollection<ZyloNavigationItemModel>)GetValue(TopNavigationItemsProperty);
        set => SetValue(TopNavigationItemsProperty, value);
    }

    /// <summary>
    /// 下方导航项数据源依赖属性
    /// </summary>
    /// <remarks>
    /// 下方区域的数据源，同时供 ListView 和 TreeView 使用
    /// 特点：
    /// - 不受搜索功能影响，始终显示完整数据
    /// - 通常用于显示固定的导航项（如设置、帮助等）
    /// - 支持层级结构，可以包含子项目
    /// - 独立于上方区域，有自己的选中状态
    ///
    /// 数据流向：
    /// BottomNavigationItems → BottomListItems (ListView显示)
    /// BottomNavigationItems → BottomTreeItems (TreeView显示)
    /// </remarks>
    public static readonly DependencyProperty BottomNavigationItemsProperty =
        YDependencyPropertyHelper.Register<ObservableCollection<ZyloNavigationItemModel>, NavigationControl>(
            nameof(BottomNavigationItems),
            propertyChangedCallback: OnBottomNavigationItemsChanged);

    /// <summary>
    /// 获取或设置下方导航项数据源
    /// </summary>
    /// <value>包含 ZyloNavigationItemModel 的可观察集合</value>
    public ObservableCollection<ZyloNavigationItemModel> BottomNavigationItems
    {
        get => (ObservableCollection<ZyloNavigationItemModel>)GetValue(BottomNavigationItemsProperty);
        set => SetValue(BottomNavigationItemsProperty, value);
    }

    /// <summary>
    /// 右侧列宽度依赖属性
    /// </summary>
    /// <remarks>
    /// 控制右侧 TreeView 区域的宽度
    ///
    /// 使用方式：
    /// - 在 XAML 中设置：RightColumnWidth="600"
    /// - 在代码中设置：navigationControl.RightColumnWidth = 600;
    ///
    /// 注意：
    /// - 设置为 0 或负数时，使用默认宽度（600）
    /// - 此属性只在右侧显示时生效（IsLeftVisible = false）
    /// - 左右切换时会保持用户设定的宽度
    /// </remarks>
    public static readonly DependencyProperty RightColumnWidthProperty =
        YDependencyPropertyHelper.Register<double, NavigationControl>(
            nameof(RightColumnWidth),
            defaultValue: 0,
            propertyChangedCallback: OnRightColumnWidthChanged);

    /// <summary>
    /// 获取或设置右侧列宽度
    /// </summary>
    /// <value>右侧列的宽度，单位为像素</value>
    public double RightColumnWidth
    {
        get => (double)GetValue(RightColumnWidthProperty);
        set => SetValue(RightColumnWidthProperty, value);
    }

    /// <summary>
    /// NavigationControl 整体宽度依赖属性
    /// </summary>
    public static readonly DependencyProperty NavigationControlWidthProperty =
        YDependencyPropertyHelper.Register<double, NavigationControl>(
            nameof(NavigationControlWidth),
            defaultValue: 48,
            propertyChangedCallback: OnNavigationControlWidthChanged);

    /// <summary>
    /// 获取或设置 NavigationControl 整体宽度
    /// </summary>
    /// <value>整体宽度，单位为像素</value>
    public double NavigationControlWidth
    {
        get => (double)GetValue(NavigationControlWidthProperty);
        set => SetValue(NavigationControlWidthProperty, value);
    }

    /// <summary>
    /// NavigationControlWidth 属性变化处理
    /// </summary>
    private static void OnNavigationControlWidthChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        // 不需要特殊处理，直接绑定即可
    }

    /// <summary>
    /// ListBox选中项依赖属性（左上、左下ListBox共享）
    /// </summary>
    public static readonly DependencyProperty SelectedListItemProperty =
        YDependencyPropertyHelper.Register<ZyloNavigationItemModel, NavigationControl>(
            nameof(SelectedListItem));

    /// <summary>
    /// ListBox选中项（左上、左下ListBox共享）
    /// </summary>
    public ZyloNavigationItemModel SelectedListItem
    {
        get => (ZyloNavigationItemModel)GetValue(SelectedListItemProperty);
        set => SetValue(SelectedListItemProperty, value);
    }

    /// <summary>
    /// TreeView选中项依赖属性（右上、右下TreeView共享）
    /// </summary>
    public static readonly DependencyProperty SelectedTreeItemProperty =
        YDependencyPropertyHelper.Register<ZyloNavigationItemModel, NavigationControl>(
            nameof(SelectedTreeItem));

    /// <summary>
    /// TreeView选中项（右上、右下TreeView共享）
    /// </summary>
    public ZyloNavigationItemModel SelectedTreeItem
    {
        get => (ZyloNavigationItemModel)GetValue(SelectedTreeItemProperty);
        set => SetValue(SelectedTreeItemProperty, value);
    }

    #endregion

    #region 命令依赖属性
    // ================================================================
    // 命令相关的依赖属性
    // 提供对外的命令接口，支持 MVVM 模式的事件处理
    // ================================================================

    /// <summary>
    /// 导航项选中命令依赖属性
    /// </summary>
    /// <remarks>
    /// 当用户选择导航项时触发的命令
    ///
    /// 命令参数：ZyloNavigationItemModel - 被选中的导航项
    ///
    /// 使用场景：
    /// - 处理导航逻辑（页面跳转、内容切换等）
    /// - 更新应用程序状态
    /// - 记录用户行为
    ///
    /// 绑定示例：
    /// NavigationItemSelectedCommand="{Binding NavigateCommand}"
    ///
    /// 自动绑定：
    /// 如果未手动绑定，控件会自动从 DataContext 中查找以下命令：
    /// - NavigateToItemCommand, NavigateCommand, SelectItemCommand 等
    /// </remarks>
    public static readonly DependencyProperty NavigationItemSelectedCommandProperty =
        YDependencyPropertyHelper.Register<IRelayCommand<ZyloNavigationItemModel>, NavigationControl>(
            nameof(NavigationItemSelectedCommand));

    /// <summary>
    /// 获取或设置导航项选中命令
    /// </summary>
    /// <value>接受 ZyloNavigationItemModel 参数的命令</value>
    public IRelayCommand<ZyloNavigationItemModel> NavigationItemSelectedCommand
    {
        get => (IRelayCommand<ZyloNavigationItemModel>)GetValue(NavigationItemSelectedCommandProperty);
        set => SetValue(NavigationItemSelectedCommandProperty, value);
    }

    /// <summary>
    /// 搜索文本变化命令依赖属性
    /// </summary>
    /// <remarks>
    /// 当搜索文本发生变化时触发的命令
    ///
    /// 命令参数：string - 新的搜索文本
    ///
    /// 使用场景：
    /// - 实现自定义搜索逻辑
    /// - 记录搜索历史
    /// - 搜索结果统计
    ///
    /// 注意：
    /// - 控件内部已实现基本搜索功能
    /// - 此命令用于扩展搜索行为
    /// - 搜索只影响上方区域，下方区域不受影响
    /// </remarks>
    public static readonly DependencyProperty SearchTextChangedCommandProperty =
        YDependencyPropertyHelper.Register<IRelayCommand<string>, NavigationControl>(
            nameof(SearchTextChangedCommand));

    /// <summary>
    /// 获取或设置搜索文本变化命令
    /// </summary>
    /// <value>接受 string 参数的命令</value>
    public IRelayCommand<string> SearchTextChangedCommand
    {
        get => (IRelayCommand<string>)GetValue(SearchTextChangedCommandProperty);
        set => SetValue(SearchTextChangedCommandProperty, value);
    }

    /// <summary>
    /// 导航切换命令依赖属性
    /// </summary>
    /// <remarks>
    /// 用于切换导航显示模式的命令
    ///
    /// 功能：
    /// - 在 ListView（图标模式）和 TreeView（树形模式）之间切换
    /// - 控制 IsLeftVisible 属性的值
    ///
    /// 使用场景：
    /// - 提供用户界面切换功能
    /// - 响应快捷键或手势操作
    /// - 程序化控制显示模式
    ///
    /// 内部实现：
    /// - 切换 IsLeftVisible 属性
    /// - 自动调整列宽
    /// - 清空搜索文本（切换到 ListView 时）
    /// </remarks>
    public static readonly DependencyProperty NavigationToggleCommandProperty =
        YDependencyPropertyHelper.Register<IRelayCommand, NavigationControl>(
            nameof(NavigationToggleCommand));

    /// <summary>
    /// 获取或设置导航切换命令
    /// </summary>
    /// <value>无参数的命令</value>
    public IRelayCommand NavigationToggleCommand
    {
        get => (IRelayCommand)GetValue(NavigationToggleCommandProperty);
        set => SetValue(NavigationToggleCommandProperty, value);
    }

    #endregion

    #region 路由事件
    // ================================================================
    // 标准 WPF 路由事件
    // 提供类似于标准控件的事件接口，支持事件冒泡
    // ================================================================

    /// <summary>
    /// 选中项改变路由事件
    /// </summary>
    /// <remarks>
    /// 类似于 TreeView 的 SelectedItemChanged 事件
    ///
    /// 事件参数：
    /// - OldValue: 之前选中的项目
    /// - NewValue: 新选中的项目
    ///
    /// 触发时机：
    /// - 用户点击导航项
    /// - 程序化设置 SelectedItem 属性
    /// - 通过键盘导航选择项目
    ///
    /// 事件路由：
    /// - 使用冒泡路由策略
    /// - 可以在父级元素中处理
    ///
    /// 与命令的区别：
    /// - 事件：传统的 WPF 事件处理方式
    /// - 命令：MVVM 模式的推荐方式
    /// </remarks>
    public static readonly RoutedEvent SelectionChangedEvent =
        EventManager.RegisterRoutedEvent(nameof(SelectionChanged), RoutingStrategy.Bubble,
            typeof(RoutedPropertyChangedEventHandler<object>), typeof(NavigationControl));

    /// <summary>
    /// 选中项改变事件
    /// </summary>
    public event RoutedPropertyChangedEventHandler<object> SelectionChanged
    {
        add => AddHandler(SelectionChangedEvent, value);
        remove => RemoveHandler(SelectionChangedEvent, value);
    }

    /// <summary>
    /// 项目双击路由事件
    /// </summary>
    /// <remarks>
    /// 当用户双击导航项时触发
    ///
    /// 使用场景：
    /// - 快速导航或激活功能
    /// - 展开/收缩树形节点
    /// - 执行默认操作
    ///
    /// 事件参数：
    /// - Source: 触发事件的导航项
    /// - OriginalSource: 实际被双击的 UI 元素
    ///
    /// 注意：
    /// - 双击事件会在选中事件之后触发
    /// - 可以通过 e.Handled = true 阻止事件继续传播
    /// </remarks>
    public static readonly RoutedEvent ItemDoubleClickEvent =
        EventManager.RegisterRoutedEvent(nameof(ItemDoubleClick), RoutingStrategy.Bubble,
            typeof(RoutedEventHandler), typeof(NavigationControl));

    /// <summary>
    /// 项目双击事件
    /// </summary>
    public event RoutedEventHandler ItemDoubleClick
    {
        add => AddHandler(ItemDoubleClickEvent, value);
        remove => RemoveHandler(ItemDoubleClickEvent, value);
    }

    #endregion

    #region 标准属性变化回调

    private static void OnItemsSourceChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is NavigationControl control)
        {
            control.OnItemsSourceChanged(e.OldValue as System.Collections.IEnumerable, e.NewValue as System.Collections.IEnumerable);
        }
    }

    private static void OnSelectedItemChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is NavigationControl control)
        {
            control.OnSelectedItemChanged(e.OldValue, e.NewValue);
        }
    }

    private static void OnSearchTextChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is NavigationControl control)
        {
            control.OnSearchTextChanged(e.OldValue as string, e.NewValue as string);
        }
    }

    private static void OnIsExpandedChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is NavigationControl control)
        {
            control.OnIsExpandedChanged((bool)e.OldValue, (bool)e.NewValue);
        }
    }

    #endregion

    #region 搜索功能属性

    /// <summary>
    /// 是否显示搜索框
    /// </summary>
    public static readonly DependencyProperty ShowSearchBoxProperty =
        DependencyProperty.Register(nameof(ShowSearchBox), typeof(bool), typeof(NavigationControl),
            new PropertyMetadata(true));

    public bool ShowSearchBox
    {
        get => (bool)GetValue(ShowSearchBoxProperty);
        set => SetValue(ShowSearchBoxProperty, value);
    }

    /// <summary>
    /// 是否高亮搜索结果
    /// </summary>
    public static readonly DependencyProperty HighlightSearchResultsProperty =
        DependencyProperty.Register(nameof(HighlightSearchResults), typeof(bool), typeof(NavigationControl),
            new PropertyMetadata(true));

    public bool HighlightSearchResults
    {
        get => (bool)GetValue(HighlightSearchResultsProperty);
        set => SetValue(HighlightSearchResultsProperty, value);
    }



    #endregion

    #region DefaultPosition 依赖属性

    /// <summary>
    /// 默认显示位置枚举
    /// </summary>
    public enum NavigationDefaultPosition
    {
        Left,   // 默认显示左边（ListView模式）
        Right   // 默认显示右边（TreeView模式）
    }

    /// <summary>
    /// 默认显示位置依赖属性
    /// </summary>
    public static readonly DependencyProperty DefaultPositionProperty =
        DependencyProperty.Register(
            nameof(DefaultPosition),
            typeof(NavigationDefaultPosition),
            typeof(NavigationControl),
            new PropertyMetadata(NavigationDefaultPosition.Right, OnDefaultPositionChanged));

    /// <summary>
    /// 默认显示位置 - 控制NavigationControl启动时显示左边还是右边
    /// </summary>
    public NavigationDefaultPosition DefaultPosition
    {
        get => (NavigationDefaultPosition)GetValue(DefaultPositionProperty);
        set => SetValue(DefaultPositionProperty, value);
    }

    /// <summary>
    /// DefaultPosition 属性变化处理
    /// </summary>
    private static void OnDefaultPositionChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is NavigationControl control)
        {
            var newPosition = (NavigationDefaultPosition)e.NewValue;
            control.IsLeftVisible = newPosition == NavigationDefaultPosition.Left;
        }
    }

    #endregion

    #region AllowCollapse 依赖属性

    /// <summary>
    /// 是否允许折叠依赖属性
    /// </summary>
    public static readonly DependencyProperty AllowCollapseProperty =
        DependencyProperty.Register(
            nameof(AllowCollapse),
            typeof(bool),
            typeof(NavigationControl),
            new PropertyMetadata(true, OnAllowCollapseChanged));

    /// <summary>
    /// 是否允许折叠 - 控制导航项是否可以折叠/展开
    /// </summary>
    public bool AllowCollapse
    {
        get => (bool)GetValue(AllowCollapseProperty);
        set => SetValue(AllowCollapseProperty, value);
    }

    /// <summary>
    /// AllowCollapse 属性变化处理
    /// </summary>
    private static void OnAllowCollapseChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is NavigationControl control && e.NewValue is bool allowCollapse)
        {
            control.UpdateCollapseState(allowCollapse);
        }
    }

    #endregion

    #region ShowToggleButton 依赖属性

    /// <summary>
    /// 是否显示切换按钮依赖属性
    /// </summary>
    public static readonly DependencyProperty ShowToggleButtonProperty =
        DependencyProperty.Register(
            nameof(ShowToggleButton),
            typeof(bool),
            typeof(NavigationControl),
            new PropertyMetadata(true));

    /// <summary>
    /// 是否显示切换按钮 - 控制汉堡菜单按钮的显示/隐藏
    /// </summary>
    public bool ShowToggleButton
    {
        get => (bool)GetValue(ShowToggleButtonProperty);
        set => SetValue(ShowToggleButtonProperty, value);
    }

    #endregion

    #region 折叠状态管理

    /// <summary>
    /// 更新折叠状态
    /// </summary>
    private void UpdateCollapseState(bool allowCollapse)
    {
        if (!allowCollapse)
        {
            // 不允许折叠时，展开所有项目
            ExpandAllItems(TopNavigationItems);
            ExpandAllItems(BottomNavigationItems);
        }
    }

    /// <summary>
    /// 展开所有项目
    /// </summary>
    private void ExpandAllItems(ObservableCollection<ZyloNavigationItemModel>? items)
    {
        if (items == null) return;

        foreach (var item in items)
        {
            item.IsExpanded = true;
            if (item.Children?.Count > 0)
            {
                ExpandAllItems(item.Children);
            }
        }
    }

    #endregion

    #region ShowSubMenuOnClick 依赖属性

    /// <summary>
    /// 是否在点击时显示子菜单依赖属性
    /// </summary>
    public static readonly DependencyProperty ShowSubMenuOnClickProperty =
        DependencyProperty.Register(
            nameof(ShowSubMenuOnClick),
            typeof(bool),
            typeof(NavigationControl),
            new PropertyMetadata(true));

    /// <summary>
    /// 是否在点击时显示子菜单 - 控制有子项目的项目点击时是否显示弹出菜单
    /// </summary>
    public bool ShowSubMenuOnClick
    {
        get => (bool)GetValue(ShowSubMenuOnClickProperty);
        set => SetValue(ShowSubMenuOnClickProperty, value);
    }

    #endregion

    #region SubMenuItems 依赖属性

    /// <summary>
    /// 子菜单项集合依赖属性
    /// </summary>
    public static readonly DependencyProperty SubMenuItemsProperty =
        DependencyProperty.Register(
            nameof(SubMenuItems),
            typeof(ObservableCollection<ZyloNavigationItemModel>),
            typeof(NavigationControl),
            new PropertyMetadata(null));

    /// <summary>
    /// 子菜单项集合 - 用于Popup显示
    /// </summary>
    public ObservableCollection<ZyloNavigationItemModel> SubMenuItems
    {
        get => (ObservableCollection<ZyloNavigationItemModel>)GetValue(SubMenuItemsProperty);
        set => SetValue(SubMenuItemsProperty, value);
    }

    #endregion

    #region SubMenuItemClickCommand 依赖属性

    /// <summary>
    /// 子菜单项点击命令依赖属性
    /// </summary>
    public static readonly DependencyProperty SubMenuItemClickCommandProperty =
        DependencyProperty.Register(
            nameof(SubMenuItemClickCommand),
            typeof(IRelayCommand<ZyloNavigationItemModel>),
            typeof(NavigationControl),
            new PropertyMetadata(null));

    /// <summary>
    /// 子菜单项点击命令
    /// </summary>
    public IRelayCommand<ZyloNavigationItemModel> SubMenuItemClickCommand
    {
        get => (IRelayCommand<ZyloNavigationItemModel>)GetValue(SubMenuItemClickCommandProperty);
        set => SetValue(SubMenuItemClickCommandProperty, value);
    }

    #endregion

    #region 布局控制属性

    /// <summary>
    /// TreeView列宽（展开模式下的右侧TreeView宽度）
    /// </summary>
    public static readonly DependencyProperty TreeViewColumnWidthProperty =
        DependencyProperty.Register(nameof(TreeViewColumnWidth), typeof(GridLength), typeof(NavigationControl),
            new PropertyMetadata(new GridLength(1, GridUnitType.Star)));

    public GridLength TreeViewColumnWidth
    {
        get => (GridLength)GetValue(TreeViewColumnWidthProperty);
        set => SetValue(TreeViewColumnWidthProperty, value);
    }

    /// <summary>
    /// 是否启用响应式布局
    /// </summary>
    public static readonly DependencyProperty EnableResponsiveLayoutProperty =
        DependencyProperty.Register(nameof(EnableResponsiveLayout), typeof(bool), typeof(NavigationControl),
            new PropertyMetadata(true));

    public bool EnableResponsiveLayout
    {
        get => (bool)GetValue(EnableResponsiveLayoutProperty);
        set => SetValue(EnableResponsiveLayoutProperty, value);
    }

    #endregion

    #region 交互增强属性

    /// <summary>
    /// 右键菜单命令
    /// </summary>
    public static readonly DependencyProperty ItemContextMenuCommandProperty =
        DependencyProperty.Register(nameof(ItemContextMenuCommand), typeof(ICommand), typeof(NavigationControl));

    public ICommand ItemContextMenuCommand
    {
        get => (ICommand)GetValue(ItemContextMenuCommandProperty);
        set => SetValue(ItemContextMenuCommandProperty, value);
    }

    /// <summary>
    /// 双击命令
    /// </summary>
    public static readonly DependencyProperty ItemDoubleClickCommandProperty =
        DependencyProperty.Register(nameof(ItemDoubleClickCommand), typeof(ICommand), typeof(NavigationControl));

    public ICommand ItemDoubleClickCommand
    {
        get => (ICommand)GetValue(ItemDoubleClickCommandProperty);
        set => SetValue(ItemDoubleClickCommandProperty, value);
    }

    /// <summary>
    /// 是否启用拖拽支持
    /// </summary>
    public static readonly DependencyProperty EnableDragDropProperty =
        DependencyProperty.Register(nameof(EnableDragDrop), typeof(bool), typeof(NavigationControl),
            new PropertyMetadata(false));

    public bool EnableDragDrop
    {
        get => (bool)GetValue(EnableDragDropProperty);
        set => SetValue(EnableDragDropProperty, value);
    }

    #endregion

    #region 子菜单管理

    /// <summary>
    /// 初始化子菜单命令
    /// </summary>
    private void InitializeSubMenuCommand()
    {
        SubMenuItemClickCommand = new RelayCommand<ZyloNavigationItemModel>(OnSubMenuItemClick);
    }

    /// <summary>
    /// 处理子菜单项点击
    /// </summary>
    private void OnSubMenuItemClick(ZyloNavigationItemModel item)
    {
        if (item == null) return;

        try
        {
            // 关闭子菜单弹出窗口
            CloseSubMenu();

            // 触发导航命令
            if (NavigationItemSelectedCommand?.CanExecute(item) == true)
            {
                NavigationItemSelectedCommand.Execute(item);
            }
        }
        catch (Exception ex)
        {
            // 静默处理异常
        }
    }

    /// <summary>
    /// 显示子菜单页面
    /// </summary>
    private void ShowSubMenu(ZyloNavigationItemModel parentItem, FrameworkElement targetElement)
    {
        if (parentItem?.Children == null || parentItem.Children.Count == 0)
            return;

        try
        {
            // 更新选中项
            SelectedItem = parentItem;

            // 执行导航命令，传递父项目信息
            if (NavigationItemSelectedCommand?.CanExecute(parentItem) == true)
            {
                NavigationItemSelectedCommand.Execute(parentItem);
            }
        }
        catch (Exception ex)
        {
            // 静默处理异常
        }
    }

    /// <summary>
    /// 关闭子菜单
    /// </summary>
    private void CloseSubMenu()
    {
        try
        {
            var popup = this.FindName("SubMenuPopup") as Popup;
            if (popup != null && popup.IsOpen)
            {
                popup.IsOpen = false;
            }
        }
        catch (Exception ex)
        {
            // 静默处理异常
        }
    }

    #endregion

    #region ObservableProperty 属性

    /// <summary>
    /// 上方ListBox显示项（来自TopNavigationItems）
    /// </summary>
    [ObservableProperty]
    public partial ObservableCollection<ZyloNavigationItemModel> TopListItems { get; set; } = new();

    /// <summary>
    /// 下方ListBox显示项（来自BottomNavigationItems）
    /// </summary>
    [ObservableProperty]
    public partial ObservableCollection<ZyloNavigationItemModel> BottomListItems { get; set; } = new();

    /// <summary>
    /// 上方TreeView显示项（来自TopNavigationItems）
    /// </summary>
    [ObservableProperty]
    public partial ObservableCollection<ZyloNavigationItemModel> TopTreeItems { get; set; } = new();

    /// <summary>
    /// 下方TreeView显示项（来自BottomNavigationItems）
    /// </summary>
    [ObservableProperty]
    public partial ObservableCollection<ZyloNavigationItemModel> BottomTreeItems { get; set; } = new();





    /// <summary>
    /// 左侧是否可见
    /// </summary>
    [ObservableProperty]
    public partial bool IsLeftVisible { get; set; }

    #endregion

    #region Commands

    /// <summary>
    /// 切换显示命令
    /// </summary>
    [RelayCommand]
    private void ToggleDisplay()
    {
        IsLeftVisible = !IsLeftVisible;
        UpdateColumnWidths();

        // 🔍 当切换到 ListView 时（IsLeftVisible = true），清空搜索文字
        if (IsLeftVisible)
        {
            SearchText = string.Empty;
            // 只恢复顶部数据，底部保持不变
            RestoreTopOriginalData();
        }

        // 触发外部命令
        NavigationToggleCommand?.Execute(null);
    }

    /// <summary>
    /// 内部导航项选中命令
    /// </summary>
    [RelayCommand]
    private void InternalNavigationItemSelected(ZyloNavigationItemModel item)
    {
        if (item == null) return;

        // 更新选中项
        SelectedListItem = item;
        SelectedTreeItem = item;

        // 触发外部命令
        NavigationItemSelectedCommand?.Execute(item);
    }

    /// <summary>
    /// 内部搜索文本变化命令
    /// </summary>
    [RelayCommand]
    private void InternalSearchTextChanged(string searchText)
    {
        SearchText = searchText ?? string.Empty;

        // 触发外部命令
        SearchTextChangedCommand?.Execute(SearchText);

        // 执行内部搜索逻辑
        PerformSearch(SearchText);
    }

    #endregion

    #region 构造函数和初始化
    // ================================================================
    // 控件初始化：构造函数、事件绑定、初始状态设置
    // ================================================================

    /// <summary>
    /// 初始化 NavigationControl 的新实例
    /// </summary>
    /// <remarks>
    /// <para><strong>初始化流程：</strong></para>
    /// <list type="number">
    ///   <item><description>调用 InitializeComponent() 加载 XAML 布局</description></item>
    ///   <item><description>保持 DataContext 为 null，确保外部绑定正常工作</description></item>
    ///   <item><description>初始化列宽设置（默认显示左侧 48px）</description></item>
    ///   <item><description>绑定 Loaded 和 DataContextChanged 事件</description></item>
    /// </list>
    ///
    /// <para><strong>设计原则：</strong></para>
    /// <list type="bullet">
    ///   <item><description>🔗 <strong>外部绑定优先</strong>：不设置 DataContext，保持绑定透明</description></item>
    ///   <item><description>🎯 <strong>内部绑定独立</strong>：使用 ElementName 方式避免冲突</description></item>
    ///   <item><description>⚡ <strong>延迟初始化</strong>：在 Loaded 事件中进行命令绑定检查</description></item>
    /// </list>
    /// </remarks>
    public NavigationControl()
    {
        InitializeComponent();

        // 🔗 不设置 DataContext，保持外部绑定正常工作
        // 内部绑定使用 ElementName 方式，避免绑定冲突

        // 🎯 根据DefaultPosition初始化显示位置
        IsLeftVisible = DefaultPosition == NavigationDefaultPosition.Left;

        // 🎯 初始化列宽设置
        InitializeColumnWidths();

        // 🎯 初始化子菜单命令
        InitializeSubMenuCommand();

        // ⚡ 添加事件处理（延迟初始化）
        Loaded += NavigationControl_Loaded;
        DataContextChanged += NavigationControl_DataContextChanged;

        // 🎯 添加互斥选中事件处理
        Loaded += (s, e) => InitializeMutualExclusion();
    }

    /// <summary>
    /// 初始化列宽设置
    /// </summary>
    /// <remarks>
    /// <para>在构造函数中调用，确保列宽有正确的初始值</para>
    /// <para><strong>初始化逻辑：</strong></para>
    /// <list type="bullet">
    ///   <item><description>显示位置由DefaultPosition属性控制（默认右侧）</description></item>
    ///   <item><description>左侧宽度：48px（显示图标导航）</description></item>
    ///   <item><description>右侧宽度：用户设置值或默认 600px</description></item>
    /// </list>
    /// </remarks>
    private void InitializeColumnWidths()
    {
        // 🎯 设置初始显示状态（默认显示左侧）
        UpdateColumnWidths();
    }

    /// <summary>
    /// 控件加载完成事件处理
    /// </summary>
    /// <param name="sender">事件源</param>
    /// <param name="e">事件参数</param>
    /// <remarks>
    /// <para>在控件完全加载后执行的初始化操作</para>
    /// <para><strong>执行内容：</strong></para>
    /// <list type="bullet">
    ///   <item><description>检查并更新命令绑定</description></item>
    ///   <item><description>适用于任何 ViewModel 类型</description></item>
    /// </list>
    /// </remarks>
    private void NavigationControl_Loaded(object sender, RoutedEventArgs e)
    {
        // 🔗 通用的命令绑定检查 - 适用于任何ViewModel
        CheckAndUpdateCommandBindings();
    }

    #endregion

    #region 依赖属性变化处理

    /// <summary>
    /// TopNavigationItems依赖属性变化处理
    /// </summary>
    private static void OnTopNavigationItemsChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is not NavigationControl control) return;

        // 清空现有数据
        control.TopListItems.Clear();
        control.TopTreeItems.Clear();

        // 处理新数据
        if (e.NewValue is ObservableCollection<ZyloNavigationItemModel> newItems && newItems.Count > 0)
        {
            // ListBox只显示第一层（顶层）项目
            foreach (var item in newItems)
            {
                control.TopListItems.Add(item);
            }

            // TreeView显示完整的层级结构
            foreach (var item in newItems)
            {
                control.TopTreeItems.Add(item);
            }
        }
    }

    /// <summary>
    /// BottomNavigationItems依赖属性变化处理
    /// </summary>
    private static void OnBottomNavigationItemsChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        // 1. 检查是否是NavigationControl，并且新值是有效的集合
        if (d is NavigationControl control && e.NewValue is ObservableCollection<ZyloNavigationItemModel> newItems)
        {
            // 2. 清空下方ListBox的数据
            control.BottomListItems.Clear();
            // 3. ListBox只显示第一层（顶层）项目
            foreach (var item in newItems)
            {
                control.BottomListItems.Add(item);
            }

            // 4. 清空下方TreeView的数据
            control.BottomTreeItems.Clear();
            // 5. TreeView显示完整的层级结构
            foreach (var item in newItems)
                control.BottomTreeItems.Add(item);
        }
    }

    #endregion

    #region 属性变化处理

    /// <summary>
    /// IsLeftVisible变化时自动调用
    /// </summary>
    partial void OnIsLeftVisibleChanged(bool value)
    {
        UpdateColumnWidths();
    }

    #endregion

    #region 私有方法 - 核心业务逻辑
    // ================================================================
    // 核心业务逻辑实现：布局控制、搜索功能、数据处理等
    // 这些方法实现了 NavigationControl 的主要功能特性
    // ================================================================

    /// <summary>
    /// 更新列宽度 - 核心布局控制方法
    /// </summary>
    /// <remarks>
    /// <para><strong>🎯 设计理念：</strong></para>
    /// <para>采用用户提出的天才方案：在顶层 Grid 直接设置宽度，左右内容只控制互斥显示</para>
    ///
    /// <para><strong>📐 宽度控制逻辑：</strong></para>
    /// <list type="bullet">
    ///   <item><description><strong>左边模式</strong>：NavigationControlWidth = 48px（固定图标导航宽度）</description></item>
    ///   <item><description><strong>右边模式</strong>：NavigationControlWidth = RightColumnWidth（用户可配置）</description></item>
    /// </list>
    ///
    /// <para><strong>🎨 布局架构：</strong></para>
    /// <list type="number">
    ///   <item><description>顶层 Grid 设置 Width="{Binding NavigationControlWidth}"</description></item>
    ///   <item><description>左侧 Border 通过 Visibility 控制显示/隐藏</description></item>
    ///   <item><description>右侧 Border 通过 Visibility 控制显示/隐藏</description></item>
    ///   <item><description>显示的内容自动占满整个可用宽度</description></item>
    /// </list>
    ///
    /// <para><strong>✨ 优势特点：</strong></para>
    /// <list type="bullet">
    ///   <item><description>🚀 <strong>简单高效</strong>：避免复杂的 GridLength 绑定</description></item>
    ///   <item><description>🎯 <strong>直观明了</strong>：宽度控制集中在一个地方</description></item>
    ///   <item><description>🔧 <strong>易于维护</strong>：逻辑清晰，不易出错</description></item>
    ///   <item><description>📱 <strong>响应式</strong>：左右内容各自占满可用空间</description></item>
    /// </list>
    /// </remarks>
    private void UpdateColumnWidths()
    {
        if (IsLeftVisible)
        {
            // 🎯 左边模式：固定 48px，适合图标导航
            // 左边内容（ListView）会自动占满这 48px 宽度
            NavigationControlWidth = 48;
        }
        else
        {
            // 🎯 右边模式：使用用户设置的宽度，适合详细内容展示
            // 右边内容（TreeView + 搜索）会自动占满这个宽度
            NavigationControlWidth = RightColumnWidth > 0 ? RightColumnWidth : 250; // 默认 600px
        }
    }

    /// <summary>
    /// 执行搜索 - 只影响顶部控件，底部控件保持不变
    /// </summary>
    private void PerformSearch(string searchText)
    {
        if (string.IsNullOrWhiteSpace(searchText))
        {
            // 恢复顶部原始数据
            RestoreTopOriginalData();
            return;
        }

        // 只搜索上方项目，底部保持不变
        var filteredTopItems = FilterItems(TopNavigationItems, searchText);
        TopListItems.Clear();
        TopTreeItems.Clear();
        foreach (var item in filteredTopItems)
        {
            TopListItems.Add(item);
            TopTreeItems.Add(item);
        }
        // 注意：底部的 BottomListItems 和 BottomTreeItems 不受搜索影响
    }

    /// <summary>
    /// 过滤项目
    /// </summary>
    private List<ZyloNavigationItemModel> FilterItems(ObservableCollection<ZyloNavigationItemModel> items, string searchText)
    {
        if (items == null) return new List<ZyloNavigationItemModel>();

        var result = new List<ZyloNavigationItemModel>();
        var searchLower = searchText.ToLower();

        foreach (var item in items)
        {
            if (item.Name.ToLower().Contains(searchLower) ||
                item.Number.ToLower().Contains(searchLower) ||
                (!string.IsNullOrEmpty(item.NavigationTarget) && item.NavigationTarget.ToLower().Contains(searchLower)))
            {
                result.Add(item);
            }

            // 递归搜索子项
            if (item.Children?.Count > 0)
            {
                var filteredChildren = FilterItems(new ObservableCollection<ZyloNavigationItemModel>(item.Children), searchText);
                result.AddRange(filteredChildren);
            }
        }

        return result;
    }

    /// <summary>
    /// 恢复顶部原始数据 - 只恢复顶部，底部不变
    /// </summary>
    private void RestoreTopOriginalData()
    {
        // 只恢复上方数据
        TopListItems.Clear();
        TopTreeItems.Clear();
        if (TopNavigationItems != null)
        {
            foreach (var item in TopNavigationItems)
            {
                TopListItems.Add(item);
                TopTreeItems.Add(item);
            }
        }

        // 注意：底部数据保持不变
    }

    /// <summary>
    /// 恢复原始数据 - 恢复所有数据（用于初始化）
    /// </summary>
    private void RestoreOriginalData()
    {
        // 恢复上方数据
        TopListItems.Clear();
        TopTreeItems.Clear();
        if (TopNavigationItems != null)
        {
            foreach (var item in TopNavigationItems)
            {
                TopListItems.Add(item);
                TopTreeItems.Add(item);
            }
        }

        // 恢复下方数据
        BottomListItems.Clear();
        BottomTreeItems.Clear();
        if (BottomNavigationItems != null)
        {
            foreach (var item in BottomNavigationItems)
            {
                BottomListItems.Add(item);
                BottomTreeItems.Add(item);
            }
        }
    }

    private double oldLeftColumnWidthT = 48;

    /// <summary>
    /// RightColumnWidth 属性变化处理
    /// </summary>
    /// <remarks>
    /// 当用户设置RightColumnWidth时，如果当前显示右侧模式，立即更新宽度
    /// </remarks>
    private static void OnRightColumnWidthChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is NavigationControl control)
        {
            // 如果当前显示右侧模式，立即更新宽度
            if (!control.IsLeftVisible)
            {
                control.UpdateColumnWidths();
            }
        }
    }

    #endregion

    #region 标准方法实现

    /// <summary>
    /// 处理ItemsSource变化
    /// </summary>
    protected virtual void OnItemsSourceChanged(System.Collections.IEnumerable oldValue, System.Collections.IEnumerable newValue)
    {
        // 这里可以实现标准的ItemsSource处理逻辑
        // 暂时保持兼容性，将新数据设置到现有属性
        if (newValue is ObservableCollection<ZyloNavigationItemModel> items)
        {
            TopNavigationItems = items;
        }
    }

    /// <summary>
    /// 处理SelectedItem变化
    /// </summary>
    protected virtual void OnSelectedItemChanged(object oldValue, object newValue)
    {
        // 触发标准事件
        var args = new RoutedPropertyChangedEventArgs<object>(oldValue, newValue, SelectionChangedEvent);
        RaiseEvent(args);

        // 保持兼容性
        if (newValue is ZyloNavigationItemModel item)
        {
            SelectedListItem = item;
            SelectedTreeItem = item;
        }

        // 检查并更新命令绑定
        CheckAndUpdateCommandBindings();
    }

    /// <summary>
    /// 处理SearchText变化
    /// </summary>
    protected virtual void OnSearchTextChanged(string oldValue, string newValue)
    {
        // 执行搜索逻辑
        PerformSearch(newValue ?? string.Empty);
    }

    /// <summary>
    /// 处理IsExpanded变化
    /// </summary>
    protected virtual void OnIsExpandedChanged(bool oldValue, bool newValue)
    {


        // 这里可以实现展开/收起逻辑
        // 暂时保持现有的切换逻辑
        if (newValue != oldValue)
        {
            ToggleDisplayCommand?.Execute(null);
        }
    }

    /// <summary>
    /// 检查并更新命令绑定 - 支持多种常见命令名称
    /// </summary>
    private void CheckAndUpdateCommandBindings()
    {
        // 如果命令还没有绑定，尝试从DataContext获取
        if (NavigationItemSelectedCommand == null && DataContext != null)
        {
       

            var dataContextType = DataContext.GetType();

            // 支持多种常见的命令名称
            string[] possibleCommandNames = {
                "NavigateToItemCommand",    // 默认名称
                "NavigateCommand",          // 简化名称
                "SelectItemCommand",        // 选择命令
                "ItemSelectedCommand",      // 项目选中命令
                "NavigationCommand",        // 导航命令
                "OnNavigateCommand"         // 事件风格命名
            };

            foreach (var commandName in possibleCommandNames)
            {
                var commandProperty = dataContextType.GetProperty(commandName);
                if (commandProperty != null)
                {
                    var command = commandProperty.GetValue(DataContext);
                    if (TrySetCommand(command, commandName))
                    {
                        return; // 找到并设置成功，退出
                    }
                }
            }

          
        }
    }

    /// <summary>
    /// 尝试设置命令
    /// </summary>
    private bool TrySetCommand(object command, string commandName)
    {
        if (command is IRelayCommand<ZyloNavigationItemModel> relayCommand)
        {
            NavigationItemSelectedCommand = relayCommand;
        
            return true;
        }
        else if (command is ICommand genericCommand)
        {
            // 创建一个包装器来适配类型
            NavigationItemSelectedCommand = new RelayCommand<ZyloNavigationItemModel>(item =>
            {
                if (genericCommand.CanExecute(item))
                {
                    genericCommand.Execute(item);
                }
            });
            return true;
        }
        else if (command != null)
        {
            // 命令类型不匹配
        }

        return false;
    }

    #endregion

    #region 交互增强实现

    /// <summary>
    /// 处理项目双击
    /// </summary>
    private void OnItemDoubleClick(ZyloNavigationItemModel item)
    {
       
        ItemDoubleClickCommand?.Execute(item);
    }

    /// <summary>
    /// 处理项目右键菜单
    /// </summary>
    private void OnItemContextMenu(ZyloNavigationItemModel item)
    {
       
        ItemContextMenuCommand?.Execute(item);
    }

    #endregion

    #region 事件处理

    private void NavigationControl_DataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
    {
    
        // DataContext变化时检查命令绑定
        CheckAndUpdateCommandBindings();
    }

    #endregion

    #region 互斥选中功能
    // ================================================================
    // 互斥选中功能：实现 ListView 和 TreeView 的互斥选中效果
    // 使用相同的逻辑处理所有视图控件
    // ================================================================

    /// <summary>
    /// 初始化互斥选中功能
    /// </summary>
    /// <remarks>
    /// 为所有视图控件（ListView 和 TreeView）添加互斥选中功能，
    /// 确保同时只有一个控件中的一个项目被选中。
    /// </remarks>
    private void InitializeMutualExclusion()
    {
        // 🔍 查找 ListView 控件
        var topListView = this.FindName("TopListView") as System.Windows.Controls.ListView;
        var secondListView = this.FindName("SecondListView") as System.Windows.Controls.ListView;

        // 🔍 查找 TreeView 控件
        var topTreeView = this.FindName("TopTreeView") as System.Windows.Controls.TreeView;
        var secondTreeView = this.FindName("SecondTreeView") as System.Windows.Controls.TreeView;

        // 🎯 为 ListView 添加事件处理
        if (topListView != null && secondListView != null)
        {
            topListView.SelectionChanged += (sender, e) => OnViewSelectionChanged(sender, e);
            secondListView.SelectionChanged += (sender, e) => OnViewSelectionChanged(sender, e);
        }

        // 🌳 为 TreeView 添加事件处理
        if (topTreeView != null && secondTreeView != null)
        {
            topTreeView.SelectedItemChanged += (sender, e) => OnTreeViewSelectionChanged(sender, e);
            secondTreeView.SelectedItemChanged += (sender, e) => OnTreeViewSelectionChanged(sender, e);
        }
    }

    /// <summary>
    /// 处理视图选中变化事件 - 统一的互斥选中逻辑
    /// </summary>
    private void OnViewSelectionChanged(object sender, System.Windows.Controls.SelectionChangedEventArgs e)
    {
        if (_isUpdatingSelection) return;

        var currentView = sender as System.Windows.Controls.ListView;
        if (currentView?.SelectedItem == null) return;

        HandleSelectionChange(currentView, currentView.SelectedItem);
    }

    /// <summary>
    /// 处理 TreeView 选中变化事件
    /// </summary>
    private void OnTreeViewSelectionChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
    {
        if (_isUpdatingSelection) return;

        var currentView = sender as System.Windows.Controls.TreeView;
        if (currentView?.SelectedItem == null) return;

        HandleSelectionChange(currentView, currentView.SelectedItem);
    }

    /// <summary>
    /// 统一处理选中变化 - 相同的逻辑处理所有控件
    /// </summary>
    private void HandleSelectionChange(object currentView, object newSelectedItem)
    {
        try
        {
            _isUpdatingSelection = true;

            // 🧹 清除其他控件的选择
            ClearOtherSelections(currentView);

            // 📋 同步 SelectedItem 属性
            if (SelectedItem != newSelectedItem)
            {
                SelectedItem = newSelectedItem;
            }

            // 🎯 检查是否需要显示子菜单
            if (newSelectedItem is ZyloNavigationItemModel navItem && ShowSubMenuOnClick)
            {
                // 如果有子项目，显示子菜单而不是直接导航
                if (navItem.Children?.Count > 0)
                {
                    ShowSubMenu(navItem, currentView as FrameworkElement);
                    return; // 不执行导航命令
                }
            }

            // ⚡ 触发导航命令（仅在没有子项目时）
            if (NavigationItemSelectedCommand?.CanExecute(newSelectedItem) == true)
            {
                NavigationItemSelectedCommand.Execute(newSelectedItem);
            }
        }
        finally
        {
            _isUpdatingSelection = false;
        }
    }

    /// <summary>
    /// 清除其他控件的选择 - 相同的清除逻辑
    /// </summary>
    private void ClearOtherSelections(object currentView)
    {
        var topListView = this.FindName("TopListView") as System.Windows.Controls.ListView;
        var secondListView = this.FindName("SecondListView") as System.Windows.Controls.ListView;
        var topTreeView = this.FindName("TopTreeView") as System.Windows.Controls.TreeView;
        var secondTreeView = this.FindName("SecondTreeView") as System.Windows.Controls.TreeView;

        // 清除其他 ListView 的选择
        if (topListView != currentView && topListView?.SelectedItem != null)
            topListView.SelectedItem = null;
        if (secondListView != currentView && secondListView?.SelectedItem != null)
            secondListView.SelectedItem = null;

        // 清除其他 TreeView 的选择 - TreeView 需要特殊处理
        if (topTreeView != currentView && topTreeView?.SelectedItem != null)
            ClearTreeViewSelection(topTreeView);
        if (secondTreeView != currentView && secondTreeView?.SelectedItem != null)
            ClearTreeViewSelection(secondTreeView);
    }

    /// <summary>
    /// 清除 TreeView 的选择 - TreeView 特殊处理方法
    /// </summary>
    private void ClearTreeViewSelection(System.Windows.Controls.TreeView treeView)
    {
        try
        {
            // 方法1：通过清除所有 TreeViewItem 的选中状态
            ClearTreeViewItemSelection(treeView);
        }
        catch (Exception ex)
        {
            // 静默处理异常
        }
    }

    /// <summary>
    /// 递归清除 TreeViewItem 的选中状态
    /// </summary>
    private void ClearTreeViewItemSelection(System.Windows.Controls.ItemsControl itemsControl)
    {
        if (itemsControl == null) return;

        for (int i = 0; i < itemsControl.Items.Count; i++)
        {
            var container = itemsControl.ItemContainerGenerator.ContainerFromIndex(i) as System.Windows.Controls.TreeViewItem;
            if (container != null)
            {
                container.IsSelected = false;
                // 递归处理子项
                ClearTreeViewItemSelection(container);
            }
        }
    }

    #endregion

    // ================================================================
    // 🎉 NavigationControl 类总结
    // ================================================================
    //
    // 📋 主要特性：
    // ✅ 双模式显示：ListView（48px）+ TreeView（可配置宽度）
    // ✅ 智能搜索：实时过滤，支持高亮显示
    // ✅ 响应式布局：左右互斥显示，各自占满空间
    // ✅ 高性能绑定：使用 CommunityToolkit.Mvvm 优化
    // ✅ WPF-UI 主题：完美适配设计系统
    //
    // 🏗️ 架构设计：
    // 📐 顶层 Grid 宽度控制：NavigationControlWidth 属性
    // 🎯 左右内容互斥显示：Visibility 绑定
    // ⚡ 简化布局逻辑：避免复杂的 GridLength 计算
    // 🔗 标准依赖属性：支持完整的 WPF 数据绑定
    //
    // 💡 设计理念：
    // 🚀 简单高效：用户提出的天才方案，直接在顶层控制宽度
    // 🎨 用户友好：符合 WPF-UI 设计规范和用户习惯
    // 🔧 易于维护：清晰的代码结构和详细的文档注释
    // 📱 现代化：支持响应式布局和主题适配
    //
    // ================================================================
}
