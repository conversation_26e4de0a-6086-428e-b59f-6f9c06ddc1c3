using System.Collections.ObjectModel;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using GongSolutions.Wpf.DragDrop;
using WPFTest.Models.DWG;
using WPFTest.Services.DWG;

namespace WPFTest.ViewModels.DWG;

/// <summary>
/// DWG 文件类型编辑器 ViewModel - 现代化 MVVM 实现
/// </summary>
/// <remarks>
/// 🎯 核心功能：
/// - 管理DWG文件类型的完整生命周期（增删改查）
/// - 提供智能文件类型分析和自动识别功能
/// - 支持基于文件名前缀的规则配置
/// - 实现可视化的文件类型编辑界面
///
/// 📋 主要属性：
/// - FileTypes: 存储所有文件类型的观察集合
/// - SelectedFileType: 当前用户选中的文件类型
/// - EditingFileType: 正在编辑中的文件类型（支持新建/修改）
/// - IsEditing: 编辑模式状态标志
/// - IsNewMode: 新建模式标志（区分新建和编辑操作）
/// - SearchText: 搜索过滤文本（支持实时过滤）
/// - IsLoading: 加载状态指示器
/// - StatusMessage: 状态栏消息显示
///
/// 🎛️ 核心命令：
/// - LoadDataAsync(): 从数据库异步加载所有文件类型
/// - NewFileType(): 创建新文件类型并进入编辑模式
/// - EditFileType(): 编辑选中的文件类型（克隆对象避免直接修改）
/// - SaveAsync(): 保存文件类型到数据库（支持新建和更新）
/// - DeleteAsync(): 删除选中的文件类型（保护系统默认类型）
/// - Cancel(): 取消编辑操作并恢复原始状态
///
/// 🔄 数据流程：
/// 1. 初始化时自动调用LoadDataAsync()加载数据
/// 2. 用户操作触发相应命令
/// 3. 命令调用Service层进行业务处理
/// 4. Service层操作数据库并触发事件
/// 5. ViewModel监听事件并更新UI状态
///
/// 🎨 设计模式：
/// - MVVM模式：清晰的视图-视图模型分离
/// - 命令模式：使用RelayCommand处理用户交互
/// - 观察者模式：通过ObservableProperty实现数据绑定
/// - 事件驱动：监听Service层的FileTypeChanged事件
///
/// 🛡️ 数据验证：
/// - 继承ObservableValidator提供验证支持
/// - 使用DataAnnotations进行模型验证
/// - CanExecute方法控制命令可用性
///
/// 🔧 技术特点：
/// - 使用CommunityToolkit.Mvvm自动生成样板代码
/// - 异步优先的操作模式
/// - 完整的错误处理和日志记录
/// - 支持取消操作和状态恢复
/// - 支持拖拽排序功能
/// </remarks>
public partial class DwgFileTypeEditorViewModel : ObservableValidator, IDropTarget
{
    #region 字段和服务

    /// <summary>
    /// 数据服务接口
    /// </summary>
    private readonly IDwgFileTypeService _service;

    #endregion

    #region 构造函数

    /// <summary>
    /// 构造函数 - 初始化ViewModel并加载数据
    /// </summary>
    /// <param name="service"></param>
    public DwgFileTypeEditorViewModel(IDwgFileTypeService service)
    {
        _service = service;

        // 使用 Toolkit 的现代化方式处理属性变化

        _ = LoadDataAsync();
    }

    #endregion

    #region 可观察属性

    /// <summary>
    /// 文件类型列表（从数据库加载的所有类型）
    /// </summary>
    [ObservableProperty]
    [Description("文件类型列表")]
    public partial ObservableCollection<DwgFileTypeModel> FileTypes { get; set; } = new();


    /// <summary>
    /// 选中的文件类型（用户在列表中选择的项）
    /// </summary>
    [ObservableProperty]
    [Description("选中的文件类型（用户在列表中选择的项）")]
    public partial DwgFileTypeModel? SelectedFileType { get; set; }

    /// <summary>
    /// 搜索文本（用于过滤文件类型列表）
    /// </summary>
    [ObservableProperty] private string searchText = string.Empty;

    /// <summary>
    /// 是否正在加载（显示进度指示器）
    /// </summary>
    [ObservableProperty] private bool isLoading;

    /// <summary>
    /// 状态消息（显示在状态栏）
    /// </summary>
    [ObservableProperty] private string statusMessage = "就绪";

    /// <summary>
    /// 可用的图标选项列表
    /// </summary>
    public List<IconOption> AvailableIcons { get; } = IconOption.GetDefaultIcons();

    /// <summary>
    /// 图标分类列表
    /// </summary>
    public List<string> IconCategories { get; } = new List<string> { "全部" }.Concat(IconOption.GetCategories()).ToList();

    /// <summary>
    /// 选中的图标分类（用于过滤图标）
    /// </summary>
    [ObservableProperty] private string selectedIconCategory = "全部";

    #endregion

    #region 编辑对象

    /// <summary>
    /// 正在编辑的文件类型（直接绑定到UI表单）
    /// 新建时为新对象，编辑时为选中对象的克隆
    /// </summary>
    [ObservableProperty] private DwgFileTypeModel? editingFileType;

    /// <summary>
    /// 是否正在编辑模式（控制UI显示编辑表单还是详情）
    /// </summary>
    [ObservableProperty] private bool isEditing;

    /// <summary>
    /// 是否为新建模式（用于区分新建和编辑）
    /// </summary>
    [ObservableProperty] private bool isNewMode;

    #endregion

    #region 计算属性

    /// <summary>
    /// 过滤后的文件类型列表
    /// </summary>
    public IEnumerable<DwgFileTypeModel> FilteredFileTypes
    {
        get
        {
            if (string.IsNullOrWhiteSpace(SearchText))
                return FileTypes;

            var keyword = SearchText.ToLower();
            return FileTypes.Where(ft =>
                ft.EnglishName.ToLower().Contains(keyword) ||
                ft.ChineseName.ToLower().Contains(keyword) ||
                ft.Description.ToLower().Contains(keyword));
        }
    }

    /// <summary>
    /// 过滤后的图标列表
    /// </summary>
    public IEnumerable<IconOption> FilteredIcons
    {
        get
        {
            if (SelectedIconCategory == "全部" || string.IsNullOrEmpty(SelectedIconCategory))
                return AvailableIcons;

            return AvailableIcons.Where(icon => icon.Category == SelectedIconCategory);
        }
    }

    #endregion


    #region 命令 - 自动生成

    /// <summary>
    /// 加载数据命令 - 从数据库加载所有文件类型
    /// </summary>
    [RelayCommand]
    private async Task LoadDataAsync()
    {
        try
        {
            IsLoading = true; // 显示加载状态
            StatusMessage = "正在加载..."; // 更新状态消息

            await _service.InitializeDatabaseAsync(); // 确保数据库已初始化
            var types = await _service.GetAllFileTypesAsync(includeDisabled: true); // 获取所有类型（包括禁用的）

            FileTypes.Clear(); // 清空现有列表
            foreach (var type in types) FileTypes.Add(type); // 添加到集合中

            // 获取数据库信息用于调试
            var dbInfo = await _service.GetDatabaseInfoAsync();
            StatusMessage = $"已加载 {FileTypes.Count} 个文件类型 (数据库: {dbInfo.DatabasePath})"; // 显示加载结果和数据库路径
        }
        catch (Exception ex)
        {
            StatusMessage = $"加载失败: {ex.Message}"; // 显示错误信息
        }
        finally
        {
            IsLoading = false; // 隐藏加载状态
        }
    }

    /// <summary>
    /// 新建文件类型命令 - 创建新的文件类型进行编辑
    /// </summary>
    [RelayCommand]
    private void NewFileType()
    {
        EditingFileType = new DwgFileTypeModel(); // 创建新的文件类型对象

        // 设置默认排序号：直接排在最后
        EditingFileType.SortOrder = FileTypes.Count + 1;

        IsEditing = true; // 进入编辑模式
        IsNewMode = true; // 设置为新建模式
        StatusMessage = "请填写新文件类型信息"; // 更新状态消息
    }

    /// <summary>
    /// 编辑文件类型命令 - 编辑选中的文件类型
    /// </summary>
    [RelayCommand]
    private void EditFileType()
    {
        if (SelectedFileType == null) return; // 检查是否有选中项

        // 如果已经在编辑同一个项目，则不重复进入编辑模式
        if (IsEditing && EditingFileType != null && EditingFileType.Id == SelectedFileType.Id)
            return;

        EditingFileType = SelectedFileType.Clone(); // 克隆选中对象（避免直接修改原对象）
        IsEditing = true; // 进入编辑模式
        IsNewMode = false; // 设置为编辑模式（非新建）
        StatusMessage = $"正在编辑: {SelectedFileType.ChineseName}"; // 显示编辑状态
    }

    /// <summary>
    /// 保存文件类型命令 - 保存编辑中的文件类型到数据库
    /// </summary>
    [RelayCommand(CanExecute = nameof(CanSave))]
    private async Task SaveAsync()
    {
        if (EditingFileType == null) return; // 检查编辑对象是否存在

        try
        {
            IsLoading = true; // 显示加载状态
            StatusMessage = "正在保存..."; // 更新状态消息

            bool success = IsNewMode // 根据模式标志判断是新建还是更新
                ? await _service.AddFileTypeAsync(EditingFileType) // 新建模式：直接添加
                : await _service.UpdateFileTypeAsync(EditingFileType); // 编辑模式：更新

            StatusMessage = success ? (IsNewMode ? "添加成功" : "修改成功") : (IsNewMode ? "添加失败" : "修改失败"); // 显示操作结果

            if (success) // 保存成功后的处理
            {
                await LoadDataAsync(); // 重新加载数据列表
                EditingFileType = null; // 清空编辑对象
                IsEditing = false; // 退出编辑模式
                IsNewMode = false; // 重置新建模式标志
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"保存失败: {ex.Message}"; // 显示错误信息
        }
        finally
        {
            IsLoading = false; // 隐藏加载状态
        }
    }

    /// <summary>
    /// 删除文件类型命令 - 删除选中的文件类型（不能删除系统默认类型）
    /// </summary>
    [RelayCommand(CanExecute = nameof(CanDelete))]
    private async Task DeleteAsync()
    {
        if (SelectedFileType == null) return; // 检查是否有选中项

        try
        {
            IsLoading = true; // 显示加载状态
            StatusMessage = $"正在删除 {SelectedFileType.ChineseName}..."; // 显示删除进度

            var success = await _service.DeleteFileTypeAsync(SelectedFileType.Id); // 调用服务删除
            StatusMessage = success ? "删除成功" : "删除失败"; // 显示操作结果

            if (success) // 删除成功后的处理
            {
                await LoadDataAsync(); // 重新加载数据列表
                EditingFileType = null; // 清空编辑对象
                IsEditing = false; // 退出编辑模式
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"删除失败: {ex.Message}"; // 显示错误信息
        }
        finally
        {
            IsLoading = false; // 隐藏加载状态
        }
    }

    /// <summary>
    /// 重置为默认数据命令 - 清空所有数据并恢复系统默认的文件类型
    /// </summary>
    [RelayCommand]
    private async Task ResetToDefaultAsync()
    {
        try
        {
            IsLoading = true; // 显示加载状态
            StatusMessage = "正在重置..."; // 显示重置进度

            var success = await _service.ResetToDefaultAsync(); // 调用服务重置数据
            StatusMessage = success ? "重置成功" : "重置失败"; // 显示操作结果

            if (success) // 重置成功后的处理
            {
                await LoadDataAsync(); // 重新加载数据列表
                EditingFileType = null; // 清空编辑对象
                IsEditing = false; // 退出编辑模式
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"重置失败: {ex.Message}"; // 显示错误信息
        }
        finally
        {
            IsLoading = false; // 隐藏加载状态
        }
    }

    /// <summary>
    /// 取消编辑命令 - 退出编辑模式，丢弃未保存的更改
    /// </summary>
    [RelayCommand]
    private void Cancel()
    {
        EditingFileType = null; // 清空编辑对象
        IsEditing = false; // 退出编辑模式
        IsNewMode = false; // 重置新建模式标志
        StatusMessage = "已取消编辑"; // 更新状态消息
    }

    /// <summary>
    /// 选择图标命令 - 符合MVVM模式的图标选择实现
    /// </summary>
    /// <remarks>
    /// 🎯 功能说明：
    /// - 打开图标选择对话框
    /// - 用户选择图标后更新EditingFileType的Icon属性
    /// - 完全在ViewModel中处理，不依赖View的事件处理器
    ///
    /// 🔄 执行流程：
    /// 1. 检查EditingFileType是否存在
    /// 2. 通过对话框服务打开图标选择对话框
    /// 3. 用户选择图标后更新模型属性
    /// 4. 触发属性变更通知
    ///
    /// 💡 MVVM优势：
    /// - View不包含任何业务逻辑
    /// - 可以轻松进行单元测试
    /// - 支持依赖注入和模拟测试
    /// </remarks>
    [RelayCommand(CanExecute = nameof(CanSelectIcon))]
    private void SelectIcon()
    {
        if (EditingFileType == null) return;

        try
        {
            // 暂时使用简化实现，直接设置一个默认图标
            // TODO: 后续实现完整的图标选择对话框
            var icons = new[] { "📁", "📋", "🏗️", "📐", "🗺️", "🔄", "📊", "🖼️", "🏢", "🔗", "📄" };
            var currentIndex = Array.IndexOf(icons, EditingFileType.Icon);
            var nextIndex = (currentIndex + 1) % icons.Length;

            EditingFileType.Icon = icons[nextIndex];
            StatusMessage = $"已选择图标: {EditingFileType.Icon}";
        }
        catch (Exception ex)
        {
            StatusMessage = $"选择图标失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 测试拖拽功能命令 - 用于调试
    /// </summary>
    [RelayCommand]
    private async Task TestDragDrop()
    {
        try
        {
            StatusMessage = "🧪 开始测试拖拽功能...";

            // 模拟创建一个测试DWG文件
            var testFilePath = Path.Combine(Path.GetTempPath(), "测试文件.dwg");
            if (!File.Exists(testFilePath))
            {
                await File.WriteAllTextAsync(testFilePath, "测试DWG文件内容");
                StatusMessage = $"📁 创建测试文件: {testFilePath}";
                await Task.Delay(1000);
            }

            // 模拟拖拽数据
            var dataObject = new DataObject();
            dataObject.SetData(DataFormats.FileDrop, new[] { testFilePath });

            // 验证数据
            var hasFileDrop = dataObject.GetDataPresent(DataFormats.FileDrop);
            var files = dataObject.GetData(DataFormats.FileDrop) as string[];

            StatusMessage = $"✅ 测试结果: FileDrop={hasFileDrop}, 文件数={files?.Length ?? 0}";
            await Task.Delay(2000);

            // 测试文件类型分析
            var testFileName = "S_建筑平面图.dwg";
            var analyzedType = await AnalyzeFileTypeFromDatabase(testFileName);
            StatusMessage = $"🔍 文件类型分析测试: {testFileName} → {analyzedType}";
            await Task.Delay(2000);

            // 清理测试文件
            if (File.Exists(testFilePath))
            {
                File.Delete(testFilePath);
                StatusMessage = "🗑️ 测试文件已清理";
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"❌ 测试失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 基于数据库的文件类型分析 - 替代硬编码方式
    /// </summary>
    private async Task<string> AnalyzeFileTypeFromDatabase(string fileName)
    {
        try
        {
            if (string.IsNullOrEmpty(fileName))
                return "其他";

            var upperFileName = fileName.ToUpper();
            var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(upperFileName);

            // 遍历所有文件类型，按SortOrder排序以确保优先级
            var sortedFileTypes = FileTypes.OrderBy(ft => ft.SortOrder).ToList();

            foreach (var fileType in sortedFileTypes)
            {
                // 检查每个文件类型的所有前缀
                foreach (var prefix in fileType.PrefixArray)
                {
                    if (string.IsNullOrEmpty(prefix)) continue;

                    var upperPrefix = prefix.ToUpper();

                    // 检查前缀匹配（支持下划线和连字符分隔符）
                    if (fileNameWithoutExtension.StartsWith($"{upperPrefix}_") ||
                        fileNameWithoutExtension.StartsWith($"{upperPrefix}-") ||
                        fileNameWithoutExtension.StartsWith(upperPrefix))
                    {
                        StatusMessage = $"🎯 匹配成功: {fileName} → {fileType.ChineseName} (前缀: {prefix})";
                        return fileType.ChineseName;
                    }
                }

                // 检查关键词匹配（如果文件类型定义了关键词）
                if (!string.IsNullOrEmpty(fileType.Description))
                {
                    var keywords = fileType.Description.Split(',', '，', ';', '；')
                        .Select(k => k.Trim().ToUpper())
                        .Where(k => !string.IsNullOrEmpty(k));

                    foreach (var keyword in keywords)
                    {
                        if (fileNameWithoutExtension.Contains(keyword))
                        {
                            StatusMessage = $"🎯 关键词匹配: {fileName} → {fileType.ChineseName} (关键词: {keyword})";
                            return fileType.ChineseName;
                        }
                    }
                }
            }

            StatusMessage = $"❓ 未匹配到文件类型: {fileName} → 其他";
            return "其他";
        }
        catch (Exception ex)
        {
            StatusMessage = $"❌ 文件类型分析失败: {ex.Message}";
            return "其他";
        }
    }

    /// <summary>
    /// 批量分析文件类型 - 用于调试和批量处理
    /// </summary>
    [RelayCommand]
    private async Task AnalyzeFileTypes()
    {
        try
        {
            StatusMessage = "🔍 开始批量文件类型分析...";

            // 测试文件名列表
            var testFiles = new[]
            {
                "S_建筑平面图.dwg",
                "JG_结构施工图.dwg",
                "DQ_电气系统图.dwg",
                "WS_给排水平面图.dwg",
                "H_暖通空调图.dwg",
                "D_底图文件.dwg",
                "未知前缀文件.dwg"
            };

            foreach (var testFile in testFiles)
            {
                var result = await AnalyzeFileTypeFromDatabase(testFile);
                StatusMessage = $"📋 {testFile} → {result}";
                await Task.Delay(800); // 让用户看到每个结果
            }

            StatusMessage = "✅ 批量文件类型分析完成";
        }
        catch (Exception ex)
        {
            StatusMessage = $"❌ 批量分析失败: {ex.Message}";
        }
    }

    #endregion

    #region 属性变化处理 - Toolkit 现代化方式

    /// <summary>
    /// 当 SelectedFileType 属性变化时自动调用
    /// </summary>
    partial void OnSelectedFileTypeChanged(DwgFileTypeModel? value)
    {
        DeleteCommand.NotifyCanExecuteChanged(); // 更新删除按钮状态
    }

    /// <summary>
    /// 当 EditingFileType 属性变化时自动调用
    /// </summary>
    partial void OnEditingFileTypeChanged(DwgFileTypeModel? value)
    {
        SaveCommand.NotifyCanExecuteChanged(); // 更新保存按钮状态

        // 如果EditingFileType不为null，监听其属性变化
        if (value != null)
        {
            value.PropertyChanged += OnEditingFileTypePropertyChanged;
        }
    }

    /// <summary>
    /// 当 SearchText 属性变化时自动调用
    /// </summary>
    partial void OnSearchTextChanged(string value)
    {
        OnPropertyChanged(nameof(FilteredFileTypes)); // 更新过滤列表
    }

    /// <summary>
    /// 当 SelectedIconCategory 属性变化时自动调用
    /// </summary>
    partial void OnSelectedIconCategoryChanged(string value)
    {
        OnPropertyChanged(nameof(FilteredIcons)); // 更新过滤图标列表
    }

    /// <summary>
    /// 编辑对象属性变化事件处理
    /// </summary>
    private void OnEditingFileTypePropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        // 当编辑对象的属性变化时，更新保存命令状态
        SaveCommand.NotifyCanExecuteChanged();
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 检查是否可以保存（用于Save命令的CanExecute）
    /// </summary>
    /// <returns>如果编辑对象存在且英文名和中文名都不为空返回true</returns>
    private bool CanSave => EditingFileType != null && // 必须有编辑对象
                            !string.IsNullOrWhiteSpace(EditingFileType.EnglishName) && // 英文名称不能为空
                            !string.IsNullOrWhiteSpace(EditingFileType.ChineseName); // 中文名称不能为空

    /// <summary>
    /// 检查是否可以删除（用于Delete命令的CanExecute）
    /// </summary>
    /// <returns>如果选中项存在且不是系统默认类型返回true</returns>
    private bool CanDelete => SelectedFileType != null && // 必须有选中项
                              SelectedFileType.EnglishName != "All" && // 不能删除"全部"类型
                              SelectedFileType.EnglishName != "Other"; // 不能删除"其他"类型

    /// <summary>
    /// 检查是否可以选择图标（用于SelectIcon命令的CanExecute）
    /// </summary>
    /// <returns>如果正在编辑文件类型返回true</returns>
    private bool CanSelectIcon => EditingFileType != null; // 必须有正在编辑的对象

    #endregion

    #region 拖拽排序实现

    /// <summary>
    /// 拖拽悬停处理 - 验证是否可以放置
    /// </summary>
    public void DragOver(IDropInfo dropInfo)
    {
        try
        {
            // 调试信息
            var dataType = dropInfo.Data?.GetType().Name ?? "null";
            StatusMessage = $"🔍 拖拽检测: {dataType}";

            // 1. 检查是否为文件类型项的排序操作
            if (dropInfo.Data is DwgFileTypeModel sourceItem)
            {
                StatusMessage = $"📋 文件类型排序: {sourceItem.ChineseName}";

                // 验证目标集合是否为文件类型列表
                if (dropInfo.TargetCollection != FileTypes)
                {
                    dropInfo.Effects = DragDropEffects.None;
                    StatusMessage = "❌ 无效的排序目标";
                    return;
                }

                // 允许重新排序
                dropInfo.DropTargetAdorner = DropTargetAdorners.Insert;
                dropInfo.Effects = DragDropEffects.Move;
                StatusMessage = $"✅ 允许排序: {sourceItem.ChineseName}";
                return;
            }

            // 2. 检查是否为DWG文件拖拽（用于改变前缀）
            if (dropInfo.Data is DataObject dataObject)
            {
                StatusMessage = "📁 检测到DataObject拖拽";

                // 检查是否包含文件拖拽数据
                if (dataObject.GetDataPresent(DataFormats.FileDrop))
                {
                    var files = dataObject.GetData(DataFormats.FileDrop) as string[];
                    StatusMessage = $"📂 文件拖拽: {files?.Length ?? 0} 个文件";

                    if (files != null && files.Any(f => Path.GetExtension(f).Equals(".dwg", StringComparison.OrdinalIgnoreCase)))
                    {
                        var dwgCount = files.Count(f => Path.GetExtension(f).Equals(".dwg", StringComparison.OrdinalIgnoreCase));
                        StatusMessage = $"✅ 发现 {dwgCount} 个DWG文件，允许拖拽";

                        // 根据拖拽目标设置不同的视觉效果
                        if (dropInfo.TargetItem is DwgFileTypeModel)
                        {
                            // 拖拽到特定文件类型上 - 高亮显示
                            dropInfo.DropTargetAdorner = DropTargetAdorners.Highlight;
                        }
                        else
                        {
                            // 拖拽到列表区域 - 插入指示器
                            dropInfo.DropTargetAdorner = DropTargetAdorners.Insert;
                        }

                        dropInfo.Effects = DragDropEffects.Copy;
                        return;
                    }
                    else
                    {
                        StatusMessage = "❌ 没有找到DWG文件";
                    }
                }
                else
                {
                    StatusMessage = "❌ DataObject不包含文件拖拽数据";

                    // 检查是否包含其他格式的数据
                    var formats = dataObject.GetFormats();
                    StatusMessage += $"\n🔍 可用格式: {string.Join(", ", formats)}";
                }
            }

            // 3. 其他情况不允许拖拽
            dropInfo.Effects = DragDropEffects.None;
            StatusMessage = $"❌ 不支持的拖拽类型: {dataType}";
        }
        catch (Exception ex)
        {
            dropInfo.Effects = DragDropEffects.None;
            StatusMessage = $"❌ 拖拽检测异常: {ex.Message}";
        }
    }

    /// <summary>
    /// 拖拽放置处理 - 执行排序操作或文件前缀修改
    /// </summary>
    public void Drop(IDropInfo dropInfo)
    {
        try
        {
            var dataType = dropInfo.Data?.GetType().Name ?? "null";
            StatusMessage = $"🎯 开始处理拖拽: {dataType}";

            // 1. 处理文件类型项的排序操作
            if (dropInfo.Data is DwgFileTypeModel sourceItem)
            {
                StatusMessage = $"📋 处理文件类型排序: {sourceItem.ChineseName}";
                HandleFileTypeSorting(dropInfo, sourceItem);
                return;
            }

            // 2. 处理DWG文件拖拽（改变前缀）
            if (dropInfo.Data is DataObject dataObject)
            {
                StatusMessage = "📁 处理DWG文件拖拽";
                HandleDwgFilesDrop(dropInfo, dataObject);
                return;
            }

            StatusMessage = $"❌ 不支持的拖拽操作: {dataType}";
        }
        catch (Exception ex)
        {
            StatusMessage = $"❌ 拖拽操作失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 处理文件类型排序
    /// </summary>
    private void HandleFileTypeSorting(IDropInfo dropInfo, DwgFileTypeModel sourceItem)
    {
        // 验证目标集合
        if (dropInfo.TargetCollection != FileTypes)
        {
            StatusMessage = "❌ 无效的拖拽目标";
            return;
        }

        // 获取当前位置和目标位置
        var currentIndex = FileTypes.IndexOf(sourceItem);
        var targetIndex = dropInfo.InsertIndex;

        // 验证索引有效性
        if (currentIndex < 0 || targetIndex < 0 || targetIndex > FileTypes.Count)
        {
            StatusMessage = "❌ 无效的拖拽位置";
            return;
        }

        // 如果位置相同，不需要移动
        if (currentIndex == targetIndex || (currentIndex == targetIndex - 1))
        {
            return;
        }

        // 执行移动操作
        FileTypes.Move(currentIndex, targetIndex > currentIndex ? targetIndex - 1 : targetIndex);

        // 更新数据库中的排序号
        UpdateSortOrderInDatabase();

        StatusMessage = $"✅ 已重新排序: {sourceItem.ChineseName}";
    }

    /// <summary>
    /// 处理DWG文件拖拽（改变前缀）
    /// </summary>
    private async void HandleDwgFilesDrop(IDropInfo dropInfo, DataObject dataObject)
    {
        StatusMessage = "🔍 开始处理DWG文件拖拽...";

        // 检查是否包含文件拖拽数据
        if (!dataObject.GetDataPresent(DataFormats.FileDrop))
        {
            StatusMessage = "❌ 无效的文件拖拽数据 - 不包含FileDrop格式";
            return;
        }

        var files = dataObject.GetData(DataFormats.FileDrop) as string[];
        StatusMessage = $"📂 获取到 {files?.Length ?? 0} 个文件";

        if (files == null || files.Length == 0)
        {
            StatusMessage = "❌ 没有找到拖拽的文件";
            return;
        }

        // 记录所有文件
        for (int i = 0; i < files.Length; i++)
        {
            StatusMessage = $"📄 文件 {i + 1}: {files[i]}";
            await Task.Delay(500); // 让用户看到每个文件
        }

        // 过滤出DWG文件
        var dwgFiles = files.Where(f => Path.GetExtension(f).Equals(".dwg", StringComparison.OrdinalIgnoreCase)).ToArray();
        StatusMessage = $"🎯 找到 {dwgFiles.Length} 个DWG文件";

        if (dwgFiles.Length == 0)
        {
            StatusMessage = "❌ 没有找到DWG文件";
            return;
        }

        // 确定目标文件类型
        var targetFileType = GetTargetFileType(dropInfo);
        StatusMessage = $"🎯 目标文件类型: {targetFileType?.ChineseName ?? "未知"}";

        if (targetFileType == null)
        {
            StatusMessage = "❌ 无法确定目标文件类型";
            return;
        }

        var firstPrefix = targetFileType.PrefixArray.FirstOrDefault() ?? "无前缀";
        StatusMessage = $"🚀 开始修改 {dwgFiles.Length} 个文件的前缀为: {firstPrefix}";

        // 执行文件前缀修改
        await ChangeDwgFilesPrefix(dwgFiles, targetFileType);
    }

    /// <summary>
    /// 获取拖拽目标的文件类型
    /// </summary>
    private DwgFileTypeModel? GetTargetFileType(IDropInfo dropInfo)
    {
        // 如果拖拽到特定的文件类型项上
        if (dropInfo.TargetItem is DwgFileTypeModel targetType)
        {
            return targetType;
        }

        // 如果拖拽到列表中但没有特定项，使用当前选中的文件类型
        if (SelectedFileType != null)
        {
            return SelectedFileType;
        }

        return null;
    }

    /// <summary>
    /// 修改DWG文件的前缀
    /// </summary>
    private async Task ChangeDwgFilesPrefix(string[] dwgFiles, DwgFileTypeModel targetFileType)
    {
        try
        {
            IsLoading = true;
            var successCount = 0;
            var failCount = 0;

            foreach (var filePath in dwgFiles)
            {
                try
                {
                    var fileName = Path.GetFileName(filePath);
                    var directory = Path.GetDirectoryName(filePath);
                    var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(filePath);
                    var extension = Path.GetExtension(filePath);

                    // 移除现有前缀（如果有）
                    var cleanFileName = RemoveExistingPrefix(fileNameWithoutExtension);

                    // 添加新前缀（使用第一个前缀）
                    var firstPrefix = targetFileType.PrefixArray.FirstOrDefault();
                    var newFileName = string.IsNullOrEmpty(firstPrefix)
                        ? cleanFileName
                        : $"{firstPrefix}_{cleanFileName}";

                    var newFilePath = Path.Combine(directory!, $"{newFileName}{extension}");

                    // 如果新文件名与原文件名相同，跳过
                    if (filePath.Equals(newFilePath, StringComparison.OrdinalIgnoreCase))
                    {
                        continue;
                    }

                    // 检查目标文件是否已存在
                    if (File.Exists(newFilePath))
                    {
                        StatusMessage = $"⚠️ 文件已存在，跳过: {Path.GetFileName(newFilePath)}";
                        failCount++;
                        continue;
                    }

                    // 重命名文件
                    File.Move(filePath, newFilePath);
                    successCount++;
                }
                catch (Exception ex)
                {
                    failCount++;
                    StatusMessage = $"❌ 重命名失败: {Path.GetFileName(filePath)} - {ex.Message}";
                }
            }

            StatusMessage = $"✅ 前缀修改完成: 成功 {successCount} 个，失败 {failCount} 个";
        }
        catch (Exception ex)
        {
            StatusMessage = $"❌ 批量前缀修改失败: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 移除文件名中的现有前缀
    /// </summary>
    private string RemoveExistingPrefix(string fileName)
    {
        foreach (var fileType in FileTypes)
        {
            foreach (var prefix in fileType.PrefixArray)
            {
                if (!string.IsNullOrEmpty(prefix) &&
                    fileName.StartsWith($"{prefix}_", StringComparison.OrdinalIgnoreCase))
                {
                    return fileName.Substring(prefix.Length + 1);
                }
            }
        }
        return fileName;
    }

    /// <summary>
    /// 调试拖拽数据的详细信息
    /// </summary>
    private void DebugDragData(IDropInfo dropInfo)
    {
        try
        {
            var sb = new System.Text.StringBuilder();
            sb.AppendLine("🔍 拖拽数据调试信息:");
            sb.AppendLine($"   - Data类型: {dropInfo.Data?.GetType().Name ?? "null"}");
            sb.AppendLine($"   - TargetItem: {dropInfo.TargetItem?.GetType().Name ?? "null"}");
            sb.AppendLine($"   - TargetCollection: {dropInfo.TargetCollection?.GetType().Name ?? "null"}");
            sb.AppendLine($"   - InsertIndex: {dropInfo.InsertIndex}");
            sb.AppendLine($"   - Effects: {dropInfo.Effects}");

            if (dropInfo.Data is DataObject dataObject)
            {
                sb.AppendLine("📁 DataObject格式:");
                var formats = dataObject.GetFormats();
                foreach (var format in formats)
                {
                    sb.AppendLine($"   - {format}: {dataObject.GetDataPresent(format)}");
                }

                if (dataObject.GetDataPresent(DataFormats.FileDrop))
                {
                    var files = dataObject.GetData(DataFormats.FileDrop) as string[];
                    sb.AppendLine($"📂 FileDrop数据 ({files?.Length ?? 0} 个文件):");
                    if (files != null)
                    {
                        for (int i = 0; i < Math.Min(files.Length, 5); i++)
                        {
                            sb.AppendLine($"   - [{i}] {files[i]}");
                        }
                        if (files.Length > 5)
                        {
                            sb.AppendLine($"   - ... 还有 {files.Length - 5} 个文件");
                        }
                    }
                }
            }

            StatusMessage = sb.ToString();
        }
        catch (Exception ex)
        {
            StatusMessage = $"❌ 调试信息获取失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 强制刷新拖拽调试命令
    /// </summary>
    [RelayCommand]
    private void RefreshDragDebug()
    {
        StatusMessage = $"🔄 拖拽调试刷新 - {DateTime.Now:HH:mm:ss}";
        StatusMessage += $"\n📋 当前文件类型数量: {FileTypes.Count}";
        StatusMessage += $"\n🎯 选中文件类型: {SelectedFileType?.ChineseName ?? "无"}";
        StatusMessage += $"\n🔍 搜索文本: '{SearchText ?? "空"}'";
        StatusMessage += $"\n📝 编辑状态: {(IsEditing ? "编辑中" : "未编辑")}";
    }

    /// <summary>
    /// 拖拽进入处理
    /// </summary>
    public void DragEnter(IDropInfo dropInfo)
    {
        DragOver(dropInfo);
    }

    /// <summary>
    /// 拖拽离开处理
    /// </summary>
    public void DragLeave(IDropInfo dropInfo)
    {
        // 不需要特殊处理
    }

    /// <summary>
    /// 更新数据库中的排序号
    /// </summary>
    private async void UpdateSortOrderInDatabase()
    {
        try
        {
            // 根据当前列表顺序更新排序号
            for (int i = 0; i < FileTypes.Count; i++)
            {
                var fileType = FileTypes[i];
                fileType.SortOrder = i + 1; // 排序号从1开始

                // 更新到数据库
                await _service.UpdateFileTypeAsync(fileType);
            }

            StatusMessage = "✅ 排序已保存到数据库";
        }
        catch (Exception ex)
        {
            StatusMessage = $"❌ 保存排序失败: {ex.Message}";
        }
    }

    #endregion
}