using System.Runtime.InteropServices;

namespace WPFTest.Services.DWG;

#region Windows拖拽接口和实现

/// <summary>
/// Windows IDropSource 接口
/// </summary>
[ComImport, Guid("00000121-0000-0000-C000-000000000046"), InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
public interface IDropSource
{
    [PreserveSig]
    int QueryContinueDrag([MarshalAs(UnmanagedType.Bool)] bool fEscapePressed, uint grfKeyState);
    
    [PreserveSig]
    int GiveFeedback(uint dwEffect);
}

/// <summary>
/// Windows拖拽源实现
/// </summary>
public class WindowsDropSource : IDropSource
{
    private const int S_OK = 0;
    private const int DRAGDROP_S_DROP = 0x00040100;
    private const int DRAGDROP_S_CANCEL = 0x00040101;
    private const int DRAGDROP_S_USEDEFAULTCURSORS = 0x00040102;
    private const uint MK_LBUTTON = 0x0001;

    public int QueryContinueDrag(bool fEscapePressed, uint grfKeyState)
    {
        if (fEscapePressed)
        {
            return DRAGDROP_S_CANCEL;
        }

        if ((grfKeyState & MK_LBUTTON) == 0)
        {
            return DRAGDROP_S_DROP;
        }

        return S_OK;
    }

    public int GiveFeedback(uint dwEffect)
    {
        return DRAGDROP_S_USEDEFAULTCURSORS;
    }
}

#endregion
