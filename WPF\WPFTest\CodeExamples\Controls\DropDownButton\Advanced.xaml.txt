<!-- DropDownButton 高级用法示例 -->
<StackPanel Margin="20" Spacing="15">
    
    <!-- 分离式操作 DropDownButton -->
    <GroupBox Header="分离式操作（主按钮和下拉菜单不同操作）" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <!-- 格式化操作 -->
            <ui:DropDownButton Content="格式化" 
                               Command="{Binding HandleInteractionCommand}"
                               CommandParameter="格式化"
                               Margin="8">
                <ui:DropDownButton.Icon>
                    <ui:SymbolIcon Symbol="TextBold24"/>
                </ui:DropDownButton.Icon>
                <ui:DropDownButton.Flyout>
                    <ContextMenu>
                        <MenuItem Header="字体">
                            <MenuItem Header="加粗" Command="{Binding HandleInteractionCommand}" CommandParameter="加粗">
                                <MenuItem.Icon>
                                    <ui:SymbolIcon Symbol="TextBold24"/>
                                </MenuItem.Icon>
                            </MenuItem>
                            <MenuItem Header="斜体" Command="{Binding HandleInteractionCommand}" CommandParameter="斜体">
                                <MenuItem.Icon>
                                    <ui:SymbolIcon Symbol="TextItalic24"/>
                                </MenuItem.Icon>
                            </MenuItem>
                            <MenuItem Header="下划线" Command="{Binding HandleInteractionCommand}" CommandParameter="下划线">
                                <MenuItem.Icon>
                                    <ui:SymbolIcon Symbol="TextUnderline24"/>
                                </MenuItem.Icon>
                            </MenuItem>
                        </MenuItem>
                        <MenuItem Header="段落">
                            <MenuItem Header="左对齐" Command="{Binding HandleInteractionCommand}" CommandParameter="左对齐">
                                <MenuItem.Icon>
                                    <ui:SymbolIcon Symbol="TextAlignLeft24"/>
                                </MenuItem.Icon>
                            </MenuItem>
                            <MenuItem Header="居中" Command="{Binding HandleInteractionCommand}" CommandParameter="居中">
                                <MenuItem.Icon>
                                    <ui:SymbolIcon Symbol="TextAlignCenter24"/>
                                </MenuItem.Icon>
                            </MenuItem>
                            <MenuItem Header="右对齐" Command="{Binding HandleInteractionCommand}" CommandParameter="右对齐">
                                <MenuItem.Icon>
                                    <ui:SymbolIcon Symbol="TextAlignRight24"/>
                                </MenuItem.Icon>
                            </MenuItem>
                        </MenuItem>
                    </ContextMenu>
                </ui:DropDownButton.Flyout>
            </ui:DropDownButton>

            <!-- 视图切换 -->
            <ui:DropDownButton Content="视图"
                               Command="{Binding HandleInteractionCommand}"
                               CommandParameter="视图"
                               Margin="8">
                <ui:DropDownButton.Icon>
                    <ui:SymbolIcon Symbol="Eye24"/>
                </ui:DropDownButton.Icon>
                <ui:DropDownButton.Flyout>
                    <ContextMenu>
                        <MenuItem Header="列表视图" Command="{Binding HandleInteractionCommand}" CommandParameter="列表视图">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="List24"/>
                            </MenuItem.Icon>
                        </MenuItem>
                        <MenuItem Header="网格视图" Command="{Binding HandleInteractionCommand}" CommandParameter="网格视图">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="Apps24"/>
                            </MenuItem.Icon>
                        </MenuItem>
                        <MenuItem Header="详细视图" Command="{Binding HandleInteractionCommand}" CommandParameter="详细视图">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="Table24"/>
                            </MenuItem.Icon>
                        </MenuItem>
                    </ContextMenu>
                </ui:DropDownButton.Flyout>
            </ui:DropDownButton>
        </WrapPanel>
    </GroupBox>

    <!-- 动态菜单 DropDownButton -->
    <GroupBox Header="动态菜单（根据状态动态生成菜单项）" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <!-- 最近文件 -->
            <ui:DropDownButton Content="最近文件" 
                               Command="{Binding HandleInteractionCommand}"
                               CommandParameter="最近文件"
                               Margin="8">
                <ui:DropDownButton.Icon>
                    <ui:SymbolIcon Symbol="History24"/>
                </ui:DropDownButton.Icon>
                <ui:DropDownButton.Flyout>
                    <ContextMenu ItemsSource="{Binding RecentFiles}">
                        <ContextMenu.ItemTemplate>
                            <DataTemplate>
                                <MenuItem Header="{Binding Name}" 
                                          Command="{Binding DataContext.HandleInteractionCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                          CommandParameter="{Binding Name}">
                                    <MenuItem.Icon>
                                        <ui:SymbolIcon Symbol="Document24"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                            </DataTemplate>
                        </ContextMenu.ItemTemplate>
                    </ContextMenu>
                </ui:DropDownButton.Flyout>
            </ui:DropDownButton>

            <!-- 工具选择 -->
            <ui:DropDownButton Content="工具" 
                               Command="{Binding HandleInteractionCommand}"
                               CommandParameter="工具"
                               Margin="8">
                <ui:DropDownButton.Icon>
                    <ui:SymbolIcon Symbol="Wrench24"/>
                </ui:DropDownButton.Icon>
                <ui:DropDownButton.Flyout>
                    <ContextMenu>
                        <MenuItem Header="开发工具">
                            <MenuItem Header="调试器" Command="{Binding HandleInteractionCommand}" CommandParameter="调试器"/>
                            <MenuItem Header="性能分析" Command="{Binding HandleInteractionCommand}" CommandParameter="性能分析"/>
                            <MenuItem Header="代码检查" Command="{Binding HandleInteractionCommand}" CommandParameter="代码检查"/>
                        </MenuItem>
                        <MenuItem Header="设计工具">
                            <MenuItem Header="UI 设计器" Command="{Binding HandleInteractionCommand}" CommandParameter="UI设计器"/>
                            <MenuItem Header="资源编辑器" Command="{Binding HandleInteractionCommand}" CommandParameter="资源编辑器"/>
                        </MenuItem>
                        <Separator/>
                        <MenuItem Header="扩展管理" Command="{Binding HandleInteractionCommand}" CommandParameter="扩展管理"/>
                    </ContextMenu>
                </ui:DropDownButton.Flyout>
            </ui:DropDownButton>
        </WrapPanel>
    </GroupBox>

    <!-- 复杂导出功能 -->
    <GroupBox Header="复杂导出功能（多级菜单和分隔符）" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <ui:DropDownButton Content="导出"
                               Command="{Binding HandleInteractionCommand}"
                               CommandParameter="导出"
                               Margin="8">
                <ui:DropDownButton.Icon>
                    <ui:SymbolIcon Symbol="Share24"/>
                </ui:DropDownButton.Icon>
                <ui:DropDownButton.Flyout>
                    <ContextMenu>
                        <MenuItem Header="文档格式">
                            <MenuItem Header="导出为 PDF" Command="{Binding HandleInteractionCommand}" CommandParameter="导出PDF">
                                <MenuItem.Icon>
                                    <ui:SymbolIcon Symbol="Document24"/>
                                </MenuItem.Icon>
                            </MenuItem>
                            <MenuItem Header="导出为 Word" Command="{Binding HandleInteractionCommand}" CommandParameter="导出Word">
                                <MenuItem.Icon>
                                    <ui:SymbolIcon Symbol="Document24"/>
                                </MenuItem.Icon>
                            </MenuItem>
                        </MenuItem>
                        <MenuItem Header="表格格式">
                            <MenuItem Header="导出为 Excel" Command="{Binding HandleInteractionCommand}" CommandParameter="导出Excel">
                                <MenuItem.Icon>
                                    <ui:SymbolIcon Symbol="Table24"/>
                                </MenuItem.Icon>
                            </MenuItem>
                            <MenuItem Header="导出为 CSV" Command="{Binding HandleInteractionCommand}" CommandParameter="导出CSV"/>
                        </MenuItem>
                        <MenuItem Header="图像格式">
                            <MenuItem Header="导出为 PNG" Command="{Binding HandleInteractionCommand}" CommandParameter="导出PNG"/>
                            <MenuItem Header="导出为 JPG" Command="{Binding HandleInteractionCommand}" CommandParameter="导出JPG"/>
                            <MenuItem Header="导出为 SVG" Command="{Binding HandleInteractionCommand}" CommandParameter="导出SVG"/>
                        </MenuItem>
                        <Separator/>
                        <MenuItem Header="自定义导出..." Command="{Binding HandleInteractionCommand}" CommandParameter="自定义导出"/>
                        <MenuItem Header="批量导出..." Command="{Binding HandleInteractionCommand}" CommandParameter="批量导出"/>
                    </ContextMenu>
                </ui:DropDownButton.Flyout>
            </ui:DropDownButton>
        </WrapPanel>
    </GroupBox>

</StackPanel>
