<!-- 高级 TitleBar 控件示例 -->
<UserControl x:Class="WPFTest.Views.WindowControls.AdvancedTitleBarExample"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:b="http://schemas.microsoft.com/xaml/behaviors">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 高级 TitleBar - 完全数据绑定 -->
        <ui:TitleBar Grid.Row="0"
                    Title="{Binding TitleBarTitle}"
                    Height="{Binding TitleBarHeight}"
                    ShowMinimize="{Binding ShowMinimize}"
                    ShowMaximize="{Binding ShowMaximize}"
                    ShowClose="{Binding ShowClose}"
                    CanMaximize="{Binding CanMaximize}">
            
            <!-- 动态图标 -->
            <ui:TitleBar.Icon>
                <ui:SymbolIcon Symbol="{Binding SelectedIcon}" 
                              FontSize="16"
                              Visibility="{Binding UseCustomIcon, Converter={StaticResource BooleanToVisibilityConverter}}"/>
            </ui:TitleBar.Icon>

            <!-- 自定义标题栏内容 -->
            <ui:TitleBar.Header>
                <DockPanel>
                    <!-- 左侧自定义按钮 -->
                    <StackPanel Orientation="Horizontal" DockPanel.Dock="Left">
                        <ui:Button Content="🔄" 
                                  Command="{Binding RefreshCommand}"
                                  ToolTip="刷新"
                                  Appearance="Transparent"
                                  Width="32" Height="32"
                                  Margin="5,0"/>
                        <ui:Button Content="⚙️" 
                                  Command="{Binding ShowSettingsCommand}"
                                  ToolTip="设置"
                                  Appearance="Transparent"
                                  Width="32" Height="32"
                                  Margin="5,0"/>
                    </StackPanel>

                    <!-- 右侧状态信息 -->
                    <StackPanel Orientation="Horizontal" DockPanel.Dock="Right" Margin="10,0">
                        <TextBlock Text="{Binding ConnectionStatus}" 
                                  FontSize="11" 
                                  VerticalAlignment="Center"
                                  Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                  Margin="0,0,8,0"/>
                        <Ellipse Width="8" Height="8" 
                                Fill="{Binding StatusColor}" 
                                VerticalAlignment="Center"/>
                    </StackPanel>
                </DockPanel>
            </ui:TitleBar.Header>

            <!-- 交互行为 -->
            <b:Interaction.Triggers>
                <b:EventTrigger EventName="MouseDoubleClick">
                    <b:InvokeCommandAction Command="{Binding TitleBarDoubleClickCommand}"/>
                </b:EventTrigger>
            </b:Interaction.Triggers>
        </ui:TitleBar>

        <!-- 主要内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧控制面板 -->
            <Border Grid.Column="0" 
                    Background="{DynamicResource LayerFillColorAltBrush}"
                    BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                    BorderThickness="0,0,1,0">
                <ScrollViewer Padding="15">
                    <StackPanel>
                        <TextBlock Text="TitleBar 控制" FontWeight="Bold" Margin="0,0,0,15"/>

                        <!-- 标题设置 -->
                        <ui:Card Padding="12" Margin="0,0,0,10">
                            <StackPanel>
                                <TextBlock Text="标题设置" FontSize="12" FontWeight="Medium" Margin="0,0,0,8"/>
                                <ui:TextBox Text="{Binding TitleBarTitle}" 
                                           PlaceholderText="输入标题"
                                           Margin="0,0,0,8"/>
                                <ui:Button Content="🎲 随机标题" 
                                          Command="{Binding GenerateRandomTitleCommand}"
                                          HorizontalAlignment="Stretch"
                                          FontSize="11"/>
                            </StackPanel>
                        </ui:Card>

                        <!-- 外观设置 -->
                        <ui:Card Padding="12" Margin="0,0,0,10">
                            <StackPanel>
                                <TextBlock Text="外观设置" FontSize="12" FontWeight="Medium" Margin="0,0,0,8"/>
                                
                                <TextBlock Text="高度" FontSize="11" Margin="0,0,0,4"/>
                                <Slider Value="{Binding TitleBarHeight}" 
                                       Minimum="25" Maximum="60" 
                                       TickFrequency="5" 
                                       IsSnapToTickEnabled="True"
                                       Margin="0,0,0,8"/>
                                
                                <CheckBox Content="使用自定义图标" 
                                         IsChecked="{Binding UseCustomIcon}" 
                                         FontSize="11"
                                         Margin="0,0,0,4"/>
                                
                                <ComboBox ItemsSource="{Binding AvailableIcons}"
                                         SelectedValue="{Binding SelectedIcon}"
                                         SelectedValuePath="Symbol"
                                         DisplayMemberPath="Name"
                                         IsEnabled="{Binding UseCustomIcon}"
                                         FontSize="11"/>
                            </StackPanel>
                        </ui:Card>

                        <!-- 按钮控制 -->
                        <ui:Card Padding="12" Margin="0,0,0,10">
                            <StackPanel>
                                <TextBlock Text="按钮控制" FontSize="12" FontWeight="Medium" Margin="0,0,0,8"/>
                                <CheckBox Content="显示最小化" IsChecked="{Binding ShowMinimize}" FontSize="11" Margin="0,0,0,4"/>
                                <CheckBox Content="显示最大化" IsChecked="{Binding ShowMaximize}" FontSize="11" Margin="0,0,0,4"/>
                                <CheckBox Content="显示关闭" IsChecked="{Binding ShowClose}" FontSize="11" Margin="0,0,0,4"/>
                                <CheckBox Content="允许最大化" IsChecked="{Binding CanMaximize}" FontSize="11"/>
                            </StackPanel>
                        </ui:Card>

                        <!-- 状态模拟 -->
                        <ui:Card Padding="12">
                            <StackPanel>
                                <TextBlock Text="状态模拟" FontSize="12" FontWeight="Medium" Margin="0,0,0,8"/>
                                <ui:Button Content="🟢 在线" 
                                          Command="{Binding SetOnlineStatusCommand}"
                                          HorizontalAlignment="Stretch"
                                          FontSize="11"
                                          Margin="0,0,0,4"/>
                                <ui:Button Content="🟡 忙碌" 
                                          Command="{Binding SetBusyStatusCommand}"
                                          HorizontalAlignment="Stretch"
                                          FontSize="11"
                                          Margin="0,0,0,4"/>
                                <ui:Button Content="🔴 离线" 
                                          Command="{Binding SetOfflineStatusCommand}"
                                          HorizontalAlignment="Stretch"
                                          FontSize="11"/>
                            </StackPanel>
                        </ui:Card>
                    </StackPanel>
                </ScrollViewer>
            </Border>

            <!-- 右侧内容区域 -->
            <Border Grid.Column="1" Padding="20">
                <StackPanel>
                    <TextBlock Text="🚀 高级 TitleBar 功能演示" 
                              FontSize="24" 
                              FontWeight="Bold"
                              HorizontalAlignment="Center"
                              Margin="0,0,0,20"/>

                    <!-- 功能展示区域 -->
                    <ui:Card Padding="20" Margin="0,0,0,20">
                        <StackPanel>
                            <TextBlock Text="实时状态信息" FontWeight="Medium" Margin="0,0,0,10"/>
                            
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="当前配置：" FontSize="12" FontWeight="Medium" Margin="0,0,0,5"/>
                                    <TextBlock Text="{Binding TitleBarTitle, StringFormat='标题: {0}'}" FontSize="11" Margin="0,0,0,2"/>
                                    <TextBlock Text="{Binding TitleBarHeight, StringFormat='高度: {0}px'}" FontSize="11" Margin="0,0,0,2"/>
                                    <TextBlock Text="{Binding ConnectionStatus, StringFormat='状态: {0}'}" FontSize="11" Margin="0,0,0,2"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1">
                                    <TextBlock Text="按钮状态：" FontSize="12" FontWeight="Medium" Margin="0,0,0,5"/>
                                    <TextBlock Text="{Binding ShowMinimize, StringFormat='最小化: {0}'}" FontSize="11" Margin="0,0,0,2"/>
                                    <TextBlock Text="{Binding ShowMaximize, StringFormat='最大化: {0}'}" FontSize="11" Margin="0,0,0,2"/>
                                    <TextBlock Text="{Binding CanMaximize, StringFormat='可最大化: {0}'}" FontSize="11" Margin="0,0,0,2"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </ui:Card>

                    <!-- 操作历史 -->
                    <ui:Card Padding="20">
                        <StackPanel>
                            <TextBlock Text="操作历史" FontWeight="Medium" Margin="0,0,0,10"/>
                            <ListBox ItemsSource="{Binding OperationHistory}" 
                                    MaxHeight="200"
                                    ScrollViewer.VerticalScrollBarVisibility="Auto">
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="{Binding Timestamp, StringFormat='{}{0:HH:mm:ss}'}" 
                                                      FontSize="10" 
                                                      Opacity="0.6" 
                                                      Margin="0,0,8,0"/>
                                            <TextBlock Text="{Binding Operation}" FontSize="11"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>
                            </ListBox>
                        </StackPanel>
                    </ui:Card>
                </StackPanel>
            </Border>
        </Grid>
    </Grid>
</UserControl>
