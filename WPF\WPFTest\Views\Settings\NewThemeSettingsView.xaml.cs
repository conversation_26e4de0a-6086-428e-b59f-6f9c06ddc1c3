using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using Wpf.Ui.Controls;

namespace WPFTest.Views;

/// <summary>
/// NewThemeSettingsView.xaml 的交互逻辑
/// </summary>
public partial class NewThemeSettingsView : UserControl
{
    public NewThemeSettingsView()
    {
        InitializeComponent();
    }

    private void ColorBlock_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
    {
        if (sender is Border border && border.Tag is string resourceName)
        {
            try
            {
                // 复制资源名称到剪贴板
                var resourceText = $"{{DynamicResource {resourceName}}}";
                Clipboard.SetText(resourceText);

                // 显示成功提示 - 使用简单的MessageBox
                System.Windows.MessageBox.Show($"已复制: {resourceText}", "复制成功", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                // 显示错误提示
                System.Windows.MessageBox.Show($"复制失败: {ex.Message}", "复制失败", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }
    }

    private void ThemeModeCard_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
    {
        if (sender is Border border && border.Tag is object themeMode && DataContext is ViewModels.NewThemeSettingsViewModel viewModel)
        {
            // 通过反射获取Theme属性
            var themeProperty = themeMode.GetType().GetProperty("Theme");
            if (themeProperty?.GetValue(themeMode) is Wpf.Ui.Appearance.ApplicationTheme theme)
            {
                viewModel.ChangeThemeCommand.Execute(theme);
            }
        }
    }
}
