using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using System.Windows;
using System.IO;
using System.Reflection;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels.ButtonControls
{
    /// <summary>
    /// DropDownButton 页面的 ViewModel，演示 DropDownButton 控件的各种功能
    /// </summary>
    public partial class DropDownButtonPageViewModel : ObservableObject
    {
        private readonly YLoggerInstance _logger = YLogger.For<DropDownButtonPageViewModel>();

        #region 属性

        /// <summary>
        /// 交互次数
        /// </summary>
        [ObservableProperty]
        private int interactionCount = 0;

        /// <summary>
        /// 最后执行的操作
        /// </summary>
        [ObservableProperty]
        private string lastAction = "无操作";

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        private string statusMessage = "欢迎使用 DropDownButton 示例库！";

        /// <summary>
        /// 当前选择的选项
        /// </summary>
        [ObservableProperty]
        private string selectedOption = "默认选项";

        /// <summary>
        /// DropDownButton 是否启用
        /// </summary>
        [ObservableProperty]
        private bool isDropDownEnabled = true;

        /// <summary>
        /// 最近文件列表（数据绑定示例）
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<RecentFileItem> recentFiles = new();

        /// <summary>
        /// 导出格式选项（数据绑定示例）
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<ExportFormatItem> exportFormats = new();

        /// <summary>
        /// 工具选项（数据绑定示例）
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<ToolItem> toolItems = new();

        /// <summary>
        /// 当前选择的导出格式
        /// </summary>
        [ObservableProperty]
        private ExportFormatItem? selectedExportFormat;

        #region 代码示例属性

        /// <summary>
        /// 基础功能 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 基础功能 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级功能 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级功能 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 样式 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string StylesXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 数据绑定 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string DataBindingXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 数据绑定 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string DataBindingCSharpExample { get; set; } = string.Empty;

        #endregion

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化 DropDownButtonPageViewModel
        /// </summary>
        public DropDownButtonPageViewModel()
        {
            StatusMessage = "DropDownButton 示例库已加载，开始体验各种功能！";
            InitializeCodeExamples();
            InitializeDataBindingExamples();
        }

        #endregion

        #region 命令

        /// <summary>
        /// 通用交互命令
        /// </summary>
        /// <param name="parameter">操作参数</param>
        [RelayCommand]
        private void HandleInteraction(string? parameter)
        {
            InteractionCount++;
            LastAction = parameter ?? "未知操作";
            
            // 根据不同的操作显示不同的状态消息
            StatusMessage = parameter switch
            {
                // 主按钮操作
                "文件操作" => "📁 点击了文件操作主按钮",
                "编辑" => "✏️ 点击了编辑主按钮",
                "视图" => "👁️ 点击了视图主按钮",
                "工具" => "🔧 点击了工具主按钮",
                
                // 文件菜单项
                "新建" => "📄 执行了新建操作",
                "打开" => "📂 执行了打开操作",
                "保存" => "💾 执行了保存操作",
                "退出" => "🚪 执行了退出操作",
                
                // 编辑菜单项
                "复制" => "📋 执行了复制操作",
                "剪切" => "✂️ 执行了剪切操作",
                "粘贴" => "📋 执行了粘贴操作",
                "撤销" => "↩️ 执行了撤销操作",
                "重做" => "↪️ 执行了重做操作",
                
                // 格式菜单项
                "加粗" => "🔤 应用了加粗格式",
                "斜体" => "🔤 应用了斜体格式",
                "下划线" => "🔤 应用了下划线格式",
                "左对齐" => "📐 设置为左对齐",
                "居中" => "📐 设置为居中对齐",
                "右对齐" => "📐 设置为右对齐",
                
                // 视图菜单项
                "列表视图" => "📋 切换到列表视图",
                "网格视图" => "⊞ 切换到网格视图",
                "详细视图" => "📊 切换到详细视图",
                
                // 导出菜单项
                "导出PDF" => "📄 导出为 PDF 格式",
                "导出Excel" => "📊 导出为 Excel 格式",
                "导出Word" => "📝 导出为 Word 格式",
                
                _ => $"🎯 执行了操作: {parameter}"
            };

            // 更新选择的选项
            if (!string.IsNullOrEmpty(parameter))
            {
                SelectedOption = parameter;
            }

            _logger.Info($"DropDownButton 交互: {parameter}");
        }

        /// <summary>
        /// 重置计数命令
        /// </summary>
        [RelayCommand]
        private void ResetCount()
        {
            InteractionCount = 0;
            StatusMessage = "计数已重置";
            _logger.Info("重置了交互计数");
        }

        /// <summary>
        /// 清除状态命令
        /// </summary>
        [RelayCommand]
        private void ClearStatus()
        {
            LastAction = "无操作";
            SelectedOption = "默认选项";
            StatusMessage = "状态已清除";
            _logger.Info("清除了状态信息");
        }

        /// <summary>
        /// 添加最近文件命令
        /// </summary>
        [RelayCommand]
        private void AddRecentFile(string? fileName)
        {
            if (string.IsNullOrEmpty(fileName))
                fileName = $"新文件{DateTime.Now:HHmmss}.txt";

            var newFile = new RecentFileItem
            {
                Name = fileName,
                Path = $@"C:\Documents\{fileName}",
                Icon = GetIconByExtension(fileName),
                LastAccessed = DateTime.Now
            };

            RecentFiles.Insert(0, newFile);

            // 限制最近文件数量
            while (RecentFiles.Count > 10)
            {
                RecentFiles.RemoveAt(RecentFiles.Count - 1);
            }

            StatusMessage = $"📁 添加了新的最近文件: {fileName}";
            InteractionCount++;
            _logger.Info($"添加了最近文件: {fileName}");
        }

        /// <summary>
        /// 清空最近文件列表命令
        /// </summary>
        [RelayCommand]
        private void ClearRecentFiles()
        {
            RecentFiles.Clear();
            StatusMessage = "🗑️ 已清空最近文件列表";
            InteractionCount++;
            _logger.Info("清空了最近文件列表");
        }

        /// <summary>
        /// 处理最近文件选择命令
        /// </summary>
        [RelayCommand]
        private void HandleRecentFileSelection(RecentFileItem fileItem)
        {
            if (fileItem == null) return;

            StatusMessage = $"📂 打开了最近文件: {fileItem.Name}";
            LastAction = $"打开文件: {fileItem.Name}";
            InteractionCount++;

            // 将选择的文件移到列表顶部
            var index = RecentFiles.IndexOf(fileItem);
            if (index > 0)
            {
                RecentFiles.RemoveAt(index);
                RecentFiles.Insert(0, fileItem);
            }

            // 更新访问时间
            fileItem.LastAccessed = DateTime.Now;

            _logger.Info($"打开了最近文件: {fileItem.Name}");
        }

        /// <summary>
        /// 处理导出格式选择命令
        /// </summary>
        [RelayCommand]
        private void HandleExportFormatSelection(ExportFormatItem formatItem)
        {
            if (formatItem == null) return;

            SelectedExportFormat = formatItem;
            StatusMessage = $"📤 选择了导出格式: {formatItem.Name} ({formatItem.Extension})";
            LastAction = $"选择导出格式: {formatItem.Name}";
            InteractionCount++;

            _logger.Info($"选择了导出格式: {formatItem.Name}");
        }

        /// <summary>
        /// 处理工具选择命令
        /// </summary>
        [RelayCommand]
        private void HandleToolSelection(ToolItem toolItem)
        {
            if (toolItem == null) return;

            StatusMessage = $"🔧 启动了工具: {toolItem.Name} ({toolItem.Category})";
            LastAction = $"启动工具: {toolItem.Name}";
            InteractionCount++;

            _logger.Info($"启动了工具: {toolItem.Name} - {toolItem.Description}");
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化代码示例
        /// </summary>
        private void InitializeCodeExamples()
        {
            try
            {
                // 获取代码示例文件的基础路径
                var baseDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
                var codeExamplesPath = Path.Combine(baseDirectory, "CodeExamples", "Controls", "DropDownButton");

                _logger.Info($"基础目录: {baseDirectory}");
                _logger.Info($"代码示例路径: {codeExamplesPath}");
                _logger.Info($"目录是否存在: {Directory.Exists(codeExamplesPath)}");

                // 读取各种示例文件
                BasicXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.xaml.txt"));
                BasicCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.cs.txt"));
                AdvancedXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Advanced.xaml.txt"));
                AdvancedCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Advanced.cs.txt"));
                StylesXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Styles.xaml.txt"));
                DataBindingXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "DataBinding.xaml.txt"));
                DataBindingCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "DataBinding.cs.txt"));

                _logger.Info("DropDownButton 代码示例加载完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"加载 DropDownButton 代码示例失败: {ex.Message}");
                SetDefaultCodeExamples();
            }
        }

        /// <summary>
        /// 读取代码示例文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件内容</returns>
        private string ReadCodeExampleFile(string filePath)
        {
            _logger.Info($"尝试读取文件: {filePath}");
            
            if (File.Exists(filePath))
            {
                var content = File.ReadAllText(filePath);
                _logger.Info($"成功读取文件，内容长度: {content.Length}");
                return content;
            }
            else
            {
                _logger.Warning($"代码示例文件不存在: {filePath}");
                return $"// 代码示例文件不存在: {Path.GetFileName(filePath)}\n// 完整路径: {filePath}";
            }
        }

        /// <summary>
        /// 设置默认代码示例（当文件读取失败时使用）
        /// </summary>
        private void SetDefaultCodeExamples()
        {
            BasicXamlExample = "<!-- DropDownButton 基础示例 -->\n<ui:DropDownButton Content=\"文件操作\">\n    <ui:DropDownButton.Flyout>\n        <ContextMenu>\n            <MenuItem Header=\"新建\" Command=\"{Binding HandleInteractionCommand}\" CommandParameter=\"新建\"/>\n            <MenuItem Header=\"打开\" Command=\"{Binding HandleInteractionCommand}\" CommandParameter=\"打开\"/>\n        </ContextMenu>\n    </ui:DropDownButton.Flyout>\n</ui:DropDownButton>";
            BasicCSharpExample = "// DropDownButton C# 基础示例\n[RelayCommand]\nprivate void HandleInteraction(string parameter)\n{\n    StatusMessage = $\"执行了操作: {parameter}\";\n    InteractionCount++;\n}";
            AdvancedXamlExample = "<!-- DropDownButton 高级示例 -->\n<!-- 在这里添加高级用法示例 -->";
            AdvancedCSharpExample = "// DropDownButton C# 高级示例\n// 在这里添加高级 C# 代码示例";
            StylesXamlExample = "<!-- DropDownButton 样式示例 -->\n<!-- 在这里添加样式使用示例 -->";
            DataBindingXamlExample = "<!-- DropDownButton 数据绑定示例 -->\n<ui:DropDownButton Content=\"最近文件\">\n    <ui:DropDownButton.Flyout>\n        <ContextMenu ItemsSource=\"{Binding RecentFiles}\">\n            <ContextMenu.ItemTemplate>\n                <DataTemplate>\n                    <MenuItem Header=\"{Binding Name}\" Command=\"{Binding DataContext.HandleInteractionCommand, RelativeSource={RelativeSource AncestorType=UserControl}}\" CommandParameter=\"{Binding Name}\"/>\n                </DataTemplate>\n            </ContextMenu.ItemTemplate>\n        </ContextMenu>\n    </ui:DropDownButton.Flyout>\n</ui:DropDownButton>";
            DataBindingCSharpExample = "// DropDownButton 数据绑定 C# 示例\n[ObservableProperty]\nprivate ObservableCollection<RecentFileItem> recentFiles = new();\n\npublic class RecentFileItem\n{\n    public string Name { get; set; } = string.Empty;\n    public string Icon { get; set; } = \"Document24\";\n}";
        }

        /// <summary>
        /// 初始化数据绑定示例
        /// </summary>
        private void InitializeDataBindingExamples()
        {
            // 初始化最近文件列表
            RecentFiles = new ObservableCollection<RecentFileItem>
            {
                new RecentFileItem { Name = "项目文档.docx", Path = @"C:\Documents\项目文档.docx", Icon = "Document24", LastAccessed = DateTime.Now.AddHours(-2) },
                new RecentFileItem { Name = "数据分析.xlsx", Path = @"C:\Documents\数据分析.xlsx", Icon = "Table24", LastAccessed = DateTime.Now.AddHours(-5) },
                new RecentFileItem { Name = "设计稿.psd", Path = @"C:\Documents\设计稿.psd", Icon = "Image24", LastAccessed = DateTime.Now.AddDays(-1) },
                new RecentFileItem { Name = "会议记录.txt", Path = @"C:\Documents\会议记录.txt", Icon = "DocumentText24", LastAccessed = DateTime.Now.AddDays(-2) },
                new RecentFileItem { Name = "演示文稿.pptx", Path = @"C:\Documents\演示文稿.pptx", Icon = "Presentation24", LastAccessed = DateTime.Now.AddDays(-3) }
            };

            // 初始化导出格式选项
            ExportFormats = new ObservableCollection<ExportFormatItem>
            {
                new ExportFormatItem { Name = "PDF 文档", Extension = ".pdf", Icon = "Document24", Description = "便携式文档格式，适合分享和打印" },
                new ExportFormatItem { Name = "Word 文档", Extension = ".docx", Icon = "DocumentText24", Description = "Microsoft Word 格式，可编辑文档" },
                new ExportFormatItem { Name = "Excel 表格", Extension = ".xlsx", Icon = "Table24", Description = "Microsoft Excel 格式，数据分析" },
                new ExportFormatItem { Name = "PowerPoint", Extension = ".pptx", Icon = "Presentation24", Description = "Microsoft PowerPoint 演示文稿" },
                new ExportFormatItem { Name = "PNG 图像", Extension = ".png", Icon = "Image24", Description = "高质量图像格式，支持透明" },
                new ExportFormatItem { Name = "JPG 图像", Extension = ".jpg", Icon = "Image24", Description = "压缩图像格式，文件较小" },
                new ExportFormatItem { Name = "CSV 数据", Extension = ".csv", Icon = "DocumentTable24", Description = "逗号分隔值，数据交换格式" }
            };

            // 初始化工具选项
            ToolItems = new ObservableCollection<ToolItem>
            {
                new ToolItem { Name = "代码编辑器", Icon = "Code24", Category = "开发工具", Description = "高级代码编辑和语法高亮" },
                new ToolItem { Name = "调试器", Icon = "Bug24", Category = "开发工具", Description = "断点调试和变量监视" },
                new ToolItem { Name = "性能分析", Icon = "SpeedHigh24", Category = "开发工具", Description = "应用程序性能分析工具" },
                new ToolItem { Name = "UI 设计器", Icon = "Design24", Category = "设计工具", Description = "可视化界面设计工具" },
                new ToolItem { Name = "颜色选择器", Icon = "Color24", Category = "设计工具", Description = "颜色选择和调色板工具" },
                new ToolItem { Name = "图标库", Icon = "Icons24", Category = "设计工具", Description = "丰富的图标资源库" },
                new ToolItem { Name = "数据库管理", Icon = "Database24", Category = "数据工具", Description = "数据库连接和管理工具" },
                new ToolItem { Name = "API 测试", Icon = "Globe24", Category = "数据工具", Description = "REST API 测试和调试" }
            };

            // 设置默认选择
            SelectedExportFormat = ExportFormats.FirstOrDefault();

            _logger.Info("数据绑定示例初始化完成");
        }

        /// <summary>
        /// 根据文件扩展名获取图标
        /// </summary>
        private string GetIconByExtension(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLower();
            return extension switch
            {
                ".txt" => "DocumentText24",
                ".docx" => "Document24",
                ".xlsx" => "Table24",
                ".png" or ".jpg" => "Image24",
                ".pdf" => "Document24",
                ".pptx" => "Presentation24",
                ".psd" => "Image24",
                _ => "Document24"
            };
        }

        #endregion
    }

    /// <summary>
    /// 最近文件项模型
    /// </summary>
    public class RecentFileItem
    {
        public string Name { get; set; } = string.Empty;
        public string Path { get; set; } = string.Empty;
        public string Icon { get; set; } = "Document24";
        public DateTime LastAccessed { get; set; } = DateTime.Now;
        public string DisplayTime => LastAccessed.ToString("yyyy-MM-dd HH:mm");
    }

    /// <summary>
    /// 导出格式项模型
    /// </summary>
    public class ExportFormatItem
    {
        public string Name { get; set; } = string.Empty;
        public string Extension { get; set; } = string.Empty;
        public string Icon { get; set; } = "Document24";
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// 工具项模型
    /// </summary>
    public class ToolItem
    {
        public string Name { get; set; } = string.Empty;
        public string Icon { get; set; } = "Wrench24";
        public string Category { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
    }
}
