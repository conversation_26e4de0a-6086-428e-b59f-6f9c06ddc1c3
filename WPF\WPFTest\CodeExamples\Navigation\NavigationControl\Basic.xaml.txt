<!-- NavigationControl 基础功能用法 -->
<Grid>
    <Grid.ColumnDefinitions>
        <ColumnDefinition Width="Auto"/>
        <ColumnDefinition Width="5"/>
        <ColumnDefinition Width="*"/>
    </Grid.ColumnDefinitions>

    <!-- 左侧导航控件 -->
    <zylo:NavigationControl Grid.Column="0"
                           Width="280"
                           ItemsSource="{Binding NavigationItems}"
                           SelectedItem="{Binding SelectedNavigationItem, Mode=TwoWay}"
                           NavigationItemSelectedCommand="{Binding NavigationCommand}"
                           SearchText="{Binding SearchText, Mode=TwoWay}"
                           IsSearchVisible="True"
                           IsCollapsed="{Binding IsNavigationCollapsed}"
                           ShowSubMenuOnClick="True"
                           Background="{DynamicResource LayerFillColorDefaultBrush}"
                           BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                           BorderThickness="0,0,1,0">
        
        <!-- 自定义项模板 -->
        <zylo:NavigationControl.ItemTemplate>
            <DataTemplate>
                <Grid Margin="5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- 图标 -->
                    <zylo:ZyloIcon Grid.Column="0"
                                  Icon="{Binding Icon}"
                                  FontSize="16"
                                  Margin="0,0,8,0"
                                  Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                    
                    <!-- 标题 -->
                    <TextBlock Grid.Column="1"
                              Text="{Binding Title}"
                              FontSize="14"
                              VerticalAlignment="Center"
                              Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                    
                    <!-- 子项数量指示器 -->
                    <Border Grid.Column="2"
                           Background="{DynamicResource AccentFillColorSecondaryBrush}"
                           CornerRadius="10"
                           Padding="6,2"
                           Visibility="{Binding ChildrenCount, Converter={StaticResource CountToVisibilityConverter}}">
                        <TextBlock Text="{Binding ChildrenCount}"
                                  FontSize="10"
                                  Foreground="{DynamicResource TextOnAccentFillColorPrimaryBrush}"
                                  HorizontalAlignment="Center"/>
                    </Border>
                </Grid>
            </DataTemplate>
        </zylo:NavigationControl.ItemTemplate>
    </zylo:NavigationControl>

    <!-- 分隔线 -->
    <GridSplitter Grid.Column="1"
                  Width="5"
                  HorizontalAlignment="Stretch"
                  Background="{DynamicResource ControlStrokeColorDefaultBrush}"/>

    <!-- 右侧内容区域 -->
    <Grid Grid.Column="2" Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 顶部信息栏 -->
        <ui:Card Grid.Row="0" Margin="0,0,0,20" Padding="20">
            <StackPanel>
                <TextBlock Text="当前选中项信息"
                          FontSize="16"
                          FontWeight="SemiBold"
                          Margin="0,0,0,10"
                          Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Grid.Column="0" Text="标题: " FontWeight="SemiBold" Margin="0,0,10,5"/>
                    <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding SelectedNavigationItem.Title}" Margin="0,0,0,5"/>

                    <TextBlock Grid.Row="1" Grid.Column="0" Text="图标: " FontWeight="SemiBold" Margin="0,0,10,5"/>
                    <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding SelectedNavigationItem.Icon}" Margin="0,0,0,5"/>

                    <TextBlock Grid.Row="2" Grid.Column="0" Text="子项数量: " FontWeight="SemiBold" Margin="0,0,10,5"/>
                    <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding SelectedNavigationItem.ChildrenCount}" Margin="0,0,0,5"/>

                    <TextBlock Grid.Row="3" Grid.Column="0" Text="导航参数: " FontWeight="SemiBold" Margin="0,0,10,0"/>
                    <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding SelectedNavigationItem.NavigationParameter}" TextWrapping="Wrap"/>
                </Grid>
            </StackPanel>
        </ui:Card>

        <!-- 主要内容区域 -->
        <ui:Card Grid.Row="1" Padding="20">
            <ScrollViewer>
                <StackPanel>
                    <TextBlock Text="NavigationControl 基础功能演示"
                              FontSize="18"
                              FontWeight="SemiBold"
                              Margin="0,0,0,15"
                              Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>

                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,15"
                              Foreground="{DynamicResource TextFillColorSecondaryBrush}">
                        这个示例展示了 NavigationControl 的基础功能，包括：
                        <LineBreak/>• 层级导航结构显示
                        <LineBreak/>• 搜索功能
                        <LineBreak/>• 选中项双向绑定
                        <LineBreak/>• 自定义项模板
                        <LineBreak/>• 子菜单展开/折叠
                        <LineBreak/>• 导航命令处理
                    </TextBlock>

                    <!-- 操作按钮区域 -->
                    <StackPanel Orientation="Horizontal" Margin="0,20,0,0">
                        <ui:Button Content="🔍 切换搜索"
                                  Command="{Binding ToggleSearchCommand}"
                                  Margin="0,0,10,0"/>
                        
                        <ui:Button Content="📁 展开所有"
                                  Command="{Binding ExpandAllCommand}"
                                  Margin="0,0,10,0"/>
                        
                        <ui:Button Content="📂 折叠所有"
                                  Command="{Binding CollapseAllCommand}"
                                  Margin="0,0,10,0"/>
                        
                        <ui:Button Content="🔄 刷新数据"
                                  Command="{Binding RefreshDataCommand}"/>
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>
        </ui:Card>
    </Grid>
</Grid>
