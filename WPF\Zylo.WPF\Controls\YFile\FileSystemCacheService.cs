using System.Collections.Concurrent;
using System.IO;

namespace Zylo.WPF.Controls.YFile;

/// <summary>
/// 文件系统缓存服务 - 使用简单的内存缓存优化文件系统访问性能
/// </summary>
public class FileSystemCacheService : IDisposable
{
    private readonly ConcurrentDictionary<string, CacheItem> _cache = new();
    private readonly Timer _cleanupTimer;
    private bool _disposed = false;

    /// <summary>
    /// 缓存过期时间（分钟）
    /// </summary>
    public int CacheExpirationMinutes { get; set; } = 5;

    /// <summary>
    /// 最大缓存项数
    /// </summary>
    public int MaxCacheItems { get; set; } = 1000;

    public FileSystemCacheService()
    {
        // 每分钟清理一次过期缓存
        _cleanupTimer = new Timer(CleanupExpiredItems, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
    }

    /// <summary>
    /// 缓存项
    /// </summary>
    private class CacheItem
    {
        public object Value { get; set; } = null!;
        public DateTime ExpirationTime { get; set; }
    }

    /// <summary>
    /// 清理过期缓存项
    /// </summary>
    private void CleanupExpiredItems(object? state)
    {
        var now = DateTime.Now;
        var expiredKeys = _cache
            .Where(kvp => kvp.Value.ExpirationTime < now)
            .Select(kvp => kvp.Key)
            .ToList();

        foreach (var key in expiredKeys)
        {
            _cache.TryRemove(key, out _);
        }

        // 如果缓存项过多，移除最旧的项
        if (_cache.Count > MaxCacheItems)
        {
            var oldestKeys = _cache
                .OrderBy(kvp => kvp.Value.ExpirationTime)
                .Take(_cache.Count - MaxCacheItems)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var key in oldestKeys)
            {
                _cache.TryRemove(key, out _);
            }
        }
    }

    /// <summary>
    /// 获取缓存项
    /// </summary>
    private T? GetCachedItem<T>(string key) where T : class
    {
        if (_cache.TryGetValue(key, out var item) && item.ExpirationTime > DateTime.Now)
        {
            return item.Value as T;
        }

        _cache.TryRemove(key, out _);
        return null;
    }

    /// <summary>
    /// 设置缓存项
    /// </summary>
    private void SetCachedItem<T>(string key, T value) where T : class
    {
        var item = new CacheItem
        {
            Value = value,
            ExpirationTime = DateTime.Now.AddMinutes(CacheExpirationMinutes)
        };

        _cache.AddOrUpdate(key, item, (k, v) => item);
    }

    /// <summary>
    /// 获取目录信息（带缓存）
    /// </summary>
    /// <param name="directoryPath">目录路径</param>
    /// <returns>目录信息，如果不存在返回null</returns>
    public DirectoryInfo? GetDirectoryInfo(string directoryPath)
    {
        if (string.IsNullOrEmpty(directoryPath))
            return null;

        var cacheKey = $"dir_info_{directoryPath}";
        var cachedInfo = GetCachedItem<DirectoryInfo>(cacheKey);

        if (cachedInfo != null && Directory.Exists(cachedInfo.FullName))
        {
            return cachedInfo;
        }

        try
        {
            if (Directory.Exists(directoryPath))
            {
                var dirInfo = new DirectoryInfo(directoryPath);
                SetCachedItem(cacheKey, dirInfo);
                return dirInfo;
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"获取目录信息失败: {ex.Message}");
        }

        return null;
    }

    /// <summary>
    /// 获取文件信息（带缓存）
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>文件信息，如果不存在返回null</returns>
    public FileInfo? GetFileInfo(string filePath)
    {
        if (string.IsNullOrEmpty(filePath))
            return null;

        var cacheKey = $"file_info_{filePath}";
        var cachedInfo = GetCachedItem<FileInfo>(cacheKey);

        if (cachedInfo != null && File.Exists(cachedInfo.FullName))
        {
            return cachedInfo;
        }

        try
        {
            if (File.Exists(filePath))
            {
                var fileInfo = new FileInfo(filePath);
                SetCachedItem(cacheKey, fileInfo);
                return fileInfo;
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"获取文件信息失败: {ex.Message}");
        }

        return null;
    }

    /// <summary>
    /// 获取目录下的子目录列表（带缓存）
    /// </summary>
    /// <param name="directoryPath">目录路径</param>
    /// <returns>子目录列表</returns>
    public List<DirectoryInfo> GetSubDirectories(string directoryPath)
    {
        if (string.IsNullOrEmpty(directoryPath))
            return new List<DirectoryInfo>();

        var cacheKey = $"sub_dirs_{directoryPath}";
        var cachedDirs = GetCachedItem<List<DirectoryInfo>>(cacheKey);

        if (cachedDirs != null)
        {
            return cachedDirs;
        }

        try
        {
            if (Directory.Exists(directoryPath))
            {
                var dirInfo = new DirectoryInfo(directoryPath);
                var subDirs = dirInfo.GetDirectories()
                    .Where(d => !d.Attributes.HasFlag(FileAttributes.Hidden))
                    .OrderBy(d => d.Name)
                    .ToList();

                SetCachedItem(cacheKey, subDirs);
                return subDirs;
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"获取子目录失败: {ex.Message}");
        }

        return new List<DirectoryInfo>();
    }

    /// <summary>
    /// 获取目录下的文件列表（带缓存）
    /// </summary>
    /// <param name="directoryPath">目录路径</param>
    /// <returns>文件列表</returns>
    public List<FileInfo> GetFiles(string directoryPath)
    {
        if (string.IsNullOrEmpty(directoryPath))
            return new List<FileInfo>();

        var cacheKey = $"files_{directoryPath}";
        var cachedFiles = GetCachedItem<List<FileInfo>>(cacheKey);

        if (cachedFiles != null)
        {
            return cachedFiles;
        }

        try
        {
            if (Directory.Exists(directoryPath))
            {
                var dirInfo = new DirectoryInfo(directoryPath);
                var files = dirInfo.GetFiles()
                    .Where(f => !f.Attributes.HasFlag(FileAttributes.Hidden))
                    .OrderBy(f => f.Name)
                    .ToList();

                SetCachedItem(cacheKey, files);
                return files;
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"获取文件列表失败: {ex.Message}");
        }

        return new List<FileInfo>();
    }

    /// <summary>
    /// 检查目录是否有子目录（带缓存）
    /// </summary>
    /// <param name="directoryPath">目录路径</param>
    /// <returns>是否有子目录</returns>
    public bool HasSubDirectories(string directoryPath)
    {
        if (string.IsNullOrEmpty(directoryPath))
            return false;

        var cacheKey = $"has_sub_dirs_{directoryPath}";

        if (_cache.TryGetValue(cacheKey, out var item) && item.ExpirationTime > DateTime.Now)
        {
            return (bool)item.Value;
        }

        try
        {
            if (Directory.Exists(directoryPath))
            {
                var hasSubDirs = Directory.GetDirectories(directoryPath).Any();
                var cacheItem = new CacheItem
                {
                    Value = hasSubDirs,
                    ExpirationTime = DateTime.Now.AddMinutes(CacheExpirationMinutes)
                };
                _cache.AddOrUpdate(cacheKey, cacheItem, (k, v) => cacheItem);
                return hasSubDirs;
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"检查子目录失败: {ex.Message}");
        }

        return false;
    }

    /// <summary>
    /// 清除指定路径的缓存
    /// </summary>
    /// <param name="path">路径</param>
    public void InvalidateCache(string path)
    {
        if (string.IsNullOrEmpty(path))
            return;

        var keysToRemove = new List<string>
        {
            $"dir_info_{path}",
            $"file_info_{path}",
            $"sub_dirs_{path}",
            $"files_{path}",
            $"has_sub_dirs_{path}"
        };

        foreach (var key in keysToRemove)
        {
            _cache.TryRemove(key, out _);
        }

        // 同时清除父目录的缓存
        var parentPath = Path.GetDirectoryName(path);
        if (!string.IsNullOrEmpty(parentPath))
        {
            InvalidateCache(parentPath);
        }
    }

    /// <summary>
    /// 清除所有缓存
    /// </summary>
    public void ClearCache()
    {
        _cache.Clear();
    }

    /// <summary>
    /// 获取缓存统计信息
    /// </summary>
    /// <returns>缓存统计信息</returns>
    public string GetCacheStats()
    {
        var count = _cache.Count;
        return $"缓存项数: {count}";
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 释放资源的具体实现
    /// </summary>
    /// <param name="disposing">是否正在释放托管资源</param>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                _cleanupTimer?.Dispose();
                _cache?.Clear();
            }
            _disposed = true;
        }
    }

    /// <summary>
    /// 析构函数
    /// </summary>
    ~FileSystemCacheService()
    {
        Dispose(false);
    }
}
