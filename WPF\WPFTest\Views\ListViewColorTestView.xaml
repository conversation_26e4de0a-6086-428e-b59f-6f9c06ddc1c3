<UserControl x:Class="WPFTest.Views.ListViewColorTestView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" 
                   Text="🎯 ListView 颜色测试"
                   FontSize="24"
                   FontWeight="Bold"
                   Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                   Margin="0,0,0,20"/>

        <!-- 控制按钮 -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,20">
            <ui:Button Content="🔴 红色强调色"
                       Padding="12,8"
                       Margin="0,0,10,0"
                       Click="SetRedAccent"/>
            <ui:Button Content="🟢 绿色强调色"
                       Padding="12,8"
                       Margin="0,0,10,0"
                       Click="SetGreenAccent"/>
            <ui:Button Content="🔵 蓝色强调色"
                       Padding="12,8"
                       Margin="0,0,10,0"
                       Click="SetBlueAccent"/>
            <ui:Button Content="🟡 黄色强调色"
                       Padding="12,8"
                       Margin="0,0,10,0"
                       Click="SetYellowAccent"/>
            <ui:Button Content="🟣 紫色强调色"
                       Padding="12,8"
                       Click="SetPurpleAccent"/>
        </StackPanel>

        <!-- ListView测试区域 -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="20"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧：默认ListView -->
            <StackPanel Grid.Column="0">
                <TextBlock Text="默认 ListView (无样式)"
                           FontSize="16"
                           FontWeight="Medium"
                           Margin="0,0,0,10"/>
                
                <ListView x:Name="DefaultListView"
                          Height="300"
                          BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                          BorderThickness="1">
                    <ListViewItem Content="项目 1 - 测试鼠标悬停效果"/>
                    <ListViewItem Content="项目 2 - 测试选中效果"/>
                    <ListViewItem Content="项目 3 - 测试强调色变化"/>
                    <ListViewItem Content="项目 4 - 测试主题切换"/>
                    <ListViewItem Content="项目 5 - 测试颜色资源"/>
                    <ListViewItem Content="项目 6 - 测试左侧指示器"/>
                    <ListViewItem Content="项目 7 - 测试背景颜色"/>
                    <ListViewItem Content="项目 8 - 测试文字颜色"/>
                </ListView>

                <TextBlock Text="当前资源值："
                           FontSize="12"
                           FontWeight="Medium"
                           Margin="0,10,0,5"/>
                
                <StackPanel x:Name="ResourceInfo" Margin="0,0,0,10">
                    <TextBlock x:Name="PillColorText" FontSize="10" FontFamily="Consolas"/>
                    <TextBlock x:Name="HoverColorText" FontSize="10" FontFamily="Consolas"/>
                    <TextBlock x:Name="AccentColorText" FontSize="10" FontFamily="Consolas"/>
                </StackPanel>
            </StackPanel>

            <!-- 右侧：WPFUI ListView -->
            <StackPanel Grid.Column="2">
                <TextBlock Text="WPFUI ListView (官方样式)"
                           FontSize="16"
                           FontWeight="Medium"
                           Margin="0,0,0,10"/>
                
                <ui:ListView x:Name="WpfUIListView"
                             Height="300"
                             BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                             BorderThickness="1">
                    <ui:ListViewItem Content="项目 1 - 测试鼠标悬停效果"/>
                    <ui:ListViewItem Content="项目 2 - 测试选中效果"/>
                    <ui:ListViewItem Content="项目 3 - 测试强调色变化"/>
                    <ui:ListViewItem Content="项目 4 - 测试主题切换"/>
                    <ui:ListViewItem Content="项目 5 - 测试颜色资源"/>
                    <ui:ListViewItem Content="项目 6 - 测试左侧指示器"/>
                    <ui:ListViewItem Content="项目 7 - 测试背景颜色"/>
                    <ui:ListViewItem Content="项目 8 - 测试文字颜色"/>
                </ui:ListView>

                <ui:Button Content="🔄 刷新资源信息"
                           Padding="8,6"
                           Margin="0,10,0,0"
                           Click="RefreshResourceInfo"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
