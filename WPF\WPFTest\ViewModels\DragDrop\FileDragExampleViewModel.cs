using System.Collections.ObjectModel;
using System.IO;
using System.Runtime.InteropServices;
using System.Windows;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using GongSolutions.Wpf.DragDrop;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels.DragDrop
{
    /// <summary>
    /// 文件拖拽示例 ViewModel
    /// 仿 Windows 文件管理器的拖拽功能，支持将文件拖拽到外部应用程序
    /// 
    /// 核心功能：
    /// 1. 读取指定目录下的 DWG 文件
    /// 2. 支持拖拽文件到外部软件（如 AutoCAD）
    /// 3. 文件预览和详细信息显示
    /// 4. 文件操作（打开、复制路径、在资源管理器中显示）
    /// 5. 支持多文件选择和批量拖拽
    /// </summary>
    public partial class FileDragExampleViewModel : ObservableObject, IDragSource, IDropTarget, IDisposable
    {
        #region 字段

        private readonly YLoggerInstance _logger = YLogger.ForSilent<FileDragExampleViewModel>();

        #endregion

        #region 属性

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        private string statusMessage = "请选择 DWG 文件目录";

        /// <summary>
        /// 当前目录路径
        /// </summary>
        [ObservableProperty]
        private string currentDirectory = string.Empty;

        /// <summary>
        /// 文件列表
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<DwgFileInfo> dwgFiles = new();

        /// <summary>
        /// 选中的文件
        /// </summary>
        [ObservableProperty]
        private DwgFileInfo? selectedFile;

        /// <summary>
        /// 选中的多个文件
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<DwgFileInfo> selectedFiles = new();

        /// <summary>
        /// 文件总数
        /// </summary>
        [ObservableProperty]
        private int totalFileCount;

        /// <summary>
        /// 总文件大小
        /// </summary>
        [ObservableProperty]
        private string totalFileSize = "0 B";

        /// <summary>
        /// 拖拽操作次数
        /// </summary>
        [ObservableProperty]
        private int dragOperationCount;

        /// <summary>
        /// 最后拖拽的文件
        /// </summary>
        [ObservableProperty]
        private string lastDraggedFile = "无";

        /// <summary>
        /// 拖拽进度状态
        /// </summary>
        [ObservableProperty]
        private string dragProgressStatus = "";

        /// <summary>
        /// 是否显示拖拽进度
        /// </summary>
        [ObservableProperty]
        private bool isDragInProgress = false;

        /// <summary>
        /// 拖拽文件数量
        /// </summary>
        [ObservableProperty]
        private int dragFileCount = 0;

        /// <summary>
        /// 是否正在加载
        /// </summary>
        [ObservableProperty]
        private bool isLoading;

        /// <summary>
        /// 搜索关键词
        /// </summary>
        [ObservableProperty]
        private string searchKeyword = string.Empty;

        /// <summary>
        /// 过滤后的文件列表
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<DwgFileInfo> filteredFiles = new();

        /// <summary>
        /// 搜索防抖计时器
        /// </summary>
        private System.Timers.Timer? _searchDebounceTimer;

        /// <summary>
        /// 文件加载取消令牌源
        /// </summary>
        private CancellationTokenSource? _loadCancellationTokenSource;

        /// <summary>
        /// 性能监控字典
        /// </summary>
        private readonly Dictionary<string, System.Diagnostics.Stopwatch> _performanceMonitors = new();

        /// <summary>
        /// 拖拽验证缓存，避免频繁的文件系统调用
        /// </summary>
        private readonly Dictionary<string, (bool canDrag, DateTime lastCheck)> _dragValidationCache = new();

        /// <summary>
        /// 拖拽验证缓存过期时间（秒）
        /// </summary>
        private const int DRAG_VALIDATION_CACHE_SECONDS = 5;

        /// <summary>
        /// 上次 CanStartDrag 调用时间，用于限制调用频率
        /// </summary>
        private DateTime _lastCanStartDragCall = DateTime.MinValue;

        /// <summary>
        /// CanStartDrag 调用间隔限制（毫秒）
        /// </summary>
        private const int CAN_START_DRAG_THROTTLE_MS = 100;

        /// <summary>
        /// 拖拽状态管理
        /// </summary>
        private bool _isDragging = false;
        private DateTime _dragStartTime = DateTime.MinValue;
        private System.Threading.Timer? _dragTimeoutTimer;

        /// <summary>
        /// 拖拽超时时间（毫秒）
        /// </summary>
        private const int DRAG_TIMEOUT_MS = 10000; // 10秒超时

        /// <summary>
        /// 拖拽策略枚举
        /// </summary>
        public enum DragStrategy
        {
            /// <summary>
            /// 自动检测：根据拖拽目标自动选择策略
            /// </summary>
            Auto,
            /// <summary>
            /// 原生拖拽：适用于拖拽到外部应用程序
            /// </summary>
            Native,
            /// <summary>
            /// Gong 拖拽：适用于界面内拖拽
            /// </summary>
            Gong
        }

        /// <summary>
        /// 当前拖拽策略
        /// </summary>
        [ObservableProperty]
        [NotifyPropertyChangedFor(nameof(CurrentDragStrategyName))]
        private DragStrategy currentDragStrategy = DragStrategy.Auto;

        /// <summary>
        /// 当前拖拽策略的友好名称
        /// </summary>
        public string CurrentDragStrategyName => CurrentDragStrategy switch
        {
            DragStrategy.Auto => "智能检测",
            DragStrategy.Native => "原生拖拽",
            DragStrategy.Gong => "界面拖拽",
            _ => "未知"
        };

        /// <summary>
        /// 最后一次文件加载耗时
        /// </summary>
        [ObservableProperty]
        private string lastLoadTime = "未加载";

        /// <summary>
        /// 最后一次搜索耗时
        /// </summary>
        [ObservableProperty]
        private string lastSearchTime = "未搜索";

        #endregion

        #region 构造函数

        public FileDragExampleViewModel()
        {
            _logger.Info("🗂️ FileDragExampleViewModel 初始化开始");

            // 初始化搜索防抖计时器
            InitializeSearchDebounceTimer();

            // 设置默认目录（项目根目录下的 Assets/DWG 文件夹）
            var projectRoot = GetProjectRootDirectory();
            var dwgDirectory = Path.Combine(projectRoot, "Assets", "DWG");

            if (Directory.Exists(dwgDirectory))
            {
                CurrentDirectory = dwgDirectory;
                LoadDwgFiles();
            }
            else
            {
                StatusMessage = "DWG 目录不存在，请选择包含 DWG 文件的目录";
                _logger.Warning($"DWG 目录不存在: {dwgDirectory}");
            }

            _logger.Info("✅ FileDragExampleViewModel 初始化完成");
        }

        /// <summary>
        /// 搜索关键词变化时的处理
        /// </summary>
        partial void OnSearchKeywordChanged(string value)
        {
            // 重置防抖计时器
            _searchDebounceTimer?.Stop();
            _searchDebounceTimer?.Start();
        }

        #endregion

        #region 命令

        /// <summary>
        /// 选择目录命令
        /// </summary>
        [RelayCommand]
        private async Task SelectDirectory()
        {
            try
            {
                var dialog = new Microsoft.Win32.OpenFolderDialog
                {
                    Title = "选择包含 DWG 文件的目录",
                    InitialDirectory = CurrentDirectory
                };

                if (dialog.ShowDialog() == true)
                {
                    CurrentDirectory = dialog.FolderName;
                    await LoadDwgFilesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"选择目录失败: {ex.Message}");
                StatusMessage = $"❌ 选择目录失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 刷新文件列表命令
        /// </summary>
        [RelayCommand]
        private async Task RefreshFiles()
        {
            if (!string.IsNullOrEmpty(CurrentDirectory))
            {
                await LoadDwgFilesAsync();
            }
        }

        /// <summary>
        /// 打开文件命令
        /// </summary>
        [RelayCommand]
        private void OpenFile(DwgFileInfo? fileInfo)
        {
            if (fileInfo == null) return;

            try
            {
                _logger.Info($"🚀 尝试打开文件: {fileInfo.FullPath}");

                // 方法1：尝试使用 Shell 执行（默认关联程序）
                var startInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = fileInfo.FullPath,
                    UseShellExecute = true,
                    Verb = "open"
                };

                var process = System.Diagnostics.Process.Start(startInfo);

                if (process != null)
                {
                    StatusMessage = $"📂 已打开文件: {fileInfo.Name}";
                    _logger.Info($"✅ 文件打开成功: {fileInfo.FullPath}");
                }
                else
                {
                    // 方法2：尝试直接启动 AutoCAD
                    TryOpenWithAutoCAD(fileInfo);
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 使用默认程序打开失败: {ex.Message}");

                // 方法3：备用方案 - 尝试找到并启动 AutoCAD
                try
                {
                    TryOpenWithAutoCAD(fileInfo);
                }
                catch (Exception ex2)
                {
                    _logger.Error($"❌ 使用 AutoCAD 打开失败: {ex2.Message}");
                    StatusMessage = $"❌ 打开文件失败: {ex.Message}";
                }
            }
        }

        /// <summary>
        /// 尝试使用 AutoCAD 打开文件
        /// </summary>
        private void TryOpenWithAutoCAD(DwgFileInfo fileInfo)
        {
            // 常见的 AutoCAD 可执行文件路径
            var autocadPaths = new[]
            {
                @"C:\Program Files\Autodesk\AutoCAD 2024\acad.exe",
                @"C:\Program Files\Autodesk\AutoCAD 2023\acad.exe",
                @"C:\Program Files\Autodesk\AutoCAD 2022\acad.exe",
                @"C:\Program Files\Autodesk\AutoCAD 2021\acad.exe",
                @"C:\Program Files (x86)\Autodesk\AutoCAD 2024\acad.exe",
                @"C:\Program Files (x86)\Autodesk\AutoCAD 2023\acad.exe",
                @"C:\Program Files (x86)\Autodesk\AutoCAD 2022\acad.exe",
                @"C:\Program Files (x86)\Autodesk\AutoCAD 2021\acad.exe"
            };

            foreach (var autocadPath in autocadPaths)
            {
                if (File.Exists(autocadPath))
                {
                    _logger.Info($"🎯 找到 AutoCAD: {autocadPath}");

                    var startInfo = new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = autocadPath,
                        Arguments = $"\"{fileInfo.FullPath}\"",
                        UseShellExecute = true
                    };

                    var process = System.Diagnostics.Process.Start(startInfo);
                    if (process != null)
                    {
                        StatusMessage = $"📂 已使用 AutoCAD 打开: {fileInfo.Name}";
                        _logger.Info($"✅ 使用 AutoCAD 打开成功: {fileInfo.FullPath}");
                        return;
                    }
                }
            }

            throw new FileNotFoundException("未找到 AutoCAD 安装路径");
        }



        /// <summary>
        /// 显示文件属性命令
        /// </summary>
        [RelayCommand]
        private void ShowFileProperties()
        {
            if (SelectedFile == null) return;

            try
            {
                var startInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = "rundll32.exe",
                    Arguments = $"shell32.dll,OpenAs_RunDLL \"{SelectedFile.FullPath}\"",
                    UseShellExecute = true
                };

                System.Diagnostics.Process.Start(startInfo);
                StatusMessage = $"📊 已显示文件属性: {SelectedFile.Name}";
                _logger.Info($"显示文件属性: {SelectedFile.FullPath}");
            }
            catch (Exception ex)
            {
                _logger.Error($"显示文件属性失败: {ex.Message}");
                StatusMessage = $"❌ 显示属性失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 复制文件路径命令
        /// </summary>
        [RelayCommand]
        private void CopyFilePath(DwgFileInfo? fileInfo)
        {
            if (fileInfo == null) return;

            try
            {
                Clipboard.SetText(fileInfo.FullPath);
                StatusMessage = $"📋 已复制路径: {fileInfo.Name}";
                _logger.Info($"复制文件路径: {fileInfo.FullPath}");
            }
            catch (Exception ex)
            {
                _logger.Error($"复制路径失败: {ex.Message}");
                StatusMessage = $"❌ 复制路径失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 在资源管理器中显示命令
        /// </summary>
        [RelayCommand]
        private void ShowInExplorer(DwgFileInfo? fileInfo)
        {
            if (fileInfo == null) return;

            try
            {
                var startInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = "explorer.exe",
                    Arguments = $"/select,\"{fileInfo.FullPath}\"",
                    UseShellExecute = true
                };
                
                System.Diagnostics.Process.Start(startInfo);
                StatusMessage = $"📁 已在资源管理器中显示: {fileInfo.Name}";
                _logger.Info($"在资源管理器中显示: {fileInfo.FullPath}");
            }
            catch (Exception ex)
            {
                _logger.Error($"在资源管理器中显示失败: {ex.Message}");
                StatusMessage = $"❌ 显示失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 搜索文件命令
        /// </summary>
        [RelayCommand]
        private void SearchFiles()
        {
            FilterFiles();
        }

        /// <summary>
        /// 测试拖拽功能命令
        /// </summary>
        [RelayCommand]
        private void TestDragDrop()
        {
            if (SelectedFile == null)
            {
                StatusMessage = "⚠️ 请先选择一个文件进行测试";
                return;
            }

            try
            {
                // 检测 AutoCAD 是否正在运行
                var autocadRunning = IsAutoCADRunning();
                _logger.Info($"🔍 AutoCAD 运行状态: {(autocadRunning ? "正在运行" : "未运行")}");

                // 创建测试拖拽数据
                var filePaths = new[] { SelectedFile.FullPath };
                var dataObject = new DataObject();
                dataObject.SetData(DataFormats.FileDrop, filePaths);

                // 验证数据对象
                var hasFileDrop = dataObject.GetDataPresent(DataFormats.FileDrop);
                var files = dataObject.GetData(DataFormats.FileDrop) as string[];

                _logger.Info($"🧪 拖拽测试结果:");
                _logger.Info($"   - 文件路径: {SelectedFile.FullPath}");
                _logger.Info($"   - 文件存在: {File.Exists(SelectedFile.FullPath)}");
                _logger.Info($"   - 数据格式正确: {hasFileDrop}");
                _logger.Info($"   - 文件数量: {files?.Length ?? 0}");
                _logger.Info($"   - AutoCAD 状态: {(autocadRunning ? "运行中" : "未运行")}");

                if (hasFileDrop && files != null && files.Length > 0)
                {
                    var message = autocadRunning
                        ? $"✅ 拖拽数据测试通过，AutoCAD 正在运行: {SelectedFile.Name}"
                        : $"✅ 拖拽数据测试通过，但 AutoCAD 未运行: {SelectedFile.Name}";

                    StatusMessage = message;

                    // 尝试将文件复制到剪贴板作为额外测试
                    var fileCollection = new System.Collections.Specialized.StringCollection();
                    fileCollection.Add(SelectedFile.FullPath);
                    Clipboard.SetFileDropList(fileCollection);

                    _logger.Info("✅ 文件已复制到剪贴板，可以尝试粘贴到其他应用程序");

                    if (!autocadRunning)
                    {
                        _logger.Info("💡 建议：启动 AutoCAD 后再尝试拖拽功能");
                    }
                }
                else
                {
                    StatusMessage = $"❌ 拖拽数据测试失败: {SelectedFile.Name}";
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"拖拽测试失败: {ex.Message}");
                StatusMessage = $"❌ 拖拽测试失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 检测 AutoCAD 是否正在运行
        /// </summary>
        private bool IsAutoCADRunning()
        {
            try
            {
                var autocadProcesses = System.Diagnostics.Process.GetProcessesByName("acad");
                return autocadProcesses.Length > 0;
            }
            catch (Exception ex)
            {
                _logger.Warning($"检测 AutoCAD 进程失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 复制到剪切板命令
        /// </summary>
        [RelayCommand]
        private void CopyToClipboard()
        {
            if (SelectedFile == null)
            {
                StatusMessage = "⚠️ 请先选择一个文件";
                return;
            }

            try
            {
                // 获取要复制的文件列表
                var filesToCopy = new List<string>();

                if (SelectedFiles.Count > 1)
                {
                    // 如果选中了多个文件，复制所有选中的文件
                    filesToCopy.AddRange(SelectedFiles.Select(f => f.FullPath));
                    _logger.Info($"📋 准备复制 {SelectedFiles.Count} 个文件到剪切板");
                }
                else
                {
                    // 只复制当前选中的文件
                    filesToCopy.Add(SelectedFile.FullPath);
                    _logger.Info($"📋 准备复制文件到剪切板: {SelectedFile.Name}");
                }

                // 验证所有文件都存在
                var validFiles = new List<string>();
                foreach (var filePath in filesToCopy)
                {
                    if (File.Exists(filePath))
                    {
                        validFiles.Add(filePath);
                        _logger.Info($"✅ 文件有效: {Path.GetFileName(filePath)}");
                    }
                    else
                    {
                        _logger.Warning($"⚠️ 文件不存在: {filePath}");
                    }
                }

                if (validFiles.Count == 0)
                {
                    StatusMessage = "❌ 没有有效的文件可以复制";
                    return;
                }

                // 使用 StringCollection 复制文件到剪切板
                var fileCollection = new System.Collections.Specialized.StringCollection();
                fileCollection.AddRange(validFiles.ToArray());

                Clipboard.SetFileDropList(fileCollection);

                // 更新状态
                var message = validFiles.Count == 1
                    ? $"📋 已复制文件到剪切板: {Path.GetFileName(validFiles[0])}"
                    : $"📋 已复制 {validFiles.Count} 个文件到剪切板";

                StatusMessage = message;
                _logger.Info($"✅ {message}");
                _logger.Info("💡 现在可以在 AutoCAD 或其他应用程序中使用 Ctrl+V 粘贴文件");

                // 检测 AutoCAD 是否运行
                if (IsAutoCADRunning())
                {
                    _logger.Info("🎯 检测到 AutoCAD 正在运行，可以直接在 AutoCAD 中粘贴文件");
                }
                else
                {
                    _logger.Info("💡 建议：启动 AutoCAD 后使用 Ctrl+V 粘贴文件");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"复制到剪切板失败: {ex.Message}");
                StatusMessage = $"❌ 复制失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 验证拖拽格式命令
        /// </summary>
        [RelayCommand]
        private void ValidateDragFormat()
        {
            if (SelectedFile == null)
            {
                StatusMessage = "⚠️ 请先选择一个文件进行验证";
                return;
            }

            try
            {
                _logger.Info("🔍 开始验证拖拽格式...");

                var filePaths = new[] { SelectedFile.FullPath };
                var dataObject = CreateStandardDropDataObject(filePaths);

                // 验证各种数据格式
                var formats = new[]
                {
                    DataFormats.FileDrop,
                    "CF_HDROP",
                    "Shell IDList Array",
                    "FileNameW",
                    "FileName",
                    "Preferred DropEffect"
                };

                var validFormats = new List<string>();
                var invalidFormats = new List<string>();

                foreach (var format in formats)
                {
                    try
                    {
                        var hasFormat = dataObject.GetDataPresent(format);
                        if (hasFormat)
                        {
                            var data = dataObject.GetData(format);
                            validFormats.Add($"✅ {format}: {data?.GetType().Name ?? "null"}");
                            _logger.Info($"✅ 格式 {format} 可用，数据类型: {data?.GetType().Name ?? "null"}");
                        }
                        else
                        {
                            invalidFormats.Add($"❌ {format}: 不可用");
                            _logger.Warning($"❌ 格式 {format} 不可用");
                        }
                    }
                    catch (Exception ex)
                    {
                        invalidFormats.Add($"❌ {format}: 异常 - {ex.Message}");
                        _logger.Error($"❌ 验证格式 {format} 时出错: {ex.Message}");
                    }
                }

                // 显示验证结果
                var result = $"📊 拖拽格式验证结果:\n\n" +
                           $"有效格式 ({validFormats.Count}):\n{string.Join("\n", validFormats)}\n\n" +
                           $"无效格式 ({invalidFormats.Count}):\n{string.Join("\n", invalidFormats)}";

                _logger.Info(result);
                StatusMessage = $"✅ 格式验证完成: {validFormats.Count}/{formats.Length} 个格式有效";

                // 特别测试 FileDrop 格式的数据
                if (dataObject.GetDataPresent(DataFormats.FileDrop))
                {
                    var fileDropData = dataObject.GetData(DataFormats.FileDrop) as string[];
                    if (fileDropData != null)
                    {
                        _logger.Info($"📁 FileDrop 数据验证:");
                        _logger.Info($"   - 文件数量: {fileDropData.Length}");
                        foreach (var file in fileDropData)
                        {
                            _logger.Info($"   - 文件: {file}");
                            _logger.Info($"   - 存在: {File.Exists(file)}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"验证拖拽格式失败: {ex.Message}");
                StatusMessage = $"❌ 验证失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 清空搜索命令
        /// </summary>
        [RelayCommand]
        private void ClearSearch()
        {
            SearchKeyword = string.Empty;
            FilterFiles();
        }

        /// <summary>
        /// 切换拖拽策略命令
        /// </summary>
        [RelayCommand]
        private void SwitchDragStrategy()
        {
            CurrentDragStrategy = CurrentDragStrategy switch
            {
                DragStrategy.Auto => DragStrategy.Native,
                DragStrategy.Native => DragStrategy.Gong,
                DragStrategy.Gong => DragStrategy.Auto,
                _ => DragStrategy.Auto
            };

            StatusMessage = $"🔄 拖拽策略已切换为: {CurrentDragStrategyName}";
            _logger.Info($"拖拽策略切换为: {CurrentDragStrategy} ({CurrentDragStrategyName})");
        }

        #endregion

        #region IDragSource 实现

        public void StartDrag(IDragInfo dragInfo)
        {
            try
            {
                if (dragInfo.SourceItem is DwgFileInfo fileInfo)
                {
                    // 设置拖拽状态
                    _isDragging = true;
                    _dragStartTime = DateTime.Now;
                    IsDragInProgress = true;

                    // 启动超时计时器
                    _dragTimeoutTimer?.Dispose();
                    _dragTimeoutTimer = new System.Threading.Timer(OnDragTimeout, null, DRAG_TIMEOUT_MS, Timeout.Infinite);

                    // 创建文件路径数组用于拖拽
                    var filePaths = new List<string>();

                    // 如果有多个选中的文件，拖拽所有选中的文件
                    if (SelectedFiles.Count > 1 && SelectedFiles.Contains(fileInfo))
                    {
                        filePaths.AddRange(SelectedFiles.Select(f => f.FullPath));
                        LastDraggedFile = $"{SelectedFiles.Count} 个文件";
                    }
                    else
                    {
                        filePaths.Add(fileInfo.FullPath);
                        LastDraggedFile = fileInfo.Name;
                    }

                    // 验证文件路径
                    var validPaths = filePaths.Where(File.Exists).ToArray();
                    if (validPaths.Length == 0)
                    {
                        _logger.Warning("⚠️ 没有有效的文件可以拖拽");
                        ResetDragState();
                        return;
                    }

                    // 设置拖拽进度信息
                    DragFileCount = validPaths.Length;
                    DragProgressStatus = $"正在拖拽 {validPaths.Length} 个文件...";

                    _logger.Info($"开始拖拽 {validPaths.Length} 个文件，策略: {CurrentDragStrategy}");

                    // 根据策略选择拖拽方式
                    switch (CurrentDragStrategy)
                    {
                        case DragStrategy.Auto:
                            StartSmartDragDrop(validPaths, dragInfo);
                            break;
                        case DragStrategy.Native:
                            StartNativeDragDrop(validPaths, dragInfo);
                            break;
                        case DragStrategy.Gong:
                            StartGongDragDrop(validPaths, dragInfo);
                            break;
                        default:
                            StartSmartDragDrop(validPaths, dragInfo);
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"开始拖拽失败: {ex.Message}");
                StatusMessage = $"❌ 拖拽失败: {ex.Message}";
                ResetDragState();
            }
        }

        /// <summary>
        /// 智能拖拽：根据拖拽目标自动选择策略
        /// </summary>
        private void StartSmartDragDrop(string[] filePaths, IDragInfo dragInfo)
        {
            try
            {
                // 检测拖拽目标
                var isInternalDrag = IsInternalDragTarget(dragInfo);

                if (isInternalDrag)
                {
                    _logger.Info("界面内拖拽 → Gong 策略");
                    StartGongDragDrop(filePaths, dragInfo);
                }
                else
                {
                    _logger.Info("外部拖拽 → 原生策略");
                    StartNativeDragDrop(filePaths, dragInfo);
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"智能拖拽失败: {ex.Message}");
                // 回退到原生拖拽
                StartNativeDragDrop(filePaths, dragInfo);
            }
        }

        /// <summary>
        /// 检测是否为界面内拖拽
        /// </summary>
        private bool IsInternalDragTarget(IDragInfo dragInfo)
        {
            try
            {
                var sourceElement = dragInfo.VisualSource;
                if (sourceElement == null) return false;

                var window = Window.GetWindow(sourceElement);
                if (window == null) return false;

                // 简化判断：默认为界面内拖拽，实际外部拖拽会在完成时处理
                return true;
            }
            catch (Exception ex)
            {
                _logger.Warning($"拖拽目标检测失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Gong 拖拽：适用于界面内拖拽
        /// </summary>
        private void StartGongDragDrop(string[] filePaths, IDragInfo dragInfo)
        {
            try
            {
                // 创建 Gong 拖拽数据对象
                var dataObject = new DataObject();

                // 设置文件拖拽数据
                dataObject.SetData(DataFormats.FileDrop, filePaths);

                // 设置自定义数据格式，用于界面内识别
                dataObject.SetData("DwgFileList", filePaths);
                dataObject.SetData("DragSource", "FileDragExample");

                // 如果只有一个文件，也设置文本格式
                if (filePaths.Length == 1)
                {
                    dataObject.SetData(DataFormats.Text, filePaths[0]);
                    dataObject.SetData(DataFormats.UnicodeText, filePaths[0]);
                }

                // 设置拖拽数据到 gong-wpf-dragdrop
                dragInfo.Data = dataObject;
                dragInfo.Effects = DragDropEffects.Copy | DragDropEffects.Move;

                DragOperationCount++;
                StatusMessage = $"开始界面内拖拽: {LastDraggedFile}";
            }
            catch (Exception ex)
            {
                _logger.Error($"启动 Gong 拖拽失败: {ex.Message}");
                StatusMessage = $"❌ Gong 拖拽启动失败: {ex.Message}";
                throw;
            }
        }

        /// <summary>
        /// 启动原生 WPF 拖拽操作（优化版本）
        /// </summary>
        private void StartNativeDragDrop(string[] filePaths, IDragInfo dragInfo)
        {
            try
            {
                // 创建标准的 DataObject
                var dataObject = new DataObject();

                // 设置文件拖拽数据
                dataObject.SetData(DataFormats.FileDrop, filePaths);

                // 如果只有一个文件，也设置文本格式
                if (filePaths.Length == 1)
                {
                    dataObject.SetData(DataFormats.Text, filePaths[0]);
                    dataObject.SetData(DataFormats.UnicodeText, filePaths[0]);
                }

                // 设置拖拽数据到 gong-wpf-dragdrop（用于外部应用程序）
                dragInfo.Data = dataObject;
                dragInfo.Effects = DragDropEffects.Copy | DragDropEffects.Move;

                DragOperationCount++;
                StatusMessage = $"开始外部拖拽: {LastDraggedFile}";
            }
            catch (Exception ex)
            {
                _logger.Error($"启动原生拖拽失败: {ex.Message}");
                StatusMessage = $"❌ 拖拽启动失败: {ex.Message}";
                throw;
            }
        }

        public bool CanStartDrag(IDragInfo dragInfo)
        {
            var now = DateTime.Now;

            // 限制调用频率，避免过于频繁的检查
            if ((now - _lastCanStartDragCall).TotalMilliseconds < CAN_START_DRAG_THROTTLE_MS)
            {
                return dragInfo.SourceItem is DwgFileInfo;
            }

            _lastCanStartDragCall = now;

            if (dragInfo.SourceItem is DwgFileInfo fileInfo)
            {
                var filePath = fileInfo.FullPath;

                // 检查缓存
                if (_dragValidationCache.TryGetValue(filePath, out var cached))
                {
                    if ((now - cached.lastCheck).TotalSeconds < DRAG_VALIDATION_CACHE_SECONDS)
                    {
                        return cached.canDrag;
                    }
                }

                // 重新验证
                var canDrag = !string.IsNullOrEmpty(filePath) && File.Exists(filePath);
                _dragValidationCache[filePath] = (canDrag, now);

                return canDrag;
            }

            return false;
        }

        public void Dropped(IDropInfo dropInfo)
        {
            // 文件拖拽到外部应用程序时会调用此方法
            StatusMessage = $"✅ 文件拖拽完成: {LastDraggedFile}";
            _logger.Info($"文件拖拽完成: {LastDraggedFile}");
        }

        public void DragDropOperationFinished(DragDropEffects operationResult, IDragInfo dragInfo)
        {
            try
            {
                // 检测拖拽策略类型
                var strategyUsed = "未知";
                var isInternalDrop = false;

                if (dragInfo?.Data is DataObject dataObject)
                {
                    if (dataObject.GetDataPresent("DragSource") && dataObject.GetData("DragSource")?.ToString() == "FileDragExample")
                    {
                        strategyUsed = "Gong";
                        isInternalDrop = true;
                    }
                    else
                    {
                        strategyUsed = "原生";
                    }
                }

                _logger.Info($"拖拽完成: {operationResult} ({strategyUsed})");

                // 详细的拖拽结果分析
                switch (operationResult)
                {
                    case DragDropEffects.Copy:
                        StatusMessage = $"✅ 拖拽成功（复制）: {LastDraggedFile}";
                        if (!isInternalDrop)
                        {
                            _logger.Info($"外部拖拽成功 → {LastDraggedFile}");
                        }
                        break;
                    case DragDropEffects.Move:
                        StatusMessage = $"✅ 拖拽成功（移动）: {LastDraggedFile}";
                        break;
                    case DragDropEffects.Link:
                        StatusMessage = $"✅ 拖拽成功（链接）: {LastDraggedFile}";
                        break;
                    case DragDropEffects.None:
                        if (isInternalDrop)
                        {
                            StatusMessage = $"界面内拖拽完成: {LastDraggedFile}";
                        }
                        else
                        {
                            StatusMessage = $"拖拽被取消: {LastDraggedFile}";
                        }
                        break;
                    default:
                        StatusMessage = $"拖拽结果未知: {LastDraggedFile}";
                        break;
                }
            }
            finally
            {
                // 确保重置拖拽状态
                ResetDragState();
            }
        }

        public bool TryCatchOccurredException(Exception exception)
        {
            _logger.Error($"拖拽过程中发生异常: {exception.Message}");
            StatusMessage = $"❌ 拖拽异常: {exception.Message}";
            return true;
        }

        public void DragCancelled()
        {
            StatusMessage = $"拖拽被取消: {LastDraggedFile}";
            _logger.Info("文件拖拽操作被取消");
            ResetDragState();
        }

        /// <summary>
        /// 重置拖拽状态
        /// </summary>
        private void ResetDragState()
        {
            _isDragging = false;
            _dragStartTime = DateTime.MinValue;
            _dragTimeoutTimer?.Dispose();
            _dragTimeoutTimer = null;

            // 重置拖拽进度状态
            IsDragInProgress = false;
            DragProgressStatus = "";
            DragFileCount = 0;
        }

        /// <summary>
        /// 拖拽超时处理
        /// </summary>
        private void OnDragTimeout(object? state)
        {
            if (_isDragging)
            {
                _logger.Warning("拖拽操作超时，强制取消");
                Application.Current?.Dispatcher.BeginInvoke(() =>
                {
                    StatusMessage = $"拖拽超时被取消: {LastDraggedFile}";
                    ResetDragState();
                });
            }
        }

        #endregion

        #region IDropTarget 实现

        public void DragOver(IDropInfo dropInfo)
        {
            try
            {
                if (dropInfo.Data is DataObject dataObject)
                {
                    if (dataObject.GetDataPresent(DataFormats.FileDrop) ||
                        dataObject.GetDataPresent("DwgFileList"))
                    {
                        dropInfo.Effects = DragDropEffects.Copy;
                        dropInfo.DropTargetAdorner = DropTargetAdorners.Highlight;
                        return;
                    }
                }
                dropInfo.Effects = DragDropEffects.None;
            }
            catch (Exception ex)
            {
                _logger.Warning($"拖拽悬停检查失败: {ex.Message}");
                dropInfo.Effects = DragDropEffects.None;
            }
        }

        public void Drop(IDropInfo dropInfo)
        {
            try
            {
                if (dropInfo.Data is DataObject dataObject)
                {
                    string[] filePaths = null;
                    string dataSource = "未知";

                    // 尝试获取自定义格式的文件列表
                    if (dataObject.GetDataPresent("DwgFileList"))
                    {
                        filePaths = dataObject.GetData("DwgFileList") as string[];
                        dataSource = "Gong";
                    }
                    // 尝试获取标准文件拖拽格式
                    else if (dataObject.GetDataPresent(DataFormats.FileDrop))
                    {
                        filePaths = dataObject.GetData(DataFormats.FileDrop) as string[];
                        dataSource = "原生";
                    }

                    if (filePaths != null && filePaths.Length > 0)
                    {
                        var fileCount = filePaths.Length;
                        var fileNames = filePaths.Select(Path.GetFileName).Take(3);
                        var displayNames = string.Join(", ", fileNames);
                        if (fileCount > 3)
                        {
                            displayNames += $" 等 {fileCount} 个文件";
                        }

                        StatusMessage = $"✅ 界面内拖拽成功 ({dataSource}): {displayNames}";
                        _logger.Info($"界面内拖拽成功: {fileCount} 个文件");

                        // 这里可以添加具体的处理逻辑，比如添加到某个列表等
                        // 目前只是演示功能
                    }
                    else
                    {
                        StatusMessage = "❌ 未找到有效的文件数据";
                        _logger.Warning("拖拽数据中未找到有效的文件路径");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"界面内拖拽处理失败: {ex.Message}");
                StatusMessage = $"❌ 拖拽处理失败: {ex.Message}";
            }
        }

        public void DragEnter(IDropInfo dropInfo)
        {
            DragOver(dropInfo);
        }

        public void DragLeave(IDropInfo dropInfo)
        {
        }

        #endregion

        #region 拖拽数据创建

        /// <summary>
        /// 创建标准的 Windows 拖拽数据对象
        /// </summary>
        private DataObject CreateStandardDropDataObject(string[] filePaths)
        {
            var dataObject = new DataObject();

            try
            {
                // 1. 添加标准的 FileDrop 格式（最重要）
                dataObject.SetData(DataFormats.FileDrop, filePaths);
                _logger.Info($"✅ 已设置 DataFormats.FileDrop 格式，文件数量: {filePaths.Length}");

                // 2. 创建 CF_HDROP 格式的数据（更底层的 Windows 格式）
                var hdropData = CreateCFHDropData(filePaths);
                if (hdropData != null)
                {
                    dataObject.SetData("CF_HDROP", hdropData);
                    _logger.Info("✅ 已设置 CF_HDROP 格式");
                }

                // 3. 添加其他兼容性格式
                try
                {
                    // Shell ID List 格式
                    dataObject.SetData("Shell IDList Array", filePaths);

                    // 文件名格式
                    var fileNames = filePaths.Select(Path.GetFileName).ToArray();
                    dataObject.SetData("FileNameW", fileNames);
                    dataObject.SetData("FileName", fileNames);

                    // 设置首选拖拽效果
                    var preferredEffect = new MemoryStream(BitConverter.GetBytes((int)DragDropEffects.Copy));
                    dataObject.SetData("Preferred DropEffect", preferredEffect);

                    _logger.Info("✅ 已设置额外的兼容性格式");
                }
                catch (Exception ex)
                {
                    _logger.Warning($"设置额外格式时出现警告: {ex.Message}");
                }

                return dataObject;
            }
            catch (Exception ex)
            {
                _logger.Error($"创建拖拽数据对象失败: {ex.Message}");

                // 回退到最基本的实现
                var fallbackDataObject = new DataObject();
                fallbackDataObject.SetData(DataFormats.FileDrop, filePaths);
                return fallbackDataObject;
            }
        }

        /// <summary>
        /// 创建 CF_HDROP 格式的数据
        /// 根据 Windows API 文档实现标准的 DROPFILES 结构
        /// </summary>
        private byte[]? CreateCFHDropData(string[] filePaths)
        {
            try
            {
                // 计算所需的缓冲区大小
                // DROPFILES 结构大小 + 所有文件路径 + 终止符
                var dropFilesSize = 20; // DROPFILES 结构的大小
                var pathsSize = filePaths.Sum(path => (path.Length + 1) * 2); // Unicode 字符串
                var totalSize = dropFilesSize + pathsSize + 2; // 额外的终止符

                var buffer = new byte[totalSize];

                using (var stream = new MemoryStream(buffer))
                using (var writer = new BinaryWriter(stream))
                {
                    // 写入 DROPFILES 结构
                    writer.Write((uint)dropFilesSize);  // pFiles: 文件列表的偏移量
                    writer.Write(0);                    // pt.x: 拖拽点 X 坐标
                    writer.Write(0);                    // pt.y: 拖拽点 Y 坐标
                    writer.Write(0);                    // fNC: 坐标是否在非客户区
                    writer.Write(1);                    // fWide: 使用 Unicode 字符串

                    // 写入文件路径列表
                    foreach (var filePath in filePaths)
                    {
                        var pathBytes = System.Text.Encoding.Unicode.GetBytes(filePath);
                        writer.Write(pathBytes);
                        writer.Write((ushort)0); // 字符串终止符
                    }

                    // 写入列表终止符
                    writer.Write((ushort)0);
                }

                _logger.Info($"✅ 创建 CF_HDROP 数据成功，大小: {totalSize} 字节");
                return buffer;
            }
            catch (Exception ex)
            {
                _logger.Error($"创建 CF_HDROP 数据失败: {ex.Message}");
                return null;
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 获取项目根目录
        /// </summary>
        private string GetProjectRootDirectory()
        {
            var currentDirectory = Directory.GetCurrentDirectory();
            var projectRoot = currentDirectory;
            
            // 向上查找项目根目录（包含 .csproj 文件的目录）
            while (!string.IsNullOrEmpty(projectRoot))
            {
                if (Directory.GetFiles(projectRoot, "*.csproj").Length > 0)
                {
                    break;
                }
                
                var parent = Directory.GetParent(projectRoot);
                if (parent == null) break;
                projectRoot = parent.FullName;
            }
            
            return projectRoot;
        }

        /// <summary>
        /// 加载 DWG 文件（同步版本）
        /// </summary>
        private void LoadDwgFiles()
        {
            Task.Run(async () => await LoadDwgFilesAsync());
        }

        /// <summary>
        /// 异步加载 DWG 文件（优化版本）
        /// </summary>
        private async Task LoadDwgFilesAsync()
        {
            if (string.IsNullOrEmpty(CurrentDirectory) || !Directory.Exists(CurrentDirectory))
            {
                StatusMessage = "❌ 目录不存在";
                return;
            }

            // 取消之前的加载操作
            _loadCancellationTokenSource?.Cancel();
            _loadCancellationTokenSource = new CancellationTokenSource();
            var cancellationToken = _loadCancellationTokenSource.Token;

            IsLoading = true;
            StatusMessage = "🔄 正在扫描文件...";
            _logger.Info($"开始加载 DWG 文件: {CurrentDirectory}");

            StartPerformanceMonitor("文件加载");

            try
            {
                await Task.Run(async () =>
                {
                    var files = new List<DwgFileInfo>();
                    var searchPatterns = new[] { "*.dwg", "*.DWG" };
                    var processedCount = 0;
                    const int batchSize = 50; // 批量处理大小

                    foreach (var pattern in searchPatterns)
                    {
                        cancellationToken.ThrowIfCancellationRequested();

                        var foundFiles = Directory.GetFiles(CurrentDirectory, pattern, SearchOption.AllDirectories);
                        _logger.Info($"找到 {foundFiles.Length} 个 {pattern} 文件");

                        // 更新状态
                        Application.Current.Dispatcher.BeginInvoke(() =>
                        {
                            StatusMessage = $"🔄 正在处理 {foundFiles.Length} 个文件...";
                        });

                        for (int i = 0; i < foundFiles.Length; i += batchSize)
                        {
                            cancellationToken.ThrowIfCancellationRequested();

                            var batch = foundFiles.Skip(i).Take(batchSize);
                            var batchFiles = new List<DwgFileInfo>();

                            foreach (var filePath in batch)
                            {
                                cancellationToken.ThrowIfCancellationRequested();

                                if (files.Any(f => f.FullPath.Equals(filePath, StringComparison.OrdinalIgnoreCase)))
                                    continue;

                                try
                                {
                                    var fileInfo = new FileInfo(filePath);
                                    batchFiles.Add(new DwgFileInfo
                                    {
                                        Name = fileInfo.Name,
                                        FullPath = fileInfo.FullName,
                                        Directory = fileInfo.DirectoryName ?? "",
                                        Size = fileInfo.Length,
                                        SizeText = FormatFileSize(fileInfo.Length),
                                        CreatedDate = fileInfo.CreationTime,
                                        ModifiedDate = fileInfo.LastWriteTime,
                                        Extension = fileInfo.Extension.ToUpper()
                                    });

                                    processedCount++;
                                }
                                catch (Exception ex)
                                {
                                    _logger.Warning($"处理文件失败: {filePath}, 错误: {ex.Message}");
                                }
                            }

                            files.AddRange(batchFiles);

                            // 批量更新 UI
                            Application.Current.Dispatcher.BeginInvoke(() =>
                            {
                                StatusMessage = $"🔄 已处理 {processedCount}/{foundFiles.Length} 个文件...";
                            });

                            // 让出 CPU 时间，避免阻塞
                            await Task.Delay(1, cancellationToken);
                        }
                    }

                    cancellationToken.ThrowIfCancellationRequested();

                    // 按名称排序
                    files = files.OrderBy(f => f.Name).ToList();

                    // 更新 UI（需要在 UI 线程中执行）
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        DwgFiles.Clear();

                        // 分批添加到 UI，避免一次性添加大量项目
                        const int uiBatchSize = 100;
                        for (int i = 0; i < files.Count; i += uiBatchSize)
                        {
                            var batch = files.Skip(i).Take(uiBatchSize);
                            foreach (var file in batch)
                            {
                                DwgFiles.Add(file);
                            }
                        }

                        TotalFileCount = files.Count;
                        TotalFileSize = FormatFileSize(files.Sum(f => f.Size));

                        FilterFiles();
                    });

                }, cancellationToken);

                var elapsed = StopPerformanceMonitor("文件加载");
                LastLoadTime = $"{elapsed}ms";
                StatusMessage = $"✅ 已加载 {TotalFileCount} 个 DWG 文件 (耗时: {elapsed}ms)";
                _logger.Info($"加载 DWG 文件完成: {TotalFileCount} 个文件，耗时: {elapsed}ms");

                // 清理过期的拖拽验证缓存
                CleanupDragValidationCache();
            }
            catch (OperationCanceledException)
            {
                StopPerformanceMonitor("文件加载");
                _logger.Info("文件加载已取消");
                StatusMessage = "⏹️ 文件加载已取消";
            }
            catch (Exception ex)
            {
                StopPerformanceMonitor("文件加载");
                _logger.Error($"加载文件失败: {ex.Message}");
                StatusMessage = $"❌ 加载失败: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 初始化搜索防抖计时器
        /// </summary>
        private void InitializeSearchDebounceTimer()
        {
            _searchDebounceTimer = new System.Timers.Timer(300); // 300ms 防抖延迟
            _searchDebounceTimer.AutoReset = false;
            _searchDebounceTimer.Elapsed += (sender, e) =>
            {
                // 在 UI 线程中执行过滤
                Application.Current.Dispatcher.BeginInvoke(() =>
                {
                    FilterFilesOptimized();
                });
            };
        }

        /// <summary>
        /// 过滤文件（优化版本）
        /// </summary>
        private void FilterFiles()
        {
            FilterFilesOptimized();
        }

        /// <summary>
        /// 优化的文件过滤方法
        /// </summary>
        private void FilterFilesOptimized()
        {
            try
            {
                StartPerformanceMonitor("文件过滤");

                var filtered = string.IsNullOrWhiteSpace(SearchKeyword)
                    ? DwgFiles.ToList()
                    : DwgFiles.Where(f => f.Name.Contains(SearchKeyword, StringComparison.OrdinalIgnoreCase)).ToList();

                // 使用增量更新而不是清空重建
                UpdateFilteredFilesIncremental(filtered);

                var elapsed = StopPerformanceMonitor("文件过滤");
                LastSearchTime = $"{elapsed}ms";
            }
            catch (Exception ex)
            {
                StopPerformanceMonitor("文件过滤");
                _logger.Error($"文件过滤失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 增量更新过滤后的文件列表
        /// </summary>
        private void UpdateFilteredFilesIncremental(List<DwgFileInfo> newFilteredFiles)
        {
            // 如果差异太大，直接重建
            if (Math.Abs(FilteredFiles.Count - newFilteredFiles.Count) > FilteredFiles.Count * 0.5)
            {
                FilteredFiles.Clear();
                foreach (var file in newFilteredFiles)
                {
                    FilteredFiles.Add(file);
                }
                return;
            }

            // 增量更新：移除不在新列表中的项目
            for (int i = FilteredFiles.Count - 1; i >= 0; i--)
            {
                if (!newFilteredFiles.Contains(FilteredFiles[i]))
                {
                    FilteredFiles.RemoveAt(i);
                }
            }

            // 增量更新：添加新项目
            foreach (var file in newFilteredFiles)
            {
                if (!FilteredFiles.Contains(file))
                {
                    FilteredFiles.Add(file);
                }
            }
        }

        /// <summary>
        /// 格式化文件大小
        /// </summary>
        private static string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            
            return $"{len:0.##} {sizes[order]}";
        }

        /// <summary>
        /// 清理过期的拖拽验证缓存
        /// </summary>
        private void CleanupDragValidationCache()
        {
            var now = DateTime.Now;
            var expiredKeys = _dragValidationCache
                .Where(kvp => (now - kvp.Value.lastCheck).TotalSeconds > DRAG_VALIDATION_CACHE_SECONDS * 2)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var key in expiredKeys)
            {
                _dragValidationCache.Remove(key);
            }


        }

        #endregion

        #region 性能监控

        /// <summary>
        /// 开始性能监控
        /// </summary>
        private void StartPerformanceMonitor(string operationName)
        {
            if (_performanceMonitors.ContainsKey(operationName))
            {
                _performanceMonitors[operationName].Restart();
            }
            else
            {
                _performanceMonitors[operationName] = System.Diagnostics.Stopwatch.StartNew();
            }
        }

        /// <summary>
        /// 停止性能监控并记录结果
        /// </summary>
        private long StopPerformanceMonitor(string operationName)
        {
            if (_performanceMonitors.TryGetValue(operationName, out var stopwatch))
            {
                stopwatch.Stop();
                var elapsed = stopwatch.ElapsedMilliseconds;
                return elapsed;
            }
            return 0;
        }

        /// <summary>
        /// 获取性能统计信息
        /// </summary>
        private string GetPerformanceStats()
        {
            var stats = new List<string>();
            foreach (var kvp in _performanceMonitors)
            {
                if (kvp.Value.IsRunning)
                {
                    stats.Add($"{kvp.Key}: 运行中");
                }
                else
                {
                    stats.Add($"{kvp.Key}: {kvp.Value.ElapsedMilliseconds}ms");
                }
            }
            return string.Join(", ", stats);
        }

        #endregion

        #region IDisposable 实现

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            // 停止搜索防抖计时器
            _searchDebounceTimer?.Stop();
            _searchDebounceTimer?.Dispose();
            _searchDebounceTimer = null;

            // 取消文件加载操作
            _loadCancellationTokenSource?.Cancel();
            _loadCancellationTokenSource?.Dispose();
            _loadCancellationTokenSource = null;

            // 清理拖拽超时计时器
            _dragTimeoutTimer?.Dispose();
            _dragTimeoutTimer = null;

            // 清理性能监控器
            foreach (var monitor in _performanceMonitors.Values)
            {
                monitor?.Stop();
            }
            _performanceMonitors.Clear();

            // 清理拖拽验证缓存
            _dragValidationCache.Clear();

            _logger.Info("FileDragExampleViewModel 资源已释放");
        }

        #endregion
    }

    /// <summary>
    /// DWG 文件信息数据模型
    /// </summary>
    public partial class DwgFileInfo : ObservableObject
    {
        /// <summary>
        /// 文件名
        /// </summary>
        [ObservableProperty]
        private string name = string.Empty;

        /// <summary>
        /// 完整路径
        /// </summary>
        [ObservableProperty]
        private string fullPath = string.Empty;

        /// <summary>
        /// 目录路径
        /// </summary>
        [ObservableProperty]
        private string directory = string.Empty;

        /// <summary>
        /// 文件大小（字节）
        /// </summary>
        [ObservableProperty]
        private long size;

        /// <summary>
        /// 文件大小文本
        /// </summary>
        [ObservableProperty]
        private string sizeText = string.Empty;

        /// <summary>
        /// 创建日期
        /// </summary>
        [ObservableProperty]
        private DateTime createdDate;

        /// <summary>
        /// 修改日期
        /// </summary>
        [ObservableProperty]
        private DateTime modifiedDate;

        /// <summary>
        /// 文件扩展名
        /// </summary>
        [ObservableProperty]
        private string extension = string.Empty;

        /// <summary>
        /// 是否选中
        /// </summary>
        [ObservableProperty]
        private bool isSelected;

        /// <summary>
        /// 文件图标（根据扩展名确定）
        /// </summary>
        public string FileIcon => Extension.ToUpper() switch
        {
            ".DWG" => "📐",
            ".DXF" => "📏",
            ".DWT" => "📋",
            _ => "📄"
        };

        /// <summary>
        /// 相对路径（用于显示）
        /// </summary>
        public string RelativePath
        {
            get
            {
                if (string.IsNullOrEmpty(Directory) || string.IsNullOrEmpty(FullPath))
                    return Name;

                var dirInfo = new DirectoryInfo(Directory);
                return Path.Combine(dirInfo.Name, Name);
            }
        }

        /// <summary>
        /// 文件描述
        /// </summary>
        public string Description => $"{SizeText} • {ModifiedDate:yyyy-MM-dd HH:mm}";

        public override string ToString() => Name;
    }
}
