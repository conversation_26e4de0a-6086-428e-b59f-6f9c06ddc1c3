#region Using 引用
using System.IO;
using Microsoft.Extensions.Logging;
using WPFTest.Models.DWG;
using Zylo.YData;
#endregion

namespace WPFTest.Services.DWG;

/// <summary>
/// DWG 文件类型服务实现 - 基于 Zylo.YData 的智能文件类型管理
/// </summary>
/// <remarks>
/// 🎯 核心功能：
/// - 智能文件类型分析：根据文件名前缀自动识别DWG文件类型
/// - 完整的CRUD操作：支持文件类型的增删改查管理
/// - 可配置的规则引擎：用户可自定义文件类型和前缀规则
/// - 数据库管理：自动初始化、重置和状态监控
///
/// 📊 数据管理：
/// - 使用YData ORM进行SQLite数据持久化
/// - CodeFirst模式自动创建和维护数据库结构
/// - 支持默认数据的自动初始化和重置
/// - 提供完整的数据库状态信息查询
///
/// 🔍 智能分析算法：
/// 1. 前缀匹配：根据文件名前缀（如"GS_", "JT_"）识别类型
/// 2. 模糊匹配：支持下划线和横线的前缀变体
/// 3. 优先级排序：按SortOrder和匹配度排序结果
/// 4. 缓存优化：缓存常用查询结果提升性能
///
/// 📋 默认文件类型：
/// - 📁 全部：通用文件类型，匹配所有文件
/// - 📋 出图：最终出图文件，前缀"出图_", "CT_"
/// - 🏗️ 施工图：施工图纸，前缀"GS_", "施工_"
/// - 📐 模板图：图纸模板，前缀"模板_", "MB_"
/// - 🗺️ 底图：底图文件，前缀"底图_", "DT_"
/// - 🔄 变更：变更图纸，前缀"变更_", "BG_"
/// - 📊 计算书：计算文档，前缀"计算_", "JS_"
/// - 🖼️ 图框：图框模板，前缀"图框_", "TK_"
/// - 🏢 建筑图：建筑专业，前缀"JT_", "建筑_"
/// - 🔗 绑定：绑定参考，前缀"绑定_", "BD_"
/// - 📄 其他：未分类文件，兜底类型
///
/// 🔄 事件机制：
/// - FileTypeChanged事件：文件类型增删改时触发
/// - 事件参数包含操作类型（Added/Updated/Deleted）和相关数据
/// - 支持多个订阅者同时监听数据变更
///
/// 🎨 设计模式：
/// - 服务模式：封装业务逻辑，提供统一的服务接口
/// - 单例模式：YData配置确保全局唯一
/// - 事件驱动：通过事件解耦组件间的依赖关系
/// - 异步模式：所有数据操作都采用异步方式
/// - 工厂模式：CreateDefaultTypes()创建默认数据
///
/// 🛡️ 错误处理：
/// - 完整的异常捕获和日志记录
/// - 数据库操作失败时的回滚机制
/// - 网络和IO异常的优雅处理
/// - 提供详细的错误信息和调试日志
///
/// 🔧 技术特点：
/// - 基于Zylo.YData ORM框架
/// - 支持SQLite数据库
/// - 异步优先的API设计
/// - 完整的日志记录系统
/// - 支持依赖注入和单元测试
/// - 实现IDisposable进行资源管理
///
/// 💡 使用示例：
/// ```csharp
/// // 智能分析文件类型
/// var fileType = await service.AnalyzeFileTypeAsync("GS_结构平面图.dwg");
///
/// // 添加自定义文件类型
/// var customType = new DwgFileTypeModel
/// {
///     EnglishName = "CUSTOM",
///     ChineseName = "自定义类型",
///     Prefixes = "自定义_,CUSTOM_"
/// };
/// await service.AddFileTypeAsync(customType);
/// ```
/// </remarks>
public class DwgFileTypeService : IDwgFileTypeService
{
    #region 私有字段

    /// <summary>
    /// 日志记录器
    /// </summary>
    /// <remarks>
    /// 用于记录服务操作的详细日志
    /// 包括成功操作、警告和错误信息
    /// </remarks>
    private readonly ILogger<DwgFileTypeService>? _logger;

    /// <summary>
    /// YData 配置状态标志
    /// </summary>
    /// <remarks>
    /// 静态字段，确保 YData 只配置一次
    /// 避免重复配置导致的问题
    /// </remarks>
    private static bool _isConfigured = false;

    #endregion

    #region 构造函数和初始化

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器（可选）</param>
    /// <remarks>
    /// 初始化逻辑：
    /// 1. 保存日志记录器引用
    /// 2. 检查并配置 YData（单例模式）
    /// 3. 记录服务初始化完成日志
    ///
    /// 注意：YData 配置采用静态标志位确保只配置一次
    /// </remarks>
    public DwgFileTypeService(ILogger<DwgFileTypeService>? logger = null)
    {
        _logger = logger;

        if (!_isConfigured)
        {
            ConfigureYData();
            _isConfigured = true;
        }

        _logger?.LogInformation("📋 DWG 文件类型服务初始化完成");
    }

    #endregion

    #region 公共事件

    /// <summary>
    /// 文件类型数据变更事件
    /// </summary>
    /// <remarks>
    /// 在文件类型增删改操作成功后触发
    /// 用于通知 UI 更新或缓存失效
    /// </remarks>
    public event EventHandler<IDwgFileTypeService.FileTypeChangedEventArgs>? FileTypeChanged;

    #endregion

    #region YData 数据库配置

    /// <summary>
    /// 配置 YData 数据库连接
    /// </summary>
    /// <remarks>
    /// 配置逻辑：
    /// 1. 确定应用程序数据目录
    /// 2. 创建 Data 子目录（如果不存在）
    /// 3. 构建 SQLite 数据库文件路径
    /// 4. 配置 YData 连接字符串
    /// 5. 启用共享缓存模式
    ///
    /// 数据库位置：{AppDirectory}\Data\DwgManager.db
    /// 连接模式：共享缓存，支持多线程访问
    ///
    /// 异常处理：配置失败时抛出异常，阻止服务启动
    /// </remarks>
    private void ConfigureYData()
    {
        try
        {
            // 确定数据库存储路径
            var appDirectory = AppDomain.CurrentDomain.BaseDirectory; // 软件运行目录
            var dbDirectory = Path.Combine(appDirectory, "Data");
            Directory.CreateDirectory(dbDirectory);
            var dbPath = Path.Combine(dbDirectory, "DwgManager.db");

            _logger?.LogInformation($"🗄️ 配置 SQLite 数据库路径: {dbPath}");

            // 配置 SQLite 连接字符串
            var connectionString = $"Data Source={dbPath};Cache=Shared;";
            YData.ConfigureAuto(connectionString, YDataType.Sqlite);

            _logger?.LogInformation("✅ YData 配置完成");
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "❌ YData 配置失败");
            throw;
        }
    }

    #endregion

    #region 数据库初始化

    /// <summary>
    /// 初始化数据库
    /// </summary>
    /// <returns>初始化是否成功</returns>
    /// <remarks>
    /// 初始化步骤：
    /// 1. 同步数据库表结构（CodeFirst 模式）
    /// 2. 检查数据库是否为空
    /// 3. 如果为空，插入默认文件类型数据
    /// 4. 记录初始化结果
    ///
    /// 默认数据包括：
    /// - 📁 全部、📋 出图、🏗️ 施工图、📐 模板图
    /// - 🗺️ 底图、🔄 变更、📊 计算书、🖼️ 图框
    /// - 🏢 建筑图、🔗 绑定、📄 其他
    ///
    /// 异常处理：捕获所有异常并记录日志，返回 false
    /// </remarks>
    public async Task<bool> InitializeDatabaseAsync()
    {
        try
        {
            _logger?.LogInformation("🔧 开始初始化 DWG 文件类型数据库...");

            // 确保数据库表结构存在
            YData.FreeSql.CodeFirst.SyncStructure<DwgFileTypeModel>();
            _logger?.LogInformation("✅ 数据库表结构同步完成");

            var existingTypes = await YData.GetAllAsync<DwgFileTypeModel>();
            _logger?.LogInformation($"📊 数据库中现有 {existingTypes.Count} 个文件类型");

            if (existingTypes.Count == 0)
            {
                _logger?.LogInformation("📝 数据库为空，正在插入默认数据...");
                var defaultTypes = DwgFileTypeModel.CreateDefaultTypes();
                var insertResult = await YData.Insert<DwgFileTypeModel>().AppendData(defaultTypes).ExecuteAffrowsAsync();
                _logger?.LogInformation($"✅ 成功插入 {insertResult} 个默认文件类型");
            }

            _logger?.LogInformation("🎉 数据库初始化完成");
            return true;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "❌ 数据库初始化失败");
            return false;
        }
    }

    #endregion

    #region 查询操作

    /// <summary>
    /// 获取所有文件类型 - 按SortOrder排序返回
    /// </summary>
    /// <param name="includeDisabled">是否包含禁用的文件类型，默认false只返回启用的类型</param>
    /// <returns>按SortOrder排序的文件类型列表</returns>
    /// <remarks>
    /// 🎯 核心功能：
    /// - 返回所有可用的文件类型配置
    /// - 支持过滤已禁用的类型（IsEnabled = false）
    /// - 自动按SortOrder和中文名称排序
    ///
    /// 📊 排序规则（重要）：
    /// 1. 🥇 首先按SortOrder升序排列（0, 1, 2, 3...）
    /// 2. 🥈 相同SortOrder时按ChineseName字母顺序排列
    /// 3. 🎯 确保UI显示顺序与数据库配置一致
    ///
    /// 💡 使用场景：
    /// - UI列表显示：按优先级顺序展示文件类型
    /// - 文件分析：按优先级匹配文件类型
    /// - 配置管理：维护文件类型的显示顺序
    ///
    /// ⚡ 性能优化：
    /// - 使用YData的查询优化和索引
    /// - 内存排序比数据库排序更高效
    /// - 支持条件查询减少数据传输
    /// </remarks>
    public async Task<IList<DwgFileTypeModel>> GetAllFileTypesAsync(bool includeDisabled = false)
    {
        try
        {
            _logger?.LogDebug($"🔍 开始获取文件类型列表，包含禁用项: {includeDisabled}");

            // 根据参数决定查询策略
            var result = includeDisabled
                ? await YData.GetAllAsync<DwgFileTypeModel>()  // 获取所有记录（包含禁用的）
                : await YData.Select<DwgFileTypeModel>()       // 只获取启用的记录
                    .Where(ft => ft.IsEnabled)
                    .ToListAsync();

            // 🎯 关键排序：按SortOrder升序，然后按中文名称排序
            var sortedResult = result
                .OrderBy(ft => ft.SortOrder)        // 主排序：按优先级排序
                .ThenBy(ft => ft.ChineseName)       // 次排序：按名称排序
                .ToList();

            _logger?.LogInformation($"✅ 获取文件类型完成: {sortedResult.Count} 个类型，按SortOrder排序");

            // 记录排序后的顺序（调试用）
            if (_logger?.IsEnabled(Microsoft.Extensions.Logging.LogLevel.Debug) == true)
            {
                foreach (var ft in sortedResult.Take(5)) // 只记录前5个
                {
                    _logger.LogDebug($"   📋 {ft.SortOrder:D2}: {ft.ChineseName} ({ft.EnglishName})");
                }
                if (sortedResult.Count > 5)
                {
                    _logger.LogDebug($"   ... 还有 {sortedResult.Count - 5} 个类型");
                }
            }

            return sortedResult;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "❌ 获取文件类型列表失败");
            throw;
        }
    }

    /// <summary>
    /// 根据ID获取文件类型
    /// </summary>
    /// <param name="id">文件类型ID</param>
    /// <returns>文件类型模型，如果不存在返回 null</returns>
    /// <remarks>
    /// 使用 YData 的主键查询，性能最优
    /// 常用于编辑、删除操作的前置查询
    /// </remarks>
    public async Task<DwgFileTypeModel?> GetFileTypeByIdAsync(int id)
    {
        return await YData.GetAsync<DwgFileTypeModel>(id);
    }

    /// <summary>
    /// 根据英文名称获取文件类型
    /// </summary>
    /// <param name="englishName">英文名称</param>
    /// <returns>文件类型模型，如果不存在返回 null</returns>
    /// <remarks>
    /// 使用英文名称进行精确匹配查询
    /// 常用于检查英文名称重复
    /// 英文名称是唯一标识，不允许重复
    /// </remarks>
    public async Task<DwgFileTypeModel?> GetFileTypeByEnglishNameAsync(string englishName)
    {
        return await YData.Select<DwgFileTypeModel>().Where(ft => ft.EnglishName == englishName).FirstAsync();
    }

    #endregion

    #region 智能分析

    /// <summary>
    /// 根据文件名分析文件类型（核心业务功能）
    /// </summary>
    /// <param name="fileName">文件名（不含路径）</param>
    /// <returns>匹配的文件类型，如果无匹配返回"其他"类型</returns>
    /// <remarks>
    /// 分析逻辑：
    /// 1. 获取所有启用的文件类型
    /// 2. 调用 DwgFileTypeModel.FindByFileName 进行智能匹配
    /// 3. 返回匹配结果
    ///
    /// 核心价值：
    /// - 替换 DwgManagerTabView 中的硬编码分析逻辑
    /// - 支持用户自定义文件类型规则
    /// - 提供统一的文件类型分析入口
    ///
    /// 使用场景：
    /// - 文件导入时自动分类
    /// - 批量文件处理
    /// - 文件列表显示
    /// </remarks>
    public async Task<DwgFileTypeModel> AnalyzeFileTypeAsync(string fileName)
    {
        var fileTypes = await GetAllFileTypesAsync(includeDisabled: false);
        return DwgFileTypeModel.FindByFileName(fileName, fileTypes);
    }

    #endregion

    #region CRUD操作 - 增加(Create)

    /// <summary>
    /// 添加新的文件类型
    /// </summary>
    /// <param name="fileType">要添加的文件类型</param>
    /// <returns>是否添加成功</returns>
    /// <remarks>
    /// 🎯 功能说明：
    /// - 自动设置创建和更新时间
    /// - 插入数据库并返回新ID
    /// - 触发FileTypeChanged事件通知订阅者
    /// - 记录详细的操作日志
    ///
    /// ⚠️ 注意事项：
    /// - 确保EnglishName唯一性
    /// - 验证必填字段完整性
    /// - 处理数据库约束异常
    /// </remarks>
    public async Task<bool> AddFileTypeAsync(DwgFileTypeModel fileType)
    {
        try
        {
            _logger?.LogInformation($"➕ 正在添加文件类型: {fileType.ChineseName} ({fileType.EnglishName})");

            fileType.CreatedAt = DateTime.Now;
            fileType.UpdatedAt = DateTime.Now;
            var result = await YData.InsertAsync(fileType);

            if (result > 0)
            {
                _logger?.LogInformation($"✅ 成功添加文件类型，ID: {result}");
                FileTypeChanged?.Invoke(this, new IDwgFileTypeService.FileTypeChangedEventArgs(IDwgFileTypeService.FileTypeChangeType.Added, fileType));
            }
            else
            {
                _logger?.LogWarning("⚠️ 添加文件类型失败，返回值为 0");
            }

            return result > 0;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, $"❌ 添加文件类型失败: {fileType.ChineseName}");
            return false;
        }
    }

    #endregion

    #region CRUD操作 - 修改(Update)

    /// <summary>
    /// 更新现有文件类型
    /// </summary>
    /// <param name="fileType">要更新的文件类型（必须包含有效的ID）</param>
    /// <returns>是否更新成功</returns>
    /// <remarks>
    /// 🎯 功能说明：
    /// - 自动更新UpdatedAt时间戳
    /// - 根据ID更新数据库记录
    /// - 触发FileTypeChanged事件通知订阅者
    /// - 返回影响的行数判断成功与否
    ///
    /// ⚠️ 注意事项：
    /// - 确保fileType.Id有效且存在
    /// - 验证数据完整性和约束
    /// - 处理并发更新冲突
    /// </remarks>
    public async Task<bool> UpdateFileTypeAsync(DwgFileTypeModel fileType)
    {
        try
        {
            _logger?.LogInformation($"✏️ 正在更新文件类型: {fileType.ChineseName} (ID: {fileType.Id})");

            fileType.UpdatedAt = DateTime.Now;
            var result = await YData.UpdateAsync(fileType);

            if (result > 0)
            {
                _logger?.LogInformation($"✅ 成功更新文件类型，影响行数: {result}");
                FileTypeChanged?.Invoke(this, new IDwgFileTypeService.FileTypeChangedEventArgs(IDwgFileTypeService.FileTypeChangeType.Updated, fileType));
            }
            else
            {
                _logger?.LogWarning($"⚠️ 更新文件类型失败，影响行数为 0，ID: {fileType.Id}");
            }

            return result > 0;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, $"❌ 更新文件类型失败: {fileType.ChineseName} (ID: {fileType.Id})");
            return false;
        }
    }

    #endregion

    #region CRUD操作 - 删除(Delete)

    /// <summary>
    /// 删除文件类型
    /// </summary>
    /// <param name="id">要删除的文件类型ID</param>
    /// <returns>是否删除成功</returns>
    /// <remarks>
    /// 🎯 功能说明：
    /// - 检查文件类型是否存在
    /// - 保护系统预设类型（All、Other）不被删除
    /// - 从数据库中物理删除记录
    /// - 触发FileTypeChanged事件通知订阅者
    ///
    /// 🛡️ 安全保护：
    /// - 禁止删除"All"和"Other"系统类型
    /// - 验证ID有效性
    /// - 处理外键约束异常
    /// </remarks>
    public async Task<bool> DeleteFileTypeAsync(int id)
    {
        try
        {
            var fileType = await GetFileTypeByIdAsync(id);
            if (fileType == null || fileType.EnglishName == "All" || fileType.EnglishName == "Other")
                return false;

            var result = await YData.DeleteAsync<DwgFileTypeModel>(id);
            if (result > 0)
            {
                FileTypeChanged?.Invoke(this, new IDwgFileTypeService.FileTypeChangedEventArgs(IDwgFileTypeService.FileTypeChangeType.Deleted, fileType));
            }
            return result > 0;
        }
        catch
        {
            return false;
        }
    }

    #endregion

    #region CRUD操作 - 辅助操作(Helper)

    /// <summary>
    /// 设置文件类型的启用状态
    /// </summary>
    /// <param name="id">文件类型ID</param>
    /// <param name="enabled">是否启用</param>
    /// <returns>是否设置成功</returns>
    /// <remarks>
    /// 🎯 功能说明：
    /// - 快速切换文件类型的启用/禁用状态
    /// - 自动更新UpdatedAt时间戳
    /// - 使用高效的批量更新操作
    /// - 不触发完整的更新事件（轻量级操作）
    ///
    /// 💡 使用场景：
    /// - 临时禁用某个文件类型
    /// - 批量管理文件类型状态
    /// - UI开关控件的快速响应
    /// </remarks>
    public async Task<bool> SetFileTypeEnabledAsync(int id, bool enabled)
    {
        try
        {
            var result = await YData.Update<DwgFileTypeModel>()
                .Set(ft => ft.IsEnabled, enabled)
                .Set(ft => ft.UpdatedAt, DateTime.Now)
                .Where(ft => ft.Id == id)
                .ExecuteAffrowsAsync();
            return result > 0;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 批量更新文件类型的排序顺序
    /// </summary>
    /// <param name="sortOrders">文件类型ID和新排序值的字典</param>
    /// <returns>是否更新成功</returns>
    /// <remarks>
    /// 🎯 功能说明：
    /// - 批量更新多个文件类型的SortOrder
    /// - 自动更新UpdatedAt时间戳
    /// - 使用事务确保数据一致性
    /// - 支持拖拽排序等UI操作
    ///
    /// 💡 使用场景：
    /// - 用户拖拽调整文件类型顺序
    /// - 批量重新排序文件类型
    /// - 导入配置时的顺序设置
    ///
    /// ⚡ 性能优化：
    /// - 使用批量更新减少数据库交互
    /// - 只更新变化的记录
    /// </remarks>
    public async Task<bool> UpdateSortOrdersAsync(Dictionary<int, int> sortOrders)
    {
        try
        {
            foreach (var kvp in sortOrders)
            {
                await YData.Update<DwgFileTypeModel>()
                    .Set(ft => ft.SortOrder, kvp.Value)
                    .Set(ft => ft.UpdatedAt, DateTime.Now)
                    .Where(ft => ft.Id == kvp.Key)
                    .ExecuteAffrowsAsync();
            }
            return true;
        }
        catch
        {
            return false;
        }
    }

    #endregion

    #region 数据库管理

    /// <summary>
    /// 重置为默认文件类型
    /// </summary>
    /// <returns>是否重置成功</returns>
    /// <remarks>
    /// 🎯 功能说明：
    /// - 清空现有所有文件类型数据
    /// - 重新插入系统默认文件类型
    /// - 恢复初始的排序和配置
    /// - 用于系统重置或故障恢复
    ///
    /// ⚠️ 危险操作：
    /// - 会删除所有用户自定义的文件类型
    /// - 不可逆操作，建议提前备份
    /// - 建议在维护模式下执行
    /// </remarks>
    public async Task<bool> ResetToDefaultAsync()
    {
        try
        {
            await YData.Delete<DwgFileTypeModel>().Where("1=1").ExecuteAffrowsAsync();
            var defaultTypes = DwgFileTypeModel.CreateDefaultTypes();
            await YData.Insert<DwgFileTypeModel>().AppendData(defaultTypes).ExecuteAffrowsAsync();
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 获取数据库状态信息
    /// </summary>
    /// <returns>数据库信息对象</returns>
    /// <remarks>
    /// 🎯 功能说明：
    /// - 统计文件类型总数
    /// - 检查数据库初始化状态
    /// - 提供数据库基本信息
    /// - 用于系统监控和诊断
    ///
    /// 📊 返回信息：
    /// - DatabasePath: 数据库路径
    /// - FileTypeCount: 文件类型总数
    /// - IsInitialized: 是否已初始化
    /// - LastModified: 最后修改时间
    /// - FileSize: 数据库文件大小
    /// </remarks>
    public async Task<IDwgFileTypeService.DatabaseInfo> GetDatabaseInfoAsync()
    {
        var count = await YData.Select<DwgFileTypeModel>().CountAsync();
        return new IDwgFileTypeService.DatabaseInfo
        {
            DatabasePath = "SQLite Database",
            FileTypeCount = (int)count,
            IsInitialized = count > 0,
            LastModified = DateTime.Now,
            FileSize = 0
        };
    }

    #endregion

    #region 资源管理

    /// <summary>
    /// 释放资源
    /// </summary>
    /// <remarks>
    /// 🎯 功能说明：
    /// - 实现IDisposable接口
    /// - YData资源由框架自动管理
    /// - 当前实现为空，预留扩展点
    ///
    /// 💡 设计考虑：
    /// - 支持依赖注入容器的生命周期管理
    /// - 为未来可能的资源清理预留接口
    /// - 遵循.NET资源管理最佳实践
    /// </remarks>
    public void Dispose()
    {
        // YData资源由框架管理，无需手动释放
        // 预留扩展点，用于未来可能的资源清理
    }

    #endregion
}
