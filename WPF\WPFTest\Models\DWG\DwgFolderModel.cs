using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using CommunityToolkit.Mvvm.ComponentModel;
using Zylo.YData;

namespace WPFTest.Models.DWG;

/// <summary>
/// DWG 文件夹模型 - 专业文件夹管理的核心数据模型
/// </summary>
/// <remarks>
/// 🎯 核心功能：
/// - 定义DWG项目文件夹的完整数据结构
/// - 支持专业文件夹的标准化管理
/// - 提供可视化的图标和名称支持
/// - 实现SQLite数据库的持久化存储
///
/// 📊 数据字段说明：
/// - Id: 主键，自增整数，唯一标识文件夹
/// - Name: 文件夹名称，用于显示和文件系统操作
/// - Icon: 显示图标，支持Emoji和Unicode字符
/// - IsDefault: 默认标志，标识系统预设的专业文件夹
/// - SortOrder: 排序权重，控制显示顺序
/// - IsEnabled: 启用状态，控制是否显示和使用
/// - CreatedAt/UpdatedAt: 时间戳，记录创建和更新时间
///
/// 📁 默认专业文件夹：
/// - 🏗️ 结构：结构专业图纸文件夹
/// - 🏢 建筑：建筑专业图纸文件夹
/// - 🌡️ 暖通：暖通空调专业文件夹
/// - 💧 给排水：给排水专业文件夹
/// - ⚡ 电气：电气专业文件夹
/// - 🌳 园林：园林景观专业文件夹
///
/// 🎨 设计模式：
/// - 数据模型：纯数据承载，无业务逻辑
/// - 观察者模式：继承ObservableValidator支持属性变更通知
/// - 验证模式：使用DataAnnotations进行数据验证
/// - ORM映射：通过Table特性映射到数据库表
///
/// 🛡️ 数据验证：
/// - Required: 必填字段验证（Name字段）
/// - MaxLength: 字符串长度限制
/// - 自动时间戳：PropertyChanged事件自动更新UpdatedAt
///
/// 🔧 技术特点：
/// - 使用CommunityToolkit.Mvvm的ObservableProperty
/// - 支持Entity Framework Core的CodeFirst模式
/// - 实现INotifyPropertyChanged接口
/// - 提供Clone()方法支持对象复制
/// - 静态工厂方法CreateDefaultFolders()创建默认数据
/// - DisplayText计算属性提供格式化显示文本
///
/// 💡 使用示例：
/// ```csharp
/// // 创建自定义文件夹
/// var customFolder = new DwgFolderModel("自定义专业", "🔧", false)
/// {
///     SortOrder = 100,
///     IsEnabled = true
/// };
///
/// // 克隆对象用于编辑
/// var editCopy = customFolder.Clone();
///
/// // 获取显示文本
/// string displayText = customFolder.DisplayText; // "🔧 自定义专业"
/// ```
/// </remarks>
[Table("DwgFolders")]
public partial class DwgFolderModel : ObservableValidator
{
    /// <summary>
    /// 主键ID（自增）
    /// </summary>
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    [ObservableProperty]
    [Description("主键ID（自增）")]
    public partial int Id { get; set; }

    /// <summary>
    /// 文件夹名称
    /// </summary>
    [ObservableProperty]
    [Required]
    [MaxLength(50)]
    [Description("文件夹名称")]
    public partial string Name { get; set; } = string.Empty;

    /// <summary>
    /// 图标
    /// </summary>
    [ObservableProperty]
    [MaxLength(10)]
    [Description("图标")]
    public partial string Icon { get; set; } = "📁";

    /// <summary>
    /// 是否为默认文件夹
    /// </summary>
    [ObservableProperty]
    [Description("是否为默认文件夹")]
    public partial bool IsDefault { get; set; } = true;

    /// <summary>
    /// 排序权重
    /// </summary>
    [ObservableProperty]
    [Description("排序权重")]
    public partial int SortOrder { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    [ObservableProperty]
    [Description("是否启用")]
    public partial bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 创建时间
    /// </summary>
    [ObservableProperty]
    [Description("创建时间")]
    public partial DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 更新时间
    /// </summary>
    [ObservableProperty]
    [Description("更新时间")]
    public partial DateTime UpdatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 是否被选中（用于UI显示，不存储到数据库）
    /// </summary>
    [ObservableProperty]
    [NotMapped]
    [Description("是否被选中（用于UI显示，不存储到数据库）")]
    public partial bool IsSelected { get; set; }

    /// <summary>
    /// 显示文本
    /// </summary>
    public string DisplayText => $"{Icon} {Name}";

    /// <summary>
    /// 构造函数
    /// </summary>
    public DwgFolderModel()
    {
        // 监听属性变化以自动更新 UpdatedAt
        PropertyChanged += (sender, e) =>
        {
            if (e.PropertyName != nameof(UpdatedAt))
            {
                UpdatedAt = DateTime.Now;
            }
        };
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    public DwgFolderModel(string name, string icon = "📁", bool isDefault = false)
    {
        Name = name;

        Icon = icon;
        IsDefault = isDefault;
        SortOrder = 999; // 默认排序值，新建的文件夹排在最后
    }

    /// <summary>
    /// 创建默认文件夹组合
    /// </summary>
    public static List<DwgFolderModel> CreateDefaultFolders()
    {
        var folders = new List<DwgFolderModel>
        {
            new("结构", "🏗️", true),
            new("建筑", "🏢", true),
            new("暖通", "🌡️", true),
            new("排水", "💧", true),
            new("电气", "⚡", true),
            new("审图版", "🌳", true),
        };

        // 设置排序顺序
        for (int i = 0; i < folders.Count; i++)
        {
            folders[i].SortOrder = i;
        }

        return folders;
    }

    /// <summary>
    /// 克隆当前对象
    /// </summary>
    /// <returns>克隆的对象</returns>
    /// <remarks>
    /// 用于编辑时创建副本，避免直接修改原对象
    /// 保持 ID 以便正确执行更新操作
    /// </remarks>
    public DwgFolderModel Clone()
    {
        return new DwgFolderModel(Name, Icon, IsDefault)
        {
            Id = Id, // 保持原始 ID，用于更新操作
            SortOrder = SortOrder,
            IsEnabled = IsEnabled,
            CreatedAt = CreatedAt,
            UpdatedAt = UpdatedAt
        };
    }
}
