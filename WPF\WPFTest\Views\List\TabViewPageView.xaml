<UserControl x:Class="WPFTest.Views.List.TabViewPageView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:converters="clr-namespace:Zylo.WPF.Converters;assembly=Zylo.WPF"
             xmlns:viewmodels="clr-namespace:WPFTest.ViewModels.List"
             xmlns:mvvm="http://prismlibrary.com/"
             d:DataContext="{d:DesignInstance Type=viewmodels:TabViewPageViewModel}"
             mc:Ignorable="d"
             mvvm:ViewModelLocator.AutoWireViewModel="True"
             d:DesignHeight="1000" d:DesignWidth="1200">

    <UserControl.Resources>
        <!-- 🔧 转换器资源 -->
        <converters:SimpleIconConverter x:Key="SimpleIconConverter" />
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- 🎯 标题和描述 -->
        <StackPanel Grid.Row="0" Margin="24,24,24,16">
            <TextBlock Text="TabView 完整功能演示"
                       FontSize="28"
                       FontWeight="SemiBold"
                       Foreground="{DynamicResource TextFillColorPrimaryBrush}" />
            <TextBlock Text="参考TreeViewPageViewModel成功模式，支持四种图标类型、搜索过滤、动态添加/关闭标签页"
                       FontSize="14"
                       Margin="0,8,0,0"
                       Foreground="{DynamicResource TextFillColorSecondaryBrush}" />
        </StackPanel>

        <!-- 🔧 工具栏 -->
        <Border Grid.Row="1" 
                Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                CornerRadius="8"
                Padding="16"
                Margin="24,0,24,16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <!-- 左侧：搜索和操作按钮 -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <!-- 🔍 搜索框 -->
                    <ui:TextBox PlaceholderText="搜索标签页..."
                                Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                                Icon="{ui:SymbolIcon Search20}"
                                Width="200"
                                Margin="0,0,16,0" />

                    <!-- 🔄 重新加载 -->
                    <ui:Button Content="🔄 重新加载"
                               Command="{Binding ReloadDataCommand}"
                               Icon="{ui:SymbolIcon ArrowClockwise20}"
                               IsEnabled="{Binding CanReloadData}"
                               Margin="0,0,8,0" />

                    <!-- ➕ 添加标签页 -->
                    <ui:Button Content="➕ 添加标签页"
                               Command="{Binding AddTabCommand}"
                               Icon="{ui:SymbolIcon Add20}"
                               IsEnabled="{Binding CanPerformActions}"
                               Appearance="Primary"
                               Margin="0,0,8,0" />
                </StackPanel>

                <!-- 右侧：状态信息 -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <!-- 加载指示器 -->
                    <ui:ProgressRing IsIndeterminate="True"
                                     Width="20"
                                     Height="20"
                                     Margin="0,0,8,0"
                                     Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}" />
                    
                    <!-- 标签页统计 -->
                    <TextBlock Text="{Binding TabItemsInfo}"
                               VerticalAlignment="Center"
                               FontSize="12"
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}" />
                </StackPanel>
            </Grid>
        </Border>

        <!-- 🎨 TabView 演示区域 -->
        <ui:Card Grid.Row="2" Margin="24,0,24,16" Padding="16">
            <!-- 🔍 调试信息 -->
            <StackPanel>
                <TextBlock Text="{Binding StatusMessage, StringFormat='状态: {0}'}"
                           Margin="0,0,0,8"
                           FontWeight="Bold"
                           Foreground="{DynamicResource TextFillColorPrimaryBrush}" />
                <TextBlock Text="{Binding FilteredTabItems.Count, StringFormat='标签页数量: {0}'}"
                           Margin="0,0,0,16"
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}" />

                <!-- 🔄 TabView 控制选项 -->
                <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                    <!-- 布局切换 -->
                    <TextBlock Text="TabView 布局:"
                               VerticalAlignment="Center"
                               Margin="0,0,8,0"
                               FontWeight="SemiBold" />
                    <RadioButton x:Name="HorizontalTabRadio"
                                 Content="水平标签"
                                 IsChecked="True"
                                 Margin="0,0,16,0" />
                    <RadioButton x:Name="VerticalTabRadio"
                                 Content="竖向标签"
                                 Margin="0,0,32,0" />

                    <!-- 关闭功能开关 -->
                    <TextBlock Text="标签页功能:"
                               VerticalAlignment="Center"
                               Margin="0,0,8,0"
                               FontWeight="SemiBold" />
                    <CheckBox x:Name="EnableCloseButtonCheckBox"
                              Content="显示关闭按钮"
                              IsChecked="True"
                              Margin="0,0,16,0"
                              VerticalAlignment="Center" />
                    <CheckBox x:Name="EnableTabDragCheckBox"
                              Content="允许拖拽排序"
                              IsChecked="False"
                              Margin="0,0,16,0"
                              VerticalAlignment="Center" />
                </StackPanel>

                <!-- 💡 水平标签的 TabView -->
                <ui:TabView x:Name="HorizontalTabView"
                            ItemsSource="{Binding FilteredTabItems}"
                            SelectedItem="{Binding SelectedTab}"
                            Visibility="{Binding IsChecked, ElementName=HorizontalTabRadio, Converter={StaticResource BooleanToVisibilityConverter}}">

                    <ui:TabView.ItemTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <ContentPresenter Width="16" Height="16" Margin="0,0,6,0" VerticalAlignment="Center">
                                    <ContentPresenter.Content>
                                        <MultiBinding>
                                            <MultiBinding.Converter>
                                                <StaticResource ResourceKey="SimpleIconConverter" />
                                            </MultiBinding.Converter>
                                            <Binding Path="WpfUiSymbol" />
                                            <Binding Path="ZyloSymbol" />
                                            <Binding Path="Emoji" />
                                            <Binding Path="DefaultIcon" />
                                        </MultiBinding>
                                    </ContentPresenter.Content>
                                </ContentPresenter>
                                <TextBlock Text="{Binding Header}"
                                           VerticalAlignment="Center"
                                           Foreground="{DynamicResource TextFillColorPrimaryBrush}" />
                            </StackPanel>
                        </DataTemplate>
                    </ui:TabView.ItemTemplate>

                    <!-- 标签页内容模板 -->
                    <ui:TabView.ContentTemplate >
                        <DataTemplate     DataType="{x:Type viewmodels:TabItemData}">
                            <ScrollViewer VerticalScrollBarVisibility="Auto"
                                          Background="{DynamicResource ApplicationBackgroundBrush}">
                                <ui:Card Margin="8" Padding="20"
                                         Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                                         BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                         BorderThickness="1">
                                    <StackPanel>
                                        <!-- 标题区域 -->
                                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                                            <!-- 大图标 -->
                                            <ContentPresenter Width="35"
                                                              Height="35"
                                                              Margin="0,0,12,0"
                                                              VerticalAlignment="Center">
                                                <ContentPresenter.Content>
                                                    <MultiBinding>
                                                        <MultiBinding.Converter>
                                                            <StaticResource ResourceKey="SimpleIconConverter" />
                                                        </MultiBinding.Converter>
                                                        <Binding Path="WpfUiSymbol" />
                                                        <Binding Path="ZyloSymbol" />
                                                        <Binding Path="Emoji" />
                                                        <Binding Path="DefaultIcon" />
                                                    </MultiBinding>
                                                </ContentPresenter.Content>
                                            </ContentPresenter>

                                            <StackPanel>
                                                <!-- 标题 -->
                                                <TextBlock Text="{Binding Header}"
                                                           FontSize="20"
                                                           FontWeight="SemiBold"
                                                           Foreground="{DynamicResource TextFillColorPrimaryBrush}" />

                                                <!-- 类型标签 -->
                                                <Border Background="{DynamicResource AccentFillColorDefaultBrush}"
                                                        CornerRadius="12"
                                                        Padding="8,4"
                                                        Margin="0,4,0,0"
                                                        HorizontalAlignment="Left">
                                                    <TextBlock Text="{Binding NodeType}"
                                                               FontSize="12"
                                                               FontWeight="Medium"
                                                               Foreground="White" />
                                                </Border>
                                            </StackPanel>
                                        </StackPanel>

                                        <!-- 内容区域 -->
                                        <TextBlock Text="{Binding Content}"
                                                   FontSize="14"
                                                   LineHeight="22"
                                                   TextWrapping="Wrap"
                                                   Margin="0,0,0,16"
                                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}" />

                                        <!-- 操作区域 -->
                                        <StackPanel Orientation="Horizontal">
                                            <ui:Button Content="编辑内容"
                                                       Icon="{ui:SymbolIcon Edit20}"
                                                       Appearance="Primary"
                                                       Margin="0,0,8,0" />

                                            <ui:Button Content="保存"
                                                       Icon="{ui:SymbolIcon Save20}"
                                                       Margin="0,0,8,0" />

                                            <ui:Button Content="刷新"
                                                       Icon="{ui:SymbolIcon ArrowClockwise20}"
                                                       Margin="0,0,8,0" />

                                            <!-- 关闭按钮 -->
                                            <ui:Button Content="关闭"
                                                       Icon="{ui:SymbolIcon Dismiss20}"
                                                       Command="{Binding DataContext.CloseTabCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                       CommandParameter="{Binding}"
                                                       Appearance="Danger">
                                                <ui:Button.Visibility>
                                                    <MultiBinding Converter="{StaticResource LogicalAndConverter}">
                                                        <Binding Path="IsClosable" />
                                                        <Binding Path="IsChecked" ElementName="EnableCloseButtonCheckBox" />
                                                    </MultiBinding>
                                                </ui:Button.Visibility>
                                            </ui:Button>
                                        </StackPanel>
                                    </StackPanel>
                                </ui:Card>
                            </ScrollViewer>
                        </DataTemplate>
                    </ui:TabView.ContentTemplate>
                </ui:TabView>

                <!-- 🎯 竖向标签的 TabView -->
                <ui:TabView x:Name="VerticalTabView"
                            ItemsSource="{Binding FilteredTabItems}"
                            SelectedItem="{Binding SelectedTab}"
                            TabStripPlacement="Left"
                            Visibility="{Binding IsChecked, ElementName=VerticalTabRadio, Converter={StaticResource BooleanToVisibilityConverter}}">

                    <!-- 竖向标签头部模板 -->
                    <ui:TabView.ItemTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal"
                                        MinWidth="120"
                                        Margin="8,4">
                                <!-- 小图标 -->
                                <ContentPresenter Width="16"
                                                  Height="16"
                                                  Margin="0,0,8,0"
                                                  VerticalAlignment="Center">
                                    <ContentPresenter.Content>
                                        <MultiBinding>
                                            <MultiBinding.Converter>
                                                <StaticResource ResourceKey="SimpleIconConverter" />
                                            </MultiBinding.Converter>
                                            <Binding Path="WpfUiSymbol" />
                                            <Binding Path="ZyloSymbol" />
                                            <Binding Path="Emoji" />
                                            <Binding Path="NodeType" />
                                        </MultiBinding>
                                    </ContentPresenter.Content>
                                </ContentPresenter>

                                <!-- 标签文本 -->
                                <TextBlock Text="{Binding Header}"
                                           VerticalAlignment="Center"
                                           TextTrimming="CharacterEllipsis"
                                           MaxWidth="80" />

                                <!-- 关闭按钮 -->
                                <ui:Button Width="16"
                                           Height="16"
                                           Margin="4,0,0,0"
                                           Padding="0"
                                           Command="{Binding DataContext.CloseTabCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                           CommandParameter="{Binding}"
                                           ToolTip="关闭标签页"
                                           Background="Transparent"
                                           BorderThickness="0">
                                    <ui:Button.Visibility>
                                        <MultiBinding Converter="{StaticResource LogicalAndConverter}">
                                            <Binding Path="IsClosable" />
                                            <Binding Path="IsChecked" ElementName="EnableCloseButtonCheckBox" />
                                        </MultiBinding>
                                    </ui:Button.Visibility>
                                    <ui:SymbolIcon Symbol="Dismiss12"
                                                   FontSize="10"
                                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}" />
                                </ui:Button>
                            </StackPanel>
                        </DataTemplate>
                    </ui:TabView.ItemTemplate>

                    <!-- 竖向标签页内容模板 -->
                    <ui:TabView.ContentTemplate>
                        <DataTemplate>
                            <ScrollViewer VerticalScrollBarVisibility="Auto"
                                          Background="{DynamicResource ApplicationBackgroundBrush}">
                                <ui:Card Margin="8" Padding="20"
                                         Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                                         BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                         BorderThickness="1">
                                    <StackPanel>
                                        <!-- 标题区域 -->
                                        <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                                            <!-- 大图标 -->
                                            <ContentPresenter Width="32"
                                                              Height="32"
                                                              Margin="0,0,12,0"
                                                              VerticalAlignment="Center">
                                                <ContentPresenter.Content>
                                                    <MultiBinding>
                                                        <MultiBinding.Converter>
                                                            <StaticResource ResourceKey="SimpleIconConverter" />
                                                        </MultiBinding.Converter>
                                                        <Binding Path="WpfUiSymbol" />
                                                        <Binding Path="ZyloSymbol" />
                                                        <Binding Path="Emoji" />
                                                        <Binding Path="NodeType" />
                                                    </MultiBinding>
                                                </ContentPresenter.Content>
                                            </ContentPresenter>

                                            <!-- 标题和描述 -->
                                            <StackPanel VerticalAlignment="Center">
                                                <TextBlock Text="{Binding Header}"
                                                           FontSize="18"
                                                           FontWeight="SemiBold"
                                                           Foreground="{DynamicResource TextFillColorPrimaryBrush}" />
                                                <TextBlock Text="{Binding Content}"
                                                           FontSize="12"
                                                           Margin="0,4,0,0"
                                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}" />
                                            </StackPanel>
                                        </StackPanel>

                                        <!-- 详细信息 -->
                                        <ui:Card Background="{DynamicResource LayerFillColorDefaultBrush}"
                                                 Padding="16"
                                                 Margin="0,0,0,16">
                                            <StackPanel>
                                                <TextBlock Text="📋 标签页详细信息"
                                                           FontWeight="SemiBold"
                                                           Margin="0,0,0,8" />
                                                <TextBlock Text="{Binding Id, StringFormat='ID: {0}'}"
                                                           Margin="0,0,0,4" />
                                                <TextBlock Text="{Binding NodeType, StringFormat='类型: {0}'}"
                                                           Margin="0,0,0,4" />
                                                <TextBlock Text="{Binding IsClosable, StringFormat='可关闭: {0}'}"
                                                           Margin="0,0,0,4" />
                                            </StackPanel>
                                        </ui:Card>

                                        <!-- 操作区域 -->
                                        <StackPanel Orientation="Horizontal">
                                            <ui:Button Content="编辑内容"
                                                       Icon="{ui:SymbolIcon Edit20}"
                                                       Appearance="Primary"
                                                       Margin="0,0,8,0" />

                                            <ui:Button Content="保存"
                                                       Icon="{ui:SymbolIcon Save20}"
                                                       Margin="0,0,8,0" />

                                            <ui:Button Content="刷新"
                                                       Icon="{ui:SymbolIcon ArrowClockwise20}"
                                                       Margin="0,0,8,0" />

                                            <!-- 关闭按钮 -->
                                            <ui:Button Content="关闭"
                                                       Icon="{ui:SymbolIcon Dismiss20}"
                                                       Command="{Binding DataContext.CloseTabCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                       CommandParameter="{Binding}"
                                                       Appearance="Danger">
                                                <ui:Button.Visibility>
                                                    <MultiBinding Converter="{StaticResource LogicalAndConverter}">
                                                        <Binding Path="IsClosable" />
                                                        <Binding Path="IsChecked" ElementName="EnableCloseButtonCheckBox" />
                                                    </MultiBinding>
                                                </ui:Button.Visibility>
                                            </ui:Button>
                                        </StackPanel>
                                    </StackPanel>
                                </ui:Card>
                            </ScrollViewer>
                        </DataTemplate>
                    </ui:TabView.ContentTemplate>
                </ui:TabView>

                <!-- 添加新标签页按钮 -->
                <ui:Button Content="+ 添加新标签页"
                           Icon="{ui:SymbolIcon Add20}"
                           Command="{Binding AddTabCommand}"
                           Margin="0,8,0,0"
                           HorizontalAlignment="Left" />
            </StackPanel>
        </ui:Card>

        <!-- 📊 状态栏 -->
        <Border Grid.Row="3"
                Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                CornerRadius="8"
                Padding="16,8"
                Margin="24,0,24,24">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <!-- 状态图标 -->
                <ui:SymbolIcon Grid.Column="0"
                               Symbol="Info20"
                               FontSize="16"
                               Margin="0,0,8,0"
                               Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}" />

                <!-- 状态消息 -->
                <TextBlock Grid.Column="1"
                           Text="{Binding StatusMessage}"
                           VerticalAlignment="Center"
                           FontSize="12"
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}" />

                <!-- 选中标签页信息 -->
                <TextBlock Grid.Column="2"
                           VerticalAlignment="Center"
                           FontSize="12"
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}">
                    <TextBlock.Text>
                        <MultiBinding StringFormat="选中: {0} | ID: {1}">
                            <Binding Path="SelectedTab.Header" />
                            <Binding Path="SelectedTab.Id" />
                        </MultiBinding>
                    </TextBlock.Text>
                </TextBlock>
            </Grid>
        </Border>
    </Grid>
</UserControl>
