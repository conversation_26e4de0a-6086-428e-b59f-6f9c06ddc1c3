// Expander C# 基础示例
// Expander 是 WPF 中的展开/折叠控件，用于节省界面空间

using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace WPFTest.Examples.LayoutControls
{
    public class ExpanderBasicExample
    {
        /// <summary>
        /// 创建基础 Expander 控件
        /// </summary>
        public static Expander CreateBasicExpander()
        {
            var expander = new Expander
            {
                // 标题文本
                Header = "基础 Expander",
                
                // 是否展开
                IsExpanded = false,
                
                // 背景色
                Background = new SolidColorBrush(Colors.LightBlue),
                
                // 边框
                BorderBrush = new SolidColorBrush(Colors.Blue),
                BorderThickness = new Thickness(1),
                
                // 边距和内边距
                Margin = new Thickness(8),
                Padding = new Thickness(4)
            };

            // 创建内容
            var content = new StackPanel
            {
                Margin = new Thickness(16)
            };

            content.Children.Add(new TextBlock
            {
                Text = "这是 Expander 的内容区域。",
                Margin = new Thickness(0, 0, 0, 8)
            });

            content.Children.Add(new TextBlock
            {
                Text = "可以包含任何 WPF 控件和布局。",
                Margin = new Thickness(0, 0, 0, 8)
            });

            content.Children.Add(new Button
            {
                Content = "示例按钮",
                HorizontalAlignment = HorizontalAlignment.Left
            });

            expander.Content = content;
            return expander;
        }

        /// <summary>
        /// 创建带图标标题的 Expander
        /// </summary>
        public static Expander CreateExpanderWithIcon()
        {
            var expander = new Expander
            {
                IsExpanded = false,
                Background = new SolidColorBrush(Colors.DarkSlateBlue),
                BorderBrush = new SolidColorBrush(Colors.SlateBlue),
                BorderThickness = new Thickness(1),
                Margin = new Thickness(8)
            };

            // 创建带图标的标题
            var headerPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal
            };

            // 添加图标（这里用 TextBlock 模拟图标）
            headerPanel.Children.Add(new TextBlock
            {
                Text = "⚙️",
                FontSize = 16,
                Margin = new Thickness(0, 0, 8, 0),
                VerticalAlignment = VerticalAlignment.Center
            });

            headerPanel.Children.Add(new TextBlock
            {
                Text = "带图标的 Expander",
                Foreground = Brushes.White,
                VerticalAlignment = VerticalAlignment.Center
            });

            expander.Header = headerPanel;

            // 创建内容
            var content = new StackPanel
            {
                Margin = new Thickness(16)
            };

            content.Children.Add(new TextBlock
            {
                Text = "这个 Expander 的标题包含图标。",
                Foreground = Brushes.White,
                Margin = new Thickness(0, 0, 0, 8)
            });

            var progressBar = new ProgressBar
            {
                Value = 75,
                Height = 20,
                Margin = new Thickness(0, 0, 0, 8)
            };
            content.Children.Add(progressBar);

            content.Children.Add(new TextBlock
            {
                Text = "进度: 75%",
                Foreground = Brushes.White,
                FontSize = 12
            });

            expander.Content = content;
            return expander;
        }

        /// <summary>
        /// 创建不同展开方向的 Expander
        /// </summary>
        public static Expander CreateExpanderWithDirection(ExpandDirection direction)
        {
            var expander = new Expander
            {
                Header = $"{direction} 展开",
                ExpandDirection = direction,
                IsExpanded = false,
                Background = Brushes.LightGreen,
                BorderBrush = Brushes.Green,
                BorderThickness = new Thickness(1),
                Margin = new Thickness(4)
            };

            var content = new StackPanel
            {
                Margin = new Thickness(12)
            };

            content.Children.Add(new TextBlock
            {
                Text = $"{direction} 展开的内容"
            });

            content.Children.Add(new Button
            {
                Content = "按钮",
                HorizontalAlignment = HorizontalAlignment.Left,
                Margin = new Thickness(0, 8, 0, 0)
            });

            expander.Content = content;
            return expander;
        }

        /// <summary>
        /// 创建默认展开的 Expander
        /// </summary>
        public static Expander CreateExpandedExpander()
        {
            var expander = new Expander
            {
                Header = "默认展开 Expander",
                IsExpanded = true, // 默认展开
                Background = Brushes.LightYellow,
                BorderBrush = Brushes.Orange,
                BorderThickness = new Thickness(1),
                Margin = new Thickness(8)
            };

            var content = new StackPanel
            {
                Margin = new Thickness(16)
            };

            content.Children.Add(new TextBlock
            {
                Text = "这个 Expander 默认是展开状态。",
                Margin = new Thickness(0, 0, 0, 8)
            });

            content.Children.Add(new CheckBox
            {
                Content = "示例复选框",
                Margin = new Thickness(0, 0, 0, 8)
            });

            content.Children.Add(new Slider
            {
                Minimum = 0,
                Maximum = 100,
                Value = 50
            });

            expander.Content = content;
            return expander;
        }

        /// <summary>
        /// 创建禁用状态的 Expander
        /// </summary>
        public static Expander CreateDisabledExpander()
        {
            var expander = new Expander
            {
                Header = "禁用状态 Expander",
                IsExpanded = false,
                IsEnabled = false, // 禁用状态
                Background = Brushes.LightGray,
                BorderBrush = Brushes.Gray,
                BorderThickness = new Thickness(1),
                Margin = new Thickness(8)
            };

            var content = new StackPanel
            {
                Margin = new Thickness(16)
            };

            content.Children.Add(new TextBlock
            {
                Text = "这是禁用状态的 Expander。"
            });

            content.Children.Add(new Button
            {
                Content = "不可点击按钮",
                HorizontalAlignment = HorizontalAlignment.Left,
                Margin = new Thickness(0, 8, 0, 0)
            });

            expander.Content = content;
            return expander;
        }

        /// <summary>
        /// 为 Expander 添加展开/折叠事件处理
        /// </summary>
        public static void AddExpanderEvents(Expander expander)
        {
            expander.Expanded += (sender, e) =>
            {
                System.Diagnostics.Debug.WriteLine($"Expander '{expander.Header}' 已展开");
            };

            expander.Collapsed += (sender, e) =>
            {
                System.Diagnostics.Debug.WriteLine($"Expander '{expander.Header}' 已折叠");
            };
        }
    }
}
