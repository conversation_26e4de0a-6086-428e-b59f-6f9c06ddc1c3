<ui:FluentWindow
    x:Class="WPFTest.Views.DWG.RenameFileDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    Title="重命名文件"
    Width="450"
    Height="200"
    WindowStartupLocation="CenterOwner"
    ResizeMode="NoResize"
    ShowInTaskbar="False"
    WindowStyle="SingleBorderWindow">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="20" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="20" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  标题和图标  -->
        <StackPanel Grid.Row="0" Orientation="Horizontal">
            <ui:SymbolIcon
                Symbol="Rename24"
                FontSize="24"
                Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                Margin="0,0,12,0" />
            <TextBlock
                Text="重命名文件"
                FontSize="18"
                FontWeight="SemiBold"
                VerticalAlignment="Center" />
        </StackPanel>

        <!--  说明文字  -->
        <TextBlock
            Grid.Row="2"
            Text="请输入新的文件名："
            FontSize="14"
            Foreground="{DynamicResource TextFillColorSecondaryBrush}" />

        <!--  输入框  -->
        <ui:TextBox
            x:Name="FileNameTextBox"
            Grid.Row="4"
            FontSize="14"
            PlaceholderText="输入新的文件名..."
            Icon="{ui:SymbolIcon Document24}"
            ClearButtonEnabled="True">
            <ui:TextBox.InputBindings>
                <KeyBinding Key="Enter" Command="{Binding ConfirmCommand, RelativeSource={RelativeSource AncestorType=Window}}" />
                <KeyBinding Key="Escape" Command="{Binding CancelCommand, RelativeSource={RelativeSource AncestorType=Window}}" />
            </ui:TextBox.InputBindings>
        </ui:TextBox>

        <!--  按钮区域  -->
        <StackPanel
            Grid.Row="6"
            Orientation="Horizontal"
            HorizontalAlignment="Right"
            Margin="0,20,0,0">

            <ui:Button
                x:Name="CancelButton"
                Content="取消"
                Appearance="Secondary"
                Margin="0,0,12,0"
                Padding="20,8"
                Click="CancelButton_Click">
                <ui:Button.Icon>
                    <ui:SymbolIcon Symbol="Dismiss24" />
                </ui:Button.Icon>
            </ui:Button>

            <ui:Button
                x:Name="ConfirmButton"
                Content="确定"
                Appearance="Primary"
                Padding="20,8"
                Click="ConfirmButton_Click">
                <ui:Button.Icon>
                    <ui:SymbolIcon Symbol="Checkmark24" />
                </ui:Button.Icon>
            </ui:Button>
        </StackPanel>
    </Grid>
</ui:FluentWindow>
