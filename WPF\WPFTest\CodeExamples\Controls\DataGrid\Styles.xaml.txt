<!-- DataGrid 样式使用示例 -->
<StackPanel Margin="20" Spacing="15">

    <!-- 标准样式 -->
    <GroupBox Header="标准样式" Padding="15">
        <StackPanel>
            <TextBlock Text="使用 DataGridStyle 样式" FontWeight="Medium" Margin="0,0,0,8"/>
            <DataGrid ItemsSource="{Binding EmployeeData}"
                      Style="{StaticResource DataGridStyle}"
                      Height="120"
                      AutoGenerateColumns="False"
                      IsReadOnly="True">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="姓名" Binding="{Binding Name}" Width="100"/>
                    <DataGridTextColumn Header="部门" Binding="{Binding Department}" Width="100"/>
                    <DataGridTextColumn Header="职位" Binding="{Binding Position}" Width="120"/>
                </DataGrid.Columns>
            </DataGrid>
        </StackPanel>
    </GroupBox>

    <!-- 现代化样式 -->
    <GroupBox Header="现代化样式" Padding="15">
        <StackPanel>
            <TextBlock Text="使用 ModernDataGridStyle 样式" FontWeight="Medium" Margin="0,0,0,8"/>
            <DataGrid ItemsSource="{Binding ProductData}"
                      Style="{StaticResource ModernDataGridStyle}"
                      Height="120"
                      AutoGenerateColumns="False"
                      IsReadOnly="True">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="产品名称" Binding="{Binding Name}" Width="150"/>
                    <DataGridTextColumn Header="分类" Binding="{Binding Category}" Width="100"/>
                    <DataGridTextColumn Header="价格" Binding="{Binding Price, StringFormat=C}" Width="100"/>
                </DataGrid.Columns>
            </DataGrid>
        </StackPanel>
    </GroupBox>

    <!-- 紧凑样式 -->
    <GroupBox Header="紧凑样式" Padding="15">
        <StackPanel>
            <TextBlock Text="使用 CompactDataGridStyle 样式" FontWeight="Medium" Margin="0,0,0,8"/>
            <DataGrid ItemsSource="{Binding EmployeeData}"
                      Style="{StaticResource CompactDataGridStyle}"
                      Height="100"
                      AutoGenerateColumns="False"
                      IsReadOnly="True">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="姓名" Binding="{Binding Name}" Width="80"/>
                    <DataGridTextColumn Header="部门" Binding="{Binding Department}" Width="80"/>
                    <DataGridTextColumn Header="薪资" Binding="{Binding Salary, StringFormat=C}" Width="80"/>
                </DataGrid.Columns>
            </DataGrid>
        </StackPanel>
    </GroupBox>

    <!-- 透明样式 -->
    <GroupBox Header="透明样式" Padding="15">
        <StackPanel>
            <TextBlock Text="使用 TransparentDataGridStyle 样式" FontWeight="Medium" Margin="0,0,0,8"/>
            <DataGrid ItemsSource="{Binding EmployeeData}"
                      Style="{StaticResource TransparentDataGridStyle}"
                      Height="120"
                      AutoGenerateColumns="False"
                      IsReadOnly="True">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="姓名" Binding="{Binding Name}" Width="100"/>
                    <DataGridTextColumn Header="部门" Binding="{Binding Department}" Width="100"/>
                </DataGrid.Columns>
            </DataGrid>
        </StackPanel>
    </GroupBox>

    <!-- 强调色样式 -->
    <GroupBox Header="强调色样式" Padding="15">
        <StackPanel>
            <TextBlock Text="使用 AccentDataGridStyle 样式" FontWeight="Medium" Margin="0,0,0,8"/>
            <DataGrid ItemsSource="{Binding ProductData}"
                      Style="{StaticResource AccentDataGridStyle}"
                      Height="120"
                      AutoGenerateColumns="False"
                      IsReadOnly="True">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="产品名称" Binding="{Binding Name}" Width="120"/>
                    <DataGridTextColumn Header="价格" Binding="{Binding Price, StringFormat=C}" Width="80"/>
                    <DataGridCheckBoxColumn Header="启用" Binding="{Binding IsActive}" Width="60"/>
                </DataGrid.Columns>
            </DataGrid>
        </StackPanel>
    </GroupBox>

    <!-- 样式说明 -->
    <GroupBox Header="样式说明" Padding="15">
        <TextBlock TextWrapping="Wrap">
            <Run Text="Zylo.WPF 提供了多种 DataGrid 样式："/>
            <LineBreak/>
            <Run Text="• DataGridStyle - 标准样式，适合大多数场景"/>
            <LineBreak/>
            <Run Text="• ModernDataGridStyle - 现代化样式，带有强调色边框"/>
            <LineBreak/>
            <Run Text="• CompactDataGridStyle - 紧凑样式，节省空间"/>
            <LineBreak/>
            <Run Text="• TransparentDataGridStyle - 透明样式，无背景"/>
            <LineBreak/>
            <Run Text="• AccentDataGridStyle - 强调色样式，突出显示"/>
        </TextBlock>
    </GroupBox>

</StackPanel>
