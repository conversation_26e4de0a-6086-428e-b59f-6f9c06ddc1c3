using System;
using System.Windows;

namespace Zylo.WPF;

/// <summary>
/// 🚀 让普通 Application 获得 Prism 功能的简单扩展
/// </summary>
public static class ApplicationExtensions
{
    /// <summary>
    /// 使用 Zylo.WPF 初始化 Prism 功能
    /// </summary>
    /// <typeparam name="TBootstrapper">Bootstrapper 类型</typeparam>
    /// <param name="app">WPF Application</param>
    /// <returns>Application 实例</returns>
    public static Application UseZyloMvvm<TBootstrapper>(this Application app) 
        where TBootstrapper : ZyloBootstrapper, new()
    {
        var bootstrapper = new TBootstrapper();
        bootstrapper.Run();
        return app;
    }

    /// <summary>
    /// 使用自定义 Bootstrapper 初始化 Prism 功能
    /// </summary>
    /// <param name="app">WPF Application</param>
    /// <param name="bootstrapper">自定义 Bootstrapper</param>
    /// <returns>Application 实例</returns>
    public static Application UseZyloMvvm(this Application app, ZyloBootstrapper bootstrapper)
    {
        bootstrapper.Run();
        return app;
    }
}
