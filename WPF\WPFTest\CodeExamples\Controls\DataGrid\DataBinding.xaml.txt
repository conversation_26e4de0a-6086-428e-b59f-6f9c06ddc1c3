<!-- DataGrid 数据绑定示例 -->
<StackPanel Margin="20" Spacing="15">

    <!-- ObservableCollection 绑定 -->
    <GroupBox Header="ObservableCollection 数据绑定" Padding="15">
        <StackPanel>
            <TextBlock Text="使用 ObservableCollection 实现动态数据绑定" FontWeight="Medium" Margin="0,0,0,8"/>
            
            <!-- 统计信息 -->
            <Border Background="{DynamicResource CardBackgroundFillColorSecondaryBrush}"
                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="6"
                    Padding="12"
                    Margin="0,0,0,12">
                <WrapPanel>
                    <TextBlock Text="{Binding EmployeeData.Count, StringFormat='员工总数: {0}'}"
                               FontSize="12"
                               Margin="0,0,20,0"/>
                    <TextBlock Text="{Binding ProductData.Count, StringFormat='产品总数: {0}'}"
                               FontSize="12"
                               Margin="0,0,20,0"/>
                    <TextBlock Text="{Binding InteractionCount, StringFormat='交互次数: {0}'}"
                               FontSize="12"/>
                </WrapPanel>
            </Border>

            <!-- 数据表格 -->
            <DataGrid ItemsSource="{Binding EmployeeData}"
                      SelectedItem="{Binding SelectedEmployee, Mode=TwoWay}"
                      Style="{StaticResource DataGridStyle}"
                      Height="150"
                      AutoGenerateColumns="False"
                      IsReadOnly="True">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="ID" Binding="{Binding Id}" Width="60"/>
                    <DataGridTextColumn Header="姓名" Binding="{Binding Name}" Width="100"/>
                    <DataGridTextColumn Header="部门" Binding="{Binding Department}" Width="100"/>
                    <DataGridTextColumn Header="薪资" Binding="{Binding Salary, StringFormat=C}" Width="100"/>
                </DataGrid.Columns>
            </DataGrid>
        </StackPanel>
    </GroupBox>

    <!-- 双向绑定 -->
    <GroupBox Header="双向数据绑定" Padding="15">
        <StackPanel>
            <TextBlock Text="支持双向绑定，实时更新数据" FontWeight="Medium" Margin="0,0,0,8"/>
            
            <!-- 操作按钮 -->
            <WrapPanel Margin="0,0,0,12">
                <ui:Button Content="添加员工"
                           Command="{Binding AddEmployeeCommand}"
                           Appearance="Primary"
                           Margin="0,0,8,0"/>
                <ui:Button Content="删除选中"
                           Command="{Binding DeleteSelectedEmployeeCommand}"
                           Appearance="Danger"
                           Margin="0,0,8,0"/>
            </WrapPanel>

            <!-- 可编辑数据表格 -->
            <DataGrid ItemsSource="{Binding ProductData}"
                      SelectedItem="{Binding SelectedProduct, Mode=TwoWay}"
                      Style="{StaticResource ModernDataGridStyle}"
                      Height="150"
                      AutoGenerateColumns="False"
                      CanUserAddRows="True"
                      CanUserDeleteRows="True"
                      IsReadOnly="False">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="产品名称" Binding="{Binding Name}" Width="120"/>
                    <DataGridTextColumn Header="分类" Binding="{Binding Category}" Width="100"/>
                    <DataGridTextColumn Header="价格" Binding="{Binding Price}" Width="80"/>
                    <DataGridTextColumn Header="库存" Binding="{Binding Stock}" Width="60"/>
                    <DataGridCheckBoxColumn Header="启用" Binding="{Binding IsActive}" Width="60"/>
                </DataGrid.Columns>
            </DataGrid>
        </StackPanel>
    </GroupBox>

    <!-- 选择状态绑定 -->
    <GroupBox Header="选择状态绑定" Padding="15">
        <StackPanel>
            <TextBlock Text="绑定选择状态，实时响应用户操作" FontWeight="Medium" Margin="0,0,0,8"/>
            
            <!-- 选择信息显示 -->
            <Border Background="{DynamicResource CardBackgroundFillColorSecondaryBrush}"
                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="6"
                    Padding="12"
                    Margin="0,0,0,12">
                <StackPanel>
                    <TextBlock Text="{Binding SelectedEmployee, StringFormat='选中员工: {0}'}"
                               FontSize="12"
                               Margin="0,0,0,4"/>
                    <TextBlock Text="{Binding SelectedProduct, StringFormat='选中产品: {0}'}"
                               FontSize="12"/>
                </StackPanel>
            </Border>

            <TextBlock TextWrapping="Wrap" FontSize="12" Foreground="{DynamicResource TextFillColorSecondaryBrush}">
                <Run Text="当您在上面的表格中选择不同的行时，这里的信息会实时更新。"/>
                <LineBreak/>
                <Run Text="这展示了 DataGrid 与 ViewModel 之间的双向数据绑定功能。"/>
            </TextBlock>
        </StackPanel>
    </GroupBox>

    <!-- 数据绑定说明 -->
    <GroupBox Header="数据绑定说明" Padding="15">
        <TextBlock TextWrapping="Wrap">
            <Run Text="DataGrid 数据绑定的关键特性："/>
            <LineBreak/>
            <Run Text="• ItemsSource - 绑定数据集合（ObservableCollection）"/>
            <LineBreak/>
            <Run Text="• SelectedItem - 绑定选中项（双向绑定）"/>
            <LineBreak/>
            <Run Text="• 列绑定 - 每列绑定到数据模型的属性"/>
            <LineBreak/>
            <Run Text="• 实时更新 - 数据变化时自动刷新界面"/>
            <LineBreak/>
            <Run Text="• 双向绑定 - 界面操作自动更新数据模型"/>
        </TextBlock>
    </GroupBox>

</StackPanel>
