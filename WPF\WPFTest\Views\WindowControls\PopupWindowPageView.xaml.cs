using System.Windows.Controls;
using WPFTest.ViewModels.WindowControls;

namespace WPFTest.Views.WindowControls;

/// <summary>
/// PopupWindowPageView.xaml 的交互逻辑
/// </summary>
public partial class PopupWindowPageView : UserControl
{
    /// <summary>
    /// ViewModel
    /// </summary>
    public PopupWindowPageViewModel ViewModel { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    public PopupWindowPageView()
    {
        ViewModel = new PopupWindowPageViewModel();
        DataContext = ViewModel;
        
        InitializeComponent();
    }
}
