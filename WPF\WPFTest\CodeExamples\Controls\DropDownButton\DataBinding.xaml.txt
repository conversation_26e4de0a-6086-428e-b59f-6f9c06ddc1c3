<!-- DropDownButton 数据绑定示例 -->
<StackPanel Margin="20" Spacing="15">
    
    <!-- 基础数据绑定 -->
    <GroupBox Header="基础数据绑定 - 最近文件列表" Padding="15">
        <StackPanel>
            <TextBlock Text="使用 ItemsSource 绑定集合数据，自动生成菜单项" FontSize="12" Margin="0,0,0,10"/>
            
            <ui:DropDownButton Content="最近文件" 
                               Command="{Binding HandleInteractionCommand}"
                               CommandParameter="最近文件"
                               Margin="8">
                <ui:DropDownButton.Icon>
                    <ui:SymbolIcon Symbol="History24"/>
                </ui:DropDownButton.Icon>
                <ui:DropDownButton.Flyout>
                    <ContextMenu ItemsSource="{Binding RecentFiles}">
                        <ContextMenu.ItemTemplate>
                            <DataTemplate>
                                <MenuItem Header="{Binding Name}" 
                                          Command="{Binding DataContext.HandleInteractionCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                          CommandParameter="{Binding Name}">
                                    <MenuItem.Icon>
                                        <ui:SymbolIcon Symbol="{Binding Icon}"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                            </DataTemplate>
                        </ContextMenu.ItemTemplate>
                    </ContextMenu>
                </ui:DropDownButton.Flyout>
            </ui:DropDownButton>
        </StackPanel>
    </GroupBox>

    <!-- 高级数据绑定 - 带工具提示 -->
    <GroupBox Header="高级数据绑定 - 导出格式（带工具提示）" Padding="15">
        <StackPanel>
            <TextBlock Text="使用 DataTemplate 自定义菜单项外观，添加工具提示显示详细信息" FontSize="12" Margin="0,0,0,10"/>
            
            <ui:DropDownButton Content="导出格式" 
                               Command="{Binding HandleInteractionCommand}"
                               CommandParameter="导出格式"
                               Margin="8">
                <ui:DropDownButton.Icon>
                    <ui:SymbolIcon Symbol="Share24"/>
                </ui:DropDownButton.Icon>
                <ui:DropDownButton.Flyout>
                    <ContextMenu ItemsSource="{Binding ExportFormats}">
                        <ContextMenu.ItemTemplate>
                            <DataTemplate>
                                <MenuItem Header="{Binding Name}" 
                                          Command="{Binding DataContext.HandleInteractionCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                          CommandParameter="{Binding Name}">
                                    <MenuItem.Icon>
                                        <ui:SymbolIcon Symbol="{Binding Icon}"/>
                                    </MenuItem.Icon>
                                    <MenuItem.ToolTip>
                                        <StackPanel>
                                            <TextBlock Text="{Binding Name}" FontWeight="Bold"/>
                                            <TextBlock Text="{Binding Description}" FontSize="12"/>
                                            <TextBlock Text="{Binding Extension}" FontSize="11" Foreground="Gray"/>
                                        </StackPanel>
                                    </MenuItem.ToolTip>
                                </MenuItem>
                            </DataTemplate>
                        </ContextMenu.ItemTemplate>
                    </ContextMenu>
                </ui:DropDownButton.Flyout>
            </ui:DropDownButton>
        </StackPanel>
    </GroupBox>

    <!-- 分组数据绑定 -->
    <GroupBox Header="分组数据绑定 - 开发工具（按类别分组）" Padding="15">
        <StackPanel>
            <TextBlock Text="使用 CollectionViewSource 对数据进行分组，创建分层菜单结构" FontSize="12" Margin="0,0,0,10"/>
            
            <ui:DropDownButton Content="开发工具" 
                               Command="{Binding HandleInteractionCommand}"
                               CommandParameter="开发工具"
                               Margin="8">
                <ui:DropDownButton.Icon>
                    <ui:SymbolIcon Symbol="Wrench24"/>
                </ui:DropDownButton.Icon>
                <ui:DropDownButton.Flyout>
                    <ContextMenu>
                        <ContextMenu.Resources>
                            <CollectionViewSource x:Key="GroupedTools" Source="{Binding ToolItems}">
                                <CollectionViewSource.GroupDescriptions>
                                    <PropertyGroupDescription PropertyName="Category"/>
                                </CollectionViewSource.GroupDescriptions>
                            </CollectionViewSource>
                        </ContextMenu.Resources>
                        <ContextMenu.ItemsSource>
                            <Binding Source="{StaticResource GroupedTools}"/>
                        </ContextMenu.ItemsSource>
                        <ContextMenu.GroupStyle>
                            <GroupStyle>
                                <GroupStyle.HeaderTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding Name}" FontWeight="Bold" FontSize="12" Margin="0,4,0,2"/>
                                    </DataTemplate>
                                </GroupStyle.HeaderTemplate>
                            </GroupStyle>
                        </ContextMenu.GroupStyle>
                        <ContextMenu.ItemTemplate>
                            <DataTemplate>
                                <MenuItem Header="{Binding Name}" 
                                          Command="{Binding DataContext.HandleInteractionCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                          CommandParameter="{Binding Name}">
                                    <MenuItem.Icon>
                                        <ui:SymbolIcon Symbol="{Binding Icon}"/>
                                    </MenuItem.Icon>
                                    <MenuItem.ToolTip>
                                        <StackPanel>
                                            <TextBlock Text="{Binding Name}" FontWeight="Bold"/>
                                            <TextBlock Text="{Binding Description}" FontSize="12"/>
                                            <TextBlock Text="{Binding Category}" FontSize="11" Foreground="Gray"/>
                                        </StackPanel>
                                    </MenuItem.ToolTip>
                                </MenuItem>
                            </DataTemplate>
                        </ContextMenu.ItemTemplate>
                    </ContextMenu>
                </ui:DropDownButton.Flyout>
            </ui:DropDownButton>
        </StackPanel>
    </GroupBox>

    <!-- 动态数据绑定 -->
    <GroupBox Header="动态数据绑定 - 运行时更新" Padding="15">
        <StackPanel>
            <TextBlock Text="使用 ObservableCollection 实现运行时动态添加/删除菜单项" FontSize="12" Margin="0,0,0,10"/>
            
            <StackPanel Orientation="Horizontal">
                <ui:Button Content="添加文件" Command="{Binding AddRecentFileCommand}" CommandParameter="新文件.txt" Margin="0,0,8,0"/>
                <ui:Button Content="清空列表" Command="{Binding ClearRecentFilesCommand}"/>
            </StackPanel>
            
            <TextBlock Text="{Binding RecentFiles.Count, StringFormat='当前文件数量: {0}'}" FontSize="12" Margin="0,8,0,0"/>
        </StackPanel>
    </GroupBox>

</StackPanel>
