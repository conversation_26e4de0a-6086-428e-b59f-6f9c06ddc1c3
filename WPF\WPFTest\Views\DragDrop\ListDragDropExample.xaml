<UserControl x:Class="WPFTest.Views.DragDrop.ListDragDropExample"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:dd="urn:gong-wpf-dragdrop"
             xmlns:codeExample="clr-namespace:Zylo.WPF.Controls.CodeExample;assembly=Zylo.WPF"
             xmlns:mvvm="http://prismlibrary.com/"
             xmlns:dragDrop="clr-namespace:WPFTest.ViewModels.DragDrop"
             mc:Ignorable="d"
             d:DesignHeight="800" d:DesignWidth="1200"
             mvvm:ViewModelLocator.AutoWireViewModel="True"
             d:DataContext="{d:DesignInstance dragDrop:ListDragDropExampleViewModel}">

    <ScrollViewer VerticalScrollBarVisibility="Auto" 
                  HorizontalScrollBarVisibility="Disabled"
                  Padding="24">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>    <!-- 标题区域 -->
                <RowDefinition Height="*"/>       <!-- 主要内容 -->
                <RowDefinition Height="Auto"/>    <!-- 底部操作栏 -->
            </Grid.RowDefinitions>

            <!-- 标题区域 -->
            <StackPanel Grid.Row="0" Margin="0,0,0,20">
                <TextBlock Text="🎯 列表拖拽示例"
                           FontSize="32"
                           FontWeight="Bold"
                           Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                           Margin="0,0,0,8"/>
                <TextBlock Text="展示 gong-wpf-dragdrop 库在列表控件中的拖拽功能" 
                           FontSize="16" 
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                           TextWrapping="Wrap"/>
                
                <!-- 状态显示 -->
                <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                        BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Padding="16,12"
                        Margin="0,16,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <ui:SymbolIcon Symbol="Info24" 
                                       FontSize="20" 
                                       Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                       VerticalAlignment="Center"
                                       Margin="0,0,12,0"/>
                        <TextBlock Grid.Column="1" 
                                   Text="{Binding StatusMessage}" 
                                   FontSize="14"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                   VerticalAlignment="Center"
                                   TextWrapping="Wrap"/>
                    </Grid>
                </Border>
            </StackPanel>

            <!-- 主要内容区域 -->
            <Border Grid.Row="1" Margin="0,0,0,20">
                <StackPanel>

                    <!-- 基础拖拽功能展示 -->
                    <ui:CardExpander Header="🎯 基础拖拽功能" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示列表间的基础拖拽功能，包括项目移动和复制"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>
                            
                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="列表间拖拽：" FontWeight="Medium" Margin="0,0,0,16"/>
                                    
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <!-- 源列表 -->
                                        <StackPanel Grid.Column="0">
                                            <Grid Margin="0,0,0,8">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>
                                                <TextBlock Text="源列表 (可拖拽)" FontWeight="Bold"/>
                                                <ui:Button Grid.Column="1" 
                                                           Content="➕" 
                                                           Command="{Binding AddSourceItemCommand}"
                                                           ToolTip="添加源项目"
                                                           Appearance="Secondary"
                                                           Width="24" Height="24"
                                                           Padding="0"/>
                                            </Grid>
                                            
                                            <!-- 源列表：支持拖拽和放置的 ListView
                                                 关键属性说明：
                                                 - dd:DragDrop.IsDragSource="True": 启用拖拽源功能，允许从此控件拖拽项目
                                                 - dd:DragDrop.IsDropTarget="True": 启用放置目标功能，允许向此控件放置项目
                                                 - dd:DragDrop.DragHandler="{Binding}": 绑定拖拽处理器到 ViewModel（实现 IDragSource）
                                                 - dd:DragDrop.DropHandler="{Binding}": 绑定放置处理器到 ViewModel（实现 IDropTarget）
                                                 - dd:DragDrop.UseDefaultDragAdorner="True": 使用默认的拖拽装饰器（拖拽时显示的预览）
                                                 - dd:DragDrop.DefaultDragAdornerOpacity="0.8": 设置默认装饰器的透明度
                                            -->
                                            <ListView ItemsSource="{Binding SourceItems}"
                                                      SelectedItem="{Binding SelectedSourceItem}"
                                                      Height="200"
                                                      dd:DragDrop.IsDragSource="True"
                                                      dd:DragDrop.IsDropTarget="True"
                                                      dd:DragDrop.DragHandler="{Binding}"
                                                      dd:DragDrop.DropHandler="{Binding}"
                                                      dd:DragDrop.UseDefaultDragAdorner="True"
                                                      dd:DragDrop.DefaultDragAdornerOpacity="0.8">
                                                <ListView.ItemTemplate>
                                                    <DataTemplate>
                                                        <!-- 源列表项目模板：设计为可拖拽的卡片样式
                                                             - Cursor="Hand": 鼠标悬停时显示手型光标，提示用户可以拖拽
                                                             - 使用动态资源确保主题适配
                                                             - 圆角和阴影提供现代化外观
                                                        -->
                                                        <Border Background="{DynamicResource CardBackgroundFillColorSecondaryBrush}"
                                                                BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                                                BorderThickness="1"
                                                                CornerRadius="4"
                                                                Padding="8"
                                                                Margin="2"
                                                                Cursor="Hand">
                                                            <Grid>
                                                                <Grid.ColumnDefinitions>
                                                                    <ColumnDefinition Width="Auto"/>
                                                                    <ColumnDefinition Width="*"/>
                                                                    <ColumnDefinition Width="Auto"/>
                                                                </Grid.ColumnDefinitions>
                                                                
                                                                <ui:SymbolIcon Grid.Column="0"
                                                                               Symbol="ArrowMove24" 
                                                                               FontSize="16"
                                                                               Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                                                               Margin="0,0,8,0"/>
                                                                
                                                                <StackPanel Grid.Column="1">
                                                                    <TextBlock Text="{Binding Name}" 
                                                                               FontWeight="Medium"
                                                                               FontSize="12"/>
                                                                    <TextBlock Text="{Binding Category}" 
                                                                               FontSize="10"
                                                                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                                                                </StackPanel>
                                                                
                                                                <Border Grid.Column="2"
                                                                        Background="{DynamicResource AccentFillColorDefaultBrush}"
                                                                        CornerRadius="8"
                                                                        Padding="4,2"
                                                                        MinWidth="16">
                                                                    <TextBlock Text="{Binding Priority}" 
                                                                               FontSize="10"
                                                                               Foreground="White"
                                                                               HorizontalAlignment="Center"/>
                                                                </Border>
                                                            </Grid>
                                                        </Border>
                                                    </DataTemplate>
                                                </ListView.ItemTemplate>
                                            </ListView>
                                        </StackPanel>
                                        
                                        <!-- 箭头指示 -->
                                        <StackPanel Grid.Column="1" 
                                                    VerticalAlignment="Center"
                                                    Margin="16,0">
                                            <ui:SymbolIcon Symbol="ArrowRight24" 
                                                           FontSize="20"
                                                           Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                                           HorizontalAlignment="Center"/>
                                            <TextBlock Text="拖拽" 
                                                       FontSize="10"
                                                       HorizontalAlignment="Center"
                                                       Margin="0,4,0,0"/>
                                        </StackPanel>
                                        
                                        <!-- 目标列表 -->
                                        <StackPanel Grid.Column="2">
                                            <Grid Margin="0,0,0,8">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>
                                                <TextBlock Text="目标列表 (可放置)" FontWeight="Bold"/>
                                                <ui:Button Grid.Column="1" 
                                                           Content="🗑️" 
                                                           Command="{Binding ClearTargetListCommand}"
                                                           ToolTip="清空目标列表"
                                                           Appearance="Secondary"
                                                           Width="24" Height="24"
                                                           Padding="0"/>
                                            </Grid>
                                            
                                            <ListView ItemsSource="{Binding TargetItems}"
                                                      SelectedItem="{Binding SelectedTargetItem}"
                                                      Height="200"
                                                      dd:DragDrop.IsDragSource="True"
                                                      dd:DragDrop.IsDropTarget="True"
                                                      dd:DragDrop.DragHandler="{Binding}"
                                                      dd:DragDrop.DropHandler="{Binding}"
                                                      Background="{DynamicResource CardBackgroundFillColorSecondaryBrush}">
                                                <ListView.ItemTemplate>
                                                    <DataTemplate>
                                                        <Border Background="{DynamicResource SystemFillColorSuccessBrush}" 
                                                                BorderBrush="{DynamicResource SystemFillColorSuccessBrush}"
                                                                BorderThickness="1"
                                                                CornerRadius="4"
                                                                Padding="8" 
                                                                Margin="2">
                                                            <Grid>
                                                                <Grid.ColumnDefinitions>
                                                                    <ColumnDefinition Width="Auto"/>
                                                                    <ColumnDefinition Width="*"/>
                                                                    <ColumnDefinition Width="Auto"/>
                                                                </Grid.ColumnDefinitions>
                                                                
                                                                <ui:SymbolIcon Grid.Column="0"
                                                                               Symbol="Checkmark24" 
                                                                               FontSize="16"
                                                                               Foreground="White"
                                                                               Margin="0,0,8,0"/>
                                                                
                                                                <StackPanel Grid.Column="1">
                                                                    <TextBlock Text="{Binding Name}" 
                                                                               FontWeight="Medium"
                                                                               FontSize="12"
                                                                               Foreground="White"/>
                                                                    <TextBlock Text="{Binding Category}" 
                                                                               FontSize="10"
                                                                               Foreground="White"
                                                                               Opacity="0.8"/>
                                                                </StackPanel>
                                                                
                                                                <Border Grid.Column="2"
                                                                        Background="White"
                                                                        CornerRadius="8"
                                                                        Padding="4,2"
                                                                        MinWidth="16">
                                                                    <TextBlock Text="{Binding Priority}" 
                                                                               FontSize="10"
                                                                               Foreground="{DynamicResource SystemFillColorSuccessBrush}"
                                                                               HorizontalAlignment="Center"/>
                                                                </Border>
                                                            </Grid>
                                                        </Border>
                                                    </DataTemplate>
                                                </ListView.ItemTemplate>
                                            </ListView>
                                        </StackPanel>
                                    </Grid>
                                    
                                    <!-- 可排序列表 -->
                                    <TextBlock Text="可排序列表：" FontWeight="Medium" Margin="0,16,0,8"/>
                                    <ListView ItemsSource="{Binding SortableItems}"
                                              SelectedItem="{Binding SelectedSortableItem}"
                                              Height="400"
                                              dd:DragDrop.IsDragSource="True"
                                              dd:DragDrop.IsDropTarget="True"
                                              dd:DragDrop.DragHandler="{Binding}"
                                              dd:DragDrop.DropHandler="{Binding}"
                                              dd:DragDrop.UseDefaultDragAdorner="True"
                                              Background="{DynamicResource CardBackgroundFillColorDefaultBrush}">
                                        <ListView.ItemTemplate>
                                            <DataTemplate>
                                                <Border Background="{DynamicResource SystemFillColorAttentionBrush}"
                                                        BorderBrush="{DynamicResource SystemFillColorAttentionBrush}"
                                                        BorderThickness="1"
                                                        CornerRadius="4"
                                                        Padding="8"
                                                        Margin="2"
                                                        Cursor="Hand">
                                                    <Grid>
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="*"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                        </Grid.ColumnDefinitions>

                                                        <ui:SymbolIcon Grid.Column="0"
                                                                       Symbol="ArrowSort24"
                                                                       FontSize="16"
                                                                       Foreground="White"
                                                                       Margin="0,0,8,0"/>

                                                        <StackPanel Grid.Column="1">
                                                            <TextBlock Text="{Binding Name}"
                                                                       FontWeight="Medium"
                                                                       FontSize="12"
                                                                       Foreground="White"/>
                                                            <TextBlock Text="{Binding Category}"
                                                                       FontSize="10"
                                                                       Foreground="White"
                                                                       Opacity="0.8"/>
                                                        </StackPanel>

                                                        <Border Grid.Column="2"
                                                                Background="White"
                                                                CornerRadius="8"
                                                                Padding="4,2"
                                                                MinWidth="16">
                                                            <TextBlock Text="{Binding Priority}"
                                                                       FontSize="10"
                                                                       Foreground="{DynamicResource SystemFillColorAttentionBrush}"
                                                                       HorizontalAlignment="Center"/>
                                                        </Border>
                                                    </Grid>
                                                </Border>
                                            </DataTemplate>
                                        </ListView.ItemTemplate>
                                    </ListView>

                                    <!-- 说明文字 -->
                                    <TextBlock Text="💡 提示："
                                               FontWeight="Bold"
                                               FontSize="12"
                                               Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                               Margin="0,16,0,4"/>
                                    <TextBlock Text="• 从源列表拖拽项目到目标列表或排序列表"
                                               FontSize="12"
                                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                               Margin="0,0,0,2"/>
                                    <TextBlock Text="• 在任何列表内部拖拽可以重新排序"
                                               FontSize="12"
                                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                               Margin="0,0,0,2"/>
                                    <TextBlock Text="• 支持在不同列表之间相互拖拽移动项目"
                                               FontSize="12"
                                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                               TextWrapping="Wrap"/>
                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="基础拖拽代码示例"
                                Language="XAML"
                                Description="展示 gong-wpf-dragdrop 库的基础用法"
                                ShowTabs="True"
                                XamlCode="{Binding BasicXamlExample}"
                                CSharpCode="{Binding BasicCSharpExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                </StackPanel>
            </Border>

            <!-- 底部操作栏 -->
            <Border Grid.Row="2" 
                    Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Padding="16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 统计信息 -->
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock Text="{Binding InteractionCount, StringFormat='交互次数: {0}'}" 
                                   FontSize="14" 
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   Margin="0,0,20,0"/>
                        <TextBlock Text="{Binding LastAction, StringFormat='最后操作: {0}'}" 
                                   FontSize="14" 
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                    </StackPanel>

                    <!-- 操作按钮 -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <ui:Button Content="重置数据" 
                                   Command="{Binding ResetDataCommand}"
                                   Appearance="Secondary"
                                   Margin="0,0,8,0"/>
                        <ui:Button Content="重置计数" 
                                   Command="{Binding ResetCountCommand}"
                                   Appearance="Secondary"
                                   Margin="0,0,8,0"/>
                        <ui:Button Content="清除状态" 
                                   Command="{Binding ClearStatusCommand}"
                                   Appearance="Secondary"/>
                    </StackPanel>
                </Grid>
            </Border>

        </Grid>
    </ScrollViewer>
</UserControl>
