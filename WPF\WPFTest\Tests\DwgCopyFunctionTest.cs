using System;
using System.IO;
using System.Threading.Tasks;
using WPFTest.Models.DWG;
using WPFTest.ViewModels.DragDrop;
using WPFTest.ViewModels.DWG;

namespace WPFTest.Tests;

/// <summary>
/// DWG复制功能测试类
/// </summary>
/// <remarks>
/// 专门测试复制功能的文件命名和后缀处理是否正确
/// </remarks>
public class DwgCopyFunctionTest
{
    private readonly DwgManagerTabViewModel _viewModel;
    private readonly string _testDirectory;

    public DwgCopyFunctionTest(DwgManagerTabViewModel viewModel)
    {
        _viewModel = viewModel ?? throw new ArgumentNullException(nameof(viewModel));
        _testDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "DwgCopyTest");
    }

    /// <summary>
    /// 初始化测试环境
    /// </summary>
    public void InitializeTestEnvironment()
    {
        try
        {
            // 创建测试目录
            if (!Directory.Exists(_testDirectory))
            {
                Directory.CreateDirectory(_testDirectory);
                Console.WriteLine($"✅ 创建测试目录: {_testDirectory}");
            }

            // 创建测试文件
            var testFilePath = Path.Combine(_testDirectory, "原始文件.dwg");
            if (!File.Exists(testFilePath))
            {
                File.WriteAllText(testFilePath, "测试DWG文件内容");
                Console.WriteLine($"✅ 创建测试文件: {testFilePath}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 初始化测试环境失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 测试复制文件到当前目录的后缀处理
    /// </summary>
    public async Task TestCopyFileWithSuffixAsync()
    {
        try
        {
            Console.WriteLine("🧪 测试复制文件后缀处理...");

            var testFilePath = Path.Combine(_testDirectory, "原始文件.dwg");
            var testFile = DwgFileModel.FromFilePath(testFilePath);

            // 第一次复制
            await _viewModel.CopyFileCommand.ExecuteAsync(testFile);
            
            var expectedCopy1 = Path.Combine(_testDirectory, "原始文件_副本.dwg");
            if (File.Exists(expectedCopy1))
            {
                Console.WriteLine("✅ 第一次复制成功，文件名: 原始文件_副本.dwg");
            }
            else
            {
                Console.WriteLine("❌ 第一次复制失败，未找到副本文件");
                return;
            }

            // 第二次复制（应该添加数字后缀）
            await _viewModel.CopyFileCommand.ExecuteAsync(testFile);
            
            var expectedCopy2 = Path.Combine(_testDirectory, "原始文件_副本1.dwg");
            if (File.Exists(expectedCopy2))
            {
                Console.WriteLine("✅ 第二次复制成功，文件名: 原始文件_副本1.dwg");
            }
            else
            {
                Console.WriteLine("❌ 第二次复制失败，未找到带数字后缀的副本文件");
            }

            // 第三次复制（应该继续递增数字）
            await _viewModel.CopyFileCommand.ExecuteAsync(testFile);
            
            var expectedCopy3 = Path.Combine(_testDirectory, "原始文件_副本2.dwg");
            if (File.Exists(expectedCopy3))
            {
                Console.WriteLine("✅ 第三次复制成功，文件名: 原始文件_副本2.dwg");
            }
            else
            {
                Console.WriteLine("❌ 第三次复制失败，数字后缀递增有问题");
            }

            Console.WriteLine("✅ 复制文件后缀处理测试完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 复制文件后缀处理测试失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 测试复制到桌面的后缀处理
    /// </summary>
    public async Task TestCopyToDesktopWithSuffixAsync()
    {
        try
        {
            Console.WriteLine("🧪 测试复制到桌面后缀处理...");

            var testFilePath = Path.Combine(_testDirectory, "桌面测试文件.dwg");
            File.WriteAllText(testFilePath, "桌面测试内容");
            var testFile = DwgFileModel.FromFilePath(testFilePath);

            var desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);

            // 第一次复制到桌面
            await _viewModel.CopyToDesktopCommand.ExecuteAsync(testFile);
            
            var expectedDesktopCopy1 = Path.Combine(desktopPath, "桌面测试文件_副本.dwg");
            if (File.Exists(expectedDesktopCopy1))
            {
                Console.WriteLine("✅ 第一次复制到桌面成功，文件名: 桌面测试文件_副本.dwg");
            }
            else
            {
                Console.WriteLine("❌ 第一次复制到桌面失败，未找到副本文件");
                return;
            }

            // 第二次复制到桌面（应该添加数字后缀）
            await _viewModel.CopyToDesktopCommand.ExecuteAsync(testFile);
            
            var expectedDesktopCopy2 = Path.Combine(desktopPath, "桌面测试文件_副本1.dwg");
            if (File.Exists(expectedDesktopCopy2))
            {
                Console.WriteLine("✅ 第二次复制到桌面成功，文件名: 桌面测试文件_副本1.dwg");
            }
            else
            {
                Console.WriteLine("❌ 第二次复制到桌面失败，未找到带数字后缀的副本文件");
            }

            // 清理桌面测试文件
            if (File.Exists(expectedDesktopCopy1))
            {
                File.Delete(expectedDesktopCopy1);
                Console.WriteLine("🧹 清理桌面测试文件1");
            }
            if (File.Exists(expectedDesktopCopy2))
            {
                File.Delete(expectedDesktopCopy2);
                Console.WriteLine("🧹 清理桌面测试文件2");
            }

            Console.WriteLine("✅ 复制到桌面后缀处理测试完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 复制到桌面后缀处理测试失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 测试新建文件的命名
    /// </summary>
    public async Task TestCreateNewFileNamingAsync()
    {
        try
        {
            Console.WriteLine("🧪 测试新建文件命名...");

            // 确保有选中的专业文件夹
            if (_viewModel.LDwgFolderModels.Count > 0)
            {
                _viewModel.TDwgFolderModel = _viewModel.LDwgFolderModels.First();
                
                var targetDirectory = Path.Combine(_viewModel.DwgFolderPath, _viewModel.TDwgFolderModel.Name);
                
                // 记录创建前的文件数量
                var filesBefore = Directory.Exists(targetDirectory) 
                    ? Directory.GetFiles(targetDirectory, "新建图纸*.dwg").Length 
                    : 0;

                // 创建新文件
                await _viewModel.CreateNewDwgCommand.ExecuteAsync(null);

                // 检查是否创建了新文件
                if (Directory.Exists(targetDirectory))
                {
                    var filesAfter = Directory.GetFiles(targetDirectory, "新建图纸*.dwg").Length;
                    if (filesAfter > filesBefore)
                    {
                        var newFiles = Directory.GetFiles(targetDirectory, "新建图纸*.dwg");
                        var latestFile = newFiles.OrderByDescending(f => File.GetCreationTime(f)).First();
                        var fileName = Path.GetFileName(latestFile);
                        Console.WriteLine($"✅ 新建文件成功，文件名: {fileName}");
                    }
                    else
                    {
                        Console.WriteLine("❌ 新建文件失败，文件数量没有增加");
                    }
                }
                else
                {
                    Console.WriteLine("❌ 目标目录不存在");
                }
            }
            else
            {
                Console.WriteLine("⚠️ 没有可用的专业文件夹，跳过新建文件测试");
            }

            Console.WriteLine("✅ 新建文件命名测试完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 新建文件命名测试失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 运行所有复制功能测试
    /// </summary>
    public async Task RunAllTestsAsync()
    {
        Console.WriteLine("🚀 开始DWG复制功能测试...");
        Console.WriteLine(new string('=', 60));

        InitializeTestEnvironment();

        await TestCopyFileWithSuffixAsync();
        Console.WriteLine();

        await TestCopyToDesktopWithSuffixAsync();
        Console.WriteLine();

        await TestCreateNewFileNamingAsync();

        Console.WriteLine(new string('=', 60));
        Console.WriteLine("🏁 所有复制功能测试完成");
    }

    /// <summary>
    /// 清理测试数据
    /// </summary>
    public void CleanupTestData()
    {
        try
        {
            if (Directory.Exists(_testDirectory))
            {
                Directory.Delete(_testDirectory, true);
                Console.WriteLine("🧹 测试数据清理完成");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"⚠️ 清理测试数据失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 显示当前测试目录中的所有文件
    /// </summary>
    public void ShowTestFiles()
    {
        try
        {
            if (Directory.Exists(_testDirectory))
            {
                var files = Directory.GetFiles(_testDirectory, "*.dwg");
                Console.WriteLine($"📁 测试目录中的文件 ({files.Length}个):");
                foreach (var file in files)
                {
                    var fileName = Path.GetFileName(file);
                    var fileSize = new FileInfo(file).Length;
                    Console.WriteLine($"   📄 {fileName} ({fileSize} 字节)");
                }
            }
            else
            {
                Console.WriteLine("📁 测试目录不存在");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 显示测试文件失败: {ex.Message}");
        }
    }
}

/// <summary>
/// DWG复制功能测试运行器
/// </summary>
public static class DwgCopyFunctionTestRunner
{
    /// <summary>
    /// 运行复制功能测试
    /// </summary>
    /// <param name="viewModel">DwgManagerTabViewModel实例</param>
    public static async Task RunTestsAsync(DwgManagerTabViewModel viewModel)
    {
        var tester = new DwgCopyFunctionTest(viewModel);
        
        try
        {
            await tester.RunAllTestsAsync();
            tester.ShowTestFiles();
        }
        finally
        {
            tester.CleanupTestData();
        }
    }

    /// <summary>
    /// 快速测试 - 只测试复制到当前目录
    /// </summary>
    /// <param name="viewModel">DwgManagerTabViewModel实例</param>
    public static async Task QuickTestAsync(DwgManagerTabViewModel viewModel)
    {
        var tester = new DwgCopyFunctionTest(viewModel);
        
        Console.WriteLine("⚡ 快速测试模式 - 复制文件后缀处理");
        tester.InitializeTestEnvironment();
        await tester.TestCopyFileWithSuffixAsync();
        tester.ShowTestFiles();
        tester.CleanupTestData();
        Console.WriteLine("⚡ 快速测试完成");
    }
}
