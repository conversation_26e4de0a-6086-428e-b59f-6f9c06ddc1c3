using System.IO;
using System.IO.Compression;
using System.Text;
using Microsoft.Win32;
using Zylo.YLog.Runtime;
using ICSharpCode.SharpZipLib.Zip;
using ICSharpCode.SharpZipLib.Core;

namespace Zylo.WPF.Controls.YFile
{
    /// <summary>
    /// 压缩文件处理服务 - 使用 SharpZipLib 和 System.IO.Compression 双引擎，完美支持中文文件名
    /// </summary>
    public static class ArchiveService
    {
        /// <summary>
        /// YLogger日志记录器实例 - 用于调试压缩解压功能
        /// </summary>
        private static readonly YLoggerInstance _logger = YLogger.ForDebug<object>();

        /// <summary>
        /// 静态构造函数 - 注册编码提供程序以支持中文文件名
        /// </summary>
        static ArchiveService()
        {
            // 注册编码提供程序，支持GBK等编码
            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

            _logger.Info("🔧 编码提供程序已注册，SharpZipLib 已准备就绪");
        }
        /// <summary>
        /// 检查文件是否为支持的压缩格式
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否为支持的压缩格式</returns>
        public static bool IsArchiveFile(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                return false;

            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            return extension switch
            {
                ".zip" => true,
                ".rar" => true,  // 可以检测但不支持解压（需要 WinRAR）
                ".7z" => true,   // 可以检测但不支持解压（需要 7-Zip）
                _ => false
            };
        }

        /// <summary>
        /// 检查压缩库是否可用（SharpZipLib 是内置库，总是可用）
        /// </summary>
        /// <returns>是否可用</returns>
        public static bool Is360ZipInstalled()
        {
            return true; // SharpZipLib 是内置库，总是可用
        }

        /// <summary>
        /// 检查文件是否为 ZIP 格式（仅支持 ZIP 解压）
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否为 ZIP 格式</returns>
        public static bool IsZipFile(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                return false;

            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            return extension == ".zip";
        }

        /// <summary>
        /// 解压到当前目录
        /// </summary>
        /// <param name="archiveFilePath">压缩文件路径</param>
        /// <returns>是否成功</returns>
        public static async Task<bool> ExtractHereAsync(string archiveFilePath)
        {
            try
            {
                var directory = Path.GetDirectoryName(archiveFilePath);
                if (string.IsNullOrEmpty(directory))
                    return false;

                // 只支持 ZIP 文件解压
                if (!IsZipFile(archiveFilePath))
                {
                    _logger.Warning($"⚠️ 不支持的文件格式，仅支持 ZIP: {archiveFilePath}");
                    return false;
                }

                return await ExtractZipAsync(archiveFilePath, directory);
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 解压到当前目录失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 解压到指定目录（用户选择）
        /// </summary>
        /// <param name="archiveFilePath">压缩文件路径</param>
        /// <returns>是否成功</returns>
        public static async Task<bool> ExtractToAsync(string archiveFilePath)
        {
            try
            {
                // 只支持 ZIP 文件解压
                if (!IsZipFile(archiveFilePath))
                {
                    _logger.Warning($"⚠️ 不支持的文件格式，仅支持 ZIP: {archiveFilePath}");
                    return false;
                }

                var dialog = new OpenFolderDialog
                {
                    Title = "选择解压目录",
                    InitialDirectory = Path.GetDirectoryName(archiveFilePath)
                };

                if (dialog.ShowDialog() != true)
                    return false;

                return await ExtractZipAsync(archiveFilePath, dialog.FolderName);
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 解压到指定目录失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 使用 SharpZipLib 解压 ZIP 文件 - 完美支持中文文件名
        /// </summary>
        /// <param name="archiveFilePath">压缩文件路径</param>
        /// <param name="extractPath">解压目录</param>
        /// <returns>是否成功</returns>
        public static async Task<bool> ExtractZipAsync(string archiveFilePath, string extractPath)
        {
            return await Task.Run(() =>
            {
                try
                {
                    using (_logger.Monitor("SharpZipLib解压文件"))
                    {
                        _logger.Info($"🗜️ 开始使用 SharpZipLib 解压: {archiveFilePath}");
                        _logger.Info($"📂 目标目录: {extractPath}");

                        if (!File.Exists(archiveFilePath))
                        {
                            _logger.Error($"❌ 压缩文件不存在: {archiveFilePath}");
                            return false;
                        }

                        if (!Directory.Exists(extractPath))
                        {
                            _logger.Debug($"📁 创建目标目录: {extractPath}");
                            Directory.CreateDirectory(extractPath);
                        }

                        // 使用 SharpZipLib 解压，自动处理中文文件名
                        using var fileStream = new FileStream(archiveFilePath, FileMode.Open, FileAccess.Read);
                        using var zipStream = new ZipInputStream(fileStream);

                        ZipEntry entry;
                        var buffer = new byte[4096];
                        int totalFiles = 0;

                        _logger.Debug("🚀 开始 SharpZipLib 解压操作...");

                        while ((entry = zipStream.GetNextEntry()) != null)
                        {
                            var entryName = entry.Name;
                            _logger.Debug($"📄 解压文件: {entryName}");

                            // 处理目录分隔符
                            entryName = entryName.Replace('/', Path.DirectorySeparatorChar);
                            var fullPath = Path.Combine(extractPath, entryName);

                            // 确保目录存在
                            var directoryName = Path.GetDirectoryName(fullPath);
                            if (!string.IsNullOrEmpty(directoryName) && !Directory.Exists(directoryName))
                            {
                                Directory.CreateDirectory(directoryName);
                            }

                            // 如果是文件（不是目录）
                            if (!entry.IsDirectory && !string.IsNullOrEmpty(Path.GetFileName(fullPath)))
                            {
                                using var outputStream = File.Create(fullPath);
                                StreamUtils.Copy(zipStream, outputStream, new byte[4096]);
                                totalFiles++;
                            }
                        }

                        _logger.Info($"🎉 SharpZipLib 解压完成，共解压 {totalFiles} 个文件");
                        return true;
                    }
                }
                catch (Exception ex)
                {
                    _logger.Error($"🚨 SharpZipLib 解压失败: {ex.Message}", ex);
                    return false;
                }
            });
        }

        /// <summary>
        /// 使用 SharpZipLib 创建 ZIP 压缩文件 - 完美支持中文文件名
        /// </summary>
        /// <param name="filePaths">要压缩的文件路径数组</param>
        /// <param name="archivePath">压缩文件输出路径</param>
        /// <returns>是否成功</returns>
        public static async Task<bool> Create360ZipAsync(string[] filePaths, string archivePath)
        {
            return await Task.Run(() =>
            {
                try
                {
                    using (_logger.Monitor("SharpZipLib压缩文件"))
                    {
                        _logger.Info($"📦 开始使用 SharpZipLib 压缩，目标文件: {archivePath}");
                        _logger.Info($"📊 要压缩的文件数量: {filePaths?.Length ?? 0}");

                        if (filePaths == null || filePaths.Length == 0)
                        {
                            _logger.Error("❌ 没有要压缩的文件");
                            return false;
                        }

                        // 检查是否所有文件都会被跳过（比如用户选择了目标压缩文件本身）
                        var validFiles = filePaths.Where(filePath =>
                            !string.Equals(Path.GetFullPath(filePath), Path.GetFullPath(archivePath), StringComparison.OrdinalIgnoreCase))
                            .ToArray();

                        if (validFiles.Length == 0)
                        {
                            _logger.Warning("⚠️ 所有选中的文件都是目标压缩文件本身，无法压缩");
                            return false;
                        }

                        if (validFiles.Length < filePaths.Length)
                        {
                            _logger.Info($"📊 过滤后有效文件数量: {validFiles.Length}/{filePaths.Length}");
                        }

                        // 删除已存在的文件
                        if (File.Exists(archivePath))
                        {
                            _logger.Debug($"🗑️ 删除已存在的压缩文件: {archivePath}");
                            File.Delete(archivePath);
                        }

                        // 使用 SharpZipLib 创建 ZIP 压缩包
                        _logger.Debug("🏗️ 创建 SharpZipLib ZIP 压缩包...");
                        using var fileStream = File.Create(archivePath);
                        using var zipStream = new ZipOutputStream(fileStream);

                        // 设置压缩级别
                        zipStream.SetLevel(6); // 0-9, 9 being the highest compression

                        var buffer = new byte[4096];
                        int totalFiles = 0;

                        foreach (var filePath in validFiles)
                        {
                            _logger.Debug($"🔄 处理文件: {filePath}");

                            if (File.Exists(filePath))
                            {
                                // 添加文件
                                var fileName = Path.GetFileName(filePath);
                                _logger.Debug($"📄 添加文件: {fileName}");

                                var entry = new ZipEntry(fileName);
                                entry.DateTime = File.GetLastWriteTime(filePath);
                                zipStream.PutNextEntry(entry);

                                using var inputStream = File.OpenRead(filePath);
                                StreamUtils.Copy(inputStream, zipStream, new byte[4096]);
                                zipStream.CloseEntry();
                                totalFiles++;
                            }
                            else if (Directory.Exists(filePath))
                            {
                                // 添加目录中的所有文件
                                var dirName = Path.GetFileName(filePath);
                                _logger.Debug($"📁 添加目录: {dirName}");
                                totalFiles += AddDirectoryToSharpZip(zipStream, filePath, dirName, buffer);
                            }
                            else
                            {
                                _logger.Warning($"⚠️ 文件或目录不存在: {filePath}");
                            }
                        }

                        zipStream.Finish();
                        _logger.Info($"🎉 SharpZipLib 压缩完成，共压缩 {totalFiles} 个文件");
                        return true;
                    }
                }
                catch (Exception ex)
                {
                    _logger.Error($"🚨 SharpZipLib 压缩失败: {ex.Message}", ex);
                    return false;
                }
            });
        }

        /// <summary>
        /// 递归添加目录到 SharpZipLib ZIP 压缩包
        /// </summary>
        /// <param name="zipStream">ZIP 输出流</param>
        /// <param name="directoryPath">目录路径</param>
        /// <param name="entryPath">压缩包中的路径</param>
        /// <param name="buffer">缓冲区</param>
        /// <returns>添加的文件数量</returns>
        private static int AddDirectoryToSharpZip(ZipOutputStream zipStream, string directoryPath, string entryPath, byte[] buffer)
        {
            _logger.Debug($"📁 添加目录: {directoryPath} -> {entryPath}");
            int fileCount = 0;

            // 添加目录中的文件
            var files = Directory.GetFiles(directoryPath);
            _logger.Debug($"📊 目录中的文件数量: {files.Length}");

            foreach (var file in files)
            {
                var fileName = Path.GetFileName(file);
                var entryName = Path.Combine(entryPath, fileName).Replace('\\', '/');
                _logger.Debug($"📄 添加文件: {fileName} -> {entryName}");

                var entry = new ZipEntry(entryName);
                entry.DateTime = File.GetLastWriteTime(file);
                zipStream.PutNextEntry(entry);

                using var inputStream = File.OpenRead(file);
                StreamUtils.Copy(inputStream, zipStream, new byte[4096]);
                zipStream.CloseEntry();
                fileCount++;
            }

            // 递归添加子目录
            var subDirs = Directory.GetDirectories(directoryPath);
            _logger.Debug($"📊 子目录数量: {subDirs.Length}");

            foreach (var subDir in subDirs)
            {
                var dirName = Path.GetFileName(subDir);
                var subEntryPath = Path.Combine(entryPath, dirName).Replace('\\', '/');
                _logger.Debug($"🔄 递归处理子目录: {dirName}");
                fileCount += AddDirectoryToSharpZip(zipStream, subDir, subEntryPath, buffer);
            }

            return fileCount;
        }


    }
}
