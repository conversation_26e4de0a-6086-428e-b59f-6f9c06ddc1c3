using System.Collections.Concurrent;
using System.Windows;
using Wpf.Ui.Controls;
using Zylo.WPF.Extensions;

namespace Zylo.WPF.Services;

/// <summary>
/// 多窗口安全的 Snackbar 服务实现
/// </summary>
/// <remarks>
/// 🎯 核心特性：
/// - 线程安全的窗口-Snackbar映射管理
/// - 自动识别当前活动窗口
/// - 窗口关闭时自动清理资源
/// - 支持窗口特定和全局通知
/// 
/// 🔧 技术实现：
/// - 使用ConcurrentDictionary确保线程安全
/// - 监听窗口Closed事件自动清理
/// - 智能的活动窗口检测算法
/// - 降级处理机制
/// </remarks>
public class WindowSnackbarService : IWindowSnackbarService
{
    #region 私有字段

    /// <summary>
    /// 窗口到Snackbar的映射表（线程安全）
    /// </summary>
    private readonly ConcurrentDictionary<Window, Controls.ZyloSnackbar> _windowSnackbars = new();

    /// <summary>
    /// 同步锁对象
    /// </summary>
    private readonly object _lock = new();

    #endregion

    #region 注册管理

    /// <summary>
    /// 注册窗口的 Snackbar 控件
    /// </summary>
    public void RegisterSnackbar(Window window, Controls.ZyloSnackbar snackbar)
    {
        if (window == null || snackbar == null) return;

        lock (_lock)
        {
            // 如果窗口已注册，先注销旧的
            if (_windowSnackbars.ContainsKey(window))
            {
                UnregisterSnackbar(window);
            }

            // 注册新的映射
            _windowSnackbars[window] = snackbar;

            // 监听窗口关闭事件，自动清理
            window.Closed += OnWindowClosed;

            // 预热Snackbar控件
            snackbar.ApplyTemplate();
            snackbar.Measure(new Size(double.PositiveInfinity, double.PositiveInfinity));
        }
    }

    /// <summary>
    /// 注销窗口的 Snackbar 控件
    /// </summary>
    public void UnregisterSnackbar(Window window)
    {
        if (window == null) return;

        lock (_lock)
        {
            if (_windowSnackbars.TryRemove(window, out _))
            {
                // 取消事件监听
                window.Closed -= OnWindowClosed;
            }
        }
    }

    /// <summary>
    /// 窗口关闭事件处理
    /// </summary>
    private void OnWindowClosed(object? sender, EventArgs e)
    {
        if (sender is Window window)
        {
            UnregisterSnackbar(window);
        }
    }

    #endregion

    #region 成功消息

    /// <summary>
    /// 在指定窗口显示成功消息
    /// </summary>
    public void ShowSuccess(Window window, string message, string title = "操作成功", int timeout = 3000)
    {
        GetSnackbarForWindow(window)?.ShowSuccess(message, title, timeout);
    }

    /// <summary>
    /// 在当前活动窗口显示成功消息
    /// </summary>
    public void ShowSuccess(string message, string title = "操作成功", int timeout = 3000)
    {
        var activeWindow = GetActiveWindow();
        if (activeWindow != null)
        {
            ShowSuccess(activeWindow, message, title, timeout);
        }
    }

    #endregion

    #region 错误消息

    /// <summary>
    /// 在指定窗口显示错误消息
    /// </summary>
    public void ShowError(Window window, string message, string title = "操作失败", int timeout = 5000)
    {
        GetSnackbarForWindow(window)?.ShowError(message, title, timeout);
    }

    /// <summary>
    /// 在当前活动窗口显示错误消息
    /// </summary>
    public void ShowError(string message, string title = "操作失败", int timeout = 5000)
    {
        var activeWindow = GetActiveWindow();
        if (activeWindow != null)
        {
            ShowError(activeWindow, message, title, timeout);
        }
    }

    #endregion

    #region 警告消息

    /// <summary>
    /// 在指定窗口显示警告消息
    /// </summary>
    public void ShowWarning(Window window, string message, string title = "警告", int timeout = 4000)
    {
        GetSnackbarForWindow(window)?.ShowWarning(message, title, timeout);
    }

    /// <summary>
    /// 在当前活动窗口显示警告消息
    /// </summary>
    public void ShowWarning(string message, string title = "警告", int timeout = 4000)
    {
        var activeWindow = GetActiveWindow();
        if (activeWindow != null)
        {
            ShowWarning(activeWindow, message, title, timeout);
        }
    }

    #endregion

    #region 信息消息

    /// <summary>
    /// 在指定窗口显示信息消息
    /// </summary>
    public void ShowInfo(Window window, string message, string title = "提示", int timeout = 3000)
    {
        GetSnackbarForWindow(window)?.ShowInfo(message, title, timeout);
    }

    /// <summary>
    /// 在当前活动窗口显示信息消息
    /// </summary>
    public void ShowInfo(string message, string title = "提示", int timeout = 3000)
    {
        var activeWindow = GetActiveWindow();
        if (activeWindow != null)
        {
            ShowInfo(activeWindow, message, title, timeout);
        }
    }

    #endregion

    #region 自定义消息

    /// <summary>
    /// 在指定窗口显示自定义消息
    /// </summary>
    public void Show(Window window, string title, string message, SymbolRegular icon = SymbolRegular.Info24, 
                     ControlAppearance appearance = ControlAppearance.Primary, int timeout = 3000)
    {
        GetSnackbarForWindow(window)?.Show(title, message, icon, appearance, timeout);
    }

    /// <summary>
    /// 在当前活动窗口显示自定义消息
    /// </summary>
    public void Show(string title, string message, SymbolRegular icon = SymbolRegular.Info24, 
                     ControlAppearance appearance = ControlAppearance.Primary, int timeout = 3000)
    {
        var activeWindow = GetActiveWindow();
        if (activeWindow != null)
        {
            Show(activeWindow, title, message, icon, appearance, timeout);
        }
    }

    #endregion

    #region 隐藏消息

    /// <summary>
    /// 隐藏指定窗口的 Snackbar
    /// </summary>
    public void Hide(Window window)
    {
        GetSnackbarForWindow(window)?.Hide();
    }

    /// <summary>
    /// 隐藏当前活动窗口的 Snackbar
    /// </summary>
    public void Hide()
    {
        var activeWindow = GetActiveWindow();
        if (activeWindow != null)
        {
            Hide(activeWindow);
        }
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 获取指定窗口的Snackbar控件
    /// </summary>
    private Controls.ZyloSnackbar? GetSnackbarForWindow(Window window)
    {
        return window != null && _windowSnackbars.TryGetValue(window, out var snackbar) ? snackbar : null;
    }

    /// <summary>
    /// 获取当前活动窗口
    /// </summary>
    public Window? GetActiveWindow()
    {
        try
        {
            // 方法1：尝试获取当前活动窗口
            var activeWindow = Application.Current?.Windows.OfType<Window>()
                .FirstOrDefault(w => w.IsActive);

            if (activeWindow != null && _windowSnackbars.ContainsKey(activeWindow))
            {
                return activeWindow;
            }

            // 方法2：获取主窗口
            var mainWindow = Application.Current?.MainWindow;
            if (mainWindow != null && _windowSnackbars.ContainsKey(mainWindow))
            {
                return mainWindow;
            }

            // 方法3：获取第一个已注册的窗口
            return _windowSnackbars.Keys.FirstOrDefault();
        }
        catch
        {
            // 降级处理：返回第一个已注册的窗口
            return _windowSnackbars.Keys.FirstOrDefault();
        }
    }

    /// <summary>
    /// 获取已注册的窗口数量
    /// </summary>
    public int GetRegisteredWindowCount()
    {
        return _windowSnackbars.Count;
    }

    /// <summary>
    /// 检查指定窗口是否已注册Snackbar
    /// </summary>
    public bool IsWindowRegistered(Window window)
    {
        return window != null && _windowSnackbars.ContainsKey(window);
    }

    #endregion
}
