// TextBlock + TextBox 组合控件 C# 示例
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace WPFTest.ViewModels.InputControls
{
    public partial class TextBoxPageViewModel : ObservableObject
    {
        #region 组合控件属性

        /// <summary>
        /// 用户名值
        /// </summary>
        [ObservableProperty]
        private string usernameValue = "";

        /// <summary>
        /// 邮箱值
        /// </summary>
        [ObservableProperty]
        private string emailValue = "";

        /// <summary>
        /// 描述值
        /// </summary>
        [ObservableProperty]
        private string descriptionValue = "";

        /// <summary>
        /// 全名值
        /// </summary>
        [ObservableProperty]
        private string fullNameValue = "";

        /// <summary>
        /// 公司值
        /// </summary>
        [ObservableProperty]
        private string companyValue = "";

        /// <summary>
        /// 电话值
        /// </summary>
        [ObservableProperty]
        private string phoneValue = "";

        /// <summary>
        /// 地址值
        /// </summary>
        [ObservableProperty]
        private string addressValue = "";

        #endregion

        #region 组合控件命令

        /// <summary>
        /// 验证表单命令
        /// </summary>
        [RelayCommand]
        private void ValidateForm()
        {
            var errors = new List<string>();

            // 验证用户名
            if (string.IsNullOrWhiteSpace(UsernameValue))
                errors.Add("用户名不能为空");
            else if (UsernameValue.Length < 3)
                errors.Add("用户名至少需要3个字符");

            // 验证邮箱
            if (!string.IsNullOrWhiteSpace(EmailValue))
            {
                if (!IsValidEmail(EmailValue))
                    errors.Add("邮箱格式不正确");
            }

            // 验证电话
            if (!string.IsNullOrWhiteSpace(PhoneValue))
            {
                if (!IsValidPhone(PhoneValue))
                    errors.Add("电话格式不正确");
            }

            // 显示验证结果
            if (errors.Count == 0)
            {
                StatusMessage = "✅ 表单验证通过";
            }
            else
            {
                StatusMessage = $"❌ 验证失败: {string.Join(", ", errors)}";
            }

            InteractionCount++;
            LastAction = "表单验证";
        }

        /// <summary>
        /// 填充示例数据命令
        /// </summary>
        [RelayCommand]
        private void FillSampleData()
        {
            UsernameValue = "john_doe";
            EmailValue = "<EMAIL>";
            DescriptionValue = "这是一个示例用户的个人描述信息。\n包含多行文本内容。";
            FullNameValue = "张三";
            CompanyValue = "示例科技有限公司";
            PhoneValue = "+86 138 0000 0000";
            AddressValue = "北京市朝阳区示例街道123号\n示例大厦A座1001室";

            StatusMessage = "📝 已填充示例数据";
            InteractionCount++;
            LastAction = "填充示例数据";
        }

        /// <summary>
        /// 清空表单命令
        /// </summary>
        [RelayCommand]
        private void ClearForm()
        {
            UsernameValue = "";
            EmailValue = "";
            DescriptionValue = "";
            FullNameValue = "";
            CompanyValue = "";
            PhoneValue = "";
            AddressValue = "";

            StatusMessage = "🧹 表单已清空";
            InteractionCount++;
            LastAction = "清空表单";
        }

        #endregion

        #region 验证方法

        /// <summary>
        /// 验证邮箱格式
        /// </summary>
        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 验证电话格式
        /// </summary>
        private bool IsValidPhone(string phone)
        {
            // 简单的电话号码验证
            var cleanPhone = phone.Replace(" ", "").Replace("-", "").Replace("+", "");
            return cleanPhone.Length >= 10 && cleanPhone.All(char.IsDigit);
        }

        /// <summary>
        /// 格式化电话号码
        /// </summary>
        private string FormatPhone(string phone)
        {
            var cleanPhone = phone.Replace(" ", "").Replace("-", "");
            
            if (cleanPhone.StartsWith("+86"))
            {
                var number = cleanPhone.Substring(3);
                if (number.Length == 11)
                {
                    return $"+86 {number.Substring(0, 3)} {number.Substring(3, 4)} {number.Substring(7)}";
                }
            }
            
            return phone;
        }

        /// <summary>
        /// 自动格式化输入
        /// </summary>
        partial void OnPhoneValueChanged(string value)
        {
            if (!string.IsNullOrEmpty(value) && value.Length > 5)
            {
                var formatted = FormatPhone(value);
                if (formatted != value)
                {
                    PhoneValue = formatted;
                }
            }
        }

        #endregion
    }
}
