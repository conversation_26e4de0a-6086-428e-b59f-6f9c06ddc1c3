using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Diagnostics;
using System.Windows;

namespace WPFTest.ViewModels.Navigation
{
    /// <summary>
    /// 自定义样式面包屑导航示例 ViewModel
    /// </summary>
    public partial class CustomStyleBreadcrumbViewModel : ObservableObject
    {
        /// <summary>
        /// 面包屑项集合
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<BreadcrumbItemModel> breadcrumbItems = new();

        /// <summary>
        /// 是否显示图标
        /// </summary>
        [ObservableProperty]
        private bool showIcons = true;

        /// <summary>
        /// 是否显示描述
        /// </summary>
        [ObservableProperty]
        private bool showDescriptions = true;

        /// <summary>
        /// 是否紧凑模式
        /// </summary>
        [ObservableProperty]
        private bool compactMode = false;

        /// <summary>
        /// 当前样式名称
        /// </summary>
        [ObservableProperty]
        private string currentStyleName = "默认样式";

        /// <summary>
        /// 样式配置
        /// </summary>
        [ObservableProperty]
        private BreadcrumbStyleConfig styleConfig = new();

        /// <summary>
        /// 构造函数
        /// </summary>
        public CustomStyleBreadcrumbViewModel()
        {
            InitializeBreadcrumbItems();
            InitializeStyleConfig();
            Debug.WriteLine("自定义样式面包屑 ViewModel 初始化");
        }

        /// <summary>
        /// 导航到指定项命令
        /// </summary>
        [RelayCommand]
        private void NavigateToItem(BreadcrumbItemModel? item)
        {
            if (item == null) return;

            try
            {
                // 更新活动状态
                foreach (var breadcrumbItem in BreadcrumbItems)
                {
                    breadcrumbItem.IsActive = breadcrumbItem == item;
                }

                Debug.WriteLine($"导航到: {item.Title} (样式: {CurrentStyleName})");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"导航失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用默认样式命令
        /// </summary>
        [RelayCommand]
        private void ApplyDefaultStyle()
        {
            try
            {
                CurrentStyleName = "默认样式";
                ShowIcons = true;
                ShowDescriptions = false;
                CompactMode = false;

                StyleConfig.ButtonPadding = "12,6";
                StyleConfig.ButtonMargin = "2";
                StyleConfig.FontSize = 14;
                StyleConfig.SeparatorIcon = "ChevronRight";
                StyleConfig.ShowBorder = false;

                Debug.WriteLine("已应用默认样式");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"应用默认样式失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用紧凑样式命令
        /// </summary>
        [RelayCommand]
        private void ApplyCompactStyle()
        {
            try
            {
                CurrentStyleName = "紧凑样式";
                ShowIcons = false;
                ShowDescriptions = false;
                CompactMode = true;

                StyleConfig.ButtonPadding = "8,4";
                StyleConfig.ButtonMargin = "1";
                StyleConfig.FontSize = 12;
                StyleConfig.SeparatorIcon = "/";
                StyleConfig.ShowBorder = true;

                Debug.WriteLine("已应用紧凑样式");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"应用紧凑样式失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用卡片样式命令
        /// </summary>
        [RelayCommand]
        private void ApplyCardStyle()
        {
            try
            {
                CurrentStyleName = "卡片样式";
                ShowIcons = true;
                ShowDescriptions = true;
                CompactMode = false;

                StyleConfig.ButtonPadding = "10,6";
                StyleConfig.ButtonMargin = "2";
                StyleConfig.FontSize = 13;
                StyleConfig.SeparatorIcon = "ArrowForward";
                StyleConfig.ShowBorder = true;

                Debug.WriteLine("已应用卡片样式");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"应用卡片样式失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 切换图标显示命令
        /// </summary>
        [RelayCommand]
        private void ToggleIcons()
        {
            ShowIcons = !ShowIcons;
            Debug.WriteLine($"图标显示: {(ShowIcons ? "开启" : "关闭")}");
        }

        /// <summary>
        /// 切换描述显示命令
        /// </summary>
        [RelayCommand]
        private void ToggleDescriptions()
        {
            ShowDescriptions = !ShowDescriptions;
            Debug.WriteLine($"描述显示: {(ShowDescriptions ? "开启" : "关闭")}");
        }

        /// <summary>
        /// 切换紧凑模式命令
        /// </summary>
        [RelayCommand]
        private void ToggleCompactMode()
        {
            CompactMode = !CompactMode;
            if (CompactMode)
            {
                ApplyCompactStyle();
            }
            else
            {
                ApplyDefaultStyle();
            }
            Debug.WriteLine($"紧凑模式: {(CompactMode ? "开启" : "关闭")}");
        }

        /// <summary>
        /// 重置样式命令
        /// </summary>
        [RelayCommand]
        private void ResetStyle()
        {
            try
            {
                ApplyDefaultStyle();
                Debug.WriteLine("样式已重置为默认");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"重置样式失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 导出样式配置命令
        /// </summary>
        [RelayCommand]
        private void ExportStyleConfig()
        {
            try
            {
                var config = $@"
<!-- {CurrentStyleName} 配置 -->
<Style x:Key=""Custom{CurrentStyleName.Replace(" ", "")}Style"" TargetType=""ui:Button"">
    <Setter Property=""Padding"" Value=""{StyleConfig.ButtonPadding}""/>
    <Setter Property=""Margin"" Value=""{StyleConfig.ButtonMargin}""/>
    <Setter Property=""FontSize"" Value=""{StyleConfig.FontSize}""/>
    <Setter Property=""BorderThickness"" Value=""{(StyleConfig.ShowBorder ? "1" : "0")}""/>
    <!-- 其他样式属性... -->
</Style>";

                Clipboard.SetText(config);
                Debug.WriteLine($"样式配置已复制到剪贴板: {CurrentStyleName}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"导出样式配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化面包屑项
        /// </summary>
        private void InitializeBreadcrumbItems()
        {
            BreadcrumbItems.Clear();

            var items = new[]
            {
                new BreadcrumbItemModel
                {
                    Title = "首页",
                    Icon = "Home",
                    Path = "/",
                    Description = "应用程序主页",
                    IsActive = false,
                    IsNavigable = true,
                    IsLast = false,
                    CanRemove = false
                },
                new BreadcrumbItemModel
                {
                    Title = "UI 组件",
                    Icon = "Palette",
                    Path = "/ui",
                    Description = "用户界面组件库",
                    IsActive = false,
                    IsNavigable = true,
                    IsLast = false,
                    CanRemove = true
                },
                new BreadcrumbItemModel
                {
                    Title = "导航组件",
                    Icon = "Navigation",
                    Path = "/ui/navigation",
                    Description = "导航相关的UI组件",
                    IsActive = false,
                    IsNavigable = true,
                    IsLast = false,
                    CanRemove = true
                },
                new BreadcrumbItemModel
                {
                    Title = "面包屑导航",
                    Icon = "Breadcrumb",
                    Path = "/ui/navigation/breadcrumb",
                    Description = "面包屑导航组件示例",
                    IsActive = false,
                    IsNavigable = true,
                    IsLast = false,
                    CanRemove = true
                },
                new BreadcrumbItemModel
                {
                    Title = "自定义样式",
                    Icon = "Style",
                    Path = "/ui/navigation/breadcrumb/custom-style",
                    Description = "自定义样式的面包屑导航示例",
                    IsActive = true,
                    IsNavigable = false,
                    IsLast = true,
                    CanRemove = true
                }
            };

            foreach (var item in items)
            {
                BreadcrumbItems.Add(item);
            }
        }

        /// <summary>
        /// 初始化样式配置
        /// </summary>
        private void InitializeStyleConfig()
        {
            StyleConfig = new BreadcrumbStyleConfig
            {
                ButtonPadding = "12,6",
                ButtonMargin = "2",
                FontSize = 14,
                SeparatorIcon = "ChevronRight",
                ShowBorder = false,
                CornerRadius = 4,
                BackgroundColor = "Transparent",
                HoverBackgroundColor = "ControlFillColorSecondaryBrush",
                ActiveBackgroundColor = "AccentFillColorDefaultBrush"
            };
        }

        /// <summary>
        /// 获取样式预览
        /// </summary>
        public string GetStylePreview()
        {
            return $@"样式: {CurrentStyleName}
图标: {(ShowIcons ? "显示" : "隐藏")}
描述: {(ShowDescriptions ? "显示" : "隐藏")}
模式: {(CompactMode ? "紧凑" : "标准")}
字体大小: {StyleConfig.FontSize}px
内边距: {StyleConfig.ButtonPadding}
外边距: {StyleConfig.ButtonMargin}
分隔符: {StyleConfig.SeparatorIcon}
边框: {(StyleConfig.ShowBorder ? "显示" : "隐藏")}";
        }

        /// <summary>
        /// 获取当前样式的 CSS 等效代码
        /// </summary>
        public string GetCssEquivalent()
        {
            return $@".breadcrumb-button {{
    padding: {StyleConfig.ButtonPadding.Replace(",", "px ")};
    margin: {StyleConfig.ButtonMargin}px;
    font-size: {StyleConfig.FontSize}px;
    border: {(StyleConfig.ShowBorder ? "1px solid" : "none")};
    border-radius: {StyleConfig.CornerRadius}px;
    background-color: {StyleConfig.BackgroundColor};
}}

.breadcrumb-button:hover {{
    background-color: var(--{StyleConfig.HoverBackgroundColor});
}}

.breadcrumb-button.active {{
    background-color: var(--{StyleConfig.ActiveBackgroundColor});
}}";
        }
    }

    /// <summary>
    /// 面包屑样式配置
    /// </summary>
    public partial class BreadcrumbStyleConfig : ObservableObject
    {
        [ObservableProperty]
        private string buttonPadding = "12,6";

        [ObservableProperty]
        private string buttonMargin = "2";

        [ObservableProperty]
        private int fontSize = 14;

        [ObservableProperty]
        private string separatorIcon = "ChevronRight";

        [ObservableProperty]
        private bool showBorder = false;

        [ObservableProperty]
        private int cornerRadius = 4;

        [ObservableProperty]
        private string backgroundColor = "Transparent";

        [ObservableProperty]
        private string hoverBackgroundColor = "ControlFillColorSecondaryBrush";

        [ObservableProperty]
        private string activeBackgroundColor = "AccentFillColorDefaultBrush";
    }
}
