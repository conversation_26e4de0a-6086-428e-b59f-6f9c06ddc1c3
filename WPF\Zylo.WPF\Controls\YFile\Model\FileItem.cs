using System.ComponentModel;

namespace Zylo.WPF.Controls.YFile.Model;

/// <summary>
/// 文件项目模型
/// </summary>
public class FileItem : INotifyPropertyChanged
{
    private string _name = string.Empty;
    private string _fullPath = string.Empty;
    private string _icon = "📄";
    private string _type = string.Empty;
    private string _sizeText = string.Empty;
    private string _modifiedDate = string.Empty;
    private bool _isSelected;

    /// <summary>
    /// 文件名称
    /// </summary>
    public string Name
    {
        get => _name;
        set
        {
            _name = value;
            OnPropertyChanged(nameof(Name));
        }
    }

    /// <summary>
    /// 完整路径
    /// </summary>
    public string FullPath
    {
        get => _fullPath;
        set
        {
            _fullPath = value;
            OnPropertyChanged(nameof(FullPath));
        }
    }

    /// <summary>
    /// 图标
    /// </summary>
    public string Icon
    {
        get => _icon;
        set
        {
            _icon = value;
            OnPropertyChanged(nameof(Icon));
        }
    }

    /// <summary>
    /// 文件类型
    /// </summary>
    public string Type
    {
        get => _type;
        set
        {
            _type = value;
            OnPropertyChanged(nameof(Type));
        }
    }

    /// <summary>
    /// 文件大小文本
    /// </summary>
    public string SizeText
    {
        get => _sizeText;
        set
        {
            _sizeText = value;
            OnPropertyChanged(nameof(SizeText));
        }
    }

    /// <summary>
    /// 修改日期
    /// </summary>
    public string ModifiedDate
    {
        get => _modifiedDate;
        set
        {
            _modifiedDate = value;
            OnPropertyChanged(nameof(ModifiedDate));
        }
    }

    /// <summary>
    /// 是否选中
    /// </summary>
    public bool IsSelected
    {
        get => _isSelected;
        set
        {
            _isSelected = value;
            OnPropertyChanged(nameof(IsSelected));
        }
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged(string propertyName)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
} 