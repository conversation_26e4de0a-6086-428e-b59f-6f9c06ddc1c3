<!-- Border 高级用法示例 -->
<StackPanel Margin="20" Spacing="15">

    <!-- 渐变背景 Border -->
    <GroupBox Header="渐变背景 Border" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <!-- 线性渐变 -->
            <Border BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Padding="16"
                    Margin="8">
                <Border.Background>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#FF6B73FF" Offset="0"/>
                        <GradientStop Color="#FF9DFFAD" Offset="1"/>
                    </LinearGradientBrush>
                </Border.Background>
                <TextBlock Text="线性渐变" 
                           Foreground="White"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"/>
            </Border>

            <!-- 径向渐变 -->
            <Border BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Padding="16"
                    Margin="8">
                <Border.Background>
                    <RadialGradientBrush>
                        <GradientStop Color="#FFFF6B6B" Offset="0"/>
                        <GradientStop Color="#FF4ECDC4" Offset="1"/>
                    </RadialGradientBrush>
                </Border.Background>
                <TextBlock Text="径向渐变" 
                           Foreground="White"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"/>
            </Border>
        </WrapPanel>
    </GroupBox>

    <!-- 虚线边框 Border -->
    <GroupBox Header="虚线边框 Border" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <Border Background="{DynamicResource ControlFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource AccentFillColorDefaultBrush}"
                    BorderThickness="2"
                    CornerRadius="4"
                    Padding="16"
                    Margin="8">
                <Border.Style>
                    <Style TargetType="Border">
                        <Setter Property="BorderDashArray" Value="5,3"/>
                    </Style>
                </Border.Style>
                <TextBlock Text="虚线边框" 
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"/>
            </Border>

            <Border Background="{DynamicResource ControlFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource AccentFillColorDefaultBrush}"
                    BorderThickness="3"
                    CornerRadius="4"
                    Padding="16"
                    Margin="8">
                <Border.Style>
                    <Style TargetType="Border">
                        <Setter Property="BorderDashArray" Value="10,5,2,5"/>
                    </Style>
                </Border.Style>
                <TextBlock Text="点划线边框" 
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"/>
            </Border>
        </WrapPanel>
    </GroupBox>

    <!-- 动画 Border -->
    <GroupBox Header="动画 Border" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <!-- 鼠标悬停缩放动画 -->
            <Border Background="{DynamicResource ControlFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource AccentFillColorDefaultBrush}"
                    BorderThickness="2"
                    CornerRadius="8"
                    Padding="16"
                    Margin="8"
                    Cursor="Hand"
                    x:Name="ScaleBorder">
                <Border.RenderTransform>
                    <ScaleTransform/>
                </Border.RenderTransform>
                <Border.RenderTransformOrigin>
                    <Point X="0.5" Y="0.5"/>
                </Border.RenderTransformOrigin>
                <Border.Triggers>
                    <EventTrigger RoutedEvent="MouseEnter">
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation Storyboard.TargetProperty="(Border.RenderTransform).(ScaleTransform.ScaleX)"
                                                 To="1.1" Duration="0:0:0.2"/>
                                <DoubleAnimation Storyboard.TargetProperty="(Border.RenderTransform).(ScaleTransform.ScaleY)"
                                                 To="1.1" Duration="0:0:0.2"/>
                            </Storyboard>
                        </BeginStoryboard>
                    </EventTrigger>
                    <EventTrigger RoutedEvent="MouseLeave">
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation Storyboard.TargetProperty="(Border.RenderTransform).(ScaleTransform.ScaleX)"
                                                 To="1.0" Duration="0:0:0.2"/>
                                <DoubleAnimation Storyboard.TargetProperty="(Border.RenderTransform).(ScaleTransform.ScaleY)"
                                                 To="1.0" Duration="0:0:0.2"/>
                            </Storyboard>
                        </BeginStoryboard>
                    </EventTrigger>
                </Border.Triggers>
                <TextBlock Text="悬停缩放" 
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"/>
            </Border>

            <!-- 颜色变化动画 -->
            <Border x:Name="ColorBorder"
                    Background="{DynamicResource ControlFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                    BorderThickness="2"
                    CornerRadius="8"
                    Padding="16"
                    Margin="8"
                    Cursor="Hand">
                <Border.Triggers>
                    <EventTrigger RoutedEvent="MouseEnter">
                        <BeginStoryboard>
                            <Storyboard>
                                <ColorAnimation Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                To="#FF6B73FF" Duration="0:0:0.3"/>
                            </Storyboard>
                        </BeginStoryboard>
                    </EventTrigger>
                    <EventTrigger RoutedEvent="MouseLeave">
                        <BeginStoryboard>
                            <Storyboard>
                                <ColorAnimation Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                                To="LightGray" Duration="0:0:0.3"/>
                            </Storyboard>
                        </BeginStoryboard>
                    </EventTrigger>
                </Border.Triggers>
                <TextBlock Text="颜色变化" 
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"/>
            </Border>
        </WrapPanel>
    </GroupBox>

    <!-- 复杂布局 Border -->
    <GroupBox Header="复杂布局 Border" Padding="15">
        <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                BorderThickness="1"
                CornerRadius="12"
                Padding="20"
                Margin="8">
            <Border.Effect>
                <DropShadowEffect Color="Black" 
                                  Opacity="0.1" 
                                  ShadowDepth="4" 
                                  BlurRadius="12"/>
            </Border.Effect>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- 标题 -->
                <TextBlock Grid.Row="0" 
                           Text="卡片式 Border" 
                           FontSize="18" 
                           FontWeight="Bold"
                           Margin="0,0,0,12"/>
                
                <!-- 内容 -->
                <TextBlock Grid.Row="1" 
                           Text="这是一个复杂的 Border 布局示例，包含标题、内容和操作按钮。使用了阴影效果和圆角设计。"
                           TextWrapping="Wrap"
                           Margin="0,0,0,16"/>
                
                <!-- 操作按钮 -->
                <StackPanel Grid.Row="2" 
                            Orientation="Horizontal" 
                            HorizontalAlignment="Right">
                    <Button Content="取消" 
                            Margin="0,0,8,0" 
                            Padding="12,6"/>
                    <Button Content="确定" 
                            Padding="12,6"/>
                </StackPanel>
            </Grid>
        </Border>
    </GroupBox>

</StackPanel>
