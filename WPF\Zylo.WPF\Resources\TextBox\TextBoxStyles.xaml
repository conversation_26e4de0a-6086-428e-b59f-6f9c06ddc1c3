<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!--  TextBox 基础样式  -->
    <Style TargetType="ui:TextBox" x:Key="TextBoxBaseStyle">
        <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}" />
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}" />
        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Padding" Value="8" />
        <Setter Property="Margin" Value="4" />
        <Setter Property="FontSize" Value="14" />
        <Setter Property="FontFamily" Value="{DynamicResource ContentControlThemeFontFamily}" />
        <Setter Property="MinHeight" Value="32" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="HorizontalContentAlignment" Value="Left" />

        <!--  添加鼠标悬停和聚焦效果  -->
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorSecondaryBrush}" />
                <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}" />
            </Trigger>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="BorderBrush" Value="{DynamicResource SystemAccentColorPrimaryBrush}" />
                <Setter Property="Background" Value="{DynamicResource ControlFillColorInputActiveBrush}" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Opacity" Value="0.5" />
                <Setter Property="Background" Value="{DynamicResource ControlFillColorDisabledBrush}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--  TextBox 标准样式  -->
    <Style
        BasedOn="{StaticResource TextBoxBaseStyle}"
        TargetType="ui:TextBox"
        x:Key="TextBoxStyle">
        <!--  标准样式的特定设置  -->
        <Setter Property="Width" Value="200" />
    </Style>

    <!--  TextBox 小型样式  -->
    <Style
        BasedOn="{StaticResource TextBoxBaseStyle}"
        TargetType="ui:TextBox"
        x:Key="SmallTextBoxStyle">
        <Setter Property="Padding" Value="6" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="MinHeight" Value="28" />
        <Setter Property="Width" Value="150" />
    </Style>

    <!--  TextBox 大型样式  -->
    <Style
        BasedOn="{StaticResource TextBoxBaseStyle}"
        TargetType="ui:TextBox"
        x:Key="LargeTextBoxStyle">
        <Setter Property="Padding" Value="12" />
        <Setter Property="FontSize" Value="16" />
        <Setter Property="MinHeight" Value="40" />
        <Setter Property="Width" Value="250" />
    </Style>

    <!--  TextBox 透明样式  -->
    <Style
        BasedOn="{StaticResource TextBoxBaseStyle}"
        TargetType="ui:TextBox"
        x:Key="TransparentTextBoxStyle">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}" />
        <Setter Property="BorderThickness" Value="0,0,0,1" />
    </Style>

    <!--  TextBox 圆角样式  -->
    <Style
        BasedOn="{StaticResource TextBoxBaseStyle}"
        TargetType="ui:TextBox"
        x:Key="RoundedTextBoxStyle">
        <Setter Property="Padding" Value="16,8" />
    </Style>

    <!--  TextBox 多行样式  -->
    <Style
        BasedOn="{StaticResource TextBoxBaseStyle}"
        TargetType="ui:TextBox"
        x:Key="MultilineTextBoxStyle">
        <Setter Property="TextWrapping" Value="Wrap" />
        <Setter Property="AcceptsReturn" Value="True" />
        <Setter Property="VerticalScrollBarVisibility" Value="Auto" />
        <Setter Property="HorizontalScrollBarVisibility" Value="Disabled" />
        <Setter Property="MinHeight" Value="100" />
        <Setter Property="Height" Value="150" />
        <Setter Property="MinWidth" Value="400" />
        <Setter Property="MaxWidth" Value="600" />
        <Setter Property="VerticalContentAlignment" Value="Top" />
        <Setter Property="Padding" Value="12" />
        <Setter Property="FontSize" Value="14" />
    </Style>

    <!--  TextBox 搜索框样式  -->
    <Style
        BasedOn="{StaticResource TextBoxBaseStyle}"
        TargetType="ui:TextBox"
        x:Key="SearchTextBoxStyle">
        <Setter Property="Padding" Value="12,8" />
        <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}" />
        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Width" Value="250" />
        <Style.Triggers>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="BorderBrush" Value="{DynamicResource SystemAccentColorPrimaryBrush}" />
                <Setter Property="Background" Value="{DynamicResource ControlFillColorInputActiveBrush}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--  TextBox 错误状态样式  -->
    <Style
        BasedOn="{StaticResource TextBoxBaseStyle}"
        TargetType="ui:TextBox"
        x:Key="ErrorTextBoxStyle">
        <Setter Property="BorderBrush" Value="{DynamicResource SystemFillColorCriticalBrush}" />
        <Setter Property="BorderThickness" Value="2" />
        <Style.Triggers>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="BorderBrush" Value="{DynamicResource SystemFillColorCriticalBrush}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--  TextBox 成功状态样式  -->
    <Style
        BasedOn="{StaticResource TextBoxBaseStyle}"
        TargetType="ui:TextBox"
        x:Key="SuccessTextBoxStyle">
        <Setter Property="BorderBrush" Value="{DynamicResource SystemFillColorSuccessBrush}" />
        <Setter Property="BorderThickness" Value="2" />
        <Style.Triggers>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="BorderBrush" Value="{DynamicResource SystemFillColorSuccessBrush}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--  ================================  -->
    <!--  🎨 TextBlock + TextBox 组合样式  -->
    <!--  ================================  -->

    <!--  组合控件容器样式  -->
    <Style TargetType="Border" x:Key="LabelTextBoxContainerStyle">
        <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}" />
        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Padding" Value="12" />
        <Setter Property="Margin" Value="4" />
        <Setter Property="MinHeight" Value="40" />
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorSecondaryBrush}" />
                <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--  标签文本样式  -->
    <Style TargetType="TextBlock" x:Key="LabelTextStyle">
        <Setter Property="FontSize" Value="14" />
        <Setter Property="FontWeight" Value="Medium" />
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}" />
        <Setter Property="VerticalAlignment" Value="Top" />
        <Setter Property="TextWrapping" Value="Wrap" />
        <Setter Property="Margin" Value="0,0,12,0" />
        <Setter Property="MinWidth" Value="80" />
        <Setter Property="MaxWidth" Value="200" />
    </Style>

    <!--  组合控件中的TextBox样式  -->
    <Style TargetType="ui:TextBox" x:Key="LabelTextBoxInputStyle">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}" />
        <Setter Property="FontSize" Value="14" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="HorizontalContentAlignment" Value="Left" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="Margin" Value="0" />
    </Style>

    <!--  现代化卡片样式组合控件  -->
    <Style TargetType="Border" x:Key="ModernLabelTextBoxStyle">
        <Setter Property="Background" Value="{DynamicResource CardBackgroundFillColorDefaultBrush}" />
        <Setter Property="BorderBrush" Value="{DynamicResource CardStrokeColorDefaultBrush}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="CornerRadius" Value="8" />
        <Setter Property="Padding" Value="16" />
        <Setter Property="Margin" Value="4" />
        <Setter Property="MinHeight" Value="60" />
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect
                    BlurRadius="8"
                    Color="Black"
                    Direction="270"
                    Opacity="0.1"
                    ShadowDepth="2" />
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="BorderBrush" Value="{DynamicResource SystemAccentColorSecondaryBrush}" />
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect
                            BlurRadius="12"
                            Color="Black"
                            Direction="270"
                            Opacity="0.15"
                            ShadowDepth="4" />
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--  现代化标签样式  -->
    <Style TargetType="TextBlock" x:Key="ModernLabelStyle">
        <Setter Property="FontSize" Value="13" />
        <Setter Property="FontWeight" Value="SemiBold" />
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorSecondaryBrush}" />
        <Setter Property="VerticalAlignment" Value="Top" />
        <Setter Property="HorizontalAlignment" Value="Left" />
        <Setter Property="TextAlignment" Value="Left" />
        <Setter Property="TextWrapping" Value="Wrap" />
        <Setter Property="Margin" Value="0,0,16,4" />
        <Setter Property="MinWidth" Value="100" />
        <Setter Property="MaxWidth" Value="180" />
    </Style>

    <!--  现代化输入框样式  -->
    <Style TargetType="ui:TextBox" x:Key="ModernInputStyle">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}" />
        <Setter Property="FontSize" Value="15" />
        <Setter Property="FontWeight" Value="Normal" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="HorizontalContentAlignment" Value="Left" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="Margin" Value="0" />
    </Style>


    <!--  文本块样式 - 适应主题  -->
    <Style TargetType="TextBlock" x:Key="YThemeTextStyle">
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}" />
    </Style>

    <!--  标题文本样式  -->
    <Style
        BasedOn="{StaticResource YThemeTextStyle}"
        TargetType="TextBlock"
        x:Key="YHeaderTextStyle">
        <Setter Property="FontSize" Value="16" />
        <Setter Property="FontWeight" Value="SemiBold" />
    </Style>

</ResourceDictionary>