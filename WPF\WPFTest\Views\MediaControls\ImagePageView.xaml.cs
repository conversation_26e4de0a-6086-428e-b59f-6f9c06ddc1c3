using System.Windows;
using System.Windows.Controls;
using WPFTest.ViewModels.MediaControls;

namespace WPFTest.Views.MediaControls
{
    /// <summary>
    /// ImagePageView.xaml 的交互逻辑
    /// Image 控件示例页面的视图
    /// </summary>
    public partial class ImagePageView : UserControl
    {
        /// <summary>
        /// 初始化 ImagePageView
        /// </summary>
        public ImagePageView()
        {
            InitializeComponent();
            
            // 设置 DataContext 为对应的 ViewModel
            // DataContext = new ImagePageViewModel();
        }

       
    }
}
