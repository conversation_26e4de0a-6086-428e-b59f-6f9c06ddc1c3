<UserControl x:Class="WPFTest.Views.ButtonControls.SplitButtonPageView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:codeExample="clr-namespace:Zylo.WPF.Controls.CodeExample;assembly=Zylo.WPF"
             xmlns:mvvm="http://prismlibrary.com/"
             mvvm:ViewModelLocator.AutoWireViewModel="True"
             mc:Ignorable="d" 
             d:DesignHeight="1800" d:DesignWidth="1200"
             Background="{DynamicResource ApplicationBackgroundBrush}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 页面标题区域 -->
        <Border Grid.Row="0" 
                Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                BorderThickness="1"
                CornerRadius="8"
                Margin="24,24,24,16"
                Padding="32">
            <StackPanel>
                <TextBlock Text="🎨 SplitButton 控件展示" 
                           FontSize="32" 
                           FontWeight="Bold" 
                           Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                           Margin="0,0,0,8"/>
                <TextBlock Text="展示 SplitButton 控件的各种样式和交互效果，包括主要操作和下拉菜单选项" 
                           FontSize="16" 
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                           TextWrapping="Wrap"/>
                
                <!-- 状态显示 -->
                <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                        BorderBrush="{DynamicResource ControlStrokeColorSecondaryBrush}"
                        BorderThickness="1"
                        CornerRadius="6"
                        Padding="16"
                        Margin="0,16,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="状态消息" FontWeight="Medium" FontSize="12" Margin="0,0,0,4"/>
                            <TextBlock Text="{Binding StatusMessage}" 
                                       FontSize="14" 
                                       Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                       TextWrapping="Wrap"/>
                        </StackPanel>
                        
                        <StackPanel Grid.Column="1" Margin="16,0">
                            <TextBlock Text="最后操作" FontWeight="Medium" FontSize="12" Margin="0,0,0,4"/>
                            <TextBlock Text="{Binding LastAction}" 
                                       FontSize="14" 
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                        </StackPanel>
                        
                        <StackPanel Grid.Column="2">
                            <TextBlock Text="交互次数" FontWeight="Medium" FontSize="12" Margin="0,0,0,4"/>
                            <TextBlock Text="{Binding InteractionCount}" 
                                       FontSize="18" 
                                       FontWeight="Bold"
                                       Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                        </StackPanel>
                    </Grid>
                </Border>
            </StackPanel>
        </Border>

        <!-- 主内容区域 -->
        <Border Grid.Row="1" 
                Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                BorderThickness="1"
                CornerRadius="8"
                Margin="24,0,24,16"
                Padding="32">
            
            <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
                <StackPanel>
                    
                    <!-- 基础功能 -->
                    <ui:CardExpander Header="🎯 基础功能" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 SplitButton 的基本用法，包括主要操作和下拉菜单选项"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>
                            
                            <!-- 基础控件展示区域 -->
                            <WrapPanel>
                                <!-- 基础 SplitButton -->
                                <ui:SplitButton Content="保存" 
                                                Command="{Binding HandlePrimaryActionCommand}"
                                                CommandParameter="保存"
                                                IsEnabled="{Binding IsSplitButtonEnabled}"
                                                Margin="8">
                                    <ui:SplitButton.Icon>
                                        <ui:SymbolIcon Symbol="Save24"/>
                                    </ui:SplitButton.Icon>
                                    <ui:SplitButton.Flyout>
                                        <ContextMenu>
                                            <MenuItem Header="保存" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="保存">
                                                <MenuItem.Icon>
                                                    <ui:SymbolIcon Symbol="Save24"/>
                                                </MenuItem.Icon>
                                            </MenuItem>
                                            <MenuItem Header="另存为" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="另存为">
                                                <MenuItem.Icon>
                                                    <ui:SymbolIcon Symbol="DocumentSave24"/>
                                                </MenuItem.Icon>
                                            </MenuItem>
                                            <Separator/>
                                            <MenuItem Header="保存所有" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="保存所有">
                                                <MenuItem.Icon>
                                                    <ui:SymbolIcon Symbol="Save24"/>
                                                </MenuItem.Icon>
                                            </MenuItem>
                                        </ContextMenu>
                                    </ui:SplitButton.Flyout>
                                </ui:SplitButton>

                                <!-- 不同外观的 SplitButton -->
                                <ui:SplitButton Content="发送" 
                                                Command="{Binding HandlePrimaryActionCommand}"
                                                CommandParameter="发送"
                                                Appearance="Primary"
                                                IsEnabled="{Binding IsSplitButtonEnabled}"
                                                Margin="8">
                                    <ui:SplitButton.Icon>
                                        <ui:SymbolIcon Symbol="Send24"/>
                                    </ui:SplitButton.Icon>
                                    <ui:SplitButton.Flyout>
                                        <ContextMenu>
                                            <MenuItem Header="立即发送" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="立即发送">
                                                <MenuItem.Icon>
                                                    <ui:SymbolIcon Symbol="Send24"/>
                                                </MenuItem.Icon>
                                            </MenuItem>
                                            <MenuItem Header="定时发送" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="定时发送">
                                                <MenuItem.Icon>
                                                    <ui:SymbolIcon Symbol="Clock24"/>
                                                </MenuItem.Icon>
                                            </MenuItem>
                                            <MenuItem Header="草稿箱" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="草稿箱">
                                                <MenuItem.Icon>
                                                    <ui:SymbolIcon Symbol="Document24"/>
                                                </MenuItem.Icon>
                                            </MenuItem>
                                        </ContextMenu>
                                    </ui:SplitButton.Flyout>
                                </ui:SplitButton>

                                <!-- 危险操作 SplitButton -->
                                <ui:SplitButton Content="删除" 
                                                Command="{Binding HandlePrimaryActionCommand}"
                                                CommandParameter="删除"
                                                Appearance="Danger"
                                                IsEnabled="{Binding IsSplitButtonEnabled}"
                                                Margin="8">
                                    <ui:SplitButton.Icon>
                                        <ui:SymbolIcon Symbol="Delete24"/>
                                    </ui:SplitButton.Icon>
                                    <ui:SplitButton.Flyout>
                                        <ContextMenu>
                                            <MenuItem Header="删除" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="删除">
                                                <MenuItem.Icon>
                                                    <ui:SymbolIcon Symbol="Delete24"/>
                                                </MenuItem.Icon>
                                            </MenuItem>
                                            <MenuItem Header="移到回收站" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="移到回收站">
                                                <MenuItem.Icon>
                                                    <ui:SymbolIcon Symbol="Delete24"/>
                                                </MenuItem.Icon>
                                            </MenuItem>
                                            <MenuItem Header="永久删除" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="永久删除">
                                                <MenuItem.Icon>
                                                    <ui:SymbolIcon Symbol="Delete24"/>
                                                </MenuItem.Icon>
                                            </MenuItem>
                                        </ContextMenu>
                                    </ui:SplitButton.Flyout>
                                </ui:SplitButton>
                            </WrapPanel>

                            <!-- 控制按钮 -->
                            <StackPanel Orientation="Horizontal" Margin="0,16,0,0">
                                <ui:Button Content="切换启用状态"
                                           Command="{Binding ToggleEnabledCommand}"
                                           Appearance="Secondary"
                                           Margin="0,0,8,0"/>
                                <ui:Button Content="清除状态" 
                                           Command="{Binding ClearStatusCommand}"
                                           Appearance="Secondary"/>
                            </StackPanel>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="基础代码示例"
                                Language="XAML"
                                Description="展示 SplitButton 的基本用法和主要功能"
                                ShowTabs="True"
                                XamlCode="{Binding BasicXamlExample}"
                                CSharpCode="{Binding BasicCSharpExample}"
                                IsExpanded="False"
                                Margin="0,16,0,0"/>
                        </StackPanel>
                    </ui:CardExpander>

                </StackPanel>
            </ScrollViewer>
        </Border>

        <!-- 底部操作栏 -->
        <Border Grid.Row="2" 
                Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                BorderThickness="1"
                CornerRadius="8"
                Margin="24,0,24,24"
                Padding="24">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="当前选择:" FontWeight="Medium" VerticalAlignment="Center" Margin="0,0,8,0"/>
                    <TextBlock Text="{Binding SelectedOption}" 
                               FontWeight="Bold"
                               Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                               VerticalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="SplitButton 状态:" FontWeight="Medium" VerticalAlignment="Center" Margin="0,0,8,0"/>
                    <TextBlock Text="{Binding IsSplitButtonEnabled}"
                               FontWeight="Bold"
                               Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                               VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>
