<!-- 现代化按钮样式示例 -->
<StackPanel Margin="20" Spacing="15">
    
    <!-- 现代化按钮 -->
    <GroupBox Header="现代化按钮样式" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <ui:Button Style="{StaticResource ModernButtonStyle}"
                       Content="现代化按钮"
                       Command="{Binding ButtonClickCommand}"
                       CommandParameter="现代化"
                       Margin="4"/>
            
            <ui:Button Style="{StaticResource ModernButtonStyle}"
                       Command="{Binding ButtonClickCommand}"
                       CommandParameter="现代化图标"
                       Margin="4">
                <StackPanel Orientation="Horizontal">
                    <ui:SymbolIcon Symbol="Sparkle24" FontSize="16" Margin="0,0,6,0"/>
                    <TextBlock Text="带图标"/>
                </StackPanel>
            </ui:Button>
            
            <ui:Button Style="{StaticResource ModernButtonStyle}"
                       Content="禁用状态"
                       IsEnabled="False"
                       Margin="4"/>
        </WrapPanel>
    </GroupBox>

    <!-- 渐变按钮 -->
    <GroupBox Header="渐变按钮样式" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <ui:Button Style="{StaticResource GradientButtonStyle}"
                       Content="渐变按钮"
                       Command="{Binding ButtonClickCommand}"
                       CommandParameter="渐变"
                       Margin="4"/>
            
            <ui:Button Style="{StaticResource GradientButtonStyle}"
                       Command="{Binding ButtonClickCommand}"
                       CommandParameter="渐变图标"
                       Margin="4">
                <StackPanel Orientation="Horizontal">
                    <ui:SymbolIcon Symbol="ColorBackground24" FontSize="16" Margin="0,0,6,0"/>
                    <TextBlock Text="彩色渐变"/>
                </StackPanel>
            </ui:Button>
        </WrapPanel>
    </GroupBox>

    <!-- WPF-UI 标准样式 -->
    <GroupBox Header="WPF-UI 标准样式" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <ui:Button Content="默认样式"
                       Command="{Binding ButtonClickCommand}"
                       CommandParameter="默认"
                       Margin="4"/>
            
            <ui:Button Content="主要按钮"
                       Appearance="Primary"
                       Command="{Binding ButtonClickCommand}"
                       CommandParameter="主要"
                       Margin="4"/>
            
            <ui:Button Content="次要按钮"
                       Appearance="Secondary"
                       Command="{Binding ButtonClickCommand}"
                       CommandParameter="次要"
                       Margin="4"/>
            
            <ui:Button Content="危险按钮"
                       Appearance="Danger"
                       Command="{Binding ButtonClickCommand}"
                       CommandParameter="危险"
                       Margin="4"/>
        </WrapPanel>
    </GroupBox>

</StackPanel>
