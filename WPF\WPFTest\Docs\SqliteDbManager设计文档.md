# SQLite 数据库管理器设计文档

## 📋 项目概述

基于 `DwgFileTypeEditorViewModel` 的设计模式，创建了一个完整的 SQLite 数据库文件管理系统。该系统支持默认文件夹配置、指定路径扫描、连接状态监控等功能。

## 🏗️ 架构设计

### 1. 设计原则
- **参考现有模式**：完全参考 `DwgFileTypeEditorViewModel` 的架构
- **MVVM 模式**：使用 CommunityToolkit.Mvvm 实现现代化 MVVM
- **服务分离**：业务逻辑与 UI 完全分离
- **事件驱动**：基于事件的状态更新机制
- **异步优先**：所有 I/O 操作均为异步

### 2. 核心组件

#### 📁 Models (数据模型)
- **SqliteDbFileModel.cs** - SQLite 数据库文件信息模型
- **SqliteManagerSettings.cs** - 管理器配置设置模型

#### 🔧 Services (服务层)
- **ISqliteManagerService.cs** - 服务接口定义
- **SqliteManagerService.cs** - 服务具体实现

#### 🎨 ViewModels (视图模型)
- **SqliteDbManagerViewModel.cs** - 主要的 ViewModel

#### 🖼️ Views (用户界面)
- **SqliteDbManagerView.xaml** - 主界面 XAML
- **SqliteDbManagerView.xaml.cs** - 代码后置和转换器

## 🚀 核心功能

### 1. 文件夹管理
```csharp
// 默认文件夹配置
string GetDefaultSqliteFolder();
Task<bool> SetDefaultSqliteFolderAsync(string folderPath);

// 文件夹扫描
Task<IList<SqliteDbFileModel>> ScanFolderAsync(string folderPath, bool includeSubfolders, string filePattern);
Task<IList<SqliteDbFileModel>> ScanDefaultFolderAsync(bool includeSubfolders);
```

### 2. 数据库文件管理
```csharp
// CRUD 操作
Task<IList<SqliteDbFileModel>> GetAllDatabaseFilesAsync(bool includeDisabled);
Task<bool> AddDatabaseFileAsync(SqliteDbFileModel dbFile);
Task<bool> UpdateDatabaseFileAsync(SqliteDbFileModel dbFile);
Task<bool> RemoveDatabaseFileAsync(int id);
Task<bool> AddDatabaseFilesBatchAsync(IList<SqliteDbFileModel> dbFiles);
```

### 3. 连接状态监控
```csharp
// 连接测试
Task<SqliteConnectionResult> TestConnectionAsync(string filePath);
Task<SqliteConnectionResult> TestConnectionAsync(SqliteDbFileModel dbFile);
Task<Dictionary<int, SqliteConnectionResult>> TestConnectionsBatchAsync(IList<SqliteDbFileModel> dbFiles);

// 数据库信息获取
Task<SqliteDatabaseInfo?> GetDatabaseInfoAsync(string filePath);
Task<bool> RefreshDatabaseInfoAsync(SqliteDbFileModel dbFile);
```

### 4. 配置管理
```csharp
// 配置持久化
Task<SqliteManagerConfig> GetConfigurationAsync();
Task<bool> SaveConfigurationAsync(SqliteManagerConfig config);
Task<bool> ResetToDefaultConfigAsync();
```

## 📊 数据模型详解

### SqliteDbFileModel
```csharp
public partial class SqliteDbFileModel : ObservableValidator
{
    // 基本属性
    [ObservableProperty] public partial int Id { get; set; }
    [ObservableProperty] public partial string FilePath { get; set; }
    [ObservableProperty] public partial string FileName { get; set; }
    [ObservableProperty] public partial string DisplayName { get; set; }
    
    // 文件信息
    [ObservableProperty] public partial long FileSize { get; set; }
    [ObservableProperty] public partial DateTime CreatedDate { get; set; }
    [ObservableProperty] public partial DateTime ModifiedDate { get; set; }
    
    // 连接状态
    [ObservableProperty] public partial SqliteConnectionStatus ConnectionStatus { get; set; }
    [ObservableProperty] public partial string ConnectionError { get; set; }
    
    // 数据库信息
    [ObservableProperty] public partial string DatabaseVersion { get; set; }
    [ObservableProperty] public partial int TableCount { get; set; }
    [ObservableProperty] public partial string Encoding { get; set; }
    [ObservableProperty] public partial int PageSize { get; set; }
}
```

### 连接状态枚举
```csharp
public enum SqliteConnectionStatus
{
    Unknown,        // 未知状态
    Connected,      // 已连接
    Disconnected,   // 未连接
    Error,          // 连接错误
    Testing         // 正在测试连接
}
```

## 🎯 ViewModel 功能

### 核心属性
```csharp
// 数据集合
[ObservableProperty] private ObservableCollection<SqliteDbFileModel> databaseFiles;
[ObservableProperty] private SqliteDbFileModel? selectedDatabaseFile;
[ObservableProperty] private SqliteDbFileModel? editingDatabaseFile;

// 搜索和过滤
[ObservableProperty] private string searchText;
public IEnumerable<SqliteDbFileModel> FilteredDatabaseFiles { get; }

// 状态管理
[ObservableProperty] private bool isLoading;
[ObservableProperty] private bool isEditing;
[ObservableProperty] private string statusMessage;

// 配置选项
[ObservableProperty] private string currentFolderPath;
[ObservableProperty] private bool includeSubfolders;
[ObservableProperty] private bool autoTestConnection;
```

### 主要命令
```csharp
[RelayCommand] private async Task LoadDataAsync();
[RelayCommand] private async Task SelectFolderAsync();
[RelayCommand] private async Task ScanFolderAsync();
[RelayCommand] private async Task AddDatabaseFileAsync();
[RelayCommand] private void EditDatabaseFile();
[RelayCommand] private async Task SaveAsync();
[RelayCommand] private async Task DeleteAsync();
[RelayCommand] private async Task TestConnectionAsync();
[RelayCommand] private async Task TestAllConnectionsAsync();
[RelayCommand] private async Task RefreshAsync();
```

## 🎨 UI 设计特点

### 1. 现代化界面
- 使用 WPF-UI 控件库
- 响应式布局设计
- 深色/浅色主题支持

### 2. 功能区域划分
- **标题栏**：应用标题和主要操作按钮
- **工具栏**：文件夹选择、扫描选项、批量操作
- **主内容区**：数据库列表 + 详细信息面板
- **状态栏**：操作状态和统计信息

### 3. 交互体验
- 实时搜索过滤
- 连接状态可视化
- 拖拽支持（可扩展）
- 批量操作支持

## 🔧 技术实现

### 1. 依赖注入配置
```csharp
// 在 AppBootstrapper.cs 中注册服务
containerRegistry.RegisterSingleton<ISqliteDbManagerService, SqliteDbManagerService>();
containerRegistry.RegisterForNavigation<SqliteDbManagerView, SqliteDbManagerViewModel>();
```

### 2. 数据持久化
- 使用 Zylo.YData 进行数据库操作
- 配置文件使用 JSON 格式存储
- 支持配置备份和恢复

### 3. 事件驱动更新
```csharp
// 服务事件
event EventHandler<SqliteDbFileChangedEventArgs>? DatabaseFileChanged;
event EventHandler<SqliteConnectionStatusChangedEventArgs>? ConnectionStatusChanged;
event EventHandler<SqliteFolderScanCompletedEventArgs>? FolderScanCompleted;

// ViewModel 事件处理
private async void OnDatabaseFileChanged(object? sender, SqliteDbFileChangedEventArgs e);
private async void OnConnectionStatusChanged(object? sender, SqliteConnectionStatusChangedEventArgs e);
```

## 📝 使用示例

### 1. 基本使用
```csharp
// 创建服务实例
var service = new SqliteDbManagerService(logger);
await service.InitializeServiceAsync();

// 扫描默认文件夹
var files = await service.ScanDefaultFolderAsync();

// 添加数据库文件
var dbFile = new SqliteDbFileModel("/path/to/database.db");
await service.AddDatabaseFileAsync(dbFile);

// 测试连接
var result = await service.TestConnectionAsync(dbFile);
```

### 2. ViewModel 集成
```csharp
// 在 View 中绑定 ViewModel
public SqliteDbManagerView()
{
    InitializeComponent();
    DataContext = App.Container.Resolve<SqliteDbManagerViewModel>();
}
```

## 🔮 扩展功能

### 1. 可添加的功能
- **数据库备份/恢复**
- **SQL 查询执行器**
- **数据库结构比较**
- **性能监控**
- **自动化任务调度**

### 2. 集成建议
- **与现有 DWG 管理器集成**
- **统一的配置管理**
- **共享的主题系统**
- **一致的用户体验**

## 📚 参考资料

1. **DwgFileTypeEditorViewModel** - 主要参考的设计模式
2. **CommunityToolkit.Mvvm** - MVVM 框架文档
3. **WPF-UI** - 现代化 UI 控件库
4. **Zylo.YData** - 数据访问层框架

## 🎉 总结

该 SQLite 数据库管理器完全参考了 `DwgFileTypeEditorViewModel` 的设计模式，实现了：

✅ **完整的 MVVM 架构**  
✅ **默认文件夹支持**  
✅ **指定路径扫描**  
✅ **连接状态监控**  
✅ **现代化 UI 界面**  
✅ **事件驱动更新**  
✅ **配置持久化**  
✅ **异步操作支持**  

这个设计为后续的功能扩展和集成提供了坚实的基础。
