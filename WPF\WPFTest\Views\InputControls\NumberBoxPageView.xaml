<UserControl x:Class="WPFTest.Views.InputControls.NumberBoxPageView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:codeExample="clr-namespace:Zylo.WPF.Controls.CodeExample;assembly=Zylo.WPF"
             xmlns:mvvm="http://prismlibrary.com/"
             xmlns:inputControls="clr-namespace:WPFTest.ViewModels.InputControls"
             mc:Ignorable="d"
             d:DesignHeight="2000" d:DesignWidth="1200"
             mvvm:ViewModelLocator.AutoWireViewModel="True"
             d:DataContext="{d:DesignInstance inputControls:NumberBoxPageViewModel}">

    <UserControl.Resources>
        <!-- 🎨 所有样式来自 Zylo.WPF 项目 -->
        <!-- NumberBox 样式已在 Zylo.WPF/Resources/NumberBox/ 目录下定义 -->
        <!-- 转换器 -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto"
                  HorizontalScrollBarVisibility="Disabled"
                  Padding="24">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>    <!-- 标题区域 -->
                <RowDefinition Height="*"/>       <!-- 主要内容 -->
                <RowDefinition Height="Auto"/>    <!-- 底部操作栏 -->
            </Grid.RowDefinitions>

            <!-- 标题区域 -->
            <StackPanel Grid.Row="0" Margin="0,0,0,20">
                <TextBlock Text="🔢 NumberBox 控件展示"
                           FontSize="32"
                           FontWeight="Bold"
                           Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                           Margin="0,0,0,8"/>
                <TextBlock Text="展示 NumberBox 控件的数值输入、验证、格式化和自定义特性"
                           FontSize="16"
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                           TextWrapping="Wrap"/>

                <!-- 状态显示 -->
                <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                        BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Padding="16,12"
                        Margin="0,16,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <ui:SymbolIcon Symbol="Info24"
                                       FontSize="20"
                                       Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                       VerticalAlignment="Center"
                                       Margin="0,0,12,0"/>
                        <TextBlock Grid.Column="1"
                                   Text="{Binding StatusMessage}"
                                   FontSize="14"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                   VerticalAlignment="Center"
                                   TextWrapping="Wrap"/>
                    </Grid>
                </Border>
            </StackPanel>

            <!-- 主要内容区域 -->
            <Border Grid.Row="1" Margin="0,0,0,20">
                <StackPanel>

                    <!-- 基础功能展示 -->
                    <ui:CardExpander Header="🎯 基础功能" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 NumberBox 的基础功能和数值验证"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="基础功能示例：" FontWeight="Medium" Margin="0,0,0,16"/>

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="16"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- 左列：输入控件 -->
                                    <StackPanel Grid.Column="0">
                                        <TextBlock Text="数值" FontWeight="Medium" Margin="0,0,0,4"/>
                                        <ui:NumberBox Value="{Binding BasicValue, UpdateSourceTrigger=PropertyChanged}"
                                                      PlaceholderText="请输入数值"
                                                      Minimum="{Binding MinimumValue}"
                                                      Maximum="{Binding MaximumValue}"
                                                      SmallChange="{Binding StepValue}"
                                                      Margin="0,0,0,8"/>

                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <StackPanel Grid.Column="0" Margin="0,0,4,0">
                                                <TextBlock Text="最小值" FontSize="12" Margin="0,0,0,2"/>
                                                <ui:NumberBox Value="{Binding MinimumValue, UpdateSourceTrigger=PropertyChanged}"
                                                              Maximum="99"
                                                              FontSize="11"/>
                                            </StackPanel>
                                            
                                            <StackPanel Grid.Column="1" Margin="4,0,0,0">
                                                <TextBlock Text="最大值" FontSize="12" Margin="0,0,0,2"/>
                                                <ui:NumberBox Value="{Binding MaximumValue, UpdateSourceTrigger=PropertyChanged}"
                                                              Minimum="1"
                                                              FontSize="11"/>
                                            </StackPanel>
                                        </Grid>
                                    </StackPanel>

                                    <!-- 右列：设置控件 -->
                                    <StackPanel Grid.Column="2">
                                        <TextBlock Text="设置" FontWeight="Medium" Margin="0,0,0,4"/>
                                        
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <StackPanel Grid.Column="0" Margin="0,0,4,0">
                                                <TextBlock Text="步进值" FontSize="12" Margin="0,0,0,2"/>
                                                <ui:NumberBox Value="{Binding StepValue, UpdateSourceTrigger=PropertyChanged}"
                                                              Minimum="0.1"
                                                              Maximum="10"
                                                              FontSize="11"/>
                                            </StackPanel>
                                            
                                            <StackPanel Grid.Column="1" Margin="4,0,0,0">
                                                <TextBlock Text="小数位" FontSize="12" Margin="0,0,0,2"/>
                                                <ui:NumberBox Value="{Binding DecimalPlaces, UpdateSourceTrigger=PropertyChanged}"
                                                              Minimum="0"
                                                              Maximum="6"
                                                              FontSize="11"/>
                                            </StackPanel>
                                        </Grid>

                                        <StackPanel Orientation="Horizontal" Margin="0,8,0,0">
                                            <ui:Button Content="验证数值"
                                                       Appearance="Primary"
                                                       Command="{Binding ValidateNumberCommand}"
                                                       Margin="0,0,4,0"/>
                                            <ui:Button Content="重置"
                                                       Appearance="Secondary"
                                                       Command="{Binding ResetValuesCommand}"/>
                                        </StackPanel>
                                    </StackPanel>
                                </Grid>

                                <!-- 验证结果 -->
                                <Border Background="{DynamicResource ControlFillColorTertiaryBrush}"
                                        CornerRadius="4"
                                        Padding="12,8"
                                        Margin="0,8,0,0"
                                        Visibility="{Binding ValidationMessage, Converter={StaticResource BooleanToVisibilityConverter}}">
                                    <StackPanel>
                                        <TextBlock Text="验证结果" FontWeight="Medium" FontSize="12" Margin="0,0,0,4"/>
                                        <TextBlock Text="{Binding ValidationMessage}"
                                                   FontSize="11"
                                                   Foreground="{Binding ValidationMessageColor}"
                                                   TextWrapping="Wrap"/>
                                    </StackPanel>
                                </Border>
                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="基础功能代码示例"
                                Language="XAML"
                                Description="展示 NumberBox 的基础用法和数值验证"
                                ShowTabs="True"
                                XamlCode="{Binding BasicXamlExample}"
                                CSharpCode="{Binding BasicCSharpExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 样式展示 -->
                    <ui:CardExpander Header="🎨 样式展示" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示来自 Zylo.WPF 的各种 NumberBox 样式"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="样式展示示例：" FontWeight="Medium" Margin="0,0,0,16"/>

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- 第一行 -->
                                    <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,8,8">
                                        <TextBlock Text="标准样式" FontWeight="Medium" FontSize="12" Margin="0,0,0,4"/>
                                        <ui:NumberBox Value="123.45"
                                                      PlaceholderText="标准样式"/>
                                    </StackPanel>

                                    <StackPanel Grid.Row="0" Grid.Column="1" Margin="4,0,4,8">
                                        <TextBlock Text="小型样式" FontWeight="Medium" FontSize="12" Margin="0,0,0,4"/>
                                        <ui:NumberBox Value="67.89"
                                                      PlaceholderText="小型样式"
                                                      FontSize="11"
                                                      Height="28"/>
                                    </StackPanel>

                                    <StackPanel Grid.Row="0" Grid.Column="2" Margin="8,0,0,8">
                                        <TextBlock Text="大型样式" FontWeight="Medium" FontSize="12" Margin="0,0,0,4"/>
                                        <ui:NumberBox Value="456.78"
                                                      PlaceholderText="大型样式"
                                                      FontSize="14"
                                                      Height="40"/>
                                    </StackPanel>

                                    <!-- 第二行 -->
                                    <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,8,0">
                                        <TextBlock Text="透明样式" FontWeight="Medium" FontSize="12" Margin="0,0,0,4"/>
                                        <ui:NumberBox Value="789.12"
                                                      PlaceholderText="透明样式"
                                                      Background="Transparent"
                                                      BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"/>
                                    </StackPanel>

                                    <StackPanel Grid.Row="1" Grid.Column="1" Margin="4,0,4,0">
                                        <TextBlock Text="圆角样式" FontWeight="Medium" FontSize="12" Margin="0,0,0,4"/>
                                        <ui:NumberBox Value="345.67"
                                                      PlaceholderText="圆角样式"/>
                                    </StackPanel>

                                    <StackPanel Grid.Row="1" Grid.Column="2" Margin="8,0,0,0">
                                        <TextBlock Text="无边框样式" FontWeight="Medium" FontSize="12" Margin="0,0,0,4"/>
                                        <ui:NumberBox Value="901.23"
                                                      PlaceholderText="无边框样式"
                                                      BorderThickness="0"
                                                      Background="{DynamicResource ControlFillColorTertiaryBrush}"/>
                                    </StackPanel>
                                </Grid>
                                </StackPanel>
                            </ui:Card>

                        <!-- 代码示例 -->
                        <codeExample:CodeExampleControl
                            Title="样式代码示例"
                            Language="XAML"
                            Description="展示不同样式的 NumberBox 实现"
                            XamlCode="{Binding StylesXamlExample}"
                            IsExpanded="False"/>
                    </StackPanel>
                </ui:CardExpander>

                <!-- 格式化功能 -->
                <ui:CardExpander Header="🔧 格式化功能"
                                 IsExpanded="True"
                                 
                                 Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 NumberBox 的数字格式化和本地化功能"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="格式化功能示例：" FontWeight="Medium" Margin="0,0,0,16"/>


                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="16"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- 左列：格式化选项 -->
                                    <StackPanel Grid.Column="0">
                                        <TextBlock Text="格式化类型" FontWeight="Medium" Margin="0,0,0,4"/>
                                        <ComboBox ItemsSource="{Binding FormatOptions}"
                                                  SelectedItem="{Binding SelectedFormatItem}"
                                                  DisplayMemberPath="Name"
                                                  Margin="0,0,0,8"/>

                                        <ui:Button Content="应用格式"
                                                   Appearance="Primary"
                                                   HorizontalAlignment="Stretch"
                                                   Command="{Binding FormatNumberCommand}"/>
                                    </StackPanel>

                                    <!-- 右列：格式化示例 -->
                                    <StackPanel Grid.Column="2">
                                        <TextBlock Text="格式化示例" FontWeight="Medium" Margin="0,0,0,8"/>

                                        <StackPanel>
                                            <!-- 货币格式 -->
                                            <StackPanel Margin="0,0,0,8">
                                                <TextBlock Text="货币格式" FontSize="12" Margin="0,0,0,2"/>
                                                <ui:NumberBox Value="{Binding CurrencyValue, UpdateSourceTrigger=PropertyChanged}"
                                                              PlaceholderText="货币金额"
                                                              Minimum="0"
                                                              Maximum="999999"
                                                              SmallChange="0.01"/>
                                                <TextBlock Text="{Binding CurrencyValue, StringFormat='货币: ¥{0:F2}'}"
                                                           FontSize="11"
                                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                           Margin="0,2,0,0"/>
                                            </StackPanel>

                                            <!-- 百分比格式 -->
                                            <StackPanel Margin="0,0,0,8">
                                                <TextBlock Text="百分比格式" FontSize="12" Margin="0,0,0,2"/>
                                                <ui:NumberBox Value="{Binding PercentageValue, UpdateSourceTrigger=PropertyChanged}"
                                                              PlaceholderText="百分比值"
                                                              Minimum="0"
                                                              Maximum="100"
                                                              SmallChange="0.1"/>
                                                <TextBlock Text="{Binding PercentageValue, StringFormat='百分比: {0:F1}%'}"
                                                           FontSize="11"
                                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                           Margin="0,2,0,0"/>
                                            </StackPanel>

                                            <!-- 科学计数法 -->
                                            <StackPanel>
                                                <TextBlock Text="科学计数法" FontSize="12" Margin="0,0,0,2"/>
                                                <ui:NumberBox Value="{Binding ScientificValue, UpdateSourceTrigger=PropertyChanged}"
                                                              PlaceholderText="科学计数值"
                                                              Minimum="0.001"
                                                              Maximum="999999999"
                                                              SmallChange="1000"/>
                                                <TextBlock Text="{Binding ScientificValue, StringFormat='科学计数: {0:E2}'}"
                                                           FontSize="11"
                                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                           Margin="0,2,0,0"/>
                                            </StackPanel>
                                        </StackPanel>
                                    </StackPanel>
                                </Grid>
                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="格式化功能代码示例"
                                Language="XAML"
                                Description="展示 NumberBox 的格式化和本地化功能"
                                ShowTabs="True"
                                XamlCode="{Binding FormattingXamlExample}"
                                CSharpCode="{Binding FormattingCSharpExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 数据验证功能 -->
                    <ui:CardExpander Header="✅ 数据验证" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 NumberBox 的数据验证和错误处理功能"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="数据验证示例：" FontWeight="Medium" Margin="0,0,0,16"/>

                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="16"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- 左列：验证规则 -->
                                        <StackPanel Grid.Column="0">
                                            <TextBlock Text="验证规则设置" FontWeight="Medium" Margin="0,0,0,8"/>

                                            <!-- 范围验证 -->
                                            <StackPanel Margin="0,0,0,12">
                                                <TextBlock Text="范围验证 (1-100)" FontSize="12" Margin="0,0,0,2"/>
                                                <ui:NumberBox Value="{Binding RangeValidationValue, UpdateSourceTrigger=PropertyChanged}"
                                                              PlaceholderText="输入1-100的数值"
                                                              Minimum="1"
                                                              Maximum="100"/>
                                                <TextBlock Text="{Binding RangeValidationMessage}"
                                                           FontSize="11"
                                                           Foreground="{Binding RangeValidationColor}"
                                                           Margin="0,2,0,0"/>
                                            </StackPanel>

                                            <!-- 必填验证 -->
                                            <StackPanel Margin="0,0,0,12">
                                                <TextBlock Text="必填验证" FontSize="12" Margin="0,0,0,2"/>
                                                <ui:NumberBox Value="{Binding RequiredValue, UpdateSourceTrigger=PropertyChanged}"
                                                              PlaceholderText="此字段为必填"/>
                                                <TextBlock Text="{Binding RequiredValidationMessage}"
                                                           FontSize="11"
                                                           Foreground="{Binding RequiredValidationColor}"
                                                           Margin="0,2,0,0"/>
                                            </StackPanel>

                                            <!-- 自定义验证 -->
                                            <StackPanel>
                                                <TextBlock Text="自定义验证 (偶数)" FontSize="12" Margin="0,0,0,2"/>
                                                <ui:NumberBox Value="{Binding CustomValidationValue, UpdateSourceTrigger=PropertyChanged}"
                                                              PlaceholderText="输入偶数"/>
                                                <TextBlock Text="{Binding CustomValidationMessage}"
                                                           FontSize="11"
                                                           Foreground="{Binding CustomValidationColor}"
                                                           Margin="0,2,0,0"/>
                                            </StackPanel>
                                        </StackPanel>

                                        <!-- 右列：验证结果 -->
                                        <StackPanel Grid.Column="2">
                                            <TextBlock Text="验证结果" FontWeight="Medium" Margin="0,0,0,8"/>

                                            <Border Background="{DynamicResource ControlFillColorTertiaryBrush}"
                                                    CornerRadius="4"
                                                    Padding="12"
                                                    Margin="0,0,0,8">
                                                <StackPanel>
                                                    <TextBlock Text="验证状态:" FontWeight="Medium" FontSize="12" Margin="0,0,0,4"/>
                                                    <TextBlock Text="{Binding ValidationSummary}"
                                                               FontSize="11"
                                                               TextWrapping="Wrap"/>
                                                </StackPanel>
                                            </Border>

                                            <StackPanel Orientation="Horizontal">
                                                <ui:Button Content="验证所有"
                                                           Appearance="Primary"
                                                           Command="{Binding ValidateAllCommand}"
                                                           Margin="0,0,8,0"/>
                                                <ui:Button Content="清除验证"
                                                           Appearance="Secondary"
                                                           Command="{Binding ClearValidationCommand}"/>
                                            </StackPanel>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="数据验证代码示例"
                                Language="XAML"
                                Description="展示 NumberBox 的数据验证实现"
                                ShowTabs="True"
                                XamlCode="{Binding ValidationXamlExample}"
                                CSharpCode="{Binding ValidationCSharpExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 单位和测量 -->
                    <ui:CardExpander Header="📏 单位和测量" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 NumberBox 的单位转换和测量功能"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="单位转换示例：" FontWeight="Medium" Margin="0,0,0,16"/>

                                    <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- 左列：温度和重量 -->
                                    <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                        <StackPanel Margin="0,0,0,12">
                                            <TextBlock Text="温度 (°C)" FontWeight="Medium" Margin="0,0,0,4"/>
                                            <Grid>
                                                <ui:NumberBox Value="{Binding TemperatureValue, UpdateSourceTrigger=PropertyChanged}"
                                                              PlaceholderText="温度值"
                                                              Minimum="-273.15"
                                                              Maximum="1000"/>
                                                <TextBlock Text="°C"
                                                           HorizontalAlignment="Right"
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"
                                                           FontSize="12"
                                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                                            </Grid>
                                        </StackPanel>

                                        <StackPanel>
                                            <TextBlock Text="重量 (kg)" FontWeight="Medium" Margin="0,0,0,4"/>
                                            <Grid>
                                                <ui:NumberBox Value="{Binding WeightValue, UpdateSourceTrigger=PropertyChanged}"
                                                              PlaceholderText="重量值"
                                                              Minimum="0"
                                                              Maximum="1000"/>
                                                <TextBlock Text="kg"
                                                           HorizontalAlignment="Right"
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"
                                                           FontSize="12"
                                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                                            </Grid>
                                        </StackPanel>
                                    </StackPanel>

                                    <!-- 右列：转换结果 -->
                                    <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                        <TextBlock Text="转换结果" FontWeight="Medium" Margin="0,0,0,8"/>

                                        <Border Background="{DynamicResource ControlFillColorTertiaryBrush}"
                                                CornerRadius="4" Padding="8">
                                            <StackPanel>
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                                                    <TextBlock Text="华氏度:" FontSize="12" Margin="0,0,4,0"/>
                                                    <StackPanel Orientation="Horizontal">
                                                        <TextBlock Text="{Binding TemperatureValue, StringFormat='{}{0:F1}°C'}" FontSize="12"/>
                                                        <TextBlock Text=" = " FontSize="12" Margin="4,0"/>
                                                        <TextBlock Text="{Binding FahrenheitValue, StringFormat='{}{0:F1}°F'}" FontSize="12"/>
                                                    </StackPanel>
                                                </StackPanel>
                                                <StackPanel Orientation="Horizontal">
                                                    <TextBlock Text="磅:" FontSize="12" Margin="0,0,4,0"/>
                                                    <StackPanel Orientation="Horizontal">
                                                        <TextBlock Text="{Binding WeightValue, StringFormat='{}{0:F1}kg'}" FontSize="12"/>
                                                        <TextBlock Text=" = " FontSize="12" Margin="4,0"/>
                                                        <TextBlock Text="{Binding PoundsValue, StringFormat='{}{0:F1}lbs'}" FontSize="12"/>
                                                    </StackPanel>
                                                </StackPanel>
                                            </StackPanel>
                                        </Border>
                                    </StackPanel>
                                </Grid>
                                </StackPanel>
                            </ui:Card>

                        <!-- 代码示例 -->
                        <codeExample:CodeExampleControl
                            Title="高级功能代码示例"
                            Language="XAML"
                            Description="展示 NumberBox 的单位显示和高级功能"
                            ShowTabs="True"
                            XamlCode="{Binding AdvancedXamlExample}"
                            CSharpCode="{Binding AdvancedCSharpExample}"
                            IsExpanded="False"/>
                    </StackPanel>
                </ui:CardExpander>

                </StackPanel>
            </Border>

            <!-- 底部操作栏 -->
            <Border Grid.Row="2"
                    Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Padding="16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 统计信息 -->
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock Text="📊" FontSize="14" Margin="0,0,8,0"/>
                        <TextBlock Text="状态:" FontWeight="Medium" Margin="0,0,4,0"/>
                        <TextBlock Text="{Binding StatusMessage}" FontSize="12"/>
                    </StackPanel>

                    <!-- 操作按钮 -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <ui:Button Content="重置计数"
                                   Command="{Binding ResetCountCommand}"
                                   Appearance="Secondary"
                                   Margin="0,0,8,0"/>
                        <ui:Button Content="清除状态"
                                   Command="{Binding ClearStatusCommand}"
                                   Appearance="Secondary"/>
                    </StackPanel>
                </Grid>
            </Border>

        </Grid>
    </ScrollViewer>
</UserControl>
