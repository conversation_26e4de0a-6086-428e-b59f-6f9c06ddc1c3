// PasswordBox 安全特性 C# 示例
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Collections.ObjectModel;

namespace WPFTest.ViewModels.InputControls
{
    public partial class PasswordBoxSecurityViewModel : ObservableObject
    {
        /// <summary>
        /// 安全密码输入
        /// </summary>
        [ObservableProperty]
        private string securePassword = string.Empty;

        /// <summary>
        /// 密码强度分析结果
        /// </summary>
        [ObservableProperty]
        private PasswordStrengthAnalysis strengthAnalysis = new();

        /// <summary>
        /// 密码泄露检测结果
        /// </summary>
        [ObservableProperty]
        private bool isPasswordCompromised = false;

        /// <summary>
        /// 密码使用天数
        /// </summary>
        [ObservableProperty]
        private int passwordAgeDays = 89;

        /// <summary>
        /// 安全审计日志
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<SecurityAuditLog> auditLogs = new();

        /// <summary>
        /// 密码过期警告
        /// </summary>
        [ObservableProperty]
        private bool showExpirationWarning = true;

        public PasswordBoxSecurityViewModel()
        {
            InitializeAuditLogs();
            
            // 监听密码变化
            PropertyChanged += (s, e) =>
            {
                if (e.PropertyName == nameof(SecurePassword))
                {
                    AnalyzePasswordStrength(SecurePassword);
                }
            };
        }

        /// <summary>
        /// 检测密码泄露命令
        /// </summary>
        [RelayCommand]
        private async Task CheckPasswordBreach()
        {
            if (string.IsNullOrEmpty(SecurePassword))
            {
                StatusMessage = "请输入密码";
                return;
            }

            StatusMessage = "正在检测密码安全性...";
            
            // 模拟异步检测过程
            await Task.Delay(1000);
            
            // 使用 SHA-1 哈希检测（Have I Been Pwned API 标准）
            var isCompromised = await CheckPasswordInBreachDatabase(SecurePassword);
            IsPasswordCompromised = isCompromised;
            
            var message = isCompromised 
                ? "⚠️ 此密码已在数据泄露中发现，建议立即更换"
                : "✅ 此密码未在已知泄露数据库中发现";
            
            StatusMessage = message;
            LogSecurityEvent(isCompromised ? "检测到已泄露密码" : "密码安全性检测通过");
        }

        /// <summary>
        /// 立即更换密码命令
        /// </summary>
        [RelayCommand]
        private void ChangePasswordNow()
        {
            // 清除当前密码
            SecurePassword = string.Empty;
            PasswordAgeDays = 0;
            ShowExpirationWarning = false;
            
            StatusMessage = "请输入新密码";
            LogSecurityEvent("用户主动更换密码");
        }

        /// <summary>
        /// 稍后提醒命令
        /// </summary>
        [RelayCommand]
        private void RemindLater()
        {
            ShowExpirationWarning = false;
            StatusMessage = "将在3天后再次提醒";
            LogSecurityEvent("用户延迟密码更换提醒");
        }

        /// <summary>
        /// 导出安全日志命令
        /// </summary>
        [RelayCommand]
        private async Task ExportSecurityLog()
        {
            try
            {
                var logContent = GenerateSecurityLogReport();
                
                // 模拟文件保存
                await Task.Delay(500);
                
                StatusMessage = "安全日志已导出到桌面";
                LogSecurityEvent("导出安全审计日志");
            }
            catch (Exception ex)
            {
                StatusMessage = $"导出失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 分析密码强度
        /// </summary>
        private void AnalyzePasswordStrength(string password)
        {
            if (string.IsNullOrEmpty(password))
            {
                StrengthAnalysis = new PasswordStrengthAnalysis();
                return;
            }

            var analysis = new PasswordStrengthAnalysis
            {
                Length = password.Length,
                HasMinLength = password.Length >= 8,
                HasUppercase = Regex.IsMatch(password, @"[A-Z]"),
                HasLowercase = Regex.IsMatch(password, @"[a-z]"),
                HasNumbers = Regex.IsMatch(password, @"\d"),
                HasSymbols = Regex.IsMatch(password, @"[!@#$%^&*(),.?""':;{}|<>]"),
                UniqueCharacters = password.Distinct().Count(),
                HasDiversity = password.Distinct().Count() > password.Length * 0.7
            };

            // 计算总体强度分数
            analysis.OverallScore = CalculateOverallScore(analysis);
            analysis.StrengthLevel = GetStrengthLevel(analysis.OverallScore);
            analysis.StrengthColor = GetStrengthColor(analysis.StrengthLevel);

            StrengthAnalysis = analysis;
        }

        /// <summary>
        /// 计算总体强度分数
        /// </summary>
        private static double CalculateOverallScore(PasswordStrengthAnalysis analysis)
        {
            double score = 0;

            // 长度评分 (最多40分)
            score += Math.Min(analysis.Length * 4, 40);

            // 字符类型评分 (每种10分)
            if (analysis.HasUppercase) score += 10;
            if (analysis.HasLowercase) score += 10;
            if (analysis.HasNumbers) score += 10;
            if (analysis.HasSymbols) score += 15;

            // 多样性评分 (5分)
            if (analysis.HasDiversity) score += 5;

            // 复杂度奖励 (最多10分)
            var complexity = (analysis.HasUppercase ? 1 : 0) +
                           (analysis.HasLowercase ? 1 : 0) +
                           (analysis.HasNumbers ? 1 : 0) +
                           (analysis.HasSymbols ? 1 : 0);
            
            if (complexity >= 3) score += 5;
            if (complexity == 4) score += 5;

            return Math.Min(score, 100);
        }

        /// <summary>
        /// 获取强度等级
        /// </summary>
        private static string GetStrengthLevel(double score)
        {
            return score switch
            {
                < 25 => "弱",
                < 50 => "一般",
                < 75 => "良好",
                _ => "强"
            };
        }

        /// <summary>
        /// 获取强度颜色
        /// </summary>
        private static string GetStrengthColor(string level)
        {
            return level switch
            {
                "弱" => "Red",
                "一般" => "Orange",
                "良好" => "Yellow",
                "强" => "Green",
                _ => "Gray"
            };
        }

        /// <summary>
        /// 检查密码是否在泄露数据库中
        /// </summary>
        private static async Task<bool> CheckPasswordInBreachDatabase(string password)
        {
            // 实际实现中应该调用 Have I Been Pwned API
            // 这里使用模拟逻辑
            await Task.Delay(500);
            
            // 模拟：常见弱密码被标记为已泄露
            var commonPasswords = new[] { "123456", "password", "123456789", "qwerty", "abc123" };
            return commonPasswords.Contains(password.ToLower());
        }

        /// <summary>
        /// 记录安全事件
        /// </summary>
        private void LogSecurityEvent(string eventDescription)
        {
            var logEntry = new SecurityAuditLog
            {
                Timestamp = DateTime.Now,
                EventType = GetEventType(eventDescription),
                Description = eventDescription,
                Icon = GetEventIcon(eventDescription)
            };

            AuditLogs.Insert(0, logEntry);

            // 限制日志数量
            while (AuditLogs.Count > 50)
            {
                AuditLogs.RemoveAt(AuditLogs.Count - 1);
            }
        }

        /// <summary>
        /// 获取事件类型
        /// </summary>
        private static string GetEventType(string description)
        {
            return description.ToLower() switch
            {
                var d when d.Contains("密码") => "Password",
                var d when d.Contains("登录") => "Login",
                var d when d.Contains("安全") => "Security",
                var d when d.Contains("检测") => "Detection",
                _ => "General"
            };
        }

        /// <summary>
        /// 获取事件图标
        /// </summary>
        private static string GetEventIcon(string description)
        {
            return description.ToLower() switch
            {
                var d when d.Contains("成功") => "✅",
                var d when d.Contains("失败") || d.Contains("错误") => "❌",
                var d when d.Contains("警告") || d.Contains("检测") => "⚠️",
                var d when d.Contains("安全") => "🛡️",
                var d when d.Contains("密码") => "🔒",
                _ => "ℹ️"
            };
        }

        /// <summary>
        /// 生成安全日志报告
        /// </summary>
        private string GenerateSecurityLogReport()
        {
            var report = new StringBuilder();
            report.AppendLine("=== 密码安全审计报告 ===");
            report.AppendLine($"生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine($"密码使用天数: {PasswordAgeDays} 天");
            report.AppendLine($"当前密码强度: {StrengthAnalysis.StrengthLevel}");
            report.AppendLine();
            
            report.AppendLine("=== 安全事件日志 ===");
            foreach (var log in AuditLogs.Take(20))
            {
                report.AppendLine($"{log.FormattedTimestamp} [{log.EventType}] {log.Description}");
            }
            
            return report.ToString();
        }

        /// <summary>
        /// 初始化审计日志
        /// </summary>
        private void InitializeAuditLogs()
        {
            AuditLogs.Add(new SecurityAuditLog
            {
                Timestamp = DateTime.Now.AddMinutes(-5),
                EventType = "Password",
                Description = "密码更换成功",
                Icon = "🔒"
            });

            AuditLogs.Add(new SecurityAuditLog
            {
                Timestamp = DateTime.Now.AddMinutes(-10),
                EventType = "Detection",
                Description = "检测到弱密码尝试",
                Icon = "⚠️"
            });

            AuditLogs.Add(new SecurityAuditLog
            {
                Timestamp = DateTime.Now.AddDays(-5),
                EventType = "Security",
                Description = "启用双因素认证",
                Icon = "🛡️"
            });
        }
    }

    /// <summary>
    /// 密码强度分析结果
    /// </summary>
    public partial class PasswordStrengthAnalysis : ObservableObject
    {
        [ObservableProperty]
        private int length;

        [ObservableProperty]
        private bool hasMinLength;

        [ObservableProperty]
        private bool hasUppercase;

        [ObservableProperty]
        private bool hasLowercase;

        [ObservableProperty]
        private bool hasNumbers;

        [ObservableProperty]
        private bool hasSymbols;

        [ObservableProperty]
        private int uniqueCharacters;

        [ObservableProperty]
        private bool hasDiversity;

        [ObservableProperty]
        private double overallScore;

        [ObservableProperty]
        private string strengthLevel = "请输入密码";

        [ObservableProperty]
        private string strengthColor = "Gray";
    }

    /// <summary>
    /// 安全审计日志项
    /// </summary>
    public class SecurityAuditLog
    {
        public DateTime Timestamp { get; set; }
        public string EventType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public string FormattedTimestamp => Timestamp.ToString("yyyy-MM-dd HH:mm");
    }
}
