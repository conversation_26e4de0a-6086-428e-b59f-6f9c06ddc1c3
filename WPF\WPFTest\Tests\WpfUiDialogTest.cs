using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using WPFTest.Views.DWG;
using Wpf.Ui.Controls;

namespace WPFTest.Tests;

/// <summary>
/// WPF-UI对话框测试类
/// </summary>
/// <remarks>
/// 测试新的WPF-UI对话框功能是否正常工作
/// </remarks>
public class WpfUiDialogTest
{
    /// <summary>
    /// 测试重命名对话框
    /// </summary>
    public async Task TestRenameDialogAsync()
    {
        try
        {
            Console.WriteLine("🧪 测试重命名对话框...");

            await Application.Current.Dispatcher.InvokeAsync(async () =>
            {
                var dialog = new RenameFileDialog("测试文件.dwg");
                
                // 设置主窗口为父窗口（如果存在）
                if (Application.Current.MainWindow != null)
                {
                    dialog.Owner = Application.Current.MainWindow;
                }

                Console.WriteLine("📝 重命名对话框已创建，等待用户交互...");
                
                // 注意：在测试环境中，这会等待用户手动操作
                // 在实际使用中，用户会输入新名称并点击确定或取消
                var result = dialog.ShowDialog();
                
                if (result == true && !string.IsNullOrEmpty(dialog.NewFileName))
                {
                    Console.WriteLine($"✅ 重命名成功: {dialog.NewFileName}");
                }
                else
                {
                    Console.WriteLine("❌ 重命名被取消");
                }
            });
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 重命名对话框测试失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 测试删除确认对话框
    /// </summary>
    public async Task TestDeleteConfirmationAsync()
    {
        try
        {
            Console.WriteLine("🧪 测试删除确认对话框...");

            await Application.Current.Dispatcher.InvokeAsync(async () =>
            {
                var messageBox = new Wpf.Ui.Controls.MessageBox
                {
                    Title = "确认删除",
                    Content = "确定要删除文件 '测试文件.dwg' 吗？\n\n⚠️ 此操作不可撤销！",
                    PrimaryButtonText = "删除",
                    SecondaryButtonText = "取消",
                    WindowStartupLocation = WindowStartupLocation.CenterOwner
                };

                // 设置主窗口为父窗口
                if (Application.Current.MainWindow != null)
                {
                    messageBox.Owner = Application.Current.MainWindow;
                }

                Console.WriteLine("🗑️ 删除确认对话框已创建，等待用户选择...");
                
                var result = await messageBox.ShowDialogAsync();
                
                if (result == Wpf.Ui.Controls.MessageBoxResult.Primary)
                {
                    Console.WriteLine("✅ 用户确认删除");
                }
                else
                {
                    Console.WriteLine("❌ 用户取消删除");
                }
            });
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 删除确认对话框测试失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 测试文件属性对话框
    /// </summary>
    public async Task TestFilePropertiesAsync()
    {
        try
        {
            Console.WriteLine("🧪 测试文件属性对话框...");

            await Application.Current.Dispatcher.InvokeAsync(async () =>
            {
                // 创建模拟文件信息
                var testFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "测试文件.dwg");
                
                // 如果文件不存在，创建一个临时文件用于测试
                if (!File.Exists(testFilePath))
                {
                    File.WriteAllText(testFilePath, "测试内容");
                }

                var fileInfo = new FileInfo(testFilePath);
                var sizeInKB = fileInfo.Length / 1024.0;
                var sizeText = sizeInKB > 1024 
                    ? $"{sizeInKB / 1024.0:F2} MB" 
                    : $"{sizeInKB:F2} KB";

                var properties = $"📄 文件名: {fileInfo.Name}\n\n" +
                               $"📏 大小: {sizeText} ({fileInfo.Length:N0} 字节)\n\n" +
                               $"📅 创建时间: {fileInfo.CreationTime:yyyy-MM-dd HH:mm:ss}\n\n" +
                               $"✏️ 修改时间: {fileInfo.LastWriteTime:yyyy-MM-dd HH:mm:ss}\n\n" +
                               $"📁 路径: {fileInfo.FullName}";

                var messageBox = new Wpf.Ui.Controls.MessageBox
                {
                    Title = $"文件属性 - {fileInfo.Name}",
                    Content = properties,
                    PrimaryButtonText = "确定",
                    WindowStartupLocation = WindowStartupLocation.CenterOwner
                };

                // 设置主窗口为父窗口
                if (Application.Current.MainWindow != null)
                {
                    messageBox.Owner = Application.Current.MainWindow;
                }

                Console.WriteLine("ℹ️ 文件属性对话框已创建，等待用户确认...");
                
                await messageBox.ShowDialogAsync();
                
                Console.WriteLine("✅ 文件属性对话框已关闭");

                // 清理测试文件
                if (File.Exists(testFilePath))
                {
                    File.Delete(testFilePath);
                }
            });
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 文件属性对话框测试失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 运行所有对话框测试
    /// </summary>
    public async Task RunAllTestsAsync()
    {
        Console.WriteLine("🚀 开始WPF-UI对话框测试...");
        Console.WriteLine(new string('=', 50));

        await TestRenameDialogAsync();
        await Task.Delay(1000); // 等待1秒

        await TestDeleteConfirmationAsync();
        await Task.Delay(1000); // 等待1秒

        await TestFilePropertiesAsync();

        Console.WriteLine(new string('=', 50));
        Console.WriteLine("🏁 所有对话框测试完成");
    }
}

/// <summary>
/// WPF-UI对话框测试运行器
/// </summary>
public static class WpfUiDialogTestRunner
{
    /// <summary>
    /// 运行对话框测试
    /// </summary>
    public static async Task RunTestsAsync()
    {
        var tester = new WpfUiDialogTest();
        await tester.RunAllTestsAsync();
    }

    /// <summary>
    /// 快速测试 - 只测试一个对话框
    /// </summary>
    public static async Task QuickTestAsync()
    {
        var tester = new WpfUiDialogTest();
        
        Console.WriteLine("⚡ 快速测试模式 - 测试文件属性对话框");
        await tester.TestFilePropertiesAsync();
        Console.WriteLine("⚡ 快速测试完成");
    }

    /// <summary>
    /// 测试重命名对话框的输入验证
    /// </summary>
    public static void TestRenameValidation()
    {
        Console.WriteLine("🧪 测试重命名对话框的输入验证...");

        // 测试非法字符
        var invalidChars = Path.GetInvalidFileNameChars();
        Console.WriteLine($"📋 非法字符列表: {string.Join(", ", invalidChars.Take(10))}...");

        // 测试文件名验证逻辑
        var testNames = new[]
        {
            "正常文件名.dwg",
            "包含<非法>字符.dwg",
            "",
            "   ",
            "文件名",
            "文件名.txt"
        };

        foreach (var testName in testNames)
        {
            var isValid = !string.IsNullOrWhiteSpace(testName) && 
                         testName.IndexOfAny(invalidChars) < 0;
            Console.WriteLine($"📝 '{testName}' -> {(isValid ? "✅ 有效" : "❌ 无效")}");
        }

        Console.WriteLine("✅ 输入验证测试完成");
    }
}
