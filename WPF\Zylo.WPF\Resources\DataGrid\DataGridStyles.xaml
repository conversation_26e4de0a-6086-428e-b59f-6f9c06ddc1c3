<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml">

    <!-- 🎨 DataGrid 基础样式 -->
    <Style x:Key="DataGridBaseStyle" TargetType="DataGrid">
        <Setter Property="Background" Value="{DynamicResource CardBackgroundFillColorDefaultBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource CardStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="RowBackground" Value="Transparent"/>
        <Setter Property="AlternatingRowBackground" Value="{DynamicResource CardBackgroundFillColorSecondaryBrush}"/>
        <Setter Property="GridLinesVisibility" Value="Horizontal"/>
        <Setter Property="HeadersVisibility" Value="Column"/>
        <Setter Property="CanUserReorderColumns" Value="True"/>
        <Setter Property="CanUserResizeColumns" Value="True"/>
        <Setter Property="CanUserSortColumns" Value="True"/>
        <Setter Property="SelectionMode" Value="Single"/>
        <Setter Property="SelectionUnit" Value="FullRow"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontFamily" Value="{DynamicResource ContentControlThemeFontFamily}"/>
        <Setter Property="RowHeight" Value="40"/>
        <Setter Property="ColumnHeaderHeight" Value="35"/>
        <Setter Property="HorizontalGridLinesBrush" Value="{DynamicResource CardStrokeColorDefaultBrush}"/>
        <Setter Property="VerticalGridLinesBrush" Value="{DynamicResource CardStrokeColorDefaultBrush}"/>
    </Style>

    <!-- 🎨 DataGrid 行样式 -->
    <Style x:Key="DataGridRowStyle" TargetType="DataGridRow">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>

        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background">
                    <Setter.Value>
                        <SolidColorBrush Color="{DynamicResource SystemAccentColor}" Opacity="0.03"/>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger Property="IsSelected" Value="True">
                <Setter Property="Background">
                    <Setter.Value>
                        <SolidColorBrush Color="{DynamicResource SystemAccentColor}" Opacity="0.08"/>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <!-- 鼠标悬停且选中的状态 -->
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="True"/>
                    <Condition Property="IsSelected" Value="True"/>
                </MultiTrigger.Conditions>
                <Setter Property="Background">
                    <Setter.Value>
                        <SolidColorBrush Color="{DynamicResource SystemAccentColor}" Opacity="0.12"/>
                    </Setter.Value>
                </Setter>
            </MultiTrigger>
        </Style.Triggers>
    </Style>

    <!-- 🎨 DataGrid 标准样式 -->
    <Style x:Key="DataGridStyle" TargetType="DataGrid" BasedOn="{StaticResource DataGridBaseStyle}">
        <!-- 标准样式的特定设置 -->
        <Setter Property="RowBackground" Value="Transparent"/>
        <Setter Property="AlternatingRowBackground" Value="{DynamicResource CardBackgroundFillColorSecondaryBrush}"/>
        <Setter Property="RowStyle" Value="{StaticResource DataGridRowStyle}"/>
    </Style>

    <!-- 🎨 DataGrid 现代化样式 -->
    <Style x:Key="ModernDataGridStyle" TargetType="DataGrid" BasedOn="{StaticResource DataGridBaseStyle}">
        <Setter Property="Background" Value="{DynamicResource CardBackgroundFillColorDefaultBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource SystemAccentColorPrimaryBrush}"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="RowBackground" Value="Transparent"/>
        <Setter Property="AlternatingRowBackground" Value="{DynamicResource CardBackgroundFillColorSecondaryBrush}"/>
        <Setter Property="GridLinesVisibility" Value="All"/>
        <Setter Property="HorizontalGridLinesBrush" Value="{DynamicResource SystemAccentColorPrimaryBrush}"/>
        <Setter Property="VerticalGridLinesBrush" Value="{DynamicResource SystemAccentColorPrimaryBrush}"/>
        
        <!-- 现代化样式的行样式 -->
        <Setter Property="RowStyle">
            <Setter.Value>
                <Style TargetType="DataGridRow">
                    <Setter Property="Background" Value="Transparent"/>
                    <Setter Property="BorderThickness" Value="0"/>
                    <Setter Property="Margin" Value="0"/>
                    <Style.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background">
                                <Setter.Value>
                                    <SolidColorBrush Color="{DynamicResource SystemAccentColor}" Opacity="0.1"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="Background">
                                <Setter.Value>
                                    <SolidColorBrush Color="{DynamicResource SystemAccentColor}" Opacity="0.2"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                    </Style.Triggers>
                </Style>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 🎨 DataGrid 紧凑样式 -->
    <Style x:Key="CompactDataGridStyle" TargetType="DataGrid" BasedOn="{StaticResource DataGridBaseStyle}">
        <Setter Property="RowHeight" Value="28"/>
        <Setter Property="ColumnHeaderHeight" Value="30"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="GridLinesVisibility" Value="Horizontal"/>
        <Setter Property="RowBackground" Value="Transparent"/>
        <Setter Property="AlternatingRowBackground" Value="Transparent"/>
        
        <!-- 紧凑样式的行样式 -->
        <Setter Property="RowStyle">
            <Setter.Value>
                <Style TargetType="DataGridRow">
                    <Setter Property="Background" Value="Transparent"/>
                    <Setter Property="BorderThickness" Value="0"/>
                    <Setter Property="Margin" Value="0"/>
                    <Setter Property="Padding" Value="4,2"/>
                    <Style.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{DynamicResource CardBackgroundFillColorSecondaryBrush}"/>
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="Background" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
                            <Setter Property="Foreground" Value="{DynamicResource AccentTextFillColorPrimaryBrush}"/>
                        </Trigger>
                    </Style.Triggers>
                </Style>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 🎨 DataGrid 透明样式 -->
    <Style x:Key="TransparentDataGridStyle" TargetType="DataGrid" BasedOn="{StaticResource DataGridBaseStyle}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="{DynamicResource CardStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="RowBackground" Value="Transparent"/>
        <Setter Property="AlternatingRowBackground" Value="Transparent"/>
        <Setter Property="GridLinesVisibility" Value="None"/>
    </Style>

    <!-- 🎨 DataGrid 强调色样式 -->
    <Style x:Key="AccentDataGridStyle" TargetType="DataGrid" BasedOn="{StaticResource DataGridBaseStyle}">
        <Setter Property="Background" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource AccentTextFillColorPrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource SystemAccentColorPrimaryBrush}"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="RowBackground" Value="Transparent"/>
        <Setter Property="AlternatingRowBackground">
            <Setter.Value>
                <SolidColorBrush Color="{DynamicResource SystemAccentColor}" Opacity="0.1"/>
            </Setter.Value>
        </Setter>
        <Setter Property="HorizontalGridLinesBrush" Value="{DynamicResource AccentTextFillColorSecondaryBrush}"/>
        <Setter Property="VerticalGridLinesBrush" Value="{DynamicResource AccentTextFillColorSecondaryBrush}"/>
    </Style>

    <!-- 🎨 DataGrid 列标题样式 -->
    <Style x:Key="DataGridColumnHeaderStyle" TargetType="DataGridColumnHeader">
        <Setter Property="Background" Value="{DynamicResource CardBackgroundFillColorSecondaryBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource CardStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="0,0,1,1"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="HorizontalContentAlignment" Value="Left"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>

        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{DynamicResource CardBackgroundFillColorTertiaryBrush}"/>
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
                <Setter Property="Foreground" Value="{DynamicResource AccentTextFillColorPrimaryBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 🎨 DataGrid 单元格样式 -->
    <Style x:Key="DataGridCellStyle" TargetType="DataGridCell">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="HorizontalContentAlignment" Value="Left"/>

        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background">
                    <Setter.Value>
                        <SolidColorBrush Color="{DynamicResource SystemAccentColor}" Opacity="0.05"/>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger Property="IsSelected" Value="True">
                <Setter Property="Background">
                    <Setter.Value>
                        <SolidColorBrush Color="{DynamicResource SystemAccentColor}" Opacity="0.15"/>
                    </Setter.Value>
                </Setter>
                <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
            </Trigger>
            <Trigger Property="IsKeyboardFocusWithin" Value="True">
                <Setter Property="BorderBrush" Value="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                <Setter Property="BorderThickness" Value="1"/>
            </Trigger>
            <!-- 鼠标悬停且选中的状态 -->
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="True"/>
                    <Condition Property="IsSelected" Value="True"/>
                </MultiTrigger.Conditions>
                <Setter Property="Background">
                    <Setter.Value>
                        <SolidColorBrush Color="{DynamicResource SystemAccentColor}" Opacity="0.2"/>
                    </Setter.Value>
                </Setter>
            </MultiTrigger>
        </Style.Triggers>
    </Style>



    <!-- 🎨 DataGrid 数据验证错误样式 -->
    <Style x:Key="DataGridValidationErrorStyle" TargetType="DataGridCell">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="8,4"/>
        <Setter Property="HorizontalContentAlignment" Value="Left"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>

        <Style.Triggers>
            <!-- 验证错误时的样式 -->
            <Trigger Property="Validation.HasError" Value="True">
                <Setter Property="Background" Value="#FFFFE6E6"/>
                <Setter Property="BorderBrush" Value="Red"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="ToolTip" Value="{Binding RelativeSource={RelativeSource Self}, Path=(Validation.Errors)[0].ErrorContent}"/>
            </Trigger>
        </Style.Triggers>
    </Style>



    <!-- 🎨 应用默认样式到所有 DataGrid 控件 -->
    <Style TargetType="DataGrid" BasedOn="{StaticResource DataGridStyle}"/>
    <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource DataGridColumnHeaderStyle}"/>
    <Style TargetType="DataGridCell" BasedOn="{StaticResource DataGridCellStyle}"/>
    <Style TargetType="DataGridRow" BasedOn="{StaticResource DataGridRowStyle}"/>

</ResourceDictionary>
