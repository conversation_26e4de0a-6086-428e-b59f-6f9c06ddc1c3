<!-- ColorShowcase 颜色展示示例 -->
<Grid>
    <Grid.RowDefinitions>
        <RowDefinition Height="Auto"/>
        <RowDefinition Height="*"/>
    </Grid.RowDefinitions>

    <!-- 顶部控制栏 -->
    <ui:Card Grid.Row="0" Margin="20,20,20,10" Padding="20">
        <StackPanel Orientation="Horizontal" HorizontalAlignment="SpaceBetween">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="WPF-UI 颜色展示"
                          FontSize="18"
                          FontWeight="SemiBold"
                          VerticalAlignment="Center"
                          Margin="0,0,20,0"/>
                
                <ui:Button Content="🌙 切换主题"
                          Command="{Binding ToggleThemeCommand}"
                          Appearance="Secondary"/>
            </StackPanel>

            <StackPanel Orientation="Horizontal">
                <ui:Button Content="📋 复制颜色"
                          Command="{Binding CopyColorCommand}"
                          Margin="0,0,10,0"/>
                
                <ui:Button Content="🔄 刷新"
                          Command="{Binding RefreshColorsCommand}"/>
            </StackPanel>
        </StackPanel>
    </ui:Card>

    <!-- 颜色展示区域 -->
    <ScrollViewer Grid.Row="1" Margin="20,0,20,20">
        <StackPanel>
            
            <!-- 背景颜色 -->
            <ui:Card Margin="0,0,0,20" Padding="20">
                <StackPanel>
                    <TextBlock Text="🎨 背景颜色 (Background Colors)"
                              FontSize="16"
                              FontWeight="SemiBold"
                              Margin="0,0,0,15"/>
                    
                    <ItemsControl ItemsSource="{Binding BackgroundColors}">
                        <ItemsControl.ItemsPanel>
                            <ItemsPanelTemplate>
                                <UniformGrid Columns="3"/>
                            </ItemsPanelTemplate>
                        </ItemsControl.ItemsPanel>
                        
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border Background="{Binding Brush}"
                                       BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                       BorderThickness="1"
                                       CornerRadius="4"
                                       Height="80"
                                       Margin="5"
                                       Cursor="Hand">
                                    
                                    <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                                        <TextBlock Text="{Binding Name}"
                                                  FontWeight="SemiBold"
                                                  HorizontalAlignment="Center"
                                                  Foreground="{Binding ContrastTextBrush}"/>
                                        
                                        <TextBlock Text="{Binding HexValue}"
                                                  FontSize="12"
                                                  HorizontalAlignment="Center"
                                                  Foreground="{Binding ContrastTextBrush}"
                                                  Margin="0,5,0,0"/>
                                    </StackPanel>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </StackPanel>
            </ui:Card>

            <!-- 文字颜色 -->
            <ui:Card Margin="0,0,0,20" Padding="20">
                <StackPanel>
                    <TextBlock Text="📝 文字颜色 (Text Colors)"
                              FontSize="16"
                              FontWeight="SemiBold"
                              Margin="0,0,0,15"/>
                    
                    <ItemsControl ItemsSource="{Binding TextColors}">
                        <ItemsControl.ItemsPanel>
                            <ItemsPanelTemplate>
                                <UniformGrid Columns="4"/>
                            </ItemsPanelTemplate>
                        </ItemsControl.ItemsPanel>
                        
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border Background="{DynamicResource ControlFillColorDefaultBrush}"
                                       BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                       BorderThickness="1"
                                       CornerRadius="4"
                                       Height="60"
                                       Margin="5"
                                       Cursor="Hand">
                                    
                                    <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                                        <TextBlock Text="{Binding Name}"
                                                  FontWeight="SemiBold"
                                                  HorizontalAlignment="Center"
                                                  Foreground="{Binding Brush}"/>
                                        
                                        <TextBlock Text="{Binding HexValue}"
                                                  FontSize="10"
                                                  HorizontalAlignment="Center"
                                                  Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                  Margin="0,2,0,0"/>
                                    </StackPanel>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </StackPanel>
            </ui:Card>

            <!-- 强调色 -->
            <ui:Card Margin="0,0,0,20" Padding="20">
                <StackPanel>
                    <TextBlock Text="✨ 强调色 (Accent Colors)"
                              FontSize="16"
                              FontWeight="SemiBold"
                              Margin="0,0,0,15"/>
                    
                    <ItemsControl ItemsSource="{Binding AccentColors}">
                        <ItemsControl.ItemsPanel>
                            <ItemsPanelTemplate>
                                <UniformGrid Columns="3"/>
                            </ItemsPanelTemplate>
                        </ItemsControl.ItemsPanel>
                        
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border Background="{Binding Brush}"
                                       BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                       BorderThickness="1"
                                       CornerRadius="4"
                                       Height="80"
                                       Margin="5"
                                       Cursor="Hand">
                                    
                                    <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                                        <TextBlock Text="{Binding Name}"
                                                  FontWeight="SemiBold"
                                                  HorizontalAlignment="Center"
                                                  Foreground="White"/>
                                        
                                        <TextBlock Text="{Binding HexValue}"
                                                  FontSize="12"
                                                  HorizontalAlignment="Center"
                                                  Foreground="White"
                                                  Margin="0,5,0,0"/>
                                    </StackPanel>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </StackPanel>
            </ui:Card>

        </StackPanel>
    </ScrollViewer>
</Grid>
