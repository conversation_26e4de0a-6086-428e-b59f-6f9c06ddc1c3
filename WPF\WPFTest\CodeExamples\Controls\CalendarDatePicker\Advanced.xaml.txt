<!-- CalendarDatePicker 高级用法示例 -->
<StackPanel Margin="20" Spacing="15">

    <!-- 日期范围选择 -->
    <GroupBox Header="日期范围选择" Padding="15">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <TextBlock Grid.Column="0" Text="开始日期：" VerticalAlignment="Center" Margin="0,0,8,0"/>
            <ui:CalendarDatePicker Grid.Column="1"
                                   Date="{Binding StartDate, Mode=TwoWay}"
                                   Style="{StaticResource CalendarDatePickerStyle}"
                                   Icon="{ui:SymbolIcon Calendar24}"
                                   Margin="0,0,16,0"/>

            <TextBlock Grid.Column="2" Text="结束日期：" VerticalAlignment="Center" Margin="0,0,8,0"/>
            <ui:CalendarDatePicker Grid.Column="3"
                                   Date="{Binding EndDate, Mode=TwoWay}"
                                   Style="{StaticResource CalendarDatePickerStyle}"
                                   Icon="{ui:SymbolIcon Calendar24}"
                                   Margin="0,0,16,0"/>
            
            <StackPanel Grid.Column="4" VerticalAlignment="Center">
                <TextBlock Text="{Binding DateRangeDays, StringFormat='间隔: {0} 天'}"
                           FontSize="12"
                           Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"/>
            </StackPanel>
        </Grid>
    </GroupBox>

    <!-- 自定义日历配置 -->
    <GroupBox Header="自定义日历配置" Padding="15">
        <StackPanel>
            <!-- 周开始日配置 -->
            <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                <TextBlock Text="一周开始日：" VerticalAlignment="Center" Margin="0,0,8,0"/>
                <ui:CalendarDatePicker Date="{Binding SelectedDate, Mode=TwoWay}"
                                       FirstDayOfWeek="Sunday"
                                       Style="{StaticResource CalendarDatePickerStyle}"
                                       Margin="0,0,8,0"/>
                <TextBlock Text="(周日开始)" VerticalAlignment="Center" FontSize="11" 
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
            </StackPanel>
            
            <!-- 今天高亮配置 -->
            <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                <TextBlock Text="今天高亮：" VerticalAlignment="Center" Margin="0,0,8,0"/>
                <ui:CalendarDatePicker Date="{Binding SelectedDate, Mode=TwoWay}"
                                       IsTodayHighlighted="False"
                                       Style="{StaticResource CalendarDatePickerStyle}"
                                       Margin="0,0,8,0"/>
                <TextBlock Text="(不高亮今天)" VerticalAlignment="Center" FontSize="11" 
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
            </StackPanel>
        </StackPanel>
    </GroupBox>

    <!-- 程序化控制 -->
    <GroupBox Header="程序化控制" Padding="15">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <!-- 日期选择器 -->
            <StackPanel Grid.Column="0" Margin="0,0,16,0">
                <TextBlock Text="程序化控制的日期选择器：" Margin="0,0,0,8"/>
                <ui:CalendarDatePicker Date="{Binding ProgrammaticDate, Mode=TwoWay}"
                                       IsCalendarOpen="{Binding IsCalendarOpen, Mode=TwoWay}"
                                       Style="{StaticResource LargeCalendarDatePickerStyle}"
                                       Icon="{ui:SymbolIcon Settings24}"/>

                <TextBlock Text="{Binding ProgrammaticDate, StringFormat='当前日期: {0:yyyy-MM-dd dddd}'}"
                           Margin="0,8,0,0"
                           FontSize="12"
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
            </StackPanel>

            <!-- 控制按钮 -->
            <StackPanel Grid.Column="1" VerticalAlignment="Top">
                <ui:Button Content="打开日历"
                           Command="{Binding OpenCalendarCommand}"
                           Appearance="Secondary"
                           Icon="{ui:SymbolIcon ChevronDown24}"
                           Margin="0,0,0,8"/>
                <ui:Button Content="关闭日历"
                           Command="{Binding CloseCalendarCommand}"
                           Appearance="Secondary"
                           Icon="{ui:SymbolIcon ChevronUp24}"
                           Margin="0,0,0,8"/>
                <ui:Button Content="设置生日"
                           Command="{Binding SetBirthdayCommand}"
                           Appearance="Secondary"
                           Icon="{ui:SymbolIcon Calendar24}"
                           Margin="0,0,0,8"/>
                <ui:Button Content="设置明年"
                           Command="{Binding SetNextYearCommand}"
                           Appearance="Secondary"
                           Icon="{ui:SymbolIcon ArrowClockwise24}"/>
            </StackPanel>
        </Grid>
    </GroupBox>

    <!-- 数据验证示例 -->
    <GroupBox Header="数据验证示例" Padding="15">
        <StackPanel>
            <!-- 年龄验证 -->
            <StackPanel Margin="0,0,0,12">
                <TextBlock Text="出生日期（必须满18岁）：" Margin="0,0,0,4"/>
                <ui:CalendarDatePicker Date="{Binding ValidatedBirthDate, Mode=TwoWay, ValidatesOnDataErrors=True}"
                                       Style="{StaticResource CalendarDatePickerStyle}"
                                       Icon="{ui:SymbolIcon Calendar24}"/>
                <TextBlock Text="{Binding AgeValidationMessage}"
                           FontSize="11"
                           Foreground="{DynamicResource SystemErrorTextColorBrush}"
                           Margin="0,4,0,0"/>
            </StackPanel>

            <!-- 未来日期验证 -->
            <StackPanel Margin="0,0,0,12">
                <TextBlock Text="预约日期（不能是过去的日期）：" Margin="0,0,0,4"/>
                <ui:CalendarDatePicker Date="{Binding AppointmentDate, Mode=TwoWay, ValidatesOnDataErrors=True}"
                                       Style="{StaticResource CalendarDatePickerStyle}"
                                       Icon="{ui:SymbolIcon Timer24}"/>
                <TextBlock Text="{Binding AppointmentValidationMessage}"
                           FontSize="11"
                           Foreground="{DynamicResource SystemErrorTextColorBrush}"
                           Margin="0,4,0,0"/>
            </StackPanel>
        </StackPanel>
    </GroupBox>

    <!-- 多语言和格式化 -->
    <GroupBox Header="多语言和格式化" Padding="15">
        <StackPanel>
            <TextBlock Text="不同格式显示：" Margin="0,0,0,8"/>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <!-- 中文格式 -->
                <StackPanel Grid.Column="0" Margin="0,0,8,0">
                    <TextBlock Text="中文格式：" FontSize="11" Margin="0,0,0,4"/>
                    <ui:CalendarDatePicker Date="{Binding SelectedDate, Mode=TwoWay}"
                                           Style="{StaticResource SmallCalendarDatePickerStyle}"/>
                    <TextBlock Text="{Binding SelectedDate, StringFormat='{}{0:yyyy年MM月dd日}'}"
                               FontSize="10"
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                               Margin="0,4,0,0"/>
                </StackPanel>
                
                <!-- 英文格式 -->
                <StackPanel Grid.Column="1" Margin="4,0">
                    <TextBlock Text="英文格式：" FontSize="11" Margin="0,0,0,4"/>
                    <ui:CalendarDatePicker Date="{Binding SelectedDate, Mode=TwoWay}"
                                           Style="{StaticResource SmallCalendarDatePickerStyle}"/>
                    <TextBlock Text="{Binding SelectedDate, StringFormat='{}{0:MMMM dd, yyyy}'}"
                               FontSize="10"
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                               Margin="0,4,0,0"/>
                </StackPanel>
                
                <!-- ISO格式 -->
                <StackPanel Grid.Column="2" Margin="8,0,0,0">
                    <TextBlock Text="ISO格式：" FontSize="11" Margin="0,0,0,4"/>
                    <ui:CalendarDatePicker Date="{Binding SelectedDate, Mode=TwoWay}"
                                           Style="{StaticResource SmallCalendarDatePickerStyle}"/>
                    <TextBlock Text="{Binding SelectedDate, StringFormat='{}{0:yyyy-MM-dd}'}"
                               FontSize="10"
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                               Margin="0,4,0,0"/>
                </StackPanel>
            </Grid>
        </StackPanel>
    </GroupBox>

</StackPanel>
