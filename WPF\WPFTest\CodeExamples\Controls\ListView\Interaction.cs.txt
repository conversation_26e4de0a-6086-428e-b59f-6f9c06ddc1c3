// ListView 交互功能 C# 代码示例
// 展示如何实现各种 ListView 交互功能

using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows.Data;
using System.Windows.Input;

/// <summary>
/// 交互功能 ListView ViewModel
/// </summary>
public partial class InteractionListViewViewModel : ObservableObject
{
    #region 属性

    /// <summary>
    /// 数据项集合
    /// </summary>
    [ObservableProperty]
    private ObservableCollection<InteractionDataItem> dataItems = new();

    /// <summary>
    /// 选择的数据项
    /// </summary>
    [ObservableProperty]
    private InteractionDataItem? selectedDataItem;

    /// <summary>
    /// 多选项集合
    /// </summary>
    [ObservableProperty]
    private ObservableCollection<InteractionDataItem> selectedItems = new();

    /// <summary>
    /// 是否启用多选
    /// </summary>
    [ObservableProperty]
    private bool isMultiSelectEnabled = false;

    /// <summary>
    /// 搜索文本
    /// </summary>
    [ObservableProperty]
    private string searchText = string.Empty;

    /// <summary>
    /// 选择的分类
    /// </summary>
    [ObservableProperty]
    private string? selectedCategory;

    /// <summary>
    /// 分类列表
    /// </summary>
    [ObservableProperty]
    private ObservableCollection<string> categories = new();

    /// <summary>
    /// 源项目列表（拖拽功能）
    /// </summary>
    [ObservableProperty]
    private ObservableCollection<string> sourceItems = new();

    /// <summary>
    /// 目标项目列表（拖拽功能）
    /// </summary>
    [ObservableProperty]
    private ObservableCollection<string> targetItems = new();

    /// <summary>
    /// 筛选后的项集合
    /// </summary>
    public ICollectionView FilteredItems { get; private set; }

    #endregion

    /// <summary>
    /// 构造函数
    /// </summary>
    public InteractionListViewViewModel()
    {
        InitializeData();
        SetupFiltering();
        PropertyChanged += OnPropertyChanged;
    }

    #region 命令

    /// <summary>
    /// 项目双击命令
    /// </summary>
    [RelayCommand]
    private void ItemDoubleClick(InteractionDataItem item)
    {
        if (item != null)
        {
            Console.WriteLine($"双击了项目: {item.Name}");
            // 执行双击操作，如打开详情页面
            ShowItemDetails(item);
        }
    }

    /// <summary>
    /// 编辑项目命令
    /// </summary>
    [RelayCommand]
    private void EditItem(InteractionDataItem item)
    {
        if (item != null)
        {
            Console.WriteLine($"编辑项目: {item.Name}");
            // 实现编辑逻辑
            item.IsEditing = true;
        }
    }

    /// <summary>
    /// 删除项目命令
    /// </summary>
    [RelayCommand]
    private void DeleteItem(InteractionDataItem item)
    {
        if (item != null)
        {
            Console.WriteLine($"删除项目: {item.Name}");
            DataItems.Remove(item);
            SelectedItems.Remove(item);
        }
    }

    /// <summary>
    /// 复制项目命令
    /// </summary>
    [RelayCommand]
    private void CopyItem(InteractionDataItem item)
    {
        if (item != null)
        {
            Console.WriteLine($"复制项目: {item.Name}");
            // 实现复制逻辑
            var copiedItem = new InteractionDataItem
            {
                Name = $"{item.Name} (副本)",
                Description = item.Description,
                Icon = item.Icon,
                Category = item.Category
            };
            DataItems.Add(copiedItem);
        }
    }

    /// <summary>
    /// 显示属性命令
    /// </summary>
    [RelayCommand]
    private void ShowProperties(InteractionDataItem item)
    {
        if (item != null)
        {
            Console.WriteLine($"显示属性: {item.Name}");
            // 显示属性对话框
        }
    }

    /// <summary>
    /// 项目选择变化命令
    /// </summary>
    [RelayCommand]
    private void ItemSelectionChanged(InteractionDataItem item)
    {
        if (item != null)
        {
            if (item.IsSelected && !SelectedItems.Contains(item))
            {
                SelectedItems.Add(item);
            }
            else if (!item.IsSelected && SelectedItems.Contains(item))
            {
                SelectedItems.Remove(item);
            }
            
            Console.WriteLine($"选择状态变化: {item.Name} - {item.IsSelected}");
        }
    }

    /// <summary>
    /// 开始拖拽命令
    /// </summary>
    [RelayCommand]
    private void StartDrag(string item)
    {
        Console.WriteLine($"开始拖拽: {item}");
        // 实现拖拽开始逻辑
        // 可以设置拖拽数据和效果
    }

    /// <summary>
    /// 放置项目命令
    /// </summary>
    [RelayCommand]
    private void DropItem()
    {
        Console.WriteLine("处理拖放操作");
        // 实现拖放逻辑
        // 从源列表移动到目标列表
    }

    /// <summary>
    /// 清除筛选命令
    /// </summary>
    [RelayCommand]
    private void ClearFilter()
    {
        SearchText = string.Empty;
        SelectedCategory = null;
        FilteredItems.Refresh();
        Console.WriteLine("已清除所有筛选条件");
    }

    /// <summary>
    /// 全选命令
    /// </summary>
    [RelayCommand]
    private void SelectAll()
    {
        if (IsMultiSelectEnabled)
        {
            foreach (var item in DataItems)
            {
                item.IsSelected = true;
                if (!SelectedItems.Contains(item))
                {
                    SelectedItems.Add(item);
                }
            }
            Console.WriteLine("已全选所有项目");
        }
    }

    /// <summary>
    /// 取消全选命令
    /// </summary>
    [RelayCommand]
    private void UnselectAll()
    {
        foreach (var item in DataItems)
        {
            item.IsSelected = false;
        }
        SelectedItems.Clear();
        Console.WriteLine("已取消全选");
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 初始化数据
    /// </summary>
    private void InitializeData()
    {
        // 初始化数据项
        DataItems.Clear();
        var sampleData = new[]
        {
            new InteractionDataItem { Name = "文档处理", Description = "处理各种文档格式", Icon = "Document24", Category = "办公" },
            new InteractionDataItem { Name = "图像编辑", Description = "编辑和处理图像", Icon = "Image24", Category = "媒体" },
            new InteractionDataItem { Name = "音频播放", Description = "播放音频文件", Icon = "Speaker24", Category = "媒体" },
            new InteractionDataItem { Name = "数据分析", Description = "分析和可视化数据", Icon = "DataBarVertical24", Category = "分析" },
            new InteractionDataItem { Name = "网络工具", Description = "网络诊断和监控", Icon = "Wifi124", Category = "网络" }
        };

        foreach (var item in sampleData)
        {
            DataItems.Add(item);
        }

        // 初始化分类
        Categories.Clear();
        Categories.Add("全部");
        var uniqueCategories = sampleData.Select(x => x.Category).Distinct();
        foreach (var category in uniqueCategories)
        {
            Categories.Add(category);
        }

        // 初始化拖拽列表
        SourceItems.Clear();
        for (int i = 1; i <= 5; i++)
        {
            SourceItems.Add($"源项目 {i}");
        }

        TargetItems.Clear();
    }

    /// <summary>
    /// 设置筛选功能
    /// </summary>
    private void SetupFiltering()
    {
        FilteredItems = CollectionViewSource.GetDefaultView(DataItems);
        FilteredItems.Filter = FilterPredicate;
    }

    /// <summary>
    /// 筛选谓词
    /// </summary>
    private bool FilterPredicate(object obj)
    {
        if (obj is not InteractionDataItem item)
            return false;

        // 文本筛选
        bool textMatch = string.IsNullOrEmpty(SearchText) ||
                        item.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                        item.Description.Contains(SearchText, StringComparison.OrdinalIgnoreCase);

        // 分类筛选
        bool categoryMatch = SelectedCategory == "全部" || SelectedCategory == null ||
                           item.Category == SelectedCategory;

        return textMatch && categoryMatch;
    }

    /// <summary>
    /// 显示项目详情
    /// </summary>
    private void ShowItemDetails(InteractionDataItem item)
    {
        Console.WriteLine($"显示项目详情: {item.Name}");
        Console.WriteLine($"描述: {item.Description}");
        Console.WriteLine($"分类: {item.Category}");
        Console.WriteLine($"图标: {item.Icon}");
    }

    /// <summary>
    /// 属性变化处理
    /// </summary>
    private void OnPropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        switch (e.PropertyName)
        {
            case nameof(SearchText):
            case nameof(SelectedCategory):
                FilteredItems.Refresh();
                break;
            case nameof(IsMultiSelectEnabled):
                if (!IsMultiSelectEnabled)
                {
                    UnselectAll();
                }
                break;
        }
    }

    #endregion
}

/// <summary>
/// 交互数据项模型
/// </summary>
public partial class InteractionDataItem : ObservableObject
{
    [ObservableProperty]
    private string name = string.Empty;

    [ObservableProperty]
    private string description = string.Empty;

    [ObservableProperty]
    private string icon = "Document24";

    [ObservableProperty]
    private string category = "默认";

    [ObservableProperty]
    private bool isSelected = false;

    [ObservableProperty]
    private bool isEditing = false;

    [ObservableProperty]
    private bool isHighlighted = false;
}

/*
交互功能实现要点：

1. 事件处理：
   - 使用命令模式处理用户交互
   - 实现双击、右键等事件
   - 提供键盘快捷键支持

2. 多选功能：
   - 管理选择状态
   - 批量操作支持
   - 选择模式切换

3. 拖拽功能：
   - 实现拖拽开始和结束
   - 提供视觉反馈
   - 处理拖放数据

4. 搜索筛选：
   - 实时搜索功能
   - 多条件筛选
   - 筛选状态管理

5. 性能优化：
   - 使用虚拟化
   - 延迟加载
   - 事件去抖动

最佳实践：
- 保持响应性
- 提供用户反馈
- 考虑可访问性
- 实现撤销/重做
*/
