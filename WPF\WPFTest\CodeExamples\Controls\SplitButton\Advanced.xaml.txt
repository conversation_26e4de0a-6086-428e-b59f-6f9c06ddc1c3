<!-- SplitButton 高级示例 -->
<StackPanel Margin="20" Spacing="15">
    
    <!-- 复杂菜单结构 -->
    <GroupBox Header="复杂菜单结构 - 多级菜单" Padding="15">
        <StackPanel>
            <TextBlock Text="SplitButton 支持复杂的多级菜单结构和分组" FontSize="12" Margin="0,0,0,10"/>
            
            <ui:SplitButton Content="文件操作" 
                            Command="{Binding HandlePrimaryActionCommand}"
                            CommandParameter="文件操作"
                            Appearance="Primary"
                            Margin="8">
                <ui:SplitButton.Icon>
                    <ui:SymbolIcon Symbol="Document24"/>
                </ui:SplitButton.Icon>
                <ui:SplitButton.Flyout>
                    <ContextMenu>
                        <!-- 新建分组 -->
                        <MenuItem Header="新建">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="Add24"/>
                            </MenuItem.Icon>
                            <MenuItem Header="新建文档" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="新建文档">
                                <MenuItem.Icon>
                                    <ui:SymbolIcon Symbol="Document24"/>
                                </MenuItem.Icon>
                            </MenuItem>
                            <MenuItem Header="新建文件夹" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="新建文件夹">
                                <MenuItem.Icon>
                                    <ui:SymbolIcon Symbol="Folder24"/>
                                </MenuItem.Icon>
                            </MenuItem>
                            <MenuItem Header="新建项目" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="新建项目">
                                <MenuItem.Icon>
                                    <ui:SymbolIcon Symbol="FolderOpen24"/>
                                </MenuItem.Icon>
                            </MenuItem>
                        </MenuItem>
                        
                        <Separator/>
                        
                        <!-- 打开分组 -->
                        <MenuItem Header="打开">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="Open24"/>
                            </MenuItem.Icon>
                            <MenuItem Header="打开文件" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="打开文件">
                                <MenuItem.Icon>
                                    <ui:SymbolIcon Symbol="DocumentOpen24"/>
                                </MenuItem.Icon>
                            </MenuItem>
                            <MenuItem Header="打开文件夹" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="打开文件夹">
                                <MenuItem.Icon>
                                    <ui:SymbolIcon Symbol="FolderOpen24"/>
                                </MenuItem.Icon>
                            </MenuItem>
                            <MenuItem Header="最近文件">
                                <MenuItem.Icon>
                                    <ui:SymbolIcon Symbol="History24"/>
                                </MenuItem.Icon>
                                <MenuItem Header="文档1.txt" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="文档1.txt"/>
                                <MenuItem Header="文档2.txt" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="文档2.txt"/>
                                <MenuItem Header="项目.sln" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="项目.sln"/>
                            </MenuItem>
                        </MenuItem>
                        
                        <Separator/>
                        
                        <!-- 导入导出分组 -->
                        <MenuItem Header="导入" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="导入">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="ArrowImport24"/>
                            </MenuItem.Icon>
                        </MenuItem>
                        <MenuItem Header="导出" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="导出">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="ArrowExport24"/>
                            </MenuItem.Icon>
                        </MenuItem>
                    </ContextMenu>
                </ui:SplitButton.Flyout>
            </ui:SplitButton>
        </StackPanel>
    </GroupBox>

    <!-- 条件菜单项 -->
    <GroupBox Header="条件菜单项 - 动态启用/禁用" Padding="15">
        <StackPanel>
            <TextBlock Text="根据应用状态动态控制菜单项的启用状态" FontSize="12" Margin="0,0,0,10"/>
            
            <ui:SplitButton Content="编辑操作" 
                            Command="{Binding HandlePrimaryActionCommand}"
                            CommandParameter="编辑操作"
                            Margin="8">
                <ui:SplitButton.Icon>
                    <ui:SymbolIcon Symbol="Edit24"/>
                </ui:SplitButton.Icon>
                <ui:SplitButton.Flyout>
                    <ContextMenu>
                        <MenuItem Header="撤销" 
                                  Command="{Binding HandleDropdownInteractionCommand}" 
                                  CommandParameter="撤销"
                                  IsEnabled="{Binding CanUndo}">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="ArrowUndo24"/>
                            </MenuItem.Icon>
                        </MenuItem>
                        <MenuItem Header="重做" 
                                  Command="{Binding HandleDropdownInteractionCommand}" 
                                  CommandParameter="重做"
                                  IsEnabled="{Binding CanRedo}">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="ArrowRedo24"/>
                            </MenuItem.Icon>
                        </MenuItem>
                        
                        <Separator/>
                        
                        <MenuItem Header="剪切" 
                                  Command="{Binding HandleDropdownInteractionCommand}" 
                                  CommandParameter="剪切"
                                  IsEnabled="{Binding HasSelection}">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="Cut24"/>
                            </MenuItem.Icon>
                        </MenuItem>
                        <MenuItem Header="复制" 
                                  Command="{Binding HandleDropdownInteractionCommand}" 
                                  CommandParameter="复制"
                                  IsEnabled="{Binding HasSelection}">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="Copy24"/>
                            </MenuItem.Icon>
                        </MenuItem>
                        <MenuItem Header="粘贴" 
                                  Command="{Binding HandleDropdownInteractionCommand}" 
                                  CommandParameter="粘贴"
                                  IsEnabled="{Binding CanPaste}">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="Paste24"/>
                            </MenuItem.Icon>
                        </MenuItem>
                    </ContextMenu>
                </ui:SplitButton.Flyout>
            </ui:SplitButton>
        </StackPanel>
    </GroupBox>

    <!-- 自定义样式 -->
    <GroupBox Header="自定义样式 - 特殊外观" Padding="15">
        <StackPanel>
            <TextBlock Text="通过自定义样式创建特殊外观的 SplitButton" FontSize="12" Margin="0,0,0,10"/>
            
            <WrapPanel>
                <!-- 大尺寸 SplitButton -->
                <ui:SplitButton Content="大尺寸按钮" 
                                Command="{Binding HandlePrimaryActionCommand}"
                                CommandParameter="大尺寸按钮"
                                FontSize="16"
                                Padding="20,12"
                                Margin="8">
                    <ui:SplitButton.Icon>
                        <ui:SymbolIcon Symbol="Settings24" FontSize="20"/>
                    </ui:SplitButton.Icon>
                    <ui:SplitButton.Flyout>
                        <ContextMenu>
                            <MenuItem Header="选项 1" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="选项 1"/>
                            <MenuItem Header="选项 2" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="选项 2"/>
                        </ContextMenu>
                    </ui:SplitButton.Flyout>
                </ui:SplitButton>

                <!-- 小尺寸 SplitButton -->
                <ui:SplitButton Content="小尺寸" 
                                Command="{Binding HandlePrimaryActionCommand}"
                                CommandParameter="小尺寸"
                                FontSize="11"
                                Padding="8,4"
                                Margin="8">
                    <ui:SplitButton.Icon>
                        <ui:SymbolIcon Symbol="Settings24" FontSize="14"/>
                    </ui:SplitButton.Icon>
                    <ui:SplitButton.Flyout>
                        <ContextMenu>
                            <MenuItem Header="选项 1" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="选项 1"/>
                            <MenuItem Header="选项 2" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="选项 2"/>
                        </ContextMenu>
                    </ui:SplitButton.Flyout>
                </ui:SplitButton>
            </WrapPanel>
        </StackPanel>
    </GroupBox>

    <!-- 工具栏集成 -->
    <GroupBox Header="工具栏集成 - 实际应用场景" Padding="15">
        <StackPanel>
            <TextBlock Text="SplitButton 在工具栏中的典型应用" FontSize="12" Margin="0,0,0,10"/>
            
            <!-- 模拟工具栏 -->
            <Border Background="{DynamicResource ControlFillColorInputActiveBrush}"
                    BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="4"
                    Padding="8">
                <StackPanel Orientation="Horizontal">
                    <!-- 文件操作 -->
                    <ui:SplitButton Content="新建" 
                                    Command="{Binding HandlePrimaryActionCommand}"
                                    CommandParameter="新建"
                                    Appearance="Primary"
                                    Margin="4">
                        <ui:SplitButton.Icon>
                            <ui:SymbolIcon Symbol="Add24"/>
                        </ui:SplitButton.Icon>
                        <ui:SplitButton.Flyout>
                            <ContextMenu>
                                <MenuItem Header="新建文档" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="新建文档"/>
                                <MenuItem Header="新建项目" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="新建项目"/>
                                <MenuItem Header="新建解决方案" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="新建解决方案"/>
                            </ContextMenu>
                        </ui:SplitButton.Flyout>
                    </ui:SplitButton>

                    <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" Margin="4"/>

                    <!-- 格式操作 -->
                    <ui:SplitButton Content="格式" 
                                    Command="{Binding HandlePrimaryActionCommand}"
                                    CommandParameter="格式"
                                    Margin="4">
                        <ui:SplitButton.Icon>
                            <ui:SymbolIcon Symbol="TextBold24"/>
                        </ui:SplitButton.Icon>
                        <ui:SplitButton.Flyout>
                            <ContextMenu>
                                <MenuItem Header="粗体" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="粗体">
                                    <MenuItem.Icon>
                                        <ui:SymbolIcon Symbol="TextBold24"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <MenuItem Header="斜体" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="斜体">
                                    <MenuItem.Icon>
                                        <ui:SymbolIcon Symbol="TextItalic24"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <MenuItem Header="下划线" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="下划线">
                                    <MenuItem.Icon>
                                        <ui:SymbolIcon Symbol="TextUnderline24"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                            </ContextMenu>
                        </ui:SplitButton.Flyout>
                    </ui:SplitButton>

                    <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" Margin="4"/>

                    <!-- 视图操作 -->
                    <ui:SplitButton Content="视图" 
                                    Command="{Binding HandlePrimaryActionCommand}"
                                    CommandParameter="视图"
                                    Margin="4">
                        <ui:SplitButton.Icon>
                            <ui:SymbolIcon Symbol="Eye24"/>
                        </ui:SplitButton.Icon>
                        <ui:SplitButton.Flyout>
                            <ContextMenu>
                                <MenuItem Header="普通视图" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="普通视图"/>
                                <MenuItem Header="大纲视图" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="大纲视图"/>
                                <MenuItem Header="页面布局" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="页面布局"/>
                                <Separator/>
                                <MenuItem Header="全屏模式" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="全屏模式"/>
                            </ContextMenu>
                        </ui:SplitButton.Flyout>
                    </ui:SplitButton>
                </StackPanel>
            </Border>
        </StackPanel>
    </GroupBox>

</StackPanel>
