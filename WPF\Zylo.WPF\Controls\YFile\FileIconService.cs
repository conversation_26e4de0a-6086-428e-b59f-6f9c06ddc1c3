using System.IO;

namespace Zylo.WPF.Controls.YFile;

/// <summary>
/// 文件图标服务 - 提供专业的文件类型图标
/// </summary>
public static class FileIconService
{
    /// <summary>
    /// 文件类型图标映射
    /// </summary>
    private static readonly Dictionary<string, string> FileTypeIcons = new()
    {
        // 文档类型
        { ".txt", "DocumentText24" },
        { ".doc", "Document24" },
        { ".docx", "Document24" },
        { ".pdf", "DocumentPdf24" },
        { ".rtf", "Document24" },
        
        // 表格类型
        { ".xls", "Table24" },
        { ".xlsx", "Table24" },
        { ".csv", "Table24" },
        
        // 演示文稿
        { ".ppt", "SlideShow24" },
        { ".pptx", "SlideShow24" },
        
        // 图片类型
        { ".jpg", "Image24" },
        { ".jpeg", "Image24" },
        { ".png", "Image24" },
        { ".gif", "Image24" },
        { ".bmp", "Image24" },
        { ".ico", "Image24" },
        { ".svg", "Image24" },
        { ".webp", "Image24" },
        
        // 音频类型
        { ".mp3", "MusicNote124" },
        { ".wav", "MusicNote124" },
        { ".flac", "MusicNote124" },
        { ".aac", "MusicNote124" },
        { ".ogg", "MusicNote124" },
        
        // 视频类型
        { ".mp4", "Video24" },
        { ".avi", "Video24" },
        { ".mkv", "Video24" },
        { ".mov", "Video24" },
        { ".wmv", "Video24" },
        { ".flv", "Video24" },
        
        // 压缩文件
        { ".zip", "FolderZip24" },
        { ".rar", "FolderZip24" },
        { ".7z", "FolderZip24" },
        { ".tar", "FolderZip24" },
        { ".gz", "FolderZip24" },
        
        // 代码文件
        { ".cs", "Code24" },
        { ".cpp", "Code24" },
        { ".c", "Code24" },
        { ".h", "Code24" },
        { ".java", "Code24" },
        { ".py", "Code24" },
        { ".js", "Code24" },
        { ".ts", "Code24" },
        { ".html", "Code24" },
        { ".css", "Code24" },
        { ".xml", "Code24" },
        { ".json", "Code24" },
        { ".xaml", "Code24" },
        
        // 可执行文件
        { ".exe", "Apps24" },
        { ".msi", "Apps24" },
        { ".bat", "CommandPrompt24" },
        { ".cmd", "CommandPrompt24" },
        { ".ps1", "CommandPrompt24" },
        
        // 配置文件
        { ".ini", "Settings24" },
        { ".cfg", "Settings24" },
        { ".conf", "Settings24" },
        { ".config", "Settings24" },
        
        // 数据库文件
        { ".db", "Database24" },
        { ".sqlite", "Database24" },
        { ".mdb", "Database24" },
        
        // 字体文件
        { ".ttf", "TextFont24" },
        { ".otf", "TextFont24" },
        { ".woff", "TextFont24" },
        { ".woff2", "TextFont24" }
    };

    /// <summary>
    /// 获取文件图标
    /// </summary>
    /// <param name="extension">文件扩展名</param>
    /// <returns>图标emoji</returns>
    public static string GetFileIcon(string extension)
    {
        if (string.IsNullOrEmpty(extension))
            return "📄";

        var lowerExtension = extension.ToLower();
        return lowerExtension switch
        {
            // 文档类型
            ".txt" => "📝",
            ".doc" or ".docx" => "📄",
            ".pdf" => "📕",
            ".xls" or ".xlsx" => "📊",
            ".ppt" or ".pptx" => "📊",

            // 图片类型
            ".jpg" or ".jpeg" or ".png" or ".gif" or ".bmp" or ".ico" or ".svg" or ".webp" => "🖼️",

            // 音频类型
            ".mp3" or ".wav" or ".flac" or ".aac" or ".ogg" => "🎵",

            // 视频类型
            ".mp4" or ".avi" or ".mkv" or ".mov" or ".wmv" or ".flv" => "🎬",

            // 压缩文件
            ".zip" or ".rar" or ".7z" or ".tar" or ".gz" => "🗜️",

            // 代码文件
            ".cs" or ".js" or ".ts" or ".py" or ".java" or ".cpp" or ".c" or ".h" => "💻",
            ".html" or ".css" or ".xml" or ".json" => "🌐",

            // 可执行文件
            ".exe" or ".msi" or ".app" => "⚙️",

            _ => "📄"
        };
    }

    /// <summary>
    /// 获取文件夹图标
    /// </summary>
    /// <param name="directoryInfo">目录信息</param>
    /// <returns>图标emoji</returns>
    public static string GetFolderIcon(DirectoryInfo directoryInfo)
    {
        // 检查是否为特殊文件夹
        if (directoryInfo.Attributes.HasFlag(FileAttributes.System))
            return "🚫";  // 系统文件夹

        if (directoryInfo.Attributes.HasFlag(FileAttributes.Hidden))
            return "👁️‍🗨️";  // 隐藏文件夹

        // 根据文件夹名称返回特定图标
        var folderName = directoryInfo.Name.ToLowerInvariant();
        return folderName switch
        {
            "documents" or "文档" => "📄",
            "downloads" or "下载" => "⬇️",
            "pictures" or "图片" => "🖼️",
            "music" or "音乐" => "🎵",
            "videos" or "视频" => "🎬",
            "desktop" or "桌面" => "🖥️",
            "temp" or "临时文件" => "🗑️",
            _ => "📁"  // 默认文件夹
        };
    }

    /// <summary>
    /// 获取驱动器图标
    /// </summary>
    /// <param name="driveType">驱动器类型</param>
    /// <returns>图标emoji</returns>
    public static string GetDriveIcon(DriveType driveType)
    {
        return driveType switch
        {
            DriveType.Fixed => "💾",        // 硬盘
            DriveType.Removable => "💿",    // 可移动磁盘
            DriveType.CDRom => "📀",        // 光盘
            DriveType.Network => "🌐",      // 网络驱动器
            DriveType.Ram => "⚡",          // 内存盘
            _ => "💾"                       // 默认硬盘
        };
    }

    /// <summary>
    /// 检查文件类型是否为图片
    /// </summary>
    /// <param name="extension">文件扩展名</param>
    /// <returns>是否为图片</returns>
    public static bool IsImageFile(string extension)
    {
        var imageExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".ico", ".svg", ".webp" };
        return imageExtensions.Contains(extension.ToLower());
    }

    /// <summary>
    /// 检查文件类型是否为音频
    /// </summary>
    /// <param name="extension">文件扩展名</param>
    /// <returns>是否为音频</returns>
    public static bool IsAudioFile(string extension)
    {
        var audioExtensions = new[] { ".mp3", ".wav", ".flac", ".aac", ".ogg" };
        return audioExtensions.Contains(extension.ToLower());
    }

    /// <summary>
    /// 检查文件类型是否为视频
    /// </summary>
    /// <param name="extension">文件扩展名</param>
    /// <returns>是否为视频</returns>
    public static bool IsVideoFile(string extension)
    {
        var videoExtensions = new[] { ".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv" };
        return videoExtensions.Contains(extension.ToLower());
    }

    /// <summary>
    /// 检查文件类型是否为代码文件
    /// </summary>
    /// <param name="extension">文件扩展名</param>
    /// <returns>是否为代码文件</returns>
    public static bool IsCodeFile(string extension)
    {
        var codeExtensions = new[] { ".cs", ".cpp", ".c", ".h", ".java", ".py", ".js", ".ts", ".html", ".css", ".xml", ".json", ".xaml" };
        return codeExtensions.Contains(extension.ToLower());
    }
}
