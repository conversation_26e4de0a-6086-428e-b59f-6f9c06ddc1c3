// PasswordBox C# 高级功能示例
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Windows.Media;

namespace WPFTest.ViewModels.InputControls
{
    public partial class PasswordBoxAdvancedViewModel : ObservableObject
    {
        /// <summary>
        /// 新密码（支持数据绑定）
        /// </summary>
        [ObservableProperty]
        private string basicPassword = string.Empty;

        /// <summary>
        /// 确认密码（支持数据绑定）
        /// </summary>
        [ObservableProperty]
        private string confirmPassword = string.Empty;

        /// <summary>
        /// 生成的密码
        /// </summary>
        [ObservableProperty]
        private string generatedPassword = string.Empty;

        /// <summary>
        /// 演示密码
        /// </summary>
        [ObservableProperty]
        private string demoPassword = string.Empty;

        /// <summary>
        /// 验证消息
        /// </summary>
        [ObservableProperty]
        private string validationMessage = string.Empty;

        /// <summary>
        /// 验证消息颜色
        /// </summary>
        [ObservableProperty]
        private Brush validationMessageColor = Brushes.Gray;

        /// <summary>
        /// 是否显示密码详情
        /// </summary>
        [ObservableProperty]
        private bool showPasswordDetails = false;

        /// <summary>
        /// 演示验证结果
        /// </summary>
        [ObservableProperty]
        private string demoVerificationResult = string.Empty;

        /// <summary>
        /// 演示验证结果颜色
        /// </summary>
        [ObservableProperty]
        private Brush demoVerificationColor = Brushes.Gray;

        /// <summary>
        /// 存储的测试密码哈希
        /// </summary>
        private string storedTestPasswordHash = string.Empty;

        /// <summary>
        /// 密码长度
        /// </summary>
        [ObservableProperty]
        private int passwordLength = 12;

        /// <summary>
        /// 是否包含符号
        /// </summary>
        [ObservableProperty]
        private bool includeSymbols = true;

        /// <summary>
        /// 是否包含数字
        /// </summary>
        [ObservableProperty]
        private bool includeNumbers = true;

        /// <summary>
        /// 是否避免相似字符
        /// </summary>
        [ObservableProperty]
        private bool avoidSimilarChars = false;

        /// <summary>
        /// 密码历史记录
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<PasswordHistoryItem> passwordHistory = new();

        /// <summary>
        /// 验证码
        /// </summary>
        [ObservableProperty]
        private string verificationCode = string.Empty;

        /// <summary>
        /// 密码要求检查结果
        /// </summary>
        [ObservableProperty]
        private PasswordRequirements requirements = new();

        /// <summary>
        /// 验证密码命令
        /// </summary>
        [RelayCommand]
        private void ValidatePassword()
        {
            if (string.IsNullOrEmpty(NewPassword))
            {
                StatusMessage = "请输入密码";
                return;
            }

            if (NewPassword != ConfirmPassword)
            {
                StatusMessage = "两次输入的密码不一致";
                return;
            }

            var (isValid, message) = ValidatePasswordSecurity(NewPassword);
            StatusMessage = message;

            if (isValid)
            {
                // 添加到历史记录
                AddToPasswordHistory(NewPassword);
            }
        }

        /// <summary>
        /// 生成密码命令
        /// </summary>
        [RelayCommand]
        private void GeneratePassword()
        {
            GeneratedPassword = CreateSecurePassword(
                PasswordLength, 
                IncludeSymbols, 
                IncludeNumbers, 
                AvoidSimilarChars);
            
            StatusMessage = $"已生成 {PasswordLength} 位安全密码";
        }

        /// <summary>
        /// 复制密码命令
        /// </summary>
        [RelayCommand]
        private void CopyPassword()
        {
            if (!string.IsNullOrEmpty(GeneratedPassword))
            {
                Clipboard.SetText(GeneratedPassword);
                StatusMessage = "密码已复制到剪贴板";
            }
        }

        /// <summary>
        /// 清除密码历史命令
        /// </summary>
        [RelayCommand]
        private void ClearPasswordHistory()
        {
            PasswordHistory.Clear();
            StatusMessage = "密码历史记录已清除";
        }

        /// <summary>
        /// 删除历史记录项命令
        /// </summary>
        [RelayCommand]
        private void DeleteHistoryItem(PasswordHistoryItem item)
        {
            PasswordHistory.Remove(item);
            StatusMessage = "历史记录项已删除";
        }

        /// <summary>
        /// 验证双因素认证命令
        /// </summary>
        [RelayCommand]
        private void VerifyTwoFactor()
        {
            if (string.IsNullOrEmpty(NewPassword))
            {
                StatusMessage = "请输入密码";
                return;
            }

            if (string.IsNullOrEmpty(VerificationCode) || VerificationCode.Length != 6)
            {
                StatusMessage = "请输入6位验证码";
                return;
            }

            // 模拟验证过程
            if (VerificationCode == "123456") // 示例验证码
            {
                StatusMessage = "双因素认证成功";
            }
            else
            {
                StatusMessage = "验证码错误，请重新输入";
            }
        }

        /// <summary>
        /// 创建安全密码
        /// </summary>
        private static string CreateSecurePassword(int length, bool includeSymbols, bool includeNumbers, bool avoidSimilar)
        {
            const string lowercase = "abcdefghijklmnopqrstuvwxyz";
            const string uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            const string numbers = "0123456789";
            const string symbols = "!@#$%^&*()_+-=[]{}|;:,.<>?";
            const string similarChars = "il1Lo0O";

            var chars = lowercase + uppercase;
            
            if (includeNumbers) chars += numbers;
            if (includeSymbols) chars += symbols;
            
            if (avoidSimilar)
            {
                chars = new string(chars.Where(c => !similarChars.Contains(c)).ToArray());
            }

            var random = new Random();
            var result = new StringBuilder();

            // 确保至少包含一个大写字母、小写字母
            result.Append(lowercase[random.Next(lowercase.Length)]);
            result.Append(uppercase[random.Next(uppercase.Length)]);

            if (includeNumbers)
                result.Append(numbers[random.Next(numbers.Length)]);
            
            if (includeSymbols)
                result.Append(symbols[random.Next(symbols.Length)]);

            // 填充剩余长度
            for (int i = result.Length; i < length; i++)
            {
                result.Append(chars[random.Next(chars.Length)]);
            }

            // 打乱字符顺序
            var shuffled = result.ToString().ToCharArray();
            for (int i = shuffled.Length - 1; i > 0; i--)
            {
                int j = random.Next(i + 1);
                (shuffled[i], shuffled[j]) = (shuffled[j], shuffled[i]);
            }

            return new string(shuffled);
        }

        /// <summary>
        /// 添加到密码历史记录
        /// </summary>
        private void AddToPasswordHistory(string password)
        {
            var hashedPassword = HashPassword(password);
            var historyItem = new PasswordHistoryItem
            {
                HashedPassword = hashedPassword,
                CreatedDate = DateTime.Now,
                DisplayPassword = new string('●', password.Length)
            };

            PasswordHistory.Insert(0, historyItem);

            // 限制历史记录数量
            while (PasswordHistory.Count > 10)
            {
                PasswordHistory.RemoveAt(PasswordHistory.Count - 1);
            }
        }

        /// <summary>
        /// 哈希密码
        /// </summary>
        private static string HashPassword(string password)
        {
            using var sha256 = SHA256.Create();
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
            return Convert.ToBase64String(hashedBytes);
        }

        /// <summary>
        /// 更新密码要求检查
        /// </summary>
        private void UpdatePasswordRequirements(string password)
        {
            Requirements.HasMinLength = password.Length >= 8;
            Requirements.HasUppercase = password.Any(char.IsUpper);
            Requirements.HasLowercase = password.Any(char.IsLower);
            Requirements.HasNumber = password.Any(char.IsDigit);
            Requirements.HasSymbol = password.Any(c => "!@#$%^&*()_+-=[]{}|;:,.<>?".Contains(c));
        }

        /// <summary>
        /// 密码匹配消息
        /// </summary>
        public string PasswordMatchMessage =>
            string.IsNullOrEmpty(BasicPassword) || string.IsNullOrEmpty(ConfirmPassword)
                ? "请输入密码"
                : BasicPassword == ConfirmPassword
                    ? "✅ 密码匹配"
                    : "❌ 密码不匹配";

        /// <summary>
        /// 密码匹配颜色
        /// </summary>
        public Brush PasswordMatchColor =>
            string.IsNullOrEmpty(BasicPassword) || string.IsNullOrEmpty(ConfirmPassword)
                ? Brushes.Gray
                : BasicPassword == ConfirmPassword
                    ? Brushes.Green
                    : Brushes.Red;

        /// <summary>
        /// 验证密码命令
        /// </summary>
        [RelayCommand]
        private void ValidatePassword()
        {
            try
            {
                if (string.IsNullOrEmpty(BasicPassword) || string.IsNullOrEmpty(ConfirmPassword))
                {
                    ShowValidationResult(false, "请输入密码和确认密码");
                    return;
                }

                if (BasicPassword != ConfirmPassword)
                {
                    ShowValidationResult(false, "两次输入的密码不一致");
                    return;
                }

                // 生成密码哈希（模拟加密存储）
                var passwordHash = HashPassword(BasicPassword);
                var strength = CalculatePasswordStrength(BasicPassword);

                var successMessage = $"✅ 密码验证通过！\n" +
                                   $"🔒 密码强度: {GetStrengthLevel(strength)} ({strength:F0}分)\n" +
                                   $"🛡️ 安全哈希: {passwordHash[..16]}...\n" +
                                   $"📅 验证时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}";

                ShowValidationResult(true, successMessage);
            }
            catch (Exception ex)
            {
                ShowValidationResult(false, $"验证过程出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示密码详情命令
        /// </summary>
        [RelayCommand]
        private void ShowPassword()
        {
            ShowPasswordDetails = !ShowPasswordDetails;
        }

        /// <summary>
        /// 生成测试密码命令
        /// </summary>
        [RelayCommand]
        private void GenerateTestPassword()
        {
            var testPassword = "Test123!@#";
            DemoPassword = testPassword;
            storedTestPasswordHash = HashPassword(testPassword);

            DemoVerificationResult = $"✅ 已生成测试密码: {testPassword}\n🔒 密码哈希已存储用于验证";
            DemoVerificationColor = new SolidColorBrush(Colors.Green);
        }

        /// <summary>
        /// 验证演示密码命令
        /// </summary>
        [RelayCommand]
        private void VerifyDemoPassword()
        {
            if (string.IsNullOrEmpty(DemoPassword))
            {
                DemoVerificationResult = "❌ 请输入要验证的密码";
                DemoVerificationColor = new SolidColorBrush(Colors.Red);
                return;
            }

            if (string.IsNullOrEmpty(storedTestPasswordHash))
            {
                DemoVerificationResult = "❌ 没有存储的密码哈希，请先生成测试密码";
                DemoVerificationColor = new SolidColorBrush(Colors.Red);
                return;
            }

            var isValid = VerifyPassword(DemoPassword, storedTestPasswordHash);
            var inputHash = HashPassword(DemoPassword);

            if (isValid)
            {
                DemoVerificationResult = $"✅ 密码验证成功！\n" +
                                       $"🔍 输入密码: {DemoPassword}\n" +
                                       $"🔒 输入哈希: {inputHash[..16]}...\n" +
                                       $"🛡️ 存储哈希: {storedTestPasswordHash[..16]}...\n" +
                                       $"✅ 哈希匹配: 是";
                DemoVerificationColor = new SolidColorBrush(Colors.Green);
            }
            else
            {
                DemoVerificationResult = $"❌ 密码验证失败！\n" +
                                       $"🔍 输入密码: {DemoPassword}\n" +
                                       $"❌ 哈希匹配: 否";
                DemoVerificationColor = new SolidColorBrush(Colors.Red);
            }
        }

        /// <summary>
        /// 显示验证结果
        /// </summary>
        private void ShowValidationResult(bool isSuccess, string message)
        {
            ValidationMessage = message;
            ValidationMessageColor = isSuccess
                ? new SolidColorBrush(Colors.Green)
                : new SolidColorBrush(Colors.Red);
        }

        /// <summary>
        /// 验证密码哈希
        /// </summary>
        private bool VerifyPassword(string password, string hash)
        {
            var computedHash = HashPassword(password);
            return computedHash == hash;
        }

        /// <summary>
        /// 计算密码强度
        /// </summary>
        private double CalculatePasswordStrength(string password)
        {
            if (string.IsNullOrEmpty(password)) return 0;

            double score = 0;

            // 长度评分 (0-25分)
            score += Math.Min(password.Length * 2, 25);

            // 字符类型评分 (0-40分)
            if (Regex.IsMatch(password, @"[a-z]")) score += 10;
            if (Regex.IsMatch(password, @"[A-Z]")) score += 10;
            if (Regex.IsMatch(password, @"[0-9]")) score += 10;
            if (Regex.IsMatch(password, @"[^a-zA-Z0-9]")) score += 10;

            // 复杂度评分 (0-35分)
            var uniqueChars = password.Distinct().Count();
            score += Math.Min(uniqueChars * 2, 20);

            if (password.Length >= 12) score += 5;
            if (uniqueChars >= password.Length * 0.7) score += 10;

            return Math.Min(score, 100);
        }

        /// <summary>
        /// 获取密码强度等级描述
        /// </summary>
        private string GetStrengthLevel(double strength)
        {
            return strength switch
            {
                >= 80 => "强",
                >= 60 => "良好",
                >= 40 => "一般",
                _ => "弱"
            };
        }
    }

    /// <summary>
    /// 密码历史记录项
    /// </summary>
    public class PasswordHistoryItem
    {
        public string HashedPassword { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; }
        public string DisplayPassword { get; set; } = string.Empty;
        public string FormattedDate => CreatedDate.ToString("yyyy-MM-dd");
    }

    /// <summary>
    /// 密码要求检查
    /// </summary>
    public partial class PasswordRequirements : ObservableObject
    {
        [ObservableProperty]
        private bool hasMinLength;

        [ObservableProperty]
        private bool hasUppercase;

        [ObservableProperty]
        private bool hasLowercase;

        [ObservableProperty]
        private bool hasNumber;

        [ObservableProperty]
        private bool hasSymbol;
    }
}
