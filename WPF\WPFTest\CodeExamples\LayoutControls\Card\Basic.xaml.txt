<!-- WPF-UI Card 基础用法示例 -->
<StackPanel Margin="20">

    <!-- 简单卡片 -->
    <TextBlock Text="简单卡片" FontWeight="Bold" Margin="0,0,0,8"/>
    <ui:Card Padding="16" Margin="0,0,0,16">
        <StackPanel>
            <TextBlock Text="这是一个简单的卡片" 
                       FontWeight="Medium" 
                       Margin="0,0,0,8"/>
            <TextBlock Text="卡片提供了现代化的容器样式，具有阴影效果和圆角边框。" 
                       TextWrapping="Wrap"
                       Margin="0,0,0,12"/>
            <Button Content="卡片按钮" 
                    HorizontalAlignment="Left"/>
        </StackPanel>
    </ui:Card>

    <!-- 带图标的卡片 -->
    <TextBlock Text="带图标的卡片" FontWeight="Bold" Margin="0,0,0,8"/>
    <ui:Card Padding="16" Margin="0,0,0,16">
        <StackPanel>
            <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                <ui:SymbolIcon Symbol="Star24" 
                               FontSize="24" 
                               Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                               Margin="0,0,12,0"/>
                <TextBlock Text="特色功能" 
                           FontWeight="Medium" 
                           FontSize="16"
                           VerticalAlignment="Center"/>
            </StackPanel>
            <TextBlock Text="这个卡片包含了图标和文字的组合，提供更丰富的视觉效果。" 
                       TextWrapping="Wrap"
                       Margin="0,0,0,12"/>
            <StackPanel Orientation="Horizontal">
                <Button Content="主要操作" Margin="0,0,8,0"/>
                <Button Content="次要操作" Style="{StaticResource DefaultButtonStyle}"/>
            </StackPanel>
        </StackPanel>
    </ui:Card>

    <!-- 信息卡片 -->
    <TextBlock Text="信息卡片" FontWeight="Bold" Margin="0,0,0,8"/>
    <ui:Card Padding="16" Margin="0,0,0,16">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <ui:SymbolIcon Grid.Column="0"
                           Symbol="Info24" 
                           FontSize="32" 
                           Foreground="{DynamicResource SystemFillColorCautionBrush}"
                           Margin="0,0,16,0"
                           VerticalAlignment="Top"/>
            
            <StackPanel Grid.Column="1">
                <TextBlock Text="重要提示" 
                           FontWeight="Bold" 
                           FontSize="16"
                           Margin="0,0,0,8"/>
                <TextBlock Text="这是一个信息提示卡片，用于显示重要的系统消息或用户通知。" 
                           TextWrapping="Wrap"
                           Margin="0,0,0,8"/>
                <TextBlock Text="• 支持多行文本显示" Margin="0,0,0,2"/>
                <TextBlock Text="• 可以包含图标和按钮" Margin="0,0,0,2"/>
                <TextBlock Text="• 响应式布局设计" Margin="0,0,0,8"/>
                <Button Content="了解更多" HorizontalAlignment="Left"/>
            </StackPanel>
        </Grid>
    </ui:Card>

    <!-- 统计卡片 -->
    <TextBlock Text="统计卡片" FontWeight="Bold" Margin="0,0,0,8"/>
    <ui:Card Padding="20" Margin="0,0,0,16">
        <StackPanel>
            <TextBlock Text="数据统计" 
                       FontWeight="Bold" 
                       FontSize="18"
                       Margin="0,0,0,16"/>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <TextBlock Grid.Row="0" Grid.Column="0" Text="总用户:" FontWeight="Medium"/>
                <TextBlock Grid.Row="0" Grid.Column="1" Text="1,234" HorizontalAlignment="Right" FontWeight="Bold"/>
                
                <TextBlock Grid.Row="1" Grid.Column="0" Text="活跃用户:" FontWeight="Medium"/>
                <TextBlock Grid.Row="1" Grid.Column="1" Text="987" HorizontalAlignment="Right" FontWeight="Bold"/>
                
                <TextBlock Grid.Row="2" Grid.Column="0" Text="新增用户:" FontWeight="Medium"/>
                <TextBlock Grid.Row="2" Grid.Column="1" Text="45" HorizontalAlignment="Right" FontWeight="Bold"/>
            </Grid>
            <ProgressBar Value="75" Height="8" Margin="0,16,0,8"/>
            <TextBlock Text="完成度: 75%" FontSize="12" HorizontalAlignment="Center"/>
        </StackPanel>
    </ui:Card>

    <!-- 操作卡片 -->
    <TextBlock Text="操作卡片" FontWeight="Bold" Margin="0,0,0,8"/>
    <ui:Card Padding="16" Margin="0,0,0,16">
        <StackPanel>
            <TextBlock Text="快速操作" 
                       FontWeight="Medium" 
                       FontSize="16"
                       Margin="0,0,0,12"/>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <Button Grid.Row="0" Grid.Column="0" Content="新建" Margin="0,0,4,4"/>
                <Button Grid.Row="0" Grid.Column="1" Content="编辑" Margin="4,0,0,4"/>
                <Button Grid.Row="1" Grid.Column="0" Content="删除" Margin="0,4,4,0"/>
                <Button Grid.Row="1" Grid.Column="1" Content="设置" Margin="4,4,0,0"/>
            </Grid>
        </StackPanel>
    </ui:Card>

    <!-- 媒体卡片 -->
    <TextBlock Text="媒体卡片" FontWeight="Bold" Margin="0,0,0,8"/>
    <ui:Card Padding="0" Margin="0,0,0,16">
        <StackPanel>
            <!-- 模拟图片区域 -->
            <Border Background="{DynamicResource AccentFillColorDefaultBrush}"
                    Height="120"
                    CornerRadius="8,8,0,0">
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                    <ui:SymbolIcon Symbol="Image24" FontSize="32" Foreground="White"/>
                    <TextBlock Text="图片区域" Foreground="White" FontSize="12" Margin="0,4,0,0"/>
                </StackPanel>
            </Border>
            
            <!-- 内容区域 -->
            <StackPanel Margin="16">
                <TextBlock Text="媒体标题" 
                           FontWeight="Bold" 
                           FontSize="16"
                           Margin="0,0,0,8"/>
                <TextBlock Text="这是一个媒体卡片的描述文本，展示了如何在卡片中组合图片和文字内容。" 
                           TextWrapping="Wrap"
                           FontSize="14"
                           Margin="0,0,0,12"/>
                <Button Content="查看详情" HorizontalAlignment="Left"/>
            </StackPanel>
        </StackPanel>
    </ui:Card>

    <!-- 卡片列表 -->
    <TextBlock Text="卡片列表" FontWeight="Bold" Margin="0,0,0,8"/>
    <StackPanel>
        <ui:Card Padding="12" Margin="0,0,0,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <ui:SymbolIcon Grid.Column="0" Symbol="Document24" FontSize="20" Margin="0,0,12,0"/>
                <StackPanel Grid.Column="1">
                    <TextBlock Text="文档 1" FontWeight="Medium"/>
                    <TextBlock Text="最后修改: 2024-01-15" FontSize="12" Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                </StackPanel>
                <Button Grid.Column="2" Content="打开" HorizontalAlignment="Right"/>
            </Grid>
        </ui:Card>
        
        <ui:Card Padding="12" Margin="0,0,0,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <ui:SymbolIcon Grid.Column="0" Symbol="Folder24" FontSize="20" Margin="0,0,12,0"/>
                <StackPanel Grid.Column="1">
                    <TextBlock Text="项目文件夹" FontWeight="Medium"/>
                    <TextBlock Text="包含 25 个文件" FontSize="12" Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                </StackPanel>
                <Button Grid.Column="2" Content="浏览" HorizontalAlignment="Right"/>
            </Grid>
        </ui:Card>
        
        <ui:Card Padding="12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <ui:SymbolIcon Grid.Column="0" Symbol="Image24" FontSize="20" Margin="0,0,12,0"/>
                <StackPanel Grid.Column="1">
                    <TextBlock Text="图片集合" FontWeight="Medium"/>
                    <TextBlock Text="共 156 张图片" FontSize="12" Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                </StackPanel>
                <Button Grid.Column="2" Content="查看" HorizontalAlignment="Right"/>
            </Grid>
        </ui:Card>
    </StackPanel>

</StackPanel>
