﻿using System.Windows.Input;
using Wpf.Ui.Controls;

namespace AlphaPM.Views;

/// <summary>
/// WPF-UI 现代化主窗口
/// </summary>
public partial class MainView : FluentWindow
{
    public MainView()
    {
        InitializeComponent();
    }

    /// <summary>
    /// 🖱️ 四个角拖拽事件 - 简单安全的拖拽实现
    /// </summary>
    private void CornerDrag_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
    {
        try
        {
            this.DragMove();
        }
        catch (InvalidOperationException)
        {
            // 忽略拖拽异常
        }
    }
}