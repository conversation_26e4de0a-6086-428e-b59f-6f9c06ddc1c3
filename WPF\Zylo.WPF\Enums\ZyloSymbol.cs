namespace Zylo.WPF.Enums;

/// <summary>
/// Zylo自定义图标枚举
/// 对应iconfont.ttf字体文件中的图标
/// </summary>
public enum ZyloSymbol
{
    /// <summary>
    /// 无图标
    /// </summary>
    None = 0,
    
  
    /// <summary>
    /// 笔记, 记录图标
    /// Unicode: &#xe6f3;
    /// </summary>
    Notes = 0xE6F3,
    
    /// <summary>
    /// DWG文件图标
    /// Unicode: &#xe661;
    /// </summary>
    Dwg = 0xE661,
    
    /// <summary>
    /// ICO文件图标
    /// Unicode: &#xe617;
    /// </summary>
    ICO = 0xE617,

    #region 项目管理图标

    /// <summary>
    /// 年份图标
    /// Unicode: &#xe621;
    /// </summary>
    Year = 0xE621,

    /// <summary>
    /// 楼栋文件图标
    /// Unicode: &#xe63f;
    /// </summary>
    Buildings = 0xE63F,

    /// <summary>
    /// 项目文件图标
    /// Unicode: &#xe628;
    /// </summary>
    Project = 0xE628,
    #endregion
 
}
