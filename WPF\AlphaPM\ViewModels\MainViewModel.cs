﻿using System.Collections.ObjectModel;
using System.Windows;
using AlphaPM.Views.Settings;
using Wpf.Ui.Controls;
using Zylo.WPF.Enums;
using Zylo.WPF.Helpers;
using Zylo.WPF.Models.Navigation;
using Zylo.WPF.YPrism;

namespace AlphaPM.ViewModels;

/// <summary>
/// MainViewModel - 主视图模型
/// </summary>
/// <remarks>
/// <para><strong>功能职责：</strong></para>
/// <list type="bullet">
///   <item><description>🎯 <strong>NavigationControl 完整功能测试</strong>：提供所有导航控件功能的测试和演示</description></item>
///   <item><description>🔍 <strong>搜索功能管理</strong>：实现导航项的实时搜索和过滤功能</description></item>
///   <item><description>📋 <strong>导航数据管理</strong>：管理顶部和底部导航项的数据源</description></item>
///   <item><description>⚡ <strong>Prism 导航集成</strong>：与 Prism 框架的区域导航系统集成</description></item>
///   <item><description>🎮 <strong>交互功能测试</strong>：提供完整的用户交互功能测试</description></item>
/// </list>
///
/// <para><strong>设计模式：</strong></para>
/// <list type="bullet">
///   <item><description>MVVM 模式：使用 CommunityToolkit.Mvvm 实现</description></item>
///   <item><description>命令模式：所有用户操作通过 RelayCommand 实现</description></item>
///   <item><description>观察者模式：使用 ObservableProperty 实现属性通知</description></item>
///   <item><description>依赖注入：通过构造函数注入 IRegionManager</description></item>
/// </list>
///
/// <para><strong>测试功能：</strong></para>
/// <list type="bullet">
///   <item><description>基础功能：导航、搜索、选择、展开/折叠</description></item>
///   <item><description>高级功能：动态添加/删除、批量操作、设置控制</description></item>
///   <item><description>交互功能：双击、右键菜单、拖拽、主题适配</description></item>
/// </list>
/// </remarks>
public partial class MainViewModel : ViewModelBase, IConfigureService
{
    public readonly YLoggerInstance _logger = YLogger.ForSilent<MainViewModel>();

    #region 私有字段和依赖注入

    // ================================================================
    // 私有字段：存储注入的服务和内部状态
    // ================================================================

    /// <summary>
    /// Prism 区域管理器 - 用于页面导航
    /// </summary>
    private readonly IRegionManager _iRegionManager;

    /// <summary>
    /// 导航日志对象 - 用于记录和管理区域导航的历史记录
    /// </summary>
    private IRegionNavigationJournal journal;

    #endregion

    #region 导航数据属性

    // ================================================================
    // 导航数据属性：NavigationControl 的数据源
    // 这些属性直接绑定到 NavigationControl 的相应属性
    // ================================================================

    /// <summary>
    /// 顶部导航项集合
    /// </summary>
    /// <remarks>
    /// <para>绑定到 NavigationControl.TopNavigationItems</para>
    /// <para><strong>数据内容：</strong></para>
    /// <list type="bullet">
    ///   <item><description>首页、控件示例、颜色展示、主题测试、设置等</description></item>
    ///   <item><description>支持层级结构，包含子项目</description></item>
    ///   <item><description>使用多种图标类型：WpfUiSymbol、FontIcon</description></item>
    /// </list>
    /// </remarks>
    [ObservableProperty]
    public partial ObservableCollection<ZyloNavigationItemModel> TnavigationItems { get; set; } = new();

    /// <summary>
    /// 底部导航项集合
    /// </summary>
    /// <remarks>
    /// <para>绑定到 NavigationControl.BottomNavigationItems</para>
    /// <para><strong>数据内容：</strong></para>
    /// <list type="bullet">
    ///   <item><description>系统管理、业务模块、工具箱等</description></item>
    ///   <item><description>主要用于系统级功能和管理功能</description></item>
    ///   <item><description>统一使用 FontIcon 图标类型</description></item>
    /// </list>
    /// </remarks>
    [ObservableProperty]
    public partial ObservableCollection<ZyloNavigationItemModel> DnavigationItems { get; set; } = new();

    /// <summary>
    /// 当前选中的导航项
    /// </summary>
    /// <remarks>
    /// <para>绑定到 NavigationControl.SelectedListItem（双向绑定）</para>
    /// <para><strong>功能作用：</strong></para>
    /// <list type="bullet">
    ///   <item><description>跟踪用户当前选中的导航项</description></item>
    ///   <item><description>触发页面导航和内容更新</description></item>
    ///   <item><description>在测试面板中显示当前状态</description></item>
    /// </list>
    /// </remarks>
    [ObservableProperty]
    public partial ZyloNavigationItemModel ZyloNavigationItemT { get; set; } = new();

    #endregion

    #region 核心导航命令

    // ================================================================
    // 核心导航命令：处理 NavigationControl 的主要导航功能
    // 这些命令直接绑定到 NavigationControl 的相应命令属性
    // ================================================================

    /// <summary>
    /// 导航项选中命令 - NavigationControl 的核心导航功能
    /// </summary>
    /// <param name="item">选中的导航项</param>
    /// <remarks>
    /// <para><strong>绑定属性：</strong>NavigationControl.NavigationItemSelectedCommand</para>
    /// <para><strong>执行流程：</strong></para>
    /// <list type="number">
    ///   <item><description>验证导航项参数有效性</description></item>
    ///   <item><description>更新当前选中项（ZyloNavigationItemT）</description></item>
    ///   <item><description>检查是否有导航目标（NavigationTarget）</description></item>
    ///   <item><description>使用 Prism 区域管理器执行页面导航</description></item>
    ///   <item><description>记录导航结果和错误信息</description></item>
    /// </list>
    /// <para><strong>支持功能：</strong></para>
    /// <list type="bullet">
    ///   <item><description>🎯 页面导航：跳转到指定的视图页面</description></item>
    ///   <item><description>📋 参数传递：传递导航项信息和标题</description></item>
    ///   <item><description>📝 导航历史：记录导航日志，支持前进后退</description></item>
    ///   <item><description>🔍 错误处理：捕获和记录导航失败信息</description></item>
    /// </list>
    /// </remarks>
    [RelayCommand]
    private void NavigateToItem(ZyloNavigationItemModel item)
    {
        _logger.Info($"🎯 导航命令被调用，目标: {item?.Name ?? "null"}");

        if (item == null)
        {
            _logger.Warning("⚠️ 导航项参数为空，忽略导航请求");
            return;
        }


        // 🎯 更新当前选中的导航项
        ZyloNavigationItemT = item;
        _logger.Info($"✅ 导航项已更新: {item.Name}");

        // 🔄 检查是否有子项目
        if (item.Children?.Count > 0)
        {
            _logger.Info($"🔄 导航项有子项目，导航到子菜单页面: {item.Name}");

            // 📋 创建导航参数，传递父项目信息
            NavigationParameters navigationParams = new NavigationParameters();
            navigationParams.Add("ParentItem", item);
            navigationParams.Add("Title", $"{item.Name} - 子菜单");

            // 🚀 导航到子菜单页面
            _iRegionManager.Regions[PrismManager.MainViewRegionName].RequestNavigate(
                "SubMenuView",
                navigationResult =>
                {
                    if (navigationResult.Result == true)
                    {
                        _logger.Info($"✅ 导航到子菜单成功: {item.Name}");
                        journal = navigationResult.Context.NavigationService.Journal;
                    }
                    else
                    {
                        _logger.Error($"❌ 导航到子菜单失败: {item.Name} - {navigationResult.Error?.Message}");
                    }
                },
                navigationParams);
        }
        // 🔄 执行页面导航（如果有导航目标且没有子项目）
        else if (!string.IsNullOrEmpty(item.NavigationTarget))
        {
            _logger.Info($"🔄 准备导航到视图: {item.NavigationTarget}");

            // 📋 创建导航参数
            NavigationParameters navigationParams = new NavigationParameters();
            navigationParams.Add("NavigationItem", item);
            navigationParams.Add("Title", item.Name);

            // 🚀 请求导航到目标页面
            _iRegionManager.Regions[PrismManager.MainViewRegionName].RequestNavigate(
                item.NavigationTarget,
                navigationResult =>
                {
                    if (navigationResult.Result == true)
                    {
                        _logger.Info($"✅ 导航成功: {item.NavigationTarget}");
                        // 📝 记录导航历史
                        journal = navigationResult.Context.NavigationService.Journal;
                    }
                    else
                    {
                        _logger.Error($"❌ 导航失败: {item.NavigationTarget} - {navigationResult.Error?.Message}");
                    }
                },
                navigationParams);
        }
        else
        {
            _logger.Info($"📁 这是一个分组项，无需导航: {item.Name}");
        }
    }

    #endregion

    #region 搜索功能属性

    // ================================================================
    // 搜索功能属性：控制 NavigationControl 的搜索行为
    // 这些属性绑定到 NavigationControl 的搜索相关属性
    // ================================================================

    /// <summary>
    /// 搜索文本内容
    /// </summary>
    /// <remarks>
    /// <para><strong>绑定属性：</strong>NavigationControl.SearchText（双向绑定）</para>
    /// <para><strong>功能特性：</strong></para>
    /// <list type="bullet">
    ///   <item><description>🔍 实时搜索：输入时立即过滤导航项</description></item>
    ///   <item><description>🔤 大小写不敏感：自动忽略大小写差异</description></item>
    ///   <item><description>📝 部分匹配：包含搜索文本即可匹配</description></item>
    ///   <item><description>🧹 自动清空：切换到 ListView 模式时自动清空</description></item>
    /// </list>
    /// </remarks>
    [ObservableProperty]
    public partial string NavigationSearchText { get; set; } = "";

    /// <summary>
    /// 是否显示搜索框
    /// </summary>
    /// <remarks>
    /// <para><strong>绑定属性：</strong>NavigationControl.ShowSearchBox</para>
    /// <para><strong>控制行为：</strong></para>
    /// <list type="bullet">
    ///   <item><description>true：在 TreeViewPageView 模式下显示搜索框</description></item>
    ///   <item><description>false：隐藏搜索框，节省界面空间</description></item>
    ///   <item><description>可通过测试面板动态切换</description></item>
    /// </list>
    /// </remarks>
    [ObservableProperty]
    public partial bool ShowNavigationSearch { get; set; } = true;

    /// <summary>
    /// 是否高亮搜索结果
    /// </summary>
    /// <remarks>
    /// <para><strong>绑定属性：</strong>NavigationControl.HighlightSearchResults</para>
    /// <para><strong>视觉效果：</strong></para>
    /// <list type="bullet">
    ///   <item><description>true：匹配的文本会被高亮显示</description></item>
    ///   <item><description>false：只过滤结果，不高亮显示</description></item>
    ///   <item><description>提升用户搜索体验</description></item>
    /// </list>
    /// </remarks>
    [ObservableProperty]
    public partial bool HighlightSearchMatches { get; set; } = true;

    #endregion

    #region 布局控制属性

    // ================================================================
    // 布局控制属性：控制 NavigationControl 的布局和显示行为
    // 这些属性影响控件的视觉表现和响应式行为
    // ================================================================

    /// <summary>
    /// TreeViewPageView 列宽设置
    /// </summary>
    /// <remarks>
    /// <para><strong>绑定属性：</strong>NavigationControl.TreeViewColumnWidth（双向绑定）</para>
    /// <para><strong>布局控制：</strong></para>
    /// <list type="bullet">
    ///   <item><description>GridLength(1, GridUnitType.Star)：占满可用空间</description></item>
    ///   <item><description>GridLength(固定值)：使用固定像素宽度</description></item>
    ///   <item><description>影响 TreeViewPageView 模式下的列宽分配</description></item>
    /// </list>
    /// </remarks>
    [ObservableProperty]
    public partial GridLength NavigationTreeViewColumnWidth { get; set; } = new GridLength(1, GridUnitType.Star);

    /// <summary>
    /// 是否启用响应式布局
    /// </summary>
    /// <remarks>
    /// <para><strong>绑定属性：</strong>NavigationControl.EnableResponsiveLayout</para>
    /// <para><strong>响应式特性：</strong></para>
    /// <list type="bullet">
    ///   <item><description>true：根据窗口大小自动调整布局</description></item>
    ///   <item><description>false：使用固定布局，不响应窗口变化</description></item>
    ///   <item><description>影响控件在不同屏幕尺寸下的表现</description></item>
    /// </list>
    /// </remarks>
    [ObservableProperty]
    public partial bool EnableResponsiveLayout { get; set; } = true;

    #endregion

    #region 交互增强命令

    // ================================================================
    // 交互增强命令：处理 NavigationControl 的高级交互功能
    // 这些命令提供更丰富的用户交互体验
    // ================================================================

    /// <summary>
    /// 搜索文本变化命令 - 处理搜索框文本变化事件
    /// </summary>
    /// <param name="searchText">新的搜索文本</param>
    /// <remarks>
    /// <para><strong>绑定属性：</strong>NavigationControl.SearchTextChangedCommand</para>
    /// <para><strong>触发时机：</strong>用户在搜索框中输入或修改文本时</para>
    /// <para><strong>处理逻辑：</strong></para>
    /// <list type="bullet">
    ///   <item><description>🔍 同步搜索文本到 NavigationSearchText 属性</description></item>
    ///   <item><description>📝 记录搜索操作日志</description></item>
    ///   <item><description>🎯 可扩展自定义搜索逻辑</description></item>
    /// </list>
    /// </remarks>
    [RelayCommand]
    private void SearchTextChanged(string searchText)
    {
        _logger.Info($"🔍 搜索文本变化: '{searchText ?? "空"}'");
        NavigationSearchText = searchText ?? "";
        // 🎯 这里可以添加自定义搜索逻辑，如搜索历史记录、搜索建议等
    }

    /// <summary>
    /// 导航项双击命令 - 处理导航项双击事件
    /// </summary>
    /// <param name="item">被双击的导航项</param>
    /// <remarks>
    /// <para><strong>绑定属性：</strong>NavigationControl.ItemDoubleClickCommand</para>
    /// <para><strong>触发时机：</strong>用户双击导航项时</para>
    /// <para><strong>处理逻辑：</strong></para>
    /// <list type="bullet">
    ///   <item><description>🖱️ 记录双击操作</description></item>
    ///   <item><description>🚀 可实现快速导航或特殊操作</description></item>
    ///   <item><description>🎯 与单击导航形成差异化交互</description></item>
    /// </list>
    /// </remarks>
    [RelayCommand]
    private void NavigationItemDoubleClick(ZyloNavigationItemModel item)
    {
        _logger.Info($"🖱️ 双击导航项: {item?.Name ?? "null"}");

        if (item != null)
        {
            // 🚀 双击时可以实现特殊操作，如：
            // - 快速导航（跳过确认）
            // - 打开编辑模式
            // - 显示详细信息
            // NavigateToItem(item); // 可选：直接导航
        }
    }

    /// <summary>
    /// 导航项右键菜单命令 - 处理导航项右键菜单事件
    /// </summary>
    /// <param name="item">被右键点击的导航项</param>
    /// <remarks>
    /// <para><strong>绑定属性：</strong>NavigationControl.ItemContextMenuCommand</para>
    /// <para><strong>触发时机：</strong>用户右键点击导航项时</para>
    /// <para><strong>处理逻辑：</strong></para>
    /// <list type="bullet">
    ///   <item><description>📋 记录右键操作</description></item>
    ///   <item><description>🎯 可显示上下文菜单</description></item>
    ///   <item><description>⚙️ 提供复制、重命名、删除等操作</description></item>
    /// </list>
    /// </remarks>
    [RelayCommand]
    private void NavigationItemContextMenu(ZyloNavigationItemModel item)
    {
        _logger.Info($"📋 右键菜单: {item?.Name ?? "null"}");

        if (item != null)
        {
            // 📋 这里可以显示上下文菜单，提供如下功能：
            // - 复制导航项信息
            // - 重命名导航项
            // - 删除导航项
            // - 添加到收藏夹
            // - 查看属性
        }
    }

    /// <summary>
    /// 导航切换命令 - 处理导航模式切换事件
    /// </summary>
    /// <remarks>
    /// <para><strong>绑定属性：</strong>NavigationControl.NavigationToggleCommand</para>
    /// <para><strong>触发时机：</strong>用户点击切换按钮时</para>
    /// <para><strong>处理逻辑：</strong></para>
    /// <list type="bullet">
    ///   <item><description>🔄 记录切换操作</description></item>
    ///   <item><description>📱 可实现自定义切换逻辑</description></item>
    ///   <item><description>🎯 可触发其他相关操作</description></item>
    /// </list>
    /// </remarks>
    [RelayCommand]
    private void NavigationToggle()
    {
        _logger.Info("🔄 导航模式切换");
        // 🔄 这里可以添加自定义切换逻辑，如：
        // - 保存当前状态
        // - 触发动画效果
        // - 更新其他相关控件
    }

    #endregion

    #region 构造函数和初始化

    // ================================================================
    // 构造函数和初始化：ViewModel 的创建和数据初始化
    // ================================================================

    /// <summary>
    /// MainViewModel 构造函数
    /// </summary>
    /// <param name="iRegionManager">Prism 区域管理器（依赖注入）</param>
    /// <remarks>
    /// <para><strong>初始化流程：</strong></para>
    /// <list type="number">
    ///   <item><description>保存注入的 IRegionManager 实例</description></item>
    ///   <item><description>调用 InitializeNavigationItems() 初始化导航数据</description></item>
    ///   <item><description>执行命令检查和调试输出</description></item>
    /// </list>
    /// <para><strong>依赖注入：</strong></para>
    /// <list type="bullet">
    ///   <item><description>IRegionManager：用于 Prism 区域导航</description></item>
    ///   <item><description>通过 DI 容器自动注入</description></item>
    /// </list>
    /// </remarks>
    public MainViewModel(IRegionManager iRegionManager)
    {
        // 💾 保存依赖注入的服务
        _iRegionManager = iRegionManager;

        // 📋 初始化导航数据（但不执行默认导航）
        InitializeNavigationItemsOnly();

        // 🔍 调试：检查命令是否正确生成（开发阶段使用）
        _logger.Debug("🔍 MainViewModel 命令检查:");

        _logger.Debug($"🚀 MainViewModel 构造完成，导航项数量 - 顶部: {TnavigationItems.Count}, 底部: {DnavigationItems.Count}");
    }


    /// <summary>
    /// 初始化导航项数据 - 创建完整的测试数据集
    /// </summary>
    /// <remarks>
    /// <para><strong>数据结构设计：</strong></para>
    /// <list type="bullet">
    ///   <item><description>🎯 顶部导航：主要功能模块（首页、控件、主题等）</description></item>
    ///   <item><description>🔧 底部导航：系统管理功能（用户、权限、工具等）</description></item>
    ///   <item><description>📋 层级结构：支持父子关系，可展开/折叠</description></item>
    ///   <item><description>🎨 多图标类型：WpfUiSymbol、FontIcon 混合使用</description></item>
    /// </list>
    ///
    /// <para><strong>测试覆盖：</strong></para>
    /// <list type="bullet">
    ///   <item><description>基础导航：单级和多级导航项</description></item>
    ///   <item><description>图标显示：不同类型图标的渲染测试</description></item>
    ///   <item><description>展开状态：默认展开和折叠状态</description></item>
    ///   <item><description>搜索功能：包含可搜索的关键词</description></item>
    /// </list>
    /// </remarks>
    private void InitializeNavigationItemsOnly()
    {
        // 🔍 字体调试信息
        _logger.Debug("🐛 开始初始化导航项数据（仅数据，不导航）");
        _logger.InfoDetailed($"📋 字体路径: {Zylo.WPF.Comman.FontKeys.FontManager.IconFontPath}");

        // 测试字体可用性
        foreach (var fontPath in Zylo.WPF.Comman.FontKeys.FontManager.TestFontPaths)
        {
            var isAvailable = Zylo.WPF.Comman.FontKeys.FontManager.IsFontAvailable(fontPath);
            _logger.InfoDetailed($"📋 字体测试: {fontPath} - {(isAvailable ? "✅可用" : "❌不可用")}");
        }

        // 🎯 完整的 WPF-UI + Zylo 控件示例库导航数据
        var topNavigationData = new Zylo.WPF.Helpers.NavigationData[]
        {
            // === 基础页面 ===
            new("1", "首页", "HomePageView", SymbolRegular.Home24),
            
        };
        // 🔧 底部导航数据 - 系统管理功能
        var bottomNavigationData = new Zylo.WPF.Helpers.NavigationData[]
        {
            new("1", "设置", nameof(SettingsView), SymbolRegular.Settings24),
         
           
        };

        // 📋 使用 ParentNumber 方法组装层级结构
        TnavigationItems = NavigationItemHelper.AssembleByParentNumber(topNavigationData);
        DnavigationItems = NavigationItemHelper.AssembleByParentNumber(bottomNavigationData);

        _logger.Info($"✅ 导航项数据初始化完成 - 顶部: {TnavigationItems.Count} 项, 底部: {DnavigationItems.Count} 项");
    }

    #endregion

    #region 服务配置接口实现

    // ================================================================
    // 服务配置接口实现：IConfigureService 接口的实现
    // ================================================================

    /// <summary>
    /// 配置服务 - IConfigureService 接口实现
    /// </summary>
    /// <remarks>
    /// <para><strong>接口职责：</strong></para>
    /// <list type="bullet">
    ///   <item><description>提供服务配置的统一入口</description></item>
    ///   <item><description>可在此处进行额外的初始化配置</description></item>
    ///   <item><description>支持依赖注入容器的配置回调</description></item>
    ///   <item><description>执行默认导航到首页</description></item>
    /// </list>
    /// </remarks>
    public void Configure()
    {
        _logger.Info("🔧 开始执行 MainViewModel 服务配置...");

        // 🚀 执行默认导航到首页
        // 在这个时候，所有的区域都已经注册完成，可以安全地进行导航
        try
        {
            if (_iRegionManager.Regions.ContainsRegionWithName(PrismManager.MainViewRegionName))
            {
                NavigateToDefaultView(PrismManager.MainViewRegionName, nameof(SettingsView));
                _logger.Info("✅ 默认导航到DWG文件夹管理系统执行成功");
            }
            else
            {
                _logger.Warning($"⚠️ 区域 {PrismManager.MainViewRegionName} 未找到，跳过默认导航");
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 默认导航执行失败: {ex.Message}");
        }

        // 🔧 这里可以添加额外的配置逻辑
        // 例如：订阅事件、注册服务、初始化资源等
        _logger.Info("✅ MainViewModel 服务配置完成");
    }

    #endregion

    #region Prism 导航管理

    // ================================================================
    // Prism 导航管理：处理区域导航和历史记录
    // ================================================================

    /// <summary>
    /// 导航到默认视图 - 内部导航辅助方法
    /// </summary>
    /// <param name="regionName">区域名称</param>
    /// <param name="viewName">视图名称</param>
    /// <remarks>
    /// <para><strong>功能说明：</strong></para>
    /// <list type="bullet">
    ///   <item><description>🎯 执行简单的区域导航</description></item>
    ///   <item><description>📝 自动记录导航历史</description></item>
    ///   <item><description>🔧 内部方法，供其他导航方法调用</description></item>
    /// </list>
    /// </remarks>
    private void NavigateToDefaultView(string regionName, string viewName)
    {
        _iRegionManager.Regions[regionName].RequestNavigate(viewName,
            navigationCallback =>
            {
                // 📝 记录导航日志，支持前进后退功能
                journal = navigationCallback.Context.NavigationService.Journal;
                _logger.Debug($"🎯 导航到默认视图: {viewName}");
            });
    }

    /// <summary>
    /// 后退命令 - 返回上一个视图
    /// </summary>
    /// <remarks>
    /// <para><strong>功能特性：</strong></para>
    /// <list type="bullet">
    ///   <item><description>⬅️ 使用 Prism 导航历史后退</description></item>
    ///   <item><description>🔍 自动检查是否可以后退</description></item>
    ///   <item><description>📝 记录操作日志</description></item>
    /// </list>
    /// </remarks>
    [RelayCommand]
    private void GoBack()
    {
        if (journal != null && journal.CanGoBack)
        {
            journal.GoBack();
            _logger.Info("⬅️ 导航后退到上一个视图");
        }
        else
        {
            _logger.Warning("⚠️ 无法后退，没有历史记录");
        }
    }

    /// <summary>
    /// 前进命令 - 前进到下一个视图
    /// </summary>
    /// <remarks>
    /// <para><strong>功能特性：</strong></para>
    /// <list type="bullet">
    ///   <item><description>➡️ 使用 Prism 导航历史前进</description></item>
    ///   <item><description>🔍 自动检查是否可以前进</description></item>
    ///   <item><description>📝 记录操作日志</description></item>
    /// </list>
    /// </remarks>
    [RelayCommand]
    private void GoForward()
    {
        if (journal != null && journal.CanGoForward)
        {
            journal.GoForward();
            _logger.Info("➡️ 导航前进到下一个视图");
        }
        else
        {
            _logger.Warning("⚠️ 无法前进，没有前进历史");
        }
    }

    /// <summary>
    /// 展开所有导航项命令
    /// </summary>
    [RelayCommand]
    private void ExpandAllNavigationItems()
    {
        try
        {
            ExpandAllItems(TnavigationItems);
            _logger.Info("📂 展开所有导航项");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 展开所有导航项失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 折叠所有导航项命令
    /// </summary>
    [RelayCommand]
    private void CollapseAllNavigationItems()
    {
        try
        {
            CollapseAllItems(TnavigationItems);
            _logger.Info("📁 折叠所有导航项");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 折叠所有导航项失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 添加测试导航项命令
    /// </summary>
    [RelayCommand]
    private void AddTestNavigationItem()
    {
        try
        {
            var newItem = new ZyloNavigationItemModel
            {
                Number = $"test_{TnavigationItems.Count + 1}",
                Name = $"测试项目 {TnavigationItems.Count + 1}",
                NavigationTarget = "TestPage",
                WpfUiSymbol = SymbolRegular.Beaker24,
                IsExpanded = false
            };

            TnavigationItems.Add(newItem);
            _logger.Info($"➕ 添加测试导航项: {newItem.Name}");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 添加测试导航项失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 移除最后一个导航项命令
    /// </summary>
    [RelayCommand]
    private void RemoveLastNavigationItem()
    {
        try
        {
            if (TnavigationItems.Count > 0)
            {
                var lastItem = TnavigationItems.Last();
                TnavigationItems.Remove(lastItem);
                _logger.Info($"➖ 移除导航项: {lastItem.Name}");
            }
            else
            {
                _logger.Warning("⚠️ 没有可移除的导航项");
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 移除导航项失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 清空搜索命令
    /// </summary>
    [RelayCommand]
    private void ClearSearch()
    {
        try
        {
            // 这里可以添加清空搜索的逻辑
            // 例如：SearchText = string.Empty;
            _logger.Info("🧹 清空搜索");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 清空搜索失败: {ex.Message}");
        }
    }

    #endregion


    #region 私有辅助方法

    // ================================================================
    // 私有辅助方法：内部使用的工具方法
    // ================================================================

    /// <summary>
    /// 递归展开所有项目 - 内部辅助方法
    /// </summary>
    /// <param name="items">要展开的导航项集合</param>
    /// <remarks>
    /// <para><strong>递归逻辑：</strong></para>
    /// <list type="bullet">
    ///   <item><description>🔄 遍历集合中的每个项目</description></item>
    ///   <item><description>📂 设置 IsExpanded = true</description></item>
    ///   <item><description>🌳 递归处理子项集合</description></item>
    /// </list>
    /// </remarks>
    private void ExpandAllItems(ObservableCollection<ZyloNavigationItemModel> items)
    {
        foreach (var item in items)
        {
            item.IsExpanded = true;
            if (item.Children?.Count > 0)
            {
                ExpandAllItems(item.Children);
            }
        }
    }

    /// <summary>
    /// 递归折叠所有项目 - 内部辅助方法
    /// </summary>
    /// <param name="items">要折叠的导航项集合</param>
    /// <remarks>
    /// <para><strong>递归逻辑：</strong></para>
    /// <list type="bullet">
    ///   <item><description>🔄 遍历集合中的每个项目</description></item>
    ///   <item><description>📁 设置 IsExpanded = false</description></item>
    ///   <item><description>🌳 递归处理子项集合</description></item>
    /// </list>
    /// </remarks>
    private void CollapseAllItems(ObservableCollection<ZyloNavigationItemModel> items)
    {
        foreach (var item in items)
        {
            item.IsExpanded = false;
            if (item.Children?.Count > 0)
            {
                CollapseAllItems(item.Children);
            }
        }
    }

    #endregion


    // ================================================================
    // 🎉 MainViewModel 类总结
    // ================================================================
    //
    // 📋 主要职责：
    // ✅ NavigationControl 完整功能测试和演示
    // ✅ Prism 区域导航集成和管理
    // ✅ 搜索功能和布局控制
    // ✅ 动态导航项管理和批量操作
    //
    // 🏗️ 架构设计：
    // 📐 MVVM 模式：使用 CommunityToolkit.Mvvm
    // 🎯 命令模式：所有操作通过 RelayCommand
    // 📋 观察者模式：ObservableProperty 属性通知
    // 💉 依赖注入：IRegionManager 服务注入
    //
    // 🎮 测试功能：
    // 🔍 搜索测试：实时过滤、高亮显示
    // 📋 导航测试：选择、双击、右键菜单
    // ⚙️ 设置测试：搜索框、响应式布局
    // 🔄 动态测试：添加、删除、展开、折叠
    //
    // 💡 设计理念：
    // 🚀 完整测试：覆盖 NavigationControl 所有功能
    // 🎨 用户友好：提供直观的测试界面
    // 🔧 易于维护：清晰的代码结构和详细注释
    // 📱 现代化：支持响应式布局和主题适配
    //
    // ================================================================
}