<UserControl x:Class="WPFTest.Views.LayoutControls.GroupBoxView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:codeExample="clr-namespace:Zylo.WPF.Controls.CodeExample;assembly=Zylo.WPF"
             xmlns:mvvm="http://prismlibrary.com/"
             xmlns:layoutControls="clr-namespace:WPFTest.ViewModels.LayoutControls"
             mc:Ignorable="d"
             d:DesignHeight="2500" d:DesignWidth="1200"
             mvvm:ViewModelLocator.AutoWireViewModel="True"
             d:DataContext="{d:DesignInstance layoutControls:GroupBoxViewModel}">

    <UserControl.Resources>
        <!-- 🎨 所有样式来自 Zylo.WPF 项目 -->
        <!-- GroupBox 样式已在 Zylo.WPF/Resources/GroupBox/ 目录下定义 -->
        <!-- 可用样式：GroupBoxBaseStyle, ModernGroupBoxStyle, CardGroupBoxStyle -->
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" 
                  HorizontalScrollBarVisibility="Disabled"
                  Padding="24">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>    <!-- 标题区域 -->
                <RowDefinition Height="*"/>       <!-- 主要内容 -->
                <RowDefinition Height="Auto"/>    <!-- 底部操作栏 -->
            </Grid.RowDefinitions>

            <!-- 标题区域 -->
            <StackPanel Grid.Row="0" Margin="0,0,0,20">
                <TextBlock Text="🎨 GroupBox 控件展示"
                           FontSize="32"
                           FontWeight="Bold"
                           Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                           Margin="0,0,0,8"/>
                <TextBlock Text="展示 WPF-UI 增强的 GroupBox 控件的各种样式和分组功能" 
                           FontSize="16" 
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                           TextWrapping="Wrap"/>
                
                <!-- 状态显示 -->
                <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                        BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Padding="16,12"
                        Margin="0,16,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <ui:SymbolIcon Symbol="Info24" 
                                       FontSize="20" 
                                       Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                       VerticalAlignment="Center"
                                       Margin="0,0,12,0"/>
                        <TextBlock Grid.Column="1" 
                                   Text="{Binding StatusMessage}" 
                                   FontSize="14"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                   VerticalAlignment="Center"
                                   TextWrapping="Wrap"/>
                    </Grid>
                </Border>
            </StackPanel>

            <!-- 主要内容区域 -->
            <Border Grid.Row="1" Margin="0,0,0,20">
                <StackPanel>

                    <!-- 基础功能展示 -->
                    <ui:CardExpander Header="🎯 基础功能" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 GroupBox 的基础功能和用法"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>
                            
                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="基础 GroupBox 示例：" FontWeight="Medium" Margin="0,0,0,16"/>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <!-- 左侧示例 -->
                                        <StackPanel Grid.Column="0" Margin="0,0,12,0">
                                            <!-- 标准 GroupBox -->
                                            <GroupBox Header="用户信息" 
                                                      Background="{DynamicResource ControlFillColorDefaultBrush}"
                                                      BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                                      BorderThickness="1"
                                                      Margin="0,0,0,16">
                                                <StackPanel Margin="12">
                                                    <Grid>
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="*"/>
                                                        </Grid.ColumnDefinitions>
                                                        <Grid.RowDefinitions>
                                                            <RowDefinition Height="Auto"/>
                                                            <RowDefinition Height="Auto"/>
                                                            <RowDefinition Height="Auto"/>
                                                        </Grid.RowDefinitions>
                                                        
                                                        <TextBlock Grid.Row="0" Grid.Column="0" Text="姓名:" Margin="0,0,8,8" VerticalAlignment="Center"/>
                                                        <TextBox Grid.Row="0" Grid.Column="1" Text="张三" Margin="0,0,0,8"/>
                                                        
                                                        <TextBlock Grid.Row="1" Grid.Column="0" Text="年龄:" Margin="0,0,8,8" VerticalAlignment="Center"/>
                                                        <TextBox Grid.Row="1" Grid.Column="1" Text="25" Margin="0,0,0,8"/>
                                                        
                                                        <TextBlock Grid.Row="2" Grid.Column="0" Text="邮箱:" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                                        <TextBox Grid.Row="2" Grid.Column="1" Text="<EMAIL>"/>
                                                    </Grid>
                                                </StackPanel>
                                            </GroupBox>

                                            <!-- 设置选项 GroupBox -->
                                            <GroupBox Header="设置选项" 
                                                      Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                                      BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                                      BorderThickness="1">
                                                <StackPanel Margin="12">
                                                    <CheckBox Content="启用通知" IsChecked="True" Margin="0,0,0,8"/>
                                                    <CheckBox Content="自动保存" IsChecked="False" Margin="0,0,0,8"/>
                                                    <CheckBox Content="深色主题" IsChecked="True" Margin="0,0,0,8"/>
                                                    <Button Content="应用设置" 
                                                            Command="{Binding HandleInteractionCommand}"
                                                            CommandParameter="应用设置"
                                                            HorizontalAlignment="Left"/>
                                                </StackPanel>
                                            </GroupBox>
                                        </StackPanel>
                                        
                                        <!-- 右侧示例 -->
                                        <StackPanel Grid.Column="1" Margin="12,0,0,0">
                                            <!-- 带图标的 GroupBox -->
                                            <GroupBox Background="{DynamicResource AccentFillColorDefaultBrush}"
                                                      BorderBrush="{DynamicResource AccentStrokeColorDefaultBrush}"
                                                      BorderThickness="2"
                                                      Margin="0,0,0,16"
                                                      MinHeight="160">
                                                <GroupBox.Header>
                                                    <StackPanel Orientation="Horizontal">
                                                        <ui:SymbolIcon Symbol="Star24"
                                                                       FontSize="16"
                                                                       Foreground="White"
                                                                       Margin="0,0,8,0"/>
                                                        <TextBlock Text="重要功能"
                                                                   Foreground="White"
                                                                   FontWeight="Bold"/>
                                                    </StackPanel>
                                                </GroupBox.Header>
                                                <StackPanel Margin="12" VerticalAlignment="Top">
                                                    <TextBlock Text="这是一个重要的功能区域。"
                                                               Foreground="White"
                                                               Margin="0,0,0,12"/>
                                                    <ProgressBar Value="75"
                                                                 Height="20"
                                                                 Margin="0,0,0,8"/>
                                                    <TextBlock Text="进度: 75%"
                                                               Foreground="White"
                                                               FontSize="12"
                                                               Margin="0,0,0,8"/>
                                                    <Button Content="执行操作"
                                                            Command="{Binding HandleInteractionCommand}"
                                                            CommandParameter="执行重要操作"
                                                            Height="32"/>
                                                </StackPanel>
                                            </GroupBox>

                                            <!-- 统计信息 GroupBox -->
                                            <GroupBox Header="统计信息" 
                                                      Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                                                      BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                                      BorderThickness="1">
                                                <Grid Margin="12">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                    </Grid.RowDefinitions>
                                                    
                                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="总数:" FontWeight="Bold"/>
                                                    <TextBlock Grid.Row="0" Grid.Column="1" Text="1,234" HorizontalAlignment="Right"/>
                                                    
                                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="活跃:" FontWeight="Bold"/>
                                                    <TextBlock Grid.Row="1" Grid.Column="1" Text="987" HorizontalAlignment="Right"/>
                                                    
                                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="新增:" FontWeight="Bold"/>
                                                    <TextBlock Grid.Row="2" Grid.Column="1" Text="45" HorizontalAlignment="Right"/>
                                                </Grid>
                                            </GroupBox>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="基础功能代码示例"
                                Language="XAML"
                                Description="展示 GroupBox 的基础用法"
                                ShowTabs="True"
                                XamlCode="{Binding BasicXamlExample}"
                                CSharpCode="{Binding BasicCSharpExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 高级功能展示 -->
                    <ui:CardExpander Header="⚡ 高级功能" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 GroupBox 的高级功能和自定义样式"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>
                            
                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="高级 GroupBox 示例：" FontWeight="Medium" Margin="0,0,0,16"/>
                                    
                                    <!-- 嵌套 GroupBox -->
                                    <GroupBox Header="嵌套分组示例" 
                                              Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                                              BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                              BorderThickness="1"
                                              Margin="0,0,0,16">
                                        <StackPanel Margin="16">
                                            <TextBlock Text="这是父级分组的内容。" Margin="0,0,0,12"/>
                                            
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>
                                                
                                                <GroupBox Grid.Column="0" 
                                                          Header="子分组 A" 
                                                          Background="{DynamicResource ControlFillColorDefaultBrush}"
                                                          Margin="0,0,8,0">
                                                    <StackPanel Margin="12">
                                                        <RadioButton Content="选项 A1" GroupName="GroupA" IsChecked="True" Margin="0,0,0,4"/>
                                                        <RadioButton Content="选项 A2" GroupName="GroupA" Margin="0,0,0,4"/>
                                                        <RadioButton Content="选项 A3" GroupName="GroupA"/>
                                                    </StackPanel>
                                                </GroupBox>
                                                
                                                <GroupBox Grid.Column="1" 
                                                          Header="子分组 B" 
                                                          Background="{DynamicResource ControlFillColorDefaultBrush}"
                                                          Margin="8,0,0,0">
                                                    <StackPanel Margin="12">
                                                        <RadioButton Content="选项 B1" GroupName="GroupB" Margin="0,0,0,4"/>
                                                        <RadioButton Content="选项 B2" GroupName="GroupB" IsChecked="True" Margin="0,0,0,4"/>
                                                        <RadioButton Content="选项 B3" GroupName="GroupB"/>
                                                    </StackPanel>
                                                </GroupBox>
                                            </Grid>
                                        </StackPanel>
                                    </GroupBox>

                                    <!-- 自定义样式 GroupBox -->
                                    <GroupBox Header="自定义样式分组"
                                              Margin="0,0,0,16"
                                              MinHeight="180">
                                        <GroupBox.Style>
                                            <Style TargetType="GroupBox">
                                                <Setter Property="Background" Value="#FF2D3748"/>
                                                <Setter Property="Foreground" Value="White"/>
                                                <Setter Property="BorderBrush" Value="#FF4A5568"/>
                                                <Setter Property="BorderThickness" Value="2"/>
                                                <Setter Property="Padding" Value="12"/>
                                            </Style>
                                        </GroupBox.Style>
                                        <Grid Margin="16">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="2*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="*"/>
                                            </Grid.RowDefinitions>

                                            <StackPanel Grid.Column="0"
                                                        Grid.Row="0"
                                                        Margin="0,0,16,0"
                                                        VerticalAlignment="Top">
                                                <TextBlock Text="配置选项"
                                                           Foreground="White"
                                                           FontWeight="Bold"
                                                           Margin="0,0,0,12"/>
                                                <ComboBox Margin="0,0,0,8" Height="32">
                                                    <ComboBoxItem Content="选项 1"/>
                                                    <ComboBoxItem Content="选项 2" IsSelected="True"/>
                                                    <ComboBoxItem Content="选项 3"/>
                                                </ComboBox>
                                                <Slider Minimum="0"
                                                        Maximum="100"
                                                        Value="60"
                                                        Margin="0,0,0,8"
                                                        Height="20"/>
                                                <TextBlock Text="当前值: 60"
                                                           Foreground="White"
                                                           FontSize="12"/>
                                            </StackPanel>

                                            <StackPanel Grid.Column="1"
                                                        Grid.Row="0"
                                                        VerticalAlignment="Top">
                                                <TextBlock Text="操作"
                                                           Foreground="White"
                                                           FontWeight="Bold"
                                                           Margin="0,0,0,12"/>
                                                <Button Content="保存"
                                                        Command="{Binding HandleInteractionCommand}"
                                                        CommandParameter="保存配置"
                                                        Margin="0,0,0,8"
                                                        Height="32"/>
                                                <Button Content="重置"
                                                        Command="{Binding HandleInteractionCommand}"
                                                        CommandParameter="重置配置"
                                                        Height="32"/>
                                            </StackPanel>
                                        </Grid>
                                    </GroupBox>
                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="高级功能代码示例"
                                Language="XAML"
                                Description="展示 GroupBox 的高级用法和自定义样式"
                                ShowTabs="True"
                                XamlCode="{Binding AdvancedXamlExample}"
                                CSharpCode="{Binding AdvancedCSharpExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 样式展示 -->
                    <ui:CardExpander Header="🎨 样式展示" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示来自 Zylo.WPF 的各种 GroupBox 样式"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>
                            
                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="样式展示示例：" FontWeight="Medium" Margin="0,0,0,16"/>
                                    <Grid MinHeight="120">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- 默认样式 -->
                                        <GroupBox Grid.Column="0"
                                                  Header="默认样式"
                                                  Background="{DynamicResource ControlFillColorDefaultBrush}"
                                                  BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                                  BorderThickness="1"
                                                  Margin="0,0,8,0"
                                                  MinHeight="100">
                                            <StackPanel Margin="12" VerticalAlignment="Center">
                                                <TextBlock Text="这是默认样式的 GroupBox。"
                                                           TextWrapping="Wrap"
                                                           HorizontalAlignment="Center"
                                                           TextAlignment="Center"/>
                                            </StackPanel>
                                        </GroupBox>

                                        <!-- 现代化样式 -->
                                        <GroupBox Grid.Column="1"
                                                  Header="现代化样式"
                                                  Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                                                  BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                                  BorderThickness="0"
                                                  Margin="4,0,4,0"
                                                  MinHeight="100">
                                            <GroupBox.Effect>
                                                <DropShadowEffect Color="Black"
                                                                  Opacity="0.1"
                                                                  ShadowDepth="4"
                                                                  BlurRadius="8"/>
                                            </GroupBox.Effect>
                                            <StackPanel Margin="12" VerticalAlignment="Center">
                                                <TextBlock Text="这是现代化样式的 GroupBox。"
                                                           TextWrapping="Wrap"
                                                           HorizontalAlignment="Center"
                                                           TextAlignment="Center"/>
                                            </StackPanel>
                                        </GroupBox>

                                        <!-- 强调样式 -->
                                        <GroupBox Grid.Column="2"
                                                  Header="强调样式"
                                                  Background="{DynamicResource AccentFillColorDefaultBrush}"
                                                  BorderBrush="{DynamicResource AccentStrokeColorDefaultBrush}"
                                                  BorderThickness="2"
                                                  Margin="8,0,0,0"
                                                  MinHeight="100">
                                            <StackPanel Margin="12" VerticalAlignment="Center">
                                                <TextBlock Text="这是强调样式的 GroupBox。"
                                                           Foreground="White"
                                                           TextWrapping="Wrap"
                                                           HorizontalAlignment="Center"
                                                           TextAlignment="Center"/>
                                            </StackPanel>
                                        </GroupBox>
                                    </Grid>
                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="样式代码示例"
                                Language="XAML"
                                Description="展示如何使用 Zylo.WPF 中的 GroupBox 样式"
                                ShowTabs="True"
                                XamlCode="{Binding StylesXamlExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                </StackPanel>
            </Border>

            <!-- 底部操作栏 -->
            <Border Grid.Row="2" 
                    Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Padding="16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 统计信息 -->
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock Text="{Binding InteractionCount, StringFormat='交互次数: {0}'}" 
                                   FontSize="14" 
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   Margin="0,0,20,0"/>
                        <TextBlock Text="{Binding LastAction, StringFormat='最后操作: {0}'}" 
                                   FontSize="14" 
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                    </StackPanel>

                    <!-- 操作按钮 -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <ui:Button Content="重置计数" 
                                   Command="{Binding ResetCountCommand}"
                                   Appearance="Secondary"
                                   Margin="0,0,8,0"/>
                        <ui:Button Content="清除状态" 
                                   Command="{Binding ClearStatusCommand}"
                                   Appearance="Secondary"/>
                    </StackPanel>
                </Grid>
            </Border>

        </Grid>
    </ScrollViewer>
</UserControl>
