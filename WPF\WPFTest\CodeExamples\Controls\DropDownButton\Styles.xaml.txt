<!-- DropDownButton 样式使用示例 -->
<StackPanel Margin="20" Spacing="15">
    
    <!-- 使用 Zylo.WPF 中的 DropDownButton 样式 -->
    <GroupBox Header="Zylo.WPF 样式库中的 DropDownButton 样式" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <!-- 标准样式 -->
            <ui:DropDownButton Content="标准样式" 
                               Style="{StaticResource DropDownButtonStyle}"
                               Command="{Binding HandleInteractionCommand}"
                               CommandParameter="标准样式"
                               Margin="8">
                <ui:DropDownButton.Flyout>
                    <ContextMenu>
                        <MenuItem Header="选项1" Command="{Binding HandleInteractionCommand}" CommandParameter="标准选项1"/>
                        <MenuItem Header="选项2" Command="{Binding HandleInteractionCommand}" CommandParameter="标准选项2"/>
                    </ContextMenu>
                </ui:DropDownButton.Flyout>
            </ui:DropDownButton>

            <!-- 小型样式 -->
            <ui:DropDownButton Content="小型样式" 
                               Style="{StaticResource SmallDropDownButtonStyle}"
                               Command="{Binding HandleInteractionCommand}"
                               CommandParameter="小型样式"
                               Margin="8">
                <ui:DropDownButton.Icon>
                    <ui:SymbolIcon Symbol="Settings24" FontSize="14"/>
                </ui:DropDownButton.Icon>
                <ui:DropDownButton.Flyout>
                    <ContextMenu>
                        <MenuItem Header="小型选项1" Command="{Binding HandleInteractionCommand}" CommandParameter="小型选项1"/>
                        <MenuItem Header="小型选项2" Command="{Binding HandleInteractionCommand}" CommandParameter="小型选项2"/>
                    </ContextMenu>
                </ui:DropDownButton.Flyout>
            </ui:DropDownButton>

            <!-- 大型样式 -->
            <ui:DropDownButton Content="大型样式" 
                               Style="{StaticResource LargeDropDownButtonStyle}"
                               Command="{Binding HandleInteractionCommand}"
                               CommandParameter="大型样式"
                               Margin="8">
                <ui:DropDownButton.Icon>
                    <ui:SymbolIcon Symbol="Settings24" FontSize="20"/>
                </ui:DropDownButton.Icon>
                <ui:DropDownButton.Flyout>
                    <ContextMenu>
                        <MenuItem Header="大型选项1" Command="{Binding HandleInteractionCommand}" CommandParameter="大型选项1"/>
                        <MenuItem Header="大型选项2" Command="{Binding HandleInteractionCommand}" CommandParameter="大型选项2"/>
                    </ContextMenu>
                </ui:DropDownButton.Flyout>
            </ui:DropDownButton>
        </WrapPanel>
    </GroupBox>

    <!-- 特殊样式 -->
    <GroupBox Header="特殊样式" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <!-- Primary 样式 -->
            <ui:DropDownButton Content="Primary 样式" 
                               Style="{StaticResource PrimaryDropDownButtonStyle}"
                               Command="{Binding HandleInteractionCommand}"
                               CommandParameter="Primary样式"
                               Margin="8">
                <ui:DropDownButton.Flyout>
                    <ContextMenu>
                        <MenuItem Header="主要操作1" Command="{Binding HandleInteractionCommand}" CommandParameter="主要操作1"/>
                        <MenuItem Header="主要操作2" Command="{Binding HandleInteractionCommand}" CommandParameter="主要操作2"/>
                    </ContextMenu>
                </ui:DropDownButton.Flyout>
            </ui:DropDownButton>

            <!-- 透明样式 -->
            <ui:DropDownButton Content="透明样式" 
                               Style="{StaticResource TransparentDropDownButtonStyle}"
                               Command="{Binding HandleInteractionCommand}"
                               CommandParameter="透明样式"
                               Margin="8">
                <ui:DropDownButton.Icon>
                    <ui:SymbolIcon Symbol="EyeOff24"/>
                </ui:DropDownButton.Icon>
                <ui:DropDownButton.Flyout>
                    <ContextMenu>
                        <MenuItem Header="透明选项1" Command="{Binding HandleInteractionCommand}" CommandParameter="透明选项1"/>
                        <MenuItem Header="透明选项2" Command="{Binding HandleInteractionCommand}" CommandParameter="透明选项2"/>
                    </ContextMenu>
                </ui:DropDownButton.Flyout>
            </ui:DropDownButton>

            <!-- 现代化样式 -->
            <ui:DropDownButton Content="现代化样式" 
                               Style="{StaticResource ModernDropDownButtonStyle}"
                               Command="{Binding HandleInteractionCommand}"
                               CommandParameter="现代化样式"
                               Margin="8">
                <ui:DropDownButton.Icon>
                    <ui:SymbolIcon Symbol="Sparkle24"/>
                </ui:DropDownButton.Icon>
                <ui:DropDownButton.Flyout>
                    <ContextMenu>
                        <MenuItem Header="现代化选项1" Command="{Binding HandleInteractionCommand}" CommandParameter="现代化选项1"/>
                        <MenuItem Header="现代化选项2" Command="{Binding HandleInteractionCommand}" CommandParameter="现代化选项2"/>
                    </ContextMenu>
                </ui:DropDownButton.Flyout>
            </ui:DropDownButton>

            <!-- 渐变样式 -->
            <ui:DropDownButton Content="渐变样式" 
                               Style="{StaticResource GradientDropDownButtonStyle}"
                               Command="{Binding HandleInteractionCommand}"
                               CommandParameter="渐变样式"
                               Margin="8">
                <ui:DropDownButton.Icon>
                    <ui:SymbolIcon Symbol="ColorBackground24"/>
                </ui:DropDownButton.Icon>
                <ui:DropDownButton.Flyout>
                    <ContextMenu>
                        <MenuItem Header="渐变选项1" Command="{Binding HandleInteractionCommand}" CommandParameter="渐变选项1"/>
                        <MenuItem Header="渐变选项2" Command="{Binding HandleInteractionCommand}" CommandParameter="渐变选项2"/>
                    </ContextMenu>
                </ui:DropDownButton.Flyout>
            </ui:DropDownButton>
        </WrapPanel>
    </GroupBox>

    <!-- 自定义样式示例 -->
    <GroupBox Header="自定义样式定义示例" Padding="15">
        <StackPanel>
            <TextBlock Text="以下是如何在 Zylo.WPF 中定义 DropDownButton 样式的示例：" 
                       FontWeight="Bold" 
                       Margin="0,0,0,10"/>
            
            <Border Background="{DynamicResource ControlFillColorInputActiveBrush}"
                    BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="4"
                    Padding="12">
                <TextBlock FontFamily="Consolas" FontSize="12" TextWrapping="Wrap">
                    <Run Text="&lt;Style x:Key=&quot;CustomDropDownButtonStyle&quot; TargetType=&quot;ui:DropDownButton&quot;&gt;"/>
                    <LineBreak/>
                    <Run Text="    &lt;Setter Property=&quot;Background&quot; Value=&quot;{DynamicResource AccentFillColorDefaultBrush}&quot;/&gt;"/>
                    <LineBreak/>
                    <Run Text="    &lt;Setter Property=&quot;Foreground&quot; Value=&quot;{DynamicResource TextOnAccentFillColorPrimaryBrush}&quot;/&gt;"/>
                    <LineBreak/>
                    <Run Text="    &lt;Setter Property=&quot;BorderBrush&quot; Value=&quot;{DynamicResource AccentFillColorDefaultBrush}&quot;/&gt;"/>
                    <LineBreak/>
                    <Run Text="    &lt;Setter Property=&quot;BorderThickness&quot; Value=&quot;1&quot;/&gt;"/>
                    <LineBreak/>
                    <Run Text="    &lt;Setter Property=&quot;CornerRadius&quot; Value=&quot;8&quot;/&gt;"/>
                    <LineBreak/>
                    <Run Text="    &lt;Setter Property=&quot;Padding&quot; Value=&quot;16,8&quot;/&gt;"/>
                    <LineBreak/>
                    <Run Text="    &lt;Setter Property=&quot;FontWeight&quot; Value=&quot;Medium&quot;/&gt;"/>
                    <LineBreak/>
                    <Run Text="    &lt;Setter Property=&quot;Cursor&quot; Value=&quot;Hand&quot;/&gt;"/>
                    <LineBreak/>
                    <Run Text="    &lt;!-- 添加鼠标悬停和按下效果 --&gt;"/>
                    <LineBreak/>
                    <Run Text="    &lt;Style.Triggers&gt;"/>
                    <LineBreak/>
                    <Run Text="        &lt;Trigger Property=&quot;IsMouseOver&quot; Value=&quot;True&quot;&gt;"/>
                    <LineBreak/>
                    <Run Text="            &lt;Setter Property=&quot;Background&quot; Value=&quot;{DynamicResource AccentFillColorSecondaryBrush}&quot;/&gt;"/>
                    <LineBreak/>
                    <Run Text="        &lt;/Trigger&gt;"/>
                    <LineBreak/>
                    <Run Text="    &lt;/Style.Triggers&gt;"/>
                    <LineBreak/>
                    <Run Text="&lt;/Style&gt;"/>
                </TextBlock>
            </Border>
        </StackPanel>
    </GroupBox>

</StackPanel>
