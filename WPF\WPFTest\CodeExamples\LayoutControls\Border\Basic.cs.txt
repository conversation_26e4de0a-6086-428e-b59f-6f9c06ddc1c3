// Border C# 基础示例
// Border 是 WPF 中最基础的装饰控件，用于为其他控件添加边框和背景

using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace WPFTest.Examples.LayoutControls
{
    public class BorderBasicExample
    {
        /// <summary>
        /// 创建基础 Border 控件
        /// </summary>
        public static Border CreateBasicBorder()
        {
            var border = new Border
            {
                // 背景色
                Background = new SolidColorBrush(Colors.LightBlue),
                
                // 边框颜色
                BorderBrush = new SolidColorBrush(Colors.Blue),
                
                // 边框厚度
                BorderThickness = new Thickness(2),
                
                // 圆角半径
                CornerRadius = new CornerRadius(8),
                
                // 内边距
                Padding = new Thickness(16),
                
                // 外边距
                Margin = new Thickness(8),
                
                // 宽度和高度
                Width = 200,
                Height = 100
            };

            // 添加子控件
            var textBlock = new TextBlock
            {
                Text = "基础 Border",
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                FontSize = 14,
                FontWeight = FontWeights.Medium
            };

            border.Child = textBlock;
            return border;
        }

        /// <summary>
        /// 创建不同圆角的 Border
        /// </summary>
        public static Border CreateRoundedBorder(double cornerRadius)
        {
            return new Border
            {
                Background = Brushes.LightGreen,
                BorderBrush = Brushes.Green,
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(cornerRadius),
                Padding = new Thickness(12),
                Margin = new Thickness(4),
                Child = new TextBlock
                {
                    Text = $"圆角: {cornerRadius}px",
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center
                }
            };
        }

        /// <summary>
        /// 创建不同边框厚度的 Border
        /// </summary>
        public static Border CreateBorderWithThickness(double thickness)
        {
            return new Border
            {
                Background = Brushes.LightYellow,
                BorderBrush = Brushes.Orange,
                BorderThickness = new Thickness(thickness),
                CornerRadius = new CornerRadius(4),
                Padding = new Thickness(16),
                Margin = new Thickness(4),
                Child = new TextBlock
                {
                    Text = $"边框: {thickness}px",
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center
                }
            };
        }

        /// <summary>
        /// 创建透明背景的 Border
        /// </summary>
        public static Border CreateTransparentBorder()
        {
            return new Border
            {
                Background = Brushes.Transparent,
                BorderBrush = Brushes.Red,
                BorderThickness = new Thickness(2),
                CornerRadius = new CornerRadius(6),
                Padding = new Thickness(16),
                Margin = new Thickness(4),
                Child = new TextBlock
                {
                    Text = "透明背景",
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center,
                    Foreground = Brushes.Red
                }
            };
        }

        /// <summary>
        /// 创建带阴影效果的 Border
        /// </summary>
        public static Border CreateShadowBorder()
        {
            var border = new Border
            {
                Background = Brushes.White,
                BorderBrush = Brushes.LightGray,
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(8),
                Padding = new Thickness(16),
                Margin = new Thickness(8),
                Child = new TextBlock
                {
                    Text = "阴影 Border",
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center
                }
            };

            // 添加阴影效果
            border.Effect = new System.Windows.Media.Effects.DropShadowEffect
            {
                Color = Colors.Black,
                Opacity = 0.2,
                ShadowDepth = 4,
                BlurRadius = 8
            };

            return border;
        }
    }
}
