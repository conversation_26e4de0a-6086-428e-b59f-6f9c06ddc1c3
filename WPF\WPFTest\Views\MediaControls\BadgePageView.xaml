<UserControl
    d:DesignHeight="800"
    d:DesignWidth="1200"
    mc:Ignorable="d"
    x:Class="WPFTest.Views.MediaControls.BadgePageView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:codeExample1="clr-namespace:Zylo.WPF.Controls.CodeExample;assembly=Zylo.WPF"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
    </UserControl.Resources>

    <ScrollViewer>
        <StackPanel Margin="20">

            <!--  页面标题和状态栏  -->
            <StackPanel Margin="0,0,0,20">
                <TextBlock
                    FontSize="28"
                    FontWeight="Bold"
                    Margin="0,0,0,10"
                    Text="Badge 控件示例" />
                <TextBlock
                    FontSize="14"
                    Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                    Margin="0,0,0,10"
                    Text="Badge 控件用于突出显示项目、吸引注意力或标记状态" />

                <!--  Badge 用途说明  -->
                <Border
                    Background="{DynamicResource AccentFillColorDefaultBrush}"
                    CornerRadius="6"
                    Padding="12,8"
                    Margin="0,0,0,10">
                    <StackPanel>
                        <TextBlock
                            FontWeight="SemiBold"
                            Foreground="White"
                            Text="💡 Badge 的常见用途："
                            Margin="0,0,0,8" />
                        <TextBlock
                            Foreground="White"
                            FontSize="13"
                            TextWrapping="Wrap">
                            <Run Text="🔴 通知计数：显示未读消息、购物车商品数量" />
                            <LineBreak />
                            <Run Text="🟢 状态标识：在线/离线/忙碌状态显示" />
                            <LineBreak />
                            <Run Text="🏷️ 标签分类：给内容打标签（技术、设计、管理）" />
                            <LineBreak />
                            <Run Text="⭐ 等级标识：VIP、Pro、Basic 用户等级" />
                            <LineBreak />
                            <Run Text="🆕 新功能提示：标记新功能、热门内容、限时优惠" />
                        </TextBlock>
                    </StackPanel>
                </Border>

                <!--  状态栏  -->
                <Border
                    Background="{DynamicResource ControlFillColorDefaultBrush}"
                    CornerRadius="6"
                    Padding="12,8">
                    <StackPanel Orientation="Horizontal">
                        <ui:SymbolIcon
                            FontSize="16"
                            Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                            Margin="0,0,8,0"
                            Symbol="Info24" />
                        <TextBlock
                            FontWeight="Medium"
                            Text="{Binding StatusMessage}"
                            VerticalAlignment="Center" />
                    </StackPanel>
                </Border>
            </StackPanel>

            <!--  基础功能演示  -->
            <ui:CardExpander
                Header="基础功能演示"
                Icon="{ui:SymbolIcon Badge24}"
                IsExpanded="True"
                Margin="0,0,0,20"
                Style="{StaticResource ZyloDemoCardStyle}">
                <StackPanel>

                    <!--  主要演示区域  -->
                    <TextBlock
                        FontSize="16"
                        FontWeight="SemiBold"
                        Margin="0,0,0,16"
                        Text="Badge 基础用法" />

                    <Grid Margin="0,0,0,20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="300" />
                        </Grid.ColumnDefinitions>

                        <!--  演示区域  -->
                        <Border
                            Background="{DynamicResource ControlFillColorDefaultBrush}"
                            CornerRadius="8"
                            Grid.Column="0"
                            MinHeight="200"
                            Padding="20">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">

                                <!--  基础 Badge 演示  -->
                                <StackPanel HorizontalAlignment="Center" Margin="0,0,0,20">
                                    <TextBlock
                                        FontWeight="Medium"
                                        HorizontalAlignment="Center"
                                        Margin="0,0,0,12"
                                        Text="基础 Badge" />
                                    <ui:Badge
                                        Appearance="{Binding BadgeAppearance}"
                                        Visibility="{Binding ShowBadge, Converter={StaticResource BooleanToVisibilityConverter}}"
                                        HorizontalAlignment="Center">
                                        <TextBlock
                                            Text="{Binding BadgeContent}"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            TextAlignment="Center" />
                                    </ui:Badge>
                                </StackPanel>

                                <!--  带内容的 Badge 演示  -->
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock
                                        FontWeight="Medium"
                                        HorizontalAlignment="Center"
                                        Margin="0,0,0,12"
                                        Text="带按钮的 Badge" />
                                    <Border HorizontalAlignment="Center">
                                        <ui:Badge Appearance="Danger" HorizontalAlignment="Center">
                                            <ui:Button
                                                Appearance="Primary"
                                                Content="消息"
                                                Icon="{ui:SymbolIcon Mail24}"
                                                HorizontalAlignment="Center" />
                                        </ui:Badge>
                                    </Border>
                                </StackPanel>

                            </StackPanel>
                        </Border>

                        <!--  控制面板  -->
                        <Border
                            Background="{DynamicResource ControlFillColorDefaultBrush}"
                            CornerRadius="8"
                            Grid.Column="1"
                            Margin="20,0,0,0"
                            Padding="16">
                            <StackPanel>
                                <TextBlock
                                    FontSize="14"
                                    FontWeight="SemiBold"
                                    Margin="0,0,0,12"
                                    Text="控制面板" />

                                <!--  内容控制  -->
                                <GroupBox Header="内容设置" Margin="0,0,0,12">
                                    <StackPanel>
                                        <TextBlock Margin="0,0,0,8" Text="Badge 内容:" />
                                        <ui:TextBox
                                            Margin="0,0,0,8"
                                            PlaceholderText="输入 Badge 内容"
                                            Text="{Binding BadgeContent}" />

                                        <TextBlock Margin="0,0,0,8" Text="预设内容:" />
                                        <ComboBox
                                            ItemsSource="{Binding PresetContentOptions}"
                                            Margin="0,0,0,8"
                                            SelectedItem="{Binding SelectedPresetContent}" />
                                        <ui:Button
                                            Appearance="Primary"
                                            Command="{Binding ApplyPresetContentCommand}"
                                            Content="应用预设" />
                                    </StackPanel>
                                </GroupBox>

                                <!--  外观控制  -->
                                <GroupBox Header="外观设置" Margin="0,0,0,12">
                                    <StackPanel>
                                        <TextBlock Margin="0,0,0,8" Text="外观样式:" />
                                        <ComboBox
                                            ItemsSource="{Binding AppearanceOptions}"
                                            Margin="0,0,0,8"
                                            SelectedItem="{Binding SelectedAppearanceName}" />
                                        <ui:Button
                                            Appearance="Secondary"
                                            Command="{Binding ApplyAppearanceCommand}"
                                            Content="应用样式"
                                            Margin="0,0,0,8" />

                                        <CheckBox Content="显示 Badge" IsChecked="{Binding ShowBadge}" />
                                    </StackPanel>
                                </GroupBox>

                                <!--  操作按钮  -->
                                <GroupBox Header="操作">
                                    <StackPanel>
                                        <ui:Button
                                            Appearance="Success"
                                            Command="{Binding ToggleBadgeVisibilityCommand}"
                                            Content="切换显示/隐藏"
                                            Icon="{ui:SymbolIcon Eye24}" />
                                    </StackPanel>
                                </GroupBox>

                            </StackPanel>
                        </Border>

                    </Grid>

                    <!--  代码示例  -->
                    <codeExample1:CodeExampleControl
                        CSharpCode="{Binding BasicBadgeCSharpExample}"
                        Description="基础 Badge 控件的使用方法"
                        IsExpanded="False"
                        ShowTabs="True"
                        Title="基础用法"
                        XamlCode="{Binding BasicBadgeXamlExample}" />
                </StackPanel>
            </ui:CardExpander>

            <!--  高级功能演示  -->
            <ui:CardExpander
                Header="高级功能演示"
                Icon="{ui:SymbolIcon Settings24}"
                IsExpanded="True"
                Margin="0,0,0,20"
                Style="{StaticResource ZyloDemoCardStyle}">
                <StackPanel>

                    <!--  多种样式的 Badge 展示  -->
                    <TextBlock
                        FontSize="16"
                        FontWeight="SemiBold"
                        Margin="0,0,0,16"
                        Text="不同样式和用途的 Badge" />

                    <UniformGrid
                        Columns="3"
                        Margin="0,0,0,20"
                        Rows="3">

                        <!--  Primary Badge  -->
                        <StackPanel HorizontalAlignment="Center" Margin="8">
                            <ui:Badge Appearance="Primary" Margin="0,0,0,8" HorizontalAlignment="Center">
                                <TextBlock Text="Primary" HorizontalAlignment="Center" TextAlignment="Center" />
                            </ui:Badge>
                            <TextBlock
                                FontSize="12"
                                HorizontalAlignment="Center"
                                Text="Primary 样式" />
                        </StackPanel>

                        <!--  Secondary Badge  -->
                        <StackPanel HorizontalAlignment="Center" Margin="8">
                            <ui:Badge Appearance="Secondary" Margin="0,0,0,8" HorizontalAlignment="Center">
                                <TextBlock Text="Secondary" HorizontalAlignment="Center" TextAlignment="Center" />
                            </ui:Badge>
                            <TextBlock
                                FontSize="12"
                                HorizontalAlignment="Center"
                                Text="Secondary 样式" />
                        </StackPanel>

                        <!--  Success Badge  -->
                        <StackPanel HorizontalAlignment="Center" Margin="8">
                            <ui:Badge Appearance="Success" Margin="0,0,0,8" HorizontalAlignment="Center">
                                <TextBlock Text="Success" HorizontalAlignment="Center" TextAlignment="Center" />
                            </ui:Badge>
                            <TextBlock
                                FontSize="12"
                                HorizontalAlignment="Center"
                                Text="Success 样式" />
                        </StackPanel>

                        <!--  Danger Badge  -->
                        <StackPanel HorizontalAlignment="Center" Margin="8">
                            <ui:Badge Appearance="Danger" Margin="0,0,0,8" HorizontalAlignment="Center">
                                <TextBlock Text="Danger" HorizontalAlignment="Center" TextAlignment="Center" />
                            </ui:Badge>
                            <TextBlock
                                FontSize="12"
                                HorizontalAlignment="Center"
                                Text="Danger 样式" />
                        </StackPanel>

                        <!--  Caution Badge  -->
                        <StackPanel HorizontalAlignment="Center" Margin="8">
                            <ui:Badge Appearance="Caution" Margin="0,0,0,8" HorizontalAlignment="Center">
                                <TextBlock Text="Caution" HorizontalAlignment="Center" TextAlignment="Center" />
                            </ui:Badge>
                            <TextBlock
                                FontSize="12"
                                HorizontalAlignment="Center"
                                Text="Caution 样式" />
                        </StackPanel>

                        <!--  Info Badge  -->
                        <StackPanel HorizontalAlignment="Center" Margin="8">
                            <ui:Badge Appearance="Info" Margin="0,0,0,8" HorizontalAlignment="Center">
                                <TextBlock Text="Info" HorizontalAlignment="Center" TextAlignment="Center" />
                            </ui:Badge>
                            <TextBlock
                                FontSize="12"
                                HorizontalAlignment="Center"
                                Text="Info 样式" />
                        </StackPanel>

                        <!--  Light Badge  -->
                        <StackPanel HorizontalAlignment="Center" Margin="8">
                            <Border BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}" BorderThickness="1" CornerRadius="12" HorizontalAlignment="Center">
                                <ui:Badge Appearance="Light" Margin="0,0,0,8" HorizontalAlignment="Center">
                                    <TextBlock Text="Light" HorizontalAlignment="Center" TextAlignment="Center" Foreground="{DynamicResource TextFillColorPrimaryBrush}" />
                                </ui:Badge>
                            </Border>
                            <TextBlock
                                FontSize="12"
                                HorizontalAlignment="Center"
                                Text="Light 样式" />
                        </StackPanel>

                        <!--  Dark Badge  -->
                        <StackPanel HorizontalAlignment="Center" Margin="8">
                            <ui:Badge Appearance="Dark" Margin="0,0,0,8" HorizontalAlignment="Center">
                                <TextBlock Text="Dark" HorizontalAlignment="Center" TextAlignment="Center" />
                            </ui:Badge>
                            <TextBlock
                                FontSize="12"
                                HorizontalAlignment="Center"
                                Text="Dark 样式" />
                        </StackPanel>

                        <!--  Transparent Badge  -->
                        <StackPanel HorizontalAlignment="Center" Margin="8">
                            <Border BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}" BorderThickness="1" CornerRadius="12" HorizontalAlignment="Center">
                                <ui:Badge Appearance="Transparent" Margin="0,0,0,8" HorizontalAlignment="Center">
                                    <TextBlock Text="Transparent" HorizontalAlignment="Center" TextAlignment="Center" Foreground="{DynamicResource TextFillColorPrimaryBrush}" />
                                </ui:Badge>
                            </Border>
                            <TextBlock
                                FontSize="12"
                                HorizontalAlignment="Center"
                                Text="Transparent 样式" />
                        </StackPanel>

                    </UniformGrid>

                </StackPanel>
            </ui:CardExpander>

        </StackPanel>
    </ScrollViewer>

</UserControl>
