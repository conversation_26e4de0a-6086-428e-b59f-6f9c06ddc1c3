<UserControl x:Class="WPFTest.Views.ButtonControls.ButtonPageView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:WPFTest.Views.ButtonControls"
             xmlns:buttonControls="clr-namespace:WPFTest.ViewModels.ButtonControls"
             xmlns:mvvm="http://prismlibrary.com/"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:codeExample="clr-namespace:Zylo.WPF.Controls.CodeExample;assembly=Zylo.WPF"
             mc:Ignorable="d"
             d:DataContext="{d:DesignInstance buttonControls:ButtonPageViewModel}"
             mvvm:ViewModelLocator.AutoWireViewModel="True"
             d:DesignHeight="2500" d:DesignWidth="1200">

    <UserControl.Resources>
        <!-- 🎨 所有按钮样式现在都来自 Zylo.WPF 项目 -->
        <!-- 圆形按钮样式：CircleButtonStyle, SmallCircleButtonStyle, LargeCircleButtonStyle -->
        <!-- 透明圆形按钮：TransparentCircleButtonStyle, BorderlessCircleButtonStyle -->
        <!-- 矩形按钮样式：RectangleButtonStyle, SmallRectangleButtonStyle, LargeRectangleButtonStyle -->
        <!-- 透明矩形按钮：TransparentRectangleButtonStyle, BorderlessRectangleButtonStyle -->
        <!-- 特殊按钮样式：ModernButtonStyle, GradientButtonStyle -->
        <!-- 所有样式定义在：WPF/Zylo.WPF/Resources/Buttons/ 目录下 -->
    </UserControl.Resources>

  

         


    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 标题区域 -->
            <StackPanel Grid.Row="0" Margin="0,0,0,20">
                <TextBlock Text="🎯 现代化按钮示例库"
                           FontSize="32"
                           FontWeight="Bold"
                           Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                           Margin="0,0,0,8"/>
                <TextBlock Text="展示各种 WPF-UI 按钮样式和自定义现代化按钮效果"
                           FontSize="14"
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                           Margin="0,0,0,16"/>

                <!-- 状态栏 -->
                <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                        BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Padding="16,12">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0"
                                   Text="{Binding StatusMessage}"
                                   FontSize="14"
                                   VerticalAlignment="Center"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>

                        <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="16,0">
                            <TextBlock Text="最后操作: "
                                       FontSize="12"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       VerticalAlignment="Center"/>
                            <TextBlock Text="{Binding LastAction}"
                                       FontSize="12"
                                       FontWeight="Medium"
                                       Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                       VerticalAlignment="Center"/>
                        </StackPanel>

                        <StackPanel Grid.Column="2" Orientation="Horizontal">
                            <TextBlock Text="点击次数: "
                                       FontSize="12"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       VerticalAlignment="Center"/>
                            <TextBlock Text="{Binding ClickCount}"
                                       FontSize="12"
                                       FontWeight="Bold"
                                       Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </Grid>
                </Border>
            </StackPanel>

            <!-- 主要内容区域 -->
            <Border Grid.Row="1"  Margin="0,0,0,20">
                <StackPanel>

                    <!-- WPF-UI 标准按钮 -->
                    <ui:CardExpander Header="🎨 WPF-UI 标准按钮" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 WPF-UI 库提供的各种按钮外观样式"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <WrapPanel Orientation="Horizontal">
                                <ui:Button Content="Primary"
                                           Appearance="Primary"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="Primary"
                                           Margin="4"/>
                                <ui:Button Content="Secondary"
                                           Appearance="Secondary"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="Secondary"
                                           Margin="4"/>
                                <ui:Button Content="Success"
                                           Appearance="Success"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="Success"
                                           Margin="4"/>
                                <ui:Button Content="Danger"
                                           Appearance="Danger"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="Danger"
                                           Margin="4"/>
                                <ui:Button Content="Info"
                                           Appearance="Info"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="Info"
                                           Margin="4"/>
                                <ui:Button Content="Caution"
                                           Appearance="Caution"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="Caution"
                                           Margin="4"/>
                                <ui:Button Content="Transparent"
                                           Appearance="Transparent"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="Transparent"
                                           Margin="4"/>
                            </WrapPanel>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="WPF-UI 标准按钮代码示例"
                                Language="C#"
                                Description="展示如何使用 WPF-UI 库的标准按钮样式"
                                ShowTabs="True"
                                XamlCode="{Binding BasicButtonXamlExample}"
                                IsExpanded="False"
                                Margin="0,16,0,0"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 按钮尺寸 -->
                    <ui:CardExpander Header="📏 按钮尺寸" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="不同尺寸的按钮展示"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <WrapPanel Orientation="Horizontal" VerticalAlignment="Center">
                                <ui:Button Content="小型按钮"
                                           FontSize="12"
                                           Padding="8,4"
                                           MinHeight="28"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="小型按钮"
                                           Margin="4"/>
                                <ui:Button Content="中型按钮"
                                           FontSize="14"
                                           Padding="12,6"
                                           MinHeight="32"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="中型按钮"
                                           Margin="4"/>
                                <ui:Button Content="大型按钮"
                                           FontSize="16"
                                           Padding="16,8"
                                           MinHeight="40"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="大型按钮"
                                           Margin="4"/>
                                <ui:Button Content="超大按钮"
                                           FontSize="18"
                                           Padding="20,10"
                                           MinHeight="48"
                                           FontWeight="SemiBold"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="超大按钮"
                                           Margin="4"/>
                            </WrapPanel>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="按钮尺寸代码示例"
                                Language="C#"
                                Description="展示如何设置不同尺寸的按钮"
                                ShowTabs="True"
                                XamlCode="{Binding BasicButtonXamlExample}"
                                IsExpanded="False"
                                Margin="0,16,0,0"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 自定义样式按钮 -->
                    <ui:CardExpander Header="✨ 自定义样式按钮" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="使用自定义样式创建的现代化按钮效果"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <WrapPanel Orientation="Horizontal">
                                <ui:Button Content="现代化按钮"
                                           Style="{StaticResource ModernButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="现代化样式"
                                           Margin="8"/>
                                <ui:Button Content="渐变按钮"
                                           Style="{StaticResource GradientButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="渐变样式"
                                           Margin="8"/>
                            </WrapPanel>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="自定义样式按钮代码示例"
                                Language="C#"
                                Description="展示如何使用现代化和渐变按钮样式"
                                ShowTabs="True"
                                XamlCode="{Binding ModernButtonXamlExample}"
                                CSharpCode="{Binding ModernButtonCSharpExample}"
                                IsExpanded="False"
                                Margin="0,16,0,0"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 图标按钮 -->
                    <ui:CardExpander Header="🔘 图标按钮" IsExpanded="True"  Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="带图标的按钮，提供更直观的用户体验"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <WrapPanel Orientation="Horizontal">
                                <ui:Button Content="添加"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="添加"
                                           Margin="4">
                                    <ui:Button.Icon>
                                        <ui:SymbolIcon Symbol="Add24"/>
                                    </ui:Button.Icon>
                                </ui:Button>
                                <ui:Button Content="编辑"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="编辑"
                                           Margin="4">
                                    <ui:Button.Icon>
                                        <ui:SymbolIcon Symbol="Edit24"/>
                                    </ui:Button.Icon>
                                </ui:Button>
                                <ui:Button Content="删除"
                                           Appearance="Danger"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="删除"
                                           Margin="4">
                                    <ui:Button.Icon>
                                        <ui:SymbolIcon Symbol="Delete24"/>
                                    </ui:Button.Icon>
                                </ui:Button>
                                <ui:Button Content="保存"
                                           Appearance="Success"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="保存"
                                           Margin="4">
                                    <ui:Button.Icon>
                                        <ui:SymbolIcon Symbol="Save24"/>
                                    </ui:Button.Icon>
                                </ui:Button>
                            </WrapPanel>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="图标按钮代码示例"
                                Language="C#"
                                Description="展示如何创建带图标的按钮"
                                ShowTabs="True"
                                XamlCode="{Binding BasicButtonXamlExample}"
                                IsExpanded="False"
                                Margin="0,16,0,0"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 圆形图标按钮 -->
                    <ui:CardExpander Header="⭕ 圆形图标按钮" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="圆形设计的图标按钮，适合工具栏和快捷操作"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 标准圆形按钮 -->
                            <TextBlock Text="标准尺寸 (48x48)" FontWeight="SemiBold" Margin="0,0,0,8"/>
                            <WrapPanel Orientation="Horizontal" Margin="0,0,0,16">
                                <ui:Button Style="{StaticResource CircleButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="添加"
                                           ToolTip="添加新项目"
                                           Margin="4">
                                    <ui:SymbolIcon Symbol="Add24" FontSize="20"/>
                                </ui:Button>
                                <ui:Button Style="{StaticResource CircleButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="编辑"
                                           ToolTip="编辑项目"
                                           Margin="4">
                                    <ui:SymbolIcon Symbol="Edit24" FontSize="20"/>
                                </ui:Button>
                                <ui:Button Style="{StaticResource CircleButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="删除"
                                           ToolTip="删除项目"
                                           Margin="4">
                                    <ui:SymbolIcon Symbol="Delete24" FontSize="20"/>
                                </ui:Button>
                                <ui:Button Style="{StaticResource CircleButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="搜索"
                                           ToolTip="搜索内容"
                                           Margin="4">
                                    <ui:SymbolIcon Symbol="Search24" FontSize="20"/>
                                </ui:Button>
                                <ui:Button Style="{StaticResource CircleButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="设置"
                                           ToolTip="打开设置"
                                           Margin="4">
                                    <ui:SymbolIcon Symbol="Settings24" FontSize="20"/>
                                </ui:Button>
                            </WrapPanel>

                            <!-- 小型圆形按钮 -->
                            <TextBlock Text="小型尺寸 (36x36)" FontWeight="SemiBold" Margin="0,0,0,8"/>
                            <WrapPanel Orientation="Horizontal" Margin="0,0,0,16">
                                <ui:Button Style="{StaticResource SmallCircleButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="小型添加"
                                           ToolTip="小型添加按钮"
                                           Margin="4">
                                    <ui:SymbolIcon Symbol="Add24" FontSize="16"/>
                                </ui:Button>
                                <ui:Button Style="{StaticResource SmallCircleButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="小型编辑"
                                           ToolTip="小型编辑按钮"
                                           Margin="4">
                                    <ui:SymbolIcon Symbol="Edit24" FontSize="16"/>
                                </ui:Button>
                                <ui:Button Style="{StaticResource SmallCircleButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="小型删除"
                                           ToolTip="小型删除按钮"
                                           Margin="4">
                                    <ui:SymbolIcon Symbol="Delete24" FontSize="16"/>
                                </ui:Button>
                            </WrapPanel>

                            <!-- 大型圆形按钮 -->
                            <TextBlock Text="大型尺寸 (56x56)" FontWeight="SemiBold" Margin="0,0,0,8"/>
                            <WrapPanel Orientation="Horizontal" Margin="0,0,0,16">
                                <ui:Button Style="{StaticResource LargeCircleButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="大型添加"
                                           ToolTip="大型添加按钮"
                                           Margin="4">
                                    <ui:SymbolIcon Symbol="Add24" FontSize="24"/>
                                </ui:Button>
                                <ui:Button Style="{StaticResource LargeCircleButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="大型播放"
                                           ToolTip="大型播放按钮"
                                           Margin="4">
                                    <ui:SymbolIcon Symbol="Play24" FontSize="24"/>
                                </ui:Button>
                            </WrapPanel>

                            <!-- 透明圆形按钮 (带边框) -->
                            <TextBlock Text="透明样式 (带边框)" FontWeight="SemiBold" Margin="0,0,0,8"/>
                            <WrapPanel Orientation="Horizontal" Margin="0,0,0,16">
                                <ui:Button Style="{StaticResource TransparentCircleButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="透明添加"
                                           ToolTip="透明添加按钮"
                                           Margin="4">
                                    <ui:SymbolIcon Symbol="Add24" FontSize="20"/>
                                </ui:Button>
                                <ui:Button Style="{StaticResource TransparentCircleButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="透明收藏"
                                           ToolTip="透明收藏按钮"
                                           Margin="4">
                                    <ui:SymbolIcon Symbol="Heart24" FontSize="20"/>
                                </ui:Button>
                                <ui:Button Style="{StaticResource TransparentCircleButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="透明分享"
                                           ToolTip="透明分享按钮"
                                           Margin="4">
                                    <ui:SymbolIcon Symbol="Share24" FontSize="20"/>
                                </ui:Button>
                            </WrapPanel>

                            <!-- 无边框透明圆形按钮 -->
                            <TextBlock Text="无边框透明样式" FontWeight="SemiBold" Margin="0,0,0,8"/>
                            <WrapPanel Orientation="Horizontal">
                                <ui:Button Style="{StaticResource BorderlessCircleButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="无边框更多"
                                           ToolTip="更多选项"
                                           Margin="4">
                                    <ui:SymbolIcon Symbol="MoreHorizontal24" FontSize="20"/>
                                </ui:Button>
                                <ui:Button Style="{StaticResource BorderlessCircleButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="无边框关闭"
                                           ToolTip="关闭"
                                           Margin="4">
                                    <ui:SymbolIcon Symbol="Dismiss24" FontSize="20"/>
                                </ui:Button>
                                <ui:Button Style="{StaticResource BorderlessCircleButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="无边框刷新"
                                           ToolTip="刷新"
                                           Margin="4">
                                    <ui:SymbolIcon Symbol="ArrowClockwise24" FontSize="20"/>
                                </ui:Button>
                                <ui:Button Style="{StaticResource BorderlessCircleButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="无边框帮助"
                                           ToolTip="帮助"
                                           Margin="4">
                                    <ui:SymbolIcon Symbol="QuestionCircle24" FontSize="20"/>
                                </ui:Button>
                            </WrapPanel>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="圆形按钮代码示例"
                                Language="C#"
                                Description="展示如何使用 Zylo.WPF 中的圆形按钮样式"
                                ShowTabs="True"
                                XamlCode="{Binding CircleButtonXamlExample}"
                                CSharpCode="{Binding CircleButtonCSharpExample}"
                                IsExpanded="False"
                                Margin="0,16,0,0"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 矩形图标按钮 -->
                    <ui:CardExpander Header="⬜ 矩形图标按钮" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="矩形设计的图标按钮，适合工具栏和操作面板"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 标准矩形按钮 -->
                            <TextBlock Text="标准尺寸 (80x48)" FontWeight="SemiBold" Margin="0,0,0,8"/>
                            <WrapPanel Orientation="Horizontal" Margin="0,0,0,16">
                                <ui:Button Style="{StaticResource RectangleButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="矩形新建"
                                           ToolTip="新建文件"
                                           Margin="4">
                                    <StackPanel Orientation="Horizontal">
                                        <ui:SymbolIcon Symbol="DocumentAdd24" FontSize="16" Margin="0,0,4,0"/>
                                        <TextBlock Text="新建" FontSize="12"/>
                                    </StackPanel>
                                </ui:Button>
                                <ui:Button Style="{StaticResource RectangleButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="矩形打开"
                                           ToolTip="打开文件"
                                           Margin="4">
                                    <StackPanel Orientation="Horizontal">
                                        <ui:SymbolIcon Symbol="FolderOpen24" FontSize="16" Margin="0,0,4,0"/>
                                        <TextBlock Text="打开" FontSize="12"/>
                                    </StackPanel>
                                </ui:Button>
                                <ui:Button Style="{StaticResource RectangleButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="矩形保存"
                                           ToolTip="保存文件"
                                           Margin="4">
                                    <StackPanel Orientation="Horizontal">
                                        <ui:SymbolIcon Symbol="Save24" FontSize="16" Margin="0,0,4,0"/>
                                        <TextBlock Text="保存" FontSize="12"/>
                                    </StackPanel>
                                </ui:Button>
                                <ui:Button Style="{StaticResource RectangleButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="矩形导出"
                                           ToolTip="导出文件"
                                           Margin="4">
                                    <StackPanel Orientation="Horizontal">
                                        <ui:SymbolIcon Symbol="ShareAndroid24" FontSize="16" Margin="0,0,4,0"/>
                                        <TextBlock Text="导出" FontSize="12"/>
                                    </StackPanel>
                                </ui:Button>
                            </WrapPanel>

                            <!-- 小型矩形按钮 -->
                            <TextBlock Text="小型尺寸 (64x36)" FontWeight="SemiBold" Margin="0,0,0,8"/>
                            <WrapPanel Orientation="Horizontal" Margin="0,0,0,16">
                                <ui:Button Style="{StaticResource SmallRectangleButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="小型复制"
                                           ToolTip="复制"
                                           Margin="4">
                                    <StackPanel Orientation="Horizontal">
                                        <ui:SymbolIcon Symbol="Copy24" FontSize="14" Margin="0,0,2,0"/>
                                        <TextBlock Text="复制" FontSize="11"/>
                                    </StackPanel>
                                </ui:Button>
                                <ui:Button Style="{StaticResource SmallRectangleButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="小型粘贴"
                                           ToolTip="粘贴"
                                           Margin="4">
                                    <StackPanel Orientation="Horizontal">
                                        <ui:SymbolIcon Symbol="ClipboardPaste24" FontSize="14" Margin="0,0,2,0"/>
                                        <TextBlock Text="粘贴" FontSize="11"/>
                                    </StackPanel>
                                </ui:Button>
                                <ui:Button Style="{StaticResource SmallRectangleButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="小型撤销"
                                           ToolTip="撤销"
                                           Margin="4">
                                    <StackPanel Orientation="Horizontal">
                                        <ui:SymbolIcon Symbol="ArrowUndo24" FontSize="14" Margin="0,0,2,0"/>
                                        <TextBlock Text="撤销" FontSize="11"/>
                                    </StackPanel>
                                </ui:Button>
                            </WrapPanel>

                            <!-- 大型矩形按钮 -->
                            <TextBlock Text="大型尺寸 (100x56)" FontWeight="SemiBold" Margin="0,0,0,8"/>
                            <WrapPanel Orientation="Horizontal" Margin="0,0,0,16">
                                <ui:Button Style="{StaticResource LargeRectangleButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="大型开始"
                                           ToolTip="开始处理"
                                           Margin="4">
                                    <StackPanel Orientation="Horizontal">
                                        <ui:SymbolIcon Symbol="Play24" FontSize="20" Margin="0,0,6,0"/>
                                        <TextBlock Text="开始" FontSize="14"/>
                                    </StackPanel>
                                </ui:Button>
                                <ui:Button Style="{StaticResource LargeRectangleButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="大型停止"
                                           ToolTip="停止处理"
                                           Margin="4">
                                    <StackPanel Orientation="Horizontal">
                                        <ui:SymbolIcon Symbol="Stop24" FontSize="20" Margin="0,0,6,0"/>
                                        <TextBlock Text="停止" FontSize="14"/>
                                    </StackPanel>
                                </ui:Button>
                            </WrapPanel>

                            <!-- 透明矩形按钮 (带边框) -->
                            <TextBlock Text="透明样式 (带边框)" FontWeight="SemiBold" Margin="0,0,0,8"/>
                            <WrapPanel Orientation="Horizontal" Margin="0,0,0,16">
                                <ui:Button Style="{StaticResource TransparentRectangleButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="透明上传"
                                           ToolTip="上传文件"
                                           Margin="4">
                                    <StackPanel Orientation="Horizontal">
                                        <ui:SymbolIcon Symbol="ArrowUpload24" FontSize="16" Margin="0,0,4,0"/>
                                        <TextBlock Text="上传" FontSize="12"/>
                                    </StackPanel>
                                </ui:Button>
                                <ui:Button Style="{StaticResource TransparentRectangleButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="透明下载"
                                           ToolTip="下载文件"
                                           Margin="4">
                                    <StackPanel Orientation="Horizontal">
                                        <ui:SymbolIcon Symbol="ArrowDownload24" FontSize="16" Margin="0,0,4,0"/>
                                        <TextBlock Text="下载" FontSize="12"/>
                                    </StackPanel>
                                </ui:Button>
                                <ui:Button Style="{StaticResource TransparentRectangleButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="透明同步"
                                           ToolTip="同步数据"
                                           Margin="4">
                                    <StackPanel Orientation="Horizontal">
                                        <ui:SymbolIcon Symbol="ArrowSync24" FontSize="16" Margin="0,0,4,0"/>
                                        <TextBlock Text="同步" FontSize="12"/>
                                    </StackPanel>
                                </ui:Button>
                            </WrapPanel>

                            <!-- 无边框矩形按钮 -->
                            <TextBlock Text="无边框透明样式" FontWeight="SemiBold" Margin="0,0,0,8"/>
                            <WrapPanel Orientation="Horizontal">
                                <ui:Button Style="{StaticResource BorderlessRectangleButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="无边框菜单"
                                           ToolTip="菜单"
                                           Margin="4">
                                    <StackPanel Orientation="Horizontal">
                                        <ui:SymbolIcon Symbol="Navigation24" FontSize="16" Margin="0,0,4,0"/>
                                        <TextBlock Text="菜单" FontSize="12"/>
                                    </StackPanel>
                                </ui:Button>
                                <ui:Button Style="{StaticResource BorderlessRectangleButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="无边框工具"
                                           ToolTip="工具"
                                           Margin="4">
                                    <StackPanel Orientation="Horizontal">
                                        <ui:SymbolIcon Symbol="Wrench24" FontSize="16" Margin="0,0,4,0"/>
                                        <TextBlock Text="工具" FontSize="12"/>
                                    </StackPanel>
                                </ui:Button>
                                <ui:Button Style="{StaticResource BorderlessRectangleButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="无边框视图"
                                           ToolTip="视图"
                                           Margin="4">
                                    <StackPanel Orientation="Horizontal">
                                        <ui:SymbolIcon Symbol="Eye24" FontSize="16" Margin="0,0,4,0"/>
                                        <TextBlock Text="视图" FontSize="12"/>
                                    </StackPanel>
                                </ui:Button>
                                <ui:Button Style="{StaticResource BorderlessRectangleButtonStyle}"
                                           Command="{Binding ButtonClickCommand}"
                                           CommandParameter="无边框选项"
                                           ToolTip="选项"
                                           Margin="4">
                                    <StackPanel Orientation="Horizontal">
                                        <ui:SymbolIcon Symbol="Options24" FontSize="16" Margin="0,0,4,0"/>
                                        <TextBlock Text="选项" FontSize="12"/>
                                    </StackPanel>
                                </ui:Button>
                            </WrapPanel>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="矩形按钮代码示例"
                                Language="C#"
                                Description="展示如何使用 Zylo.WPF 中的矩形按钮样式"
                                ShowTabs="True"
                                XamlCode="{Binding RectangleButtonXamlExample}"
                                CSharpCode="{Binding RectangleButtonCSharpExample}"
                                IsExpanded="False"
                                Margin="0,16,0,0"/>
                        </StackPanel>
                    </ui:CardExpander>
                </StackPanel>
            </Border>

            <!-- 底部操作栏 -->
            <Border Grid.Row="2"
                    Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Padding="16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                        <ui:SymbolIcon Symbol="Info24"
                                       FontSize="16"
                                       Margin="0,0,8,0"
                                       Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"/>
                        <TextBlock Text="这是一个简化的 WPF-UI 按钮示例库，展示了各种现代化的按钮样式和功能。"
                                   FontSize="14"
                                   VerticalAlignment="Center"
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                    </StackPanel>

                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <ui:Button Content="重置计数"
                                   Appearance="Caution"
                                   Command="{Binding ResetCommand}"
                                   Margin="0,0,8,0">
                            <ui:Button.Icon>
                                <ui:SymbolIcon Symbol="ArrowReset24"/>
                            </ui:Button.Icon>
                        </ui:Button>
                        <ui:Button Content="关于"
                                   Appearance="Transparent"
                                   Command="{Binding ButtonClickCommand}"
                                   CommandParameter="关于">
                            <ui:Button.Icon>
                                <ui:SymbolIcon Symbol="Info24"/>
                            </ui:Button.Icon>
                        </ui:Button>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>
    </ScrollViewer>
</UserControl>