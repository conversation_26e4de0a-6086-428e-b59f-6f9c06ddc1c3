<!-- WPF-UI CardExpander 基础用法示例 -->
<StackPanel Margin="20">

    <!-- 简单的 CardExpander -->
    <TextBlock Text="简单的 CardExpander" FontWeight="Bold" Margin="0,0,0,8"/>
    <ui:CardExpander Header="简单的可展开卡片" 
                     IsExpanded="True"
                     Margin="0,0,0,16">
        <StackPanel Margin="16">
            <TextBlock Text="这是一个简单的 CardExpander 示例。" 
                       FontWeight="Medium" 
                       Margin="0,0,0,8"/>
            <TextBlock Text="CardExpander 结合了 Card 的现代化样式和 Expander 的可展开功能。" 
                       TextWrapping="Wrap"
                       Margin="0,0,0,12"/>
            <Button Content="卡片内按钮" HorizontalAlignment="Left"/>
        </StackPanel>
    </ui:CardExpander>

    <!-- 带图标的 CardExpander -->
    <TextBlock Text="带图标的 CardExpander" FontWeight="Bold" Margin="0,0,0,8"/>
    <ui:CardExpander IsExpanded="False" Margin="0,0,0,16">
        <ui:CardExpander.Header>
            <StackPanel Orientation="Horizontal">
                <ui:SymbolIcon Symbol="Settings24" 
                               FontSize="20" 
                               Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                               Margin="0,0,12,0"/>
                <TextBlock Text="带图标的可展开卡片" 
                           FontWeight="Medium" 
                           VerticalAlignment="Center"/>
            </StackPanel>
        </ui:CardExpander.Header>
        <StackPanel Margin="16">
            <TextBlock Text="这个 CardExpander 在标题中包含了图标。" 
                       Margin="0,0,0,8"/>
            <TextBlock Text="可以通过自定义 Header 来添加更丰富的标题内容。" 
                       TextWrapping="Wrap"
                       Margin="0,0,0,12"/>
            <StackPanel Orientation="Horizontal">
                <Button Content="配置" Margin="0,0,8,0"/>
                <Button Content="重置"/>
            </StackPanel>
        </StackPanel>
    </ui:CardExpander>

    <!-- 信息类型的 CardExpander -->
    <TextBlock Text="信息类型的 CardExpander" FontWeight="Bold" Margin="0,0,0,8"/>
    <ui:CardExpander Header="重要信息" 
                     IsExpanded="False"
                     Margin="0,0,0,16">
        <Grid Margin="16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <ui:SymbolIcon Grid.Column="0"
                           Symbol="Warning24" 
                           FontSize="32" 
                           Foreground="{DynamicResource SystemFillColorCautionBrush}"
                           Margin="0,0,16,0"
                           VerticalAlignment="Top"/>
            
            <StackPanel Grid.Column="1">
                <TextBlock Text="系统维护通知" 
                           FontWeight="Bold" 
                           FontSize="16"
                           Margin="0,0,0,8"/>
                <TextBlock Text="系统将在今晚 23:00 - 01:00 进行维护升级，期间服务可能会中断。" 
                           TextWrapping="Wrap"
                           Margin="0,0,0,8"/>
                <TextBlock Text="• 请提前保存您的工作" Margin="0,0,0,2"/>
                <TextBlock Text="• 维护期间无法访问系统" Margin="0,0,0,2"/>
                <TextBlock Text="• 如有紧急情况请联系管理员" Margin="0,0,0,8"/>
                <Button Content="我知道了" HorizontalAlignment="Left"/>
            </StackPanel>
        </Grid>
    </ui:CardExpander>

    <!-- 数据展示的 CardExpander -->
    <TextBlock Text="数据展示的 CardExpander" FontWeight="Bold" Margin="0,0,0,8"/>
    <ui:CardExpander Header="数据统计" 
                     IsExpanded="True"
                     Margin="0,0,0,16">
        <StackPanel Margin="16">
            <Grid Margin="0,0,0,16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <TextBlock Text="156" 
                               FontSize="24" 
                               FontWeight="Bold" 
                               HorizontalAlignment="Center"
                               Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"/>
                    <TextBlock Text="总项目" 
                               FontSize="12" 
                               HorizontalAlignment="Center"
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <TextBlock Text="89" 
                               FontSize="24" 
                               FontWeight="Bold" 
                               HorizontalAlignment="Center"
                               Foreground="{DynamicResource SystemFillColorSuccessBrush}"/>
                    <TextBlock Text="已完成" 
                               FontSize="12" 
                               HorizontalAlignment="Center"
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                </StackPanel>
                
                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                    <TextBlock Text="67" 
                               FontSize="24" 
                               FontWeight="Bold" 
                               HorizontalAlignment="Center"
                               Foreground="{DynamicResource SystemFillColorCautionBrush}"/>
                    <TextBlock Text="进行中" 
                               FontSize="12" 
                               HorizontalAlignment="Center"
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                </StackPanel>
            </Grid>
            
            <ProgressBar Value="57" Height="8" Margin="0,0,0,8"/>
            <TextBlock Text="总体完成度: 57%" 
                       FontSize="12" 
                       HorizontalAlignment="Center"
                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
        </StackPanel>
    </ui:CardExpander>

    <!-- 操作面板的 CardExpander -->
    <TextBlock Text="操作面板的 CardExpander" FontWeight="Bold" Margin="0,0,0,8"/>
    <ui:CardExpander Header="快速操作" 
                     IsExpanded="False"
                     Margin="0,0,0,16">
        <Grid Margin="16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <Button Grid.Row="0" Grid.Column="0" Content="新建项目" Margin="0,0,4,4"/>
            <Button Grid.Row="0" Grid.Column="1" Content="导入数据" Margin="4,0,0,4"/>
            <Button Grid.Row="1" Grid.Column="0" Content="导出报告" Margin="0,4,4,4"/>
            <Button Grid.Row="1" Grid.Column="1" Content="系统设置" Margin="4,4,0,4"/>
            <Button Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2" Content="高级选项" Margin="0,4,0,0"/>
        </Grid>
    </ui:CardExpander>

    <!-- 表单类型的 CardExpander -->
    <TextBlock Text="表单类型的 CardExpander" FontWeight="Bold" Margin="0,0,0,8"/>
    <ui:CardExpander Header="用户设置" 
                     IsExpanded="False"
                     Margin="0,0,0,16">
        <StackPanel Margin="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <TextBlock Grid.Row="0" Grid.Column="0" 
                           Text="用户名:" 
                           VerticalAlignment="Center"
                           Margin="0,0,8,8"/>
                <TextBox Grid.Row="0" Grid.Column="1" 
                         Text="admin" 
                         Margin="0,0,0,8"/>
                
                <TextBlock Grid.Row="1" Grid.Column="0" 
                           Text="邮箱:" 
                           VerticalAlignment="Center"
                           Margin="0,0,8,8"/>
                <TextBox Grid.Row="1" Grid.Column="1" 
                         Text="<EMAIL>" 
                         Margin="0,0,0,8"/>
                
                <TextBlock Grid.Row="2" Grid.Column="0" 
                           Text="主题:" 
                           VerticalAlignment="Center"
                           Margin="0,0,8,8"/>
                <ComboBox Grid.Row="2" Grid.Column="1" 
                          SelectedIndex="0"
                          Margin="0,0,0,8">
                    <ComboBoxItem Content="浅色主题"/>
                    <ComboBoxItem Content="深色主题"/>
                    <ComboBoxItem Content="自动"/>
                </ComboBox>
                
                <TextBlock Grid.Row="3" Grid.Column="0" 
                           Text="语言:" 
                           VerticalAlignment="Center"
                           Margin="0,0,8,8"/>
                <ComboBox Grid.Row="3" Grid.Column="1" 
                          SelectedIndex="0"
                          Margin="0,0,0,12">
                    <ComboBoxItem Content="中文"/>
                    <ComboBoxItem Content="English"/>
                    <ComboBoxItem Content="日本語"/>
                </ComboBox>
                
                <StackPanel Grid.Row="4" Grid.Column="0" Grid.ColumnSpan="2" 
                            Orientation="Horizontal">
                    <Button Content="保存设置" Margin="0,0,8,0"/>
                    <Button Content="重置"/>
                </StackPanel>
            </Grid>
        </StackPanel>
    </ui:CardExpander>

    <!-- 列表类型的 CardExpander -->
    <TextBlock Text="列表类型的 CardExpander" FontWeight="Bold" Margin="0,0,0,8"/>
    <ui:CardExpander Header="最近文件" 
                     IsExpanded="True">
        <StackPanel Margin="16">
            <Grid Margin="0,0,0,8">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <ui:SymbolIcon Grid.Column="0" Symbol="Document24" FontSize="16" Margin="0,0,8,0"/>
                <TextBlock Grid.Column="1" Text="项目文档.docx" VerticalAlignment="Center"/>
                <TextBlock Grid.Column="2" Text="2024-01-15" FontSize="12" VerticalAlignment="Center"/>
            </Grid>
            
            <Grid Margin="0,0,0,8">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <ui:SymbolIcon Grid.Column="0" Symbol="Image24" FontSize="16" Margin="0,0,8,0"/>
                <TextBlock Grid.Column="1" Text="设计图.png" VerticalAlignment="Center"/>
                <TextBlock Grid.Column="2" Text="2024-01-14" FontSize="12" VerticalAlignment="Center"/>
            </Grid>
            
            <Grid Margin="0,0,0,8">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <ui:SymbolIcon Grid.Column="0" Symbol="Code24" FontSize="16" Margin="0,0,8,0"/>
                <TextBlock Grid.Column="1" Text="源代码.cs" VerticalAlignment="Center"/>
                <TextBlock Grid.Column="2" Text="2024-01-13" FontSize="12" VerticalAlignment="Center"/>
            </Grid>
            
            <Button Content="查看全部" HorizontalAlignment="Left" Margin="0,8,0,0"/>
        </StackPanel>
    </ui:CardExpander>

</StackPanel>
