using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using Zylo.WPF.Enums;

namespace Zylo.WPF.Controls.Icon;

/// <summary>
/// Zylo图标控件 - 类似WPF-UI的SymbolIcon
/// 使用iconfont.ttf字体文件显示自定义图标
/// </summary>
public class ZyloIcon : Control
{
    static ZyloIcon()
    {
        DefaultStyleKeyProperty.OverrideMetadata(typeof(ZyloIcon), new FrameworkPropertyMetadata(typeof(ZyloIcon)));
    }

    #region Symbol 依赖属性

    /// <summary>
    /// 图标符号枚举
    /// </summary>
    public static readonly DependencyProperty SymbolProperty =
        DependencyProperty.Register(
            nameof(Symbol),
            typeof(ZyloSymbol),
            typeof(ZyloIcon),
            new PropertyMetadata(ZyloSymbol.None, OnSymbolChanged));

    /// <summary>
    /// 图标符号
    /// </summary>
    public ZyloSymbol Symbol
    {
        get => (ZyloSymbol)GetValue(SymbolProperty);
        set => SetValue(SymbolProperty, value);
    }

    private static void OnSymbolChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is ZyloIcon icon)
        {
            icon.UpdateGlyph();
        }
    }

    #endregion

    #region Glyph 只读依赖属性

    /// <summary>
    /// 字形字符串（只读）
    /// </summary>
    private static readonly DependencyPropertyKey GlyphPropertyKey =
        DependencyProperty.RegisterReadOnly(
            nameof(Glyph),
            typeof(string),
            typeof(ZyloIcon),
            new PropertyMetadata(string.Empty));

    /// <summary>
    /// 字形字符串依赖属性
    /// </summary>
    public static readonly DependencyProperty GlyphProperty = GlyphPropertyKey.DependencyProperty;

    /// <summary>
    /// 字形字符串
    /// </summary>
    public string Glyph
    {
        get => (string)GetValue(GlyphProperty);
        private set => SetValue(GlyphPropertyKey, value);
    }

    #endregion

    #region FontFamily 依赖属性

    /// <summary>
    /// 字体族依赖属性
    /// </summary>
    public static readonly DependencyProperty FontFamilyProperty =
        TextElement.FontFamilyProperty.AddOwner(
            typeof(ZyloIcon),
            new FrameworkPropertyMetadata(
                new FontFamily("/Zylo.WPF;component/Assets/Font/#iconfont"),
                FrameworkPropertyMetadataOptions.Inherits));

    /// <summary>
    /// 字体族
    /// </summary>
    public FontFamily FontFamily
    {
        get => (FontFamily)GetValue(FontFamilyProperty);
        set => SetValue(FontFamilyProperty, value);
    }

    #endregion

    #region FontSize 依赖属性

    /// <summary>
    /// 字体大小依赖属性
    /// </summary>
    public static readonly DependencyProperty FontSizeProperty =
        TextElement.FontSizeProperty.AddOwner(
            typeof(ZyloIcon),
            new FrameworkPropertyMetadata(
                20.0,
                FrameworkPropertyMetadataOptions.Inherits));

    /// <summary>
    /// 字体大小
    /// </summary>
    public double FontSize
    {
        get => (double)GetValue(FontSizeProperty);
        set => SetValue(FontSizeProperty, value);
    }

    #endregion

    #region Foreground 依赖属性

    /// <summary>
    /// 前景色依赖属性
    /// </summary>
    public static readonly DependencyProperty ForegroundProperty =
        TextElement.ForegroundProperty.AddOwner(
            typeof(ZyloIcon),
            new FrameworkPropertyMetadata(
                SystemColors.ControlTextBrush,
                FrameworkPropertyMetadataOptions.Inherits));

    /// <summary>
    /// 前景色
    /// </summary>
    public Brush Foreground
    {
        get => (Brush)GetValue(ForegroundProperty);
        set => SetValue(ForegroundProperty, value);
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 更新字形
    /// </summary>
    private void UpdateGlyph()
    {
        if (Symbol == ZyloSymbol.None)
        {
            Glyph = string.Empty;
        }
        else
        {
            // 将枚举值转换为实际的Unicode字符
            var unicodeValue = (int)Symbol;
            var unicodeChar = char.ConvertFromUtf32(unicodeValue);
            Glyph = unicodeChar;
        }
    }

    #endregion

    #region 重写方法

    /// <summary>
    /// 应用模板时调用
    /// </summary>
    public override void OnApplyTemplate()
    {
        base.OnApplyTemplate();
        UpdateGlyph();
    }

    #endregion
}
