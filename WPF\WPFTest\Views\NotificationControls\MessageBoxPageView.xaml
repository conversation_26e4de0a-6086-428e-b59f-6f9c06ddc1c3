<UserControl
    d:DataContext="{d:DesignInstance Type=vm:MessageBoxPageViewModel,
                                     IsDesignTimeCreatable=False}"
    mc:Ignorable="d"
    x:Class="WPFTest.Views.NotificationControls.MessageBoxPageView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:codeExample="clr-namespace:Zylo.WPF.Controls.CodeExample;assembly=Zylo.WPF"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:WPFTest.Views.NotificationControls"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:vm="clr-namespace:WPFTest.ViewModels.NotificationControls"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <Grid Margin="18">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!--  页面标题  -->
        <TextBlock
            FontSize="20"
            FontWeight="Medium"
            Grid.Row="0"
            Text="MessageBox 示例" />

        <ScrollViewer Grid.Row="1" Margin="0,12,0,0">
            <StackPanel Margin="24">
                <!--  基础MessageBox示例  -->
                <ui:Card>
                    <StackPanel>
                        <!--  标题区域  -->
                        <StackPanel Margin="16,16,16,8" Orientation="Horizontal">
                            <ui:SymbolIcon
                                FontSize="20"
                                Margin="0,0,8,0"
                                Symbol="Chat24" />
                            <StackPanel>
                                <TextBlock
                                    FontSize="16"
                                    FontWeight="Medium"
                                    Text="基础 MessageBox 示例" />
                                <TextBlock
                                    FontSize="12"
                                    Foreground="{ui:ThemeResource TextFillColorSecondaryBrush}"
                                    Text="展示不同类型的 MessageBox 对话框" />
                            </StackPanel>
                        </StackPanel>

                        <StackPanel Margin="16,8,16,16">
                            <WrapPanel Margin="0,0,0,8" Orientation="Horizontal">
                                <ui:Button
                                    Appearance="Info"
                                    Command="{Binding ShowInfoMessageBoxCommand}"
                                    Content="信息 MessageBox"
                                    Icon="{ui:SymbolIcon Info24}"
                                    Margin="0,0,8,8" />

                                <ui:Button
                                    Appearance="Primary"
                                    Command="{Binding ShowConfirmMessageBoxCommand}"
                                    Content="确认 MessageBox"
                                    Icon="{ui:SymbolIcon CheckmarkCircle24}"
                                    Margin="0,0,8,8" />

                                <ui:Button
                                    Appearance="Caution"
                                    Command="{Binding ShowWarningMessageBoxCommand}"
                                    Content="警告 MessageBox"
                                    Icon="{ui:SymbolIcon Warning24}"
                                    Margin="0,0,8,8" />

                                <ui:Button
                                    Appearance="Danger"
                                    Command="{Binding ShowErrorMessageBoxCommand}"
                                    Content="错误 MessageBox"
                                    Icon="{ui:SymbolIcon ErrorCircle24}"
                                    Margin="0,0,8,8" />

                                <ui:Button
                                    Appearance="Success"
                                    Command="{Binding ShowSuccessMessageBoxCommand}"
                                    Content="成功 MessageBox"
                                    Icon="{ui:SymbolIcon CheckmarkCircle24}"
                                    Margin="0,0,8,8" />

                                <ui:Button
                                    Appearance="Secondary"
                                    Command="{Binding ShowLongContentMessageBoxCommand}"
                                    Content="长内容 MessageBox"
                                    Icon="{ui:SymbolIcon Document24}"
                                    Margin="0,0,8,8" />

                                <ui:Button
                                    Appearance="Transparent"
                                    Command="{Binding ShowMessageWithGlobalMessageCommand}"
                                    Content="消息传递 MessageBox"
                                    Icon="{ui:SymbolIcon Send24}"
                                    Margin="0,0,8,8" />
                            </WrapPanel>

                            <!--  操作结果显示  -->
                            <Border
                                Background="{ui:ThemeResource ControlFillColorDefaultBrush}"
                                CornerRadius="4"
                                Padding="12">
                                <StackPanel Margin="8">
                                    <TextBlock FontWeight="Medium" Text="操作结果:" />
                                    <TextBlock Foreground="{Binding LastResultColor}" Text="{Binding LastResult}" />
                                    <TextBlock Margin="0,4,0,0" Text="{Binding OperationStatus}" />
                                    <TextBlock
                                        Foreground="{ui:ThemeResource TextFillColorSecondaryBrush}"
                                        Margin="0,4,0,0"
                                        Text="{Binding LastReceivedMessage, StringFormat=最近接收的消息: {0}}" />
                                    <TextBlock
                                        Foreground="{ui:ThemeResource TextFillColorTertiaryBrush}"
                                        Margin="0,4,0,0"
                                        Text="{Binding InteractionCount, StringFormat=交互次数: {0}}" />
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </StackPanel>
                </ui:Card>

                <!--  自定义MessageBox  -->
                <ui:Card Margin="0,24,0,0">
                    <StackPanel>
                        <!--  标题区域  -->
                        <StackPanel Margin="16,16,16,8" Orientation="Horizontal">
                            <ui:SymbolIcon
                                FontSize="20"
                                Margin="0,0,8,0"
                                Symbol="DesignIdeas24" />
                            <StackPanel>
                                <TextBlock
                                    FontSize="16"
                                    FontWeight="Medium"
                                    Text="自定义 MessageBox" />
                                <TextBlock
                                    FontSize="12"
                                    Foreground="{ui:ThemeResource TextFillColorSecondaryBrush}"
                                    Text="自定义 MessageBox 的各种属性" />
                            </StackPanel>
                        </StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <!--  左侧配置面板  -->
                            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                <ui:TextBox
                                    Icon="{ui:SymbolIcon TextHanging20}"
                                    PlaceholderText="MessageBox 标题"
                                    Text="{Binding MessageBoxTitle, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />

                                <ui:TextBox
                                    AcceptsReturn="True"
                                    Height="80"
                                    Icon="{ui:SymbolIcon TextParagraph24}"
                                    PlaceholderText="MessageBox 内容"
                                    Text="{Binding MessageBoxContent, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                    TextWrapping="Wrap" />

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>

                                    <ui:TextBox
                                        Grid.Column="0"
                                        Margin="0,0,4,0"
                                        PlaceholderText="主按钮文本"
                                        Text="{Binding PrimaryButtonText, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />

                                    <ui:TextBox
                                        Grid.Column="1"
                                        Margin="4,0,4,0"
                                        PlaceholderText="次按钮文本"
                                        Text="{Binding SecondaryButtonText, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />

                                    <ui:TextBox
                                        Grid.Column="2"
                                        Margin="4,0,0,0"
                                        PlaceholderText="关闭按钮文本"
                                        Text="{Binding CloseButtonText, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                                </Grid>
                            </StackPanel>

                            <!--  右侧配置面板  -->
                            <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>

                                    <ui:ToggleSwitch
                                        Content="显示标题"
                                        Grid.Column="0"
                                        IsChecked="{Binding ShowTitle, Mode=TwoWay}" />

                                    <ui:ToggleSwitch
                                        Content="主按钮启用"
                                        Grid.Column="1"
                                        IsChecked="{Binding IsPrimaryButtonEnabled, Mode=TwoWay}" />
                                </Grid>

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>

                                    <ui:ToggleSwitch
                                        Content="次按钮启用"
                                        Grid.Column="0"
                                        IsChecked="{Binding IsSecondaryButtonEnabled, Mode=TwoWay}" />

                                    <ui:ToggleSwitch
                                        Content="关闭按钮启用"
                                        Grid.Column="1"
                                        IsChecked="{Binding IsCloseButtonEnabled, Mode=TwoWay}" />
                                </Grid>

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>

                                    <ComboBox
                                        Grid.Column="0"
                                        Margin="0,0,4,0"
                                        SelectedItem="{Binding PrimaryButtonAppearance, Mode=TwoWay}">
                                        <ui:ControlAppearance>Primary</ui:ControlAppearance>
                                        <ui:ControlAppearance>Secondary</ui:ControlAppearance>
                                        <ui:ControlAppearance>Danger</ui:ControlAppearance>
                                        <ui:ControlAppearance>Success</ui:ControlAppearance>
                                        <ui:ControlAppearance>Caution</ui:ControlAppearance>
                                        <ui:ControlAppearance>Info</ui:ControlAppearance>
                                        <ui:ControlAppearance>Light</ui:ControlAppearance>
                                        <ui:ControlAppearance>Dark</ui:ControlAppearance>
                                        <ui:ControlAppearance>Transparent</ui:ControlAppearance>
                                    </ComboBox>

                                    <ComboBox
                                        Grid.Column="1"
                                        Margin="4,0,4,0"
                                        SelectedItem="{Binding SecondaryButtonAppearance, Mode=TwoWay}">
                                        <ui:ControlAppearance>Primary</ui:ControlAppearance>
                                        <ui:ControlAppearance>Secondary</ui:ControlAppearance>
                                        <ui:ControlAppearance>Danger</ui:ControlAppearance>
                                        <ui:ControlAppearance>Success</ui:ControlAppearance>
                                        <ui:ControlAppearance>Caution</ui:ControlAppearance>
                                        <ui:ControlAppearance>Info</ui:ControlAppearance>
                                        <ui:ControlAppearance>Light</ui:ControlAppearance>
                                        <ui:ControlAppearance>Dark</ui:ControlAppearance>
                                        <ui:ControlAppearance>Transparent</ui:ControlAppearance>
                                    </ComboBox>

                                    <ComboBox
                                        Grid.Column="2"
                                        Margin="4,0,0,0"
                                        SelectedItem="{Binding CloseButtonAppearance, Mode=TwoWay}">
                                        <ui:ControlAppearance>Primary</ui:ControlAppearance>
                                        <ui:ControlAppearance>Secondary</ui:ControlAppearance>
                                        <ui:ControlAppearance>Danger</ui:ControlAppearance>
                                        <ui:ControlAppearance>Success</ui:ControlAppearance>
                                        <ui:ControlAppearance>Caution</ui:ControlAppearance>
                                        <ui:ControlAppearance>Info</ui:ControlAppearance>
                                        <ui:ControlAppearance>Light</ui:ControlAppearance>
                                        <ui:ControlAppearance>Dark</ui:ControlAppearance>
                                        <ui:ControlAppearance>Transparent</ui:ControlAppearance>
                                    </ComboBox>
                                </Grid>

                                <ui:Button
                                    Appearance="Primary"
                                    Command="{Binding ShowCustomMessageBoxCommand}"
                                    Content="显示自定义 MessageBox"
                                    HorizontalAlignment="Stretch"
                                    Icon="{ui:SymbolIcon DesignIdeas24}" />
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </ui:Card>

                <!--  代码示例  -->
                <codeExample:CodeExampleControl
                    CSharpCode="{Binding BasicCSharpExample}"
                    Description="基础的 MessageBox 用法示例，展示如何创建和显示一个简单的 MessageBox"
                    Title="MessageBox 基础用法"
                    XamlCode="{Binding BasicXamlExample}" />

                <codeExample:CodeExampleControl
                    CSharpCode="{Binding AdvancedCSharpExample}"
                    Description="高级的 MessageBox 配置示例，展示如何自定义 MessageBox 的各种属性"
                    Title="MessageBox 高级配置"
                    XamlCode="{Binding AdvancedXamlExample}" />

                <codeExample:CodeExampleControl
                    CSharpCode="{Binding CustomCSharpExample}"
                    Description="展示 MessageBox 如何与 CommunityToolkit.Mvvm 消息传递机制集成"
                    Title="MessageBox 与 MVVM 集成"
                    XamlCode="{Binding CustomXamlExample}" />
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
