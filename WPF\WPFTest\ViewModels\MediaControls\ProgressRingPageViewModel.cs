using System.Collections.ObjectModel;
using System.Reflection;
using System.Windows.Threading;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels.MediaControls
{
    /// <summary>
    /// ProgressRing 控件页面的 ViewModel，演示各种进度环功能
    /// 使用 CommunityToolkit.MVVM 8.4.0 的 Partial Properties 语法
    /// </summary>
    public partial class ProgressRingPageViewModel : ObservableObject
    {
        private readonly YLoggerInstance _logger = YLogger.For<ProgressRingPageViewModel>();
        private readonly DispatcherTimer _progressTimer;
        private readonly DispatcherTimer _simulationTimer;

        #region 基础属性 - 使用 Partial Properties 语法

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        public partial string StatusMessage { get; set; } = "欢迎使用 ProgressRing 控件示例！";

        /// <summary>
        /// 是否为不确定进度模式
        /// </summary>
        [ObservableProperty]
        public partial bool IsIndeterminate { get; set; } = true;

        /// <summary>
        /// 模拟进度值 (0-100，仅用于显示文本)
        /// </summary>
        [ObservableProperty]
        public partial double ProgressValue { get; set; } = 0;

        /// <summary>
        /// 最大进度值 (用于计算百分比显示)
        /// </summary>
        [ObservableProperty]
        public partial double MaximumValue { get; set; } = 100;

        /// <summary>
        /// 进度环大小
        /// </summary>
        [ObservableProperty]
        public partial double RingSize { get; set; } = 48;

        /// <summary>
        /// 进度环厚度
        /// </summary>
        [ObservableProperty]
        public partial double StrokeThickness { get; set; } = 4;

        /// <summary>
        /// 是否显示进度文本
        /// </summary>
        [ObservableProperty]
        public partial bool ShowProgressText { get; set; } = true;

        /// <summary>
        /// 进度文本格式
        /// </summary>
        [ObservableProperty]
        public partial string ProgressText { get; set; } = "0%";

        /// <summary>
        /// 是否正在运行进度模拟
        /// </summary>
        [ObservableProperty]
        public partial bool IsSimulationRunning { get; set; } = false;

        /// <summary>
        /// 模拟速度 (毫秒)
        /// </summary>
        [ObservableProperty]
        public partial int SimulationSpeed { get; set; } = 100;

        #endregion

        #region 样式属性

        /// <summary>
        /// 当前选择的大小预设
        /// </summary>
        [ObservableProperty]
        public partial string SelectedSizePreset { get; set; } = "中等";

        /// <summary>
        /// 当前选择的厚度预设
        /// </summary>
        [ObservableProperty]
        public partial string SelectedThicknessPreset { get; set; } = "标准";

        #endregion

        #region 代码示例属性

        /// <summary>
        /// 基础 ProgressRing XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicProgressRingXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 基础 ProgressRing C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicProgressRingCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级 ProgressRing XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedProgressRingXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级 ProgressRing C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedProgressRingCSharpExample { get; set; } = string.Empty;

        #endregion

        #region 集合属性

        /// <summary>
        /// 大小预设选项
        /// </summary>
        public ObservableCollection<string> SizePresets { get; } = new()
        {
            "小", "中等", "大", "超大", "自定义"
        };

        /// <summary>
        /// 厚度预设选项
        /// </summary>
        public ObservableCollection<string> ThicknessPresets { get; } = new()
        {
            "细", "标准", "粗", "超粗", "自定义"
        };

        /// <summary>
        /// 速度预设选项
        /// </summary>
        public ObservableCollection<string> SpeedPresets { get; } = new()
        {
            "慢速", "标准", "快速", "极速"
        };

        #endregion

        #region 构造函数

        public ProgressRingPageViewModel()
        {
            _logger.Info("🎯 ProgressRingPageViewModel 初始化开始");

            // 初始化定时器
            _progressTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(SimulationSpeed)
            };
            _progressTimer.Tick += ProgressTimer_Tick;

            _simulationTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            _simulationTimer.Tick += SimulationTimer_Tick;

            // 初始化代码示例
            InitializeCodeExamples();

            // 监听属性变化
            PropertyChanged += OnPropertyChanged;

            _logger.Info("✅ ProgressRingPageViewModel 初始化完成");
        }

        #endregion

        #region 属性变化处理

        private void OnPropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            switch (e.PropertyName)
            {
                case nameof(ProgressValue):
                    UpdateProgressText();
                    break;
                case nameof(SelectedSizePreset):
                    ApplySizePreset();
                    break;
                case nameof(SelectedThicknessPreset):
                    ApplyThicknessPreset();
                    break;
                case nameof(SimulationSpeed):
                    UpdateTimerInterval();
                    break;
                case nameof(IsIndeterminate):
                    if (IsIndeterminate)
                    {
                        StatusMessage = "切换到不确定进度模式";
                    }
                    else
                    {
                        StatusMessage = "切换到确定进度模式";
                        UpdateProgressText();
                    }
                    break;
            }
        }

        #endregion

        #region 命令

        /// <summary>
        /// 开始进度模拟命令
        /// </summary>
        [RelayCommand]
        private void StartSimulation()
        {
            if (IsSimulationRunning) return;

            IsSimulationRunning = true;
            IsIndeterminate = false;
            ProgressValue = 0;
            _progressTimer.Start();
            StatusMessage = "进度模拟已开始";
            _logger.Info("进度模拟开始");
        }

        /// <summary>
        /// 停止进度模拟命令
        /// </summary>
        [RelayCommand]
        private void StopSimulation()
        {
            if (!IsSimulationRunning) return;

            IsSimulationRunning = false;
            _progressTimer.Stop();
            StatusMessage = "进度模拟已停止";
            _logger.Info("进度模拟停止");
        }

        /// <summary>
        /// 重置进度命令
        /// </summary>
        [RelayCommand]
        private void ResetProgress()
        {
            StopSimulation();
            ProgressValue = 0;
            IsIndeterminate = true;
            StatusMessage = "进度已重置";
            _logger.Info("进度已重置");
        }

        /// <summary>
        /// 设置进度值命令
        /// </summary>
        [RelayCommand]
        private void SetProgress(string? value)
        {
            if (string.IsNullOrEmpty(value) || !double.TryParse(value, out double progress))
                return;

            IsIndeterminate = false;
            ProgressValue = Math.Max(0, Math.Min(100, progress));
            StatusMessage = $"进度设置为 {ProgressValue:F0}%";
            _logger.Info($"手动设置进度: {ProgressValue}%");
        }

        /// <summary>
        /// 切换模式命令
        /// </summary>
        [RelayCommand]
        private void ToggleMode()
        {
            IsIndeterminate = !IsIndeterminate;
            if (IsIndeterminate)
            {
                StopSimulation();
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 进度定时器事件
        /// </summary>
        private void ProgressTimer_Tick(object? sender, EventArgs e)
        {
            if (ProgressValue >= MaximumValue)
            {
                StopSimulation();
                StatusMessage = "进度模拟完成！";
                UpdateProgressText();
                return;
            }

            ProgressValue += 1;
            UpdateProgressText();
            StatusMessage = $"进度模拟中... {ProgressValue:F0}%";
        }

        /// <summary>
        /// 模拟定时器事件
        /// </summary>
        private void SimulationTimer_Tick(object? sender, EventArgs e)
        {
            if (ProgressValue >= 100)
            {
                StopSimulation();
                StatusMessage = "进度模拟完成！";
                UpdateProgressText();
                return;
            }

            ProgressValue += 1;
            UpdateProgressText();
            StatusMessage = $"进度模拟中... {ProgressValue:F0}%";
        }

        /// <summary>
        /// 更新进度文本
        /// </summary>
        private void UpdateProgressText()
        {
            if (IsIndeterminate)
            {
                ProgressText = "加载中...";
            }
            else
            {
                ProgressText = $"{ProgressValue:F0}%";
            }
        }

        /// <summary>
        /// 应用大小预设
        /// </summary>
        private void ApplySizePreset()
        {
            RingSize = SelectedSizePreset switch
            {
                "小" => 24,
                "中等" => 48,
                "大" => 72,
                "超大" => 96,
                _ => RingSize
            };

            if (SelectedSizePreset != "自定义")
            {
                StatusMessage = $"大小已设置为: {SelectedSizePreset} ({RingSize}px)";
            }
        }

        /// <summary>
        /// 应用厚度预设
        /// </summary>
        private void ApplyThicknessPreset()
        {
            StrokeThickness = SelectedThicknessPreset switch
            {
                "细" => 2,
                "标准" => 4,
                "粗" => 6,
                "超粗" => 8,
                _ => StrokeThickness
            };

            if (SelectedThicknessPreset != "自定义")
            {
                StatusMessage = $"厚度已设置为: {SelectedThicknessPreset} ({StrokeThickness}px)";
            }
        }

        /// <summary>
        /// 更新定时器间隔
        /// </summary>
        private void UpdateTimerInterval()
        {
            _progressTimer.Interval = TimeSpan.FromMilliseconds(SimulationSpeed);
        }

        /// <summary>
        /// 初始化代码示例
        /// </summary>
        private void InitializeCodeExamples()
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                var basePath = "WPFTest.CodeExamples.Controls.ProgressRing.";

                // 加载基础 ProgressRing 示例
                BasicProgressRingXamlExample = LoadEmbeddedResource(assembly, basePath + "BasicProgressRing.xaml.txt");
                BasicProgressRingCSharpExample = LoadEmbeddedResource(assembly, basePath + "BasicProgressRing.cs.txt");

                // 加载高级 ProgressRing 示例
                AdvancedProgressRingXamlExample = LoadEmbeddedResource(assembly, basePath + "AdvancedProgressRing.xaml.txt");
                AdvancedProgressRingCSharpExample = LoadEmbeddedResource(assembly, basePath + "AdvancedProgressRing.cs.txt");

                _logger.Info("✅ ProgressRing 代码示例加载完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 加载代码示例失败: {ex.Message}");
                // 设置默认示例
                SetDefaultCodeExamples();
            }
        }

        /// <summary>
        /// 加载嵌入资源
        /// </summary>
        private string LoadEmbeddedResource(Assembly assembly, string resourceName)
        {
            try
            {
                using var stream = assembly.GetManifestResourceStream(resourceName);
                if (stream == null)
                {
                    _logger.Warning($"⚠️ 未找到嵌入资源: {resourceName}");
                    return $"// 未找到资源: {resourceName}";
                }

                using var reader = new StreamReader(stream);
                return reader.ReadToEnd();
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 读取嵌入资源失败 {resourceName}: {ex.Message}");
                return $"// 读取资源失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 设置默认代码示例
        /// </summary>
        private void SetDefaultCodeExamples()
        {
            BasicProgressRingXamlExample = """
                <!-- 基础 ProgressRing 示例 -->
                <ui:ProgressRing IsIndeterminate="True" 
                                Width="48" 
                                Height="48" />
                """;

            BasicProgressRingCSharpExample = """
                // 基础 ProgressRing 使用
                var progressRing = new ProgressRing
                {
                    IsIndeterminate = true,
                    Width = 48,
                    Height = 48
                };
                """;

            AdvancedProgressRingXamlExample = """
                <!-- 高级 ProgressRing 示例 -->
                <ui:ProgressRing IsIndeterminate="{Binding IsIndeterminate}"
                                Value="{Binding ProgressValue}"
                                Maximum="{Binding MaximumValue}"
                                Width="{Binding RingSize}"
                                Height="{Binding RingSize}" />
                """;

            AdvancedProgressRingCSharpExample = """
                // 高级 ProgressRing 控制
                private void StartProgress()
                {
                    progressRing.IsIndeterminate = false;
                    progressRing.Value = 0;
                    progressRing.Maximum = 100;
                    
                    // 使用定时器更新进度
                    var timer = new DispatcherTimer();
                    timer.Interval = TimeSpan.FromMilliseconds(100);
                    timer.Tick += (s, e) => {
                        if (progressRing.Value >= progressRing.Maximum)
                        {
                            timer.Stop();
                            return;
                        }
                        progressRing.Value += 1;
                    };
                    timer.Start();
                }
                """;
        }

        #endregion
    }
}
