<!-- SplitButton 样式示例 -->
<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml">

    <!-- 自定义 SplitButton 样式 -->
    
    <!-- 大尺寸 SplitButton 样式 -->
    <Style x:Key="LargeSplitButtonStyle" TargetType="{x:Type ui:SplitButton}">
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="Padding" Value="20,12"/>
        <Setter Property="MinHeight" Value="48"/>
        <Setter Property="MinWidth" Value="120"/>
    </Style>

    <!-- 小尺寸 SplitButton 样式 -->
    <Style x:Key="SmallSplitButtonStyle" TargetType="{x:Type ui:SplitButton}">
        <Setter Property="FontSize" Value="11"/>
        <Setter Property="Padding" Value="8,4"/>
        <Setter Property="MinHeight" Value="24"/>
        <Setter Property="MinWidth" Value="60"/>
    </Style>

    <!-- 圆角 SplitButton 样式 -->
    <Style x:Key="RoundedSplitButtonStyle" TargetType="{x:Type ui:SplitButton}">
        <Setter Property="CornerRadius" Value="20"/>
        <Setter Property="Padding" Value="16,8"/>
    </Style>

    <!-- 工具栏 SplitButton 样式 -->
    <Style x:Key="ToolbarSplitButtonStyle" TargetType="{x:Type ui:SplitButton}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="8,6"/>
        <Setter Property="Margin" Value="2"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 彩色 SplitButton 样式 -->
    <Style x:Key="ColorfulSplitButtonStyle" TargetType="{x:Type ui:SplitButton}">
        <Setter Property="Background" Value="#FF4CAF50"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderBrush" Value="#FF388E3C"/>
        <Setter Property="CornerRadius" Value="6"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#FF66BB6A"/>
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="#FF388E3C"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 扁平化 SplitButton 样式 -->
    <Style x:Key="FlatSplitButtonStyle" TargetType="{x:Type ui:SplitButton}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
        <Setter Property="Padding" Value="12,8"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 阴影 SplitButton 样式 -->
    <Style x:Key="ShadowSplitButtonStyle" TargetType="{x:Type ui:SplitButton}">
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" 
                                  Opacity="0.3" 
                                  ShadowDepth="2" 
                                  BlurRadius="4"/>
            </Setter.Value>
        </Setter>
        <Setter Property="Margin" Value="4"/>
    </Style>

    <!-- 渐变背景 SplitButton 样式 -->
    <Style x:Key="GradientSplitButtonStyle" TargetType="{x:Type ui:SplitButton}">
        <Setter Property="Background">
            <Setter.Value>
                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                    <GradientStop Color="#FF2196F3" Offset="0"/>
                    <GradientStop Color="#FF1976D2" Offset="1"/>
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="CornerRadius" Value="4"/>
    </Style>

    <!-- 使用示例 -->
    <!--
    <StackPanel Margin="20">
        
        <TextBlock Text="SplitButton 样式示例" FontSize="18" FontWeight="Bold" Margin="0,0,0,20"/>
        
        <WrapPanel>
            <!-- 大尺寸样式 -->
            <ui:SplitButton Content="大尺寸" 
                            Style="{StaticResource LargeSplitButtonStyle}"
                            Margin="8">
                <ui:SplitButton.Flyout>
                    <ContextMenu>
                        <MenuItem Header="选项 1"/>
                        <MenuItem Header="选项 2"/>
                    </ContextMenu>
                </ui:SplitButton.Flyout>
            </ui:SplitButton>

            <!-- 小尺寸样式 -->
            <ui:SplitButton Content="小尺寸" 
                            Style="{StaticResource SmallSplitButtonStyle}"
                            Margin="8">
                <ui:SplitButton.Flyout>
                    <ContextMenu>
                        <MenuItem Header="选项 1"/>
                        <MenuItem Header="选项 2"/>
                    </ContextMenu>
                </ui:SplitButton.Flyout>
            </ui:SplitButton>

            <!-- 圆角样式 -->
            <ui:SplitButton Content="圆角" 
                            Style="{StaticResource RoundedSplitButtonStyle}"
                            Margin="8">
                <ui:SplitButton.Flyout>
                    <ContextMenu>
                        <MenuItem Header="选项 1"/>
                        <MenuItem Header="选项 2"/>
                    </ContextMenu>
                </ui:SplitButton.Flyout>
            </ui:SplitButton>

            <!-- 彩色样式 -->
            <ui:SplitButton Content="彩色" 
                            Style="{StaticResource ColorfulSplitButtonStyle}"
                            Margin="8">
                <ui:SplitButton.Flyout>
                    <ContextMenu>
                        <MenuItem Header="选项 1"/>
                        <MenuItem Header="选项 2"/>
                    </ContextMenu>
                </ui:SplitButton.Flyout>
            </ui:SplitButton>

            <!-- 扁平化样式 -->
            <ui:SplitButton Content="扁平化" 
                            Style="{StaticResource FlatSplitButtonStyle}"
                            Margin="8">
                <ui:SplitButton.Flyout>
                    <ContextMenu>
                        <MenuItem Header="选项 1"/>
                        <MenuItem Header="选项 2"/>
                    </ContextMenu>
                </ui:SplitButton.Flyout>
            </ui:SplitButton>

            <!-- 阴影样式 -->
            <ui:SplitButton Content="阴影" 
                            Style="{StaticResource ShadowSplitButtonStyle}"
                            Margin="8">
                <ui:SplitButton.Flyout>
                    <ContextMenu>
                        <MenuItem Header="选项 1"/>
                        <MenuItem Header="选项 2"/>
                    </ContextMenu>
                </ui:SplitButton.Flyout>
            </ui:SplitButton>

            <!-- 渐变样式 -->
            <ui:SplitButton Content="渐变" 
                            Style="{StaticResource GradientSplitButtonStyle}"
                            Margin="8">
                <ui:SplitButton.Flyout>
                    <ContextMenu>
                        <MenuItem Header="选项 1"/>
                        <MenuItem Header="选项 2"/>
                    </ContextMenu>
                </ui:SplitButton.Flyout>
            </ui:SplitButton>
        </WrapPanel>

        <!-- 工具栏示例 -->
        <GroupBox Header="工具栏样式示例" Margin="0,20,0,0" Padding="10">
            <StackPanel Orientation="Horizontal">
                <ui:SplitButton Content="文件" 
                                Style="{StaticResource ToolbarSplitButtonStyle}">
                    <ui:SplitButton.Flyout>
                        <ContextMenu>
                            <MenuItem Header="新建"/>
                            <MenuItem Header="打开"/>
                            <MenuItem Header="保存"/>
                        </ContextMenu>
                    </ui:SplitButton.Flyout>
                </ui:SplitButton>

                <ui:SplitButton Content="编辑" 
                                Style="{StaticResource ToolbarSplitButtonStyle}">
                    <ui:SplitButton.Flyout>
                        <ContextMenu>
                            <MenuItem Header="撤销"/>
                            <MenuItem Header="重做"/>
                            <MenuItem Header="复制"/>
                        </ContextMenu>
                    </ui:SplitButton.Flyout>
                </ui:SplitButton>

                <ui:SplitButton Content="视图" 
                                Style="{StaticResource ToolbarSplitButtonStyle}">
                    <ui:SplitButton.Flyout>
                        <ContextMenu>
                            <MenuItem Header="普通视图"/>
                            <MenuItem Header="大纲视图"/>
                            <MenuItem Header="全屏"/>
                        </ContextMenu>
                    </ui:SplitButton.Flyout>
                </ui:SplitButton>
            </StackPanel>
        </GroupBox>
        
    </StackPanel>
    -->

</ResourceDictionary>
