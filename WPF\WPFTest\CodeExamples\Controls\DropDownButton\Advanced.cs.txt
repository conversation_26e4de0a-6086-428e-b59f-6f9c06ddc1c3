// DropDownButton C# 高级用法示例
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;

namespace WPFTest.ViewModels.ButtonControls
{
    public partial class DropDownButtonPageViewModel : ObservableObject
    {
        /// <summary>
        /// 最近文件列表（动态菜单示例）
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<RecentFileItem> recentFiles = new();

        /// <summary>
        /// 当前选择的工具
        /// </summary>
        [ObservableProperty]
        private string selectedTool = "无";

        /// <summary>
        /// 导出格式选项
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<string> exportFormats = new()
        {
            "PDF", "Word", "Excel", "CSV", "PNG", "JPG", "SVG"
        };

        /// <summary>
        /// 初始化高级功能
        /// </summary>
        private void InitializeAdvancedFeatures()
        {
            // 初始化最近文件列表
            RecentFiles = new ObservableCollection<RecentFileItem>
            {
                new RecentFileItem { Name = "项目文档.docx", Path = @"C:\Documents\项目文档.docx" },
                new RecentFileItem { Name = "数据分析.xlsx", Path = @"C:\Documents\数据分析.xlsx" },
                new RecentFileItem { Name = "设计稿.psd", Path = @"C:\Documents\设计稿.psd" },
                new RecentFileItem { Name = "会议记录.txt", Path = @"C:\Documents\会议记录.txt" }
            };
        }

        /// <summary>
        /// 处理分离式操作（主按钮和菜单项不同操作）
        /// </summary>
        [RelayCommand]
        private void HandleSeparateAction(string action)
        {
            var message = action switch
            {
                // 主按钮操作（执行默认操作）
                "格式化" => "🎨 执行了默认格式化操作",
                "视图" => "👁️ 切换到默认视图",
                "工具" => "🔧 打开了默认工具",
                "导出" => "📤 执行了快速导出",
                
                // 格式化子菜单
                "加粗" => "🔤 应用了加粗格式",
                "斜体" => "🔤 应用了斜体格式",
                "下划线" => "🔤 应用了下划线格式",
                "左对齐" => "📐 设置为左对齐",
                "居中" => "📐 设置为居中对齐",
                "右对齐" => "📐 设置为右对齐",
                
                // 视图子菜单
                "列表视图" => "📋 切换到列表视图",
                "网格视图" => "⊞ 切换到网格视图",
                "详细视图" => "📊 切换到详细视图",
                
                // 工具子菜单
                "调试器" => "🐛 启动了调试器",
                "性能分析" => "📈 启动了性能分析工具",
                "代码检查" => "🔍 启动了代码检查工具",
                "UI设计器" => "🎨 启动了UI设计器",
                "资源编辑器" => "📦 启动了资源编辑器",
                "扩展管理" => "🧩 打开了扩展管理器",
                
                // 导出子菜单
                "导出PDF" => "📄 导出为 PDF 格式",
                "导出Word" => "📝 导出为 Word 格式",
                "导出Excel" => "📊 导出为 Excel 格式",
                "导出CSV" => "📋 导出为 CSV 格式",
                "导出PNG" => "🖼️ 导出为 PNG 图像",
                "导出JPG" => "🖼️ 导出为 JPG 图像",
                "导出SVG" => "🖼️ 导出为 SVG 矢量图",
                "自定义导出" => "⚙️ 打开自定义导出对话框",
                "批量导出" => "📦 启动批量导出向导",
                
                _ => $"⚡ 执行了高级操作: {action}"
            };
            
            StatusMessage = message;
            InteractionCount++;
            
            // 更新相关状态
            if (action.Contains("工具") || action.Contains("调试") || action.Contains("分析") || action.Contains("检查") || action.Contains("设计") || action.Contains("编辑") || action.Contains("扩展"))
            {
                SelectedTool = action;
            }
        }

        /// <summary>
        /// 处理动态菜单项选择
        /// </summary>
        [RelayCommand]
        private void HandleDynamicMenuItem(RecentFileItem fileItem)
        {
            StatusMessage = $"📂 打开了最近文件: {fileItem.Name}";
            InteractionCount++;
            
            // 执行打开文件的业务逻辑
            OpenRecentFile(fileItem.Path);
        }

        /// <summary>
        /// 打开最近文件
        /// </summary>
        private void OpenRecentFile(string filePath)
        {
            // 实现打开文件的逻辑
            // 例如：Process.Start(filePath);
            
            // 更新最近文件列表（将选择的文件移到顶部）
            var selectedFile = RecentFiles.FirstOrDefault(f => f.Path == filePath);
            if (selectedFile != null)
            {
                RecentFiles.Remove(selectedFile);
                RecentFiles.Insert(0, selectedFile);
            }
        }

        /// <summary>
        /// 添加新的最近文件
        /// </summary>
        [RelayCommand]
        private void AddRecentFile(string fileName)
        {
            var newFile = new RecentFileItem 
            { 
                Name = fileName, 
                Path = $@"C:\Documents\{fileName}" 
            };
            
            RecentFiles.Insert(0, newFile);
            
            // 限制最近文件数量
            while (RecentFiles.Count > 10)
            {
                RecentFiles.RemoveAt(RecentFiles.Count - 1);
            }
            
            StatusMessage = $"📁 添加了新的最近文件: {fileName}";
        }

        /// <summary>
        /// 清空最近文件列表
        /// </summary>
        [RelayCommand]
        private void ClearRecentFiles()
        {
            RecentFiles.Clear();
            StatusMessage = "🗑️ 已清空最近文件列表";
        }

        /// <summary>
        /// 执行复杂导出操作
        /// </summary>
        [RelayCommand]
        private async Task ExecuteComplexExport(string format)
        {
            StatusMessage = $"📤 正在导出为 {format} 格式...";
            
            // 模拟导出过程
            await Task.Delay(1000);
            
            StatusMessage = $"✅ 成功导出为 {format} 格式";
            InteractionCount++;
        }
    }

    /// <summary>
    /// 最近文件项模型
    /// </summary>
    public class RecentFileItem
    {
        public string Name { get; set; } = string.Empty;
        public string Path { get; set; } = string.Empty;
        public DateTime LastAccessed { get; set; } = DateTime.Now;
    }
}
