using System.Collections.ObjectModel;
using System.IO;
using WPFTest.Models.DWG;


namespace WPFTest.Services.DWG;

/// <summary>
/// DWG文件管理器 - 负责文件系统操作和文件数据管理
/// </summary>
/// <remarks>
/// 🎯 职责分离：
/// - 将文件系统操作从ViewModel中分离出来
/// - 提供统一的文件管理接口
/// - 处理文件加载、过滤、搜索等操作
/// - 管理文件缓存和性能优化
/// 
/// 🏗️ 设计模式：
/// - 单一职责原则：专注于文件管理
/// - 依赖倒置：通过接口提供服务
/// - 策略模式：支持不同的文件过滤策略
/// </remarks>
public class DwgFileManager
{
    #region 私有字段

    private readonly YLoggerInstance _logger = YLogger.ForSilent<DwgFileManager>();
    private readonly List<DwgFileModel> _fileCache = new();

    #endregion

    #region 公共方法

    /// <summary>
    /// 异步加载指定文件夹下的DWG文件
    /// </summary>
    /// <param name="folderPath">文件夹路径</param>
    /// <param name="fileTypes">文件类型集合，用于分类</param>
    /// <returns>加载的文件列表</returns>
    public async Task<List<DwgFileModel>> LoadFilesAsync(string folderPath, IEnumerable<DwgFileTypeModel> fileTypes)
    {
        return await Task.Run(() =>
        {
            try
            {
                if (!Directory.Exists(folderPath))
                {
                    _logger.Warning($"⚠️ 文件夹不存在: {folderPath}");
                    return new List<DwgFileModel>();
                }

                var files = Directory.GetFiles(folderPath, "*.dwg", SearchOption.TopDirectoryOnly);
                var fileModels = new List<DwgFileModel>();

                foreach (var filePath in files)
                {
                    var fileModel = DwgFileModel.FromFilePath(filePath);
                    fileModel.Category = AnalyzeFileCategory(fileModel.FileName, fileTypes);
                    fileModels.Add(fileModel);
                }

                // 更新缓存
                _fileCache.Clear();
                _fileCache.AddRange(fileModels);

                _logger.Info($"✅ 加载了 {fileModels.Count} 个DWG文件");
                return fileModels;
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 加载文件失败: {ex.Message}");
                return new List<DwgFileModel>();
            }
        });
    }

    /// <summary>
    /// 根据文件类型过滤文件
    /// </summary>
    /// <param name="fileType">选中的文件类型</param>
    /// <param name="allFileTypes">所有文件类型</param>
    /// <returns>过滤后的文件列表</returns>
    public IEnumerable<DwgFileModel> FilterByFileType(DwgFileTypeModel? fileType, IEnumerable<DwgFileTypeModel> allFileTypes)
    {
        try
        {
            if (fileType == null || fileType.EnglishName == "All")
            {
                return _fileCache;
            }

            if (fileType.EnglishName == "Other")
            {
                // 显示未匹配任何前缀的文件
                var allPrefixes = allFileTypes
                    .Where(ft => ft.EnglishName != "All" && ft.EnglishName != "Other")
                    .SelectMany(ft => ft.PrefixArray)
                    .Select(p => p.ToUpper())
                    .ToArray();

                return _fileCache.Where(f => 
                    !allPrefixes.Any(prefix => f.FileName.ToUpper().StartsWith(prefix)));
            }

            // 按前缀过滤
            var prefixes = fileType.PrefixArray.Select(p => p.ToUpper()).ToArray();
            if (prefixes.Length > 0)
            {
                return _fileCache.Where(f => 
                    prefixes.Any(prefix => f.FileName.ToUpper().StartsWith(prefix)));
            }

            return _fileCache;
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 文件类型过滤失败: {ex.Message}");
            return _fileCache;
        }
    }

    /// <summary>
    /// 根据关键词过滤文件
    /// </summary>
    /// <param name="files">源文件列表</param>
    /// <param name="keyword">搜索关键词</param>
    /// <returns>过滤后的文件列表</returns>
    public IEnumerable<DwgFileModel> FilterByKeyword(IEnumerable<DwgFileModel> files, string? keyword)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(keyword))
            {
                return files;
            }

            var keywordLower = keyword.ToLower();
            return files.Where(f => f.FileName.ToLower().Contains(keywordLower));
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 关键词过滤失败: {ex.Message}");
            return files;
        }
    }

    /// <summary>
    /// 获取缓存的文件列表
    /// </summary>
    /// <returns>缓存的文件列表</returns>
    public IReadOnlyList<DwgFileModel> GetCachedFiles()
    {
        return _fileCache.AsReadOnly();
    }

    /// <summary>
    /// 清空文件缓存
    /// </summary>
    public void ClearCache()
    {
        _fileCache.Clear();
        _logger.Debug("🗑️ 文件缓存已清空");
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 分析文件类别 - 基于文件名前缀
    /// </summary>
    /// <param name="fileName">文件名</param>
    /// <param name="fileTypes">文件类型集合</param>
    /// <returns>文件类别</returns>
    private string AnalyzeFileCategory(string fileName, IEnumerable<DwgFileTypeModel> fileTypes)
    {
        try
        {
            if (string.IsNullOrEmpty(fileName))
                return "其他";

            var fileNameUpper = fileName.ToUpper();

            // 遍历文件类型，查找匹配的前缀
            foreach (var fileType in fileTypes.Where(ft => ft.IsEnabled && ft.EnglishName != "All"))
            {
                foreach (var prefix in fileType.PrefixArray)
                {
                    if (!string.IsNullOrEmpty(prefix) && fileNameUpper.StartsWith(prefix.ToUpper()))
                    {
                        return fileType.ChineseName;
                    }
                }
            }

            return "其他";
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 文件类别分析失败: {fileName} - {ex.Message}");
            return "其他";
        }
    }

    #endregion
}
