using System.Collections.ObjectModel;
using System.ComponentModel;

namespace WPFTest.Models.DragDrop;

/// <summary>
/// 文件类型模型 - DWG文件类型分类模型
/// </summary>
/// <remarks>
/// 用于表示DWG文件的类型分类：
/// - 出图、施工图、模板图、底图等类型
/// - 包含类型名称、图标、前缀和文件列表
/// - 支持分隔线显示和选中状态管理
/// </remarks>
public partial class FileTypeModel : ObservableObject
{
    /// <summary>
    /// 文件类型名称（如：出图、施工图、模板图）
    /// </summary>
    [ObservableProperty]
    [Description("文件类型名称（如：出图、施工图、模板图）")]
    public partial string Name { get; set; } = string.Empty;

    /// <summary>
    /// 文件类型图标（Emoji或图标字符）
    /// </summary>
    [ObservableProperty]
    [Description("文件类型图标（Emoji或图标字符）")]
    public partial string Icon { get; set; } = string.Empty;

    /// <summary>
    /// 文件名前缀（如：S_、GS_、M_）
    /// </summary>
    [ObservableProperty]
    [Description("文件名前缀（如：S_、GS_、M_）")]
    public partial string Prefix { get; set; } = string.Empty;

    /// <summary>
    /// 该类型下的文件数量
    /// </summary>
    [ObservableProperty]
    [Description("该类型下的文件数量")]
    public partial int FileCount { get; set; }

    /// <summary>
    /// 是否被选中
    /// </summary>
    [ObservableProperty]
    [Description("是否被选中")]
    public partial bool IsSelected { get; set; }

    /// <summary>
    /// 是否为分隔线（用于UI显示分组）
    /// </summary>
    [ObservableProperty]
    [Description("是否为分隔线（用于UI显示分组）")]
    public partial bool IsSeparator { get; set; }

    /// <summary>
    /// 该类型下的文件列表
    /// </summary>
    [ObservableProperty]
    [Description("该类型下的文件列表")]
    public partial ObservableCollection<DWG.DwgFileModel> Files { get; set; } = new();

    /// <summary>
    /// 显示文本（用于UI绑定）
    /// </summary>
    public string DisplayText => IsSeparator ? Name : $"{Icon} {Name} ({FileCount})";

    /// <summary>
    /// 类型描述信息
    /// </summary>
    public string Description => IsSeparator ? "分隔线" : $"{Name}类型 - {FileCount}个文件";

    /// <summary>
    /// 是否有文件
    /// </summary>
    public bool HasFiles => FileCount > 0 && !IsSeparator;

    /// <summary>
    /// 是否可选择（分隔线不可选择）
    /// </summary>
    public bool IsSelectable => !IsSeparator;

    /// <summary>
    /// 构造函数
    /// </summary>
    public FileTypeModel()
    {
        // 监听文件集合变化，更新计算属性
        Files.CollectionChanged += (s, e) =>
        {
            FileCount = Files.Count;
            OnPropertyChanged(nameof(DisplayText));
            OnPropertyChanged(nameof(Description));
            OnPropertyChanged(nameof(HasFiles));
        };

        // 监听属性变化，更新计算属性
        PropertyChanged += (s, e) =>
        {
            switch (e.PropertyName)
            {
                case nameof(Name):
                case nameof(Icon):
                case nameof(FileCount):
                case nameof(IsSeparator):
                    OnPropertyChanged(nameof(DisplayText));
                    OnPropertyChanged(nameof(Description));
                    OnPropertyChanged(nameof(HasFiles));
                    OnPropertyChanged(nameof(IsSelectable));
                    break;
            }
        };
    }

    /// <summary>
    /// 创建普通文件类型模型的工厂方法
    /// </summary>
    /// <param name="name">类型名称</param>
    /// <param name="icon">类型图标</param>
    /// <param name="prefix">文件前缀</param>
    /// <returns>文件类型模型实例</returns>
    public static FileTypeModel Create(string name, string icon, string prefix = "")
    {
        return new FileTypeModel
        {
            Name = name,
            Icon = icon,
            Prefix = prefix,
            FileCount = 0,
            IsSelected = false,
            IsSeparator = false
        };
    }

    /// <summary>
    /// 创建分隔线模型的工厂方法
    /// </summary>
    /// <param name="separatorText">分隔线文本</param>
    /// <returns>分隔线模型实例</returns>
    public static FileTypeModel CreateSeparator(string separatorText = "------------------------")
    {
        return new FileTypeModel
        {
            Name = separatorText,
            Icon = "",
            Prefix = "",
            FileCount = 0,
            IsSelected = false,
            IsSeparator = true
        };
    }

    /// <summary>
    /// 添加文件到类型
    /// </summary>
    /// <param name="file">要添加的文件</param>
    public void AddFile(DWG.DwgFileModel file)
    {
        if (!IsSeparator && file != null)
        {
            Files.Add(file);
        }
    }

    /// <summary>
    /// 从类型中移除文件
    /// </summary>
    /// <param name="file">要移除的文件</param>
    public void RemoveFile(DWG.DwgFileModel file)
    {
        if (!IsSeparator && file != null)
        {
            Files.Remove(file);
        }
    }

    /// <summary>
    /// 清空文件列表
    /// </summary>
    public void ClearFiles()
    {
        if (!IsSeparator)
        {
            Files.Clear();
        }
    }

    /// <summary>
    /// 重置选中状态
    /// </summary>
    public void ResetSelection()
    {
        if (IsSelectable)
        {
            IsSelected = false;
        }
    }

    /// <summary>
    /// 设置为选中状态
    /// </summary>
    public void Select()
    {
        if (IsSelectable)
        {
            IsSelected = true;
        }
    }

    /// <summary>
    /// 获取文件类型枚举（用于业务逻辑）
    /// </summary>
    public DwgFileType GetFileType()
    {
        return Name switch
        {
            "出图" => DwgFileType.Drawing,
            "施工图" => DwgFileType.Construction,
            "模板图" => DwgFileType.Template,
            "底图" => DwgFileType.Base,
            "变更" => DwgFileType.Change,
            "计算书" => DwgFileType.Calculation,
            "图框" => DwgFileType.Frame,
            "建筑图" => DwgFileType.Architecture,
            "绑定" => DwgFileType.Binding,
            "全部" => DwgFileType.All,
            _ => DwgFileType.Other
        };
    }
}

/// <summary>
/// DWG文件类型枚举
/// </summary>
public enum DwgFileType
{
    /// <summary>全部</summary>
    All,
    /// <summary>出图</summary>
    Drawing,
    /// <summary>施工图</summary>
    Construction,
    /// <summary>模板图</summary>
    Template,
    /// <summary>底图</summary>
    Base,
    /// <summary>变更</summary>
    Change,
    /// <summary>计算书</summary>
    Calculation,
    /// <summary>图框</summary>
    Frame,
    /// <summary>建筑图</summary>
    Architecture,
    /// <summary>绑定</summary>
    Binding,
    /// <summary>其他</summary>
    Other
}
