<!-- Image 基础用法示例 -->
<StackPanel Orientation="Vertical" Spacing="16">
    <!-- 基础图片显示 -->
    <StackPanel>
        <TextBlock Text="基础图片显示" FontWeight="Bold" Margin="0,0,0,8"/>
        <Image Source="pack://application:,,,/Assets/Images/sample.jpg"
               Width="200" Height="150"
               Stretch="Uniform"
               HorizontalAlignment="Left"/>
    </StackPanel>

    <!-- 不同拉伸模式 -->
    <StackPanel>
        <TextBlock Text="拉伸模式对比" FontWeight="Bold" Margin="0,0,0,8"/>
        <WrapPanel>
            <!-- None -->
            <StackPanel Margin="0,0,16,0">
                <TextBlock Text="None" FontSize="12" Margin="0,0,0,4"/>
                <Border BorderBrush="Gray" BorderThickness="1" Width="100" Height="80">
                    <Image Source="pack://application:,,,/Assets/Images/sample.jpg"
                           Stretch="None"/>
                </Border>
            </StackPanel>

            <!-- Fill -->
            <StackPanel Margin="0,0,16,0">
                <TextBlock Text="Fill" FontSize="12" Margin="0,0,0,4"/>
                <Border BorderBrush="Gray" BorderThickness="1" Width="100" Height="80">
                    <Image Source="pack://application:,,,/Assets/Images/sample.jpg"
                           Stretch="Fill"/>
                </Border>
            </StackPanel>

            <!-- Uniform -->
            <StackPanel Margin="0,0,16,0">
                <TextBlock Text="Uniform" FontSize="12" Margin="0,0,0,4"/>
                <Border BorderBrush="Gray" BorderThickness="1" Width="100" Height="80">
                    <Image Source="pack://application:,,,/Assets/Images/sample.jpg"
                           Stretch="Uniform"/>
                </Border>
            </StackPanel>

            <!-- UniformToFill -->
            <StackPanel>
                <TextBlock Text="UniformToFill" FontSize="12" Margin="0,0,0,4"/>
                <Border BorderBrush="Gray" BorderThickness="1" Width="100" Height="80">
                    <Image Source="pack://application:,,,/Assets/Images/sample.jpg"
                           Stretch="UniformToFill"/>
                </Border>
            </StackPanel>
        </WrapPanel>
    </StackPanel>

    <!-- 圆形图片 -->
    <StackPanel>
        <TextBlock Text="圆形图片" FontWeight="Bold" Margin="0,0,0,8"/>
        <Border Width="100" Height="100"
                CornerRadius="50"
                Background="LightGray">
            <Border.Clip>
                <EllipseGeometry Center="50,50" RadiusX="50" RadiusY="50"/>
            </Border.Clip>
            <Image Source="pack://application:,,,/Assets/Images/avatar.jpg"
                   Stretch="UniformToFill"/>
        </Border>
    </StackPanel>

    <!-- 带边框的图片 -->
    <StackPanel>
        <TextBlock Text="带边框的图片" FontWeight="Bold" Margin="0,0,0,8"/>
        <Border BorderBrush="DodgerBlue" BorderThickness="3"
                CornerRadius="8" Padding="4">
            <Image Source="pack://application:,,,/Assets/Images/sample.jpg"
                   Width="150" Height="100"
                   Stretch="Uniform"/>
        </Border>
    </StackPanel>
</StackPanel>
