<!-- AutoSuggestBox 数据绑定示例 -->
<StackPanel Margin="20" Spacing="15">

    <!-- 基础数据绑定 -->
    <GroupBox Header="基础数据绑定" Padding="15">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="16"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左列：搜索控件 -->
            <StackPanel Grid.Column="0">
                <TextBlock Text="数据绑定搜索框" FontWeight="Medium" Margin="0,0,0,8"/>
                
                <ui:AutoSuggestBox Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                                   PlaceholderText="搜索城市..."
                                   Icon="{ui:SymbolIcon Search16}"
                                   Style="{StaticResource AutoSuggestBoxStyle}"
                                   Margin="0,0,0,8"/>
                
                <ui:AutoSuggestBox Text="{Binding LanguageSearchText, UpdateSourceTrigger=PropertyChanged}"
                                   PlaceholderText="搜索编程语言..."
                                   Icon="{ui:SymbolIcon Code16}"
                                   Style="{StaticResource AutoSuggestBoxStyle}"
                                   Margin="0,0,0,8"/>
                
                <ui:AutoSuggestBox Text="{Binding CountrySearchText, UpdateSourceTrigger=PropertyChanged}"
                                   PlaceholderText="搜索国家..."
                                   Icon="{ui:SymbolIcon Globe16}"
                                   Style="{StaticResource SmallAutoSuggestBoxStyle}"/>
            </StackPanel>

            <!-- 右列：绑定结果 -->
            <StackPanel Grid.Column="2">
                <TextBlock Text="绑定结果" FontWeight="Medium" Margin="0,0,0,8"/>
                
                <Border Background="{DynamicResource ControlFillColorTertiaryBrush}"
                        CornerRadius="4" Padding="12">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                            <TextBlock Text="城市搜索:" FontSize="12" Margin="0,0,4,0"/>
                            <TextBlock Text="{Binding SearchText}" FontWeight="Medium" FontSize="12"/>
                        </StackPanel>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                            <TextBlock Text="语言搜索:" FontSize="12" Margin="0,0,4,0"/>
                            <TextBlock Text="{Binding LanguageSearchText}" FontWeight="Medium" FontSize="12"/>
                        </StackPanel>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                            <TextBlock Text="国家搜索:" FontSize="12" Margin="0,0,4,0"/>
                            <TextBlock Text="{Binding CountrySearchText}" FontWeight="Medium" FontSize="12"/>
                        </StackPanel>
                        
                        <Separator Margin="0,4"/>
                        
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="搜索次数:" FontSize="12" Margin="0,0,4,0"/>
                            <TextBlock Text="{Binding InteractionCount}" FontWeight="Medium" FontSize="12"/>
                        </StackPanel>
                    </StackPanel>
                </Border>
            </StackPanel>
        </Grid>
    </GroupBox>

    <!-- 建议列表绑定 -->
    <GroupBox Header="建议列表绑定" Padding="15">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="16"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左列：带建议的搜索框 -->
            <StackPanel Grid.Column="0">
                <TextBlock Text="带建议列表的搜索框" FontWeight="Medium" Margin="0,0,0,8"/>
                
                <!-- 城市搜索 -->
                <StackPanel Margin="0,0,0,8">
                    <TextBlock Text="城市搜索" FontSize="12" Margin="0,0,0,2"/>
                    <ui:AutoSuggestBox Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                                       ItemsSource="{Binding CitySuggestions}"
                                       PlaceholderText="输入城市名称..."
                                       Icon="{ui:SymbolIcon Search16}"
                                       Style="{StaticResource AutoSuggestBoxStyle}"/>
                </StackPanel>
                
                <!-- 编程语言搜索 -->
                <StackPanel Margin="0,0,0,8">
                    <TextBlock Text="编程语言搜索" FontSize="12" Margin="0,0,0,2"/>
                    <ui:AutoSuggestBox Text="{Binding LanguageSearchText, UpdateSourceTrigger=PropertyChanged}"
                                       ItemsSource="{Binding LanguageSuggestions}"
                                       PlaceholderText="输入编程语言..."
                                       Icon="{ui:SymbolIcon Code16}"
                                       Style="{StaticResource AutoSuggestBoxStyle}"/>
                </StackPanel>
                
                <!-- 国家搜索 -->
                <StackPanel>
                    <TextBlock Text="国家搜索" FontSize="12" Margin="0,0,0,2"/>
                    <ui:AutoSuggestBox Text="{Binding CountrySearchText, UpdateSourceTrigger=PropertyChanged}"
                                       ItemsSource="{Binding CountrySuggestions}"
                                       PlaceholderText="输入国家名称..."
                                       Icon="{ui:SymbolIcon Globe16}"
                                       Style="{StaticResource SmallAutoSuggestBoxStyle}"/>
                </StackPanel>
            </StackPanel>

            <!-- 右列：建议列表显示 -->
            <StackPanel Grid.Column="2">
                <TextBlock Text="当前建议列表" FontWeight="Medium" Margin="0,0,0,8"/>
                
                <!-- 城市建议 -->
                <StackPanel Margin="0,0,0,8">
                    <TextBlock Text="城市建议:" FontSize="12" Margin="0,0,0,2"/>
                    <Border Background="{DynamicResource ControlFillColorTertiaryBrush}"
                            CornerRadius="4" Padding="8" MinHeight="40">
                        <ItemsControl ItemsSource="{Binding CitySuggestions}">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <WrapPanel/>
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                            CornerRadius="2" Padding="4,2" Margin="2">
                                        <TextBlock Text="{Binding}" FontSize="10"/>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </Border>
                </StackPanel>
                
                <!-- 编程语言建议 -->
                <StackPanel Margin="0,0,0,8">
                    <TextBlock Text="语言建议:" FontSize="12" Margin="0,0,0,2"/>
                    <Border Background="{DynamicResource ControlFillColorTertiaryBrush}"
                            CornerRadius="4" Padding="8" MinHeight="40">
                        <ItemsControl ItemsSource="{Binding LanguageSuggestions}">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <WrapPanel/>
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                            CornerRadius="2" Padding="4,2" Margin="2">
                                        <TextBlock Text="{Binding}" FontSize="10"/>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </Border>
                </StackPanel>
                
                <!-- 国家建议 -->
                <StackPanel>
                    <TextBlock Text="国家建议:" FontSize="12" Margin="0,0,0,2"/>
                    <Border Background="{DynamicResource ControlFillColorTertiaryBrush}"
                            CornerRadius="4" Padding="8" MinHeight="40">
                        <ItemsControl ItemsSource="{Binding CountrySuggestions}">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <WrapPanel/>
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                            CornerRadius="2" Padding="4,2" Margin="2">
                                        <TextBlock Text="{Binding}" FontSize="10"/>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </Border>
                </StackPanel>
            </StackPanel>
        </Grid>
    </GroupBox>

    <!-- 搜索统计绑定 -->
    <GroupBox Header="搜索统计绑定" Padding="15">
        <Border Background="{DynamicResource ControlFillColorTertiaryBrush}"
                CornerRadius="4" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 搜索次数 -->
                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <TextBlock Text="搜索次数" FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,4"/>
                    <TextBlock Text="{Binding InteractionCount}" 
                               FontSize="24" 
                               FontWeight="Bold" 
                               HorizontalAlignment="Center"
                               Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                </StackPanel>

                <!-- 城市搜索长度 -->
                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <TextBlock Text="城市搜索长度" FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,4"/>
                    <TextBlock Text="{Binding SearchText.Length}" 
                               FontSize="24" 
                               FontWeight="Bold" 
                               HorizontalAlignment="Center"
                               Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                </StackPanel>

                <!-- 语言搜索长度 -->
                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                    <TextBlock Text="语言搜索长度" FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,4"/>
                    <TextBlock Text="{Binding LanguageSearchText.Length}" 
                               FontSize="24" 
                               FontWeight="Bold" 
                               HorizontalAlignment="Center"
                               Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                </StackPanel>

                <!-- 国家搜索长度 -->
                <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                    <TextBlock Text="国家搜索长度" FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,4"/>
                    <TextBlock Text="{Binding CountrySearchText.Length}" 
                               FontSize="24" 
                               FontWeight="Bold" 
                               HorizontalAlignment="Center"
                               Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                </StackPanel>
            </Grid>
        </Border>
    </GroupBox>

    <!-- 数据绑定操作 -->
    <GroupBox Header="数据绑定操作" Padding="15">
        <StackPanel Orientation="Horizontal">
            <ui:Button Content="清除所有搜索"
                       Command="{Binding ClearStatusCommand}"
                       Appearance="Primary"
                       Margin="0,0,8,0"/>
            
            <ui:Button Content="重置计数"
                       Command="{Binding ResetCountCommand}"
                       Appearance="Secondary"
                       Margin="0,0,8,0"/>
            
            <ui:Button Content="刷新建议"
                       Command="{Binding HandleInteractionCommand}"
                       CommandParameter="刷新建议"
                       Appearance="Secondary"/>
        </StackPanel>
    </GroupBox>

</StackPanel>
