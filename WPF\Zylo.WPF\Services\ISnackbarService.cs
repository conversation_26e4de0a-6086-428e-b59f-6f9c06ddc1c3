using Wpf.Ui.Controls;

namespace Zylo.WPF.Services;

/// <summary>
/// Snackbar 服务接口
/// </summary>
public interface ISnackbarService
{
    /// <summary>
    /// 设置 Snackbar 控件实例
    /// </summary>
    void SetSnackbar(Controls.ZyloSnackbar snackbar);

    /// <summary>
    /// 显示成功消息
    /// </summary>
    void ShowSuccess(string message, string title = "操作成功", int timeout = 3000);

    /// <summary>
    /// 显示错误消息
    /// </summary>
    void ShowError(string message, string title = "操作失败", int timeout = 5000);

    /// <summary>
    /// 显示警告消息
    /// </summary>
    void ShowWarning(string message, string title = "警告", int timeout = 4000);

    /// <summary>
    /// 显示信息消息
    /// </summary>
    void ShowInfo(string message, string title = "提示", int timeout = 3000);

    /// <summary>
    /// 显示主要消息
    /// </summary>
    void ShowPrimary(string message, string title = "消息", int timeout = 3000);

    /// <summary>
    /// 显示自定义消息
    /// </summary>
    void Show(string title, string message, SymbolRegular icon = SymbolRegular.Info24, 
              ControlAppearance appearance = ControlAppearance.Primary, int timeout = 3000);

    /// <summary>
    /// 隐藏当前显示的 Snackbar
    /// </summary>
    void Hide();
}
