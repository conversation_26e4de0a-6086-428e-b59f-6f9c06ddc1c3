<!-- WPF-UI Flyout 基础用法示例 -->
<StackPanel Margin="20">

    <!-- 简单的 Flyout -->
    <TextBlock Text="简单的 Flyout" FontWeight="Bold" Margin="0,0,0,8"/>
    <ui:Button Content="显示简单 Flyout" Margin="0,0,0,16">
        <ui:Button.Flyout>
            <ui:Flyout>
                <StackPanel Margin="16">
                    <TextBlock Text="这是一个简单的 Flyout" 
                               FontWeight="Medium" 
                               Margin="0,0,0,8"/>
                    <TextBlock Text="Flyout 提供了轻量级的浮出面板功能。" 
                               TextWrapping="Wrap"
                               Margin="0,0,0,12"/>
                    <Button Content="关闭" HorizontalAlignment="Left"/>
                </StackPanel>
            </ui:Flyout>
        </ui:Button.Flyout>
    </ui:Button>

    <!-- 带图标的信息 Flyout -->
    <TextBlock Text="带图标的信息 Flyout" FontWeight="Bold" Margin="0,0,0,8"/>
    <ui:Button Content="显示信息 Flyout" Margin="0,0,0,16">
        <ui:Button.Flyout>
            <ui:Flyout>
                <Grid Margin="16" MinWidth="250">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <ui:SymbolIcon Grid.Column="0"
                                   Symbol="Info24" 
                                   FontSize="24" 
                                   Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                   Margin="0,0,12,0"
                                   VerticalAlignment="Top"/>
                    
                    <StackPanel Grid.Column="1">
                        <TextBlock Text="信息提示" 
                                   FontWeight="Bold" 
                                   Margin="0,0,0,8"/>
                        <TextBlock Text="这是一个包含图标的信息 Flyout，用于显示重要提示。" 
                                   TextWrapping="Wrap"
                                   Margin="0,0,0,12"/>
                        <Button Content="我知道了" HorizontalAlignment="Left"/>
                    </StackPanel>
                </Grid>
            </ui:Flyout>
        </ui:Button.Flyout>
    </ui:Button>

    <!-- 不同位置的 Flyout -->
    <TextBlock Text="不同位置的 Flyout" FontWeight="Bold" Margin="0,0,0,8"/>
    <Grid Margin="0,0,0,16">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 顶部 -->
        <ui:Button Grid.Row="0" Grid.Column="1" Content="顶部" Margin="4">
            <ui:Button.Flyout>
                <ui:Flyout Placement="Top">
                    <StackPanel Margin="12">
                        <TextBlock Text="顶部 Flyout" FontWeight="Medium"/>
                        <TextBlock Text="显示在按钮上方" FontSize="12"/>
                    </StackPanel>
                </ui:Flyout>
            </ui:Button.Flyout>
        </ui:Button>
        
        <!-- 左侧 -->
        <ui:Button Grid.Row="1" Grid.Column="0" Content="左侧" Margin="4">
            <ui:Button.Flyout>
                <ui:Flyout Placement="Left">
                    <StackPanel Margin="12">
                        <TextBlock Text="左侧 Flyout" FontWeight="Medium"/>
                        <TextBlock Text="显示在按钮左侧" FontSize="12"/>
                    </StackPanel>
                </ui:Flyout>
            </ui:Button.Flyout>
        </ui:Button>
        
        <!-- 中心 -->
        <ui:Button Grid.Row="1" Grid.Column="1" Content="中心" Margin="4">
            <ui:Button.Flyout>
                <ui:Flyout>
                    <StackPanel Margin="12">
                        <TextBlock Text="默认位置" FontWeight="Medium"/>
                        <TextBlock Text="自动选择最佳位置" FontSize="12"/>
                    </StackPanel>
                </ui:Flyout>
            </ui:Button.Flyout>
        </ui:Button>
        
        <!-- 右侧 -->
        <ui:Button Grid.Row="1" Grid.Column="2" Content="右侧" Margin="4">
            <ui:Button.Flyout>
                <ui:Flyout Placement="Right">
                    <StackPanel Margin="12">
                        <TextBlock Text="右侧 Flyout" FontWeight="Medium"/>
                        <TextBlock Text="显示在按钮右侧" FontSize="12"/>
                    </StackPanel>
                </ui:Flyout>
            </ui:Button.Flyout>
        </ui:Button>
        
        <!-- 底部 -->
        <ui:Button Grid.Row="2" Grid.Column="1" Content="底部" Margin="4">
            <ui:Button.Flyout>
                <ui:Flyout Placement="Bottom">
                    <StackPanel Margin="12">
                        <TextBlock Text="底部 Flyout" FontWeight="Medium"/>
                        <TextBlock Text="显示在按钮下方" FontSize="12"/>
                    </StackPanel>
                </ui:Flyout>
            </ui:Button.Flyout>
        </ui:Button>
    </Grid>

    <!-- 菜单类型的 Flyout -->
    <TextBlock Text="菜单类型的 Flyout" FontWeight="Bold" Margin="0,0,0,8"/>
    <ui:Button Content="操作菜单" Margin="0,0,0,16">
        <ui:Button.Flyout>
            <ui:Flyout>
                <StackPanel Margin="8" MinWidth="150">
                    <ui:Button Content="新建" 
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Appearance="Transparent"
                               Margin="0,0,0,4"/>
                    <ui:Button Content="编辑" 
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Appearance="Transparent"
                               Margin="0,0,0,4"/>
                    <ui:Button Content="删除" 
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Appearance="Transparent"
                               Margin="0,0,0,4"/>
                    <Separator Margin="0,4,0,4"/>
                    <ui:Button Content="设置" 
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Appearance="Transparent"/>
                </StackPanel>
            </ui:Flyout>
        </ui:Button.Flyout>
    </ui:Button>

    <!-- 用户菜单 Flyout -->
    <TextBlock Text="用户菜单 Flyout" FontWeight="Bold" Margin="0,0,0,8"/>
    <ui:Button Content="用户菜单" Margin="0,0,0,16">
        <ui:Button.Flyout>
            <ui:Flyout>
                <StackPanel Margin="16" MinWidth="200">
                    <!-- 用户信息 -->
                    <Grid Margin="0,0,0,12">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <Border Grid.Column="0" 
                                Background="{DynamicResource AccentFillColorDefaultBrush}"
                                Width="40" Height="40" 
                                CornerRadius="20"
                                Margin="0,0,12,0">
                            <TextBlock Text="U" 
                                       Foreground="White" 
                                       FontWeight="Bold"
                                       HorizontalAlignment="Center" 
                                       VerticalAlignment="Center"/>
                        </Border>
                        
                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                            <TextBlock Text="用户名" FontWeight="Bold"/>
                            <TextBlock Text="<EMAIL>" 
                                       FontSize="12" 
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                        </StackPanel>
                    </Grid>
                    
                    <Separator Margin="0,0,0,8"/>
                    
                    <!-- 菜单项 -->
                    <ui:Button Content="个人资料" 
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Appearance="Transparent"
                               Margin="0,0,0,4"/>
                    <ui:Button Content="账户设置" 
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Appearance="Transparent"
                               Margin="0,0,0,4"/>
                    <ui:Button Content="帮助" 
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Appearance="Transparent"
                               Margin="0,0,0,8"/>
                    
                    <Separator Margin="0,0,0,8"/>
                    
                    <ui:Button Content="退出登录" 
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Appearance="Transparent"/>
                </StackPanel>
            </ui:Flyout>
        </ui:Button.Flyout>
    </ui:Button>

    <!-- 表单类型的 Flyout -->
    <TextBlock Text="表单类型的 Flyout" FontWeight="Bold" Margin="0,0,0,8"/>
    <ui:Button Content="快速设置" Margin="0,0,0,16">
        <ui:Button.Flyout>
            <ui:Flyout>
                <StackPanel Margin="16" MinWidth="250">
                    <TextBlock Text="快速设置" 
                               FontWeight="Bold" 
                               FontSize="16"
                               Margin="0,0,0,12"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" 
                                   Text="主题:" 
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,8"/>
                        <ComboBox Grid.Row="0" Grid.Column="1" 
                                  SelectedIndex="0"
                                  Margin="0,0,0,8">
                            <ComboBoxItem Content="浅色"/>
                            <ComboBoxItem Content="深色"/>
                            <ComboBoxItem Content="自动"/>
                        </ComboBox>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" 
                                   Text="语言:" 
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,8"/>
                        <ComboBox Grid.Row="1" Grid.Column="1" 
                                  SelectedIndex="0"
                                  Margin="0,0,0,8">
                            <ComboBoxItem Content="中文"/>
                            <ComboBoxItem Content="English"/>
                        </ComboBox>
                        
                        <CheckBox Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2"
                                  Content="启用通知" 
                                  IsChecked="True"
                                  Margin="0,0,0,8"/>
                        
                        <CheckBox Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="2"
                                  Content="自动保存" 
                                  IsChecked="False"
                                  Margin="0,0,0,12"/>
                    </Grid>
                    
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                        <Button Content="取消" Margin="0,0,8,0"/>
                        <Button Content="保存"/>
                    </StackPanel>
                </StackPanel>
            </ui:Flyout>
        </ui:Button.Flyout>
    </ui:Button>

    <!-- 通知类型的 Flyout -->
    <TextBlock Text="通知类型的 Flyout" FontWeight="Bold" Margin="0,0,0,8"/>
    <ui:Button Content="显示通知" Margin="0,0,0,16">
        <ui:Button.Flyout>
            <ui:Flyout>
                <StackPanel Margin="16" MinWidth="280">
                    <Grid Margin="0,0,0,12">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <ui:SymbolIcon Grid.Column="0"
                                       Symbol="Mail24" 
                                       FontSize="20" 
                                       Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                       Margin="0,0,12,0"/>
                        
                        <StackPanel Grid.Column="1">
                            <TextBlock Text="新消息" FontWeight="Bold"/>
                            <TextBlock Text="您有 3 条未读消息" FontSize="12"/>
                        </StackPanel>
                        
                        <TextBlock Grid.Column="2" 
                                   Text="刚刚" 
                                   FontSize="10" 
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   VerticalAlignment="Top"/>
                    </Grid>
                    
                    <TextBlock Text="• 系统更新通知" Margin="0,0,0,4"/>
                    <TextBlock Text="• 用户反馈消息" Margin="0,0,0,4"/>
                    <TextBlock Text="• 安全提醒" Margin="0,0,0,12"/>
                    
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                        <Button Content="忽略" Margin="0,0,8,0"/>
                        <Button Content="查看全部"/>
                    </StackPanel>
                </StackPanel>
            </ui:Flyout>
        </ui:Button.Flyout>
    </ui:Button>

</StackPanel>
