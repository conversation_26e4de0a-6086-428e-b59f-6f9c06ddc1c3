using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Zylo.WPF.Models.Navigation;
using System.Windows;

namespace WPFTest.ViewModels.Navigation
{
    /// <summary>
    /// BreadcrumbBar 面包屑导航示例 ViewModel
    /// </summary>
    public partial class BreadcrumbBarExampleViewModel : ObservableObject
    {
        /// <summary>
        /// 面包屑项集合
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<BreadcrumbItemModel> breadcrumbItems = new();

        /// <summary>
        /// 当前路径
        /// </summary>
        [ObservableProperty]
        private string currentPath = string.Empty;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        [ObservableProperty]
        private DateTime lastUpdated = DateTime.Now;

        /// <summary>
        /// 快速导航项集合
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<QuickNavigationItem> quickNavigationItems = new();

        /// <summary>
        /// 构造函数
        /// </summary>
        public BreadcrumbBarExampleViewModel()
        {
            InitializeBreadcrumbItems();
            InitializeQuickNavigationItems();
        }

        /// <summary>
        /// 导航到首页命令
        /// </summary>
        [RelayCommand]
        private void NavigateToHome()
        {
            BreadcrumbItems.Clear();
            BreadcrumbItems.Add(new BreadcrumbItemModel
            {
                Title = "首页",
                Icon = "Home",
                Path = "/"
            });
            
            UpdateCurrentPath();
            System.Diagnostics.Debug.WriteLine("导航到首页");
        }

        /// <summary>
        /// 向上导航命令
        /// </summary>
        [RelayCommand]
        private void NavigateUp()
        {
            if (BreadcrumbItems.Count > 1)
            {
                BreadcrumbItems.RemoveAt(BreadcrumbItems.Count - 1);
                UpdateCurrentPath();
                System.Diagnostics.Debug.WriteLine("向上导航");
            }
        }

        /// <summary>
        /// 刷新路径命令
        /// </summary>
        [RelayCommand]
        private void RefreshPath()
        {
            LastUpdated = DateTime.Now;
            System.Diagnostics.Debug.WriteLine("路径已刷新");
        }

        /// <summary>
        /// 复制路径命令
        /// </summary>
        [RelayCommand]
        private void CopyPath()
        {
            try
            {
                Clipboard.SetText(CurrentPath);
                System.Diagnostics.Debug.WriteLine($"路径已复制: {CurrentPath}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"复制路径失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 快速导航命令
        /// </summary>
        [RelayCommand]
        private void QuickNavigate(QuickNavigationItem? item)
        {
            if (item == null) return;

            // 清空当前路径
            BreadcrumbItems.Clear();

            // 添加新路径
            var pathParts = item.Path.Split('/', StringSplitOptions.RemoveEmptyEntries);
            var currentPath = "";

            // 添加首页
            BreadcrumbItems.Add(new BreadcrumbItemModel
            {
                Title = "首页",
                Icon = "Home",
                Path = "/"
            });

            // 添加路径各级
            foreach (var part in pathParts)
            {
                currentPath += "/" + part;
                BreadcrumbItems.Add(new BreadcrumbItemModel
                {
                    Title = GetTitleForPath(part),
                    Icon = GetIconForPath(part),
                    Path = currentPath
                });
            }

            UpdateCurrentPath();
            System.Diagnostics.Debug.WriteLine($"快速导航到: {item.Title}");
        }

        /// <summary>
        /// 初始化面包屑项
        /// </summary>
        private void InitializeBreadcrumbItems()
        {
            BreadcrumbItems.Clear();

            var items = new[]
            {
                new BreadcrumbItemModel
                {
                    Title = "首页",
                    Icon = "Home",
                    Path = "/"
                },
                new BreadcrumbItemModel
                {
                    Title = "控件示例",
                    Icon = "Apps",
                    Path = "/controls"
                },
                new BreadcrumbItemModel
                {
                    Title = "导航控件",
                    Icon = "Navigation",
                    Path = "/controls/navigation"
                },
                new BreadcrumbItemModel
                {
                    Title = "面包屑导航",
                    Icon = "Breadcrumb",
                    Path = "/controls/navigation/breadcrumb"
                }
            };

            foreach (var item in items)
            {
                BreadcrumbItems.Add(item);
            }

            UpdateCurrentPath();
        }

        /// <summary>
        /// 初始化快速导航项
        /// </summary>
        private void InitializeQuickNavigationItems()
        {
            QuickNavigationItems.Clear();

            var items = new[]
            {
                new QuickNavigationItem { Title = "首页", Path = "/" },
                new QuickNavigationItem { Title = "控件示例", Path = "/controls" },
                new QuickNavigationItem { Title = "导航示例", Path = "/controls/navigation" },
                new QuickNavigationItem { Title = "布局示例", Path = "/controls/layout" },
                new QuickNavigationItem { Title = "主题设置", Path = "/settings/theme" },
                new QuickNavigationItem { Title = "关于", Path = "/about" }
            };

            foreach (var item in items)
            {
                QuickNavigationItems.Add(item);
            }
        }

        /// <summary>
        /// 更新当前路径
        /// </summary>
        private void UpdateCurrentPath()
        {
            if (BreadcrumbItems.Any())
            {
                CurrentPath = BreadcrumbItems.Last().Path;
            }
            else
            {
                CurrentPath = "/";
            }

            LastUpdated = DateTime.Now;
        }

        /// <summary>
        /// 根据路径获取标题
        /// </summary>
        private string GetTitleForPath(string pathPart)
        {
            return pathPart switch
            {
                "controls" => "控件示例",
                "navigation" => "导航控件",
                "breadcrumb" => "面包屑导航",
                "layout" => "布局控件",
                "settings" => "设置",
                "theme" => "主题",
                "about" => "关于",
                _ => pathPart
            };
        }

        /// <summary>
        /// 根据路径获取图标
        /// </summary>
        private string GetIconForPath(string pathPart)
        {
            return pathPart switch
            {
                "controls" => "Apps",
                "navigation" => "Navigation",
                "breadcrumb" => "Breadcrumb",
                "layout" => "ViewQuilt",
                "settings" => "Settings",
                "theme" => "Palette",
                "about" => "Info",
                _ => "Folder"
            };
        }
    }

    /// <summary>
    /// 面包屑项模型
    /// </summary>
    public class BreadcrumbItemModel
    {
        public string Title { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public string Path { get; set; } = string.Empty;
    }

    /// <summary>
    /// 快速导航项
    /// </summary>
    public class QuickNavigationItem
    {
        public string Title { get; set; } = string.Empty;
        public string Path { get; set; } = string.Empty;
    }
}
