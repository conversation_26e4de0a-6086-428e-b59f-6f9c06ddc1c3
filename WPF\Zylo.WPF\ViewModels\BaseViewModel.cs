using CommunityToolkit.Mvvm.ComponentModel;
using Zylo.WPF.Services;

namespace Zylo.WPF.ViewModels;

/// <summary>
/// 基础 ViewModel 类，提供通用功能
/// </summary>
public abstract partial class BaseViewModel : ObservableObject
{
    // 保持基类纯净，不包含任何服务依赖
}

/// <summary>
/// 支持验证的基础 ViewModel 类
/// </summary>
public abstract partial class BaseValidatorViewModel : ObservableValidator
{
    // 保持基类纯净，不包含任何服务依赖
}
