# 🎨 Zylo WPF 控件示例库

一个现代化的 WPF 自定义控件示例库，展示各种高质量的 WPF 控件实现和使用方法。

## 🎯 项目定位

**Zylo WPF 控件示例库** 是一个专门用于展示和学习 WPF 自定义控件开发的项目，包含：

- 🧩 **丰富的控件示例** - 各种类型的自定义控件实现
- 📚 **最佳实践展示** - 遵循 WPF 开发的最佳实践
- 🎨 **现代化设计** - 集成 WPF-UI 等现代化 UI 框架
- 🔧 **即用代码** - 可直接复制到项目中使用
- 📖 **详细文档** - 每个控件都有完整的使用说明

## 🏗️ 项目结构

```
📁 ZyloWPF/
├── 📁 Zylo.WPF/                   # 控件库核心
│   ├── 📁 Controls/               # 自定义控件
│   │   ├── Navigation/            # 导航控件
│   │   ├── Icon/                  # 图标控件
│   │   ├── Data/                  # 数据控件
│   │   └── Layout/                # 布局控件
│   ├── 📁 Converters/             # 值转换器
│   ├── 📁 Behaviors/              # 行为
│   └── 📁 Themes/                 # 主题样式
│
└── 📁 WPFTest/                    # 示例应用
    ├── 📁 Views/                  # 示例页面
    └── 📁 ViewModels/             # 视图模型
```

## 🎨 已实现的控件

### 🧭 导航控件
- **NavigationControl** - 高级导航控件
  - 支持树形和列表两种显示模式
  - 自动视图-视图模型绑定
  - 搜索和过滤功能
  - 命令绑定支持

### 🎯 图标控件
- **ZyloIcon** - 自定义图标控件
  - 类似 WPF-UI SymbolIcon 的使用方式
  - 支持自定义字体图标
  - 枚举类型安全
  - 统一的图标转换器

## 🚀 快速开始

### 1. 克隆项目
```bash
git clone [repository-url]
cd ZyloWPF
```

### 2. 运行示例
```bash
dotnet run --project WPFTest
```

### 3. 浏览控件示例
- 启动应用后，通过左侧导航菜单浏览各种控件示例
- 每个示例都包含源代码和使用说明

## 📖 使用示例

### NavigationControl 使用
```xaml
<zylo:NavigationControl 
    ItemsSource="{Binding NavigationItems}"
    SelectedItemBinding="{Binding SelectedItem}"
    SelectionCommand="{Binding SelectionCommand}" />
```

### ZyloIcon 使用
```xaml
<!-- 使用枚举 -->
<zyloIcon:ZyloIcon Symbol="Dwg" FontSize="24"/>

<!-- 在数据中使用 -->
new NavigationItemModel("1", "DWG文件", "DwgView") 
{ 
    ZyloSymbol = ZyloSymbol.Dwg 
}
```

## 🛠️ 技术栈

- **.NET 8** - 最新的 .NET 平台
- **WPF** - Windows Presentation Foundation
- **WPF-UI** - 现代化 UI 框架
- **CommunityToolkit.Mvvm** - MVVM 模式支持
- **DryIoc** - 依赖注入容器

## 🎯 设计原则

1. **可复用性** - 控件设计为可独立使用
2. **可扩展性** - 支持自定义和扩展
3. **类型安全** - 使用强类型和枚举
4. **性能优化** - 遵循 WPF 性能最佳实践
5. **现代化** - 集成最新的 WPF 技术和模式

## 📝 贡献指南

欢迎贡献新的控件示例！请确保：

1. 遵循项目的代码风格
2. 提供完整的使用示例
3. 添加必要的文档说明
4. 确保控件的可复用性

## 📄 许可证

[添加许可证信息]

## 🔗 相关链接

- [WPF-UI](https://wpfui.lepo.co/)
- [CommunityToolkit.Mvvm](https://docs.microsoft.com/en-us/dotnet/communitytoolkit/mvvm/)
- [DryIoc](https://github.com/dadhi/DryIoc)
