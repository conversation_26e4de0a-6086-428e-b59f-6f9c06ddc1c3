<!-- AutoSuggestBox 高级功能示例 -->
<StackPanel Margin="20" Spacing="15">

    <!-- 专用样式搜索框 -->
    <GroupBox Header="专用样式搜索框" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <StackPanel Margin="8">
                <TextBlock Text="专用搜索框" FontWeight="Medium" Margin="0,0,0,4"/>
                <ui:AutoSuggestBox PlaceholderText="专用搜索框..."
                                   Style="{StaticResource SearchAutoSuggestBoxStyle}"/>
            </StackPanel>
            
            <StackPanel Margin="8">
                <TextBlock Text="圆角搜索框" FontWeight="Medium" Margin="0,0,0,4"/>
                <ui:AutoSuggestBox PlaceholderText="圆角搜索框..."
                                   Icon="{ui:SymbolIcon Search16}"
                                   Style="{StaticResource RoundedAutoSuggestBoxStyle}"/>
            </StackPanel>
            
            <StackPanel Margin="8">
                <TextBlock Text="紧凑搜索框" FontWeight="Medium" Margin="0,0,0,4"/>
                <ui:AutoSuggestBox PlaceholderText="紧凑搜索框..."
                                   Icon="{ui:SymbolIcon Search16}"
                                   Style="{StaticResource CompactAutoSuggestBoxStyle}"/>
            </StackPanel>
        </WrapPanel>
    </GroupBox>

    <!-- 不同状态展示 -->
    <GroupBox Header="不同状态展示" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <StackPanel Margin="8">
                <TextBlock Text="正常状态" FontWeight="Medium" Margin="0,0,0,4"/>
                <ui:AutoSuggestBox PlaceholderText="正常状态"
                                   Icon="{ui:SymbolIcon Search16}"
                                   Style="{StaticResource AutoSuggestBoxStyle}"/>
            </StackPanel>
            
            <StackPanel Margin="8">
                <TextBlock Text="禁用状态" FontWeight="Medium" Margin="0,0,0,4"/>
                <ui:AutoSuggestBox PlaceholderText="禁用状态"
                                   Icon="{ui:SymbolIcon Search16}"
                                   Style="{StaticResource AutoSuggestBoxStyle}"
                                   IsEnabled="False"/>
            </StackPanel>
            
            <StackPanel Margin="8">
                <TextBlock Text="只读状态" FontWeight="Medium" Margin="0,0,0,4"/>
                <ui:AutoSuggestBox Text="只读内容"
                                   Icon="{ui:SymbolIcon Lock16}"
                                   Style="{StaticResource AutoSuggestBoxStyle}"
                                   IsReadOnly="True"/>
            </StackPanel>
        </WrapPanel>
    </GroupBox>

    <!-- 实时搜索功能 -->
    <GroupBox Header="实时搜索功能" Padding="15">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="16"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左列：搜索输入 -->
            <StackPanel Grid.Column="0">
                <TextBlock Text="实时搜索输入" FontWeight="Medium" Margin="0,0,0,8"/>
                
                <ui:AutoSuggestBox Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                                   PlaceholderText="搜索城市..."
                                   Icon="{ui:SymbolIcon Search16}"
                                   Style="{StaticResource AutoSuggestBoxStyle}"
                                   Margin="0,0,0,8"/>
                
                <ui:AutoSuggestBox Text="{Binding LanguageSearchText, UpdateSourceTrigger=PropertyChanged}"
                                   PlaceholderText="搜索编程语言..."
                                   Icon="{ui:SymbolIcon Code16}"
                                   Style="{StaticResource AutoSuggestBoxStyle}"
                                   Margin="0,0,0,8"/>
                
                <ui:AutoSuggestBox Text="{Binding CountrySearchText, UpdateSourceTrigger=PropertyChanged}"
                                   PlaceholderText="搜索国家..."
                                   Icon="{ui:SymbolIcon Globe16}"
                                   Style="{StaticResource SmallAutoSuggestBoxStyle}"/>
            </StackPanel>

            <!-- 右列：搜索结果 -->
            <StackPanel Grid.Column="2">
                <TextBlock Text="搜索结果" FontWeight="Medium" Margin="0,0,0,8"/>
                
                <Border Background="{DynamicResource ControlFillColorTertiaryBrush}"
                        CornerRadius="4" Padding="12">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                            <TextBlock Text="城市搜索:" FontSize="12" Margin="0,0,4,0"/>
                            <TextBlock Text="{Binding SearchText}" FontWeight="Medium" FontSize="12"/>
                        </StackPanel>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                            <TextBlock Text="语言搜索:" FontSize="12" Margin="0,0,4,0"/>
                            <TextBlock Text="{Binding LanguageSearchText}" FontWeight="Medium" FontSize="12"/>
                        </StackPanel>
                        
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="国家搜索:" FontSize="12" Margin="0,0,4,0"/>
                            <TextBlock Text="{Binding CountrySearchText}" FontWeight="Medium" FontSize="12"/>
                        </StackPanel>
                    </StackPanel>
                </Border>
            </StackPanel>
        </Grid>
    </GroupBox>

    <!-- 自定义图标搜索 -->
    <GroupBox Header="自定义图标搜索" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <StackPanel Margin="8">
                <TextBlock Text="文档搜索" FontWeight="Medium" Margin="0,0,0,4"/>
                <ui:AutoSuggestBox PlaceholderText="搜索文档..."
                                   Icon="{ui:SymbolIcon Document16}"
                                   Style="{StaticResource AutoSuggestBoxStyle}"/>
            </StackPanel>
            
            <StackPanel Margin="8">
                <TextBlock Text="用户搜索" FontWeight="Medium" Margin="0,0,0,4"/>
                <ui:AutoSuggestBox PlaceholderText="搜索用户..."
                                   Icon="{ui:SymbolIcon Person16}"
                                   Style="{StaticResource AutoSuggestBoxStyle}"/>
            </StackPanel>
            
            <StackPanel Margin="8">
                <TextBlock Text="邮件搜索" FontWeight="Medium" Margin="0,0,0,4"/>
                <ui:AutoSuggestBox PlaceholderText="搜索邮件..."
                                   Icon="{ui:SymbolIcon Mail16}"
                                   Style="{StaticResource AutoSuggestBoxStyle}"/>
            </StackPanel>
            
            <StackPanel Margin="8">
                <TextBlock Text="设置搜索" FontWeight="Medium" Margin="0,0,0,4"/>
                <ui:AutoSuggestBox PlaceholderText="搜索设置..."
                                   Icon="{ui:SymbolIcon Settings16}"
                                   Style="{StaticResource AutoSuggestBoxStyle}"/>
            </StackPanel>
        </WrapPanel>
    </GroupBox>

    <!-- 搜索建议功能 -->
    <GroupBox Header="搜索建议功能" Padding="15">
        <StackPanel>
            <TextBlock Text="带建议列表的搜索框" FontWeight="Medium" Margin="0,0,0,8"/>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="16"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 左列：搜索框 -->
                <StackPanel Grid.Column="0">
                    <ui:AutoSuggestBox PlaceholderText="输入城市名称..."
                                       Icon="{ui:SymbolIcon Search16}"
                                       Style="{StaticResource AutoSuggestBoxStyle}"
                                       Margin="0,0,0,8"/>
                    
                    <ui:AutoSuggestBox PlaceholderText="输入编程语言..."
                                       Icon="{ui:SymbolIcon Code16}"
                                       Style="{StaticResource AutoSuggestBoxStyle}"
                                       Margin="0,0,0,8"/>
                    
                    <ui:AutoSuggestBox PlaceholderText="输入国家名称..."
                                       Icon="{ui:SymbolIcon Globe16}"
                                       Style="{StaticResource AutoSuggestBoxStyle}"/>
                </StackPanel>

                <!-- 右列：建议说明 -->
                <StackPanel Grid.Column="2">
                    <TextBlock Text="建议功能说明" FontWeight="Medium" Margin="0,0,0,8"/>
                    
                    <Border Background="{DynamicResource ControlFillColorTertiaryBrush}"
                            CornerRadius="4" Padding="12">
                        <StackPanel>
                            <TextBlock Text="• 输入时自动显示建议" FontSize="12" Margin="0,0,0,2"/>
                            <TextBlock Text="• 支持模糊匹配" FontSize="12" Margin="0,0,0,2"/>
                            <TextBlock Text="• 键盘导航选择" FontSize="12" Margin="0,0,0,2"/>
                            <TextBlock Text="• 点击或回车确认" FontSize="12" Margin="0,0,0,2"/>
                            <TextBlock Text="• 实时过滤结果" FontSize="12"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Grid>
        </StackPanel>
    </GroupBox>

    <!-- 搜索操作按钮 -->
    <GroupBox Header="搜索操作" Padding="15">
        <StackPanel Orientation="Horizontal">
            <ui:Button Content="执行搜索"
                       Command="{Binding SearchCommand}"
                       CommandParameter="高级搜索"
                       Appearance="Primary"
                       Margin="0,0,8,0"/>
            
            <ui:Button Content="清除搜索"
                       Command="{Binding ClearSearchCommand}"
                       Appearance="Secondary"
                       Margin="0,0,8,0"/>
            
            <ui:Button Content="重置计数"
                       Command="{Binding ResetCountCommand}"
                       Appearance="Secondary"/>
        </StackPanel>
    </GroupBox>

</StackPanel>
