<!-- AutoSuggestBox 基础用法示例 - MVVM模式 -->
<StackPanel Margin="20" Spacing="15"
            xmlns:b="http://schemas.microsoft.com/xaml/behaviors">

    <!-- 标准 AutoSuggestBox -->
    <GroupBox Header="标准 AutoSuggestBox" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <ui:AutoSuggestBox PlaceholderText="搜索城市..."
                               Icon="{ui:SymbolIcon Search16}"
                               Style="{StaticResource AutoSuggestBoxStyle}"
                               Margin="4"/>

            <ui:AutoSuggestBox PlaceholderText="搜索产品..."
                               Icon="{ui:SymbolIcon ShoppingBag16}"
                               Style="{StaticResource AutoSuggestBoxStyle}"
                               Margin="4"/>

            <ui:AutoSuggestBox PlaceholderText="搜索用户..."
                               Icon="{ui:SymbolIcon Person16}"
                               Style="{StaticResource AutoSuggestBoxStyle}"
                               Margin="4"/>
        </WrapPanel>
    </GroupBox>

    <!-- 不同尺寸 -->
    <GroupBox Header="不同尺寸" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <ui:AutoSuggestBox PlaceholderText="小型搜索"
                               Icon="{ui:SymbolIcon Search16}"
                               Style="{StaticResource SmallAutoSuggestBoxStyle}"
                               Margin="4"/>
            
            <ui:AutoSuggestBox PlaceholderText="标准搜索"
                               Icon="{ui:SymbolIcon Search16}"
                               Style="{StaticResource AutoSuggestBoxStyle}"
                               Margin="4"/>
            
            <ui:AutoSuggestBox PlaceholderText="大型搜索"
                               Icon="{ui:SymbolIcon Search16}"
                               Style="{StaticResource LargeAutoSuggestBoxStyle}"
                               Margin="4"/>
        </WrapPanel>
    </GroupBox>

    <!-- 不同图标 -->
    <GroupBox Header="不同图标" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <ui:AutoSuggestBox PlaceholderText="搜索文件..."
                               Icon="{ui:SymbolIcon Document16}"
                               Style="{StaticResource AutoSuggestBoxStyle}"
                               Margin="4"/>
            
            <ui:AutoSuggestBox PlaceholderText="搜索邮件..."
                               Icon="{ui:SymbolIcon Mail16}"
                               Style="{StaticResource AutoSuggestBoxStyle}"
                               Margin="4"/>
            
            <ui:AutoSuggestBox PlaceholderText="搜索设置..."
                               Icon="{ui:SymbolIcon Settings16}"
                               Style="{StaticResource AutoSuggestBoxStyle}"
                               Margin="4"/>
        </WrapPanel>
    </GroupBox>

    <!-- 数据绑定示例 -->
    <GroupBox Header="数据绑定示例" Padding="15">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="16"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左列：搜索框 -->
            <StackPanel Grid.Column="0">
                <TextBlock Text="搜索控件" FontWeight="Medium" Margin="0,0,0,8"/>
                
                <StackPanel>
                    <!-- MVVM模式：使用Command处理TextChanged事件 -->
                    <ui:AutoSuggestBox x:Name="CitySearchBox"
                                       Text="{Binding SearchText, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                       ItemsSource="{Binding CitySuggestions}"
                                       PlaceholderText="搜索城市..."
                                       Icon="{ui:SymbolIcon Search16}"
                                       Style="{StaticResource AutoSuggestBoxStyle}"
                                       Margin="0,0,0,8">
                        <b:Interaction.Triggers>
                            <b:EventTrigger EventName="TextChanged">
                                <b:InvokeCommandAction Command="{Binding CitySearchCommand}"
                                                       CommandParameter="{Binding Text, ElementName=CitySearchBox}" />
                            </b:EventTrigger>
                        </b:Interaction.Triggers>
                    </ui:AutoSuggestBox>

                    <ui:AutoSuggestBox x:Name="LanguageSearchBox"
                                       Text="{Binding LanguageSearchText, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                       ItemsSource="{Binding LanguageSuggestions}"
                                       PlaceholderText="搜索编程语言..."
                                       Icon="{ui:SymbolIcon Code16}"
                                       Style="{StaticResource AutoSuggestBoxStyle}"
                                       Margin="0,0,0,8">
                        <b:Interaction.Triggers>
                            <b:EventTrigger EventName="TextChanged">
                                <b:InvokeCommandAction Command="{Binding LanguageSearchCommand}"
                                                       CommandParameter="{Binding Text, ElementName=LanguageSearchBox}" />
                            </b:EventTrigger>
                        </b:Interaction.Triggers>
                    </ui:AutoSuggestBox>

                    <ui:AutoSuggestBox x:Name="CountrySearchBox"
                                       Text="{Binding CountrySearchText, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                       ItemsSource="{Binding CountrySuggestions}"
                                       PlaceholderText="搜索国家..."
                                       Icon="{ui:SymbolIcon Globe16}"
                                       Style="{StaticResource SmallAutoSuggestBoxStyle}">
                        <b:Interaction.Triggers>
                            <b:EventTrigger EventName="TextChanged">
                                <b:InvokeCommandAction Command="{Binding CountrySearchCommand}"
                                                       CommandParameter="{Binding Text, ElementName=CountrySearchBox}" />
                            </b:EventTrigger>
                        </b:Interaction.Triggers>
                    </ui:AutoSuggestBox>
                </StackPanel>
            </StackPanel>

            <!-- 右列：搜索结果 -->
            <StackPanel Grid.Column="2">
                <TextBlock Text="搜索结果" FontWeight="Medium" Margin="0,0,0,8"/>
                
                <Border Background="{DynamicResource ControlFillColorTertiaryBrush}"
                        CornerRadius="4" Padding="12">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                            <TextBlock Text="城市:" FontSize="12" Margin="0,0,4,0"/>
                            <TextBlock Text="{Binding SearchText}" FontWeight="Medium" FontSize="12"/>
                        </StackPanel>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                            <TextBlock Text="语言:" FontSize="12" Margin="0,0,4,0"/>
                            <TextBlock Text="{Binding LanguageSearchText}" FontWeight="Medium" FontSize="12"/>
                        </StackPanel>
                        
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="国家:" FontSize="12" Margin="0,0,4,0"/>
                            <TextBlock Text="{Binding CountrySearchText}" FontWeight="Medium" FontSize="12"/>
                        </StackPanel>
                    </StackPanel>
                </Border>
            </StackPanel>
        </Grid>
    </GroupBox>

</StackPanel>
