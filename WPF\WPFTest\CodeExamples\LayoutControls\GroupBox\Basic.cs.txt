// GroupBox C# 基础示例
// GroupBox 是 WPF 中的分组控件，用于逻辑分组界面元素

using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace WPFTest.Examples.LayoutControls
{
    public class GroupBoxBasicExample
    {
        /// <summary>
        /// 创建基础 GroupBox 控件
        /// </summary>
        public static GroupBox CreateBasicGroupBox()
        {
            var groupBox = new GroupBox
            {
                Header = "用户信息",
                Background = new SolidColorBrush(Colors.LightBlue),
                BorderBrush = new SolidColorBrush(Colors.Blue),
                BorderThickness = new Thickness(1),
                Margin = new Thickness(8),
                Padding = new Thickness(12)
            };

            // 创建内容网格
            var grid = new Grid
            {
                Margin = new Thickness(12)
            };

            // 设置列定义
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            // 设置行定义
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            // 添加表单控件
            AddFormField(grid, "姓名:", "张三", 0);
            AddFormField(grid, "年龄:", "25", 1);
            AddFormField(grid, "邮箱:", "<EMAIL>", 2);

            groupBox.Content = grid;
            return groupBox;
        }

        /// <summary>
        /// 创建设置选项 GroupBox
        /// </summary>
        public static GroupBox CreateSettingsGroupBox()
        {
            var groupBox = new GroupBox
            {
                Header = "设置选项",
                Background = new SolidColorBrush(Colors.LightYellow),
                BorderBrush = new SolidColorBrush(Colors.Orange),
                BorderThickness = new Thickness(1),
                Margin = new Thickness(8)
            };

            var content = new StackPanel
            {
                Margin = new Thickness(12)
            };

            content.Children.Add(new CheckBox
            {
                Content = "启用通知",
                IsChecked = true,
                Margin = new Thickness(0, 0, 0, 8)
            });

            content.Children.Add(new CheckBox
            {
                Content = "自动保存",
                IsChecked = false,
                Margin = new Thickness(0, 0, 0, 8)
            });

            content.Children.Add(new CheckBox
            {
                Content = "深色主题",
                IsChecked = true,
                Margin = new Thickness(0, 0, 0, 8)
            });

            content.Children.Add(new Button
            {
                Content = "应用设置",
                HorizontalAlignment = HorizontalAlignment.Left
            });

            groupBox.Content = content;
            return groupBox;
        }

        /// <summary>
        /// 创建带图标标题的 GroupBox
        /// </summary>
        public static GroupBox CreateGroupBoxWithIcon()
        {
            var groupBox = new GroupBox
            {
                Background = new SolidColorBrush(Colors.DarkSlateBlue),
                BorderBrush = new SolidColorBrush(Colors.SlateBlue),
                BorderThickness = new Thickness(2),
                Margin = new Thickness(8)
            };

            // 创建带图标的标题
            var headerPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal
            };

            // 添加图标（这里用 TextBlock 模拟图标）
            headerPanel.Children.Add(new TextBlock
            {
                Text = "⭐",
                FontSize = 16,
                Margin = new Thickness(0, 0, 8, 0),
                VerticalAlignment = VerticalAlignment.Center
            });

            headerPanel.Children.Add(new TextBlock
            {
                Text = "重要功能",
                Foreground = Brushes.White,
                FontWeight = FontWeights.Bold,
                VerticalAlignment = VerticalAlignment.Center
            });

            groupBox.Header = headerPanel;

            // 创建内容
            var content = new StackPanel
            {
                Margin = new Thickness(12)
            };

            content.Children.Add(new TextBlock
            {
                Text = "这是一个重要的功能区域。",
                Foreground = Brushes.White,
                Margin = new Thickness(0, 0, 0, 12)
            });

            var progressBar = new ProgressBar
            {
                Value = 75,
                Height = 20,
                Margin = new Thickness(0, 0, 0, 8)
            };
            content.Children.Add(progressBar);

            content.Children.Add(new TextBlock
            {
                Text = "进度: 75%",
                Foreground = Brushes.White,
                FontSize = 12,
                Margin = new Thickness(0, 0, 0, 8)
            });

            content.Children.Add(new Button
            {
                Content = "执行操作"
            });

            groupBox.Content = content;
            return groupBox;
        }

        /// <summary>
        /// 创建统计信息 GroupBox
        /// </summary>
        public static GroupBox CreateStatsGroupBox()
        {
            var groupBox = new GroupBox
            {
                Header = "统计信息",
                Background = Brushes.White,
                BorderBrush = Brushes.LightGray,
                BorderThickness = new Thickness(1),
                Margin = new Thickness(8)
            };

            var grid = new Grid
            {
                Margin = new Thickness(12)
            };

            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            AddStatsField(grid, "总数:", "1,234", 0);
            AddStatsField(grid, "活跃:", "987", 1);
            AddStatsField(grid, "新增:", "45", 2);

            groupBox.Content = grid;
            return groupBox;
        }

        /// <summary>
        /// 创建单选按钮分组 GroupBox
        /// </summary>
        public static GroupBox CreateRadioButtonGroupBox()
        {
            var groupBox = new GroupBox
            {
                Header = "选择选项",
                Background = new SolidColorBrush(Colors.LightGreen),
                BorderBrush = new SolidColorBrush(Colors.Green),
                BorderThickness = new Thickness(1),
                Margin = new Thickness(8)
            };

            var content = new StackPanel
            {
                Margin = new Thickness(12)
            };

            content.Children.Add(new RadioButton
            {
                Content = "选项 1",
                GroupName = "Options",
                IsChecked = true,
                Margin = new Thickness(0, 0, 0, 4)
            });

            content.Children.Add(new RadioButton
            {
                Content = "选项 2",
                GroupName = "Options",
                Margin = new Thickness(0, 0, 0, 4)
            });

            content.Children.Add(new RadioButton
            {
                Content = "选项 3",
                GroupName = "Options",
                Margin = new Thickness(0, 0, 0, 4)
            });

            content.Children.Add(new RadioButton
            {
                Content = "选项 4",
                GroupName = "Options"
            });

            groupBox.Content = content;
            return groupBox;
        }

        /// <summary>
        /// 创建禁用状态的 GroupBox
        /// </summary>
        public static GroupBox CreateDisabledGroupBox()
        {
            var groupBox = new GroupBox
            {
                Header = "禁用状态",
                Background = new SolidColorBrush(Colors.LightGray),
                BorderBrush = new SolidColorBrush(Colors.Gray),
                BorderThickness = new Thickness(1),
                IsEnabled = false, // 禁用状态
                Margin = new Thickness(8)
            };

            var content = new StackPanel
            {
                Margin = new Thickness(12)
            };

            content.Children.Add(new TextBlock
            {
                Text = "这是禁用状态的 GroupBox。"
            });

            content.Children.Add(new Button
            {
                Content = "不可点击按钮",
                HorizontalAlignment = HorizontalAlignment.Left,
                Margin = new Thickness(0, 8, 0, 0)
            });

            groupBox.Content = content;
            return groupBox;
        }

        /// <summary>
        /// 辅助方法：添加表单字段
        /// </summary>
        private static void AddFormField(Grid grid, string labelText, string valueText, int row)
        {
            var label = new TextBlock
            {
                Text = labelText,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(0, 0, 8, row < 2 ? 8 : 0)
            };
            Grid.SetRow(label, row);
            Grid.SetColumn(label, 0);
            grid.Children.Add(label);

            var textBox = new TextBox
            {
                Text = valueText,
                Margin = new Thickness(0, 0, 0, row < 2 ? 8 : 0)
            };
            Grid.SetRow(textBox, row);
            Grid.SetColumn(textBox, 1);
            grid.Children.Add(textBox);
        }

        /// <summary>
        /// 辅助方法：添加统计字段
        /// </summary>
        private static void AddStatsField(Grid grid, string labelText, string valueText, int row)
        {
            var label = new TextBlock
            {
                Text = labelText,
                FontWeight = FontWeights.Bold
            };
            Grid.SetRow(label, row);
            Grid.SetColumn(label, 0);
            grid.Children.Add(label);

            var value = new TextBlock
            {
                Text = valueText,
                HorizontalAlignment = HorizontalAlignment.Right
            };
            Grid.SetRow(value, row);
            Grid.SetColumn(value, 1);
            grid.Children.Add(value);
        }
    }
}
