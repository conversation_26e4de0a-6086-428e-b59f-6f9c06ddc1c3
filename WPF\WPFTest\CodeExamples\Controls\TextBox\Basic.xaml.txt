<!-- TextBox 基础用法示例 -->
<StackPanel Margin="20" Spacing="15">

    <!-- 标准 TextBox -->
    <GroupBox Header="标准 TextBox" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <ui:TextBox Style="{StaticResource TextBoxStyle}"
                        Text="{Binding StandardTextValue, UpdateSourceTrigger=PropertyChanged}"
                        PlaceholderText="请输入文本..."
                        Margin="4"/>
            
            <ui:TextBox Style="{StaticResource TextBoxStyle}"
                        Text="预设文本"
                        Margin="4"/>
            
            <ui:TextBox Style="{StaticResource TextBoxStyle}"
                        PlaceholderText="占位符文本"
                        Margin="4"/>
        </WrapPanel>
    </GroupBox>

    <!-- 不同尺寸 -->
    <GroupBox Header="不同尺寸" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <ui:TextBox Style="{StaticResource SmallTextBoxStyle}"
                        Text="小型"
                        Margin="4"/>
            <ui:TextBox Style="{StaticResource TextBoxStyle}"
                        Text="标准"
                        Margin="4"/>
            <ui:TextBox Style="{StaticResource LargeTextBoxStyle}"
                        Text="大型"
                        Margin="4"/>
        </WrapPanel>
    </GroupBox>

    <!-- 基础属性 -->
    <GroupBox Header="基础属性" Padding="15">
        <StackPanel Spacing="10">
            <!-- 只读 -->
            <ui:TextBox Style="{StaticResource TextBoxStyle}"
                        Text="只读文本框"
                        IsReadOnly="True"/>
            
            <!-- 禁用 -->
            <ui:TextBox Style="{StaticResource TextBoxStyle}"
                        Text="禁用文本框"
                        IsEnabled="False"/>
            
            <!-- 最大长度 -->
            <ui:TextBox Style="{StaticResource TextBoxStyle}"
                        PlaceholderText="最大长度10字符"
                        MaxLength="10"/>
        </StackPanel>
    </GroupBox>

    <!-- 文本对齐 -->
    <GroupBox Header="文本对齐" Padding="15">
        <StackPanel Spacing="10">
            <ui:TextBox Style="{StaticResource TextBoxStyle}"
                        Text="左对齐（默认）"
                        HorizontalContentAlignment="Left"/>
            
            <ui:TextBox Style="{StaticResource TextBoxStyle}"
                        Text="居中对齐"
                        HorizontalContentAlignment="Center"/>
            
            <ui:TextBox Style="{StaticResource TextBoxStyle}"
                        Text="右对齐"
                        HorizontalContentAlignment="Right"/>
        </StackPanel>
    </GroupBox>

</StackPanel>
