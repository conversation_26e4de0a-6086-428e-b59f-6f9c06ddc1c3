using Wpf.Ui.Controls;
using Zylo.WPF.Enums;

namespace Zylo.WPF.Models.TreeNode;

/// <summary>
/// 通用树节点工厂类 - 提供便利的创建方法，支持任何业务场景
///
/// 🎯 设计理念：
/// - 通用性：支持文件系统、组织架构、项目管理、数据库结构等任何树形数据
/// - 便利性：提供常用类型的快捷创建方法
/// - 扩展性：支持自定义节点类型和图标
/// - 一致性：统一的创建接口和参数规范
///
/// 📚 使用示例：
/// - 通用创建：TreeNodeFactory.Create(1, "节点", "custom-type")
/// - 文件系统：TreeNodeFactory.CreateFolder(1, "文档")
/// - 组织架构：TreeNodeFactory.CreateUser(1, "张三")
/// - 项目管理：TreeNodeFactory.CreateTask(1, "开发任务")
/// - 数据库：TreeNodeFactory.CreateDatabase(1, "用户数据库", "mysql")
///
/// 🔧 支持150+种预定义节点类型，详见TreeNodeIconHelper
/// </summary>
public static class TreeNodeFactory
{
    #region 通用创建方法

    /// <summary>
    /// 创建通用节点
    /// </summary>
    /// <param name="id">节点ID</param>
    /// <param name="name">节点名称</param>
    /// <param name="nodeType">节点类型</param>
    /// <param name="description">描述</param>
    /// <returns>通用节点</returns>
    public static TreeNodeData Create(int id, string name, string nodeType, string description = "")
    {
        return new TreeNodeData(id, name, description, nodeType);
    }

    /// <summary>
    /// 创建带Emoji的节点
    /// </summary>
    /// <param name="id">节点ID</param>
    /// <param name="name">节点名称</param>
    /// <param name="nodeType">节点类型</param>
    /// <param name="description">描述</param>
    /// <param name="emoji">Emoji图标</param>
    /// <returns>节点</returns>
    public static TreeNodeData CreateWithEmoji(int id, string name, string nodeType, string description, string emoji)
    {
        return new TreeNodeData(id, name, description, nodeType, emoji);
    }

    /// <summary>
    /// 创建带WPF-UI图标的节点
    /// </summary>
    /// <param name="id">节点ID</param>
    /// <param name="name">节点名称</param>
    /// <param name="nodeType">节点类型</param>
    /// <param name="description">描述</param>
    /// <param name="wpfUiSymbol">WPF-UI图标</param>
    /// <returns>节点</returns>
    public static TreeNodeData CreateWithWpfIcon(int id, string name, string nodeType, string description, SymbolRegular wpfUiSymbol)
    {
        return new TreeNodeData(id, name, description, nodeType, wpfUiSymbol);
    }

    /// <summary>
    /// 创建带Zylo图标的节点
    /// </summary>
    /// <param name="id">节点ID</param>
    /// <param name="name">节点名称</param>
    /// <param name="nodeType">节点类型</param>
    /// <param name="description">描述</param>
    /// <param name="zyloSymbol">Zylo图标</param>
    /// <returns>节点</returns>
    public static TreeNodeData CreateWithZyloIcon(int id, string name, string nodeType, string description, ZyloSymbol zyloSymbol)
    {
        return new TreeNodeData(id, name, description, nodeType, zyloSymbol);
    }

    #endregion

    #region 文件系统类型 - 通用文件和文件夹节点

    /// <summary>
    /// 创建文件夹节点 - 适用于文件系统、目录结构、分类管理等场景
    /// </summary>
    public static TreeNodeData CreateFolder(int id, string name, string description = "", SymbolRegular? wpfUiSymbol = null)
    {
        var node = new TreeNodeData(id, name, description, "folder");
        if (wpfUiSymbol.HasValue)
            node.WpfUiSymbol = wpfUiSymbol.Value;
        return node;
    }

    /// <summary>
    /// 创建文件节点
    /// </summary>
    public static TreeNodeData CreateFile(int id, string name, string description = "", SymbolRegular? wpfUiSymbol = null)
    {
        var node = new TreeNodeData(id, name, description, "file");
        if (wpfUiSymbol.HasValue)
            node.WpfUiSymbol = wpfUiSymbol.Value;
        return node;
    }

    /// <summary>
    /// 创建代码文件节点
    /// </summary>
    public static TreeNodeData CreateCodeFile(int id, string name, string language, string description = "")
    {
        return new TreeNodeData(id, name, description, language.ToLower());
    }

    #endregion

    #region 用户和组织类型 - 通用人员和组织结构节点

    /// <summary>
    /// 创建用户节点 - 适用于用户管理、成员列表、联系人等场景
    /// </summary>
    public static TreeNodeData CreateUser(int id, string name, string description = "", SymbolRegular? wpfUiSymbol = null)
    {
        var node = new TreeNodeData(id, name, description, "user");
        if (wpfUiSymbol.HasValue)
            node.WpfUiSymbol = wpfUiSymbol.Value;
        return node;
    }

    /// <summary>
    /// 创建管理员节点
    /// </summary>
    public static TreeNodeData CreateAdmin(int id, string name, string description = "")
    {
        return new TreeNodeData(id, name, description, "admin");
    }

    /// <summary>
    /// 创建组织节点
    /// </summary>
    public static TreeNodeData CreateOrganization(int id, string name, string description = "", SymbolRegular? wpfUiSymbol = null)
    {
        var node = new TreeNodeData(id, name, description, "organization");
        if (wpfUiSymbol.HasValue)
            node.WpfUiSymbol = wpfUiSymbol.Value;
        return node;
    }

    /// <summary>
    /// 创建部门节点
    /// </summary>
    public static TreeNodeData CreateDepartment(int id, string name, string description = "", SymbolRegular? wpfUiSymbol = null)
    {
        var node = new TreeNodeData(id, name, description, "department");
        if (wpfUiSymbol.HasValue)
            node.WpfUiSymbol = wpfUiSymbol.Value;
        return node;
    }

    /// <summary>
    /// 创建团队节点
    /// </summary>
    public static TreeNodeData CreateTeam(int id, string name, string description = "", SymbolRegular? wpfUiSymbol = null)
    {
        var node = new TreeNodeData(id, name, description, "team");
        if (wpfUiSymbol.HasValue)
            node.WpfUiSymbol = wpfUiSymbol.Value;
        return node;
    }

    #endregion

    #region 项目和任务类型 - 通用项目管理节点

    /// <summary>
    /// 创建项目节点 - 适用于项目管理、工作计划、产品规划等场景
    /// </summary>
    public static TreeNodeData CreateProject(int id, string name, string description = "", SymbolRegular? wpfUiSymbol = null)
    {
        var node = new TreeNodeData(id, name, description, "project");
        if (wpfUiSymbol.HasValue)
            node.WpfUiSymbol = wpfUiSymbol.Value;
        return node;
    }

    /// <summary>
    /// 创建任务节点
    /// </summary>
    public static TreeNodeData CreateTask(int id, string name, string description = "")
    {
        return new TreeNodeData(id, name, description, "task");
    }

    /// <summary>
    /// 创建Bug节点
    /// </summary>
    public static TreeNodeData CreateBug(int id, string name, string description = "")
    {
        return new TreeNodeData(id, name, description, "bug");
    }

    /// <summary>
    /// 创建功能节点
    /// </summary>
    public static TreeNodeData CreateFeature(int id, string name, string description = "")
    {
        return new TreeNodeData(id, name, description, "feature");
    }

    #endregion

    #region 数据库类型 - 通用数据库和表结构节点

    /// <summary>
    /// 创建数据库节点
    /// </summary>
    public static TreeNodeData CreateDatabase(int id, string name, string dbType = "database", string description = "")
    {
        return new TreeNodeData(id, name, description, dbType.ToLower());
    }

    /// <summary>
    /// 创建表节点
    /// </summary>
    public static TreeNodeData CreateTable(int id, string name, string description = "")
    {
        return new TreeNodeData(id, name, description, "table");
    }

    /// <summary>
    /// 创建视图节点
    /// </summary>
    public static TreeNodeData CreateView(int id, string name, string description = "")
    {
        return new TreeNodeData(id, name, description, "view");
    }

    #endregion

    #region 系统和网络类型 - 通用系统架构节点

    /// <summary>
    /// 创建服务器节点
    /// </summary>
    public static TreeNodeData CreateServer(int id, string name, string description = "")
    {
        return new TreeNodeData(id, name, description, "server");
    }

    /// <summary>
    /// 创建服务节点
    /// </summary>
    public static TreeNodeData CreateService(int id, string name, string description = "")
    {
        return new TreeNodeData(id, name, description, "service");
    }

    /// <summary>
    /// 创建API节点
    /// </summary>
    public static TreeNodeData CreateApi(int id, string name, string description = "")
    {
        return new TreeNodeData(id, name, description, "api");
    }

    #endregion

    #region 安全和权限类型 - 通用安全管理节点

    /// <summary>
    /// 创建密钥节点
    /// </summary>
    public static TreeNodeData CreateKey(int id, string name, string description = "")
    {
        return new TreeNodeData(id, name, description, "key");
    }

    /// <summary>
    /// 创建证书节点
    /// </summary>
    public static TreeNodeData CreateCertificate(int id, string name, string description = "")
    {
        return new TreeNodeData(id, name, description, "certificate");
    }

    #endregion

    #region 图标测试方法 - 专门用于测试四种图标类型

    /// <summary>
    /// 创建WPF-UI图标测试节点
    /// </summary>
    public static TreeNodeData CreateWpfIconTest(int id, string name, SymbolRegular symbol, string description = "")
    {
        return new TreeNodeData(id, name, description, "wpf-test", symbol);
    }

    /// <summary>
    /// 创建Zylo图标测试节点
    /// </summary>
    public static TreeNodeData CreateZyloIconTest(int id, string name, ZyloSymbol symbol, string description = "")
    {
        return new TreeNodeData(id, name, description, "zylo-test", symbol);
    }

    /// <summary>
    /// 创建Emoji图标测试节点
    /// </summary>
    public static TreeNodeData CreateEmojiTest(int id, string name, string emoji, string description = "")
    {
        return new TreeNodeData(id, name, description, "emoji-test", emoji);
    }

    /// <summary>
    /// 创建默认图标测试节点（基于NodeType自动选择图标）
    /// </summary>
    public static TreeNodeData CreateDefaultIconTest(int id, string name, string nodeType, string description = "")
    {
        return new TreeNodeData(id, name, description, nodeType);
    }

    #endregion
}
