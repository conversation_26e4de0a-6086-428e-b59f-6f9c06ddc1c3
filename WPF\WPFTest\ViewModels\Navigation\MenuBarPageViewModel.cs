using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Wpf.Ui.Controls;
using Zylo.WPF.Models.Navigation;

namespace WPFTest.ViewModels.Navigation
{
    /// <summary>
    /// MenuBar 页面 ViewModel
    /// 展示 MenuBar 控件的各种功能和用法
    /// </summary>
    public partial class MenuBarPageViewModel : ObservableObject
    {
        #region 属性

        /// <summary>
        /// 页面标题
        /// </summary>
        [ObservableProperty]
        public partial string PageTitle { get; set; } = "WPF-UI Menu - 原生菜单控件";

        /// <summary>
        /// 页面描述
        /// </summary>
        [ObservableProperty]
        public partial string PageDescription { get; set; } = "展示 WPF-UI 原生 Menu 控件的功能，包括静态声明和数据绑定两种实现方式";

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        public partial string StatusMessage { get; set; } = "WPF-UI Menu 示例已加载，点击菜单项开始体验！";

        /// <summary>
        /// 最后操作
        /// </summary>
        [ObservableProperty]
        public partial string LastAction { get; set; } = "无";

        /// <summary>
        /// 点击次数
        /// </summary>
        [ObservableProperty]
        public partial int ClickCount { get; set; }

        /// <summary>
        /// 是否启用菜单栏
        /// </summary>
        [ObservableProperty]
        public partial bool IsMenuBarEnabled { get; set; } = true;

        /// <summary>
        /// 基础菜单项集合
        /// </summary>
        [ObservableProperty]
        public partial ObservableCollection<MenuItemModel> BasicMenuItems { get; set; } = new();

        /// <summary>
        /// 高级菜单项集合
        /// </summary>
        [ObservableProperty]
        public partial ObservableCollection<MenuItemModel> AdvancedMenuItems { get; set; } = new();

        /// <summary>
        /// 自定义菜单项集合
        /// </summary>
        [ObservableProperty]
        public partial ObservableCollection<MenuItemModel> CustomMenuItems { get; set; } = new();

        #endregion

        #region 代码示例属性

        /// <summary>
        /// 基础 MenuBar XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicMenuBarXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 基础 MenuBar C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicMenuBarCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级 MenuBar XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedMenuBarXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级 MenuBar C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedMenuBarCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// WPF-UI 原生菜单项集合
        /// </summary>
        [ObservableProperty]
        public partial ObservableCollection<MenuItemModel> WpfUiMenuItems { get; set; } = new();

        /// <summary>
        /// WPF-UI 原生 Menu XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string WpfUiMenuXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// WPF-UI 原生 Menu C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string WpfUiMenuCSharpExample { get; set; } = string.Empty;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化 MenuBarPageViewModel
        /// </summary>
        public MenuBarPageViewModel()
        {
            InitializeMenuItems();
            InitializeCodeExamples();
            StatusMessage = "MenuBar 示例库已加载，点击任意菜单项开始体验！";
        }

        #endregion

        #region 命令

        /// <summary>
        /// 菜单项点击命令
        /// </summary>
        /// <param name="parameter">菜单项参数</param>
        [RelayCommand]
        private void MenuItemClick(string? parameter)
        {
            ClickCount++;
            LastAction = parameter ?? "未知操作";
            
            // 根据不同的菜单项显示不同的状态消息
            StatusMessage = parameter switch
            {
                "新建" => "📄 执行了新建操作",
                "打开" => "📂 执行了打开操作",
                "保存" => "💾 执行了保存操作",
                "另存为" => "💾 执行了另存为操作",
                "退出" => "🚪 执行了退出操作",
                "撤销" => "↶ 执行了撤销操作",
                "重做" => "↷ 执行了重做操作",
                "剪切" => "✂️ 执行了剪切操作",
                "复制" => "📋 执行了复制操作",
                "粘贴" => "📋 执行了粘贴操作",
                "全选" => "🔘 执行了全选操作",
                "查找" => "🔍 执行了查找操作",
                "替换" => "🔄 执行了替换操作",
                "工具栏" => "🔧 切换了工具栏显示",
                "状态栏" => "📊 切换了状态栏显示",
                "全屏" => "🖥️ 切换了全屏模式",
                "选项" => "⚙️ 打开了选项设置",
                "自定义" => "🎨 打开了自定义设置",
                "帮助主题" => "❓ 打开了帮助主题",
                "关于" => "ℹ️ 显示了关于信息",
                _ => $"🎯 点击了菜单项: {parameter}"
            };

            Debug.WriteLine($"MenuBar 菜单项点击: {parameter}");
        }

        /// <summary>
        /// 切换菜单栏启用状态命令
        /// </summary>
        [RelayCommand]
        private void ToggleMenuBarEnabled()
        {
            IsMenuBarEnabled = !IsMenuBarEnabled;
            StatusMessage = IsMenuBarEnabled ? "✅ 菜单栏已启用" : "❌ 菜单栏已禁用";
            Debug.WriteLine($"MenuBar 启用状态切换: {IsMenuBarEnabled}");
        }

        /// <summary>
        /// 添加自定义菜单项命令
        /// </summary>
        [RelayCommand]
        private void AddCustomMenuItem()
        {
            var newItem = new MenuItemModel($"自定义项 {CustomMenuItems.Count + 1}", MenuItemClickCommand, $"自定义项{CustomMenuItems.Count + 1}");
            CustomMenuItems.Add(newItem);
            StatusMessage = $"➕ 添加了自定义菜单项: {newItem.Header}";
            Debug.WriteLine($"添加自定义菜单项: {newItem.Header}");
        }

        /// <summary>
        /// 清空自定义菜单项命令
        /// </summary>
        [RelayCommand]
        private void ClearCustomMenuItems()
        {
            var count = CustomMenuItems.Count;
            CustomMenuItems.Clear();
            StatusMessage = $"🗑️ 清空了 {count} 个自定义菜单项";
            Debug.WriteLine($"清空自定义菜单项: {count} 个");
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化菜单项
        /// </summary>
        private void InitializeMenuItems()
        {
            InitializeBasicMenuItems();
            InitializeAdvancedMenuItems();
            InitializeCustomMenuItems();
            InitializeWpfUiMenuItems();
        }

        /// <summary>
        /// 初始化基础菜单项
        /// </summary>
        private void InitializeBasicMenuItems()
        {
            // 文件菜单
            var fileMenu = new MenuItemModel("文件(_F)");
            fileMenu.AddChild(new MenuItemModel("新建(_N)", MenuItemClickCommand, "新建") { InputGestureText = "Ctrl+N" });
            fileMenu.AddChild(new MenuItemModel("打开(_O)", MenuItemClickCommand, "打开") { InputGestureText = "Ctrl+O" });
            fileMenu.AddChild(new MenuItemModel("保存(_S)", MenuItemClickCommand, "保存") { InputGestureText = "Ctrl+S" });
            fileMenu.AddChild(new MenuItemModel("另存为(_A)", MenuItemClickCommand, "另存为") { InputGestureText = "Ctrl+Shift+S" });
            fileMenu.AddChild(new MenuItemModel { IsSeparator = true });
            fileMenu.AddChild(new MenuItemModel("退出(_X)", MenuItemClickCommand, "退出") { InputGestureText = "Alt+F4" });

            // 编辑菜单
            var editMenu = new MenuItemModel("编辑(_E)");
            editMenu.AddChild(new MenuItemModel("撤销(_U)", MenuItemClickCommand, "撤销") { InputGestureText = "Ctrl+Z" });
            editMenu.AddChild(new MenuItemModel("重做(_R)", MenuItemClickCommand, "重做") { InputGestureText = "Ctrl+Y" });
            editMenu.AddChild(new MenuItemModel { IsSeparator = true });
            editMenu.AddChild(new MenuItemModel("剪切(_T)", MenuItemClickCommand, "剪切") { InputGestureText = "Ctrl+X" });
            editMenu.AddChild(new MenuItemModel("复制(_C)", MenuItemClickCommand, "复制") { InputGestureText = "Ctrl+C" });
            editMenu.AddChild(new MenuItemModel("粘贴(_P)", MenuItemClickCommand, "粘贴") { InputGestureText = "Ctrl+V" });

            // 帮助菜单
            var helpMenu = new MenuItemModel("帮助(_H)");
            helpMenu.AddChild(new MenuItemModel("帮助主题(_H)", MenuItemClickCommand, "帮助主题") { InputGestureText = "F1" });
            helpMenu.AddChild(new MenuItemModel { IsSeparator = true });
            helpMenu.AddChild(new MenuItemModel("关于(_A)", MenuItemClickCommand, "关于"));

            BasicMenuItems.Add(fileMenu);
            BasicMenuItems.Add(editMenu);
            BasicMenuItems.Add(helpMenu);
        }

        /// <summary>
        /// 初始化高级菜单项
        /// </summary>
        private void InitializeAdvancedMenuItems()
        {
            // 文件菜单（带图标）
            var fileMenu = new MenuItemModel("文件(_F)")
            {
                Icon = new SymbolIcon { Symbol = SymbolRegular.Document24 }
            };
            
            fileMenu.AddChild(new MenuItemModel("新建(_N)", MenuItemClickCommand, "新建") 
            { 
                InputGestureText = "Ctrl+N",
                Icon = new SymbolIcon { Symbol = SymbolRegular.DocumentAdd24 }
            });
            
            fileMenu.AddChild(new MenuItemModel("打开(_O)", MenuItemClickCommand, "打开") 
            { 
                InputGestureText = "Ctrl+O",
                Icon = new SymbolIcon { Symbol = SymbolRegular.FolderOpen24 }
            });

            // 最近文件子菜单
            var recentMenu = new MenuItemModel("最近文件(_R)")
            {
                Icon = new SymbolIcon { Symbol = SymbolRegular.History24 }
            };
            recentMenu.AddChild(new MenuItemModel("文档1.txt", MenuItemClickCommand, "文档1"));
            recentMenu.AddChild(new MenuItemModel("文档2.txt", MenuItemClickCommand, "文档2"));
            recentMenu.AddChild(new MenuItemModel("文档3.txt", MenuItemClickCommand, "文档3"));
            fileMenu.AddChild(recentMenu);

            fileMenu.AddChild(new MenuItemModel { IsSeparator = true });
            fileMenu.AddChild(new MenuItemModel("退出(_X)", MenuItemClickCommand, "退出") 
            { 
                InputGestureText = "Alt+F4",
                Icon = new SymbolIcon { Symbol = SymbolRegular.Power24 }
            });

            AdvancedMenuItems.Add(fileMenu);
        }

        /// <summary>
        /// 初始化自定义菜单项
        /// </summary>
        private void InitializeCustomMenuItems()
        {
            // 初始时为空，用户可以动态添加
        }

        /// <summary>
        /// 初始化 WPF-UI 原生菜单项
        /// </summary>
        private void InitializeWpfUiMenuItems()
        {
            // 文件菜单
            var fileMenu = new MenuItemModel("File", MenuItemClickCommand, "File")
            {
                Icon = new SymbolIcon { Symbol = SymbolRegular.Document24 }
            };
            fileMenu.AddChild(new MenuItemModel("New", MenuItemClickCommand, "New") { InputGestureText = "Ctrl+N" });
            fileMenu.AddChild(new MenuItemModel("Open...", MenuItemClickCommand, "Open") { InputGestureText = "Ctrl+O" });
            fileMenu.AddChild(new MenuItemModel("Save", MenuItemClickCommand, "Save") { InputGestureText = "Ctrl+S" });
            fileMenu.AddChild(new MenuItemModel("Save As...", MenuItemClickCommand, "SaveAs") { InputGestureText = "Ctrl+Shift+S" });
            fileMenu.AddChild(new MenuItemModel { IsSeparator = true });
            fileMenu.AddChild(new MenuItemModel("Exit", MenuItemClickCommand, "Exit"));

            // 编辑菜单
            var editMenu = new MenuItemModel("Edit", MenuItemClickCommand, "Edit")
            {
                Icon = new SymbolIcon { Symbol = SymbolRegular.DocumentEdit20 }
            };
            editMenu.AddChild(new MenuItemModel("Undo", MenuItemClickCommand, "Undo") { InputGestureText = "Ctrl+Z" });
            editMenu.AddChild(new MenuItemModel { IsSeparator = true });
            editMenu.AddChild(new MenuItemModel("Cut", MenuItemClickCommand, "Cut") { InputGestureText = "Ctrl+X" });
            editMenu.AddChild(new MenuItemModel("Copy", MenuItemClickCommand, "Copy") { InputGestureText = "Ctrl+C" });
            editMenu.AddChild(new MenuItemModel("Paste", MenuItemClickCommand, "Paste") { InputGestureText = "Ctrl+V" });
            editMenu.AddChild(new MenuItemModel { IsSeparator = true });
            editMenu.AddChild(new MenuItemModel("Word wrap", MenuItemClickCommand, "WordWrap")
            {
                InputGestureText = "Ctrl+Shift+W",
                IsChecked = true
            });
            editMenu.AddChild(new MenuItemModel("Find...", MenuItemClickCommand, "Find") { InputGestureText = "Ctrl+F" });
            editMenu.AddChild(new MenuItemModel("Find next", MenuItemClickCommand, "FindNext") { InputGestureText = "F3" });
            editMenu.AddChild(new MenuItemModel { IsSeparator = true });
            editMenu.AddChild(new MenuItemModel("Select All", MenuItemClickCommand, "SelectAll") { InputGestureText = "Ctrl+A" });

            WpfUiMenuItems.Add(fileMenu);
            WpfUiMenuItems.Add(editMenu);

            // 分隔符（在工具栏中使用）
            WpfUiMenuItems.Add(new MenuItemModel { IsSeparator = true });

            // 工具按钮
            WpfUiMenuItems.Add(new MenuItemModel("Bold", MenuItemClickCommand, "Bold")
            {
                Icon = new SymbolIcon { Symbol = SymbolRegular.TextBold20 }
            });
            WpfUiMenuItems.Add(new MenuItemModel("Italic", MenuItemClickCommand, "Italic")
            {
                Icon = new SymbolIcon { Symbol = SymbolRegular.TextItalic20 }
            });
            WpfUiMenuItems.Add(new MenuItemModel("Underline", MenuItemClickCommand, "Underline")
            {
                Icon = new SymbolIcon { Symbol = SymbolRegular.TextUnderline20 }
            });
        }

        /// <summary>
        /// 初始化代码示例
        /// </summary>
        private void InitializeCodeExamples()
        {
            // 基础 XAML 示例
            BasicMenuBarXamlExample = @"<!-- 基础 MenuBar 使用示例 -->
<zylo:MenuBarControl MenuItems=""{Binding BasicMenuItems}""
                     MenuItemClickCommand=""{Binding MenuItemClickCommand}""
                     IsMenuEnabled=""{Binding IsMenuBarEnabled}""
                     MenuHeight=""40""/>";

            // 基础 C# 示例
            BasicMenuBarCSharpExample = @"// 基础 MenuBar ViewModel 实现
public partial class MenuBarPageViewModel : ObservableObject
{
    [ObservableProperty]
    public partial ObservableCollection<MenuItemModel> BasicMenuItems { get; set; } = new();

    [RelayCommand]
    private void MenuItemClick(string? parameter)
    {
        // 处理菜单项点击
        Debug.WriteLine($""菜单项点击: {parameter}"");
    }

    private void InitializeBasicMenuItems()
    {
        var fileMenu = new MenuItemModel(""文件(_F)"");
        fileMenu.AddChild(new MenuItemModel(""新建(_N)"", MenuItemClickCommand, ""新建"")
        {
            InputGestureText = ""Ctrl+N""
        });
        BasicMenuItems.Add(fileMenu);
    }
}";

            // 高级 XAML 示例
            AdvancedMenuBarXamlExample = @"<!-- 高级 MenuBar 使用示例 -->
<zylo:MenuBarControl MenuItems=""{Binding AdvancedMenuItems}""
                     MenuItemClickCommand=""{Binding MenuItemClickCommand}""
                     IsMenuEnabled=""{Binding IsMenuBarEnabled}""
                     MenuHeight=""40"">
    <!-- 自定义样式 -->
    <zylo:MenuBarControl.Resources>
        <Style TargetType=""MenuItem"" BasedOn=""{StaticResource TopLevelMenuItemStyle}"">
            <Setter Property=""FontWeight"" Value=""SemiBold""/>
            <Setter Property=""Foreground"" Value=""{DynamicResource AccentTextFillColorPrimaryBrush}""/>
        </Style>
    </zylo:MenuBarControl.Resources>
</zylo:MenuBarControl>";

            // 高级 C# 示例
            AdvancedMenuBarCSharpExample = @"// 高级 MenuBar 功能实现
private void InitializeAdvancedMenuItems()
{
    // 带图标的文件菜单
    var fileMenu = new MenuItemModel(""文件(_F)"")
    {
        Icon = new SymbolIcon { Symbol = SymbolRegular.Document24 }
    };

    // 带图标的子菜单项
    fileMenu.AddChild(new MenuItemModel(""新建(_N)"", MenuItemClickCommand, ""新建"")
    {
        InputGestureText = ""Ctrl+N"",
        Icon = new SymbolIcon { Symbol = SymbolRegular.DocumentAdd24 }
    });

    // 多级子菜单
    var recentMenu = new MenuItemModel(""最近文件(_R)"")
    {
        Icon = new SymbolIcon { Symbol = SymbolRegular.History24 }
    };
    recentMenu.AddChild(new MenuItemModel(""文档1.txt"", MenuItemClickCommand, ""文档1""));
    recentMenu.AddChild(new MenuItemModel(""文档2.txt"", MenuItemClickCommand, ""文档2""));
    fileMenu.AddChild(recentMenu);

    AdvancedMenuItems.Add(fileMenu);
}";

            // WPF-UI 原生 Menu XAML 示例（静态版本）
            WpfUiMenuXamlExample = @"<!-- WPF-UI 原生 Menu 控件（静态声明） -->
<Menu FontSize=""14"">
    <ui:MenuItem Header=""File"" Icon=""{ui:SymbolIcon Symbol=Document24}"">
        <ui:MenuItem Header=""New"" InputGestureText=""CTRL+N""
                     Command=""{Binding MenuItemClickCommand}""
                     CommandParameter=""New""/>
        <ui:MenuItem Header=""Open..."" InputGestureText=""CTRL+O""
                     Command=""{Binding MenuItemClickCommand}""
                     CommandParameter=""Open""/>
        <Separator />
        <ui:MenuItem Header=""Exit""
                     Command=""{Binding MenuItemClickCommand}""
                     CommandParameter=""Exit""/>
    </ui:MenuItem>
</Menu>

<!-- WPF-UI 原生 Menu 控件（数据绑定版本） -->
<Menu FontSize=""14"">
    <Menu.Resources>
        <HierarchicalDataTemplate DataType=""{x:Type models:MenuItemModel}""
                                  ItemsSource=""{Binding Children}"">
            <StackPanel Orientation=""Horizontal"">
                <ContentPresenter Content=""{Binding Icon}"" Margin=""0,0,8,0""/>
                <TextBlock Text=""{Binding Header}"" VerticalAlignment=""Center""/>
                <TextBlock Text=""{Binding InputGestureText}""
                           Margin=""20,0,0,0""
                           VerticalAlignment=""Center""
                           Foreground=""{DynamicResource TextFillColorSecondaryBrush}""
                           FontSize=""12""/>
            </StackPanel>
        </HierarchicalDataTemplate>
    </Menu.Resources>

    <Menu.ItemsSource>
        <Binding Path=""WpfUiMenuItems""/>
    </Menu.ItemsSource>

    <Menu.ItemContainerStyle>
        <Style TargetType=""MenuItem"">
            <Setter Property=""Command"" Value=""{Binding Command}""/>
            <Setter Property=""CommandParameter"" Value=""{Binding CommandParameter}""/>
            <Setter Property=""IsEnabled"" Value=""{Binding IsEnabled}""/>
            <Style.Triggers>
                <DataTrigger Binding=""{Binding IsSeparator}"" Value=""True"">
                    <Setter Property=""Template"">
                        <Setter.Value>
                            <ControlTemplate TargetType=""MenuItem"">
                                <Separator Margin=""0,2""/>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </Menu.ItemContainerStyle>
</Menu>";

            // WPF-UI 原生 Menu C# 示例
            WpfUiMenuCSharpExample = @"// WPF-UI 原生 Menu 数据绑定实现
public partial class MenuBarPageViewModel : ObservableObject
{
    [ObservableProperty]
    public partial ObservableCollection<MenuItemModel> WpfUiMenuItems { get; set; } = new();

    [RelayCommand]
    private void MenuItemClick(string? parameter)
    {
        // 处理菜单项点击
        Debug.WriteLine($""菜单项点击: {parameter}"");
    }

    private void InitializeWpfUiMenuItems()
    {
        var fileMenu = new MenuItemModel(""File"", MenuItemClickCommand, ""File"")
        {
            Icon = new SymbolIcon { Symbol = SymbolRegular.Document24 }
        };
        fileMenu.AddChild(new MenuItemModel(""New"", MenuItemClickCommand, ""New"")
        {
            InputGestureText = ""Ctrl+N""
        });
        WpfUiMenuItems.Add(fileMenu);
    }
}";
        }

        #endregion
    }
}
