using System.Collections.ObjectModel;
using System.Windows.Media;
using Prism.Commands;
using Prism.Mvvm;
using Zylo.WPF.Services;
using ThemeConfiguration = Zylo.WPF.Models.ThemeConfiguration;

namespace WPFTest.ViewModels;

/// <summary>
/// 主题控件ViewModel - 符合Prism框架规范
/// </summary>
public class ThemeControlViewModel : BindableBase
{
    public readonly YLoggerInstance _logger = YLogger.ForSilent<ThemeControlViewModel>();
    
    private readonly IThemeManagementService _themeService;

    private string _selectedThemeMode = "Auto";
    private string _selectedAccentColor = "#0078D4";
    private bool _useCustomAccentColor = false;
    private string _statusMessage = "";
    private bool _isApplying = false;

    /// <summary>
    /// 选中的主题模式
    /// </summary>
    public string SelectedThemeMode
    {
        get => _selectedThemeMode;
        set => SetProperty(ref _selectedThemeMode, value);
    }

    /// <summary>
    /// 选中的强调色
    /// </summary>
    public string SelectedAccentColor
    {
        get => _selectedAccentColor;
        set => SetProperty(ref _selectedAccentColor, value);
    }

    /// <summary>
    /// 是否使用自定义强调色
    /// </summary>
    public bool UseCustomAccentColor
    {
        get => _useCustomAccentColor;
        set => SetProperty(ref _useCustomAccentColor, value);
    }

    /// <summary>
    /// 状态消息
    /// </summary>
    public string StatusMessage
    {
        get => _statusMessage;
        set => SetProperty(ref _statusMessage, value);
    }

    /// <summary>
    /// 是否正在应用主题
    /// </summary>
    public bool IsApplying
    {
        get => _isApplying;
        set => SetProperty(ref _isApplying, value);
    }



    /// <summary>
    /// 主题模式选项
    /// </summary>
    public ObservableCollection<ThemeModeOption> ThemeModeOptions { get; }

    /// <summary>
    /// 预定义强调色选项
    /// </summary>
    public ObservableCollection<AccentColorOption> AccentColorOptions { get; }

    /// <summary>
    /// 应用主题命令
    /// </summary>
    public DelegateCommand ApplyThemeCommand { get; }

    /// <summary>
    /// 重置主题命令
    /// </summary>
    public DelegateCommand ResetThemeCommand { get; }



    /// <summary>
    /// 更新主题模式命令
    /// </summary>
    public DelegateCommand<string> UpdateThemeModeCommand { get; }

    /// <summary>
    /// 选择强调色命令
    /// </summary>
    public DelegateCommand<AccentColorOption> SelectAccentColorCommand { get; }

    public ThemeControlViewModel(IThemeManagementService themeService)
    {
        _themeService = themeService;

        // 初始化选项集合
        ThemeModeOptions = new ObservableCollection<ThemeModeOption>();
        AccentColorOptions = new ObservableCollection<AccentColorOption>();

        // 初始化Prism命令
        ApplyThemeCommand = new DelegateCommand(async () => await ApplyThemeAsync());
        ResetThemeCommand = new DelegateCommand(async () => await ResetThemeAsync());
        UpdateThemeModeCommand = new DelegateCommand<string>(async (mode) => await UpdateThemeModeAsync(mode));
        SelectAccentColorCommand = new DelegateCommand<AccentColorOption>(SelectAccentColor);

        // 加载数据
        LoadThemeModeOptions();
        LoadAccentColorOptions();
        LoadCurrentConfiguration();

        // 订阅主题变更事件
        _themeService.ThemeConfigurationChanged += OnThemeConfigurationChanged;
    }

    /// <summary>
    /// 加载主题模式选项
    /// </summary>
    private void LoadThemeModeOptions()
    {
        ThemeModeOptions.Clear();

        foreach (var option in ThemeConfiguration.ThemeModeOptions)
        {
            ThemeModeOptions.Add(new ThemeModeOption
            {
                DisplayName = option.Key,
                Value = option.Value
            });
        }
    }

    /// <summary>
    /// 加载强调色选项
    /// </summary>
    private void LoadAccentColorOptions()
    {
        AccentColorOptions.Clear();

        foreach (var option in ThemeConfiguration.PredefinedAccentColors)
        {
            AccentColorOptions.Add(new AccentColorOption
            {
                Name = option.Key,
                ColorValue = option.Value,
                ColorBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString(option.Value))
            });
        }
    }



    /// <summary>
    /// 加载当前配置
    /// </summary>
    private void LoadCurrentConfiguration()
    {
        try
        {
            var config = _themeService.CurrentConfiguration;
            SelectedThemeMode = config.ThemeMode;
            SelectedAccentColor = config.AccentColor;
            UseCustomAccentColor = config.UseCustomAccentColor;

            _logger.Info($"📖 加载当前主题配置: {config.ThemeMode}, {config.AccentColor}");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 加载当前配置失败: {ex.Message}");
            StatusMessage = "❌ 加载配置失败";
        }
    }

    /// <summary>
    /// 应用主题
    /// </summary>
    private async Task ApplyThemeAsync()
    {
        try
        {
            IsApplying = true;
            StatusMessage = "🎨 正在应用主题...";

            // 更新主题模式
            await _themeService.UpdateThemeModeAsync(SelectedThemeMode);

            // 更新强调色
            await _themeService.UpdateAccentColorAsync(SelectedAccentColor, UseCustomAccentColor);

            StatusMessage = "✅ 主题应用成功！";
            _logger.Info($"✅ 主题应用成功: {SelectedThemeMode}, {SelectedAccentColor}");

            // 清除状态消息
            await Task.Delay(2000);
            if (StatusMessage == "✅ 主题应用成功！")
            {
                StatusMessage = "";
            }
        }
        catch (Exception ex)
        {
            StatusMessage = "❌ 主题应用失败";
            _logger.Error($"❌ 应用主题失败: {ex.Message}");
        }
        finally
        {
            IsApplying = false;
        }
    }

    /// <summary>
    /// 重置主题
    /// </summary>
    private async Task ResetThemeAsync()
    {
        try
        {
            IsApplying = true;
            StatusMessage = "🔄 正在重置主题...";

            await _themeService.ResetToDefaultAsync();

            StatusMessage = "✅ 主题重置成功！";
            _logger.Info("✅ 主题重置成功");

            // 清除状态消息
            await Task.Delay(2000);
            if (StatusMessage == "✅ 主题重置成功！")
            {
                StatusMessage = "";
            }
        }
        catch (Exception ex)
        {
            StatusMessage = "❌ 主题重置失败";
            _logger.Error($"❌ 重置主题失败: {ex.Message}");
        }
        finally
        {
            IsApplying = false;
        }
    }



    /// <summary>
    /// 更新主题模式 - 点击即切换
    /// </summary>
    private async Task UpdateThemeModeAsync(string? themeMode)
    {
        if (!string.IsNullOrEmpty(themeMode))
        {
            SelectedThemeMode = themeMode;

            // 直接调用ThemeService更新主题模式，立即生效
            var success = await _themeService.UpdateThemeModeAsync(themeMode);
            if (success)
            {
                var modeDisplayName = themeMode switch
                {
                    "Light" => "浅色模式",
                    "Dark" => "深色模式",
                    "Auto" => "自动模式",
                    _ => themeMode
                };

                StatusMessage = $"✅ {modeDisplayName}";
                _logger.Info($"✅ 主题已切换为: {modeDisplayName}");

                // 快速清除状态消息
                await Task.Delay(1000);
                if (StatusMessage == $"✅ {modeDisplayName}")
                {
                    StatusMessage = "";
                }
            }
            else
            {
                StatusMessage = "❌ 切换失败";
                _logger.Error($"❌ 主题模式切换失败: {themeMode}");
            }
        }
    }

    /// <summary>
    /// 选择强调色 - 点击即切换
    /// </summary>
    private async void SelectAccentColor(AccentColorOption? option)
    {
        if (option != null)
        {
            SelectedAccentColor = option.ColorValue;
            UseCustomAccentColor = true;

            // 立即应用强调色，无需额外确认
            var success = await _themeService.UpdateAccentColorAsync(option.ColorValue, true);
            if (success)
            {
                StatusMessage = $"✅ {option.Name}";
                _logger.Info($"🎨 已切换到预定义强调色: {option.Name} - {option.ColorValue}");

                // 快速清除状态消息
                await Task.Delay(1000);
                if (StatusMessage == $"✅ {option.Name}")
                {
                    StatusMessage = "";
                }
            }
            else
            {
                StatusMessage = "❌ 切换失败";
                _logger.Error($"❌ 强调色切换失败: {option.ColorValue}");
            }
        }
    }

    /// <summary>
    /// 主题配置变更事件处理
    /// </summary>
    private void OnThemeConfigurationChanged(object? sender, EventArgs e)
    {
        // 在UI线程上更新属性
        App.Current.Dispatcher.Invoke(() =>
        {
            var configuration = _themeService.CurrentConfiguration;
            SelectedThemeMode = configuration.ThemeMode;
            SelectedAccentColor = configuration.AccentColor;

            _logger.Info($"🔄 主题配置已更新: {configuration.ThemeMode}, {configuration.AccentColor}");
        });
    }
}

/// <summary>
/// 主题模式选项
/// </summary>
public class ThemeModeOption
{
    public string DisplayName { get; set; } = "";
    public string Value { get; set; } = "";
}

/// <summary>
/// 强调色选项
/// </summary>
public class AccentColorOption
{
    public string Name { get; set; } = "";
    public string ColorValue { get; set; } = "";
    public SolidColorBrush ColorBrush { get; set; } = new(Colors.Blue);
}
