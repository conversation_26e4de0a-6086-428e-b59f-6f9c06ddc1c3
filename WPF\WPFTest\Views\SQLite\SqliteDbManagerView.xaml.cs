using System.Globalization;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Media;
using WPFTest.Models.SQLite;

namespace WPFTest.Views.SQLite;

/// <summary>
/// SqliteDbManagerView.xaml 的交互逻辑
/// </summary>
public partial class SqliteDbManagerView : UserControl
{
    public SqliteDbManagerView()
    {
        InitializeComponent();
    }
}

/// <summary>
/// 连接状态到颜色的转换器
/// </summary>
public class ConnectionStatusToColorConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is SqliteConnectionStatus status)
        {
            return status switch
            {
                SqliteConnectionStatus.Connected => new SolidColorBrush(Color.FromRgb(40, 167, 69)),      // 绿色
                SqliteConnectionStatus.Disconnected => new SolidColorBrush(Color.FromRgb(220, 53, 69)),   // 红色
                SqliteConnectionStatus.Error => new SolidColorBrush(Color.FromRgb(255, 193, 7)),          // 黄色
                SqliteConnectionStatus.Testing => new SolidColorBrush(Color.FromRgb(0, 123, 255)),        // 蓝色
                _ => new SolidColorBrush(Color.FromRgb(108, 117, 125))                                     // 灰色
            };
        }

        return new SolidColorBrush(Color.FromRgb(108, 117, 125));
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 连接状态到图标的转换器
/// </summary>
public class ConnectionStatusToIconConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is SqliteConnectionStatus status)
        {
            return status switch
            {
                SqliteConnectionStatus.Connected => "✓",
                SqliteConnectionStatus.Disconnected => "✗",
                SqliteConnectionStatus.Error => "⚠",
                SqliteConnectionStatus.Testing => "⟳",
                _ => "?"
            };
        }

        return "?";
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 文件大小转换器
/// </summary>
public class FileSizeConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is long bytes)
        {
            return FormatFileSize(bytes);
        }

        return "0 B";
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }

    private static string FormatFileSize(long bytes)
    {
        if (bytes == 0) return "0 B";

        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        int order = 0;
        double size = bytes;

        while (size >= 1024 && order < sizes.Length - 1)
        {
            order++;
            size /= 1024;
        }

        return $"{size:0.##} {sizes[order]}";
    }
}
