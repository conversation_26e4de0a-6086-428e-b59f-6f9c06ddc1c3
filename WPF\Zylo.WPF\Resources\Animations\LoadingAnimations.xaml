<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- ⏳ 加载动画专用 -->
    
    <!-- ========================================= -->
    <!-- 🔄 旋转加载器 -->
    <!-- ========================================= -->
    
    <!-- 标准旋转加载器 -->
    <Storyboard x:Key="ZyloLoadingSpinAnimation" RepeatBehavior="Forever">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(RotateTransform.Angle)"
                       From="0" To="360" Duration="0:0:1">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseInOut" Power="1"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 快速旋转加载器 -->
    <Storyboard x:Key="ZyloLoadingSpinFastAnimation" RepeatBehavior="Forever">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(RotateTransform.Angle)"
                       From="0" To="360" Duration="0:0:0.6"/>
    </Storyboard>

    <!-- 慢速旋转加载器 -->
    <Storyboard x:Key="ZyloLoadingSpinSlowAnimation" RepeatBehavior="Forever">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(RotateTransform.Angle)"
                       From="0" To="360" Duration="0:0:1.5"/>
    </Storyboard>

    <!-- ========================================= -->
    <!-- 💫 脉冲加载器 -->
    <!-- ========================================= -->
    
    <!-- 标准脉冲动画 -->
    <Storyboard x:Key="ZyloLoadingPulseAnimation" RepeatBehavior="Forever">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                       From="0.8" To="1.2" Duration="0:0:1"
                       AutoReverse="True">
            <DoubleAnimation.EasingFunction>
                <SineEase EasingMode="EaseInOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                       From="0.8" To="1.2" Duration="0:0:1"
                       AutoReverse="True">
            <DoubleAnimation.EasingFunction>
                <SineEase EasingMode="EaseInOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="0.5" To="1" Duration="0:0:1"
                       AutoReverse="True">
            <DoubleAnimation.EasingFunction>
                <SineEase EasingMode="EaseInOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 快速脉冲动画 -->
    <Storyboard x:Key="ZyloLoadingPulseFastAnimation" RepeatBehavior="Forever">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                       From="0.9" To="1.1" Duration="0:0:0.5"
                       AutoReverse="True">
            <DoubleAnimation.EasingFunction>
                <SineEase EasingMode="EaseInOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                       From="0.9" To="1.1" Duration="0:0:0.5"
                       AutoReverse="True">
            <DoubleAnimation.EasingFunction>
                <SineEase EasingMode="EaseInOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- ========================================= -->
    <!-- 🌊 波浪加载器 -->
    <!-- ========================================= -->
    
    <!-- 三点波浪动画 - 需要三个元素 Dot1, Dot2, Dot3 -->
    <Storyboard x:Key="ZyloLoadingWaveAnimation" RepeatBehavior="Forever">
        <!-- 第一个点 -->
        <DoubleAnimation Storyboard.TargetName="Dot1"
                       Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                       From="0" To="-10" Duration="0:0:0.4"
                       AutoReverse="True">
            <DoubleAnimation.EasingFunction>
                <SineEase EasingMode="EaseInOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        
        <!-- 第二个点 - 延迟 0.1 秒 -->
        <DoubleAnimation Storyboard.TargetName="Dot2"
                       Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                       From="0" To="-10" Duration="0:0:0.4"
                       BeginTime="0:0:0.1"
                       AutoReverse="True">
            <DoubleAnimation.EasingFunction>
                <SineEase EasingMode="EaseInOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        
        <!-- 第三个点 - 延迟 0.2 秒 -->
        <DoubleAnimation Storyboard.TargetName="Dot3"
                       Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                       From="0" To="-10" Duration="0:0:0.4"
                       BeginTime="0:0:0.2"
                       AutoReverse="True">
            <DoubleAnimation.EasingFunction>
                <SineEase EasingMode="EaseInOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- ========================================= -->
    <!-- 📊 进度条动画 -->
    <!-- ========================================= -->
    
    <!-- 进度条填充动画 -->
    <Storyboard x:Key="ZyloProgressFillAnimation">
        <DoubleAnimation Storyboard.TargetProperty="Width"
                       From="0" Duration="0:0:2">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 不确定进度条动画 -->
    <Storyboard x:Key="ZyloProgressIndeterminateAnimation" RepeatBehavior="Forever">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                       From="-100" To="300" Duration="0:0:1.5">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseInOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- ========================================= -->
    <!-- ✨ 闪烁加载器 -->
    <!-- ========================================= -->
    
    <!-- 标准闪烁动画 -->
    <Storyboard x:Key="ZyloLoadingBlinkAnimation" RepeatBehavior="Forever">
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="0.3" To="1" Duration="0:0:0.8"
                       AutoReverse="True">
            <DoubleAnimation.EasingFunction>
                <SineEase EasingMode="EaseInOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 快速闪烁动画 -->
    <Storyboard x:Key="ZyloLoadingBlinkFastAnimation" RepeatBehavior="Forever">
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="0.5" To="1" Duration="0:0:0.3"
                       AutoReverse="True">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseInOut" Power="1"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- ========================================= -->
    <!-- 🔄 组合加载动画 -->
    <!-- ========================================= -->
    
    <!-- 旋转 + 脉冲组合动画 -->
    <Storyboard x:Key="ZyloLoadingSpinPulseAnimation" RepeatBehavior="Forever">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(RotateTransform.Angle)"
                       From="0" To="360" Duration="0:0:2"/>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(ScaleTransform.ScaleX)"
                       From="0.8" To="1.2" Duration="0:0:1"
                       AutoReverse="True">
            <DoubleAnimation.EasingFunction>
                <SineEase EasingMode="EaseInOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(ScaleTransform.ScaleY)"
                       From="0.8" To="1.2" Duration="0:0:1"
                       AutoReverse="True">
            <DoubleAnimation.EasingFunction>
                <SineEase EasingMode="EaseInOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 骨架屏加载动画 -->
    <Storyboard x:Key="ZyloSkeletonLoadingAnimation" RepeatBehavior="Forever">
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="0.4" To="0.8" Duration="0:0:1"
                       AutoReverse="True">
            <DoubleAnimation.EasingFunction>
                <SineEase EasingMode="EaseInOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                       From="-50" To="50" Duration="0:0:1.5">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseInOut" Power="1"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

</ResourceDictionary>
