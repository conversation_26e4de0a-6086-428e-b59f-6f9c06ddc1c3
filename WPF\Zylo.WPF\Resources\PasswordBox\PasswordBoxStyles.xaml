<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml">

    <!-- PasswordBox 基础样式 -->
    <Style x:Key="PasswordBoxBaseStyle" TargetType="PasswordBox">
        <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontFamily" Value="{DynamicResource ContentControlThemeFontFamily}"/>
        <Setter Property="PasswordChar" Value="●"/>
        <Setter Property="MaxLength" Value="128"/>
        <Setter Property="AllowDrop" Value="False"/>
        <Setter Property="ContextMenu" Value="{x:Null}"/>

        <!-- 添加鼠标悬停和聚焦效果 -->
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorSecondaryBrush}"/>
            </Trigger>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="Background" Value="{DynamicResource ControlFillColorInputActiveBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource SystemAccentColorPrimaryBrush}"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Opacity" Value="0.5"/>
                <Setter Property="Background" Value="{DynamicResource ControlFillColorDisabledBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- PasswordBox 标准样式 -->
    <Style x:Key="PasswordBoxStyle" TargetType="PasswordBox" BasedOn="{StaticResource PasswordBoxBaseStyle}">
        <!-- 标准样式的特定设置 -->
        <Setter Property="MinHeight" Value="32"/>
        <Setter Property="HorizontalContentAlignment" Value="Left"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
    </Style>

    <!-- PasswordBox 小型样式 -->
    <Style x:Key="SmallPasswordBoxStyle" TargetType="PasswordBox" BasedOn="{StaticResource PasswordBoxBaseStyle}">
        <Setter Property="Padding" Value="6"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="MinHeight" Value="24"/>
    </Style>

    <!-- PasswordBox 大型样式 -->
    <Style x:Key="LargePasswordBoxStyle" TargetType="PasswordBox" BasedOn="{StaticResource PasswordBoxBaseStyle}">
        <Setter Property="Padding" Value="12"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="MinHeight" Value="40"/>
    </Style>

    <!-- PasswordBox 透明样式 -->
    <Style x:Key="TransparentPasswordBoxStyle" TargetType="PasswordBox" BasedOn="{StaticResource PasswordBoxBaseStyle}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0,0,0,2"/>
        <Setter Property="Padding" Value="4,8"/>

        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorSecondaryBrush}"/>
            </Trigger>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="BorderBrush" Value="{DynamicResource SystemAccentColorPrimaryBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- PasswordBox 圆角样式 -->
    <Style x:Key="RoundedPasswordBoxStyle" TargetType="PasswordBox" BasedOn="{StaticResource PasswordBoxBaseStyle}">
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="BorderThickness" Value="2"/>

        <Style.Triggers>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="BorderThickness" Value="3"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- PasswordBox 安全增强样式 -->
    <Style x:Key="SecurePasswordBoxStyle" TargetType="PasswordBox" BasedOn="{StaticResource PasswordBoxBaseStyle}">
        <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource SystemAccentColorPrimaryBrush}"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="PasswordChar" Value="■"/>
        <Setter Property="MaxLength" Value="64"/>

        <!-- 完全禁用上下文菜单和拖放 -->
        <Setter Property="AllowDrop" Value="False"/>
        <Setter Property="ContextMenu" Value="{x:Null}"/>

        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource SystemAccentColorSecondaryBrush}"/>
            </Trigger>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="Background" Value="{DynamicResource ControlFillColorInputActiveBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                <Setter Property="BorderThickness" Value="3"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- PasswordBox 验证错误样式 -->
    <Style x:Key="ErrorPasswordBoxStyle" TargetType="PasswordBox" BasedOn="{StaticResource PasswordBoxBaseStyle}">
        <Setter Property="BorderBrush" Value="{DynamicResource SystemFillColorCriticalBrush}"/>
        <Setter Property="BorderThickness" Value="2"/>
        
        <Style.Triggers>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="BorderBrush" Value="{DynamicResource SystemFillColorCriticalBrush}"/>
                <Setter Property="BorderThickness" Value="3"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- PasswordBox 成功验证样式 -->
    <Style x:Key="SuccessPasswordBoxStyle" TargetType="PasswordBox" BasedOn="{StaticResource PasswordBoxBaseStyle}">
        <Setter Property="BorderBrush" Value="{DynamicResource SystemFillColorSuccessBrush}"/>
        <Setter Property="BorderThickness" Value="2"/>
        
        <Style.Triggers>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="BorderBrush" Value="{DynamicResource SystemFillColorSuccessBrush}"/>
                <Setter Property="BorderThickness" Value="3"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 密码显示用的 TextBox 样式 - 与 PasswordBox 保持一致 -->
    <Style x:Key="PasswordDisplayTextBoxStyle" TargetType="ui:TextBox">
        <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontFamily" Value="{DynamicResource ContentControlThemeFontFamily}"/>
        <Setter Property="MinHeight" Value="32"/>
        <Setter Property="HorizontalContentAlignment" Value="Left"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>

        <!-- 添加与 PasswordBox 相同的鼠标悬停和聚焦效果 -->
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorSecondaryBrush}"/>
            </Trigger>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="Background" Value="{DynamicResource ControlFillColorInputActiveBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource SystemAccentColorPrimaryBrush}"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Opacity" Value="0.5"/>
                <Setter Property="Background" Value="{DynamicResource ControlFillColorDisabledBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

</ResourceDictionary>
