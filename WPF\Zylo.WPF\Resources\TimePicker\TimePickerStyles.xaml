<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml">

    <!-- TimePicker 基础样式 -->
    <Style x:Key="TimePickerBaseStyle" TargetType="ui:TimePicker">
        <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontFamily" Value="{DynamicResource ContentControlThemeFontFamily}"/>
        <Setter Property="HorizontalContentAlignment" Value="Left"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="MinHeight" Value="32"/>
        <Setter Property="MinWidth" Value="120"/>

        <!-- 添加鼠标悬停和聚焦效果 -->
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorSecondaryBrush}"/>
            </Trigger>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="Background" Value="{DynamicResource ControlFillColorInputActiveBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                <Setter Property="BorderThickness" Value="2"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Opacity" Value="0.5"/>
                <Setter Property="Background" Value="{DynamicResource ControlFillColorDisabledBrush}"/>
                <Setter Property="Foreground" Value="{DynamicResource TextFillColorDisabledBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- TimePicker 标准样式 -->
    <Style x:Key="TimePickerStyle" TargetType="ui:TimePicker" BasedOn="{StaticResource TimePickerBaseStyle}">
        <!-- 标准样式的特定设置 -->
        <Setter Property="Height" Value="36"/>
        <Setter Property="FontSize" Value="14"/>
    </Style>

    <!-- TimePicker 小型样式 -->
    <Style x:Key="SmallTimePickerStyle" TargetType="ui:TimePicker" BasedOn="{StaticResource TimePickerBaseStyle}">
        <Setter Property="Padding" Value="6"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Height" Value="28"/>
        <Setter Property="MinHeight" Value="28"/>
        <Setter Property="MinWidth" Value="100"/>
    </Style>

    <!-- TimePicker 大型样式 -->
    <Style x:Key="LargeTimePickerStyle" TargetType="ui:TimePicker" BasedOn="{StaticResource TimePickerBaseStyle}">
        <Setter Property="Padding" Value="12"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="Height" Value="44"/>
        <Setter Property="MinHeight" Value="44"/>
        <Setter Property="MinWidth" Value="140"/>
    </Style>

    <!-- TimePicker 紧凑样式 -->
    <Style x:Key="CompactTimePickerStyle" TargetType="ui:TimePicker" BasedOn="{StaticResource TimePickerBaseStyle}">
        <Setter Property="Padding" Value="4"/>
        <Setter Property="FontSize" Value="11"/>
        <Setter Property="Height" Value="24"/>
        <Setter Property="MinHeight" Value="24"/>
        <Setter Property="MinWidth" Value="90"/>
        <Setter Property="Margin" Value="2"/>
    </Style>

    <!-- TimePicker 透明样式 -->
    <Style x:Key="TransparentTimePickerStyle" TargetType="ui:TimePicker" BasedOn="{StaticResource TimePickerBaseStyle}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}"/>
            </Trigger>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="Background" Value="{DynamicResource ControlFillColorInputActiveBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- TimePicker 强调色样式 -->
    <Style x:Key="AccentTimePickerStyle" TargetType="ui:TimePicker" BasedOn="{StaticResource TimePickerBaseStyle}">
        <Setter Property="BorderBrush" Value="{DynamicResource SystemAccentColorPrimaryBrush}"/>
        <Setter Property="BorderThickness" Value="2"/>
        
        <Style.Triggers>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="Background" Value="{DynamicResource AccentFillColorSecondaryBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource SystemAccentColorSecondaryBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- TimePicker 圆角样式 -->
    <Style x:Key="RoundedTimePickerStyle" TargetType="ui:TimePicker" BasedOn="{StaticResource TimePickerBaseStyle}">
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="Height" Value="36"/>
    </Style>

    <!-- TimePicker 工作时间样式 -->
    <Style x:Key="WorkTimePickerStyle" TargetType="ui:TimePicker" BasedOn="{StaticResource TimePickerBaseStyle}">
        <Setter Property="Background" Value="#1A6B46C1"/>
        <Setter Property="BorderBrush" Value="#FF6B46C1"/>
        <Setter Property="Foreground" Value="#FF6B46C1"/>
        <Setter Property="FontWeight" Value="Medium"/>
        
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#2A6B46C1"/>
            </Trigger>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="Background" Value="#3A6B46C1"/>
                <Setter Property="BorderThickness" Value="2"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- TimePicker 提醒时间样式 -->
    <Style x:Key="ReminderTimePickerStyle" TargetType="ui:TimePicker" BasedOn="{StaticResource TimePickerBaseStyle}">
        <Setter Property="Background" Value="#1A10B981"/>
        <Setter Property="BorderBrush" Value="#FF10B981"/>
        <Setter Property="Foreground" Value="#FF10B981"/>
        <Setter Property="FontWeight" Value="Medium"/>
        
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#2A10B981"/>
            </Trigger>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="Background" Value="#3A10B981"/>
                <Setter Property="BorderThickness" Value="2"/>
            </Trigger>
        </Style.Triggers>
    </Style>

</ResourceDictionary>
