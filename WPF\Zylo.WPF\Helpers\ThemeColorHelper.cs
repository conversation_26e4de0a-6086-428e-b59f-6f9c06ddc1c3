using System;
using System.Windows;
using System.Windows.Media;

namespace Zylo.WPF.Helpers
{
    /// <summary>
    /// 主题颜色帮助类 - 提供通用的主题颜色获取方法
    /// </summary>
    public static class ThemeColorHelper
    {
        /// <summary>
        /// 根据字符串获取主题颜色
        /// </summary>
        /// <param name="text">输入文本</param>
        /// <param name="fallbackColor">备用颜色</param>
        /// <returns>主题颜色画刷</returns>
        public static Brush GetThemeColorFromString(string text, Color? fallbackColor = null)
        {
            if (string.IsNullOrEmpty(text))
            {
                return GetFallbackBrush(fallbackColor);
            }

            var hash = text.GetHashCode();
            var colorIndex = Math.Abs(hash) % 8;

            var resourceKey = colorIndex switch
            {
                0 => "AccentFillColorDefaultBrush",
                1 => "AccentFillColorSecondaryBrush", 
                2 => "SystemFillColorSuccessBrush",
                3 => "SystemFillColorCautionBrush",
                4 => "SystemFillColorCriticalBrush",
                5 => "SystemFillColorNeutralBrush",
                6 => "TextFillColorSecondaryBrush",
                7 => "TextFillColorTertiaryBrush",
                _ => "TextFillColorDisabledBrush"
            };

            return GetResourceBrush(resourceKey, fallbackColor);
        }

        /// <summary>
        /// 获取选中状态的边框颜色
        /// </summary>
        /// <param name="isSelected">是否选中</param>
        /// <returns>边框颜色画刷</returns>
        public static Brush GetSelectionBorderBrush(bool isSelected)
        {
            if (isSelected)
            {
                return GetResourceBrush("AccentFillColorDefaultBrush") 
                       ?? GetResourceBrush("SystemAccentColorPrimary1Brush")
                       ?? GetResourceBrush("ControlStrokeColorSecondaryBrush")
                       ?? new SolidColorBrush(Colors.DodgerBlue);
            }

            return GetResourceBrush("ControlElevationBorderBrush")
                   ?? GetResourceBrush("ControlStrokeColorDefaultBrush") 
                   ?? GetResourceBrush("CardStrokeColorDefaultBrush")
                   ?? new SolidColorBrush(Colors.LightGray);
        }

        /// <summary>
        /// 获取分类颜色
        /// </summary>
        /// <param name="category">分类名称</param>
        /// <returns>分类颜色画刷</returns>
        public static Brush GetCategoryBrush(string category)
        {
            if (string.IsNullOrEmpty(category))
            {
                return GetResourceBrush("TextFillColorTertiaryBrush") 
                       ?? new SolidColorBrush(Colors.Gray);
            }

            // 为常见分类提供固定的主题颜色映射
            var resourceKey = category switch
            {
                "办公" or "Office" => "AccentFillColorDefaultBrush",
                "媒体" or "Media" => "SystemFillColorCautionBrush", 
                "效率" or "Productivity" => "SystemFillColorSuccessBrush",
                "通讯" or "Communication" => "AccentFillColorSecondaryBrush",
                "系统" or "System" => "SystemFillColorCriticalBrush",
                "工具" or "Tools" => "SystemFillColorNeutralBrush",
                _ => null
            };

            if (!string.IsNullOrEmpty(resourceKey))
            {
                return GetResourceBrush(resourceKey) ?? GetThemeColorFromString(category);
            }

            return GetThemeColorFromString(category);
        }

        /// <summary>
        /// 获取资源画刷
        /// </summary>
        /// <param name="resourceKey">资源键</param>
        /// <param name="fallbackColor">备用颜色</param>
        /// <returns>画刷</returns>
        private static Brush? GetResourceBrush(string resourceKey, Color? fallbackColor = null)
        {
            try
            {
                return Application.Current?.FindResource(resourceKey) as Brush;
            }
            catch
            {
                return fallbackColor.HasValue ? new SolidColorBrush(fallbackColor.Value) : null;
            }
        }

        /// <summary>
        /// 获取备用画刷
        /// </summary>
        /// <param name="fallbackColor">备用颜色</param>
        /// <returns>备用画刷</returns>
        private static Brush GetFallbackBrush(Color? fallbackColor = null)
        {
            return GetResourceBrush("TextFillColorTertiaryBrush") 
                   ?? new SolidColorBrush(fallbackColor ?? Colors.Gray);
        }

        /// <summary>
        /// 获取主题适应的文本颜色
        /// </summary>
        /// <param name="isPrimary">是否为主要文本</param>
        /// <returns>文本颜色画刷</returns>
        public static Brush GetTextBrush(bool isPrimary = true)
        {
            var resourceKey = isPrimary ? "TextFillColorPrimaryBrush" : "TextFillColorSecondaryBrush";
            return GetResourceBrush(resourceKey) ?? new SolidColorBrush(isPrimary ? Colors.Black : Colors.Gray);
        }

        /// <summary>
        /// 获取主题适应的背景颜色
        /// </summary>
        /// <param name="isCard">是否为卡片背景</param>
        /// <returns>背景颜色画刷</returns>
        public static Brush GetBackgroundBrush(bool isCard = false)
        {
            var resourceKey = isCard ? "CardBackgroundFillColorDefaultBrush" : "ApplicationBackgroundBrush";
            return GetResourceBrush(resourceKey) ?? new SolidColorBrush(Colors.White);
        }
    }
}
