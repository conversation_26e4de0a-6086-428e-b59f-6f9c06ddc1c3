# 🎬 Zylo.WPF 动画系统完整指南

Zylo.WPF 提供了一个完整、分类清晰的动画系统，包含 **150+ 个专业动画**，覆盖现代 WPF 应用程序的所有动画需求。

## 📁 动画文件结构

```
📁 WPF/Zylo.WPF/Resources/Animations/
├── 🔄 TransitionAnimations.xaml    # 通用过渡动画 (50+ 动画)
├── 🎯 ButtonAnimations.xaml        # 按钮专用动画 (15+ 动画)
├── 🎴 CardAnimations.xaml          # 卡片专用动画 (8+ 动画)
├── 📄 PageAnimations.xaml          # 页面级动画 (专用页面动画)
├── ⏳ LoadingAnimations.xaml       # 加载动画 (20+ 动画)
├── 🔔 NotificationAnimations.xaml  # 通知和提示动画 (25+ 动画)
└── 🧭 NavigationAnimations.xaml    # 导航专用动画 (20+ 动画)
```

## 🎨 动画命名规范

所有动画遵循统一的命名规范：
```
Zylo[类型][动作][方向/状态]Animation
```

**示例：**
- `ZyloCardHoverEnterAnimation` - 卡片悬停进入动画
- `ZyloNotificationSlideInRightAnimation` - 通知从右侧滑入动画
- `ZyloButtonClickAnimation` - 按钮点击动画

## 🔄 TransitionAnimations.xaml - 通用过渡动画

### 📱 淡入淡出动画
```xml
<!-- 基础用法 -->
<Button>
    <Button.Triggers>
        <EventTrigger RoutedEvent="MouseEnter">
            <BeginStoryboard Storyboard="{StaticResource ZyloFadeInAnimation}"/>
        </EventTrigger>
    </Button.Triggers>
</Button>
```

**可用动画：**
- `ZyloFadeInAnimation` - 标准淡入 (0.4s)
- `ZyloFadeOutAnimation` - 标准淡出 (0.4s)
- `ZyloFadeInFastAnimation` - 快速淡入 (0.2s)
- `ZyloFadeOutFastAnimation` - 快速淡出 (0.2s)

### 🔄 滑动动画
```xml
<!-- 页面切换示例 -->
<ContentControl>
    <ContentControl.Triggers>
        <EventTrigger RoutedEvent="Loaded">
            <BeginStoryboard Storyboard="{StaticResource ZyloSlideInFromRightAnimation}"/>
        </EventTrigger>
    </ContentControl.Triggers>
</ContentControl>
```

**可用动画：**
- `ZyloSlideInFromLeftAnimation` - 从左滑入
- `ZyloSlideInFromRightAnimation` - 从右滑入
- `ZyloSlideInFromTopAnimation` - 从上滑入
- `ZyloSlideInFromBottomAnimation` - 从下滑入

### 📏 缩放动画
```xml
<!-- 模态对话框进入 -->
<Border>
    <Border.Triggers>
        <EventTrigger RoutedEvent="Loaded">
            <BeginStoryboard Storyboard="{StaticResource ZyloScaleInAnimation}"/>
        </EventTrigger>
    </Border.Triggers>
</Border>
```

**可用动画：**
- `ZyloScaleInAnimation` - 缩放进入 (带回弹效果)
- `ZyloScaleOutAnimation` - 缩放退出

### 🌀 旋转动画
```xml
<!-- 刷新按钮 -->
<Button>
    <Button.Triggers>
        <EventTrigger RoutedEvent="Click">
            <BeginStoryboard Storyboard="{StaticResource ZyloRotateClockwiseAnimation}"/>
        </EventTrigger>
    </Button.Triggers>
</Button>
```

**可用动画：**
- `ZyloRotateClockwiseAnimation` - 顺时针旋转 360°
- `ZyloRotateCounterClockwiseAnimation` - 逆时针旋转 360°
- `ZyloSpinAnimation` - 无限旋转 (1s/圈)
- `ZyloSpinFastAnimation` - 快速无限旋转 (0.5s/圈)
- `ZyloSpinSlowAnimation` - 慢速无限旋转 (2s/圈)

### 💫 弹跳动画
```xml
<!-- 成功提示 -->
<TextBlock>
    <TextBlock.Triggers>
        <EventTrigger RoutedEvent="Loaded">
            <BeginStoryboard Storyboard="{StaticResource ZyloBounceInAnimation}"/>
        </EventTrigger>
    </TextBlock.Triggers>
</TextBlock>
```

**可用动画：**
- `ZyloBounceInAnimation` - 弹跳进入 (带回弹)
- `ZyloBounceOutAnimation` - 弹跳退出
- `ZyloBounceVerticalAnimation` - 垂直弹跳 (无限循环)

### 🌊 摇摆动画
```xml
<!-- 错误提示摇摆 -->
<Border>
    <Border.Triggers>
        <EventTrigger RoutedEvent="SomeErrorEvent">
            <BeginStoryboard Storyboard="{StaticResource ZyloShakeHorizontalAnimation}"/>
        </EventTrigger>
    </Border.Triggers>
</Border>
```

**可用动画：**
- `ZyloShakeHorizontalAnimation` - 左右摇摆 (3次)
- `ZyloShakeVerticalAnimation` - 上下摇摆 (3次)
- `ZyloWobbleAnimation` - 旋转摇摆 (4次)

## 🎯 ButtonAnimations.xaml - 按钮专用动画

### 基础交互动画
```xml
<!-- 完整的按钮交互 -->
<Button>
    <Button.Style>
        <Style TargetType="Button">
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Trigger.EnterActions>
                        <BeginStoryboard Storyboard="{StaticResource ZyloButtonHoverEnterAnimation}"/>
                    </Trigger.EnterActions>
                    <Trigger.ExitActions>
                        <BeginStoryboard Storyboard="{StaticResource ZyloButtonHoverExitAnimation}"/>
                    </Trigger.ExitActions>
                </Trigger>
                <EventTrigger RoutedEvent="Click">
                    <BeginStoryboard Storyboard="{StaticResource ZyloButtonClickAnimation}"/>
                </EventTrigger>
            </Style.Triggers>
        </Style>
    </Button.Style>
</Button>
```

**基础动画：**
- `ZyloButtonClickAnimation` - 点击反馈 (快速缩放)
- `ZyloButtonHoverEnterAnimation` - 悬停进入 (轻微放大)
- `ZyloButtonHoverExitAnimation` - 悬停退出 (恢复原状)

### 状态变化动画
```xml
<!-- 按钮状态切换 -->
<Button IsEnabled="{Binding IsButtonEnabled}">
    <Button.Style>
        <Style TargetType="Button">
            <Style.Triggers>
                <Trigger Property="IsEnabled" Value="False">
                    <Trigger.EnterActions>
                        <BeginStoryboard Storyboard="{StaticResource ZyloButtonDisableAnimation}"/>
                    </Trigger.EnterActions>
                    <Trigger.ExitActions>
                        <BeginStoryboard Storyboard="{StaticResource ZyloButtonEnableAnimation}"/>
                    </Trigger.ExitActions>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Button.Style>
</Button>
```

**状态动画：**
- `ZyloButtonDisableAnimation` - 禁用状态 (透明度降低)
- `ZyloButtonEnableAnimation` - 启用状态 (透明度恢复)

### 特殊效果动画
```xml
<!-- 重要按钮脉冲提示 -->
<Button>
    <Button.Triggers>
        <EventTrigger RoutedEvent="Loaded">
            <BeginStoryboard Storyboard="{StaticResource ZyloButtonPulseAnimation}"/>
        </EventTrigger>
    </Button.Triggers>
</Button>
```

**特殊动画：**
- `ZyloButtonPulseAnimation` - 脉冲动画 (吸引注意力)
- `ZyloButtonBlinkAnimation` - 闪烁动画 (警告提示)
- `ZyloButtonLoadingAnimation` - 加载动画 (旋转+透明度)

### 反馈动画
```xml
<!-- 操作结果反馈 -->
<Button Command="{Binding SaveCommand}">
    <i:Interaction.Triggers>
        <i:EventTrigger EventName="CommandSucceeded">
            <i:InvokeCommandAction Command="{Binding PlaySuccessAnimationCommand}"/>
        </i:EventTrigger>
    </i:Interaction.Triggers>
</Button>
```

**反馈动画：**
- `ZyloButtonSuccessAnimation` - 成功反馈 (绿色闪烁)
- `ZyloButtonErrorAnimation` - 错误反馈 (红色闪烁+摇摆)
- `ZyloButtonElasticClickAnimation` - 弹性点击 (复杂的弹性效果)

## 🎴 CardAnimations.xaml - 卡片专用动画

### 标准卡片交互
```xml
<!-- 标准卡片悬停效果 -->
<Border>
    <Border.Style>
        <Style TargetType="Border">
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Trigger.EnterActions>
                        <BeginStoryboard Storyboard="{StaticResource ZyloCardHoverEnterAnimation}"/>
                    </Trigger.EnterActions>
                    <Trigger.ExitActions>
                        <BeginStoryboard Storyboard="{StaticResource ZyloCardHoverExitAnimation}"/>
                    </Trigger.ExitActions>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Border.Style>
</Border>
```

### 强调卡片交互
```xml
<!-- 重要内容卡片 -->
<Border>
    <Border.Style>
        <Style TargetType="Border">
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Trigger.EnterActions>
                        <BeginStoryboard Storyboard="{StaticResource ZyloCardHoverEnterStrongAnimation}"/>
                    </Trigger.EnterActions>
                    <Trigger.ExitActions>
                        <BeginStoryboard Storyboard="{StaticResource ZyloCardHoverExitStrongAnimation}"/>
                    </Trigger.ExitActions>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Border.Style>
</Border>
```

**可用动画：**
- `ZyloCardHoverEnterAnimation` - 标准悬停进入 (1.02x 缩放, -2px 上移)
- `ZyloCardHoverExitAnimation` - 标准悬停退出
- `ZyloCardHoverEnterStrongAnimation` - 强调悬停进入 (1.03x 缩放, -3px 上移)
- `ZyloCardHoverExitStrongAnimation` - 强调悬停退出
- `ZyloCardClickAnimation` - 卡片点击反馈

## 📄 PageAnimations.xaml - 页面级动画

### 专用页面动画
```xml
<!-- HomePageView 专用 -->
<Border x:Name="TopBanner">
    <Border.RenderTransform>
        <TranslateTransform/>
    </Border.RenderTransform>
    <Border.Triggers>
        <EventTrigger RoutedEvent="Loaded">
            <BeginStoryboard Storyboard="{StaticResource ZyloHomePageLoadAnimation}"/>
        </EventTrigger>
    </Border.Triggers>
</Border>
```

**可用动画：**
- `ZyloHomePageLoadAnimation` - HomePageView 专用加载动画
- `ZyloSubMenuPageLoadAnimation` - SubMenuView 专用加载动画
- `ZyloDotsAnimation` - 装饰圆点动画 (无限循环)
- `ZyloPageLoadAnimation` - 通用页面加载动画

## ⏳ LoadingAnimations.xaml - 加载动画

### 旋转加载器
```xml
<!-- 标准加载指示器 -->
<Border>
    <Border.RenderTransform>
        <RotateTransform/>
    </Border.RenderTransform>
    <Border.Triggers>
        <EventTrigger RoutedEvent="Loaded">
            <BeginStoryboard Storyboard="{StaticResource ZyloLoadingSpinAnimation}"/>
        </EventTrigger>
    </Border.Triggers>
</Border>
```

**旋转动画：**
- `ZyloLoadingSpinAnimation` - 标准旋转 (1s/圈)
- `ZyloLoadingSpinFastAnimation` - 快速旋转 (0.6s/圈)
- `ZyloLoadingSpinSlowAnimation` - 慢速旋转 (1.5s/圈)

### 脉冲加载器
```xml
<!-- 脉冲加载效果 -->
<Ellipse>
    <Ellipse.RenderTransform>
        <ScaleTransform/>
    </Ellipse.RenderTransform>
    <Ellipse.Triggers>
        <EventTrigger RoutedEvent="Loaded">
            <BeginStoryboard Storyboard="{StaticResource ZyloLoadingPulseAnimation}"/>
        </EventTrigger>
    </Ellipse.Triggers>
</Ellipse>
```

**脉冲动画：**
- `ZyloLoadingPulseAnimation` - 标准脉冲 (0.8-1.2x 缩放)
- `ZyloLoadingPulseFastAnimation` - 快速脉冲 (0.9-1.1x 缩放)

### 波浪加载器
```xml
<!-- 三点波浪动画 -->
<StackPanel Orientation="Horizontal">
    <Ellipse x:Name="Dot1">
        <Ellipse.RenderTransform>
            <TranslateTransform/>
        </Ellipse.RenderTransform>
    </Ellipse>
    <Ellipse x:Name="Dot2">
        <Ellipse.RenderTransform>
            <TranslateTransform/>
        </Ellipse.RenderTransform>
    </Ellipse>
    <Ellipse x:Name="Dot3">
        <Ellipse.RenderTransform>
            <TranslateTransform/>
        </Ellipse.RenderTransform>
    </Ellipse>
    <StackPanel.Triggers>
        <EventTrigger RoutedEvent="Loaded">
            <BeginStoryboard Storyboard="{StaticResource ZyloLoadingWaveAnimation}"/>
        </EventTrigger>
    </StackPanel.Triggers>
</StackPanel>
```

**波浪动画：**
- `ZyloLoadingWaveAnimation` - 三点波浪 (需要 Dot1, Dot2, Dot3 元素)

### 进度条动画
```xml
<!-- 进度条填充 -->
<Rectangle x:Name="ProgressBar">
    <Rectangle.Triggers>
        <EventTrigger RoutedEvent="SomeProgressEvent">
            <BeginStoryboard Storyboard="{StaticResource ZyloProgressFillAnimation}"/>
        </EventTrigger>
    </Rectangle.Triggers>
</Rectangle>
```

**进度动画：**
- `ZyloProgressFillAnimation` - 进度条填充动画
- `ZyloProgressIndeterminateAnimation` - 不确定进度条动画

### 其他加载动画
**闪烁动画：**
- `ZyloLoadingBlinkAnimation` - 标准闪烁
- `ZyloLoadingBlinkFastAnimation` - 快速闪烁

**组合动画：**
- `ZyloLoadingSpinPulseAnimation` - 旋转+脉冲组合
- `ZyloSkeletonLoadingAnimation` - 骨架屏加载动画

## 🔔 NotificationAnimations.xaml - 通知和提示动画

### 通知进入动画
```xml
<!-- 从右侧滑入的通知 -->
<Border>
    <Border.RenderTransform>
        <TranslateTransform/>
    </Border.RenderTransform>
    <Border.Triggers>
        <EventTrigger RoutedEvent="Loaded">
            <BeginStoryboard Storyboard="{StaticResource ZyloNotificationSlideInRightAnimation}"/>
        </EventTrigger>
    </Border.Triggers>
</Border>
```

**进入动画：**
- `ZyloNotificationSlideInRightAnimation` - 从右侧滑入
- `ZyloNotificationSlideInLeftAnimation` - 从左侧滑入
- `ZyloNotificationSlideInTopAnimation` - 从顶部滑入
- `ZyloNotificationSlideInBottomAnimation` - 从底部滑入

### 通知退出动画
**退出动画：**
- `ZyloNotificationSlideOutRightAnimation` - 向右滑出
- `ZyloNotificationSlideOutLeftAnimation` - 向左滑出
- `ZyloNotificationSlideOutTopAnimation` - 向上滑出
- `ZyloNotificationSlideOutBottomAnimation` - 向下滑出

### 弹出式通知
```xml
<!-- 模态通知弹出 -->
<Border>
    <Border.RenderTransform>
        <ScaleTransform/>
    </Border.RenderTransform>
    <Border.Triggers>
        <EventTrigger RoutedEvent="Loaded">
            <BeginStoryboard Storyboard="{StaticResource ZyloNotificationPopInAnimation}"/>
        </EventTrigger>
    </Border.Triggers>
</Border>
```

**弹出动画：**
- `ZyloNotificationPopInAnimation` - 弹出进入 (带回弹)
- `ZyloNotificationPopOutAnimation` - 弹出退出

### 状态反馈动画
```xml
<!-- 警告消息 -->
<Border>
    <Border.Triggers>
        <EventTrigger RoutedEvent="ShowWarning">
            <BeginStoryboard Storyboard="{StaticResource ZyloWarningShakeAnimation}"/>
        </EventTrigger>
    </Border.Triggers>
</Border>
```

**状态动画：**
- `ZyloWarningShakeAnimation` - 警告摇摆 (黄色背景+摇摆)
- `ZyloErrorBlinkAnimation` - 错误闪烁 (红色背景+闪烁)
- `ZyloSuccessConfirmAnimation` - 成功确认 (绿色背景+弹性缩放)

### 提示动画
**提示动画：**
- `ZyloHintBlinkAnimation` - 提示闪烁 (3次)
- `ZyloHintHighlightAnimation` - 提示高亮 (黄色背景渐变)

## 🧭 NavigationAnimations.xaml - 导航专用动画

### 页面切换动画
```xml
<!-- 页面切换容器 -->
<ContentControl>
    <ContentControl.Triggers>
        <EventTrigger RoutedEvent="ContentChanged">
            <BeginStoryboard Storyboard="{StaticResource ZyloPageSlideInRightAnimation}"/>
        </EventTrigger>
    </ContentControl.Triggers>
</ContentControl>
```

**页面切换：**
- `ZyloPageSlideInRightAnimation` - 页面从右滑入
- `ZyloPageSlideInLeftAnimation` - 页面从左滑入
- `ZyloPageSlideOutRightAnimation` - 页面向右滑出
- `ZyloPageSlideOutLeftAnimation` - 页面向左滑出

### 导航项动画
```xml
<!-- 导航菜单项 -->
<ListBoxItem>
    <ListBoxItem.Style>
        <Style TargetType="ListBoxItem">
            <Style.Triggers>
                <Trigger Property="IsSelected" Value="True">
                    <Trigger.EnterActions>
                        <BeginStoryboard Storyboard="{StaticResource ZyloNavItemSelectAnimation}"/>
                    </Trigger.EnterActions>
                    <Trigger.ExitActions>
                        <BeginStoryboard Storyboard="{StaticResource ZyloNavItemDeselectAnimation}"/>
                    </Trigger.ExitActions>
                </Trigger>
            </Style.Triggers>
        </Style>
    </ListBoxItem.Style>
</ListBoxItem>
```

**导航项动画：**
- `ZyloNavItemSelectAnimation` - 导航项选中
- `ZyloNavItemDeselectAnimation` - 导航项取消选中
- `ZyloNavItemHoverEnterAnimation` - 导航项悬停进入
- `ZyloNavItemHoverExitAnimation` - 导航项悬停退出

### 子菜单动画
```xml
<!-- 可展开的子菜单 -->
<Expander>
    <Expander.Style>
        <Style TargetType="Expander">
            <Style.Triggers>
                <Trigger Property="IsExpanded" Value="True">
                    <Trigger.EnterActions>
                        <BeginStoryboard Storyboard="{StaticResource ZyloSubMenuExpandAnimation}"/>
                    </Trigger.EnterActions>
                    <Trigger.ExitActions>
                        <BeginStoryboard Storyboard="{StaticResource ZyloSubMenuCollapseAnimation}"/>
                    </Trigger.ExitActions>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Expander.Style>
</Expander>
```

**子菜单动画：**
- `ZyloSubMenuExpandAnimation` - 子菜单展开
- `ZyloSubMenuCollapseAnimation` - 子菜单收缩
- `ZyloSubMenuItemEnterAnimation` - 子菜单项进入

### 面包屑动画
**面包屑动画：**
- `ZyloBreadcrumbAddAnimation` - 面包屑项添加
- `ZyloBreadcrumbRemoveAnimation` - 面包屑项移除

### 搜索动画
**搜索动画：**
- `ZyloSearchExpandAnimation` - 搜索框展开
- `ZyloSearchCollapseAnimation` - 搜索框收缩
- `ZyloSearchHighlightAnimation` - 搜索结果高亮

### 侧边栏动画
**侧边栏动画：**
- `ZyloSidebarSlideInAnimation` - 侧边栏滑入
- `ZyloSidebarSlideOutAnimation` - 侧边栏滑出

## 🚀 使用最佳实践

### 1. 性能优化
- 所有动画都使用了硬件加速的 Transform 属性
- 避免了会导致重排的属性动画 (如 Width, Height)
- 使用了合适的 EasingFunction 提升视觉效果

### 2. 响应式设计
- 动画时长经过优化，平衡流畅性和性能
- 支持不同设备的性能差异
- 可以通过系统设置禁用动画

### 3. 一致性
- 统一的命名规范便于记忆和使用
- 相似功能的动画使用相同的时长和缓动函数
- 符合现代 UI 设计规范

### 4. 扩展性
- 模块化的文件结构便于维护
- 易于添加新的动画类型
- 支持自定义动画参数

## 📝 注意事项

1. **Transform 要求**: 大部分动画需要目标元素设置相应的 RenderTransform
2. **元素命名**: 某些动画需要特定的元素名称 (如波浪动画的 Dot1, Dot2, Dot3)
3. **性能考虑**: 避免同时运行过多复杂动画
4. **主题适配**: 颜色动画会自动适应当前主题

## 🎯 快速开始

1. **引用动画资源**: Zylo.WPF 会自动加载所有动画资源
2. **选择合适动画**: 根据使用场景选择对应分类的动画
3. **设置 Transform**: 为目标元素添加必要的 RenderTransform
4. **绑定触发器**: 使用 EventTrigger 或 Trigger 绑定动画

## 🎨 高级用法示例

### 组合动画效果
```xml
<!-- 复杂的卡片交互：悬停 + 点击 + 加载 -->
<Border x:Name="InteractiveCard">
    <Border.RenderTransform>
        <TransformGroup>
            <ScaleTransform/>
            <TranslateTransform/>
            <RotateTransform/>
        </TransformGroup>
    </Border.RenderTransform>
    <Border.Style>
        <Style TargetType="Border">
            <Style.Triggers>
                <!-- 悬停效果 -->
                <Trigger Property="IsMouseOver" Value="True">
                    <Trigger.EnterActions>
                        <BeginStoryboard Storyboard="{StaticResource ZyloCardHoverEnterAnimation}"/>
                    </Trigger.EnterActions>
                    <Trigger.ExitActions>
                        <BeginStoryboard Storyboard="{StaticResource ZyloCardHoverExitAnimation}"/>
                    </Trigger.ExitActions>
                </Trigger>
                <!-- 加载状态 -->
                <DataTrigger Binding="{Binding IsLoading}" Value="True">
                    <DataTrigger.EnterActions>
                        <BeginStoryboard Storyboard="{StaticResource ZyloLoadingSpinAnimation}"/>
                    </DataTrigger.EnterActions>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </Border.Style>
    <Border.Triggers>
        <!-- 点击反馈 -->
        <EventTrigger RoutedEvent="MouseLeftButtonDown">
            <BeginStoryboard Storyboard="{StaticResource ZyloCardClickAnimation}"/>
        </EventTrigger>
    </Border.Triggers>
</Border>
```

### 自定义动画参数
```xml
<!-- 修改动画时长和缓动函数 -->
<Storyboard x:Key="CustomFadeIn">
    <DoubleAnimation Storyboard.TargetProperty="Opacity"
                   From="0" To="1" Duration="0:0:0.8">
        <DoubleAnimation.EasingFunction>
            <ElasticEase EasingMode="EaseOut" Oscillations="3" Springiness="5"/>
        </DoubleAnimation.EasingFunction>
    </DoubleAnimation>
</Storyboard>
```

### 条件动画
```xml
<!-- 根据数据状态播放不同动画 -->
<Border>
    <Border.Style>
        <Style TargetType="Border">
            <Style.Triggers>
                <DataTrigger Binding="{Binding Status}" Value="Success">
                    <DataTrigger.EnterActions>
                        <BeginStoryboard Storyboard="{StaticResource ZyloSuccessConfirmAnimation}"/>
                    </DataTrigger.EnterActions>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Error">
                    <DataTrigger.EnterActions>
                        <BeginStoryboard Storyboard="{StaticResource ZyloErrorBlinkAnimation}"/>
                    </DataTrigger.EnterActions>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Warning">
                    <DataTrigger.EnterActions>
                        <BeginStoryboard Storyboard="{StaticResource ZyloWarningShakeAnimation}"/>
                    </DataTrigger.EnterActions>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </Border.Style>
</Border>
```

### 动画序列
```xml
<!-- 依次播放多个动画 -->
<Border>
    <Border.Triggers>
        <EventTrigger RoutedEvent="Loaded">
            <!-- 第一个动画：淡入 -->
            <BeginStoryboard>
                <Storyboard>
                    <DoubleAnimation Storyboard.TargetProperty="Opacity"
                                   From="0" To="1" Duration="0:0:0.5"/>
                    <!-- 第二个动画：缩放 (延迟0.5秒) -->
                    <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                   From="0.8" To="1" Duration="0:0:0.3"
                                   BeginTime="0:0:0.5">
                        <DoubleAnimation.EasingFunction>
                            <BackEase EasingMode="EaseOut" Amplitude="0.3"/>
                        </DoubleAnimation.EasingFunction>
                    </DoubleAnimation>
                    <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                   From="0.8" To="1" Duration="0:0:0.3"
                                   BeginTime="0:0:0.5">
                        <DoubleAnimation.EasingFunction>
                            <BackEase EasingMode="EaseOut" Amplitude="0.3"/>
                        </DoubleAnimation.EasingFunction>
                    </DoubleAnimation>
                </Storyboard>
            </BeginStoryboard>
        </EventTrigger>
    </Border.Triggers>
</Border>
```

## 🔧 故障排除

### 常见问题

**1. 动画不播放**
- 检查目标元素是否设置了正确的 RenderTransform
- 确认动画资源已正确引用
- 验证触发器条件是否满足

**2. 动画效果不明显**
- 检查元素的初始状态
- 确认动画的 From 和 To 值设置正确
- 调整动画时长和缓动函数

**3. 性能问题**
- 避免同时运行过多动画
- 使用硬件加速的属性 (Transform, Opacity)
- 考虑降低动画复杂度

**4. 动画冲突**
- 确保不同动画不会同时修改相同属性
- 使用 Storyboard.Stop() 停止冲突的动画
- 合理安排动画的播放时机

### 调试技巧

```xml
<!-- 添加动画事件监听 -->
<Border>
    <Border.Triggers>
        <EventTrigger RoutedEvent="Loaded">
            <BeginStoryboard>
                <Storyboard Completed="OnAnimationCompleted">
                    <DoubleAnimation Storyboard.TargetProperty="Opacity"
                                   From="0" To="1" Duration="0:0:0.5"/>
                </Storyboard>
            </BeginStoryboard>
        </EventTrigger>
    </Border.Triggers>
</Border>
```

```csharp
// 代码后台处理动画事件
private void OnAnimationCompleted(object sender, EventArgs e)
{
    // 动画完成后的处理逻辑
    Debug.WriteLine("Animation completed!");
}
```

## 📚 参考资源

### 相关文档
- [WPF 动画概述](https://docs.microsoft.com/zh-cn/dotnet/desktop/wpf/graphics-multimedia/animation-overview)
- [WPF 缓动函数](https://docs.microsoft.com/zh-cn/dotnet/desktop/wpf/graphics-multimedia/easing-functions)
- [WPF 变换概述](https://docs.microsoft.com/zh-cn/dotnet/desktop/wpf/graphics-multimedia/transforms-overview)

### 设计参考
- [Material Design Motion](https://material.io/design/motion/)
- [Fluent Design System](https://www.microsoft.com/design/fluent/)
- [Human Interface Guidelines](https://developer.apple.com/design/human-interface-guidelines/)

## 🤝 贡献指南

欢迎为 Zylo.WPF 动画系统贡献新的动画效果！

### 添加新动画的步骤：
1. 选择合适的分类文件 (或创建新的分类)
2. 遵循命名规范：`Zylo[类型][动作][状态]Animation`
3. 添加详细的 XML 注释
4. 更新此 README 文档
5. 提供使用示例

### 动画设计原则：
- **流畅性**: 使用合适的缓动函数
- **一致性**: 保持与现有动画的风格统一
- **性能**: 优先使用硬件加速属性
- **可访问性**: 考虑用户的动画偏好设置

现在你可以在 Zylo.WPF 应用程序中使用这些专业的动画效果了！🎉

---

**版本**: 1.0.0
**最后更新**: 2025-01-17
**维护者**: Zylo.WPF 开发团队
