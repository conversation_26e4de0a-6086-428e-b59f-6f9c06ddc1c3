using System;
using System.IO;
using System.Runtime.InteropServices;
using System.Windows;
using WPFTest.Models.DWG;
using Zylo.YLog.Runtime;

namespace WPFTest.Services.DWG;

/// <summary>
/// DWG文件拖拽服务 - 处理文件拖拽到外部应用程序
/// </summary>
public class DwgFileDragService
{
    private readonly YLoggerInstance _logger = YLogger.ForSilent<DwgFileDragService>();

    #region 公共方法

    /// <summary>
    /// 启动Microsoft标准文件拖拽操作 - 与Windows资源管理器完全一致
    /// </summary>
    public DragDropEffects StartNativeDrag(DwgFileModel fileModel, FrameworkElement source)
    {
        try
        {
            if (fileModel == null)
            {
                _logger.Warning("❌ 文件模型为空");
                return DragDropEffects.None;
            }

            var fullPath = Path.GetFullPath(fileModel.FullPath);

            if (!File.Exists(fullPath))
            {
                _logger.Error($"❌ 文件不存在: {fullPath}");
                return DragDropEffects.None;
            }

            var fullFileName = Path.GetFileName(fullPath); // 获取完整文件名（含扩展名）
            _logger.Info($"🌍 启动Microsoft标准拖拽: {fullFileName}");

            // 🎯 创建最简单的标准数据对象 - 与Windows资源管理器完全一致
            var dataObject = new DataObject();
            var filePaths = new[] { fullPath };

            // 🎯 核心：最简单的CF_HDROP格式
            dataObject.SetData(DataFormats.FileDrop, filePaths);

            // 🎯 备选：文本格式
            dataObject.SetData(DataFormats.Text, fullPath);
            dataObject.SetData(DataFormats.UnicodeText, fullPath);

            _logger.Info($"📋 Microsoft标准拖拽数据已设置: {fullPath}");
            _logger.Info($"📁 完整文件名: {fullFileName}");
            _logger.Info($"📁 文件大小: {new FileInfo(fullPath).Length} bytes");

            // 🎯 使用原生WPF拖拽API - 与Windows资源管理器相同的行为
            var result = System.Windows.DragDrop.DoDragDrop(
                source,
                dataObject,
                DragDropEffects.Copy | DragDropEffects.Move
            );

            _logger.Info($"✅ Microsoft标准拖拽完成，结果: {result}");
            return result;
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ Microsoft标准拖拽失败: {ex.Message}");
            return DragDropEffects.None;
        }
    }

    /// <summary>
    /// 使用Windows底层API进行通用文件拖拽 - 最大兼容性
    /// </summary>
    public bool StartWindowsApiDrag(DwgFileModel fileModel)
    {
        try
        {
            if (fileModel == null)
            {
                _logger.Warning("❌ 文件模型为空");
                return false;
            }

            var fullPath = Path.GetFullPath(fileModel.FullPath);

            if (!File.Exists(fullPath))
            {
                _logger.Error($"❌ 文件不存在: {fullPath}");
                return false;
            }

            var fullFileName = Path.GetFileName(fullPath); // 获取完整文件名（含扩展名）
            _logger.Info($"🔧 使用Windows底层API通用拖拽: {fullFileName}");

            // 初始化OLE
            var hr = OleInitialize(IntPtr.Zero);
            if (hr != 0 && hr != 1) // 1表示已经初始化，这是正常的
            {
                _logger.Warning($"⚠️ OLE初始化异常: {hr}");
            }

            // 创建Shell文件数据对象 - 这是Windows最标准的方式
            var pidl = ILCreateFromPath(fullPath);
            if (pidl == IntPtr.Zero)
            {
                _logger.Error($"❌ 无法创建Shell路径: {fullPath}");
                return false;
            }

            try
            {
                // 分离父目录和文件项 - 按照Windows Shell标准
                var pidlItem = ILFindLastID(pidl);
                var pidlItemClone = ILClone(pidlItem);
                ILRemoveLastID(pidl);
                var pidlParent = ILClone(pidl);

                var arrayPidl = new IntPtr[] { pidlItemClone };

                // 创建Shell标准数据对象 - 与资源管理器完全一致
                System.Runtime.InteropServices.ComTypes.IDataObject dataObject = null;
                hr = SHCreateFileDataObject(pidlParent, 1, arrayPidl, null, out dataObject);

                if (hr == 0 && dataObject != null)
                {
                    _logger.Info($"✅ Shell标准数据对象创建成功");

                    // 创建标准拖拽源
                    var dropSource = new WindowsDropSource();

                    // 执行标准拖拽操作 - 支持所有Windows应用程序
                    uint effect = 0;
                    hr = DoDragDrop(dataObject, dropSource,
                        (uint)(DragDropEffects.Copy | DragDropEffects.Move | DragDropEffects.Link),
                        out effect);

                    _logger.Info($"🎯 通用底层拖拽完成，结果: {hr}, 效果: {effect}");

                    // 清理资源
                    ILFree(pidlItemClone);
                    ILFree(pidlParent);

                    return hr == 0 || hr == 1; // 0=成功, 1=取消但正常
                }
                else
                {
                    _logger.Error($"❌ Shell数据对象创建失败: {hr}");
                    return false;
                }
            }
            finally
            {
                ILFree(pidl);
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ Windows底层API拖拽失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 诊断文件和拖拽数据状态
    /// </summary>
    public void DiagnoseFile(DwgFileModel fileModel)
    {
        try
        {
            if (fileModel == null)
            {
                _logger.Warning("❌ 文件模型为空");
                return;
            }

            var fullPath = Path.GetFullPath(fileModel.FullPath);
            
            var fullFileName = Path.GetFileName(fullPath); // 获取完整文件名（含扩展名）
            _logger.Info("🔍 ===== 拖拽诊断开始 =====");
            _logger.Info($"📁 完整文件名: {fullFileName}");
            _logger.Info($"📂 完整路径: {fullPath}");
            _logger.Info($"✅ 文件存在: {File.Exists(fullPath)}");
            
            if (File.Exists(fullPath))
            {
                var fileInfo = new FileInfo(fullPath);
                _logger.Info($"📊 文件大小: {fileInfo.Length} bytes");
                _logger.Info($"📅 修改时间: {fileInfo.LastWriteTime}");
                _logger.Info($"🔒 只读属性: {fileInfo.IsReadOnly}");
                _logger.Info($"📝 扩展名: {fileInfo.Extension}");
            }
            
            // 测试创建拖拽数据
            var filePaths = new[] { fullPath };
            var dataObject = new DataObject();
            dataObject.SetData(DataFormats.FileDrop, filePaths);
            
            _logger.Info($"🎯 CF_HDROP数据已创建");
            _logger.Info($"📋 数据格式支持: {dataObject.GetDataPresent(DataFormats.FileDrop)}");
            
            var extension = Path.GetExtension(fullPath);
            _logger.Info($"🔗 文件扩展名: {extension}");
            
            _logger.Info("🔍 ===== 拖拽诊断完成 =====");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 拖拽诊断失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 测试文件是否可以被系统默认程序打开
    /// </summary>
    public bool TestFileOpen(DwgFileModel fileModel)
    {
        try
        {
            if (fileModel == null)
            {
                _logger.Warning("❌ 文件模型为空");
                return false;
            }

            if (!File.Exists(fileModel.FullPath))
            {
                _logger.Error($"❌ 文件不存在: {fileModel.FileName}");
                return false;
            }

            var fullFileName = Path.GetFileName(fileModel.FullPath); // 获取完整文件名（含扩展名）
            _logger.Info($"🧪 测试文件打开: {fullFileName} ({fileModel.FullPath})");

            var processInfo = new System.Diagnostics.ProcessStartInfo
            {
                FileName = fileModel.FullPath,
                UseShellExecute = true,
                Verb = "open"
            };

            System.Diagnostics.Process.Start(processInfo);
            
            _logger.Info($"✅ 文件打开命令已执行: {fileModel.FullPath}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 测试文件打开失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 创建增强的通用拖拽数据对象 - 最大化外部应用程序兼容性
    /// </summary>
    public DataObject CreateDragDataObject(DwgFileModel fileModel)
    {
        try
        {
            if (fileModel == null)
            {
                _logger.Warning("❌ 文件模型为空");
                return new DataObject();
            }

            var fullPath = Path.GetFullPath(fileModel.FullPath);
            var fullFileName = Path.GetFileName(fullPath);

            // 🎯 验证文件存在
            if (!File.Exists(fullPath))
            {
                _logger.Warning($"⚠️ 文件不存在: {fullPath}");
            }

            var dataObject = new DataObject();
            var filePaths = new[] { fullPath };

            // 🎯 核心：Windows标准文件拖拽格式
            dataObject.SetData(DataFormats.FileDrop, filePaths);

            // 🎯 增强兼容性：多种文本格式
            dataObject.SetData(DataFormats.Text, fullPath);
            dataObject.SetData(DataFormats.UnicodeText, fullPath);
            dataObject.SetData(DataFormats.StringFormat, fullPath);

            // 🎯 Windows Shell格式 - 提高外部应用程序识别率
            dataObject.SetData("FileName", fullFileName);
            dataObject.SetData("FileNameW", fullFileName);

            // 🎯 设置拖拽偏好 - 帮助外部应用程序理解操作意图
            dataObject.SetData("Preferred DropEffect", (int)DragDropEffects.Copy);

            // 🎯 内部对象格式（仅用于应用程序内部拖拽）
            dataObject.SetData(typeof(DwgFileModel), fileModel);

            _logger.Info($"📋 增强拖拽数据已创建: {fullFileName}");
            _logger.Info($"📂 完整路径: {fullPath}");
            _logger.Info($"🔧 数据格式: FileDrop + Text + FileName + PreferredDropEffect");

            return dataObject;
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 创建拖拽数据对象失败: {ex.Message}");
            return new DataObject();
        }
    }

    #endregion

    #region Windows API 声明

    [DllImport("ole32.dll")]
    private static extern int OleInitialize(IntPtr pvReserved);

    [DllImport("shell32.dll", CharSet = CharSet.Unicode)]
    private static extern IntPtr ILCreateFromPath(string pszPath);

    [DllImport("shell32.dll")]
    private static extern IntPtr ILFindLastID(IntPtr pidl);

    [DllImport("shell32.dll")]
    private static extern IntPtr ILClone(IntPtr pidl);

    [DllImport("shell32.dll")]
    private static extern bool ILRemoveLastID(IntPtr pidl);

    [DllImport("shell32.dll")]
    private static extern void ILFree(IntPtr pidl);

    [DllImport("shell32.dll", EntryPoint = "#740")]
    private static extern int SHCreateFileDataObject(
        IntPtr pidlFolder,
        uint cidl,
        IntPtr[] apidl,
        System.Runtime.InteropServices.ComTypes.IDataObject pdtInner,
        out System.Runtime.InteropServices.ComTypes.IDataObject ppdtobj);

    [DllImport("ole32.dll", EntryPoint = "DoDragDrop")]
    private static extern int DoDragDrop(
        System.Runtime.InteropServices.ComTypes.IDataObject pDataObj,
        IDropSource pDropSource,
        uint dwOKEffects,
        out uint pdwEffect);

    #endregion
}
