// 圆形按钮 C# 代码示例
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace WPFTest.ViewModels.ButtonControls
{
    public partial class ButtonPageViewModel : ObservableObject
    {
        /// <summary>
        /// 按钮点击命令
        /// </summary>
        [RelayCommand]
        private void ButtonClick(string parameter)
        {
            var message = parameter switch
            {
                "添加" => "➕ 执行了添加操作",
                "编辑" => "✏️ 执行了编辑操作", 
                "删除" => "🗑️ 执行了删除操作",
                "小型添加" => "🔸 点击了小型添加按钮",
                "小型编辑" => "🔸 点击了小型编辑按钮",
                "大型添加" => "🔷 点击了大型添加按钮",
                "大型播放" => "🔷 点击了大型播放按钮",
                "透明添加" => "👻 点击了透明添加按钮",
                "无边框更多" => "⚪ 点击了无边框更多按钮",
                _ => $"🔘 点击了按钮: {parameter}"
            };
            
            StatusMessage = message;
        }

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        public partial string StatusMessage { get; set; } = "准备就绪";
    }
}
