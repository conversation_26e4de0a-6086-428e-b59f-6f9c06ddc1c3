<!-- WPF-UI Card 高级用法示例 -->
<StackPanel Margin="20">

    <!-- 卡片网格布局 -->
    <TextBlock Text="卡片网格布局" FontWeight="Bold" Margin="0,0,0,12"/>
    <Grid Margin="0,0,0,24">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>
        
        <!-- 卡片 1 -->
        <ui:Card Grid.Column="0" Padding="16" Margin="0,0,8,0">
            <StackPanel HorizontalAlignment="Center">
                <ui:SymbolIcon Symbol="Home24" 
                               FontSize="32" 
                               Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                               HorizontalAlignment="Center"
                               Margin="0,0,0,8"/>
                <TextBlock Text="主页" 
                           FontWeight="Bold" 
                           HorizontalAlignment="Center"
                           Margin="0,0,0,4"/>
                <TextBlock Text="返回主页面" 
                           FontSize="12" 
                           HorizontalAlignment="Center"
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
            </StackPanel>
        </ui:Card>
        
        <!-- 卡片 2 -->
        <ui:Card Grid.Column="1" Padding="16" Margin="4,0,4,0">
            <StackPanel HorizontalAlignment="Center">
                <ui:SymbolIcon Symbol="Settings24" 
                               FontSize="32" 
                               Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                               HorizontalAlignment="Center"
                               Margin="0,0,0,8"/>
                <TextBlock Text="设置" 
                           FontWeight="Bold" 
                           HorizontalAlignment="Center"
                           Margin="0,0,0,4"/>
                <TextBlock Text="系统配置" 
                           FontSize="12" 
                           HorizontalAlignment="Center"
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
            </StackPanel>
        </ui:Card>
        
        <!-- 卡片 3 -->
        <ui:Card Grid.Column="2" Padding="16" Margin="8,0,0,0">
            <StackPanel HorizontalAlignment="Center">
                <ui:SymbolIcon Symbol="Person24" 
                               FontSize="32" 
                               Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                               HorizontalAlignment="Center"
                               Margin="0,0,0,8"/>
                <TextBlock Text="用户" 
                           FontWeight="Bold" 
                           HorizontalAlignment="Center"
                           Margin="0,0,0,4"/>
                <TextBlock Text="用户管理" 
                           FontSize="12" 
                           HorizontalAlignment="Center"
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
            </StackPanel>
        </ui:Card>
    </Grid>

    <!-- 嵌套卡片布局 -->
    <TextBlock Text="嵌套卡片布局" FontWeight="Bold" Margin="0,0,0,12"/>
    <ui:Card Padding="20" Margin="0,0,0,24">
        <StackPanel>
            <TextBlock Text="父级卡片" 
                       FontWeight="Bold" 
                       FontSize="18"
                       Margin="0,0,0,16"/>
            <TextBlock Text="这是一个包含子卡片的父级卡片容器。" 
                       Margin="0,0,0,16"/>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <!-- 子卡片 1 -->
                <ui:Card Grid.Column="0" 
                         Padding="12" 
                         Margin="0,0,8,0"
                         Background="{DynamicResource ControlFillColorSecondaryBrush}">
                    <StackPanel>
                        <TextBlock Text="子卡片 A" 
                                   FontWeight="Medium" 
                                   Margin="0,0,0,8"/>
                        <TextBlock Text="这是第一个子卡片的内容。" 
                                   FontSize="12"
                                   Margin="0,0,0,8"/>
                        <Button Content="操作 A" HorizontalAlignment="Left"/>
                    </StackPanel>
                </ui:Card>
                
                <!-- 子卡片 2 -->
                <ui:Card Grid.Column="1" 
                         Padding="12" 
                         Margin="8,0,0,0"
                         Background="{DynamicResource ControlFillColorSecondaryBrush}">
                    <StackPanel>
                        <TextBlock Text="子卡片 B" 
                                   FontWeight="Medium" 
                                   Margin="0,0,0,8"/>
                        <TextBlock Text="这是第二个子卡片的内容。" 
                                   FontSize="12"
                                   Margin="0,0,0,8"/>
                        <Button Content="操作 B" HorizontalAlignment="Left"/>
                    </StackPanel>
                </ui:Card>
            </Grid>
        </StackPanel>
    </ui:Card>

    <!-- 交互式卡片 -->
    <TextBlock Text="交互式卡片" FontWeight="Bold" Margin="0,0,0,12"/>
    <Grid Margin="0,0,0,24">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>
        
        <!-- 可点击卡片 -->
        <ui:Card Grid.Column="0" 
                 Padding="16" 
                 Margin="0,0,8,0"
                 Cursor="Hand">
            <ui:Card.Style>
                <Style TargetType="ui:Card">
                    <Style.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}"/>
                        </Trigger>
                    </Style.Triggers>
                </Style>
            </ui:Card.Style>
            <StackPanel>
                <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                    <ui:SymbolIcon Symbol="ChevronRight24" 
                                   FontSize="16" 
                                   Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                   Margin="0,0,8,0"/>
                    <TextBlock Text="可点击卡片" FontWeight="Medium"/>
                </StackPanel>
                <TextBlock Text="鼠标悬停时会有视觉反馈" 
                           FontSize="12"
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
            </StackPanel>
        </ui:Card>
        
        <!-- 状态卡片 -->
        <ui:Card Grid.Column="1" 
                 Padding="16" 
                 Margin="8,0,0,0">
            <StackPanel>
                <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                    <Ellipse Width="12" 
                             Height="12" 
                             Fill="{DynamicResource SystemFillColorSuccessBrush}"
                             Margin="0,0,8,0"/>
                    <TextBlock Text="在线状态" FontWeight="Medium"/>
                </StackPanel>
                <TextBlock Text="显示实时状态信息" 
                           FontSize="12"
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
            </StackPanel>
        </ui:Card>
    </Grid>

    <!-- 复杂布局卡片 -->
    <TextBlock Text="复杂布局卡片" FontWeight="Bold" Margin="0,0,0,12"/>
    <ui:Card Padding="0" Margin="0,0,0,24">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- 标题栏 -->
            <Border Grid.Row="0" 
                    Background="{DynamicResource AccentFillColorDefaultBrush}"
                    CornerRadius="8,8,0,0"
                    Padding="16,12">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" 
                               Text="项目仪表板" 
                               FontWeight="Bold" 
                               FontSize="16"
                               Foreground="White"
                               VerticalAlignment="Center"/>
                    
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <Button Content="设置" 
                                Background="Transparent" 
                                Foreground="White"
                                BorderThickness="0"
                                Margin="0,0,8,0"/>
                        <Button Content="更多" 
                                Background="Transparent" 
                                Foreground="White"
                                BorderThickness="0"/>
                    </StackPanel>
                </Grid>
            </Border>
            
            <!-- 内容区域 -->
            <StackPanel Grid.Row="1" Margin="16">
                <Grid Margin="0,0,0,16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                        <TextBlock Text="24" FontSize="24" FontWeight="Bold" HorizontalAlignment="Center"/>
                        <TextBlock Text="任务" FontSize="12" HorizontalAlignment="Center"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                        <TextBlock Text="8" FontSize="24" FontWeight="Bold" HorizontalAlignment="Center"/>
                        <TextBlock Text="完成" FontSize="12" HorizontalAlignment="Center"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                        <TextBlock Text="16" FontSize="24" FontWeight="Bold" HorizontalAlignment="Center"/>
                        <TextBlock Text="进行中" FontSize="12" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Grid>
                
                <ProgressBar Value="33" Height="8" Margin="0,0,0,8"/>
                <TextBlock Text="项目进度: 33%" FontSize="12" HorizontalAlignment="Center"/>
            </StackPanel>
            
            <!-- 底部操作栏 -->
            <Border Grid.Row="2" 
                    Background="{DynamicResource ControlFillColorSecondaryBrush}"
                    CornerRadius="0,0,8,8"
                    Padding="16,12">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                    <Button Content="查看详情" Margin="0,0,8,0"/>
                    <Button Content="编辑项目"/>
                </StackPanel>
            </Border>
        </Grid>
    </ui:Card>

    <!-- 响应式卡片网格 -->
    <TextBlock Text="响应式卡片网格" FontWeight="Bold" Margin="0,0,0,12"/>
    <UniformGrid Columns="2" Margin="0,0,0,24">
        <ui:Card Padding="16" Margin="0,0,4,4">
            <StackPanel HorizontalAlignment="Center">
                <ui:SymbolIcon Symbol="Calendar24" FontSize="24" Margin="0,0,0,8"/>
                <TextBlock Text="日历" FontWeight="Medium" HorizontalAlignment="Center"/>
                <TextBlock Text="查看日程" FontSize="12" HorizontalAlignment="Center"/>
            </StackPanel>
        </ui:Card>
        
        <ui:Card Padding="16" Margin="4,0,0,4">
            <StackPanel HorizontalAlignment="Center">
                <ui:SymbolIcon Symbol="Mail24" FontSize="24" Margin="0,0,0,8"/>
                <TextBlock Text="邮件" FontWeight="Medium" HorizontalAlignment="Center"/>
                <TextBlock Text="收件箱" FontSize="12" HorizontalAlignment="Center"/>
            </StackPanel>
        </ui:Card>
        
        <ui:Card Padding="16" Margin="0,4,4,0">
            <StackPanel HorizontalAlignment="Center">
                <ui:SymbolIcon Symbol="Document24" FontSize="24" Margin="0,0,0,8"/>
                <TextBlock Text="文档" FontWeight="Medium" HorizontalAlignment="Center"/>
                <TextBlock Text="文件管理" FontSize="12" HorizontalAlignment="Center"/>
            </StackPanel>
        </ui:Card>
        
        <ui:Card Padding="16" Margin="4,4,0,0">
            <StackPanel HorizontalAlignment="Center">
                <ui:SymbolIcon Symbol="Chart24" FontSize="24" Margin="0,0,0,8"/>
                <TextBlock Text="报表" FontWeight="Medium" HorizontalAlignment="Center"/>
                <TextBlock Text="数据分析" FontSize="12" HorizontalAlignment="Center"/>
            </StackPanel>
        </ui:Card>
    </UniformGrid>

    <!-- 动态内容卡片 -->
    <TextBlock Text="动态内容卡片" FontWeight="Bold" Margin="0,0,0,12"/>
    <ui:Card Padding="16">
        <StackPanel>
            <TextBlock Text="实时数据" FontWeight="Bold" FontSize="16" Margin="0,0,0,12"/>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0">
                    <TextBlock Text="CPU 使用率" FontSize="12" Margin="0,0,0,4"/>
                    <ProgressBar Value="45" Height="6" Margin="0,0,0,8"/>
                    
                    <TextBlock Text="内存使用率" FontSize="12" Margin="0,0,0,4"/>
                    <ProgressBar Value="67" Height="6" Margin="0,0,0,8"/>
                    
                    <TextBlock Text="磁盘使用率" FontSize="12" Margin="0,0,0,4"/>
                    <ProgressBar Value="23" Height="6"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Margin="16,0,0,0">
                    <TextBlock Text="45%" FontWeight="Bold" HorizontalAlignment="Right"/>
                    <TextBlock Text="67%" FontWeight="Bold" HorizontalAlignment="Right" Margin="0,14,0,0"/>
                    <TextBlock Text="23%" FontWeight="Bold" HorizontalAlignment="Right" Margin="0,14,0,0"/>
                </StackPanel>
            </Grid>
        </StackPanel>
    </ui:Card>

</StackPanel>
