using System.Windows.Controls;
using System.Windows;

namespace WPFTest.Views.Pages;

/// <summary>
/// 文件浏览器页面 - 演示FileExplorerControl的依赖属性功能
/// </summary>
public partial class FileExplorerPage : UserControl
{
    public FileExplorerPage()
    {
        InitializeComponent();
        
        // 页面加载完成后的初始化
        Loaded += FileExplorerPage_Loaded;
    }

    private void FileExplorerPage_Loaded(object sender, RoutedEventArgs e)
    {
        // 演示：设置初始路径
        if (string.IsNullOrEmpty(FileExplorer.CurrentPath))
        {
            FileExplorer.CurrentPath = System.Environment.GetFolderPath(System.Environment.SpecialFolder.MyDocuments);
        }
    }

    /// <summary>
    /// 清除搜索按钮点击事件
    /// </summary>
    private void ClearSearch_Click(object sender, RoutedEventArgs e)
    {
        FileExplorer.SearchText = string.Empty;
    }

    /// <summary>
    /// 重置默认设置按钮点击事件
    /// </summary>
    private void ResetDefaults_Click(object sender, RoutedEventArgs e)
    {
        ResetToDefaults();
    }

    /// <summary>
    /// 最小界面按钮点击事件
    /// </summary>
    private void MinimizeInterface_Click(object sender, RoutedEventArgs e)
    {
        MinimizeInterface();
    }

    /// <summary>
    /// 只读模式按钮点击事件
    /// </summary>
    private void ReadOnlyMode_Click(object sender, RoutedEventArgs e)
    {
        ReadOnlyMode();
    }

    /// <summary>
    /// 演示：重置所有设置到默认值
    /// </summary>
    private void ResetToDefaults()
    {
        FileExplorer.ShowToolbar = true;
        FileExplorer.ShowAddressBar = true;
        FileExplorer.ShowBreadcrumb = true;
        FileExplorer.ShowSearchBox = true;
        FileExplorer.ShowStatusBar = true;
        FileExplorer.ShowFolderTree = true;
        FileExplorer.FolderTreeWidth = 250;
        FileExplorer.AllowFileSelection = true;
        FileExplorer.AllowFolderSelection = true;
        FileExplorer.SearchText = string.Empty;
    }

    /// <summary>
    /// 演示：最小化界面（隐藏不必要的元素）
    /// </summary>
    private void MinimizeInterface()
    {
        FileExplorer.ShowToolbar = false;
        FileExplorer.ShowAddressBar = false;
        FileExplorer.ShowBreadcrumb = false;
        FileExplorer.ShowSearchBox = false;
        FileExplorer.ShowStatusBar = false;
        FileExplorer.ShowFolderTree = false;
    }

    /// <summary>
    /// 演示：只读模式（禁用选择功能）
    /// </summary>
    private void ReadOnlyMode()
    {
        FileExplorer.AllowFileSelection = false;
        FileExplorer.AllowFolderSelection = false;
    }

    /// <summary>
    /// 演示：获取当前选中的文件信息
    /// </summary>
    private void GetSelectedFilesInfo()
    {
        var selectedFiles = FileExplorer.GetSelectedFiles();
        var message = $"选中了 {selectedFiles.Count} 个文件:\n";
        
        foreach (var file in selectedFiles)
        {
            message += $"- {file.Name} ({file.SizeText})\n";
        }
        
        MessageBox.Show(message, "选中的文件", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    /// <summary>
    /// 演示：获取当前选中的文件夹信息
    /// </summary>
    private void GetSelectedFolderInfo()
    {
        var selectedFolder = FileExplorer.GetSelectedFolder();
        if (selectedFolder != null)
        {
            MessageBox.Show($"选中的文件夹: {selectedFolder.Name}\n路径: {selectedFolder.FullPath}", 
                          "选中的文件夹", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        else
        {
            MessageBox.Show("没有选中文件夹", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
} 