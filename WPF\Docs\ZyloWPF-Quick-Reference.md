# 🚀 ZyloWPF 快速参考

> **一页纸掌握 ZyloWPF 框架核心要点**

---

## 📋 核心技术栈

| 技术 | 版本 | 用途 |
|------|------|------|
| **.NET** | 8.0 | 运行时平台 |
| **WPF-UI** | 3.0+ | 现代化 UI 控件 |
| **CommunityToolkit.MVVM** | 8.4.0 | MVVM 框架 |
| **Prism** | 9.0+ | 导航和依赖注入 |
| **YLog** | 自研 | 高性能日志 |

---

## 🏗️ MVVM 快速语法

### ViewModel 基础模板

```csharp
public partial class ExampleViewModel : ObservableObject
{
    // 🔸 属性 - 使用 Partial Properties
    [ObservableProperty]
    public partial string Title { get; set; } = "默认标题";

    [ObservableProperty]
    public partial ObservableCollection<ItemModel> Items { get; set; } = new();

    // 🔸 命令 - 使用 RelayCommand
    [RelayCommand]
    private void SimpleAction() => Debug.WriteLine("执行操作");

    [RelayCommand]
    private void ActionWithParam(string param) => Title = param;

    [RelayCommand]
    private async Task AsyncAction() => await Task.Delay(1000);

    [RelayCommand(CanExecute = nameof(CanSave))]
    private void Save() => Debug.WriteLine("保存");
    
    private bool CanSave() => !string.IsNullOrEmpty(Title);
}
```

### View 绑定模板

```xml
<UserControl xmlns:mvvm="http://prismlibrary.com/"
             xmlns:vm="clr-namespace:App.ViewModels"
             mvvm:ViewModelLocator.AutoWireViewModel="True"
             d:DataContext="{d:DesignInstance vm:ExampleViewModel}">
    
    <StackPanel>
        <TextBox Text="{Binding Title}"/>
        <Button Content="执行" Command="{Binding SimpleActionCommand}"/>
        <ListBox ItemsSource="{Binding Items}"/>
    </StackPanel>
</UserControl>
```

---

## 🎛️ 控件开发模板

### 控件结构

```
📁 Controls/Category/
├── 📄 MyControl.xaml           # UI 定义
├── 📄 MyControl.xaml.cs        # 代码后台
└── 📄 MyControlModel.cs        # 数据模型
```

### 控件代码模板

```csharp
public partial class MyControl : UserControl
{
    private readonly YLoggerInstance _logger = YLogger.ForSilent<MyControl>();

    // 依赖属性
    public static readonly DependencyProperty ValueProperty =
        DependencyProperty.Register(nameof(Value), typeof(string), 
            typeof(MyControl), new PropertyMetadata(string.Empty));

    public string Value
    {
        get => (string)GetValue(ValueProperty);
        set => SetValue(ValueProperty, value);
    }

    public MyControl()
    {
        InitializeComponent();
        DataContext = this;
        _logger.Info("✅ MyControl 初始化完成");
    }
}
```

---

## 🧭 导航系统

### 1. 注册页面

```csharp
// AppBootstrapper.cs
protected override void RegisterTypes(IContainerRegistry containerRegistry)
{
    containerRegistry.RegisterForNavigation<ExampleView, ExampleViewModel>();
}
```

### 2. 配置导航

```csharp
// MainViewModel.cs
var navigationData = new NavigationData[]
{
    new("10", "分类名", "") { WpfUiSymbol = SymbolRegular.Apps24, ParentNumber = null },
    new("101", "页面名", "ExampleView") { WpfUiSymbol = SymbolRegular.Document24, ParentNumber = "10" },
};
```

---

## 📚 代码示例系统

### 页面示例结构

```xml
<ui:CardExpander Header="📋 基础示例" IsExpanded="True">
    <StackPanel>
        <!-- 控件演示 -->
        <ui:Card>
            <my:ExampleControl Value="{Binding DemoValue}"/>
        </ui:Card>
        
        <!-- 代码示例 -->
        <codeExample:CodeExampleControl
            Title="基础用法"
            ShowTabs="True"
            XamlCode="{Binding BasicXamlExample}"
            CSharpCode="{Binding BasicCSharpExample}"/>
    </StackPanel>
</ui:CardExpander>
```

### 示例代码初始化

```csharp
private void InitializeCodeExamples()
{
    BasicXamlExample = @"<my:ExampleControl Value=""Hello""/>";
    BasicCSharpExample = @"var control = new ExampleControl { Value = ""Hello"" };";
}
```

---

## 🎨 样式和主题

### 使用动态资源

```xml
<Border Background="{DynamicResource ApplicationBackgroundBrush}"
        BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
        BorderThickness="1">
    <TextBlock Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
</Border>
```

### 常用 WPF-UI 资源

| 资源名称 | 用途 |
|----------|------|
| `ApplicationBackgroundBrush` | 应用背景色 |
| `ControlFillColorDefaultBrush` | 控件填充色 |
| `ControlStrokeColorDefaultBrush` | 控件边框色 |
| `TextFillColorPrimaryBrush` | 主要文本色 |
| `TextFillColorSecondaryBrush` | 次要文本色 |
| `AccentFillColorDefaultBrush` | 强调色 |

---

## 🔧 开发工具

### 日志使用

```csharp
private readonly YLoggerInstance _logger = YLogger.ForSilent<ClassName>();

_logger.Info("ℹ️ 信息日志");
_logger.Debug("🐛 调试日志");
_logger.Warning("⚠️ 警告日志");
_logger.Error("❌ 错误日志");
_logger.InfoDetailed($"📊 详细信息: {data}");
```

### 性能监控

```csharp
var stopwatch = Stopwatch.StartNew();
// 执行操作
stopwatch.Stop();
_logger.InfoDetailed($"⏱️ 操作耗时: {stopwatch.ElapsedMilliseconds}ms");
```

---

## 📁 项目结构

```
ZyloWPF/
├── 📁 Zylo.WPF/                    # 核心框架
│   ├── 📁 Controls/                # 控件库
│   ├── 📁 Models/                  # 数据模型
│   └── 📁 Helpers/                 # 辅助类
└── 📁 WPFTest/                     # 示例应用
    ├── 📁 Views/                   # 视图
    ├── 📁 ViewModels/              # 视图模型
    └── 📁 CodeExamples/            # 代码示例
```

---

## ✅ 开发检查清单

### 新控件开发

- [ ] 创建控件 XAML 和代码后台
- [ ] 定义必要的依赖属性
- [ ] 实现 WPF-UI 主题适配
- [ ] 创建示例页面和 ViewModel
- [ ] 编写 XAML 和 C# 代码示例
- [ ] 在 AppBootstrapper 中注册页面
- [ ] 在 MainViewModel 中添加导航项
- [ ] 添加单元测试
- [ ] 更新文档

### 代码质量

- [ ] 添加 XML 文档注释
- [ ] 使用适当的日志记录
- [ ] 处理异常情况
- [ ] 遵循命名约定
- [ ] 编译无警告
- [ ] 功能测试通过

---

## 🚀 常用命令

```bash
# 构建项目
dotnet build

# 运行测试应用
dotnet run --project WPFTest

# 发布应用
dotnet publish --configuration Release --output publish

# 运行测试
dotnet test

# 清理项目
dotnet clean
```

---

## 📞 获取帮助

- 📖 [完整开发指南](./ZyloWPF-Framework-Development-Guide.md)
- 📋 [控件实现状态](../WPF-UI-Controls-Implementation-Status.md)
- 🔧 [CodeExampleControl 文档](./CodeExampleControl开发文档.md)
- 📚 [MVVM 完整指南](./CommunityToolkit-MVVM-8.4.0-Complete-Guide.md)

---

**💡 提示**: 将此页面加入书签，开发时随时查阅！
