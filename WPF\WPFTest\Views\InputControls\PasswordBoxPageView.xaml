<UserControl x:Class="WPFTest.Views.InputControls.PasswordBoxPageView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:codeExample="clr-namespace:Zylo.WPF.Controls.CodeExample;assembly=Zylo.WPF"
             xmlns:mvvm="http://prismlibrary.com/"
             xmlns:inputControls="clr-namespace:WPFTest.ViewModels.InputControls"
             xmlns:attachedProps="clr-namespace:Zylo.WPF.AttachedProperties;assembly=Zylo.WPF"
             mc:Ignorable="d"
             d:DesignHeight="800" d:DesignWidth="1200"
             mvvm:ViewModelLocator.AutoWireViewModel="True"
             d:DataContext="{d:DesignInstance inputControls:PasswordBoxPageViewModel}">

    <UserControl.Resources>
        <!-- 🎨 所有样式来自 Zylo.WPF 项目 -->
        <!-- PasswordBox 样式已在 Zylo.WPF/Resources/PasswordBox/ 目录下定义 -->
        <!-- 可用样式：PasswordBoxBaseStyle, PasswordBoxStyle, SmallPasswordBoxStyle, LargePasswordBoxStyle, TransparentPasswordBoxStyle, RoundedPasswordBoxStyle -->
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" 
                  HorizontalScrollBarVisibility="Disabled"
                  Padding="24">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>    <!-- 标题区域 -->
                <RowDefinition Height="*"/>       <!-- 主要内容 -->
                <RowDefinition Height="Auto"/>    <!-- 底部操作栏 -->
            </Grid.RowDefinitions>

            <!-- 标题区域 -->
            <StackPanel Grid.Row="0" Margin="0,0,0,24">
                <TextBlock Text="🔒 PasswordBox 控件展示"
                           FontSize="32"
                           FontWeight="Bold"
                           Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                           Margin="0,0,0,8"/>
                <TextBlock Text="展示 PasswordBox 控件的各种样式、安全特性和交互效果" 
                           FontSize="16" 
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                           TextWrapping="Wrap"
                           Margin="0,0,0,12"/>
                
                <!-- 功能亮点 -->
                <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                    <Border Background="{DynamicResource SystemAccentColorSecondaryBrush}"
                            CornerRadius="12" Padding="8,4" Margin="0,0,8,0">
                        <TextBlock Text="🔒 安全输入" FontSize="12" Foreground="White"/>
                    </Border>
                    <Border Background="{DynamicResource SystemAccentColorSecondaryBrush}"
                            CornerRadius="12" Padding="8,4" Margin="0,0,8,0">
                        <TextBlock Text="👁️ 显示切换" FontSize="12" Foreground="White"/>
                    </Border>
                    <Border Background="{DynamicResource SystemAccentColorSecondaryBrush}"
                            CornerRadius="12" Padding="8,4" Margin="0,0,8,0">
                        <TextBlock Text="💪 强度检测" FontSize="12" Foreground="White"/>
                    </Border>
                    <Border Background="{DynamicResource SystemAccentColorSecondaryBrush}"
                            CornerRadius="12" Padding="8,4" Margin="0,0,8,0">
                        <TextBlock Text="🎨 多种样式" FontSize="12" Foreground="White"/>
                    </Border>
                </StackPanel>
                
                <!-- 状态显示 -->
                <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                        BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Padding="16,12"
                        Margin="0,16,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <ui:SymbolIcon Symbol="Info24" 
                                       FontSize="20" 
                                       Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                       VerticalAlignment="Center"
                                       Margin="0,0,12,0"/>
                        <TextBlock Grid.Column="1" 
                                   Text="{Binding StatusMessage}" 
                                   FontSize="14"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                   VerticalAlignment="Center"
                                   TextWrapping="Wrap"/>
                    </Grid>
                </Border>
            </StackPanel>

            <!-- 主要内容区域 -->
            <Border Grid.Row="1" Margin="0,0,0,20">
                <StackPanel>

                    <!-- 基础功能展示 -->
                    <ui:CardExpander Header="🎯 基础功能" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 PasswordBox 的基础功能和用法"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 控件展示区域 -->
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- 标准密码框 -->
                                <Border Grid.Column="0" Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                        CornerRadius="8" Padding="12" Margin="0,0,4,0">
                                    <StackPanel>
                                        <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                                            <TextBlock Text="🔒" FontSize="14" Margin="0,0,4,0"/>
                                            <TextBlock Text="标准密码框" FontWeight="Bold"/>
                                        </StackPanel>
                                        <TextBlock Text="基础密码输入功能"
                                                   FontSize="11"
                                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                   Margin="0,0,0,8"/>
                                        <PasswordBox Style="{StaticResource PasswordBoxStyle}"/>
                                    </StackPanel>
                                </Border>

                                <!-- 显示/隐藏切换 -->
                                <Border Grid.Column="1" Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                        CornerRadius="8" Padding="12" Margin="4,0">
                                    <StackPanel>
                                        <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                                            <TextBlock Text="👁️" FontSize="14" Margin="0,0,4,0"/>
                                            <TextBlock Text="显示切换" FontWeight="Bold"/>
                                        </StackPanel>
                                        <TextBlock Text="可切换显示密码"
                                                   FontSize="11"
                                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                   Margin="0,0,0,8"/>
                                        <Grid>
                                            <!-- 密码框（隐藏状态） -->
                                            <PasswordBox x:Name="TogglePasswordBox"
                                                         Style="{StaticResource PasswordBoxStyle}"
                                                         attachedProps:PasswordBoxProperties.EnablePasswordBinding="True"
                                                         attachedProps:PasswordBoxProperties.BoundPassword="{Binding TogglePassword, UpdateSourceTrigger=PropertyChanged}"
                                                         Visibility="{Binding IsPasswordVisible, Converter={StaticResource InverseBooleanToVisibilityConverter}, ConverterParameter=Inverse}"/>

                                            <!-- 文本框（显示状态） -->
                                            <ui:TextBox x:Name="ToggleTextBox"
                                                        Style="{StaticResource PasswordDisplayTextBoxStyle}"
                                                        Text="{Binding TogglePassword, UpdateSourceTrigger=PropertyChanged}"
                                                        PlaceholderText="可显示密码"
                                                        Visibility="{Binding IsPasswordVisible, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                                            <!-- 切换按钮 -->
                                            <ui:Button HorizontalAlignment="Right"
                                                       VerticalAlignment="Center"
                                                       Background="Transparent"
                                                       BorderThickness="0"
                                                       Padding="8,4"
                                                       Margin="0,0,4,0"
                                                       Command="{Binding TogglePasswordVisibilityCommand}">
                                                <ui:Button.Content>
                                                    <TextBlock Text="{Binding IsPasswordVisible, Converter={StaticResource BooleanToStringConverter}, ConverterParameter='🙈|👁️'}"
                                                               FontSize="16"/>
                                                </ui:Button.Content>
                                                <ui:Button.ToolTip>
                                                    <TextBlock Text="{Binding IsPasswordVisible, Converter={StaticResource BooleanToStringConverter}, ConverterParameter='隐藏密码|显示密码'}"/>
                                                </ui:Button.ToolTip>
                                            </ui:Button>
                                        </Grid>
                                    </StackPanel>
                                </Border>

                                <!-- 密码强度指示 -->
                                <Border Grid.Column="2" Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                        CornerRadius="8" Padding="12" Margin="4,0,0,0">
                                    <StackPanel>
                                        <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                                            <TextBlock Text="💪" FontSize="14" Margin="0,0,4,0"/>
                                            <TextBlock Text="强度检测" FontWeight="Bold"/>
                                        </StackPanel>
                                        <TextBlock Text="实时密码强度检测"
                                                   FontSize="11"
                                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                   Margin="0,0,0,8"/>
                                        <PasswordBox Style="{StaticResource PasswordBoxStyle}"
                                                     attachedProps:PasswordBoxProperties.EnablePasswordBinding="True"
                                                     attachedProps:PasswordBoxProperties.BoundPassword="{Binding StrengthPassword, UpdateSourceTrigger=PropertyChanged}"/>
                                        <ProgressBar Value="{Binding PasswordStrength}"
                                                     Maximum="100"
                                                     Height="4"
                                                     Margin="0,4,0,2"/>
                                        <TextBlock Text="{Binding PasswordStrengthText}"
                                                   FontSize="10"
                                                   Foreground="{Binding PasswordStrengthColor}"/>
                                    </StackPanel>
                                </Border>
                            </Grid>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="基础功能代码示例"
                                Language="XAML"
                                Description="展示 PasswordBox 的基础用法"
                                ShowTabs="True"
                                XamlCode="{Binding BasicXamlExample}"
                                CSharpCode="{Binding BasicCSharpExample}"
                                IsExpanded="False"
                                Margin="0,16,0,0"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 样式展示 -->
                    <ui:CardExpander Header="🎨 样式展示" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示来自 Zylo.WPF 的各种 PasswordBox 样式"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 样式展示区域 -->
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- 不同尺寸样式 -->
                                <Border Grid.Column="0" Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                        CornerRadius="8" Padding="12" Margin="0,0,4,0">
                                    <StackPanel>
                                        <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                                            <TextBlock Text="📏" FontSize="14" Margin="0,0,4,0"/>
                                            <TextBlock Text="不同尺寸" FontWeight="Bold"/>
                                        </StackPanel>
                                        <TextBlock Text="小型、标准、大型样式"
                                                   FontSize="11"
                                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                   Margin="0,0,0,8"/>

                                        <PasswordBox Style="{StaticResource SmallPasswordBoxStyle}"
                                                     Margin="0,0,0,4"/>
                                        <PasswordBox Style="{StaticResource PasswordBoxStyle}"
                                                     Margin="0,0,0,4"/>
                                        <PasswordBox Style="{StaticResource LargePasswordBoxStyle}"/>
                                    </StackPanel>
                                </Border>

                                <!-- 特殊样式 -->
                                <Border Grid.Column="1" Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                        CornerRadius="8" Padding="12" Margin="4,0">
                                    <StackPanel>
                                        <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                                            <TextBlock Text="✨" FontSize="14" Margin="0,0,4,0"/>
                                            <TextBlock Text="特殊样式" FontWeight="Bold"/>
                                        </StackPanel>
                                        <TextBlock Text="透明、圆角样式"
                                                   FontSize="11"
                                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                   Margin="0,0,0,8"/>

                                        <PasswordBox Style="{StaticResource TransparentPasswordBoxStyle}"
                                                     Margin="0,0,0,8"/>
                                        <PasswordBox Style="{StaticResource RoundedPasswordBoxStyle}"/>
                                    </StackPanel>
                                </Border>

                                <!-- 安全增强样式 -->
                                <Border Grid.Column="2" Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                        CornerRadius="8" Padding="12" Margin="4,0,0,0">
                                    <StackPanel>
                                        <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                                            <TextBlock Text="🛡️" FontSize="14" Margin="0,0,4,0"/>
                                            <TextBlock Text="安全增强" FontWeight="Bold"/>
                                        </StackPanel>
                                        <TextBlock Text="安全增强样式"
                                                   FontSize="11"
                                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                   Margin="0,0,0,8"/>

                                        <PasswordBox Style="{StaticResource SecurePasswordBoxStyle}"/>
                                    </StackPanel>
                                </Border>
                            </Grid>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="样式代码示例"
                                Language="XAML"
                                Description="展示如何使用 Zylo.WPF 中的 PasswordBox 样式"
                                ShowTabs="True"
                                XamlCode="{Binding StylesXamlExample}"
                                IsExpanded="False"
                                Margin="0,16,0,0"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 高级功能展示 -->
                    <ui:CardExpander Header="⚡ 高级功能" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 PasswordBox 的高级功能和自定义特性"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 高级功能展示区域 -->
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- 左列：密码验证 -->
                                <Border Grid.Column="0" Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                        CornerRadius="8" Padding="16" Margin="0,0,8,0">
                                    <StackPanel>
                                        <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                            <TextBlock Text="🔐" FontSize="16" Margin="0,0,8,0"/>
                                            <TextBlock Text="密码验证表单" FontWeight="Bold" FontSize="14"/>
                                        </StackPanel>

                                        <TextBlock Text="新密码" FontWeight="Medium" Margin="0,0,0,4"/>
                                        <PasswordBox Style="{StaticResource PasswordBoxStyle}"
                                                     attachedProps:PasswordBoxProperties.EnablePasswordBinding="True"
                                                     attachedProps:PasswordBoxProperties.BoundPassword="{Binding BasicPassword, UpdateSourceTrigger=PropertyChanged}"
                                                     Margin="0,0,0,8"/>

                                        <TextBlock Text="确认密码" FontWeight="Medium" Margin="0,0,0,4"/>
                                        <PasswordBox Style="{StaticResource PasswordBoxStyle}"
                                                     attachedProps:PasswordBoxProperties.EnablePasswordBinding="True"
                                                     attachedProps:PasswordBoxProperties.BoundPassword="{Binding ConfirmPassword, UpdateSourceTrigger=PropertyChanged}"
                                                     Margin="0,0,0,8"/>

                                        <!-- 密码匹配状态 -->
                                        <TextBlock Text="{Binding PasswordMatchMessage}"
                                                   FontSize="11"
                                                   Foreground="{Binding PasswordMatchColor}"
                                                   Margin="0,0,0,8"/>

                                        <ui:Button Content="验证密码"
                                                   Appearance="Primary"
                                                   HorizontalAlignment="Stretch"
                                                   Command="{Binding ValidatePasswordCommand}"
                                                   Margin="0,0,0,8"/>

                                        <!-- 验证结果显示 -->
                                        <Border Background="{DynamicResource ControlFillColorTertiaryBrush}"
                                                CornerRadius="4"
                                                Padding="12,8"
                                                Visibility="{Binding ValidationMessage, Converter={StaticResource StringToVisibilityConverter}}">
                                            <StackPanel>
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                                                    <TextBlock Text="验证结果" FontWeight="Medium" FontSize="12"/>
                                                    <ui:Button Content="👁️ 查看密码"
                                                               FontSize="10"
                                                               Padding="4,2"
                                                               Margin="8,0,0,0"
                                                               Appearance="Secondary"
                                                               Command="{Binding ShowPasswordCommand}"/>
                                                </StackPanel>

                                                <TextBlock Text="{Binding ValidationMessage}"
                                                           FontSize="11"
                                                           Foreground="{Binding ValidationMessageColor}"
                                                           TextWrapping="Wrap"/>

                                                <!-- 密码查看区域 -->
                                                <Border Background="{DynamicResource ControlFillColorDefaultBrush}"
                                                        CornerRadius="4"
                                                        Padding="8"
                                                        Margin="0,8,0,0"
                                                        Visibility="{Binding ShowPasswordDetails, Converter={StaticResource BooleanToVisibilityConverter}}">
                                                    <StackPanel>
                                                        <TextBlock Text="🔍 密码详情" FontWeight="Medium" FontSize="11" Margin="0,0,0,4"/>
                                                        <Grid>
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="Auto"/>
                                                                <ColumnDefinition Width="*"/>
                                                            </Grid.ColumnDefinitions>
                                                            <Grid.RowDefinitions>
                                                                <RowDefinition Height="Auto"/>
                                                                <RowDefinition Height="Auto"/>
                                                                <RowDefinition Height="Auto"/>
                                                            </Grid.RowDefinitions>

                                                            <TextBlock Grid.Row="0" Grid.Column="0" Text="新密码:" FontSize="10" Margin="0,0,8,2"/>
                                                            <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding BasicPassword}" FontSize="10" FontFamily="Consolas" Margin="0,0,0,2"/>

                                                            <TextBlock Grid.Row="1" Grid.Column="0" Text="确认密码:" FontSize="10" Margin="0,0,8,2"/>
                                                            <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding ConfirmPassword}" FontSize="10" FontFamily="Consolas" Margin="0,0,0,2"/>

                                                            <TextBlock Grid.Row="2" Grid.Column="0" Text="匹配状态:" FontSize="10" Margin="0,0,8,0"/>
                                                            <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding PasswordMatchMessage}" FontSize="10" Foreground="{Binding PasswordMatchColor}"/>
                                                        </Grid>
                                                    </StackPanel>
                                                </Border>
                                            </StackPanel>
                                        </Border>
                                    </StackPanel>
                                </Border>

                                <!-- 右列：密码生成器 -->
                                <Border Grid.Column="1" Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                        CornerRadius="8" Padding="16" Margin="8,0,0,0">
                                    <StackPanel>
                                        <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                            <TextBlock Text="🎲" FontSize="16" Margin="0,0,8,0"/>
                                            <TextBlock Text="密码生成器" FontWeight="Bold" FontSize="14"/>
                                        </StackPanel>

                                        <ui:TextBox Text="{Binding GeneratedPassword}"
                                                    IsReadOnly="True"
                                                    PlaceholderText="生成的密码将显示在这里"
                                                    FontFamily="Consolas"
                                                    Margin="0,0,0,8"/>

                                        <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                            <TextBlock Text="长度:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                            <Slider Value="{Binding PasswordLength}"
                                                    Minimum="8" Maximum="32" Width="80"
                                                    VerticalAlignment="Center"/>
                                            <TextBlock Text="{Binding PasswordLength}"
                                                       VerticalAlignment="Center" Margin="8,0,0,0"/>
                                        </StackPanel>

                                        <!-- 生成选项 -->
                                        <StackPanel Margin="0,0,0,8">
                                            <CheckBox Content="大写字母" IsChecked="{Binding IncludeUppercase}" FontSize="11" Margin="0,2"/>
                                            <CheckBox Content="小写字母" IsChecked="{Binding IncludeLowercase}" FontSize="11" Margin="0,2"/>
                                            <CheckBox Content="数字" IsChecked="{Binding IncludeNumbers}" FontSize="11" Margin="0,2"/>
                                            <CheckBox Content="特殊字符" IsChecked="{Binding IncludeSymbols}" FontSize="11" Margin="0,2"/>
                                            <CheckBox Content="避免相似字符" IsChecked="{Binding AvoidSimilarChars}" FontSize="11" Margin="0,2"/>
                                        </StackPanel>

                                        <StackPanel Orientation="Horizontal">
                                            <ui:Button Content="生成"
                                                       Appearance="Secondary"
                                                       Margin="0,0,4,0"
                                                       Command="{Binding GeneratePasswordCommand}"/>
                                            <ui:Button Content="复制"
                                                       Appearance="Secondary"
                                                       Command="{Binding CopyPasswordCommand}"/>
                                        </StackPanel>
                                    </StackPanel>
                                </Border>
                            </Grid>

                            <!-- 密码验证详情 -->
                            <Border Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                    CornerRadius="8" Padding="16" Margin="0,16,0,0">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                        <TextBlock Text="📋" FontSize="16" Margin="0,0,8,0"/>
                                        <TextBlock Text="密码验证详情" FontWeight="Bold" FontSize="14"/>
                                        <TextBlock Text="{Binding ValidationResult.ValidCount, StringFormat='({0}/9 项通过)'}"
                                                   FontSize="12"
                                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                   VerticalAlignment="Center"
                                                   Margin="8,0,0,0"/>
                                    </StackPanel>

                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- 左列：基础要求 -->
                                        <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                            <TextBlock Text="基础要求" FontWeight="Medium" FontSize="12" Margin="0,0,0,4"/>
                                            <StackPanel>
                                                <TextBlock FontSize="11" Margin="0,1">
                                                    <Run Text="{Binding ValidationResult.HasMinLength, Converter={StaticResource BooleanToStringConverter}, ConverterParameter='✅|❌'}"/>
                                                    <Run Text=" 长度 ≥ 8 字符"/>
                                                </TextBlock>
                                                <TextBlock FontSize="11" Margin="0,1">
                                                    <Run Text="{Binding ValidationResult.HasMaxLength, Converter={StaticResource BooleanToStringConverter}, ConverterParameter='✅|❌'}"/>
                                                    <Run Text=" 长度 ≤ 128 字符"/>
                                                </TextBlock>
                                                <TextBlock FontSize="11" Margin="0,1">
                                                    <Run Text="{Binding ValidationResult.HasUppercase, Converter={StaticResource BooleanToStringConverter}, ConverterParameter='✅|❌'}"/>
                                                    <Run Text=" 包含大写字母"/>
                                                </TextBlock>
                                            </StackPanel>
                                        </StackPanel>

                                        <!-- 中列：字符要求 -->
                                        <StackPanel Grid.Column="1" Margin="4,0">
                                            <TextBlock Text="字符要求" FontWeight="Medium" FontSize="12" Margin="0,0,0,4"/>
                                            <StackPanel>
                                                <TextBlock FontSize="11" Margin="0,1">
                                                    <Run Text="{Binding ValidationResult.HasLowercase, Converter={StaticResource BooleanToStringConverter}, ConverterParameter='✅|❌'}"/>
                                                    <Run Text=" 包含小写字母"/>
                                                </TextBlock>
                                                <TextBlock FontSize="11" Margin="0,1">
                                                    <Run Text="{Binding ValidationResult.HasNumbers, Converter={StaticResource BooleanToStringConverter}, ConverterParameter='✅|❌'}"/>
                                                    <Run Text=" 包含数字"/>
                                                </TextBlock>
                                                <TextBlock FontSize="11" Margin="0,1">
                                                    <Run Text="{Binding ValidationResult.HasSymbols, Converter={StaticResource BooleanToStringConverter}, ConverterParameter='✅|❌'}"/>
                                                    <Run Text=" 包含特殊字符"/>
                                                </TextBlock>
                                            </StackPanel>
                                        </StackPanel>

                                        <!-- 右列：安全要求 -->
                                        <StackPanel Grid.Column="2" Margin="8,0,0,0">
                                            <TextBlock Text="安全要求" FontWeight="Medium" FontSize="12" Margin="0,0,0,4"/>
                                            <StackPanel>
                                                <TextBlock FontSize="11" Margin="0,1">
                                                    <Run Text="{Binding ValidationResult.NoRepeatingChars, Converter={StaticResource BooleanToStringConverter}, ConverterParameter='✅|❌'}"/>
                                                    <Run Text=" 无重复字符"/>
                                                </TextBlock>
                                                <TextBlock FontSize="11" Margin="0,1">
                                                    <Run Text="{Binding ValidationResult.NoKeyboardSequence, Converter={StaticResource BooleanToStringConverter}, ConverterParameter='✅|❌'}"/>
                                                    <Run Text=" 无键盘序列"/>
                                                </TextBlock>
                                                <TextBlock FontSize="11" Margin="0,1">
                                                    <Run Text="{Binding ValidationResult.NoCommonPatterns, Converter={StaticResource BooleanToStringConverter}, ConverterParameter='✅|❌'}"/>
                                                    <Run Text=" 无常见模式"/>
                                                </TextBlock>
                                            </StackPanel>
                                        </StackPanel>
                                    </Grid>

                                    <!-- 验证进度条 -->
                                    <ProgressBar Value="{Binding ValidationResult.CompletionPercentage, Mode=OneWay}"
                                                 Maximum="100"
                                                 Height="6"
                                                 Margin="0,8,0,4"/>
                                    <TextBlock Text="{Binding ValidationResult.CompletionPercentage, StringFormat='验证完成度: {0:F0}%'}"
                                               FontSize="11"
                                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                               HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="高级功能代码示例"
                                Language="XAML"
                                Description="展示 PasswordBox 的高级用法和自定义特性"
                                ShowTabs="True"
                                XamlCode="{Binding AdvancedXamlExample}"
                                CSharpCode="{Binding AdvancedCSharpExample}"
                                IsExpanded="False"
                                Margin="0,16,0,0"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 安全特性展示 -->
                    <ui:CardExpander Header="🛡️ 安全特性" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 PasswordBox 的安全特性和防护机制"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 安全特性展示区域 -->
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- 安全输入控制 -->
                                <Border Grid.Column="0" Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                        CornerRadius="8" Padding="12" Margin="0,0,4,0">
                                    <StackPanel>
                                        <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                                            <TextBlock Text="🔒" FontSize="14" Margin="0,0,4,0"/>
                                            <TextBlock Text="安全输入" FontWeight="Bold"/>
                                        </StackPanel>
                                        <TextBlock Text="禁用复制粘贴和拖放"
                                                   FontSize="11"
                                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                   Margin="0,0,0,8"/>
                                        <PasswordBox Style="{StaticResource SecurePasswordBoxStyle}"/>
                                    </StackPanel>
                                </Border>

                                <!-- 密码泄露检测 -->
                                <Border Grid.Column="1" Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                        CornerRadius="8" Padding="12" Margin="4,0">
                                    <StackPanel>
                                        <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                                            <TextBlock Text="🔍" FontSize="14" Margin="0,0,4,0"/>
                                            <TextBlock Text="泄露检测" FontWeight="Bold"/>
                                        </StackPanel>
                                        <TextBlock Text="检测密码是否已泄露"
                                                   FontSize="11"
                                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                   Margin="0,0,0,8"/>
                                        <PasswordBox Style="{StaticResource PasswordBoxStyle}"/>
                                        <ui:Button Content="检测"
                                                   Appearance="Secondary"
                                                   FontSize="10"
                                                   Padding="4,2"
                                                   Margin="0,4,0,0"
                                                   Command="{Binding CheckPasswordBreachCommand}"/>
                                    </StackPanel>
                                </Border>

                                <!-- 双因素认证 -->
                                <Border Grid.Column="2" Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                        CornerRadius="8" Padding="12" Margin="4,0,0,0">
                                    <StackPanel>
                                        <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                                            <TextBlock Text="🔐" FontSize="14" Margin="0,0,4,0"/>
                                            <TextBlock Text="双因素认证" FontWeight="Bold"/>
                                        </StackPanel>
                                        <TextBlock Text="密码+验证码双重验证"
                                                   FontSize="11"
                                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                   Margin="0,0,0,8"/>
                                        <PasswordBox Style="{StaticResource PasswordBoxStyle}"
                                                     attachedProps:PasswordBoxProperties.EnablePasswordBinding="True"
                                                     attachedProps:PasswordBoxProperties.BoundPassword="{Binding BasicPassword, UpdateSourceTrigger=PropertyChanged}"
                                                     Margin="0,0,0,4"/>
                                        <ui:TextBox PlaceholderText="验证码"
                                                    MaxLength="6"
                                                    HorizontalContentAlignment="Center"
                                                    FontFamily="Consolas"
                                                    Margin="0,0,0,4"/>
                                        <ui:Button Content="验证"
                                                   Appearance="Primary"
                                                   FontSize="10"
                                                   Padding="4,2"
                                                   Command="{Binding VerifyTwoFactorCommand}"/>
                                    </StackPanel>
                                </Border>
                            </Grid>

                            <!-- 密码核对演示 -->
                            <Border Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                    CornerRadius="8" Padding="16" Margin="0,16,0,0">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                        <TextBlock Text="🔍" FontSize="16" Margin="0,0,8,0"/>
                                        <TextBlock Text="密码核对演示" FontWeight="Bold" FontSize="14"/>
                                    </StackPanel>

                                    <TextBlock Text="模拟密码" FontWeight="Medium" Margin="0,0,0,4"/>
                                    <ui:TextBox Text="{Binding DemoPassword, UpdateSourceTrigger=PropertyChanged}"
                                                PlaceholderText="输入要核对的密码"
                                                Margin="0,0,0,8"/>

                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <ui:Button Grid.Column="0" Content="核对密码"
                                                   Appearance="Secondary"
                                                   HorizontalAlignment="Stretch"
                                                   Command="{Binding VerifyDemoPasswordCommand}"
                                                   Margin="0,0,4,0"/>

                                        <ui:Button Grid.Column="1" Content="生成测试密码"
                                                   Appearance="Primary"
                                                   FontSize="10"
                                                   Padding="8,4"
                                                   Command="{Binding GenerateTestPasswordCommand}"/>
                                    </Grid>

                                    <!-- 核对结果 -->
                                    <Border Background="{DynamicResource ControlFillColorTertiaryBrush}"
                                            CornerRadius="4"
                                            Padding="8"
                                            Margin="0,8,0,0"
                                            Visibility="{Binding DemoVerificationResult, Converter={StaticResource StringToVisibilityConverter}}">
                                        <TextBlock Text="{Binding DemoVerificationResult}"
                                                   FontSize="11"
                                                   Foreground="{Binding DemoVerificationColor}"
                                                   TextWrapping="Wrap"/>
                                    </Border>
                                </StackPanel>
                            </Border>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="安全特性代码示例"
                                Language="XAML"
                                Description="展示 PasswordBox 的安全特性和防护机制"
                                ShowTabs="True"
                                XamlCode="{Binding SecurityXamlExample}"
                                CSharpCode="{Binding SecurityCSharpExample}"
                                IsExpanded="False"
                                Margin="0,16,0,0"/>
                        </StackPanel>
                    </ui:CardExpander>

                </StackPanel>
            </Border>

            <!-- 底部操作栏 -->
            <Border Grid.Row="2" 
                    Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Padding="16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 统计信息 -->
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock Text="{Binding InteractionCount, StringFormat='交互次数: {0}'}" 
                                   FontSize="14" 
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   Margin="0,0,20,0"/>
                        <TextBlock Text="{Binding LastAction, StringFormat='最后操作: {0}'}" 
                                   FontSize="14" 
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                    </StackPanel>

                    <!-- 操作按钮 -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <ui:Button Content="重置计数" 
                                   Command="{Binding ResetCountCommand}"
                                   Appearance="Secondary"
                                   Margin="0,0,8,0"/>
                        <ui:Button Content="清除密码" 
                                   Command="{Binding ClearPasswordsCommand}"
                                   Appearance="Secondary"/>
                    </StackPanel>
                </Grid>
            </Border>

        </Grid>
    </ScrollViewer>
</UserControl>
