// GroupBox 高级 C# 示例
// 展示如何通过代码创建复杂的 GroupBox 结构和功能

using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace WPFTest.Examples.LayoutControls
{
    public class GroupBoxAdvancedExample
    {
        /// <summary>
        /// 创建嵌套 GroupBox
        /// </summary>
        public static GroupBox CreateNestedGroupBox()
        {
            var parentGroupBox = new GroupBox
            {
                Header = "嵌套分组示例",
                Background = new SolidColorBrush(Colors.LightGray),
                BorderBrush = new SolidColorBrush(Colors.Gray),
                BorderThickness = new Thickness(1),
                Margin = new Thickness(8)
            };

            var parentContent = new StackPanel
            {
                Margin = new Thickness(16)
            };

            parentContent.Children.Add(new TextBlock
            {
                Text = "这是父级分组的内容。",
                Margin = new Thickness(0, 0, 0, 12)
            });

            var grid = new Grid();
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            // 创建子分组 A
            var childGroupBoxA = CreateChildGroupBox("子分组 A", "GroupA", Colors.LightBlue);
            Grid.SetColumn(childGroupBoxA, 0);
            childGroupBoxA.Margin = new Thickness(0, 0, 8, 0);
            grid.Children.Add(childGroupBoxA);

            // 创建子分组 B
            var childGroupBoxB = CreateChildGroupBox("子分组 B", "GroupB", Colors.LightGreen);
            Grid.SetColumn(childGroupBoxB, 1);
            childGroupBoxB.Margin = new Thickness(8, 0, 0, 0);
            grid.Children.Add(childGroupBoxB);

            parentContent.Children.Add(grid);
            parentGroupBox.Content = parentContent;

            return parentGroupBox;
        }

        /// <summary>
        /// 创建自定义样式的 GroupBox
        /// </summary>
        public static GroupBox CreateCustomStyledGroupBox()
        {
            var groupBox = new GroupBox
            {
                Header = "自定义样式分组",
                Background = new SolidColorBrush(Color.FromRgb(45, 55, 72)), // #2D3748
                Foreground = Brushes.White,
                BorderBrush = new SolidColorBrush(Color.FromRgb(74, 85, 104)), // #4A5568
                BorderThickness = new Thickness(2),
                Padding = new Thickness(12),
                Margin = new Thickness(8)
            };

            var grid = new Grid
            {
                Margin = new Thickness(16)
            };

            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(2, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            // 左侧配置选项
            var leftPanel = new StackPanel
            {
                Margin = new Thickness(0, 0, 16, 0)
            };

            leftPanel.Children.Add(new TextBlock
            {
                Text = "配置选项",
                Foreground = Brushes.White,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 12)
            });

            var comboBox = new ComboBox
            {
                Margin = new Thickness(0, 0, 0, 8)
            };
            comboBox.Items.Add(new ComboBoxItem { Content = "选项 1" });
            comboBox.Items.Add(new ComboBoxItem { Content = "选项 2", IsSelected = true });
            comboBox.Items.Add(new ComboBoxItem { Content = "选项 3" });
            leftPanel.Children.Add(comboBox);

            leftPanel.Children.Add(new Slider
            {
                Minimum = 0,
                Maximum = 100,
                Value = 60,
                Margin = new Thickness(0, 0, 0, 8)
            });

            leftPanel.Children.Add(new TextBlock
            {
                Text = "当前值: 60",
                Foreground = Brushes.White,
                FontSize = 12
            });

            Grid.SetColumn(leftPanel, 0);
            grid.Children.Add(leftPanel);

            // 右侧操作按钮
            var rightPanel = new StackPanel();

            rightPanel.Children.Add(new TextBlock
            {
                Text = "操作",
                Foreground = Brushes.White,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 12)
            });

            rightPanel.Children.Add(new Button
            {
                Content = "保存",
                Margin = new Thickness(0, 0, 0, 8)
            });

            rightPanel.Children.Add(new Button
            {
                Content = "重置"
            });

            Grid.SetColumn(rightPanel, 1);
            grid.Children.Add(rightPanel);

            groupBox.Content = grid;
            return groupBox;
        }

        /// <summary>
        /// 创建复杂表单 GroupBox
        /// </summary>
        public static GroupBox CreateComplexFormGroupBox()
        {
            var groupBox = new GroupBox
            {
                Header = "用户注册表单",
                Background = Brushes.White,
                BorderBrush = Brushes.LightGray,
                BorderThickness = new Thickness(1),
                Margin = new Thickness(8)
            };

            var grid = new Grid
            {
                Margin = new Thickness(16)
            };

            // 设置列定义
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            // 设置行定义
            for (int i = 0; i < 6; i++)
            {
                grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            }

            // 基本信息标题
            var basicInfoTitle = new TextBlock
            {
                Text = "基本信息",
                FontWeight = FontWeights.Bold,
                FontSize = 14,
                Margin = new Thickness(0, 0, 0, 12)
            };
            Grid.SetRow(basicInfoTitle, 0);
            Grid.SetColumn(basicInfoTitle, 0);
            Grid.SetColumnSpan(basicInfoTitle, 4);
            grid.Children.Add(basicInfoTitle);

            // 添加表单字段
            AddFormField(grid, "用户名:", "", 1, 0);
            AddPasswordField(grid, "密码:", 1, 2);
            AddFormField(grid, "邮箱:", "", 2, 0);
            AddFormField(grid, "电话:", "", 2, 2);

            // 个人信息标题
            var personalInfoTitle = new TextBlock
            {
                Text = "个人信息",
                FontWeight = FontWeights.Bold,
                FontSize = 14,
                Margin = new Thickness(0, 12, 0, 12)
            };
            Grid.SetRow(personalInfoTitle, 3);
            Grid.SetColumn(personalInfoTitle, 0);
            Grid.SetColumnSpan(personalInfoTitle, 4);
            grid.Children.Add(personalInfoTitle);

            // 性别选择
            AddGenderField(grid, 4, 0);
            AddFormField(grid, "年龄:", "", 4, 2);

            // 操作按钮
            var buttonPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Right,
                Margin = new Thickness(0, 16, 0, 0)
            };

            buttonPanel.Children.Add(new Button
            {
                Content = "重置",
                Margin = new Thickness(0, 0, 8, 0)
            });

            buttonPanel.Children.Add(new Button
            {
                Content = "注册"
            });

            Grid.SetRow(buttonPanel, 5);
            Grid.SetColumn(buttonPanel, 0);
            Grid.SetColumnSpan(buttonPanel, 4);
            grid.Children.Add(buttonPanel);

            groupBox.Content = grid;
            return groupBox;
        }

        /// <summary>
        /// 创建多级嵌套 GroupBox
        /// </summary>
        public static GroupBox CreateMultiLevelNestedGroupBox()
        {
            var level1GroupBox = new GroupBox
            {
                Header = "多级嵌套示例",
                Background = new SolidColorBrush(Colors.LightBlue),
                BorderBrush = new SolidColorBrush(Colors.Blue),
                BorderThickness = new Thickness(1),
                Margin = new Thickness(8)
            };

            var level1Content = new StackPanel
            {
                Margin = new Thickness(16)
            };

            level1Content.Children.Add(new TextBlock
            {
                Text = "第一级分组内容",
                Margin = new Thickness(0, 0, 0, 12)
            });

            // 第二级 GroupBox
            var level2GroupBox = new GroupBox
            {
                Header = "第二级分组",
                Background = new SolidColorBrush(Colors.LightYellow),
                Margin = new Thickness(0, 0, 0, 12)
            };

            var level2Content = new StackPanel
            {
                Margin = new Thickness(12)
            };

            level2Content.Children.Add(new TextBlock
            {
                Text = "第二级分组内容",
                Margin = new Thickness(0, 0, 0, 8)
            });

            // 第三级 GroupBox
            var level3GroupBox = new GroupBox
            {
                Header = "第三级分组",
                Background = new SolidColorBrush(Colors.LightGreen)
            };

            var level3Content = new StackPanel
            {
                Margin = new Thickness(12)
            };

            level3Content.Children.Add(new TextBlock
            {
                Text = "第三级分组内容"
            });

            level3Content.Children.Add(new CheckBox
            {
                Content = "深层选项",
                Margin = new Thickness(0, 8, 0, 0)
            });

            level3GroupBox.Content = level3Content;
            level2Content.Children.Add(level3GroupBox);
            level2GroupBox.Content = level2Content;
            level1Content.Children.Add(level2GroupBox);

            level1Content.Children.Add(new Button
            {
                Content = "提交所有设置",
                HorizontalAlignment = HorizontalAlignment.Left
            });

            level1GroupBox.Content = level1Content;
            return level1GroupBox;
        }

        #region 辅助方法

        /// <summary>
        /// 创建子分组 GroupBox
        /// </summary>
        private static GroupBox CreateChildGroupBox(string header, string groupName, Color backgroundColor)
        {
            var groupBox = new GroupBox
            {
                Header = header,
                Background = new SolidColorBrush(backgroundColor)
            };

            var content = new StackPanel
            {
                Margin = new Thickness(12)
            };

            content.Children.Add(new RadioButton
            {
                Content = $"选项 {groupName}1",
                GroupName = groupName,
                IsChecked = true,
                Margin = new Thickness(0, 0, 0, 4)
            });

            content.Children.Add(new RadioButton
            {
                Content = $"选项 {groupName}2",
                GroupName = groupName,
                Margin = new Thickness(0, 0, 0, 4)
            });

            content.Children.Add(new RadioButton
            {
                Content = $"选项 {groupName}3",
                GroupName = groupName
            });

            groupBox.Content = content;
            return groupBox;
        }

        /// <summary>
        /// 添加表单字段
        /// </summary>
        private static void AddFormField(Grid grid, string labelText, string valueText, int row, int column)
        {
            var label = new TextBlock
            {
                Text = labelText,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(0, 0, 8, 8)
            };
            Grid.SetRow(label, row);
            Grid.SetColumn(label, column);
            grid.Children.Add(label);

            var textBox = new TextBox
            {
                Text = valueText,
                Margin = new Thickness(0, 0, column == 0 ? 16 : 0, 8)
            };
            Grid.SetRow(textBox, row);
            Grid.SetColumn(textBox, column + 1);
            grid.Children.Add(textBox);
        }

        /// <summary>
        /// 添加密码字段
        /// </summary>
        private static void AddPasswordField(Grid grid, string labelText, int row, int column)
        {
            var label = new TextBlock
            {
                Text = labelText,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(0, 0, 8, 8)
            };
            Grid.SetRow(label, row);
            Grid.SetColumn(label, column);
            grid.Children.Add(label);

            var passwordBox = new PasswordBox
            {
                Margin = new Thickness(0, 0, 0, 8)
            };
            Grid.SetRow(passwordBox, row);
            Grid.SetColumn(passwordBox, column + 1);
            grid.Children.Add(passwordBox);
        }

        /// <summary>
        /// 添加性别字段
        /// </summary>
        private static void AddGenderField(Grid grid, int row, int column)
        {
            var label = new TextBlock
            {
                Text = "性别:",
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(0, 0, 8, 8)
            };
            Grid.SetRow(label, row);
            Grid.SetColumn(label, column);
            grid.Children.Add(label);

            var genderPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 0, 16, 8)
            };

            genderPanel.Children.Add(new RadioButton
            {
                Content = "男",
                GroupName = "Gender",
                IsChecked = true,
                Margin = new Thickness(0, 0, 16, 0)
            });

            genderPanel.Children.Add(new RadioButton
            {
                Content = "女",
                GroupName = "Gender"
            });

            Grid.SetRow(genderPanel, row);
            Grid.SetColumn(genderPanel, column + 1);
            grid.Children.Add(genderPanel);
        }

        #endregion
    }
}
