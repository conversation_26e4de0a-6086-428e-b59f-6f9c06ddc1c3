// Border 高级 C# 示例
// 展示如何通过代码创建复杂的 Border 效果

using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;

namespace WPFTest.Examples.LayoutControls
{
    public class BorderAdvancedExample
    {
        /// <summary>
        /// 创建带渐变背景的 Border
        /// </summary>
        public static Border CreateGradientBorder()
        {
            var border = new Border
            {
                BorderBrush = Brushes.Gray,
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(8),
                Padding = new Thickness(16),
                Margin = new Thickness(8)
            };

            // 创建线性渐变背景
            var gradientBrush = new LinearGradientBrush
            {
                StartPoint = new Point(0, 0),
                EndPoint = new Point(1, 1)
            };
            gradientBrush.GradientStops.Add(new GradientStop(Color.FromRgb(107, 115, 255), 0));
            gradientBrush.GradientStops.Add(new GradientStop(Color.FromRgb(157, 255, 173), 1));

            border.Background = gradientBrush;

            border.Child = new TextBlock
            {
                Text = "渐变背景",
                Foreground = Brushes.White,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };

            return border;
        }

        /// <summary>
        /// 创建带径向渐变的 Border
        /// </summary>
        public static Border CreateRadialGradientBorder()
        {
            var border = new Border
            {
                BorderBrush = Brushes.Gray,
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(8),
                Padding = new Thickness(16),
                Margin = new Thickness(8)
            };

            // 创建径向渐变背景
            var radialBrush = new RadialGradientBrush();
            radialBrush.GradientStops.Add(new GradientStop(Color.FromRgb(255, 107, 107), 0));
            radialBrush.GradientStops.Add(new GradientStop(Color.FromRgb(78, 205, 196), 1));

            border.Background = radialBrush;

            border.Child = new TextBlock
            {
                Text = "径向渐变",
                Foreground = Brushes.White,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };

            return border;
        }

        /// <summary>
        /// 创建带动画效果的 Border
        /// </summary>
        public static Border CreateAnimatedBorder()
        {
            var border = new Border
            {
                Background = Brushes.LightBlue,
                BorderBrush = Brushes.Blue,
                BorderThickness = new Thickness(2),
                CornerRadius = new CornerRadius(8),
                Padding = new Thickness(16),
                Margin = new Thickness(8),
                Cursor = System.Windows.Input.Cursors.Hand,
                RenderTransform = new ScaleTransform(),
                RenderTransformOrigin = new Point(0.5, 0.5)
            };

            border.Child = new TextBlock
            {
                Text = "动画 Border",
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };

            // 添加鼠标悬停动画
            border.MouseEnter += (s, e) =>
            {
                var scaleAnimation = new DoubleAnimation
                {
                    To = 1.1,
                    Duration = TimeSpan.FromMilliseconds(200),
                    EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseOut }
                };

                border.RenderTransform.BeginAnimation(ScaleTransform.ScaleXProperty, scaleAnimation);
                border.RenderTransform.BeginAnimation(ScaleTransform.ScaleYProperty, scaleAnimation);
            };

            border.MouseLeave += (s, e) =>
            {
                var scaleAnimation = new DoubleAnimation
                {
                    To = 1.0,
                    Duration = TimeSpan.FromMilliseconds(200),
                    EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseOut }
                };

                border.RenderTransform.BeginAnimation(ScaleTransform.ScaleXProperty, scaleAnimation);
                border.RenderTransform.BeginAnimation(ScaleTransform.ScaleYProperty, scaleAnimation);
            };

            return border;
        }

        /// <summary>
        /// 创建带阴影效果的卡片式 Border
        /// </summary>
        public static Border CreateCardBorder()
        {
            var border = new Border
            {
                Background = Brushes.White,
                BorderBrush = Brushes.LightGray,
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(12),
                Padding = new Thickness(20),
                Margin = new Thickness(8)
            };

            // 添加阴影效果
            border.Effect = new DropShadowEffect
            {
                Color = Colors.Black,
                Opacity = 0.1,
                ShadowDepth = 4,
                BlurRadius = 12
            };

            // 创建复杂的内容布局
            var grid = new Grid();
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            // 标题
            var title = new TextBlock
            {
                Text = "卡片式 Border",
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 12)
            };
            Grid.SetRow(title, 0);
            grid.Children.Add(title);

            // 内容
            var content = new TextBlock
            {
                Text = "这是一个复杂的 Border 布局示例，包含标题、内容和操作按钮。使用了阴影效果和圆角设计。",
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(0, 0, 0, 16)
            };
            Grid.SetRow(content, 1);
            grid.Children.Add(content);

            // 操作按钮
            var buttonPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Right
            };

            var cancelButton = new Button
            {
                Content = "取消",
                Margin = new Thickness(0, 0, 8, 0),
                Padding = new Thickness(12, 6)
            };

            var okButton = new Button
            {
                Content = "确定",
                Padding = new Thickness(12, 6)
            };

            buttonPanel.Children.Add(cancelButton);
            buttonPanel.Children.Add(okButton);

            Grid.SetRow(buttonPanel, 2);
            grid.Children.Add(buttonPanel);

            border.Child = grid;
            return border;
        }

        /// <summary>
        /// 创建带颜色动画的 Border
        /// </summary>
        public static Border CreateColorAnimatedBorder()
        {
            var border = new Border
            {
                Background = new SolidColorBrush(Colors.LightGray),
                BorderBrush = Brushes.Gray,
                BorderThickness = new Thickness(2),
                CornerRadius = new CornerRadius(8),
                Padding = new Thickness(16),
                Margin = new Thickness(8),
                Cursor = System.Windows.Input.Cursors.Hand
            };

            border.Child = new TextBlock
            {
                Text = "颜色动画",
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };

            // 添加颜色变化动画
            border.MouseEnter += (s, e) =>
            {
                var colorAnimation = new ColorAnimation
                {
                    To = Color.FromRgb(107, 115, 255),
                    Duration = TimeSpan.FromMilliseconds(300),
                    EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseOut }
                };

                var brush = border.Background as SolidColorBrush;
                if (brush != null)
                {
                    brush.BeginAnimation(SolidColorBrush.ColorProperty, colorAnimation);
                }
            };

            border.MouseLeave += (s, e) =>
            {
                var colorAnimation = new ColorAnimation
                {
                    To = Colors.LightGray,
                    Duration = TimeSpan.FromMilliseconds(300),
                    EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseOut }
                };

                var brush = border.Background as SolidColorBrush;
                if (brush != null)
                {
                    brush.BeginAnimation(SolidColorBrush.ColorProperty, colorAnimation);
                }
            };

            return border;
        }

        /// <summary>
        /// 创建带图片背景的 Border
        /// </summary>
        public static Border CreateImageBorder(string imagePath)
        {
            var border = new Border
            {
                BorderBrush = Brushes.White,
                BorderThickness = new Thickness(3),
                CornerRadius = new CornerRadius(8),
                Margin = new Thickness(8),
                Width = 200,
                Height = 150
            };

            // 设置图片背景
            var imageBrush = new ImageBrush
            {
                ImageSource = new System.Windows.Media.Imaging.BitmapImage(new Uri(imagePath, UriKind.RelativeOrAbsolute)),
                Stretch = Stretch.UniformToFill
            };

            border.Background = imageBrush;

            // 添加半透明覆盖层和文字
            var overlay = new Border
            {
                Background = new SolidColorBrush(Color.FromArgb(128, 0, 0, 0)),
                CornerRadius = new CornerRadius(5),
                Child = new TextBlock
                {
                    Text = "图片背景",
                    Foreground = Brushes.White,
                    FontSize = 16,
                    FontWeight = FontWeights.Bold,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center
                }
            };

            border.Child = overlay;
            return border;
        }
    }
}
