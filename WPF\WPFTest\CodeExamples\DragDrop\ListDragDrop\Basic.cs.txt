// gong-wpf-dragdrop 列表拖拽基础功能 C# 示例
//
// 这个示例展示了如何使用 gong-wpf-dragdrop 库实现基础的列表拖拽功能
// 主要特性：
// 1. 使用 CommunityToolkit.Mvvm 的 ObservableObject 和 ObservableProperty
// 2. 简单的数据绑定和集合管理
// 3. 为进阶功能（自定义拖拽处理器）做准备

using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using GongSolutions.Wpf.DragDrop;

namespace WPFTest.ViewModels.DragDrop
{
    /// <summary>
    /// 列表拖拽基础示例 ViewModel
    ///
    /// 这个类展示了最简单的拖拽实现方式：
    /// - 仅使用数据绑定，不实现自定义拖拽接口
    /// - 依赖 gong-wpf-dragdrop 的默认行为
    /// - 适用于简单的拖拽场景
    /// </summary>
    public partial class ListDragDropViewModel : ObservableObject
    {
        /// <summary>
        /// 源列表 - 可拖拽的项目
        ///
        /// 使用 ObservableCollection 确保 UI 自动更新
        /// 当项目被拖拽移除时，UI 会自动反映变化
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<DragDropItem> sourceItems = new();

        /// <summary>
        /// 目标列表 - 可接收拖拽的项目
        ///
        /// 初始为空，等待从源列表拖拽项目过来
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<DragDropItem> targetItems = new();

        /// <summary>
        /// 构造函数
        /// 初始化 ViewModel 并加载示例数据
        /// </summary>
        public ListDragDropViewModel()
        {
            InitializeData();
        }

        /// <summary>
        /// 初始化示例数据
        /// 创建一些测试用的拖拽项目
        /// </summary>
        private void InitializeData()
        {
            // 添加源项目 - 这些项目可以被拖拽到目标列表
            for (int i = 1; i <= 5; i++)
            {
                SourceItems.Add(new DragDropItem
                {
                    Id = i,
                    Name = $"源项目 {i}",
                    Description = $"这是第 {i} 个可拖拽的项目",
                    Category = "源类别"
                });
            }
        }
    }

    /// <summary>
    /// 拖拽项目数据模型
    ///
    /// 这个类定义了可以被拖拽的数据项的结构
    /// 使用 CommunityToolkit.Mvvm 的 ObservableObject 确保属性变化通知
    /// </summary>
    public partial class DragDropItem : ObservableObject
    {
        /// <summary>
        /// 项目唯一标识符
        /// </summary>
        [ObservableProperty]
        private int id;

        /// <summary>
        /// 项目显示名称
        /// </summary>
        [ObservableProperty]
        private string name = string.Empty;

        /// <summary>
        /// 项目详细描述
        /// </summary>
        [ObservableProperty]
        private string description = string.Empty;

        /// <summary>
        /// 项目分类
        /// 可用于实现分类拖拽规则
        /// </summary>
        [ObservableProperty]
        private string category = string.Empty;

        /// <summary>
        /// 项目优先级
        /// 可用于排序和显示
        /// </summary>
        [ObservableProperty]
        private int priority;

        /// <summary>
        /// 重写 ToString 方法，便于调试和日志记录
        /// </summary>
        /// <returns>项目的字符串表示</returns>
        public override string ToString()
        {
            return $"{Name} (ID: {Id}, Category: {Category})";
        }
    }
}

// ============================================================================
// 使用说明和最佳实践
// ============================================================================

// 1. 安装必要的 NuGet 包：
//    - gong-wpf-dragdrop (拖拽功能)
//    - CommunityToolkit.Mvvm (MVVM 支持)

// 2. 在 XAML 中添加命名空间：
//    xmlns:dd="urn:gong-wpf-dragdrop"

// 3. 基础拖拽设置（在 ListView 或其他 ItemsControl 上）：
//    dd:DragDrop.IsDragSource="True"     - 启用拖拽源功能
//    dd:DragDrop.IsDropTarget="True"     - 启用放置目标功能

// 4. 可选的视觉效果设置：
//    dd:DragDrop.UseDefaultDragAdorner="True"        - 显示默认拖拽装饰器
//    dd:DragDrop.DefaultDragAdornerOpacity="0.7"     - 设置装饰器透明度

// 5. 高级功能（需要实现 IDragSource 和 IDropTarget 接口）：
//    dd:DragDrop.DragHandler="{Binding}"  - 自定义拖拽处理器
//    dd:DragDrop.DropHandler="{Binding}"  - 自定义放置处理器

// 6. 常见的拖拽装饰器类型：
//    - DropTargetAdorners.Insert     - 显示插入位置指示器
//    - DropTargetAdorners.Highlight  - 高亮整个放置区域
//    - DropTargetAdorners.Touch      - 触摸友好的装饰器

// 7. 拖拽效果类型：
//    - DragDropEffects.Move   - 移动操作（默认）
//    - DragDropEffects.Copy   - 复制操作
//    - DragDropEffects.Link   - 链接操作
//    - DragDropEffects.None   - 禁止拖拽
