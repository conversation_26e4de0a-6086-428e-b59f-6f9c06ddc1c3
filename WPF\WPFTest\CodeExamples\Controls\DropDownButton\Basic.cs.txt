// DropDownButton C# 基础用法示例
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace WPFTest.ViewModels.ButtonControls
{
    public partial class DropDownButtonPageViewModel : ObservableObject
    {
        /// <summary>
        /// DropDownButton 交互命令
        /// </summary>
        [RelayCommand]
        private void HandleDropDownButtonInteraction(string parameter)
        {
            var message = parameter switch
            {
                // 主按钮操作
                "文件操作" => "📁 点击了文件操作主按钮",
                "编辑" => "✏️ 点击了编辑主按钮",
                
                // 文件菜单项
                "新建" => "📄 执行了新建操作",
                "打开" => "📂 执行了打开操作",
                "保存" => "💾 执行了保存操作",
                "退出" => "🚪 执行了退出操作",
                
                // 编辑菜单项
                "复制" => "📋 执行了复制操作",
                "剪切" => "✂️ 执行了剪切操作",
                "粘贴" => "📋 执行了粘贴操作",
                
                // 外观样式
                "Primary" => "🔵 点击了 Primary 样式按钮",
                "Secondary" => "⚪ 点击了 Secondary 样式按钮",
                "Success" => "✅ 点击了 Success 样式按钮",
                "Danger" => "❌ 点击了 Danger 样式按钮",
                
                _ => $"🎯 执行了操作: {parameter}"
            };
            
            StatusMessage = message;
            InteractionCount++;
        }

        /// <summary>
        /// DropDownButton 状态属性
        /// </summary>
        [ObservableProperty]
        private string selectedOption = "默认选项";

        /// <summary>
        /// DropDownButton 启用状态
        /// </summary>
        [ObservableProperty]
        private bool isDropDownEnabled = true;

        /// <summary>
        /// 切换 DropDownButton 启用状态
        /// </summary>
        [RelayCommand]
        private void ToggleDropDownEnabled()
        {
            IsDropDownEnabled = !IsDropDownEnabled;
            StatusMessage = IsDropDownEnabled ? "DropDownButton 已启用" : "DropDownButton 已禁用";
        }

        /// <summary>
        /// 更新选择的选项
        /// </summary>
        /// <param name="option">选择的选项</param>
        private void UpdateSelectedOption(string option)
        {
            SelectedOption = option;
            StatusMessage = $"当前选择: {option}";
        }

        /// <summary>
        /// 处理菜单项选择
        /// </summary>
        /// <param name="menuItem">菜单项</param>
        [RelayCommand]
        private void HandleMenuItemSelection(string menuItem)
        {
            UpdateSelectedOption(menuItem);
            InteractionCount++;
            
            // 根据菜单项执行相应的业务逻辑
            switch (menuItem)
            {
                case "新建":
                    // 执行新建文档逻辑
                    break;
                case "打开":
                    // 执行打开文件逻辑
                    break;
                case "保存":
                    // 执行保存文件逻辑
                    break;
                // 添加更多菜单项处理逻辑
            }
        }
    }
}
