using System.Globalization;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using WPFTest.ViewModels.DragDrop;

namespace WPFTest.Views.DragDrop
{
    /// <summary>
    /// TreeDragDropExample.xaml 的交互逻辑
    /// </summary>
    public partial class TreeDragDropExample : UserControl
    {
        public TreeDragDropExample()
        {
            InitializeComponent();
        }
    }

    /// <summary>
    /// 节点类型到图标的转换器
    /// 根据节点类型返回对应的图标
    /// </summary>
    public class NodeTypeToIconConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is TreeNodeType nodeType)
            {
                return nodeType switch
                {
                    TreeNodeType.Folder => "Folder24",
                    TreeNodeType.Project => "Apps24",
                    TreeNodeType.Task => "TaskListSquare24",
                    TreeNodeType.Document => "Document24",
                    TreeNodeType.File => "DocumentText24",
                    _ => "Circle24"
                };
            }
            return "Circle24";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 层级到边距的转换器
    /// 根据节点层级计算左边距，实现缩进效果
    /// </summary>
    public class LevelToMarginConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int level)
            {
                // 每个层级缩进 20 像素
                return new Thickness(level * 20, 0, 0, 0);
            }
            return new Thickness(0);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
