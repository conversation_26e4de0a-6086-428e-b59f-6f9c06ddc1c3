// NumberBox C# 基础用法示例
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Windows.Media;

namespace WPFTest.ViewModels.InputControls
{
    public partial class NumberBoxBasicViewModel : ObservableObject
    {
        /// <summary>
        /// 基础数值
        /// </summary>
        [ObservableProperty]
        private double basicValue = 0;

        /// <summary>
        /// 最小值
        /// </summary>
        [ObservableProperty]
        private double minimumValue = 0;

        /// <summary>
        /// 最大值
        /// </summary>
        [ObservableProperty]
        private double maximumValue = 100;

        /// <summary>
        /// 步进值
        /// </summary>
        [ObservableProperty]
        private double stepValue = 1;



        /// <summary>
        /// 验证的数值
        /// </summary>
        [ObservableProperty]
        [Range(1, 999, ErrorMessage = "数值必须在1-999之间")]
        private double validatedValue = 50;

        /// <summary>
        /// 验证消息
        /// </summary>
        [ObservableProperty]
        private string validationMessage = string.Empty;

        /// <summary>
        /// 验证消息颜色
        /// </summary>
        [ObservableProperty]
        private Brush validationMessageColor = Brushes.Gray;

        /// <summary>
        /// 构造函数
        /// </summary>
        public NumberBoxBasicViewModel()
        {
            // 监听属性变化
            PropertyChanged += OnPropertyChanged;
        }

        /// <summary>
        /// 属性变化处理
        /// </summary>
        private void OnPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            switch (e.PropertyName)
            {
                case nameof(BasicValue):
                    ValidateBasicValue();
                    break;
                case nameof(ValidatedValue):
                    ValidateRangeValue();
                    break;
            }
        }

        /// <summary>
        /// 验证基础数值
        /// </summary>
        private void ValidateBasicValue()
        {
            if (BasicValue < MinimumValue || BasicValue > MaximumValue)
            {
                ValidationMessage = $"数值应在 {MinimumValue} - {MaximumValue} 范围内";
                ValidationMessageColor = new SolidColorBrush(Colors.Orange);
            }
            else
            {
                ValidationMessage = "✅ 数值有效";
                ValidationMessageColor = new SolidColorBrush(Colors.Green);
            }
        }

        /// <summary>
        /// 验证范围数值
        /// </summary>
        private void ValidateRangeValue()
        {
            if (ValidatedValue < 1 || ValidatedValue > 999)
            {
                ValidationMessage = "❌ 数值必须在1-999之间";
                ValidationMessageColor = new SolidColorBrush(Colors.Red);
            }
            else
            {
                ValidationMessage = "✅ 验证通过";
                ValidationMessageColor = new SolidColorBrush(Colors.Green);
            }
        }

        /// <summary>
        /// 重置数值命令
        /// </summary>
        [RelayCommand]
        private void ResetValues()
        {
            BasicValue = 0;
            ValidatedValue = 50;
            ValidationMessage = string.Empty;
        }

        /// <summary>
        /// 设置随机数值命令
        /// </summary>
        [RelayCommand]
        private void SetRandomValue()
        {
            var random = new Random();
            BasicValue = Math.Round(random.NextDouble() * (MaximumValue - MinimumValue) + MinimumValue, 2);
        }

        /// <summary>
        /// 增加数值命令
        /// </summary>
        [RelayCommand]
        private void IncrementValue()
        {
            if (BasicValue + StepValue <= MaximumValue)
            {
                BasicValue += StepValue;
            }
        }

        /// <summary>
        /// 减少数值命令
        /// </summary>
        [RelayCommand]
        private void DecrementValue()
        {
            if (BasicValue - StepValue >= MinimumValue)
            {
                BasicValue -= StepValue;
            }
        }

        /// <summary>
        /// 格式化数值为字符串
        /// </summary>
        /// <param name="value">数值</param>
        /// <param name="format">格式字符串</param>
        /// <returns>格式化后的字符串</returns>
        public string FormatNumber(double value, string format = "N2")
        {
            try
            {
                return value.ToString(format, CultureInfo.CurrentCulture);
            }
            catch
            {
                return value.ToString();
            }
        }

        /// <summary>
        /// 解析字符串为数值
        /// </summary>
        /// <param name="text">文本</param>
        /// <param name="result">解析结果</param>
        /// <returns>是否解析成功</returns>
        public bool TryParseNumber(string text, out double result)
        {
            return double.TryParse(text, NumberStyles.Number, CultureInfo.CurrentCulture, out result);
        }

        /// <summary>
        /// 验证数值是否在指定范围内
        /// </summary>
        /// <param name="value">数值</param>
        /// <param name="min">最小值</param>
        /// <param name="max">最大值</param>
        /// <returns>是否在范围内</returns>
        public bool IsValueInRange(double value, double min, double max)
        {
            return value >= min && value <= max;
        }

        /// <summary>
        /// 限制数值在指定范围内
        /// </summary>
        /// <param name="value">数值</param>
        /// <param name="min">最小值</param>
        /// <param name="max">最大值</param>
        /// <returns>限制后的数值</returns>
        public double ClampValue(double value, double min, double max)
        {
            return Math.Max(min, Math.Min(max, value));
        }
    }
}
