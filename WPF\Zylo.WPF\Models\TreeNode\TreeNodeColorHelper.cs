namespace Zylo.WPF.Models.TreeNode;

/// <summary>
/// 树节点颜色帮助类
/// </summary>
public static class TreeNodeColorHelper
{
    /// <summary>
    /// 根据节点类型获取颜色 - 扩展版本，支持更多类型
    /// </summary>
    public static string GetColorByType(string nodeType) => nodeType.ToLower() switch
    {
        // === 文件系统 ===
        "folder" => "#F7DC6F",
        "file" => "#98D8C8",
        "document" => "#AED6F1",
        "text" => "#D5DBDB",
        "image" => "#F8C471",
        "photo" => "#F8C471",
        "video" => "#BB8FCE",
        "audio" => "#85C1E9",
        "music" => "#85C1E9",
        "archive" => "#F1948A",
        "zip" => "#F1948A",
        "pdf" => "#E74C3C",
        "excel" => "#27AE60",
        "word" => "#3498DB",
        "powerpoint" => "#E67E22",
        "csv" => "#95A5A6",
        "json" => "#F39C12",
        "xml" => "#9B59B6",
        "config" => "#34495E",
        "settings" => "#34495E",
        "log" => "#7F8C8D",
        
        // === 编程语言 ===
        "code" => "#82E0AA",
        "javascript" => "#F7DF1E",
        "js" => "#F7DF1E",
        "typescript" => "#3178C6",
        "ts" => "#3178C6",
        "python" => "#3776AB",
        "py" => "#3776AB",
        "java" => "#ED8B00",
        "csharp" => "#239120",
        "cs" => "#239120",
        "cpp" => "#00599C",
        "c" => "#A8B9CC",
        "php" => "#777BB4",
        "ruby" => "#CC342D",
        "go" => "#00ADD8",
        "rust" => "#000000",
        "swift" => "#FA7343",
        "kotlin" => "#7F52FF",
        "dart" => "#0175C2",
        "scala" => "#DC322F",
        "r" => "#276DC3",
        "matlab" => "#E16737",
        "sql" => "#336791",
        
        // === Web开发 ===
        "html" => "#E34F26",
        "css" => "#1572B6",
        "sass" => "#CC6699",
        "scss" => "#CC6699",
        "less" => "#1D365D",
        "react" => "#61DAFB",
        "vue" => "#4FC08D",
        "angular" => "#DD0031",
        "nodejs" => "#339933",
        "npm" => "#CB3837",
        "webpack" => "#8DD6F9",
        "babel" => "#F9DC3E",
        
        // === 数据库 ===
        "database" => "#F9E79F",
        "mysql" => "#4479A1",
        "postgresql" => "#336791",
        "mongodb" => "#47A248",
        "redis" => "#DC382D",
        "sqlite" => "#003B57",
        "oracle" => "#F80000",
        "table" => "#85C1E9",
        "view" => "#AED6F1",
        "procedure" => "#F8C471",
        "function" => "#82E0AA",
        "trigger" => "#F1948A",
        "index" => "#D5DBDB",
        
        // === 系统和网络 ===
        "network" => "#85C1E9",
        "server" => "#34495E",
        "service" => "#2ECC71",
        "api" => "#E74C3C",
        "endpoint" => "#9B59B6",
        "url" => "#3498DB",
        "domain" => "#1ABC9C",
        "ip" => "#95A5A6",
        "port" => "#E67E22",
        "protocol" => "#F39C12",
        "ssh" => "#2C3E50",
        "ftp" => "#8E44AD",
        "http" => "#27AE60",
        "https" => "#16A085",
        
        // === 组织和人员 ===
        "user" => "#96CEB4",
        "admin" => "#E74C3C",
        "guest" => "#BDC3C7",
        "group" => "#45B7D1",
        "team" => "#3498DB",
        "department" => "#9B59B6",
        "organization" => "#FF6B35",
        "company" => "#E67E22",
        "branch" => "#27AE60",
        "office" => "#F39C12",
        "role" => "#8E44AD",
        "permission" => "#C0392B",
        
        // === 项目管理 ===
        "project" => "#FFEAA7",
        "task" => "#DDA0DD",
        "subtask" => "#D1C4E9",
        "milestone" => "#FFD54F",
        "sprint" => "#81C784",
        "epic" => "#FF8A65",
        "story" => "#90CAF9",
        "bug" => "#F44336",
        "feature" => "#4CAF50",
        "enhancement" => "#FF9800",
        "issue" => "#FFC107",
        "ticket" => "#9C27B0",
        "todo" => "#607D8B",
        "done" => "#4CAF50",
        "inprogress" => "#2196F3",
        "blocked" => "#F44336",
        "review" => "#FF9800",
        "testing" => "#9C27B0",
        
        // === 内容和媒体 ===
        "note" => "#D5DBDB",
        "memo" => "#F8D7DA",
        "comment" => "#D1ECF1",
        "message" => "#D4EDDA",
        "email" => "#CCE5FF",
        "notification" => "#FFF3CD",
        "alert" => "#F8D7DA",
        "warning" => "#FFF3CD",
        "error" => "#F8D7DA",
        "success" => "#D4EDDA",
        "info" => "#D1ECF1",
        "bookmark" => "#E2E3E5",
        "favorite" => "#FFE082",
        "star" => "#FFD54F",
        "like" => "#C8E6C9",
        "tag" => "#D1ECF1",
        "label" => "#E1F5FE",
        "category" => "#F3E5F5",
        
        // === 工具和应用 ===
        "tool" => "#CFD8DC",
        "plugin" => "#B39DDB",
        "extension" => "#CE93D8",
        "widget" => "#F8BBD9",
        "component" => "#C5CAE9",
        "module" => "#DCEDC8",
        "library" => "#F0F4C3",
        "framework" => "#FFECB3",
        "template" => "#FFE0B2",
        "theme" => "#FFCDD2",
        "style" => "#F8BBD9",
        "layout" => "#E1BEE7",
        "design" => "#D1C4E9",
        
        // === 安全和权限 ===
        "security" => "#FFCDD2",
        "key" => "#FFE0B2",
        "certificate" => "#F0F4C3",
        "token" => "#DCEDC8",
        "password" => "#C8E6C9",
        "encryption" => "#B2DFDB",
        "firewall" => "#B2EBF2",
        "shield" => "#B3E5FC",
        "lock" => "#BBDEFB",
        "unlock" => "#C5CAE9",
        
        // === 时间和日期 ===
        "calendar" => "#E8F5E8",
        "date" => "#E3F2FD",
        "time" => "#F3E5F5",
        "clock" => "#FCE4EC",
        "schedule" => "#FFF8E1",
        "event" => "#F1F8E9",
        "meeting" => "#E0F2F1",
        "appointment" => "#E8F5E8",
        
        // === 状态和指示器 ===
        "status" => "#E3F2FD",
        "health" => "#E8F5E8",
        "monitor" => "#F3E5F5",
        "dashboard" => "#FCE4EC",
        "chart" => "#FFF8E1",
        "graph" => "#F1F8E9",
        "report" => "#E0F2F1",
        "analytics" => "#E8F5E8",
        "metrics" => "#E3F2FD",
        "kpi" => "#F3E5F5",
        
        // === 默认 ===
        _ => "#BDC3C7"
    };
}
