<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:converters="clr-namespace:Zylo.WPF.Converters"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!--  🎨 Zylo.MVVM 通用资源字典  -->
    <!--  支持多种 UI 框架：WPF-UI, iNKORE.UI, 原生 WPF  -->

    <ResourceDictionary.MergedDictionaries>

        <!--  按钮样式  -->
        <ResourceDictionary Source="pack://application:,,,/Zylo.WPF;component/Resources/Buttons/ButtonStyles.xaml" />
        <ResourceDictionary Source="pack://application:,,,/Zylo.WPF;component/Resources/Buttons/RectangleButtonStyles.xaml" />

        <!--  DropDownButton 样式  -->
        <ResourceDictionary Source="pack://application:,,,/Zylo.WPF;component/Resources/DropDownButton/DropDownButtonStyles.xaml" />

        <!--  TextBox 样式  -->
        <ResourceDictionary Source="pack://application:,,,/Zylo.WPF;component/Resources/TextBox/TextBoxStyles.xaml" />

        <!--  PasswordBox 样式  -->
        <ResourceDictionary Source="pack://application:,,,/Zylo.WPF;component/Resources/PasswordBox/PasswordBoxStyles.xaml" />

        <!--  NumberBox 样式  -->
        <ResourceDictionary Source="pack://application:,,,/Zylo.WPF;component/Resources/NumberBox/NumberBoxStyles.xaml" />

        <!--  AutoSuggestBox 样式  -->
        <ResourceDictionary Source="pack://application:,,,/Zylo.WPF;component/Resources/AutoSuggestBox/AutoSuggestBoxStyles.xaml" />

        <!--  ListView 样式  -->
        <ResourceDictionary Source="pack://application:,,,/Zylo.WPF;component/Resources/ListView/ListViewStyles.xaml" />

        <!--  DataGrid 样式  -->
        <ResourceDictionary Source="pack://application:,,,/Zylo.WPF;component/Resources/DataGrid/DataGridStyles.xaml" />

        <!--  CalendarDatePicker 样式  -->
        <ResourceDictionary Source="pack://application:,,,/Zylo.WPF;component/Resources/CalendarDatePicker/CalendarDatePickerStyles.xaml" />

        <!--  TimePicker 样式  -->
        <ResourceDictionary Source="pack://application:,,,/Zylo.WPF;component/Resources/TimePicker/TimePickerStyles.xaml" />

        <!--  LabelTextBox 组合控件样式  -->
        <ResourceDictionary Source="pack://application:,,,/Zylo.WPF;component/Resources/Input/LabelTextBoxStyles.xaml" />

        <!--  Border 样式  -->
        <ResourceDictionary Source="pack://application:,,,/Zylo.WPF;component/Resources/Border/BorderStyles.xaml" />

        <!--  Expander 样式  -->
        <ResourceDictionary Source="pack://application:,,,/Zylo.WPF;component/Resources/Expander/ExpanderStyles.xaml" />

        <!--  GroupBox 样式  -->
        <ResourceDictionary Source="pack://application:,,,/Zylo.WPF;component/Resources/GroupBox/GroupBoxStyles.xaml" />

        <!--  ScrollViewer 样式  -->
        <ResourceDictionary Source="pack://application:,,,/Zylo.WPF;component/Resources/ScrollViewer/ScrollViewerStyles.xaml" />

        <!--  CardExpander 样式  -->
        <ResourceDictionary Source="pack://application:,,,/Zylo.WPF;component/Resources/CardExpander/CardExpanderStyles.xaml" />


        <!--  Prism 资源字典  -->
        <!--  注意：由于我们使用普通 Application，这些资源需要手动包含  -->









        <!--  🔧 可选：根据需要取消注释相应的 UI 框架  -->

        <!--  WPF-UI 支持 (推荐)  -->
        <!-- <ResourceDictionary Source="pack://application:,,,/Wpf.Ui;component/Resources/Theme/Dark.xaml" /> -->

        <!--  iNKORE.UI 支持  -->
        <!-- <ResourceDictionary Source="/iNKORE.UI.WPF.Modern;component/Themes/Generic.xaml" /> -->

        <!--  原生 WPF 支持  -->
        <!-- <ResourceDictionary Source="pack://application:,,,/PresentationFramework.Classic;component/themes/Classic.xaml" /> -->

        <!--  🎨 Zylo.WPF 控件资源  -->
        <!--  NavigationControl 样式和模板  -->
        <ResourceDictionary Source="/Zylo.WPF;component/Resources/Navigation/NavigationControlStyles.xaml" />
        <ResourceDictionary Source="/Zylo.WPF;component/Resources/Navigation/NavigationControlTemplates.xaml" />

        <!--  按钮样式资源  -->
        <ResourceDictionary Source="/Zylo.WPF;component/Resources/Buttons/ButtonStyles.xaml" />
        <ResourceDictionary Source="/Zylo.WPF;component/Resources/Buttons/RectangleButtonStyles.xaml" />

        <!--  ZyloIcon 图标控件样式  -->
        <ResourceDictionary Source="/Zylo.WPF;component/Themes/Controls/ZyloIcon.xaml" />

        <!--  ZyloSnackbar 通知控件样式  -->
        <ResourceDictionary Source="/Zylo.WPF;component/Themes/Controls/ZyloSnackbar.xaml" />

        <!--  🎬 Zylo.WPF 动画资源 - 按功能分类  -->
        <ResourceDictionary Source="/Zylo.WPF;component/Resources/Animations/TransitionAnimations.xaml" />
        <ResourceDictionary Source="/Zylo.WPF;component/Resources/Animations/ButtonAnimations.xaml" />
        <ResourceDictionary Source="/Zylo.WPF;component/Resources/Animations/CardAnimations.xaml" />
        <ResourceDictionary Source="/Zylo.WPF;component/Resources/Animations/PageAnimations.xaml" />
        <ResourceDictionary Source="/Zylo.WPF;component/Resources/Animations/LoadingAnimations.xaml" />
        <ResourceDictionary Source="/Zylo.WPF;component/Resources/Animations/NotificationAnimations.xaml" />
        <ResourceDictionary Source="/Zylo.WPF;component/Resources/Animations/NavigationAnimations.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <!--  🎯 Zylo.WPF 自定义样式和模板  -->

    <!--  🔄 转换器资源  -->
    <converters:EnumToVisibilityConverter x:Key="EnumToVisibilityConverter" xmlns:converters="clr-namespace:Zylo.WPF.Converters" />
    <converters:EnumToStringConverter x:Key="EnumToStringConverter" xmlns:converters="clr-namespace:Zylo.WPF.Converters" />
    <converters:SimpleIconConverter x:Key="SimpleIconConverter" xmlns:converters="clr-namespace:Zylo.WPF.Converters" />
    <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter" xmlns:converters="clr-namespace:Zylo.WPF.Converters" />
    <converters:WidthConverter x:Key="WidthConverter" xmlns:converters="clr-namespace:Zylo.WPF.Converters" />
    <converters:BooleanToStringConverter x:Key="BooleanToStringConverter" xmlns:converters="clr-namespace:Zylo.WPF.Converters" />
    <converters:InverseBooleanConverter x:Key="InverseBooleanConverter" xmlns:converters="clr-namespace:Zylo.WPF.Converters" />
    <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter" xmlns:converters="clr-namespace:Zylo.WPF.Converters" />

    <!--  字体资源  -->
    <FontFamily x:Key="ZyloIconFont">/Zylo.WPF;component/Assets/Font/#iconfont</FontFamily>

    <!--  🔍 验证加载的测试资源  -->
    <SolidColorBrush Color="#FF6B46C1" x:Key="ZyloTestBrush" />
    <sys:String x:Key="ZyloTestMessage" xmlns:sys="clr-namespace:System;assembly=mscorlib">✅ Zylo.WPF Generic.xaml 已成功加载！</sys:String>

    <!--  🎨 测试样式 - 如果加载成功，按钮会有紫色边框  -->
    <Style TargetType="Button" x:Key="ZyloTestButtonStyle">
        <Setter Property="BorderBrush" Value="{StaticResource ZyloTestBrush}" />
        <Setter Property="BorderThickness" Value="2" />
        <Setter Property="Padding" Value="10,5" />
        <Setter Property="Margin" Value="5" />
    </Style>

    <!--  🔄 ListView 专用转换器  -->
    <converters:BoolToSelectionModeConverter x:Key="BoolToSelectionModeConverter" />
    <converters:BoolToSelectionModeTextConverter x:Key="BoolToSelectionModeTextConverter" />
    <converters:BoolToBorderBrushConverter x:Key="BoolToBorderBrushConverter" />
    <converters:BoolToEnabledTextConverter x:Key="BoolToEnabledTextConverter" />
    <converters:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter" />
    <!--  CountToVisibilityConverter 已在 StringToVisibilityConverter.cs 中定义  -->
    <converters:CategoryToColorConverter x:Key="CategoryToColorConverter" />
    <converters:StringToThemeColorConverter x:Key="StringToThemeColorConverter" />
    <converters:SelectionStatusMultiConverter x:Key="SelectionStatusMultiConverter" />

    <!--  🔧 逻辑转换器  -->
    <converters:LogicalAndConverter x:Key="LogicalAndConverter" />

    <!--  自定义样式可以在这里添加  -->

</ResourceDictionary>