<!-- TimePicker 基础用法示例 -->
<StackPanel Margin="20" Spacing="15">

    <!-- 标准 TimePicker -->
    <GroupBox Header="标准 TimePicker" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <ui:TimePicker Style="{StaticResource TimePickerStyle}"
                          SelectedTime="{Binding SelectedTime, Mode=TwoWay}"
                          Margin="4"/>
            
            <ui:TimePicker Style="{StaticResource TimePickerStyle}"
                          SelectedTime="{Binding WorkTime, Mode=TwoWay}"
                          Margin="4"/>
            
            <ui:TimePicker Style="{StaticResource TimePickerStyle}"
                          SelectedTime="{Binding ReminderTime, Mode=TwoWay}"
                          Margin="4"/>
        </WrapPanel>
    </GroupBox>

    <!-- 不同尺寸 -->
    <GroupBox Header="不同尺寸" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <ui:TimePicker Style="{StaticResource CompactTimePickerStyle}"
                          SelectedTime="{Binding SelectedTime, Mode=TwoWay}"
                          Margin="4"/>
            <ui:TimePicker Style="{StaticResource SmallTimePickerStyle}"
                          SelectedTime="{Binding SelectedTime, Mode=TwoWay}"
                          Margin="4"/>
            <ui:TimePicker Style="{StaticResource TimePickerStyle}"
                          SelectedTime="{Binding SelectedTime, Mode=TwoWay}"
                          Margin="4"/>
            <ui:TimePicker Style="{StaticResource LargeTimePickerStyle}"
                          SelectedTime="{Binding SelectedTime, Mode=TwoWay}"
                          Margin="4"/>
        </WrapPanel>
    </GroupBox>

    <!-- 基础时间绑定 -->
    <GroupBox Header="时间绑定示例" Padding="15">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Row="0" Grid.Column="0" 
                       Text="当前时间：" 
                       VerticalAlignment="Center" 
                       Margin="0,0,12,8"/>
            <ui:TimePicker Grid.Row="0" Grid.Column="1"
                          SelectedTime="{Binding SelectedTime, Mode=TwoWay}"
                          Style="{StaticResource TimePickerStyle}"
                          Margin="0,0,0,8"/>

            <TextBlock Grid.Row="1" Grid.Column="0" 
                       Text="工作时间：" 
                       VerticalAlignment="Center" 
                       Margin="0,0,12,8"/>
            <ui:TimePicker Grid.Row="1" Grid.Column="1"
                          SelectedTime="{Binding WorkTime, Mode=TwoWay}"
                          Style="{StaticResource WorkTimePickerStyle}"
                          Margin="0,0,0,8"/>

            <TextBlock Grid.Row="2" Grid.Column="0" 
                       Text="提醒时间：" 
                       VerticalAlignment="Center" 
                       Margin="0,0,12,0"/>
            <ui:TimePicker Grid.Row="2" Grid.Column="1"
                          SelectedTime="{Binding ReminderTime, Mode=TwoWay}"
                          Style="{StaticResource ReminderTimePickerStyle}"/>
        </Grid>
    </GroupBox>

</StackPanel>
