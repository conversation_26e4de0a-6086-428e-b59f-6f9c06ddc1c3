// WPF-UI Card C# 基础示例
// Card 是 WPF-UI 库中的现代化卡片容器控件

using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using Wpf.Ui.Controls;

namespace WPFTest.Examples.LayoutControls
{
    public class CardBasicExample
    {
        /// <summary>
        /// 创建基础 Card 控件
        /// </summary>
        public static Card CreateBasicCard()
        {
            var card = new Card
            {
                Padding = new Thickness(16),
                Margin = new Thickness(8)
            };

            var content = new StackPanel();

            content.Children.Add(new TextBlock
            {
                Text = "这是一个简单的卡片",
                FontWeight = FontWeights.Medium,
                Margin = new Thickness(0, 0, 0, 8)
            });

            content.Children.Add(new TextBlock
            {
                Text = "卡片提供了现代化的容器样式，具有阴影效果和圆角边框。",
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(0, 0, 0, 12)
            });

            content.Children.Add(new Button
            {
                Content = "卡片按钮",
                HorizontalAlignment = HorizontalAlignment.Left
            });

            card.Content = content;
            return card;
        }

        /// <summary>
        /// 创建带图标的 Card
        /// </summary>
        public static Card CreateCardWithIcon()
        {
            var card = new Card
            {
                Padding = new Thickness(16),
                Margin = new Thickness(8)
            };

            var content = new StackPanel();

            // 创建标题区域（图标 + 文字）
            var headerPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 0, 0, 12)
            };

            headerPanel.Children.Add(new SymbolIcon
            {
                Symbol = SymbolRegular.Star24,
                FontSize = 24,
                Margin = new Thickness(0, 0, 12, 0)
            });

            headerPanel.Children.Add(new TextBlock
            {
                Text = "特色功能",
                FontWeight = FontWeights.Medium,
                FontSize = 16,
                VerticalAlignment = VerticalAlignment.Center
            });

            content.Children.Add(headerPanel);

            content.Children.Add(new TextBlock
            {
                Text = "这个卡片包含了图标和文字的组合，提供更丰富的视觉效果。",
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(0, 0, 0, 12)
            });

            // 创建按钮区域
            var buttonPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal
            };

            buttonPanel.Children.Add(new Button
            {
                Content = "主要操作",
                Margin = new Thickness(0, 0, 8, 0)
            });

            buttonPanel.Children.Add(new Button
            {
                Content = "次要操作"
            });

            content.Children.Add(buttonPanel);
            card.Content = content;
            return card;
        }

        /// <summary>
        /// 创建信息提示 Card
        /// </summary>
        public static Card CreateInfoCard()
        {
            var card = new Card
            {
                Padding = new Thickness(16),
                Margin = new Thickness(8)
            };

            var grid = new Grid();
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            // 图标
            var icon = new SymbolIcon
            {
                Symbol = SymbolRegular.Info24,
                FontSize = 32,
                Margin = new Thickness(0, 0, 16, 0),
                VerticalAlignment = VerticalAlignment.Top
            };
            Grid.SetColumn(icon, 0);
            grid.Children.Add(icon);

            // 内容区域
            var contentPanel = new StackPanel();

            contentPanel.Children.Add(new TextBlock
            {
                Text = "重要提示",
                FontWeight = FontWeights.Bold,
                FontSize = 16,
                Margin = new Thickness(0, 0, 0, 8)
            });

            contentPanel.Children.Add(new TextBlock
            {
                Text = "这是一个信息提示卡片，用于显示重要的系统消息或用户通知。",
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(0, 0, 0, 8)
            });

            contentPanel.Children.Add(new TextBlock
            {
                Text = "• 支持多行文本显示",
                Margin = new Thickness(0, 0, 0, 2)
            });

            contentPanel.Children.Add(new TextBlock
            {
                Text = "• 可以包含图标和按钮",
                Margin = new Thickness(0, 0, 0, 2)
            });

            contentPanel.Children.Add(new TextBlock
            {
                Text = "• 响应式布局设计",
                Margin = new Thickness(0, 0, 0, 8)
            });

            contentPanel.Children.Add(new Button
            {
                Content = "了解更多",
                HorizontalAlignment = HorizontalAlignment.Left
            });

            Grid.SetColumn(contentPanel, 1);
            grid.Children.Add(contentPanel);

            card.Content = grid;
            return card;
        }

        /// <summary>
        /// 创建统计数据 Card
        /// </summary>
        public static Card CreateStatsCard()
        {
            var card = new Card
            {
                Padding = new Thickness(20),
                Margin = new Thickness(8)
            };

            var content = new StackPanel();

            content.Children.Add(new TextBlock
            {
                Text = "数据统计",
                FontWeight = FontWeights.Bold,
                FontSize = 18,
                Margin = new Thickness(0, 0, 0, 16)
            });

            // 创建统计数据网格
            var statsGrid = new Grid();
            statsGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            statsGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            for (int i = 0; i < 3; i++)
            {
                statsGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            }

            // 添加统计项
            AddStatsItem(statsGrid, "总用户:", "1,234", 0);
            AddStatsItem(statsGrid, "活跃用户:", "987", 1);
            AddStatsItem(statsGrid, "新增用户:", "45", 2);

            content.Children.Add(statsGrid);

            // 添加进度条
            var progressBar = new ProgressBar
            {
                Value = 75,
                Height = 8,
                Margin = new Thickness(0, 16, 0, 8)
            };
            content.Children.Add(progressBar);

            content.Children.Add(new TextBlock
            {
                Text = "完成度: 75%",
                FontSize = 12,
                HorizontalAlignment = HorizontalAlignment.Center
            });

            card.Content = content;
            return card;
        }

        /// <summary>
        /// 创建操作按钮 Card
        /// </summary>
        public static Card CreateActionCard()
        {
            var card = new Card
            {
                Padding = new Thickness(16),
                Margin = new Thickness(8)
            };

            var content = new StackPanel();

            content.Children.Add(new TextBlock
            {
                Text = "快速操作",
                FontWeight = FontWeights.Medium,
                FontSize = 16,
                Margin = new Thickness(0, 0, 0, 12)
            });

            // 创建按钮网格
            var buttonGrid = new Grid();
            buttonGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            buttonGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            buttonGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            buttonGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            // 添加按钮
            var buttons = new[]
            {
                ("新建", 0, 0),
                ("编辑", 0, 1),
                ("删除", 1, 0),
                ("设置", 1, 1)
            };

            foreach (var (text, row, col) in buttons)
            {
                var button = new Button
                {
                    Content = text,
                    Margin = new Thickness(
                        col == 0 ? 0 : 4,
                        row == 0 ? 0 : 4,
                        col == 1 ? 0 : 4,
                        row == 1 ? 0 : 4)
                };

                Grid.SetRow(button, row);
                Grid.SetColumn(button, col);
                buttonGrid.Children.Add(button);
            }

            content.Children.Add(buttonGrid);
            card.Content = content;
            return card;
        }

        /// <summary>
        /// 创建媒体 Card
        /// </summary>
        public static Card CreateMediaCard()
        {
            var card = new Card
            {
                Padding = new Thickness(0),
                Margin = new Thickness(8)
            };

            var content = new StackPanel();

            // 创建模拟图片区域
            var imageArea = new Border
            {
                Background = new SolidColorBrush(Colors.DodgerBlue),
                Height = 120,
                CornerRadius = new CornerRadius(8, 8, 0, 0)
            };

            var imageContent = new StackPanel
            {
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };

            imageContent.Children.Add(new SymbolIcon
            {
                Symbol = SymbolRegular.Image24,
                FontSize = 32,
                Foreground = Brushes.White
            });

            imageContent.Children.Add(new TextBlock
            {
                Text = "图片区域",
                Foreground = Brushes.White,
                FontSize = 12,
                Margin = new Thickness(0, 4, 0, 0)
            });

            imageArea.Child = imageContent;
            content.Children.Add(imageArea);

            // 创建文字内容区域
            var textContent = new StackPanel
            {
                Margin = new Thickness(16)
            };

            textContent.Children.Add(new TextBlock
            {
                Text = "媒体标题",
                FontWeight = FontWeights.Bold,
                FontSize = 16,
                Margin = new Thickness(0, 0, 0, 8)
            });

            textContent.Children.Add(new TextBlock
            {
                Text = "这是一个媒体卡片的描述文本，展示了如何在卡片中组合图片和文字内容。",
                TextWrapping = TextWrapping.Wrap,
                FontSize = 14,
                Margin = new Thickness(0, 0, 0, 12)
            });

            textContent.Children.Add(new Button
            {
                Content = "查看详情",
                HorizontalAlignment = HorizontalAlignment.Left
            });

            content.Children.Add(textContent);
            card.Content = content;
            return card;
        }

        /// <summary>
        /// 辅助方法：添加统计项
        /// </summary>
        private static void AddStatsItem(Grid grid, string label, string value, int row)
        {
            var labelText = new TextBlock
            {
                Text = label,
                FontWeight = FontWeights.Medium
            };
            Grid.SetRow(labelText, row);
            Grid.SetColumn(labelText, 0);
            grid.Children.Add(labelText);

            var valueText = new TextBlock
            {
                Text = value,
                FontWeight = FontWeights.Bold,
                HorizontalAlignment = HorizontalAlignment.Right
            };
            Grid.SetRow(valueText, row);
            Grid.SetColumn(valueText, 1);
            grid.Children.Add(valueText);
        }

        /// <summary>
        /// 创建卡片列表
        /// </summary>
        public static StackPanel CreateCardList()
        {
            var container = new StackPanel();

            var items = new[]
            {
                (SymbolRegular.Document24, "文档 1", "最后修改: 2024-01-15", "打开"),
                (SymbolRegular.Folder24, "项目文件夹", "包含 25 个文件", "浏览"),
                (SymbolRegular.Image24, "图片集合", "共 156 张图片", "查看")
            };

            foreach (var (icon, title, subtitle, action) in items)
            {
                var card = new Card
                {
                    Padding = new Thickness(12),
                    Margin = new Thickness(0, 0, 0, 8)
                };

                var grid = new Grid();
                grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
                grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
                grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

                // 图标
                var symbolIcon = new SymbolIcon
                {
                    Symbol = icon,
                    FontSize = 20,
                    Margin = new Thickness(0, 0, 12, 0)
                };
                Grid.SetColumn(symbolIcon, 0);
                grid.Children.Add(symbolIcon);

                // 文字内容
                var textPanel = new StackPanel();
                textPanel.Children.Add(new TextBlock
                {
                    Text = title,
                    FontWeight = FontWeights.Medium
                });
                textPanel.Children.Add(new TextBlock
                {
                    Text = subtitle,
                    FontSize = 12
                });

                Grid.SetColumn(textPanel, 1);
                grid.Children.Add(textPanel);

                // 操作按钮
                var actionButton = new Button
                {
                    Content = action,
                    HorizontalAlignment = HorizontalAlignment.Right
                };
                Grid.SetColumn(actionButton, 2);
                grid.Children.Add(actionButton);

                card.Content = grid;
                container.Children.Add(card);
            }

            return container;
        }
    }
}
