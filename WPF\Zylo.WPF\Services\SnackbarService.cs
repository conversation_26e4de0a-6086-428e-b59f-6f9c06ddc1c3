using Wpf.Ui.Controls;
using Zylo.WPF.Extensions;

namespace Zylo.WPF.Services;

/// <summary>
/// Snackbar 服务实现
/// </summary>
public class SnackbarService : ISnackbarService
{
    private Controls.ZyloSnackbar? _snackbar;

    /// <summary>
    /// 设置 Snackbar 控件实例
    /// </summary>
    public void SetSnackbar(Controls.ZyloSnackbar snackbar)
    {
        _snackbar = snackbar;

        // 预热：确保控件已经完全初始化
        if (_snackbar != null)
        {
            // 强制加载模板
            _snackbar.ApplyTemplate();

            // 预设置一些属性，避免第一次显示时的延迟
            _snackbar.Measure(new System.Windows.Size(double.PositiveInfinity, double.PositiveInfinity));
        }
    }

    /// <summary>
    /// 显示成功消息
    /// </summary>
    public void ShowSuccess(string message, string title = "操作成功", int timeout = 3000)
    {
        _snackbar?.ShowSuccess(message, title, timeout);
    }

    /// <summary>
    /// 显示错误消息
    /// </summary>
    public void ShowError(string message, string title = "操作失败", int timeout = 5000)
    {
        _snackbar?.ShowError(message, title, timeout);
    }

    /// <summary>
    /// 显示警告消息
    /// </summary>
    public void ShowWarning(string message, string title = "警告", int timeout = 4000)
    {
        _snackbar?.ShowWarning(message, title, timeout);
    }

    /// <summary>
    /// 显示信息消息
    /// </summary>
    public void ShowInfo(string message, string title = "提示", int timeout = 3000)
    {
        _snackbar?.ShowInfo(message, title, timeout);
    }

    /// <summary>
    /// 显示主要消息
    /// </summary>
    public void ShowPrimary(string message, string title = "消息", int timeout = 3000)
    {
        _snackbar?.ShowPrimary(message, title, timeout);
    }

    /// <summary>
    /// 显示自定义消息
    /// </summary>
    public void Show(string title, string message, SymbolRegular icon = SymbolRegular.Info24, 
                     ControlAppearance appearance = ControlAppearance.Primary, int timeout = 3000)
    {
        _snackbar?.Show(title, message, icon, appearance, timeout);
    }

    /// <summary>
    /// 隐藏当前显示的 Snackbar
    /// </summary>
    public void Hide()
    {
        _snackbar?.Hide();
    }
}
