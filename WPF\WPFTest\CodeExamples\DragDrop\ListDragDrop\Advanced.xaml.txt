<!-- gong-wpf-dragdrop 列表拖拽高级示例 -->
<Grid>
    <Grid.ColumnDefinitions>
        <ColumnDefinition Width="*"/>
        <ColumnDefinition Width="*"/>
        <ColumnDefinition Width="*"/>
    </Grid.ColumnDefinitions>
    
    <!-- 源列表 -->
    <StackPanel Grid.Column="0">
        <TextBlock Text="源列表 (可拖拽)" FontWeight="Bold" Margin="0,0,0,8"/>
        <ListView ItemsSource="{Binding SourceItems}"
                  dd:DragDrop.IsDragSource="True"
                  dd:DragDrop.IsDropTarget="True"
                  dd:DragDrop.DragHandler="{Binding}"
                  dd:DragDrop.DropHandler="{Binding}"
                  dd:DragDrop.UseDefaultDragAdorner="True"
                  dd:DragDrop.DefaultDragAdornerOpacity="0.8">
            <ListView.ItemTemplate>
                <DataTemplate>
                    <Border Background="LightBlue" 
                            Padding="8" 
                            Margin="2"
                            CornerRadius="4"
                            Cursor="Hand">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <ui:SymbolIcon Grid.Column="0"
                                           Symbol="ArrowMove24" 
                                           FontSize="16"
                                           Margin="0,0,8,0"/>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="{Binding Name}" FontWeight="Medium"/>
                                <TextBlock Text="{Binding Category}" FontSize="11"/>
                            </StackPanel>
                            
                            <Border Grid.Column="2"
                                    Background="DarkBlue"
                                    CornerRadius="8"
                                    Padding="4,2">
                                <TextBlock Text="{Binding Priority}" 
                                           FontSize="10"
                                           Foreground="White"
                                           HorizontalAlignment="Center"/>
                            </Border>
                        </Grid>
                    </Border>
                </DataTemplate>
            </ListView.ItemTemplate>
        </ListView>
    </StackPanel>
    
    <!-- 目标列表 -->
    <StackPanel Grid.Column="1">
        <TextBlock Text="目标列表 (可放置)" FontWeight="Bold" Margin="0,0,0,8"/>
        <ListView ItemsSource="{Binding TargetItems}"
                  dd:DragDrop.IsDragSource="True"
                  dd:DragDrop.IsDropTarget="True"
                  dd:DragDrop.DragHandler="{Binding}"
                  dd:DragDrop.DropHandler="{Binding}"
                  Background="LightGray">
            <ListView.ItemTemplate>
                <DataTemplate>
                    <Border Background="LightGreen" 
                            Padding="8" 
                            Margin="2"
                            CornerRadius="4"
                            Cursor="Hand">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <ui:SymbolIcon Grid.Column="0"
                                           Symbol="Checkmark24" 
                                           FontSize="16"
                                           Foreground="DarkGreen"
                                           Margin="0,0,8,0"/>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="{Binding Name}" FontWeight="Medium"/>
                                <TextBlock Text="{Binding Category}" FontSize="11"/>
                            </StackPanel>
                            
                            <Border Grid.Column="2"
                                    Background="DarkGreen"
                                    CornerRadius="8"
                                    Padding="4,2">
                                <TextBlock Text="{Binding Priority}" 
                                           FontSize="10"
                                           Foreground="White"
                                           HorizontalAlignment="Center"/>
                            </Border>
                        </Grid>
                    </Border>
                </DataTemplate>
            </ListView.ItemTemplate>
        </ListView>
    </StackPanel>
    
    <!-- 可排序列表 -->
    <StackPanel Grid.Column="2">
        <TextBlock Text="排序列表 (可排序)" FontWeight="Bold" Margin="0,0,0,8"/>
        <ListView ItemsSource="{Binding SortableItems}"
                  dd:DragDrop.IsDragSource="True"
                  dd:DragDrop.IsDropTarget="True"
                  dd:DragDrop.DragHandler="{Binding}"
                  dd:DragDrop.DropHandler="{Binding}"
                  dd:DragDrop.UseDefaultDragAdorner="True">
            <ListView.ItemTemplate>
                <DataTemplate>
                    <Border Background="Orange" 
                            Padding="8" 
                            Margin="2"
                            CornerRadius="4"
                            Cursor="Hand">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <ui:SymbolIcon Grid.Column="0"
                                           Symbol="ArrowSort24" 
                                           FontSize="16"
                                           Foreground="DarkOrange"
                                           Margin="0,0,8,0"/>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="{Binding Name}" FontWeight="Medium"/>
                                <TextBlock Text="{Binding Category}" FontSize="11"/>
                            </StackPanel>
                            
                            <Border Grid.Column="2"
                                    Background="DarkOrange"
                                    CornerRadius="8"
                                    Padding="4,2">
                                <TextBlock Text="{Binding Priority}" 
                                           FontSize="10"
                                           Foreground="White"
                                           HorizontalAlignment="Center"/>
                            </Border>
                        </Grid>
                    </Border>
                </DataTemplate>
            </ListView.ItemTemplate>
        </ListView>
    </StackPanel>
</Grid>

<!-- 命名空间声明 -->
xmlns:dd="urn:gong-wpf-dragdrop"
xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"

<!-- 关键属性说明 -->
<!-- 
dd:DragDrop.IsDragSource="True"     - 启用拖拽源
dd:DragDrop.IsDropTarget="True"     - 启用放置目标
dd:DragDrop.DragHandler="{Binding}" - 绑定拖拽处理器
dd:DragDrop.DropHandler="{Binding}" - 绑定放置处理器
dd:DragDrop.UseDefaultDragAdorner="True" - 使用默认拖拽装饰器
-->
