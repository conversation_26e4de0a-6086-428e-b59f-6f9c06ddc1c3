<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
                    xmlns:converters="clr-namespace:Zylo.WPF.Converters"
                    xmlns:converters1="clr-namespace:Zylo.WPF.Converters">

    <!-- 🎨 NavigationControl 样式资源字典 -->
    <!-- ================================================================ -->
    <!-- 📋 功能说明：                                                      -->
    <!-- • 分类组织所有导航控件相关的样式、模板和资源                          -->
    <!-- • 符合用户要求：无圆角、高对比度悬停、WPF-UI 官方颜色               -->
    <!-- • 支持主题自动适配，完美融入 WPF-UI 设计系统                        -->
    <!-- ================================================================ -->

    <!-- ================================================================ -->
    <!-- 🔧 转换器资源 - 核心功能转换器                                        -->
    <!-- ================================================================ -->

    <!-- 图标转换器：支持 SymbolRegular、ZyloSymbol、Emoji 多种图标类型 -->
    <converters1:SimpleIconConverter x:Key="SimpleIconConverter" />

    <!-- ================================================================ -->
    <!-- 📐 尺寸和间距常量 - 统一的设计规范                                    -->
    <!-- ================================================================ -->
    <!--
    设计原则：
    • ListView 项目：40x35px（适合图标显示）
    • 字体大小：10px（紧凑显示）
    • 图标尺寸：20px（清晰可见）
    • 无圆角：CornerRadius="0"（符合用户要求）
    • 无内边距：简洁设计
    -->
    
    <!-- ListView 项目尺寸 -->
    <sys:Double x:Key="NavigationListItemWidth" xmlns:sys="clr-namespace:System;assembly=mscorlib">40</sys:Double>
    <sys:Double x:Key="NavigationListItemHeight" xmlns:sys="clr-namespace:System;assembly=mscorlib">35</sys:Double>

    <!-- 字体大小 -->
    <sys:Double x:Key="NavigationListItemFontSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">10</sys:Double>
    
    <!-- 图标尺寸 -->
    <sys:Double x:Key="NavigationListIconSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">20</sys:Double>
    <sys:Double x:Key="NavigationTreeIconSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">20</sys:Double>
    
    <!-- 间距 -->
    <Thickness x:Key="NavigationItemMargin">0,0</Thickness>
    <Thickness x:Key="NavigationItemPadding">0,0</Thickness>
    <Thickness x:Key="NavigationTreeItemPadding">0,0</Thickness>

    <!-- ================================================================ -->
    <!-- 🎨 按钮样式 - 导航按钮的统一外观                                      -->
    <!-- ================================================================ -->
    <!--
    设计特点：
    • 透明背景：与容器融为一体
    • 无边框：简洁设计
    • 高对比度悬停：符合用户要求
    • WPF-UI 官方颜色：完美主题适配
    • 无圆角：CornerRadius="0"
    -->

    <!-- 导航按钮统一样式 -->
    <Style x:Key="NavigationButtonStyle" TargetType="Button">
        <Setter Property="Width" Value="{StaticResource NavigationListItemWidth}" />
        <Setter Property="Height" Value="{StaticResource NavigationListItemHeight}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Padding" Value="{StaticResource NavigationItemPadding}" />
        <Setter Property="Margin" Value="{StaticResource NavigationItemMargin}" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="ButtonBorder"
                            Background="{TemplateBinding Background}"
                            BorderBrush="Transparent"
                            BorderThickness="0"
                            CornerRadius="0"
                            Padding="0"
                            Margin="0"
                            Width="{TemplateBinding Width}"
                            Height="{TemplateBinding Height}">
                        <ContentPresenter Content="{TemplateBinding Content}"
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}" />
                    </Border>

                    <ControlTemplate.Triggers>
                        <!-- 悬停效果 - 使用高对比度效果 -->
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="ButtonBorder" Property="Background" 
                                    Value="{DynamicResource ControlFillColorSecondaryBrush}" />
                            <Setter TargetName="ButtonBorder" Property="BorderBrush" 
                                    Value="{DynamicResource ControlStrokeColorDefaultBrush}" />
                            <Setter TargetName="ButtonBorder" Property="BorderThickness" Value="1" />
                        </Trigger>

                        <!-- 按下效果 - 使用更深的背景色 -->
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="ButtonBorder" Property="Background" 
                                    Value="{DynamicResource ControlStrongFillColorDefaultBrush}" />
                            <Setter TargetName="ButtonBorder" Property="BorderBrush" 
                                    Value="{DynamicResource ControlStrokeColorDefaultBrush}" />
                            <Setter TargetName="ButtonBorder" Property="BorderThickness" Value="1" />
                        </Trigger>

                        <!-- 禁用状态 -->
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="ButtonBorder" Property="Opacity" Value="0.5" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- ================================ -->
    <!-- 📋 ListView 样式 -->
    <!-- ================================ -->
    
    <!-- ListView 项目样式 -->
    <Style x:Key="NavigationListViewItemStyle" TargetType="ListViewItem">
        <Setter Property="Padding" Value="{StaticResource NavigationItemPadding}" />
        <Setter Property="Margin" Value="{StaticResource NavigationItemMargin}" />
        <Setter Property="FontSize" Value="{StaticResource NavigationListItemFontSize}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Width" Value="{StaticResource NavigationListItemWidth}" />
        <Setter Property="Height" Value="{StaticResource NavigationListItemHeight}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ListViewItem">
                    <Grid Width="{TemplateBinding Width}" Height="{TemplateBinding Height}">
                        <!-- 主要内容区域 -->
                        <Border x:Name="ItemBorder"
                                Background="{TemplateBinding Background}"
                                BorderBrush="Transparent"
                                BorderThickness="0"
                                CornerRadius="0"
                                Padding="0"
                                Margin="0">
                            <ContentPresenter Content="{TemplateBinding Content}"
                                              ContentTemplate="{TemplateBinding ContentTemplate}"
                                              HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              VerticalAlignment="{TemplateBinding VerticalContentAlignment}" />
                        </Border>

                        <!-- 左侧选中指示线 -->
                        <Rectangle x:Name="SelectionIndicator"
                                   Width="3"
                                   HorizontalAlignment="Left"
                                   VerticalAlignment="Stretch"
                                   Fill="{DynamicResource SystemAccentColorPrimaryBrush}"
                                   Visibility="Collapsed" />
                    </Grid>

                    <ControlTemplate.Triggers>
                        <!-- 悬停效果 - 轻微背景色变化 -->
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="ItemBorder" Property="Background"
                                    Value="{DynamicResource ControlFillColorSecondaryBrush}" />
                        </Trigger>

                        <!-- 选中效果 - 显示左侧指示线 -->
                        <Trigger Property="IsSelected" Value="True">
                            <Setter TargetName="SelectionIndicator" Property="Visibility" Value="Visible" />
                            <Setter TargetName="ItemBorder" Property="Background"
                                    Value="{DynamicResource ControlFillColorSecondaryBrush}" />
                        </Trigger>

                        <!-- 选中且悬停状态 - 保持指示线，稍微加深背景 -->
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="True" />
                                <Condition Property="IsMouseOver" Value="True" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="SelectionIndicator" Property="Visibility" Value="Visible" />
                            <Setter TargetName="ItemBorder" Property="Background"
                                    Value="{DynamicResource ControlFillColorTertiaryBrush}" />
                        </MultiTrigger>

                        <!-- 禁用状态 -->
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="ItemBorder" Property="Opacity" Value="0.5" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- ================================ -->
    <!-- 🌳 TreeView 样式 -->
    <!-- ================================ -->
    
    <!-- TreeView 项目样式 -->
    <Style x:Key="NavigationTreeViewItemStyle" TargetType="TreeViewItem">
        <Setter Property="IsExpanded" Value="{Binding IsExpanded, Mode=TwoWay}" />
        <Setter Property="IsSelected" Value="{Binding IsSelected, Mode=TwoWay}" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="Margin" Value="{StaticResource NavigationItemMargin}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TreeViewItem">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>

                        <!-- 主要内容区域 -->
                        <Grid Grid.Row="0">
                            <!-- 左侧选中指示线 -->
                            <Rectangle x:Name="SelectionIndicator"
                                       Width="3"
                                       HorizontalAlignment="Left"
                                       VerticalAlignment="Stretch"
                                       Fill="{DynamicResource SystemAccentColorPrimaryBrush}"
                                       Visibility="Collapsed" />

                            <!-- 内容边框 - 添加左边距为指示线留出空间 -->
                            <Border x:Name="ItemBorder"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    Padding="2,6"
                                    CornerRadius="0"
                                    Margin="8,1,1,1">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>

                                    <!-- 内容区域 -->
                                    <ContentPresenter x:Name="PART_Header"
                                                      Grid.Column="0"
                                                      ContentSource="Header"
                                                      HorizontalAlignment="Left"
                                                      VerticalAlignment="Center" />

                                    <!-- 展开/折叠按钮 - 放在右边 -->
                                    <ToggleButton x:Name="Expander"
                                                  Grid.Column="1"
                                                  ClickMode="Press"
                                                  IsChecked="{Binding IsExpanded, RelativeSource={RelativeSource TemplatedParent}}"
                                                  Width="16"
                                                  Height="16"
                                                  Margin="4,0,0,0"
                                                  Background="Transparent"
                                                  BorderThickness="0">
                                        <ToggleButton.Template>
                                            <ControlTemplate TargetType="ToggleButton">
                                                <Border Background="{TemplateBinding Background}"
                                                        BorderBrush="{TemplateBinding BorderBrush}"
                                                        BorderThickness="{TemplateBinding BorderThickness}">
                                                    <ui:SymbolIcon x:Name="ExpanderIcon"
                                                                   Symbol="ChevronRight16"
                                                                   FontSize="12"
                                                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                                   HorizontalAlignment="Center"
                                                                   VerticalAlignment="Center" />
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsChecked" Value="True">
                                                        <Setter TargetName="ExpanderIcon" Property="Symbol" Value="ChevronDown16" />
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </ToggleButton.Template>
                                    </ToggleButton>
                                </Grid>
                            </Border>
                        </Grid>

                        <!-- 子项容器 -->
                        <ItemsPresenter x:Name="ItemsHost"
                                        Grid.Row="1"
                                        Margin="16,0,0,0" />
                    </Grid>

                    <ControlTemplate.Triggers>
                        <!-- 隐藏没有子项的展开按钮 -->
                        <Trigger Property="HasItems" Value="False">
                            <Setter TargetName="Expander" Property="Visibility" Value="Hidden" />
                        </Trigger>

                        <!-- 当不允许折叠时，隐藏展开按钮 -->
                        <DataTrigger Binding="{Binding AllowCollapse, RelativeSource={RelativeSource AncestorType={x:Type UserControl}}}" Value="False">
                            <Setter TargetName="Expander" Property="Visibility" Value="Collapsed" />
                        </DataTrigger>

                        <!-- 折叠状态隐藏子项 -->
                        <Trigger Property="IsExpanded" Value="False">
                            <Setter TargetName="ItemsHost" Property="Visibility" Value="Collapsed" />
                        </Trigger>

                        <!-- 悬停效果 -->
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="ItemBorder" Property="Background"
                                    Value="{DynamicResource ControlFillColorSecondaryBrush}" />
                        </Trigger>

                        <!-- 选中效果 - 显示左侧指示线 -->
                        <Trigger Property="IsSelected" Value="True">
                            <Setter TargetName="SelectionIndicator" Property="Visibility" Value="Visible" />
                            <Setter TargetName="ItemBorder" Property="Background"
                                    Value="{DynamicResource ControlFillColorSecondaryBrush}" />
                        </Trigger>

                        <!-- 选中且悬停状态 -->
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="True" />
                                <Condition Property="IsMouseOver" Value="True" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="SelectionIndicator" Property="Visibility" Value="Visible" />
                            <Setter TargetName="ItemBorder" Property="Background"
                                    Value="{DynamicResource ControlFillColorTertiaryBrush}" />
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
