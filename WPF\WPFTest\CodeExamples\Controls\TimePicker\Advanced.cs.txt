// TimePicker C# 高级用法示例
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.ComponentModel;

namespace WPFTest.ViewModels.InputControls
{
    public partial class TimePickerPageViewModel : ObservableObject
    {
        /// <summary>
        /// 开始时间
        /// </summary>
        [ObservableProperty]
        private DateTime? startTime = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 8, 0, 0);

        /// <summary>
        /// 结束时间
        /// </summary>
        [ObservableProperty]
        private DateTime? endTime = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 17, 30, 0);

        /// <summary>
        /// 程序化时间
        /// </summary>
        [ObservableProperty]
        private DateTime? programmaticTime = DateTime.Now;

        /// <summary>
        /// 受限时间（工作时间约束）
        /// </summary>
        [ObservableProperty]
        private DateTime? restrictedTime;

        /// <summary>
        /// 时间验证消息
        /// </summary>
        [ObservableProperty]
        private string timeValidationMessage = string.Empty;

        /// <summary>
        /// 设置特定时间命令
        /// </summary>
        [RelayCommand]
        private void SetSpecificTime(string timeString)
        {
            if (TimeSpan.TryParse(timeString, out var timeSpan))
            {
                var today = DateTime.Today;
                ProgrammaticTime = today.Add(timeSpan);
                StatusMessage = $"⏰ 已设置程序化时间为: {timeString}";
                InteractionCount++;
            }
        }

        /// <summary>
        /// 设置工作时间命令
        /// </summary>
        [RelayCommand]
        private void SetWorkTime()
        {
            var workStart = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 9, 0, 0);
            ProgrammaticTime = workStart;
            StatusMessage = "💼 已设置为工作开始时间: 09:00";
            InteractionCount++;
        }

        /// <summary>
        /// 设置休息时间命令
        /// </summary>
        [RelayCommand]
        private void SetBreakTime()
        {
            var breakTime = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 12, 0, 0);
            ProgrammaticTime = breakTime;
            StatusMessage = "🍽️ 已设置为休息时间: 12:00";
            InteractionCount++;
        }

        /// <summary>
        /// 验证时间范围
        /// </summary>
        private void ValidateTimeRange()
        {
            if (StartTime.HasValue && EndTime.HasValue)
            {
                if (StartTime.Value >= EndTime.Value)
                {
                    TimeValidationMessage = "⚠️ 开始时间不能晚于或等于结束时间";
                }
                else
                {
                    var duration = EndTime.Value - StartTime.Value;
                    TimeValidationMessage = $"✅ 时间范围有效，持续时间: {duration.Hours}小时{duration.Minutes}分钟";
                }
            }
        }

        /// <summary>
        /// 验证工作时间约束
        /// </summary>
        private void ValidateWorkTimeConstraint()
        {
            if (RestrictedTime.HasValue)
            {
                var time = RestrictedTime.Value.TimeOfDay;
                var workStart = new TimeSpan(8, 0, 0);
                var workEnd = new TimeSpan(18, 0, 0);

                if (time < workStart || time > workEnd)
                {
                    TimeValidationMessage = "⚠️ 时间超出工作时间范围 (8:00 - 18:00)";
                }
                else
                {
                    TimeValidationMessage = "✅ 时间在工作时间范围内";
                }
            }
        }

        /// <summary>
        /// 属性变化处理
        /// </summary>
        partial void OnStartTimeChanged(DateTime? value)
        {
            ValidateTimeRange();
            StatusMessage = $"📅 开始时间变更: {value:HH:mm}";
            InteractionCount++;
        }

        partial void OnEndTimeChanged(DateTime? value)
        {
            ValidateTimeRange();
            StatusMessage = $"📅 结束时间变更: {value:HH:mm}";
            InteractionCount++;
        }

        partial void OnRestrictedTimeChanged(DateTime? value)
        {
            ValidateWorkTimeConstraint();
            StatusMessage = $"🔒 受限时间变更: {value:HH:mm}";
            InteractionCount++;
        }

        /// <summary>
        /// 计算时间差
        /// </summary>
        public TimeSpan? TimeDifference
        {
            get
            {
                if (StartTime.HasValue && EndTime.HasValue)
                {
                    return EndTime.Value - StartTime.Value;
                }
                return null;
            }
        }

        /// <summary>
        /// 格式化时间差
        /// </summary>
        public string FormattedTimeDifference
        {
            get
            {
                var diff = TimeDifference;
                if (diff.HasValue)
                {
                    return $"{diff.Value.Hours}小时{diff.Value.Minutes}分钟";
                }
                return "未计算";
            }
        }

        /// <summary>
        /// 检查是否为工作时间
        /// </summary>
        public bool IsWorkingTime
        {
            get
            {
                if (SelectedTime.HasValue)
                {
                    var time = SelectedTime.Value.TimeOfDay;
                    return time >= new TimeSpan(8, 0, 0) && time <= new TimeSpan(18, 0, 0);
                }
                return false;
            }
        }

        /// <summary>
        /// 获取时间段描述
        /// </summary>
        public string TimeOfDayDescription
        {
            get
            {
                if (!SelectedTime.HasValue) return "未选择时间";

                var hour = SelectedTime.Value.Hour;
                return hour switch
                {
                    >= 6 and < 12 => "🌅 上午",
                    >= 12 and < 14 => "🍽️ 午餐时间",
                    >= 14 and < 18 => "🌞 下午",
                    >= 18 and < 22 => "🌆 晚上",
                    _ => "🌙 深夜"
                };
            }
        }
    }
}
