<!-- 数据绑定面包屑导航示例 -->
<Grid>
    <Grid.RowDefinitions>
        <RowDefinition Height="Auto"/>
        <RowDefinition Height="*"/>
    </Grid.RowDefinitions>

    <!-- 数据绑定面包屑导航 -->
    <Border Grid.Row="0"
            Background="{DynamicResource LayerFillColorDefaultBrush}"
            BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
            BorderThickness="0,0,0,1"
            Padding="20,10">
        
        <!-- 使用 ItemsControl 进行数据绑定 -->
        <ItemsControl ItemsSource="{Binding BreadcrumbItems}">
            <ItemsControl.ItemsPanel>
                <ItemsPanelTemplate>
                    <StackPanel Orientation="Horizontal"/>
                </ItemsPanelTemplate>
            </ItemsControl.ItemsPanel>
            
            <ItemsControl.ItemTemplate>
                <DataTemplate>
                    <StackPanel Orientation="Horizontal">
                        <!-- 面包屑项 -->
                        <ui:Button
                                  Command="{Binding DataContext.NavigateToItemCommand, RelativeSource={RelativeSource AncestorType=ItemsControl}}"
                                  CommandParameter="{Binding}"
                                  Background="Transparent"
                                  BorderThickness="0"
                                  Padding="8,4"
                                  FontSize="14"
                                  Foreground="{Binding IsActive, Converter={StaticResource BoolToAccentBrushConverter}}"
                                  FontWeight="{Binding IsActive, Converter={StaticResource BoolToFontWeightConverter}}"
                                  IsEnabled="{Binding IsNavigable}">
                            
                            <ui:Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <!-- 图标 -->
                                    <zylo:ZyloIcon Icon="{Binding Icon}"
                                                  FontSize="14"
                                                  Margin="0,0,5,0"
                                                  Foreground="{Binding IsActive, Converter={StaticResource BoolToAccentBrushConverter}}"/>
                                    
                                    <!-- 标题 -->
                                    <TextBlock Text="{Binding Title}"
                                              VerticalAlignment="Center"/>
                                </StackPanel>
                            </ui:Button.Content>
                        </ui:Button>
                        
                        <!-- 分隔符（不是最后一项时显示） -->
                        <zylo:ZyloIcon Icon="ChevronRight"
                                      FontSize="12"
                                      Margin="8,0"
                                      VerticalAlignment="Center"
                                      Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                      Visibility="{Binding IsLast, Converter={StaticResource InverseBoolToVisibilityConverter}}"/>
                    </StackPanel>
                </DataTemplate>
            </ItemsControl.ItemTemplate>
        </ItemsControl>
    </Border>

    <!-- 主要内容区域 -->
    <ui:Card Grid.Row="1" Margin="20" Padding="20">
        <ScrollViewer>
            <StackPanel>
                <TextBlock Text="数据绑定面包屑导航示例"
                          FontSize="18"
                          FontWeight="SemiBold"
                          Margin="0,0,0,15"
                          Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>

                <TextBlock TextWrapping="Wrap" Margin="0,0,0,15"
                          Foreground="{DynamicResource TextFillColorSecondaryBrush}">
                    这个示例展示了数据绑定面包屑导航的实现方式：
                    <LineBreak/>• 使用 ObservableCollection 管理面包屑项
                    <LineBreak/>• ItemsControl 自动生成导航项
                    <LineBreak/>• 支持动态添加/删除导航项
                    <LineBreak/>• 数据驱动的导航状态管理
                    <LineBreak/>• 灵活的模板定制
                </TextBlock>

                <!-- 当前导航信息 -->
                <Border Background="{DynamicResource ControlFillColorSecondaryBrush}"
                       CornerRadius="4"
                       Padding="15"
                       Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="当前导航信息"
                                  FontWeight="SemiBold"
                                  Margin="0,0,0,10"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="当前页面: " FontWeight="SemiBold" Margin="0,0,10,5"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding CurrentPageTitle}" Margin="0,0,0,5"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="导航深度: " FontWeight="SemiBold" Margin="0,0,10,5"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding BreadcrumbItems.Count}" Margin="0,0,0,5"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="完整路径: " FontWeight="SemiBold" Margin="0,0,10,5"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding FullNavigationPath}" TextWrapping="Wrap" Margin="0,0,0,5"/>

                            <TextBlock Grid.Row="3" Grid.Column="0" Text="最后更新: " FontWeight="SemiBold" Margin="0,0,10,0"/>
                            <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding LastUpdated, StringFormat='yyyy-MM-dd HH:mm:ss'}"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- 动态操作按钮 -->
                <GroupBox Header="动态导航操作" Padding="15" Margin="0,0,0,20">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <ui:Button Content="📁 添加子页面"
                                      Command="{Binding AddChildPageCommand}"
                                      Appearance="Primary"
                                      Margin="0,0,10,0"/>
                            
                            <ui:Button Content="⬆️ 返回上级"
                                      Command="{Binding NavigateUpCommand}"
                                      Appearance="Secondary"
                                      Margin="0,0,10,0"/>
                            
                            <ui:Button Content="🏠 返回首页"
                                      Command="{Binding NavigateToRootCommand}"
                                      Appearance="Secondary"/>
                        </StackPanel>
                        
                        <StackPanel Orientation="Horizontal">
                            <ui:Button Content="🔄 刷新路径"
                                      Command="{Binding RefreshPathCommand}"
                                      Margin="0,0,10,0"/>
                            
                            <ui:Button Content="📋 复制路径"
                                      Command="{Binding CopyPathCommand}"
                                      Margin="0,0,10,0"/>
                            
                            <ui:Button Content="🗑️ 清空历史"
                                      Command="{Binding ClearHistoryCommand}"/>
                        </StackPanel>
                    </StackPanel>
                </GroupBox>

                <!-- 面包屑项列表 -->
                <GroupBox Header="面包屑项详情" Padding="15">
                    <ItemsControl ItemsSource="{Binding BreadcrumbItems}">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border Background="{DynamicResource ControlFillColorDefaultBrush}"
                                       BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                       BorderThickness="1"
                                       CornerRadius="4"
                                       Padding="10"
                                       Margin="0,0,0,5">
                                    
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- 图标和标题 -->
                                        <StackPanel Grid.Column="0" Orientation="Horizontal">
                                            <zylo:ZyloIcon Icon="{Binding Icon}"
                                                          FontSize="16"
                                                          Margin="0,0,8,0"
                                                          VerticalAlignment="Center"/>
                                            
                                            <TextBlock Text="{Binding Title}"
                                                      FontSize="14"
                                                      FontWeight="{Binding IsActive, Converter={StaticResource BoolToFontWeightConverter}}"
                                                      VerticalAlignment="Center"/>
                                        </StackPanel>

                                        <!-- 路径信息 -->
                                        <StackPanel Grid.Column="1" Margin="20,0">
                                            <TextBlock Text="{Binding Path}"
                                                      FontSize="12"
                                                      Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                                            
                                            <TextBlock Text="{Binding Description}"
                                                      FontSize="11"
                                                      Foreground="{DynamicResource TextFillColorTertiaryBrush}"
                                                      Visibility="{Binding Description, Converter={StaticResource StringToVisibilityConverter}}"/>
                                        </StackPanel>

                                        <!-- 状态标识 -->
                                        <StackPanel Grid.Column="2" Orientation="Horizontal">
                                            <Border Background="{DynamicResource AccentFillColorSecondaryBrush}"
                                                   CornerRadius="10"
                                                   Padding="6,2"
                                                   Margin="0,0,5,0"
                                                   Visibility="{Binding IsActive, Converter={StaticResource BoolToVisibilityConverter}}">
                                                <TextBlock Text="当前"
                                                          FontSize="10"
                                                          Foreground="{DynamicResource TextOnAccentFillColorPrimaryBrush}"/>
                                            </Border>
                                            
                                            <ui:Button Width="24" Height="24"
                                                      Padding="0"
                                                      Background="Transparent"
                                                      BorderThickness="0"
                                                      Command="{Binding DataContext.RemoveItemCommand, RelativeSource={RelativeSource AncestorType=ItemsControl}}"
                                                      CommandParameter="{Binding}"
                                                      ToolTip="移除此项"
                                                      Visibility="{Binding CanRemove, Converter={StaticResource BoolToVisibilityConverter}}">
                                                <zylo:ZyloIcon Icon="Close" FontSize="12" Foreground="Red"/>
                                            </ui:Button>
                                        </StackPanel>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>
    </ui:Card>
</Grid>
