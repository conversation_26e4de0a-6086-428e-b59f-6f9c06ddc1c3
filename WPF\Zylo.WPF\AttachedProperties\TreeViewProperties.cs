using System.Collections;
using System.Windows;
using System.Windows.Controls;

namespace Zylo.WPF.AttachedProperties;

/// <summary>
/// TreeView附加属性 - 完全MVVM方式
/// </summary>
public static class TreeViewProperties
{
    #region BindableItemsSource 附加属性

    /// <summary>
    /// 可绑定的ItemsSource附加属性
    /// </summary>
    public static readonly DependencyProperty BindableItemsSourceProperty =
        DependencyProperty.RegisterAttached(
            "BindableItemsSource",
            typeof(IEnumerable),
            typeof(TreeViewProperties),
            new PropertyMetadata(null, OnBindableItemsSourceChanged));

    public static IEnumerable GetBindableItemsSource(DependencyObject obj)
    {
        return (IEnumerable)obj.GetValue(BindableItemsSourceProperty);
    }

    public static void SetBindableItemsSource(DependencyObject obj, IEnumerable value)
    {
        obj.SetValue(BindableItemsSourceProperty, value);
    }

    private static void OnBindableItemsSourceChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is TreeView treeView && e.NewValue is IEnumerable items)
        {
            treeView.ItemsSource = items;
        }
    }

    #endregion

    #region SelectedItemBinding 附加属性

    /// <summary>
    /// 可绑定的SelectedItem附加属性
    /// </summary>
    public static readonly DependencyProperty SelectedItemBindingProperty =
        DependencyProperty.RegisterAttached(
            "SelectedItemBinding",
            typeof(object),
            typeof(TreeViewProperties),
            new FrameworkPropertyMetadata(null, FrameworkPropertyMetadataOptions.BindsTwoWayByDefault, OnSelectedItemBindingChanged));

    public static object GetSelectedItemBinding(DependencyObject obj)
    {
        return obj.GetValue(SelectedItemBindingProperty);
    }

    public static void SetSelectedItemBinding(DependencyObject obj, object value)
    {
        obj.SetValue(SelectedItemBindingProperty, value);
    }

    private static void OnSelectedItemBindingChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is TreeView treeView)
        {
            treeView.SelectedItemChanged -= OnTreeViewSelectedItemChanged;
            treeView.SelectedItemChanged += OnTreeViewSelectedItemChanged;

            // 🔥 添加从ViewModel到TreeView的更新
            if (e.NewValue != null && !ReferenceEquals(treeView.SelectedItem, e.NewValue))
            {
                // 尝试在TreeView中选中指定项
                SelectTreeViewItem(treeView, e.NewValue);
            }
        }
    }

    private static void OnTreeViewSelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
    {
        if (sender is TreeView treeView)
        {
            SetSelectedItemBinding(treeView, e.NewValue);
        }
    }

    /// <summary>
    /// 在TreeView中选中指定项
    /// </summary>
    private static void SelectTreeViewItem(TreeView treeView, object targetItem)
    {
        if (treeView == null || targetItem == null) return;

        // 递归查找并选中项
        SelectItemRecursive(treeView, targetItem);
    }

    /// <summary>
    /// 递归查找并选中项
    /// </summary>
    private static bool SelectItemRecursive(ItemsControl parent, object targetItem)
    {
        if (parent == null || targetItem == null) return false;

        foreach (var item in parent.Items)
        {
            if (ReferenceEquals(item, targetItem) || item.Equals(targetItem))
            {
                // 找到目标项
                var container = parent.ItemContainerGenerator.ContainerFromItem(item) as TreeViewItem;
                if (container != null)
                {
                    container.IsSelected = true;
                    container.BringIntoView();
                    return true;
                }
            }

            // 递归查找子项
            var childContainer = parent.ItemContainerGenerator.ContainerFromItem(item) as TreeViewItem;
            if (childContainer != null && SelectItemRecursive(childContainer, targetItem))
            {
                childContainer.IsExpanded = true; // 展开父项
                return true;
            }
        }

        return false;
    }

    #endregion
}

/// <summary>
/// ListBox附加属性
/// </summary>
public static class ListBoxProperties
{
    #region SelectedItemBinding 附加属性

    public static readonly DependencyProperty SelectedItemBindingProperty =
        DependencyProperty.RegisterAttached(
            "SelectedItemBinding",
            typeof(object),
            typeof(ListBoxProperties),
            new FrameworkPropertyMetadata(null, FrameworkPropertyMetadataOptions.BindsTwoWayByDefault, OnSelectedItemBindingChanged));

    public static object GetSelectedItemBinding(DependencyObject obj)
    {
        return obj.GetValue(SelectedItemBindingProperty);
    }

    public static void SetSelectedItemBinding(DependencyObject obj, object value)
    {
        obj.SetValue(SelectedItemBindingProperty, value);
    }

    private static void OnSelectedItemBindingChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is ListBox listBox)
        {
            listBox.SelectionChanged -= OnListBoxSelectionChanged;
            listBox.SelectionChanged += OnListBoxSelectionChanged;
            
            if (e.NewValue != null)
            {
                listBox.SelectedItem = e.NewValue;
            }
        }
    }

    private static void OnListBoxSelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        if (sender is ListBox listBox && e.AddedItems.Count > 0)
        {
            SetSelectedItemBinding(listBox, e.AddedItems[0]);
        }
    }

    #endregion
}
