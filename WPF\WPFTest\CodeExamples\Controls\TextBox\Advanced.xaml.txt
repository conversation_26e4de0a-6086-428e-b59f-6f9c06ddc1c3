<!-- TextBox 高级用法示例 -->
<StackPanel Margin="20" Spacing="15">

    <!-- 多行文本框 -->
    <GroupBox Header="多行文本框" Padding="15">
        <ui:TextBox Style="{StaticResource MultilineTextBoxStyle}"
                    Text="{Binding MultilineTextValue, UpdateSourceTrigger=PropertyChanged}"
                    PlaceholderText="支持多行输入，自动换行和滚动条..."
                    TextWrapping="Wrap"
                    AcceptsReturn="True"
                    VerticalScrollBarVisibility="Auto"/>
    </GroupBox>

    <!-- 特殊样式文本框 -->
    <GroupBox Header="特殊样式" Padding="15">
        <StackPanel Spacing="10">
            <!-- 透明样式 -->
            <ui:TextBox Style="{StaticResource TransparentTextBoxStyle}"
                        Text="{Binding TransparentTextValue, UpdateSourceTrigger=PropertyChanged}"
                        PlaceholderText="透明背景样式"/>
            
            <!-- 圆角样式 -->
            <ui:TextBox Style="{StaticResource RoundedTextBoxStyle}"
                        Text="{Binding RoundedTextValue, UpdateSourceTrigger=PropertyChanged}"
                        PlaceholderText="圆角样式"/>
            
            <!-- 搜索框样式 -->
            <ui:TextBox Style="{StaticResource SearchTextBoxStyle}"
                        Text="{Binding SearchTextValue, UpdateSourceTrigger=PropertyChanged}"
                        PlaceholderText="🔍 搜索框样式"/>
        </StackPanel>
    </GroupBox>

    <!-- 状态样式 -->
    <GroupBox Header="状态样式" Padding="15">
        <StackPanel Spacing="10">
            <!-- 错误状态 -->
            <ui:TextBox Style="{StaticResource ErrorTextBoxStyle}"
                        Text="{Binding ErrorTextValue, UpdateSourceTrigger=PropertyChanged}"
                        PlaceholderText="错误状态样式"/>
            
            <!-- 成功状态 -->
            <ui:TextBox Style="{StaticResource SuccessTextBoxStyle}"
                        Text="{Binding SuccessTextValue, UpdateSourceTrigger=PropertyChanged}"
                        PlaceholderText="成功状态样式"/>
        </StackPanel>
    </GroupBox>

    <!-- 带图标的文本框 -->
    <GroupBox Header="带图标文本框" Padding="15">
        <StackPanel Spacing="10">
            <!-- 左侧图标 -->
            <Border BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="4"
                    Background="{DynamicResource ControlFillColorDefaultBrush}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <ui:SymbolIcon Grid.Column="0" 
                                   Symbol="Person24" 
                                   FontSize="16"
                                   Margin="8,0,4,0"
                                   VerticalAlignment="Center"
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                    
                    <ui:TextBox Grid.Column="1"
                                Background="Transparent"
                                BorderThickness="0"
                                PlaceholderText="用户名"
                                Margin="0,0,8,0"/>
                </Grid>
            </Border>
            
            <!-- 右侧图标 -->
            <Border BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="4"
                    Background="{DynamicResource ControlFillColorDefaultBrush}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <ui:TextBox Grid.Column="0"
                                Background="Transparent"
                                BorderThickness="0"
                                PlaceholderText="搜索内容"
                                Margin="8,0,0,0"/>
                    
                    <ui:Button Grid.Column="1"
                               Background="Transparent"
                               BorderThickness="0"
                               Padding="8"
                               Command="{Binding SearchCommand}">
                        <ui:SymbolIcon Symbol="Search24" FontSize="16"/>
                    </ui:Button>
                </Grid>
            </Border>
        </StackPanel>
    </GroupBox>

    <!-- 数字输入框 -->
    <GroupBox Header="数字输入框" Padding="15">
        <StackPanel Spacing="10">
            <ui:TextBox Style="{StaticResource TextBoxStyle}"
                        Text="{Binding NumberValue, UpdateSourceTrigger=PropertyChanged}"
                        PlaceholderText="只能输入数字"
                        PreviewTextInput="NumberTextBox_PreviewTextInput"/>
            
            <ui:TextBox Style="{StaticResource TextBoxStyle}"
                        Text="{Binding DecimalValue, UpdateSourceTrigger=PropertyChanged}"
                        PlaceholderText="小数输入"
                        PreviewTextInput="DecimalTextBox_PreviewTextInput"/>
        </StackPanel>
    </GroupBox>

</StackPanel>
