// AutoSuggestBox C# 数据绑定示例
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;

namespace WPFTest.ViewModels.InputControls
{
    public partial class AutoSuggestBoxDataBindingViewModel : ObservableObject
    {
        #region 数据绑定属性

        /// <summary>
        /// 城市搜索文本
        /// </summary>
        [ObservableProperty]
        private string searchText = string.Empty;

        /// <summary>
        /// 编程语言搜索文本
        /// </summary>
        [ObservableProperty]
        private string languageSearchText = string.Empty;

        /// <summary>
        /// 国家搜索文本
        /// </summary>
        [ObservableProperty]
        private string countrySearchText = string.Empty;

        /// <summary>
        /// 城市建议列表
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<string> citySuggestions = new();

        /// <summary>
        /// 编程语言建议列表
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<string> languageSuggestions = new();

        /// <summary>
        /// 国家建议列表
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<string> countrySuggestions = new();

        /// <summary>
        /// 交互次数
        /// </summary>
        [ObservableProperty]
        private int interactionCount = 0;

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        private string statusMessage = "数据绑定示例已就绪";

        #endregion

        #region 数据源

        private readonly List<string> _allCities = new()
        {
            "北京", "上海", "广州", "深圳", "杭州", "南京", "苏州", "成都", "重庆", "武汉",
            "西安", "天津", "青岛", "大连", "厦门", "宁波", "无锡", "佛山", "东莞", "长沙",
            "郑州", "济南", "哈尔滨", "长春", "沈阳", "石家庄", "太原", "呼和浩特", "兰州", "银川"
        };

        private readonly List<string> _allLanguages = new()
        {
            "C#", "Java", "Python", "JavaScript", "TypeScript", "C++", "C", "Go", "Rust", "Swift",
            "Kotlin", "PHP", "Ruby", "Scala", "F#", "VB.NET", "Dart", "R", "MATLAB", "SQL",
            "HTML", "CSS", "Shell", "PowerShell", "Perl", "Lua", "Haskell", "Erlang", "Clojure", "Objective-C"
        };

        private readonly List<string> _allCountries = new()
        {
            "中国", "美国", "日本", "德国", "英国", "法国", "意大利", "加拿大", "澳大利亚", "韩国",
            "俄罗斯", "印度", "巴西", "墨西哥", "西班牙", "荷兰", "瑞士", "瑞典", "挪威", "丹麦",
            "芬兰", "比利时", "奥地利", "新西兰", "新加坡", "泰国", "马来西亚", "印度尼西亚", "菲律宾", "越南"
        };

        #endregion

        #region 构造函数

        public AutoSuggestBoxDataBindingViewModel()
        {
            // 监听属性变化
            PropertyChanged += OnPropertyChanged;
            
            // 初始化建议列表
            InitializeSuggestions();
            
            StatusMessage = "数据绑定示例已加载，开始输入以查看建议！";
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 属性变化处理
        /// </summary>
        private void OnPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            switch (e.PropertyName)
            {
                case nameof(SearchText):
                    UpdateCitySuggestions();
                    UpdateInteractionCount("城市搜索");
                    break;
                case nameof(LanguageSearchText):
                    UpdateLanguageSuggestions();
                    UpdateInteractionCount("编程语言搜索");
                    break;
                case nameof(CountrySearchText):
                    UpdateCountrySuggestions();
                    UpdateInteractionCount("国家搜索");
                    break;
            }
        }

        /// <summary>
        /// 更新交互次数
        /// </summary>
        private void UpdateInteractionCount(string action)
        {
            InteractionCount++;
            StatusMessage = $"🔍 {action} - 第 {InteractionCount} 次交互";
        }

        #endregion

        #region 建议更新方法

        /// <summary>
        /// 更新城市建议
        /// </summary>
        private void UpdateCitySuggestions()
        {
            CitySuggestions.Clear();
            
            if (string.IsNullOrWhiteSpace(SearchText))
            {
                // 显示热门城市
                var popularCities = _allCities.Take(8);
                foreach (var city in popularCities)
                {
                    CitySuggestions.Add(city);
                }
            }
            else
            {
                // 过滤匹配的城市
                var filteredCities = _allCities
                    .Where(city => city.Contains(SearchText, StringComparison.OrdinalIgnoreCase))
                    .Take(10);
                
                foreach (var city in filteredCities)
                {
                    CitySuggestions.Add(city);
                }
            }
        }

        /// <summary>
        /// 更新编程语言建议
        /// </summary>
        private void UpdateLanguageSuggestions()
        {
            LanguageSuggestions.Clear();
            
            if (string.IsNullOrWhiteSpace(LanguageSearchText))
            {
                // 显示热门编程语言
                var popularLanguages = _allLanguages.Take(8);
                foreach (var language in popularLanguages)
                {
                    LanguageSuggestions.Add(language);
                }
            }
            else
            {
                // 过滤匹配的编程语言
                var filteredLanguages = _allLanguages
                    .Where(lang => lang.Contains(LanguageSearchText, StringComparison.OrdinalIgnoreCase))
                    .Take(10);
                
                foreach (var language in filteredLanguages)
                {
                    LanguageSuggestions.Add(language);
                }
            }
        }

        /// <summary>
        /// 更新国家建议
        /// </summary>
        private void UpdateCountrySuggestions()
        {
            CountrySuggestions.Clear();
            
            if (string.IsNullOrWhiteSpace(CountrySearchText))
            {
                // 显示热门国家
                var popularCountries = _allCountries.Take(8);
                foreach (var country in popularCountries)
                {
                    CountrySuggestions.Add(country);
                }
            }
            else
            {
                // 过滤匹配的国家
                var filteredCountries = _allCountries
                    .Where(country => country.Contains(CountrySearchText, StringComparison.OrdinalIgnoreCase))
                    .Take(10);
                
                foreach (var country in filteredCountries)
                {
                    CountrySuggestions.Add(country);
                }
            }
        }

        /// <summary>
        /// 初始化建议列表
        /// </summary>
        private void InitializeSuggestions()
        {
            UpdateCitySuggestions();
            UpdateLanguageSuggestions();
            UpdateCountrySuggestions();
        }

        #endregion

        #region 命令

        /// <summary>
        /// 处理交互命令
        /// </summary>
        [RelayCommand]
        private void HandleInteraction(string? parameter)
        {
            var action = parameter ?? "未知操作";
            InteractionCount++;
            StatusMessage = $"🎯 执行了操作: {action}";
            
            if (action == "刷新建议")
            {
                InitializeSuggestions();
                StatusMessage = "✅ 建议列表已刷新";
            }
        }

        /// <summary>
        /// 清除状态命令
        /// </summary>
        [RelayCommand]
        private void ClearStatus()
        {
            SearchText = string.Empty;
            LanguageSearchText = string.Empty;
            CountrySearchText = string.Empty;
            
            InitializeSuggestions();
            
            StatusMessage = "🧹 所有搜索内容已清除";
        }

        /// <summary>
        /// 重置计数命令
        /// </summary>
        [RelayCommand]
        private void ResetCount()
        {
            InteractionCount = 0;
            StatusMessage = "🔄 交互计数已重置";
        }

        /// <summary>
        /// 选择建议命令
        /// </summary>
        [RelayCommand]
        private void SelectSuggestion(string? suggestion)
        {
            if (string.IsNullOrEmpty(suggestion))
                return;

            // 简化的选择逻辑，实际应用中需要更复杂的处理
            if (_allCities.Contains(suggestion))
            {
                SearchText = suggestion;
                StatusMessage = $"🏙️ 已选择城市: {suggestion}";
            }
            else if (_allLanguages.Contains(suggestion))
            {
                LanguageSearchText = suggestion;
                StatusMessage = $"💻 已选择编程语言: {suggestion}";
            }
            else if (_allCountries.Contains(suggestion))
            {
                CountrySearchText = suggestion;
                StatusMessage = $"🌍 已选择国家: {suggestion}";
            }
            
            InteractionCount++;
        }

        #endregion

        #region 实用方法

        /// <summary>
        /// 获取搜索统计信息
        /// </summary>
        public string GetSearchStatistics()
        {
            var totalLength = SearchText.Length + LanguageSearchText.Length + CountrySearchText.Length;
            return $"总搜索字符数: {totalLength}, 交互次数: {InteractionCount}";
        }

        /// <summary>
        /// 验证搜索输入
        /// </summary>
        public bool ValidateSearchInput(string input)
        {
            return !string.IsNullOrWhiteSpace(input) && input.Length <= 20;
        }

        /// <summary>
        /// 格式化建议显示
        /// </summary>
        public string FormatSuggestion(string suggestion, string searchText)
        {
            if (string.IsNullOrWhiteSpace(searchText))
                return suggestion;
            
            // 简化的格式化逻辑
            return suggestion.Replace(searchText, $"[{searchText}]", StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// 获取建议数量统计
        /// </summary>
        public string GetSuggestionCounts()
        {
            return $"城市: {CitySuggestions.Count}, 语言: {LanguageSuggestions.Count}, 国家: {CountrySuggestions.Count}";
        }

        #endregion
    }
}
