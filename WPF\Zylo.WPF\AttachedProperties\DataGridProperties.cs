using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;

namespace Zylo.WPF.AttachedProperties
{
    /// <summary>
    /// DataGrid 附加属性
    /// 提供DataGrid的扩展功能，如文字对齐控制
    /// </summary>
    public static class DataGridProperties
    {
        #region HeaderTextAlignment 附加属性

        /// <summary>
        /// 列标题文字对齐方式附加属性
        /// </summary>
        public static readonly DependencyProperty HeaderTextAlignmentProperty =
            DependencyProperty.RegisterAttached(
                "HeaderTextAlignment",
                typeof(HorizontalAlignment),
                typeof(DataGridProperties),
                new PropertyMetadata(HorizontalAlignment.Left, OnHeaderTextAlignmentChanged));

        /// <summary>
        /// 获取列标题文字对齐方式
        /// </summary>
        public static HorizontalAlignment GetHeaderTextAlignment(DependencyObject obj)
        {
            return (HorizontalAlignment)obj.GetValue(HeaderTextAlignmentProperty);
        }

        /// <summary>
        /// 设置列标题文字对齐方式
        /// </summary>
        public static void SetHeaderTextAlignment(DependencyObject obj, HorizontalAlignment value)
        {
            obj.SetValue(HeaderTextAlignmentProperty, value);
        }

        private static void OnHeaderTextAlignmentChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is DataGrid dataGrid && e.NewValue is HorizontalAlignment alignment)
            {
                // 应用到所有现有列
                ApplyHeaderAlignmentToColumns(dataGrid, alignment);
                
                // 监听列集合变化，自动应用到新添加的列
                dataGrid.Loaded += (s, args) => ApplyHeaderAlignmentToColumns(dataGrid, alignment);
            }
        }

        private static void ApplyHeaderAlignmentToColumns(DataGrid dataGrid, HorizontalAlignment alignment)
        {
            foreach (var column in dataGrid.Columns)
            {
                if (column is DataGridBoundColumn boundColumn)
                {
                    // 创建或更新列标题样式
                    var headerStyle = new Style(typeof(DataGridColumnHeader));
                    headerStyle.Setters.Add(new Setter(DataGridColumnHeader.HorizontalContentAlignmentProperty, alignment));
                    boundColumn.HeaderStyle = headerStyle;
                }
            }
        }

        #endregion

        #region CellTextAlignment 附加属性

        /// <summary>
        /// 单元格文字对齐方式附加属性
        /// </summary>
        public static readonly DependencyProperty CellTextAlignmentProperty =
            DependencyProperty.RegisterAttached(
                "CellTextAlignment",
                typeof(HorizontalAlignment),
                typeof(DataGridProperties),
                new PropertyMetadata(HorizontalAlignment.Left, OnCellTextAlignmentChanged));

        /// <summary>
        /// 获取单元格文字对齐方式
        /// </summary>
        public static HorizontalAlignment GetCellTextAlignment(DependencyObject obj)
        {
            return (HorizontalAlignment)obj.GetValue(CellTextAlignmentProperty);
        }

        /// <summary>
        /// 设置单元格文字对齐方式
        /// </summary>
        public static void SetCellTextAlignment(DependencyObject obj, HorizontalAlignment value)
        {
            obj.SetValue(CellTextAlignmentProperty, value);
        }

        private static void OnCellTextAlignmentChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is DataGrid dataGrid && e.NewValue is HorizontalAlignment alignment)
            {
                // 应用到所有现有列
                ApplyCellAlignmentToColumns(dataGrid, alignment);
                
                // 监听列集合变化，自动应用到新添加的列
                dataGrid.Loaded += (s, args) => ApplyCellAlignmentToColumns(dataGrid, alignment);
            }
        }

        private static void ApplyCellAlignmentToColumns(DataGrid dataGrid, HorizontalAlignment alignment)
        {
            foreach (var column in dataGrid.Columns)
            {
                if (column is DataGridTextColumn textColumn)
                {
                    // 创建或更新单元格文字样式
                    var elementStyle = new Style(typeof(TextBlock));
                    elementStyle.Setters.Add(new Setter(TextBlock.HorizontalAlignmentProperty, alignment));
                    elementStyle.Setters.Add(new Setter(TextBlock.VerticalAlignmentProperty, VerticalAlignment.Center));
                    elementStyle.Setters.Add(new Setter(TextBlock.TextAlignmentProperty, ConvertToTextAlignment(alignment)));
                    textColumn.ElementStyle = elementStyle;
                }
            }
        }

        private static TextAlignment ConvertToTextAlignment(HorizontalAlignment horizontalAlignment)
        {
            return horizontalAlignment switch
            {
                HorizontalAlignment.Left => TextAlignment.Left,
                HorizontalAlignment.Center => TextAlignment.Center,
                HorizontalAlignment.Right => TextAlignment.Right,
                HorizontalAlignment.Stretch => TextAlignment.Justify,
                _ => TextAlignment.Left
            };
        }

        #endregion

        #region TextAlignment 附加属性 (统一设置标题和内容)

        /// <summary>
        /// 统一文字对齐方式附加属性（同时设置标题和内容）
        /// </summary>
        public static readonly DependencyProperty TextAlignmentProperty =
            DependencyProperty.RegisterAttached(
                "TextAlignment",
                typeof(HorizontalAlignment),
                typeof(DataGridProperties),
                new PropertyMetadata(HorizontalAlignment.Left, OnTextAlignmentChanged));

        /// <summary>
        /// 获取统一文字对齐方式
        /// </summary>
        public static HorizontalAlignment GetTextAlignment(DependencyObject obj)
        {
            return (HorizontalAlignment)obj.GetValue(TextAlignmentProperty);
        }

        /// <summary>
        /// 设置统一文字对齐方式
        /// </summary>
        public static void SetTextAlignment(DependencyObject obj, HorizontalAlignment value)
        {
            obj.SetValue(TextAlignmentProperty, value);
        }

        private static void OnTextAlignmentChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is DataGrid dataGrid && e.NewValue is HorizontalAlignment alignment)
            {
                // 同时设置标题和内容的对齐方式
                SetHeaderTextAlignment(dataGrid, alignment);
                SetCellTextAlignment(dataGrid, alignment);
            }
        }

        #endregion
    }
}
