// TextBox C# 高级用法示例
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Text.RegularExpressions;
using System.Windows.Input;

namespace WPFTest.ViewModels.InputControls
{
    public partial class TextBoxPageViewModel : ObservableObject
    {
        #region 高级属性

        /// <summary>
        /// 多行文本值
        /// </summary>
        [ObservableProperty]
        private string multilineTextValue = "";

        /// <summary>
        /// 透明样式文本值
        /// </summary>
        [ObservableProperty]
        private string transparentTextValue = "";

        /// <summary>
        /// 圆角样式文本值
        /// </summary>
        [ObservableProperty]
        private string roundedTextValue = "";

        /// <summary>
        /// 搜索文本值
        /// </summary>
        [ObservableProperty]
        private string searchTextValue = "";

        /// <summary>
        /// 错误状态文本值
        /// </summary>
        [ObservableProperty]
        private string errorTextValue = "";

        /// <summary>
        /// 成功状态文本值
        /// </summary>
        [ObservableProperty]
        private string successTextValue = "";

        /// <summary>
        /// 数字值
        /// </summary>
        [ObservableProperty]
        private string numberValue = "";

        /// <summary>
        /// 小数值
        /// </summary>
        [ObservableProperty]
        private string decimalValue = "";

        #endregion

        #region 高级命令

        /// <summary>
        /// 搜索命令
        /// </summary>
        [RelayCommand]
        private void Search()
        {
            if (string.IsNullOrWhiteSpace(SearchTextValue))
            {
                StatusMessage = "🔍 请输入搜索内容";
                return;
            }

            StatusMessage = $"🔍 搜索: {SearchTextValue}";
            InteractionCount++;
        }

        /// <summary>
        /// 验证文本命令
        /// </summary>
        [RelayCommand]
        private void ValidateText(string textType)
        {
            var isValid = textType switch
            {
                "error" => ValidateErrorText(),
                "success" => ValidateSuccessText(),
                "number" => ValidateNumber(),
                "decimal" => ValidateDecimal(),
                _ => false
            };

            StatusMessage = isValid ? "✅ 验证通过" : "❌ 验证失败";
        }

        /// <summary>
        /// 格式化文本命令
        /// </summary>
        [RelayCommand]
        private void FormatText(string textType)
        {
            switch (textType)
            {
                case "multiline":
                    MultilineTextValue = FormatMultilineText(MultilineTextValue);
                    break;
                case "number":
                    NumberValue = FormatNumberText(NumberValue);
                    break;
                case "decimal":
                    DecimalValue = FormatDecimalText(DecimalValue);
                    break;
            }

            StatusMessage = $"📝 {textType} 文本已格式化";
        }

        #endregion

        #region 验证方法

        /// <summary>
        /// 验证错误文本
        /// </summary>
        private bool ValidateErrorText()
        {
            // 模拟错误验证逻辑
            return !string.IsNullOrWhiteSpace(ErrorTextValue) && ErrorTextValue.Length >= 3;
        }

        /// <summary>
        /// 验证成功文本
        /// </summary>
        private bool ValidateSuccessText()
        {
            // 模拟成功验证逻辑
            return !string.IsNullOrWhiteSpace(SuccessTextValue) && 
                   SuccessTextValue.Length >= 5 && 
                   !SuccessTextValue.Contains(" ");
        }

        /// <summary>
        /// 验证数字
        /// </summary>
        private bool ValidateNumber()
        {
            return int.TryParse(NumberValue, out _);
        }

        /// <summary>
        /// 验证小数
        /// </summary>
        private bool ValidateDecimal()
        {
            return decimal.TryParse(DecimalValue, out _);
        }

        #endregion

        #region 格式化方法

        /// <summary>
        /// 格式化多行文本
        /// </summary>
        private string FormatMultilineText(string text)
        {
            if (string.IsNullOrEmpty(text))
                return text;

            // 移除多余的空行和空格
            var lines = text.Split('\n')
                           .Select(line => line.Trim())
                           .Where(line => !string.IsNullOrEmpty(line));

            return string.Join("\n", lines);
        }

        /// <summary>
        /// 格式化数字文本
        /// </summary>
        private string FormatNumberText(string text)
        {
            if (int.TryParse(text, out int number))
            {
                return number.ToString("N0"); // 添加千位分隔符
            }
            return text;
        }

        /// <summary>
        /// 格式化小数文本
        /// </summary>
        private string FormatDecimalText(string text)
        {
            if (decimal.TryParse(text, out decimal number))
            {
                return number.ToString("F2"); // 保留两位小数
            }
            return text;
        }

        #endregion

        #region 输入限制方法

        /// <summary>
        /// 数字输入限制
        /// </summary>
        public void NumberTextBox_PreviewTextInput(object sender, TextCompositionEventArgs e)
        {
            // 只允许数字
            e.Handled = !IsTextAllowed(e.Text, @"^[0-9]+$");
        }

        /// <summary>
        /// 小数输入限制
        /// </summary>
        public void DecimalTextBox_PreviewTextInput(object sender, TextCompositionEventArgs e)
        {
            // 允许数字和小数点
            e.Handled = !IsTextAllowed(e.Text, @"^[0-9.]+$");
        }

        /// <summary>
        /// 检查文本是否符合正则表达式
        /// </summary>
        private static bool IsTextAllowed(string text, string pattern)
        {
            return Regex.IsMatch(text, pattern);
        }

        #endregion
    }
}
