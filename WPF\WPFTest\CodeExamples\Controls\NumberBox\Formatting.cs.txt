// NumberBox C# 格式化示例
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Globalization;
using System.Collections.ObjectModel;

namespace WPFTest.ViewModels.InputControls
{
    public partial class NumberBoxFormattingViewModel : ObservableObject
    {
        /// <summary>
        /// 基础数值
        /// </summary>
        [ObservableProperty]
        private double basicValue = 1234.5678;

        /// <summary>
        /// 货币值
        /// </summary>
        [ObservableProperty]
        private double currencyValue = 1234.56;

        /// <summary>
        /// 百分比值
        /// </summary>
        [ObservableProperty]
        private double percentageValue = 0.75;

        /// <summary>
        /// 温度值（摄氏度）
        /// </summary>
        [ObservableProperty]
        private double temperatureValue = 25.5;

        /// <summary>
        /// 重量值（千克）
        /// </summary>
        [ObservableProperty]
        private double weightValue = 68.5;

        /// <summary>
        /// 长度值（米）
        /// </summary>
        [ObservableProperty]
        private double lengthValue = 1.75;

        /// <summary>
        /// 速度值（公里/小时）
        /// </summary>
        [ObservableProperty]
        private double speedValue = 60;

        /// <summary>
        /// 当前格式
        /// </summary>
        [ObservableProperty]
        private string currentFormat = "N2";

        /// <summary>
        /// 格式化结果
        /// </summary>
        [ObservableProperty]
        private string formattedResult = string.Empty;

        /// <summary>
        /// 格式化选项
        /// </summary>
        public ObservableCollection<NumberFormatOption> FormatOptions { get; } = new();

        /// <summary>
        /// 选中的格式选项
        /// </summary>
        [ObservableProperty]
        private NumberFormatOption? selectedFormatOption;

        /// <summary>
        /// 构造函数
        /// </summary>
        public NumberBoxFormattingViewModel()
        {
            InitializeFormatOptions();
            SelectedFormatOption = FormatOptions.First();
            UpdateFormattedResult();
        }

        /// <summary>
        /// 初始化格式化选项
        /// </summary>
        private void InitializeFormatOptions()
        {
            FormatOptions.Add(new NumberFormatOption("标准数字", "N2", "1,234.57"));
            FormatOptions.Add(new NumberFormatOption("货币", "C2", "¥1,234.56"));
            FormatOptions.Add(new NumberFormatOption("百分比", "P2", "123,456.00%"));
            FormatOptions.Add(new NumberFormatOption("科学计数法", "E2", "1.23E+003"));
            FormatOptions.Add(new NumberFormatOption("固定小数点", "F2", "1234.56"));
            FormatOptions.Add(new NumberFormatOption("十六进制", "X", "4D2"));
        }

        /// <summary>
        /// 应用格式命令
        /// </summary>
        [RelayCommand]
        private void ApplyFormat()
        {
            if (SelectedFormatOption != null)
            {
                CurrentFormat = SelectedFormatOption.Format;
                UpdateFormattedResult();
            }
        }

        /// <summary>
        /// 更新格式化结果
        /// </summary>
        private void UpdateFormattedResult()
        {
            try
            {
                FormattedResult = FormatNumber(BasicValue, CurrentFormat);
            }
            catch (Exception ex)
            {
                FormattedResult = $"格式化错误: {ex.Message}";
            }
        }

        /// <summary>
        /// 格式化数字
        /// </summary>
        /// <param name="value">数值</param>
        /// <param name="format">格式字符串</param>
        /// <returns>格式化后的字符串</returns>
        public string FormatNumber(double value, string format)
        {
            try
            {
                return format.ToUpper() switch
                {
                    "N2" => value.ToString("N2", CultureInfo.CurrentCulture),
                    "C2" => value.ToString("C2", CultureInfo.CurrentCulture),
                    "P2" => (value / 100).ToString("P2", CultureInfo.CurrentCulture),
                    "E2" => value.ToString("E2", CultureInfo.CurrentCulture),
                    "F2" => value.ToString("F2", CultureInfo.CurrentCulture),
                    "X" => ((int)value).ToString("X", CultureInfo.CurrentCulture),
                    _ => value.ToString(format, CultureInfo.CurrentCulture)
                };
            }
            catch
            {
                return value.ToString();
            }
        }

        /// <summary>
        /// 格式化货币
        /// </summary>
        /// <param name="value">数值</param>
        /// <param name="culture">文化信息</param>
        /// <returns>格式化后的货币字符串</returns>
        public string FormatCurrency(double value, CultureInfo? culture = null)
        {
            culture ??= CultureInfo.CurrentCulture;
            return value.ToString("C", culture);
        }

        /// <summary>
        /// 格式化百分比
        /// </summary>
        /// <param name="value">数值（0-1之间）</param>
        /// <param name="decimals">小数位数</param>
        /// <returns>格式化后的百分比字符串</returns>
        public string FormatPercentage(double value, int decimals = 2)
        {
            var format = $"P{decimals}";
            return value.ToString(format, CultureInfo.CurrentCulture);
        }

        /// <summary>
        /// 格式化科学计数法
        /// </summary>
        /// <param name="value">数值</param>
        /// <param name="decimals">小数位数</param>
        /// <returns>格式化后的科学计数法字符串</returns>
        public string FormatScientific(double value, int decimals = 2)
        {
            var format = $"E{decimals}";
            return value.ToString(format, CultureInfo.CurrentCulture);
        }

        /// <summary>
        /// 格式化带单位的数值
        /// </summary>
        /// <param name="value">数值</param>
        /// <param name="unit">单位</param>
        /// <param name="decimals">小数位数</param>
        /// <returns>格式化后的带单位字符串</returns>
        public string FormatWithUnit(double value, string unit, int decimals = 1)
        {
            var format = $"F{decimals}";
            return $"{value.ToString(format, CultureInfo.CurrentCulture)} {unit}";
        }

        /// <summary>
        /// 温度转换和格式化
        /// </summary>
        public class TemperatureFormatter
        {
            /// <summary>
            /// 摄氏度转华氏度
            /// </summary>
            public static double CelsiusToFahrenheit(double celsius)
            {
                return celsius * 9 / 5 + 32;
            }

            /// <summary>
            /// 华氏度转摄氏度
            /// </summary>
            public static double FahrenheitToCelsius(double fahrenheit)
            {
                return (fahrenheit - 32) * 5 / 9;
            }

            /// <summary>
            /// 摄氏度转开尔文
            /// </summary>
            public static double CelsiusToKelvin(double celsius)
            {
                return celsius + 273.15;
            }

            /// <summary>
            /// 格式化温度
            /// </summary>
            public static string FormatTemperature(double value, string unit = "°C", int decimals = 1)
            {
                return $"{value.ToString($"F{decimals}", CultureInfo.CurrentCulture)}{unit}";
            }
        }

        /// <summary>
        /// 长度转换和格式化
        /// </summary>
        public class LengthFormatter
        {
            /// <summary>
            /// 米转厘米
            /// </summary>
            public static double MeterToCentimeter(double meter)
            {
                return meter * 100;
            }

            /// <summary>
            /// 米转英寸
            /// </summary>
            public static double MeterToInch(double meter)
            {
                return meter * 39.3701;
            }

            /// <summary>
            /// 米转英尺
            /// </summary>
            public static double MeterToFeet(double meter)
            {
                return meter * 3.28084;
            }

            /// <summary>
            /// 格式化长度
            /// </summary>
            public static string FormatLength(double value, string unit = "m", int decimals = 2)
            {
                return $"{value.ToString($"F{decimals}", CultureInfo.CurrentCulture)} {unit}";
            }
        }

        /// <summary>
        /// 重量转换和格式化
        /// </summary>
        public class WeightFormatter
        {
            /// <summary>
            /// 千克转磅
            /// </summary>
            public static double KilogramToPound(double kilogram)
            {
                return kilogram * 2.20462;
            }

            /// <summary>
            /// 千克转盎司
            /// </summary>
            public static double KilogramToOunce(double kilogram)
            {
                return kilogram * 35.274;
            }

            /// <summary>
            /// 格式化重量
            /// </summary>
            public static string FormatWeight(double value, string unit = "kg", int decimals = 1)
            {
                return $"{value.ToString($"F{decimals}", CultureInfo.CurrentCulture)} {unit}";
            }
        }

        /// <summary>
        /// 本地化格式化
        /// </summary>
        public class LocalizedFormatter
        {
            /// <summary>
            /// 获取本地化的数字格式
            /// </summary>
            public static string GetLocalizedNumber(double value, string cultureName)
            {
                try
                {
                    var culture = CultureInfo.GetCultureInfo(cultureName);
                    return value.ToString("N2", culture);
                }
                catch
                {
                    return value.ToString("N2", CultureInfo.CurrentCulture);
                }
            }

            /// <summary>
            /// 获取本地化的货币格式
            /// </summary>
            public static string GetLocalizedCurrency(double value, string cultureName)
            {
                try
                {
                    var culture = CultureInfo.GetCultureInfo(cultureName);
                    return value.ToString("C2", culture);
                }
                catch
                {
                    return value.ToString("C2", CultureInfo.CurrentCulture);
                }
            }
        }
    }

    /// <summary>
    /// 数字格式化选项
    /// </summary>
    public class NumberFormatOption
    {
        public string Name { get; set; }
        public string Format { get; set; }
        public string Example { get; set; }

        public NumberFormatOption(string name, string format, string example)
        {
            Name = name;
            Format = format;
            Example = example;
        }

        public override string ToString() => $"{Name} ({Example})";
    }
}
