<!-- TextBox 样式使用示例 -->
<StackPanel Margin="20" Spacing="15">

    <!-- 基础样式系列 -->
    <GroupBox Header="基础样式系列" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <!-- 基础样式 -->
            <StackPanel Margin="8">
                <TextBlock Text="基础样式" FontWeight="Bold" Margin="0,0,0,4"/>
                <ui:TextBox Style="{StaticResource TextBoxBaseStyle}"
                            Text="基础样式"
                            Width="150"/>
            </StackPanel>

            <!-- 标准样式 -->
            <StackPanel Margin="8">
                <TextBlock Text="标准样式" FontWeight="Bold" Margin="0,0,0,4"/>
                <ui:TextBox Style="{StaticResource TextBoxStyle}"
                            Text="标准样式"/>
            </StackPanel>

            <!-- 小型样式 -->
            <StackPanel Margin="8">
                <TextBlock Text="小型样式" FontWeight="Bold" Margin="0,0,0,4"/>
                <ui:TextBox Style="{StaticResource SmallTextBoxStyle}"
                            Text="小型样式"/>
            </StackPanel>

            <!-- 大型样式 -->
            <StackPanel Margin="8">
                <TextBlock Text="大型样式" FontWeight="Bold" Margin="0,0,0,4"/>
                <ui:TextBox Style="{StaticResource LargeTextBoxStyle}"
                            Text="大型样式"/>
            </StackPanel>
        </WrapPanel>
    </GroupBox>

    <!-- 特殊样式系列 (优化后的卡片展示) -->
    <GroupBox Header="特殊样式系列" Padding="15">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 透明样式 -->
            <Border Grid.Column="0" Background="{DynamicResource ControlFillColorSecondaryBrush}"
                    CornerRadius="8" Padding="12" Margin="0,0,4,0">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                        <TextBlock Text="🔍" FontSize="14" Margin="0,0,4,0"/>
                        <TextBlock Text="透明样式" FontWeight="Bold"/>
                    </StackPanel>
                    <TextBlock Text="底部边框，透明背景"
                               FontSize="11"
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                               Margin="0,0,0,8"/>
                    <ui:TextBox Style="{StaticResource TransparentTextBoxStyle}"
                                Text="透明背景"
                                PlaceholderText="透明样式示例"/>
                </StackPanel>
            </Border>

            <!-- 圆角样式 -->
            <StackPanel>
                <TextBlock Text="圆角样式 (RoundedTextBoxStyle)" FontWeight="Bold" Margin="0,0,0,4"/>
                <ui:TextBox Style="{StaticResource RoundedTextBoxStyle}"
                            Text="圆角边框样式"
                            Width="250"
                            HorizontalAlignment="Left"/>
            </StackPanel>

            <!-- 搜索框样式 -->
            <StackPanel>
                <TextBlock Text="搜索框样式 (SearchTextBoxStyle)" FontWeight="Bold" Margin="0,0,0,4"/>
                <ui:TextBox Style="{StaticResource SearchTextBoxStyle}"
                            PlaceholderText="🔍 搜索内容..."
                            HorizontalAlignment="Left"/>
            </StackPanel>

            <!-- 多行样式 -->
            <StackPanel>
                <TextBlock Text="多行样式 (MultilineTextBoxStyle)" FontWeight="Bold" Margin="0,0,0,4"/>
                <ui:TextBox Style="{StaticResource MultilineTextBoxStyle}"
                            Text="这是多行文本框样式&#x0a;支持换行和滚动条&#x0a;适合长文本输入"
                            HorizontalAlignment="Left"/>
            </StackPanel>
        </StackPanel>
    </GroupBox>

    <!-- 状态样式系列 -->
    <GroupBox Header="状态样式系列" Padding="15">
        <StackPanel Spacing="10">
            <!-- 错误状态 -->
            <StackPanel>
                <TextBlock Text="错误状态 (ErrorTextBoxStyle)" FontWeight="Bold" Margin="0,0,0,4"/>
                <ui:TextBox Style="{StaticResource ErrorTextBoxStyle}"
                            Text="错误状态样式"
                            Width="200"
                            HorizontalAlignment="Left"/>
            </StackPanel>

            <!-- 成功状态 -->
            <StackPanel>
                <TextBlock Text="成功状态 (SuccessTextBoxStyle)" FontWeight="Bold" Margin="0,0,0,4"/>
                <ui:TextBox Style="{StaticResource SuccessTextBoxStyle}"
                            Text="成功状态样式"
                            Width="200"
                            HorizontalAlignment="Left"/>
            </StackPanel>
        </StackPanel>
    </GroupBox>

    <!-- 自定义样式示例 -->
    <GroupBox Header="自定义样式示例" Padding="15">
        <StackPanel Spacing="10">
            <!-- 自定义样式1 -->
            <StackPanel>
                <TextBlock Text="自定义样式 - 渐变背景" FontWeight="Bold" Margin="0,0,0,4"/>
                <ui:TextBox Width="250" HorizontalAlignment="Left">
                    <ui:TextBox.Style>
                        <Style TargetType="ui:TextBox" BasedOn="{StaticResource TextBoxBaseStyle}">
                            <Setter Property="Background">
                                <Setter.Value>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#E3F2FD" Offset="0"/>
                                        <GradientStop Color="#BBDEFB" Offset="1"/>
                                    </LinearGradientBrush>
                                </Setter.Value>
                            </Setter>
                            <Setter Property="BorderBrush" Value="#2196F3"/>
                            <Setter Property="BorderThickness" Value="2"/>
                        </Style>
                    </ui:TextBox.Style>
                    渐变背景样式
                </ui:TextBox>
            </StackPanel>

            <!-- 自定义样式2 -->
            <StackPanel>
                <TextBlock Text="自定义样式 - 阴影效果" FontWeight="Bold" Margin="0,0,0,4"/>
                <ui:TextBox Width="250" HorizontalAlignment="Left">
                    <ui:TextBox.Style>
                        <Style TargetType="ui:TextBox" BasedOn="{StaticResource TextBoxBaseStyle}">
                            <Setter Property="Effect">
                                <Setter.Value>
                                    <DropShadowEffect Color="Gray" 
                                                      Direction="315" 
                                                      ShadowDepth="3" 
                                                      BlurRadius="5" 
                                                      Opacity="0.3"/>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </ui:TextBox.Style>
                    阴影效果样式
                </ui:TextBox>
            </StackPanel>
        </StackPanel>
    </GroupBox>

</StackPanel>
