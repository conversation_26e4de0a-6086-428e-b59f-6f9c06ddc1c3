using System.Diagnostics;
using System.IO;

namespace Zylo.WPF.Controls.YFile;

/// <summary>
/// 文件操作服务 - 提供文件系统操作功能
/// </summary>
public static class FileOperationService
{
    /// <summary>
    /// 异步打开文件或文件夹
    /// </summary>
    /// <param name="path">文件或文件夹路径</param>
    /// <returns>是否成功打开</returns>
    public static async Task<bool> OpenAsync(string path)
    {
        try
        {
            if (string.IsNullOrEmpty(path) || !File.Exists(path) && !Directory.Exists(path))
                return false;

            await Task.Run(() =>
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = path,
                    UseShellExecute = true,
                    Verb = "open"
                };
                Process.Start(startInfo);
            });

            return true;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"打开文件失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 在新窗口中打开文件夹
    /// </summary>
    /// <param name="folderPath">文件夹路径</param>
    /// <returns>是否成功打开</returns>
    public static async Task<bool> OpenInNewWindowAsync(string folderPath)
    {
        try
        {
            if (string.IsNullOrEmpty(folderPath) || !Directory.Exists(folderPath))
                return false;

            await Task.Run(() =>
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = "explorer.exe",
                    Arguments = $"\"{folderPath}\"",
                    UseShellExecute = true
                };
                Process.Start(startInfo);
            });

            return true;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"在新窗口打开文件夹失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 在资源管理器中显示文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>是否成功显示</returns>
    public static async Task<bool> ShowInExplorerAsync(string filePath)
    {
        try
        {
            if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                return false;

            await Task.Run(() =>
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = "explorer.exe",
                    Arguments = $"/select,\"{filePath}\"",
                    UseShellExecute = true
                };
                Process.Start(startInfo);
            });

            return true;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"在资源管理器中显示文件失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 复制文件到剪贴板
    /// </summary>
    /// <param name="filePaths">文件路径数组</param>
    /// <returns>是否成功复制</returns>
    public static bool CopyToClipboard(string[] filePaths)
    {
        try
        {
            var fileDropList = new System.Collections.Specialized.StringCollection();
            foreach (var path in filePaths)
            {
                if (File.Exists(path) || Directory.Exists(path))
                {
                    fileDropList.Add(path);
                }
            }

            if (fileDropList.Count > 0)
            {
                System.Windows.Clipboard.SetFileDropList(fileDropList);
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"复制到剪贴板失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 剪切文件路径到剪贴板
    /// </summary>
    /// <param name="filePaths">文件路径数组</param>
    /// <returns>是否成功剪切</returns>
    public static bool CutToClipboard(string[] filePaths)
    {
        try
        {
            if (filePaths == null || filePaths.Length == 0)
                return false;

            var fileDropList = new System.Collections.Specialized.StringCollection();
            foreach (var path in filePaths)
            {
                if (File.Exists(path) || Directory.Exists(path))
                {
                    fileDropList.Add(path);
                }
            }

            if (fileDropList.Count > 0)
            {
                // 设置剪切标记
                var data = new DataObject();
                data.SetFileDropList(fileDropList);

                // 添加剪切标记（Windows Shell格式）
                var dropEffect = new byte[] { 2, 0, 0, 0 }; // DROPEFFECT_MOVE
                data.SetData("Preferred DropEffect", dropEffect);

                System.Windows.Clipboard.SetDataObject(data);
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"剪切到剪贴板失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 从剪贴板粘贴文件
    /// </summary>
    /// <param name="filePaths">源文件路径数组</param>
    /// <param name="targetPath">目标路径</param>
    /// <returns>是否成功粘贴</returns>
    public static async Task<bool> PasteFromClipboardAsync(string[] filePaths, string targetPath)
    {
        try
        {
            if (filePaths == null || filePaths.Length == 0 || string.IsNullOrEmpty(targetPath))
                return false;

            if (!Directory.Exists(targetPath))
                return false;

            // 检查是否为剪切操作
            bool isCutOperation = false;
            if (System.Windows.Clipboard.GetDataObject() is DataObject dataObject)
            {
                if (dataObject.GetDataPresent("Preferred DropEffect"))
                {
                    var dropEffect = (byte[])dataObject.GetData("Preferred DropEffect");
                    isCutOperation = dropEffect != null && dropEffect.Length >= 4 && dropEffect[0] == 2;
                }
            }

            await Task.Run(() =>
            {
                foreach (var sourcePath in filePaths)
                {
                    if (File.Exists(sourcePath))
                    {
                        var fileName = Path.GetFileName(sourcePath);
                        var destPath = Path.Combine(targetPath, fileName);

                        // 如果目标文件已存在，生成新名称
                        destPath = GetUniqueFileName(destPath);

                        if (isCutOperation)
                        {
                            File.Move(sourcePath, destPath);
                        }
                        else
                        {
                            File.Copy(sourcePath, destPath);
                        }
                    }
                    else if (Directory.Exists(sourcePath))
                    {
                        var dirName = Path.GetFileName(sourcePath);
                        var destPath = Path.Combine(targetPath, dirName);

                        // 如果目标目录已存在，生成新名称
                        destPath = GetUniqueDirectoryName(destPath);

                        if (isCutOperation)
                        {
                            Directory.Move(sourcePath, destPath);
                        }
                        else
                        {
                            CopyDirectory(sourcePath, destPath);
                        }
                    }
                }
            });

            return true;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"粘贴失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 递归复制目录
    /// </summary>
    /// <param name="sourceDir">源目录</param>
    /// <param name="destDir">目标目录</param>
    private static void CopyDirectory(string sourceDir, string destDir)
    {
        Directory.CreateDirectory(destDir);

        foreach (var file in Directory.GetFiles(sourceDir))
        {
            var fileName = Path.GetFileName(file);
            var destFile = Path.Combine(destDir, fileName);
            destFile = GetUniqueFileName(destFile);
            File.Copy(file, destFile);
        }

        foreach (var dir in Directory.GetDirectories(sourceDir))
        {
            var dirName = Path.GetFileName(dir);
            var destSubDir = Path.Combine(destDir, dirName);
            CopyDirectory(dir, destSubDir);
        }
    }

    /// <summary>
    /// 获取唯一的文件名（如果文件已存在，添加数字后缀）
    /// </summary>
    /// <param name="filePath">原文件路径</param>
    /// <returns>唯一的文件路径</returns>
    private static string GetUniqueFileName(string filePath)
    {
        if (!File.Exists(filePath))
            return filePath;

        var directory = Path.GetDirectoryName(filePath);
        var fileName = Path.GetFileNameWithoutExtension(filePath);
        var extension = Path.GetExtension(filePath);

        int counter = 1;
        string newFilePath;

        do
        {
            newFilePath = Path.Combine(directory, $"{fileName} ({counter}){extension}");
            counter++;
        } while (File.Exists(newFilePath));

        return newFilePath;
    }

    /// <summary>
    /// 获取唯一的目录名（如果目录已存在，添加数字后缀）
    /// </summary>
    /// <param name="dirPath">原目录路径</param>
    /// <returns>唯一的目录路径</returns>
    private static string GetUniqueDirectoryName(string dirPath)
    {
        if (!Directory.Exists(dirPath))
            return dirPath;

        var parentDir = Path.GetDirectoryName(dirPath);
        var dirName = Path.GetFileName(dirPath);

        int counter = 1;
        string newDirPath;

        do
        {
            newDirPath = Path.Combine(parentDir, $"{dirName} ({counter})");
            counter++;
        } while (Directory.Exists(newDirPath));

        return newDirPath;
    }

    /// <summary>
    /// 删除文件或文件夹到回收站
    /// </summary>
    /// <param name="path">文件或文件夹路径</param>
    /// <returns>是否成功删除</returns>
    public static async Task<bool> DeleteToRecycleBinAsync(string path)
    {
        try
        {
            if (string.IsNullOrEmpty(path) || !File.Exists(path) && !Directory.Exists(path))
                return false;

            await Task.Run(() =>
            {
                // 使用 Shell32 API 删除到回收站
                Microsoft.VisualBasic.FileIO.FileSystem.DeleteFile(path,
                    Microsoft.VisualBasic.FileIO.UIOption.OnlyErrorDialogs,
                    Microsoft.VisualBasic.FileIO.RecycleOption.SendToRecycleBin);
            });

            return true;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"删除到回收站失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 删除多个文件或文件夹到回收站
    /// </summary>
    /// <param name="paths">文件或文件夹路径数组</param>
    /// <returns>是否成功删除</returns>
    public static async Task<bool> DeleteToRecycleBinAsync(string[] paths)
    {
        try
        {
            if (paths == null || paths.Length == 0)
                return false;

            await Task.Run(() =>
            {
                foreach (var path in paths)
                {
                    if (File.Exists(path))
                    {
                        Microsoft.VisualBasic.FileIO.FileSystem.DeleteFile(path,
                            Microsoft.VisualBasic.FileIO.UIOption.OnlyErrorDialogs,
                            Microsoft.VisualBasic.FileIO.RecycleOption.SendToRecycleBin);
                    }
                    else if (Directory.Exists(path))
                    {
                        Microsoft.VisualBasic.FileIO.FileSystem.DeleteDirectory(path,
                            Microsoft.VisualBasic.FileIO.UIOption.OnlyErrorDialogs,
                            Microsoft.VisualBasic.FileIO.RecycleOption.SendToRecycleBin);
                    }
                }
            });

            return true;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"删除到回收站失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 重命名文件或文件夹
    /// </summary>
    /// <param name="oldPath">原路径</param>
    /// <param name="newName">新名称</param>
    /// <returns>是否成功重命名</returns>
    public static async Task<bool> RenameAsync(string oldPath, string newName)
    {
        try
        {
            if (string.IsNullOrEmpty(oldPath) || string.IsNullOrEmpty(newName))
                return false;

            var directory = Path.GetDirectoryName(oldPath);
            if (string.IsNullOrEmpty(directory))
                return false;

            var newPath = Path.Combine(directory, newName);

            // 检查新路径是否已存在（但排除自身）
            if (!string.Equals(oldPath, newPath, StringComparison.OrdinalIgnoreCase))
            {
                if (File.Exists(newPath) || Directory.Exists(newPath))
                    return false;
            }

            await Task.Run(() =>
            {
                if (File.Exists(oldPath))
                {
                    File.Move(oldPath, newPath);
                }
                else if (Directory.Exists(oldPath))
                {
                    Directory.Move(oldPath, newPath);
                }
                else
                {
                    throw new FileNotFoundException("文件或文件夹不存在");
                }
            });

            return true;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"重命名失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 获取文件或文件夹属性信息
    /// </summary>
    /// <param name="path">文件或文件夹路径</param>
    /// <returns>属性信息字符串</returns>
    public static async Task<string> GetPropertiesAsync(string path)
    {
        try
        {
            if (string.IsNullOrEmpty(path))
                return "路径为空";

            return await Task.Run(() =>
            {
                if (File.Exists(path))
                {
                    var fileInfo = new FileInfo(path);
                    return $"名称: {fileInfo.Name}\n" +
                           $"类型: 文件\n" +
                           $"大小: {FileSizeService.FormatFileSize(fileInfo.Length)}\n" +
                           $"创建时间: {fileInfo.CreationTime:yyyy-MM-dd HH:mm:ss}\n" +
                           $"修改时间: {fileInfo.LastWriteTime:yyyy-MM-dd HH:mm:ss}\n" +
                           $"访问时间: {fileInfo.LastAccessTime:yyyy-MM-dd HH:mm:ss}\n" +
                           $"属性: {fileInfo.Attributes}\n" +
                           $"路径: {fileInfo.FullName}";
                }
                else if (Directory.Exists(path))
                {
                    var dirInfo = new DirectoryInfo(path);
                    var fileCount = dirInfo.GetFiles().Length;
                    var folderCount = dirInfo.GetDirectories().Length;
                    
                    return $"名称: {dirInfo.Name}\n" +
                           $"类型: 文件夹\n" +
                           $"包含: {fileCount} 个文件, {folderCount} 个文件夹\n" +
                           $"创建时间: {dirInfo.CreationTime:yyyy-MM-dd HH:mm:ss}\n" +
                           $"修改时间: {dirInfo.LastWriteTime:yyyy-MM-dd HH:mm:ss}\n" +
                           $"访问时间: {dirInfo.LastAccessTime:yyyy-MM-dd HH:mm:ss}\n" +
                           $"属性: {dirInfo.Attributes}\n" +
                           $"路径: {dirInfo.FullName}";
                }
                else
                {
                    return "文件或文件夹不存在";
                }
            });
        }
        catch (Exception ex)
        {
            return $"获取属性失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 检查路径是否有效
    /// </summary>
    /// <param name="path">路径</param>
    /// <returns>是否有效</returns>
    public static bool IsValidPath(string path)
    {
        try
        {
            return !string.IsNullOrEmpty(path) && 
                   (File.Exists(path) || Directory.Exists(path));
        }
        catch
        {
            return false;
        }
    }
}
