<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml">

    <!-- ScrollViewer 基础样式 -->
    <Style x:Key="ScrollViewerBaseStyle" TargetType="ScrollViewer">
        <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="0"/>
        <Setter Property="Margin" Value="0"/>
        <Setter Property="VerticalScrollBarVisibility" Value="Auto"/>
        <Setter Property="HorizontalScrollBarVisibility" Value="Auto"/>
        <Setter Property="CanContentScroll" Value="True"/>
        <Setter Property="PanningMode" Value="Both"/>
    </Style>

    <!-- ScrollViewer 标准样式 -->
    <Style x:Key="ScrollViewerStyle" TargetType="ScrollViewer" BasedOn="{StaticResource ScrollViewerBaseStyle}">
        <!-- 标准样式的特定设置 -->
    </Style>

    <!-- ScrollViewer 现代化样式 -->
    <Style x:Key="ModernScrollViewerStyle" TargetType="ScrollViewer" BasedOn="{StaticResource ScrollViewerBaseStyle}">
        <Setter Property="Background" Value="{DynamicResource CardBackgroundFillColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" 
                                  Opacity="0.1" 
                                  ShadowDepth="4" 
                                  BlurRadius="8"/>
            </Setter.Value>
        </Setter>
        
        <!-- 悬停效果 -->
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="Black" 
                                          Opacity="0.15" 
                                          ShadowDepth="6" 
                                          BlurRadius="12"/>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- ScrollViewer 紧凑样式 -->
    <Style x:Key="CompactScrollViewerStyle" TargetType="ScrollViewer" BasedOn="{StaticResource ScrollViewerBaseStyle}">
        <Setter Property="Padding" Value="4"/>
        <Setter Property="Margin" Value="2"/>
        <Setter Property="BorderThickness" Value="1"/>
    </Style>

    <!-- ScrollViewer 强调样式 -->
    <Style x:Key="AccentScrollViewerStyle" TargetType="ScrollViewer" BasedOn="{StaticResource ScrollViewerBaseStyle}">
        <Setter Property="Background" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource AccentStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="2"/>
    </Style>

    <!-- ScrollViewer 透明样式 -->
    <Style x:Key="TransparentScrollViewerStyle" TargetType="ScrollViewer" BasedOn="{StaticResource ScrollViewerBaseStyle}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
    </Style>

    <!-- ScrollViewer 卡片样式 -->
    <Style x:Key="CardScrollViewerStyle" TargetType="ScrollViewer">
        <Setter Property="Background" Value="{DynamicResource CardBackgroundFillColorDefaultBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource CardStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="VerticalScrollBarVisibility" Value="Auto"/>
        <Setter Property="HorizontalScrollBarVisibility" Value="Auto"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" 
                                  Opacity="0.08" 
                                  ShadowDepth="2" 
                                  BlurRadius="8"/>
            </Setter.Value>
        </Setter>
        
        <!-- 悬停效果 -->
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="Black" 
                                          Opacity="0.15" 
                                          ShadowDepth="4" 
                                          BlurRadius="12"/>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- ScrollViewer 列表样式 -->
    <Style x:Key="ListScrollViewerStyle" TargetType="ScrollViewer" BasedOn="{StaticResource ScrollViewerBaseStyle}">
        <Setter Property="HorizontalScrollBarVisibility" Value="Disabled"/>
        <Setter Property="VerticalScrollBarVisibility" Value="Auto"/>
        <Setter Property="CanContentScroll" Value="True"/>
        <Setter Property="PanningMode" Value="VerticalOnly"/>
    </Style>

    <!-- ScrollViewer 表格样式 -->
    <Style x:Key="GridScrollViewerStyle" TargetType="ScrollViewer" BasedOn="{StaticResource ScrollViewerBaseStyle}">
        <Setter Property="HorizontalScrollBarVisibility" Value="Auto"/>
        <Setter Property="VerticalScrollBarVisibility" Value="Auto"/>
        <Setter Property="CanContentScroll" Value="False"/>
        <Setter Property="PanningMode" Value="Both"/>
    </Style>

    <!-- ScrollViewer 文本样式 -->
    <Style x:Key="TextScrollViewerStyle" TargetType="ScrollViewer" BasedOn="{StaticResource ScrollViewerBaseStyle}">
        <Setter Property="HorizontalScrollBarVisibility" Value="Auto"/>
        <Setter Property="VerticalScrollBarVisibility" Value="Auto"/>
        <Setter Property="CanContentScroll" Value="False"/>
        <Setter Property="PanningMode" Value="Both"/>
        <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
    </Style>

</ResourceDictionary>
