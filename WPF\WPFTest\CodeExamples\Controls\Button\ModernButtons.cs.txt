// 现代化按钮 C# 代码示例
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace WPFTest.ViewModels.ButtonControls
{
    public partial class ButtonPageViewModel : ObservableObject
    {
        /// <summary>
        /// 现代化按钮点击命令
        /// </summary>
        [RelayCommand]
        private void ModernButtonClick(string parameter)
        {
            var message = parameter switch
            {
                "现代化" => "✨ 点击了现代化按钮",
                "现代化图标" => "✨ 点击了现代化图标按钮",
                "渐变" => "🌈 点击了渐变按钮",
                "渐变图标" => "🌈 点击了渐变图标按钮",
                "默认" => "🔘 点击了默认样式按钮",
                "主要" => "🔵 点击了主要按钮",
                "次要" => "⚪ 点击了次要按钮",
                "危险" => "🔴 点击了危险按钮",
                _ => $"🎨 点击了按钮: {parameter}"
            };
            
            StatusMessage = message;
        }

        /// <summary>
        /// 样式切换命令
        /// </summary>
        [RelayCommand]
        private void SwitchButtonStyle(string styleName)
        {
            StatusMessage = $"切换到 {styleName} 样式";
            
            // 这里可以实现动态样式切换逻辑
            // 例如：更新资源字典中的样式
        }

        /// <summary>
        /// 主题相关操作
        /// </summary>
        [RelayCommand]
        private void ThemeOperation(string operation)
        {
            StatusMessage = operation switch
            {
                "light" => "切换到浅色主题",
                "dark" => "切换到深色主题",
                "auto" => "切换到自动主题",
                _ => $"主题操作: {operation}"
            };
        }
    }
}
