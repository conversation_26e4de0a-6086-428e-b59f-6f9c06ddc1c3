<!-- GroupBox 高级用法示例 -->
<StackPanel Margin="20">

    <!-- 嵌套 GroupBox -->
    <GroupBox Header="嵌套分组示例" 
              Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
              BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
              BorderThickness="1"
              Margin="0,0,0,16">
        <StackPanel Margin="16">
            <TextBlock Text="这是父级分组的内容。" Margin="0,0,0,12"/>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <GroupBox Grid.Column="0" 
                          Header="子分组 A" 
                          Background="{DynamicResource ControlFillColorDefaultBrush}"
                          Margin="0,0,8,0">
                    <StackPanel Margin="12">
                        <RadioButton Content="选项 A1" GroupName="GroupA" IsChecked="True" Margin="0,0,0,4"/>
                        <RadioButton Content="选项 A2" GroupName="GroupA" Margin="0,0,0,4"/>
                        <RadioButton Content="选项 A3" GroupName="GroupA"/>
                    </StackPanel>
                </GroupBox>
                
                <GroupBox Grid.Column="1" 
                          Header="子分组 B" 
                          Background="{DynamicResource ControlFillColorDefaultBrush}"
                          Margin="8,0,0,0">
                    <StackPanel Margin="12">
                        <RadioButton Content="选项 B1" GroupName="GroupB" Margin="0,0,0,4"/>
                        <RadioButton Content="选项 B2" GroupName="GroupB" IsChecked="True" Margin="0,0,0,4"/>
                        <RadioButton Content="选项 B3" GroupName="GroupB"/>
                    </StackPanel>
                </GroupBox>
            </Grid>
        </StackPanel>
    </GroupBox>

    <!-- 自定义样式 GroupBox -->
    <GroupBox Header="自定义样式分组" Margin="0,0,0,16">
        <GroupBox.Style>
            <Style TargetType="GroupBox">
                <Setter Property="Background" Value="#FF2D3748"/>
                <Setter Property="Foreground" Value="White"/>
                <Setter Property="BorderBrush" Value="#FF4A5568"/>
                <Setter Property="BorderThickness" Value="2"/>
                <Setter Property="Padding" Value="12"/>
            </Style>
        </GroupBox.Style>
        <Grid Margin="16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <StackPanel Grid.Column="0" Margin="0,0,16,0">
                <TextBlock Text="配置选项" 
                           Foreground="White" 
                           FontWeight="Bold" 
                           Margin="0,0,0,12"/>
                <ComboBox Margin="0,0,0,8">
                    <ComboBoxItem Content="选项 1"/>
                    <ComboBoxItem Content="选项 2" IsSelected="True"/>
                    <ComboBoxItem Content="选项 3"/>
                </ComboBox>
                <Slider Minimum="0" Maximum="100" Value="60" Margin="0,0,0,8"/>
                <TextBlock Text="当前值: 60" 
                           Foreground="White" 
                           FontSize="12"/>
            </StackPanel>
            
            <StackPanel Grid.Column="1">
                <TextBlock Text="操作" 
                           Foreground="White" 
                           FontWeight="Bold" 
                           Margin="0,0,0,12"/>
                <Button Content="保存" Margin="0,0,0,8"/>
                <Button Content="重置"/>
            </StackPanel>
        </Grid>
    </GroupBox>

    <!-- 复杂表单 GroupBox -->
    <GroupBox Header="用户注册表单" 
              Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
              BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
              BorderThickness="1"
              Margin="0,0,0,16">
        <Grid Margin="16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- 基本信息 -->
            <TextBlock Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="4" 
                       Text="基本信息" 
                       FontWeight="Bold" 
                       FontSize="14" 
                       Margin="0,0,0,12"/>
            
            <TextBlock Grid.Row="1" Grid.Column="0" Text="用户名:" Margin="0,0,8,8" VerticalAlignment="Center"/>
            <TextBox Grid.Row="1" Grid.Column="1" Text="" Margin="0,0,16,8"/>
            
            <TextBlock Grid.Row="1" Grid.Column="2" Text="密码:" Margin="0,0,8,8" VerticalAlignment="Center"/>
            <PasswordBox Grid.Row="1" Grid.Column="3" Margin="0,0,0,8"/>
            
            <TextBlock Grid.Row="2" Grid.Column="0" Text="邮箱:" Margin="0,0,8,8" VerticalAlignment="Center"/>
            <TextBox Grid.Row="2" Grid.Column="1" Text="" Margin="0,0,16,8"/>
            
            <TextBlock Grid.Row="2" Grid.Column="2" Text="电话:" Margin="0,0,8,8" VerticalAlignment="Center"/>
            <TextBox Grid.Row="2" Grid.Column="3" Text="" Margin="0,0,0,8"/>
            
            <!-- 个人信息 -->
            <TextBlock Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="4" 
                       Text="个人信息" 
                       FontWeight="Bold" 
                       FontSize="14" 
                       Margin="0,12,0,12"/>
            
            <TextBlock Grid.Row="4" Grid.Column="0" Text="性别:" Margin="0,0,8,8" VerticalAlignment="Center"/>
            <StackPanel Grid.Row="4" Grid.Column="1" Orientation="Horizontal" Margin="0,0,16,8">
                <RadioButton Content="男" GroupName="Gender" IsChecked="True" Margin="0,0,16,0"/>
                <RadioButton Content="女" GroupName="Gender"/>
            </StackPanel>
            
            <TextBlock Grid.Row="4" Grid.Column="2" Text="年龄:" Margin="0,0,8,8" VerticalAlignment="Center"/>
            <TextBox Grid.Row="4" Grid.Column="3" Text="" Margin="0,0,0,8"/>
            
            <!-- 操作按钮 -->
            <StackPanel Grid.Row="5" Grid.Column="0" Grid.ColumnSpan="4" 
                        Orientation="Horizontal" 
                        HorizontalAlignment="Right" 
                        Margin="0,16,0,0">
                <Button Content="重置" Margin="0,0,8,0"/>
                <Button Content="注册"/>
            </StackPanel>
        </Grid>
    </GroupBox>

    <!-- 多级嵌套 GroupBox -->
    <GroupBox Header="多级嵌套示例" 
              Background="{DynamicResource ControlFillColorDefaultBrush}"
              BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
              BorderThickness="1">
        <StackPanel Margin="16">
            <TextBlock Text="第一级分组内容" Margin="0,0,0,12"/>
            
            <GroupBox Header="第二级分组" 
                      Background="{DynamicResource ControlFillColorSecondaryBrush}"
                      Margin="0,0,0,12">
                <StackPanel Margin="12">
                    <TextBlock Text="第二级分组内容" Margin="0,0,0,8"/>
                    
                    <GroupBox Header="第三级分组" 
                              Background="{DynamicResource CardBackgroundFillColorDefaultBrush}">
                        <StackPanel Margin="12">
                            <TextBlock Text="第三级分组内容"/>
                            <CheckBox Content="深层选项" Margin="0,8,0,0"/>
                        </StackPanel>
                    </GroupBox>
                </StackPanel>
            </GroupBox>
            
            <Button Content="提交所有设置" HorizontalAlignment="Left"/>
        </StackPanel>
    </GroupBox>

</StackPanel>
