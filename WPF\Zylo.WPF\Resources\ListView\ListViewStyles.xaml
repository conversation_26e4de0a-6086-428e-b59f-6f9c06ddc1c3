<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml">

    <!-- ListView 基础样式 -->
    <Style x:Key="ListViewBaseStyle" TargetType="ListView">
        <Setter Property="Background" Value="{DynamicResource ApplicationBackgroundBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ControlElevationBorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Auto"/>
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto"/>
        <Setter Property="ScrollViewer.CanContentScroll" Value="True"/>
        <Setter Property="VirtualizingPanel.IsVirtualizing" Value="True"/>
        <Setter Property="VirtualizingPanel.VirtualizationMode" Value="Recycling"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ListView">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4">
                        <ScrollViewer Padding="{TemplateBinding Padding}"
                                      HorizontalScrollBarVisibility="{TemplateBinding ScrollViewer.HorizontalScrollBarVisibility}"
                                      VerticalScrollBarVisibility="{TemplateBinding ScrollViewer.VerticalScrollBarVisibility}"
                                      CanContentScroll="{TemplateBinding ScrollViewer.CanContentScroll}">
                            <ItemsPresenter/>
                        </ScrollViewer>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>

        <!-- 添加鼠标悬停和选择效果 -->
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorSecondaryBrush}"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Opacity" Value="0.5"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 🎨 简洁现代ListView项目样式 - 极简设计 -->
    <Style x:Key="CleanListViewItemStyle" TargetType="ListViewItem">
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Margin" Value="0,0,0,1" />
        <Setter Property="Padding" Value="6,4" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ListViewItem">
                    <Border x:Name="Border"
                            Background="Transparent"
                            BorderThickness="0"
                            CornerRadius="4">
                        <Grid>
                            <ContentPresenter x:Name="PART_ContentPresenter"
                                              Margin="{TemplateBinding Padding}"
                                              HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              VerticalAlignment="{TemplateBinding VerticalContentAlignment}" />

                            <!-- 极简选中指示器 - 左侧细线 -->
                            <Rectangle x:Name="SelectionIndicator"
                                       Width="2"
                                       HorizontalAlignment="Left"
                                       VerticalAlignment="Stretch"
                                       Fill="{DynamicResource SystemAccentColorPrimaryBrush}"
                                       Opacity="0" />
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <!-- 悬停效果 - 极其微妙 -->
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Border" Property="Background">
                                <Setter.Value>
                                    <SolidColorBrush Color="{DynamicResource SystemAccentColor}" Opacity="0.05"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>

                        <!-- 选中效果 - 简洁指示器 -->
                        <Trigger Property="IsSelected" Value="True">
                            <Setter TargetName="SelectionIndicator" Property="Opacity" Value="1" />
                            <Setter TargetName="Border" Property="Background">
                                <Setter.Value>
                                    <SolidColorBrush Color="{DynamicResource SystemAccentColor}" Opacity="0.08"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 🎯 WPFUI标准ListView项目样式 - 完全符合WPFUI设计规范 -->
    <Style x:Key="WpfUIListViewItemStyle" TargetType="ListViewItem">
        <Setter Property="Foreground" Value="{DynamicResource ListViewItemForeground}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Margin" Value="0,0,0,2" />
        <Setter Property="Padding" Value="4" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ListViewItem">
                    <Border x:Name="Border"
                            Margin="0"
                            Padding="0"
                            Background="Transparent"
                            BorderThickness="0"
                            CornerRadius="4">
                        <Grid>
                            <!-- 内容展示器 -->
                            <ContentPresenter x:Name="PART_ContentPresenter"
                                              Margin="{TemplateBinding Padding}"
                                              HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              VerticalAlignment="{TemplateBinding VerticalContentAlignment}" />

                            <!-- 左侧选中指示器 - WPFUI标准样式 -->
                            <Rectangle x:Name="ActiveRectangle"
                                       Width="3"
                                       Height="18"
                                       Margin="0"
                                       HorizontalAlignment="Left"
                                       VerticalAlignment="Center"
                                       Fill="{DynamicResource ListViewItemPillFillBrush}"
                                       RadiusX="2"
                                       RadiusY="2"
                                       Visibility="Collapsed" />
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <!-- 鼠标悬停效果 -->
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsEnabled" Value="True" />
                                <Condition Property="IsMouseOver" Value="True" />
                            </MultiTrigger.Conditions>
                            <Setter TargetName="Border" Property="Background" Value="{DynamicResource ListViewItemBackgroundPointerOver}" />
                        </MultiTrigger>

                        <!-- 选中效果 -->
                        <Trigger Property="IsSelected" Value="True">
                            <Setter TargetName="ActiveRectangle" Property="Visibility" Value="Visible" />
                            <Setter TargetName="Border" Property="Background" Value="{DynamicResource ListViewItemBackgroundPointerOver}" />
                        </Trigger>

                        <!-- 禁用状态 -->
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" Value="0.5" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 现代化 ListViewItem 样式 -->
    <Style x:Key="ModernListViewItemStyle" TargetType="ListViewItem">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="0"/>
        <Setter Property="Margin" Value="0"/>
        <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
        <Setter Property="VerticalContentAlignment" Value="Stretch"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ListViewItem">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter x:Name="PART_ContentPresenter"
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <!-- 鼠标悬停效果 - 使用WPFUI标准悬停背景色 -->
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{DynamicResource ListViewItemBackgroundPointerOver}"/>
                        </Trigger>
                        <!-- 选中效果 - 使用WPFUI标准选中指示器颜色 -->
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="BorderBrush" Value="{DynamicResource ListViewItemPillFillBrush}"/>
                            <Setter Property="BorderThickness" Value="4,0,0,0"/>
                            <Setter TargetName="PART_ContentPresenter" Property="Margin" Value="4,0,0,0"/>
                            <Setter Property="Background" Value="{DynamicResource ListViewItemBackgroundPointerOver}"/>
                        </Trigger>
                        <!-- 选中且鼠标悬停 - 保持悬停背景 + 选中左边框 -->
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="True"/>
                                <Condition Property="IsMouseOver" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Background" Value="{DynamicResource ListViewItemBackgroundPointerOver}"/>
                            <Setter Property="BorderBrush" Value="{DynamicResource ListViewItemPillFillBrush}"/>
                            <Setter Property="BorderThickness" Value="4,0,0,0"/>
                            <Setter TargetName="PART_ContentPresenter" Property="Margin" Value="4,0,0,0"/>
                        </MultiTrigger>
                        <!-- 禁用状态 -->
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" Value="0.5"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 🎨 简洁现代ListView样式 - 极简设计 -->
    <Style x:Key="CleanListViewStyle" TargetType="ListView" BasedOn="{StaticResource ListViewBaseStyle}">
        <Setter Property="ItemContainerStyle" Value="{StaticResource CleanListViewItemStyle}"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="0"/>
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Disabled"/>
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto"/>
    </Style>

    <!-- 🎯 WPFUI标准ListView样式 - 完全符合WPFUI设计规范 -->
    <Style x:Key="WpfUIListViewStyle" TargetType="ListView" BasedOn="{StaticResource ListViewBaseStyle}">
        <Setter Property="ItemContainerStyle" Value="{StaticResource WpfUIListViewItemStyle}"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
        <Setter Property="Padding" Value="0"/>
    </Style>

    <!-- ListView 标准样式 -->
    <Style x:Key="ListViewStyle" TargetType="ListView" BasedOn="{StaticResource ListViewBaseStyle}">
        <!-- 应用现代化的 ListViewItem 样式 -->
        <Setter Property="ItemContainerStyle" Value="{StaticResource ModernListViewItemStyle}"/>
    </Style>

    <!-- ListView 小型样式 -->
    <Style x:Key="SmallListViewStyle" TargetType="ListView" BasedOn="{StaticResource ListViewBaseStyle}">
        <Setter Property="Padding" Value="6"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="ItemContainerStyle" Value="{StaticResource ModernListViewItemStyle}"/>
    </Style>

    <!-- ListView 大型样式 -->
    <Style x:Key="LargeListViewStyle" TargetType="ListView" BasedOn="{StaticResource ListViewBaseStyle}">
        <Setter Property="Padding" Value="12"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="ItemContainerStyle" Value="{StaticResource ModernListViewItemStyle}"/>
    </Style>

    <!-- ListView 透明样式 -->
    <Style x:Key="TransparentListViewStyle" TargetType="ListView" BasedOn="{StaticResource ListViewBaseStyle}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
        <Setter Property="ItemContainerStyle" Value="{StaticResource ModernListViewItemStyle}"/>
    </Style>

    <!-- ListView 强调色样式 -->
    <Style x:Key="AccentListViewStyle" TargetType="ListView" BasedOn="{StaticResource ListViewBaseStyle}">
        <Setter Property="ItemContainerStyle" Value="{StaticResource ModernListViewItemStyle}"/>
    </Style>

    <!-- ListView 紧凑样式 -->
    <Style x:Key="CompactListViewStyle" TargetType="ListView" BasedOn="{StaticResource ListViewBaseStyle}">
        <Setter Property="Padding" Value="2"/>
        <Setter Property="FontSize" Value="11"/>
        <Setter Property="ItemContainerStyle" Value="{StaticResource ModernListViewItemStyle}"/>
    </Style>

    <!-- ListView 现代化样式 -->
    <Style x:Key="ModernListViewStyle" TargetType="ListView" BasedOn="{StaticResource ListViewBaseStyle}">
        <Setter Property="Background" Value="{DynamicResource CardBackgroundFillColorDefaultBrush}"/>
        <Setter Property="Padding" Value="12"/>
        <Setter Property="FontSize" Value="15"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="ItemContainerStyle" Value="{StaticResource ModernListViewItemStyle}"/>
    </Style>

    <!-- ListView 卡片式样式 -->
    <Style x:Key="CardListViewStyle" TargetType="ListView" BasedOn="{StaticResource ListViewBaseStyle}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="ItemContainerStyle" Value="{StaticResource ModernListViewItemStyle}"/>
    </Style>

    <!-- 🎨 ListView 网格式样式 - 现代化网格布局 -->
    <Style x:Key="GridListViewStyle" TargetType="ListView" BasedOn="{StaticResource ListViewBaseStyle}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="4"/>
        <Setter Property="ItemsPanel">
            <Setter.Value>
                <ItemsPanelTemplate>
                    <UniformGrid Columns="5" Rows="2"/>
                </ItemsPanelTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="ItemContainerStyle">
            <Setter.Value>
                <Style TargetType="ListViewItem">
                    <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}" />
                    <Setter Property="Background" Value="Transparent" />
                    <Setter Property="Margin" Value="2" />
                    <Setter Property="Padding" Value="0" />
                    <Setter Property="BorderThickness" Value="0" />
                    <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                    <Setter Property="VerticalContentAlignment" Value="Stretch" />
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="ListViewItem">
                                <Border x:Name="Border"
                                        Background="Transparent"
                                        BorderThickness="0"
                                        CornerRadius="4">
                                    <ContentPresenter x:Name="PART_ContentPresenter"
                                                      Margin="{TemplateBinding Padding}"
                                                      HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                      VerticalAlignment="{TemplateBinding VerticalContentAlignment}" />
                                </Border>
                                <ControlTemplate.Triggers>
                                    <!-- 悬停效果 -->
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter TargetName="Border" Property="Background">
                                            <Setter.Value>
                                                <SolidColorBrush Color="{DynamicResource SystemAccentColor}" Opacity="0.08"/>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger>

                                    <!-- 选中效果 -->
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter TargetName="Border" Property="Background">
                                            <Setter.Value>
                                                <SolidColorBrush Color="{DynamicResource SystemAccentColor}" Opacity="0.12"/>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- ListView 成功样式 -->
    <Style x:Key="SuccessListViewStyle" TargetType="ListView" BasedOn="{StaticResource ListViewBaseStyle}">
        <Setter Property="ItemContainerStyle" Value="{StaticResource ModernListViewItemStyle}"/>
    </Style>

    <!-- ListView 警告样式 -->
    <Style x:Key="WarningListViewStyle" TargetType="ListView" BasedOn="{StaticResource ListViewBaseStyle}">
        <Setter Property="ItemContainerStyle" Value="{StaticResource ModernListViewItemStyle}"/>
    </Style>

    <!-- ListView 危险样式 -->
    <Style x:Key="DangerListViewStyle" TargetType="ListView" BasedOn="{StaticResource ListViewBaseStyle}">
        <Setter Property="ItemContainerStyle" Value="{StaticResource ModernListViewItemStyle}"/>
    </Style>

    <!-- ListView 密集样式 - 紧密排列，无间距 -->
    <Style x:Key="DenseListViewStyle" TargetType="ListView" BasedOn="{StaticResource ListViewBaseStyle}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="0"/>
        <Setter Property="ItemContainerStyle">
            <Setter.Value>
                <Style TargetType="ListViewItem">
                    <Setter Property="Background" Value="Transparent"/>
                    <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
                    <Setter Property="BorderThickness" Value="0"/>
                    <Setter Property="Padding" Value="8,2"/>
                    <!-- 密集样式：最小上下间距，左右保持基本间距 -->
                    <Setter Property="Margin" Value="0"/>
                    <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                    <Setter Property="VerticalContentAlignment" Value="Stretch"/>
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="ListViewItem">
                                <Border Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}"
                                        Padding="{TemplateBinding Padding}">
                                    <ContentPresenter x:Name="PART_ContentPresenter"
                                                      HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                      VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                                </Border>

                                <ControlTemplate.Triggers>
                                    <!-- 鼠标悬停效果 - 使用浅强调色背景 -->
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background">
                                            <Setter.Value>
                                                <SolidColorBrush Color="{DynamicResource AccentFillColorDefault}" Opacity="0.15"/>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger>
                                    <!-- 选中效果 - 使用左边粗边框线，文字后移 -->
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter Property="Background">
                                            <Setter.Value>
                                                <SolidColorBrush Color="{DynamicResource AccentFillColorDefault}" Opacity="0.08"/>
                                            </Setter.Value>
                                        </Setter>
                                        <Setter Property="BorderBrush" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
                                        <Setter Property="BorderThickness" Value="4,0,0,0"/>
                                        <Setter TargetName="PART_ContentPresenter" Property="Margin" Value="4,0,0,0"/>
                                    </Trigger>
                                    <!-- 选中且鼠标悬停 - 保持悬停背景 + 选中左边框 -->
                                    <MultiTrigger>
                                        <MultiTrigger.Conditions>
                                            <Condition Property="IsSelected" Value="True"/>
                                            <Condition Property="IsMouseOver" Value="True"/>
                                        </MultiTrigger.Conditions>
                                        <Setter Property="Background">
                                            <Setter.Value>
                                                <SolidColorBrush Color="{DynamicResource AccentFillColorDefault}" Opacity="0.20"/>
                                            </Setter.Value>
                                        </Setter>
                                        <Setter Property="BorderBrush" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
                                        <Setter Property="BorderThickness" Value="4,0,0,0"/>
                                        <Setter TargetName="PART_ContentPresenter" Property="Margin" Value="4,0,0,0"/>
                                    </MultiTrigger>
                                    <!-- 禁用状态 -->
                                    <Trigger Property="IsEnabled" Value="False">
                                        <Setter Property="Opacity" Value="0.5"/>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- ListView 密集边框样式 - 紧密排列，带分隔边框 -->
    <Style x:Key="DenseBorderedListViewStyle" TargetType="ListView" BasedOn="{StaticResource ListViewBaseStyle}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="0"/>
        <Setter Property="ItemContainerStyle">
            <Setter.Value>
                <Style TargetType="ListViewItem">
                    <Setter Property="Background" Value="Transparent"/>
                    <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
                    <Setter Property="BorderThickness" Value="0,0,0,2"/>
                    <Setter Property="Padding" Value="8,2"/>
                    <Setter Property="Margin" Value="0"/>
                    <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                    <Setter Property="VerticalContentAlignment" Value="Stretch"/>
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="ListViewItem">
                                <Border Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}"
                                        Padding="{TemplateBinding Padding}">
                                    <ContentPresenter x:Name="PART_ContentPresenter"
                                                      HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                      VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                                </Border>

                                <ControlTemplate.Triggers>
                                    <!-- 鼠标悬停效果 - 使用浅强调色背景 -->
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background">
                                            <Setter.Value>
                                                <SolidColorBrush Color="{DynamicResource AccentFillColorDefault}" Opacity="0.15"/>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger>
                                    <!-- 选中效果 - 使用左边粗边框线，保持底边框，文字后移 -->
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter Property="Background">
                                            <Setter.Value>
                                                <SolidColorBrush Color="{DynamicResource AccentFillColorDefault}" Opacity="0.08"/>
                                            </Setter.Value>
                                        </Setter>
                                        <Setter Property="BorderBrush" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
                                        <Setter Property="BorderThickness" Value="4,0,0,1"/>
                                        <Setter TargetName="PART_ContentPresenter" Property="Margin" Value="4,0,0,0"/>
                                    </Trigger>
                                    <!-- 选中且鼠标悬停 -->
                                    <MultiTrigger>
                                        <MultiTrigger.Conditions>
                                            <Condition Property="IsSelected" Value="True"/>
                                            <Condition Property="IsMouseOver" Value="True"/>
                                        </MultiTrigger.Conditions>
                                        <Setter Property="Background">
                                            <Setter.Value>
                                                <SolidColorBrush Color="{DynamicResource AccentFillColorDefault}" Opacity="0.20"/>
                                            </Setter.Value>
                                        </Setter>
                                        <Setter Property="BorderBrush" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
                                        <Setter Property="BorderThickness" Value="4,0,0,1"/>
                                        <Setter TargetName="PART_ContentPresenter" Property="Margin" Value="4,0,0,0"/>
                                    </MultiTrigger>
                                    <!-- 禁用状态 -->
                                    <Trigger Property="IsEnabled" Value="False">
                                        <Setter Property="Opacity" Value="0.5"/>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- ListView 密集完整边框样式 - 紧密排列，完整边框包围 -->
    <Style x:Key="DenseFullBorderedListViewStyle" TargetType="ListView" BasedOn="{StaticResource ListViewBaseStyle}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="0"/>
        <Setter Property="ItemContainerStyle">
            <Setter.Value>
                <Style TargetType="ListViewItem">
                    <Setter Property="Background" Value="Transparent"/>
                    <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
                    <Setter Property="BorderThickness" Value="0,0,0,1"/>
                    <Setter Property="Padding" Value="8,2"/>
                    <Setter Property="Margin" Value="0"/>
                    <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                    <Setter Property="VerticalContentAlignment" Value="Stretch"/>
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="ListViewItem">
                                <Border Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}"
                                        Padding="{TemplateBinding Padding}">
                                    <ContentPresenter x:Name="PART_ContentPresenter"
                                                      HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                      VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                                </Border>

                                <ControlTemplate.Triggers>
                                    <!-- 鼠标悬停效果 - 使用浅强调色背景 -->
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background">
                                            <Setter.Value>
                                                <SolidColorBrush Color="{DynamicResource AccentFillColorDefault}" Opacity="0.15"/>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger>
                                    <!-- 选中效果 - 使用左边粗边框线，保持底边框，文字后移 -->
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter Property="Background">
                                            <Setter.Value>
                                                <SolidColorBrush Color="{DynamicResource AccentFillColorDefault}" Opacity="0.08"/>
                                            </Setter.Value>
                                        </Setter>
                                        <Setter Property="BorderBrush" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
                                        <Setter Property="BorderThickness" Value="4,0,0,1"/>
                                        <Setter TargetName="PART_ContentPresenter" Property="Margin" Value="4,0,0,0"/>
                                    </Trigger>
                                    <!-- 选中且鼠标悬停 -->
                                    <MultiTrigger>
                                        <MultiTrigger.Conditions>
                                            <Condition Property="IsSelected" Value="True"/>
                                            <Condition Property="IsMouseOver" Value="True"/>
                                        </MultiTrigger.Conditions>
                                        <Setter Property="Background">
                                            <Setter.Value>
                                                <SolidColorBrush Color="{DynamicResource AccentFillColorDefault}" Opacity="0.20"/>
                                            </Setter.Value>
                                        </Setter>
                                        <Setter Property="BorderBrush" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
                                        <Setter Property="BorderThickness" Value="4,0,0,1"/>
                                        <Setter TargetName="PART_ContentPresenter" Property="Margin" Value="4,0,0,0"/>
                                    </MultiTrigger>
                                    <!-- 禁用状态 -->
                                    <Trigger Property="IsEnabled" Value="False">
                                        <Setter Property="Opacity" Value="0.5"/>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- ListView 边框样式 - 标准间距，完整边框包围 -->
    <Style x:Key="BorderedListViewStyle" TargetType="ListView" BasedOn="{StaticResource ListViewBaseStyle}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="ItemContainerStyle">
            <Setter.Value>
                <Style TargetType="ListViewItem">
                    <Setter Property="Background" Value="Transparent"/>
                    <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
                    <Setter Property="BorderThickness" Value="0,0,0,1"/>
                    <Setter Property="Padding" Value="12,8"/>
                    <Setter Property="Margin" Value="2"/>
                    <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                    <Setter Property="VerticalContentAlignment" Value="Stretch"/>
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="ListViewItem">
                                <Border Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}"
                                        Padding="{TemplateBinding Padding}">
                                    <ContentPresenter x:Name="PART_ContentPresenter"
                                                      HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                      VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                                </Border>

                                <ControlTemplate.Triggers>
                                    <!-- 鼠标悬停效果 -->
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background">
                                            <Setter.Value>
                                                <SolidColorBrush Color="{DynamicResource AccentFillColorDefault}" Opacity="0.15"/>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger>
                                    <!-- 选中效果 -->
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter Property="Background">
                                            <Setter.Value>
                                                <SolidColorBrush Color="{DynamicResource AccentFillColorDefault}" Opacity="0.08"/>
                                            </Setter.Value>
                                        </Setter>
                                        <Setter Property="BorderBrush" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
                                        <Setter Property="BorderThickness" Value="4,0,0,1"/>
                                        <Setter TargetName="PART_ContentPresenter" Property="Margin" Value="4,0,0,0"/>
                                    </Trigger>
                                    <!-- 选中且鼠标悬停 -->
                                    <MultiTrigger>
                                        <MultiTrigger.Conditions>
                                            <Condition Property="IsSelected" Value="True"/>
                                            <Condition Property="IsMouseOver" Value="True"/>
                                        </MultiTrigger.Conditions>
                                        <Setter Property="Background">
                                            <Setter.Value>
                                                <SolidColorBrush Color="{DynamicResource AccentFillColorDefault}" Opacity="0.20"/>
                                            </Setter.Value>
                                        </Setter>
                                        <Setter Property="BorderBrush" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
                                        <Setter Property="BorderThickness" Value="4,0,0,1"/>
                                        <Setter TargetName="PART_ContentPresenter" Property="Margin" Value="4,0,0,0"/>
                                    </MultiTrigger>
                                    <!-- 禁用状态 -->
                                    <Trigger Property="IsEnabled" Value="False">
                                        <Setter Property="Opacity" Value="0.5"/>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- ListView 瓷砖拼接样式 -->
    <Style x:Key="TileListViewStyle" TargetType="ListView" BasedOn="{StaticResource ListViewBaseStyle}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="ItemsPanel">
            <Setter.Value>
                <ItemsPanelTemplate>
                    <WrapPanel Orientation="Horizontal" ItemWidth="200" ItemHeight="120"/>
                </ItemsPanelTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="ItemContainerStyle">
            <Setter.Value>
                <Style TargetType="ListViewItem">
                    <Setter Property="Background" Value="Transparent"/>
                    <Setter Property="BorderBrush" Value="Transparent"/>
                    <Setter Property="BorderThickness" Value="0"/>
                    <Setter Property="Padding" Value="4"/>
                    <Setter Property="Margin" Value="4"/>
                    <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                    <Setter Property="VerticalContentAlignment" Value="Stretch"/>
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="ListViewItem">
                                <Border x:Name="PART_Border"
                                        Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                                        BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                        BorderThickness="1"
                                        CornerRadius="4"
                                        Padding="12"
                                        Margin="0">
                                    <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                      VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>

                                    <!-- 瓷砖效果阴影 -->
                                    <Border.Effect>
                                        <DropShadowEffect Color="{DynamicResource ControlStrokeColorDefault}"
                                                          BlurRadius="8"
                                                          ShadowDepth="2"
                                                          Opacity="0.1"/>
                                    </Border.Effect>
                                </Border>

                                <ControlTemplate.Triggers>
                                    <!-- 鼠标悬停效果 - 使用浅强调色背景 -->
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter TargetName="PART_Border" Property="Background">
                                            <Setter.Value>
                                                <SolidColorBrush Color="{DynamicResource AccentFillColorDefault}" Opacity="0.15"/>
                                            </Setter.Value>
                                        </Setter>
                                        <Setter TargetName="PART_Border" Property="Effect">
                                            <Setter.Value>
                                                <DropShadowEffect Color="Gray"
                                                                  BlurRadius="12"
                                                                  ShadowDepth="4"
                                                                  Opacity="0.2"/>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger>

                                    <!-- 选中效果 - 使用左边粗边框和阴影，增加左边距 -->
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter TargetName="PART_Border" Property="BorderBrush" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
                                        <Setter TargetName="PART_Border" Property="BorderThickness" Value="1,1,1,1"/>
                                        <Setter TargetName="PART_Border" Property="Padding" Value="16,12,12,12"/>
                                        <Setter TargetName="PART_Border" Property="Effect">
                                            <Setter.Value>
                                                <DropShadowEffect Color="{DynamicResource AccentFillColorDefault}"
                                                                  BlurRadius="16"
                                                                  ShadowDepth="4"
                                                                  Opacity="0.3"/>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger>

                                    <!-- 选中且鼠标悬停 - 保持悬停背景 + 选中左边框和阴影 -->
                                    <MultiTrigger>
                                        <MultiTrigger.Conditions>
                                            <Condition Property="IsSelected" Value="True"/>
                                            <Condition Property="IsMouseOver" Value="True"/>
                                        </MultiTrigger.Conditions>
                                        <Setter TargetName="PART_Border" Property="Background">
                                            <Setter.Value>
                                                <SolidColorBrush Color="{DynamicResource AccentFillColorDefault}" Opacity="0.15"/>
                                            </Setter.Value>
                                        </Setter>
                                        <Setter TargetName="PART_Border" Property="BorderBrush" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
                                        <Setter TargetName="PART_Border" Property="BorderThickness" Value="1,1,1,1"/>
                                        <Setter TargetName="PART_Border" Property="Padding" Value="16,12,12,12"/>
                                    </MultiTrigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
