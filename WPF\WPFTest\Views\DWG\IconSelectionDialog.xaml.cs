using System.ComponentModel;
using System.Globalization;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using WPFTest.Models.DWG;

namespace WPFTest.Views.DWG;

/// <summary>
/// 图标选择对话框
/// </summary>
public partial class IconSelectionDialog : Window, INotifyPropertyChanged
{
    private string _selectedIcon = "📁";
    private string _selectedIconCategory = "全部";

    /// <summary>
    /// 选中的图标
    /// </summary>
    public string SelectedIcon
    {
        get => _selectedIcon;
        set
        {
            if (_selectedIcon != value)
            {
                _selectedIcon = value;
                OnPropertyChanged();
            }
        }
    }

    /// <summary>
    /// 选中的图标分类
    /// </summary>
    public string SelectedIconCategory
    {
        get => _selectedIconCategory;
        set
        {
            if (_selectedIconCategory != value)
            {
                _selectedIconCategory = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(FilteredIcons));
            }
        }
    }

    /// <summary>
    /// 可用的图标选项列表
    /// </summary>
    public List<IconOption> AvailableIcons { get; } = IconOption.GetDefaultIcons();

    /// <summary>
    /// 图标分类列表
    /// </summary>
    public List<string> IconCategories { get; } = new List<string> { "全部" }.Concat(IconOption.GetCategories()).ToList();

    /// <summary>
    /// 过滤后的图标列表
    /// </summary>
    public IEnumerable<IconOption> FilteredIcons
    {
        get
        {
            if (SelectedIconCategory == "全部" || string.IsNullOrEmpty(SelectedIconCategory))
                return AvailableIcons;
            
            return AvailableIcons.Where(icon => icon.Category == SelectedIconCategory);
        }
    }

    public IconSelectionDialog()
    {
        InitializeComponent();
        DataContext = this;
    }

    /// <summary>
    /// 图标点击事件
    /// </summary>
    private void OnIconClick(object sender, RoutedEventArgs e)
    {
        if (sender is System.Windows.Controls.Button button && button.Tag is string icon)
        {
            SelectedIcon = icon;
            UpdateButtonStyles(); // 更新按钮样式
        }
    }

    /// <summary>
    /// 更新按钮样式以显示选中状态
    /// </summary>
    private void UpdateButtonStyles()
    {
        // 这里可以通过代码更新按钮样式，但为了简化，我们先不实现
        // 用户可以通过顶部的预览区域看到选中的图标
    }

    /// <summary>
    /// 确定按钮点击事件
    /// </summary>
    private void OnOkClick(object sender, RoutedEventArgs e)
    {
        DialogResult = true;
        Close();
    }

    /// <summary>
    /// 取消按钮点击事件
    /// </summary>
    private void OnCancelClick(object sender, RoutedEventArgs e)
    {
        DialogResult = false;
        Close();
    }

    #region INotifyPropertyChanged

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    #endregion
}
