# 📖 Zylo.YData API 参考文档

本文档详细介绍了 Zylo.YData 的所有公共 API，包括方法签名、参数说明、返回值和使用示例。

## 🔥 核心静态 API

### YData 类

`YData` 是 Zylo.YData 的主要入口点，提供静态方法访问所有核心功能。

#### 配置方法

##### ConfigureAuto

自动配置数据库连接。

```csharp
// 方法签名
public static void ConfigureAuto(string connectionString)
public static void ConfigureAuto(string connectionString, YDataType dataType)

// 使用示例
YData.ConfigureAuto("Data Source=myapp.db");
YData.ConfigureAuto("Server=localhost;Database=MyApp;Trusted_Connection=true;", YDataType.SqlServer);
```

**参数说明：**

- `connectionString`: 数据库连接字符串
- `dataType`: 数据库类型（可选，会自动检测）

##### Configure

使用详细配置选项配置数据库。

```csharp
// 方法签名
public static void Configure(Action<YDataOptions> configureOptions)

// 使用示例
YData.Configure(options =>
{
    options.ConnectionString = "Data Source=myapp.db";
    options.DataType = YDataType.Sqlite;
    options.EnableAutoSyncStructure = true;
    options.DefaultQueryTimeout = TimeSpan.FromSeconds(60);
});
```

#### 基础 CRUD 方法

##### GetAsync

根据主键获取单个实体。

```csharp
// 方法签名
public static Task<T?> GetAsync<T>(object id) where T : class

// 使用示例
var user = await YData.GetAsync<User>(1);
var product = await YData.GetAsync<Product>("P001");
```

##### GetAllAsync

获取所有实体。

```csharp
// 方法签名
public static Task<List<T>> GetAllAsync<T>() where T : class

// 使用示例
var allUsers = await YData.GetAllAsync<User>();
```

⚠️ **注意：** 此方法会加载表中所有数据，大表慎用。

##### InsertAsync

插入单个实体。

```csharp
// 方法签名
public static Task<int> InsertAsync<T>(T entity) where T : class

// 使用示例
var user = new User { Name = "张三", Email = "<EMAIL>" };
var affectedRows = await YData.InsertAsync(user);
Console.WriteLine($"插入成功，用户ID: {user.Id}");
```

##### UpdateAsync

更新单个实体。

```csharp
// 方法签名
public static Task<int> UpdateAsync<T>(T entity) where T : class

// 使用示例
var user = await YData.GetAsync<User>(1);
user.Age = 26;
var affectedRows = await YData.UpdateAsync(user);
```

##### DeleteAsync

根据主键删除实体。

```csharp
// 方法签名
public static Task<int> DeleteAsync<T>(object id) where T : class

// 使用示例
var affectedRows = await YData.DeleteAsync<User>(1);
```

#### 查询构建方法

##### Select

创建查询构建器。

```csharp
// 方法签名
public static ISelect<T> Select<T>() where T : class

// 使用示例
var query = YData.Select<User>()
    .Where(u => u.IsActive)
    .Where(u => u.Age >= 18)
    .OrderBy(u => u.Name);

var users = await query.ToListAsync();
```

##### Insert

创建插入构建器。

```csharp
// 方法签名
public static IInsert<T> Insert<T>() where T : class

// 使用示例
var users = new[] { /* 用户数组 */ };
var affectedRows = await YData.Insert<User>()
    .AppendData(users)
    .ExecuteAffrowsAsync();
```

##### Update

创建更新构建器。

```csharp
// 方法签名
public static IUpdate<T> Update<T>() where T : class

// 使用示例
var affectedRows = await YData.Update<User>()
    .Set(u => u.IsActive, false)
    .Where(u => u.Age > 65)
    .ExecuteAffrowsAsync();
```

##### Delete

创建删除构建器。

```csharp
// 方法签名
public static IDelete<T> Delete<T>() where T : class

// 使用示例
var affectedRows = await YData.Delete<User>()
    .Where(u => !u.IsActive)
    .Where(u => u.CreateTime < DateTime.Now.AddYears(-1))
    .ExecuteAffrowsAsync();
```

#### 分页查询方法

##### GetPagedAsync

执行分页查询。

```csharp
// 方法签名
public static Task<PagedResult<T>> GetPagedAsync<T>(
    int pageIndex, 
    int pageSize, 
    Expression<Func<T, bool>>? where = null) where T : class

// 使用示例
var result = await YData.GetPagedAsync<User>(1, 20, u => u.IsActive);
Console.WriteLine($"第 {result.PageIndex}/{result.TotalPages} 页");
Console.WriteLine($"共 {result.TotalCount} 条记录");
```

#### 事务方法

##### TransactionAsync

执行事务操作。

```csharp
// 方法签名
public static Task<TResult> TransactionAsync<TResult>(Func<Task<TResult>> operation)
public static Task TransactionAsync(Func<Task> operation)

// 使用示例
var result = await YData.TransactionAsync(async () =>
{
    var user = new User { Name = "张三", Email = "<EMAIL>" };
    await YData.InsertAsync(user);
    
    var profile = new UserProfile { UserId = user.Id, Bio = "这是简介" };
    await YData.InsertAsync(profile);
    
    return user.Id;
});
```

## 🗄️ 多数据库管理 API

### YDataManager 类

通过 `YData.Manager` 访问多数据库管理功能。

#### RegisterDatabase

注册数据库。

```csharp
// 方法签名
public static void RegisterDatabase(string name, string connectionString, YDataType dataType)
public static void RegisterDatabase(string name, YDataOptions options)

// 使用示例
YData.Manager.RegisterDatabase("main", "Data Source=main.db", YDataType.Sqlite);
YData.Manager.RegisterDatabase("logs", new YDataOptions
{
    ConnectionString = "Data Source=logs.db",
    DataType = YDataType.Sqlite,
    EnableMonitorCommand = false
});
```

#### GetDatabase

获取指定数据库的上下文。

```csharp
// 方法签名
public static IYDataContext GetDatabase(string name)

// 使用示例
var mainDb = YData.Manager.GetDatabase("main");
var users = await mainDb.Select<User>().ToListAsync();
```

#### SetDefaultDatabase

设置默认数据库。

```csharp
// 方法签名
public static void SetDefaultDatabase(string name)

// 使用示例
YData.Manager.SetDefaultDatabase("main");
```

#### GetDatabaseNames

获取所有已注册的数据库名称。

```csharp
// 方法签名
public static IEnumerable<string> GetDatabaseNames()

// 使用示例
var dbNames = YData.Manager.GetDatabaseNames();
foreach (var name in dbNames)
{
    Console.WriteLine($"数据库: {name}");
}
```

#### TestConnectionAsync

测试数据库连接。

```csharp
// 方法签名
public static Task<bool> TestConnectionAsync(string name)

// 使用示例
var isConnected = await YData.Manager.TestConnectionAsync("main");
if (isConnected)
{
    Console.WriteLine("数据库连接正常");
}
```

## 🔧 扩展方法 API

### 查询扩展方法

#### WhereIf

条件查询扩展。

```csharp
// 方法签名
public static ISelect<T> WhereIf<T>(this ISelect<T> select, bool condition, Expression<Func<T, bool>> predicate)

// 使用示例
string? nameFilter = GetNameFilter();
int? minAge = GetMinAge();

var users = await YData.Select<User>()
    .WhereIf(!string.IsNullOrEmpty(nameFilter), u => u.Name.Contains(nameFilter))
    .WhereIf(minAge.HasValue, u => u.Age >= minAge.Value)
    .ToListAsync();
```

#### ToPagedResultAsync

分页查询扩展。

```csharp
// 方法签名
public static Task<PagedResult<T>> ToPagedResultAsync<T>(this ISelect<T> select, int pageIndex, int pageSize)

// 使用示例
var result = await YData.Select<User>()
    .Where(u => u.IsActive)
    .OrderBy(u => u.Name)
    .ToPagedResultAsync(1, 20);
```

### 聚合查询扩展

#### YCountAsync

计数查询。

```csharp
// 方法签名
public static Task<long> YCountAsync<T>(this ISelect<T> select, Expression<Func<T, bool>>? predicate = null)

// 使用示例
var totalUsers = await YData.Select<User>().YCountAsync();
var activeUsers = await YData.Select<User>().YCountAsync(u => u.IsActive);
```

#### YSumAsync

求和查询。

```csharp
// 方法签名
public static Task<decimal> YSumAsync<T>(this ISelect<T> select, Expression<Func<T, object>> selector)

// 使用示例
var totalAmount = await YData.Select<Order>().YSumAsync(o => o.Amount);
```

#### YAverageAsync

平均值查询。

```csharp
// 方法签名
public static Task<decimal> YAverageAsync<T>(this ISelect<T> select, Expression<Func<T, object>> selector)

// 使用示例
var avgAge = await YData.Select<User>().YAverageAsync(u => u.Age);
```

#### YMaxAsync / YMinAsync

最大值/最小值查询。

```csharp
// 方法签名
public static Task<TResult> YMaxAsync<T, TResult>(this ISelect<T> select, Expression<Func<T, TResult>> selector)
public static Task<TResult> YMinAsync<T, TResult>(this ISelect<T> select, Expression<Func<T, TResult>> selector)

// 使用示例
var maxAge = await YData.Select<User>().YMaxAsync(u => u.Age);
var minAge = await YData.Select<User>().YMinAsync(u => u.Age);
```

### 多表关联扩展

#### YInclude

包含导航属性。

```csharp
// 方法签名
public static ISelect<T> YInclude<T, TProperty>(this ISelect<T> select, Expression<Func<T, TProperty>> navigationProperty)

// 使用示例
var users = await YData.Select<User>()
    .YInclude(u => u.Orders)
    .YInclude(u => u.Profile)
    .ToListAsync();
```

#### YLeftJoin / YInnerJoin / YRightJoin

表连接操作。

```csharp
// 方法签名
public static ISelect<T, T2> YLeftJoin<T, T2>(this ISelect<T> select, Expression<Func<T, T2, bool>> joinCondition)

// 使用示例
var result = await YData.Select<User>()
    .YLeftJoin<User, Order>((u, o) => u.Id == o.UserId)
    .ToListAsync((u, o) => new { User = u, Order = o });
```

### 批量操作扩展

#### YBatchInsertAsync

批量插入。

```csharp
// 方法签名
public static Task<int> YBatchInsertAsync<T>(this IInsert<T> insert, IEnumerable<T> entities, int batchSize = 1000)

// 使用示例
var users = GetLargeUserList();
var affected = await YData.Insert<User>().YBatchInsertAsync(users, 500);
```

#### YBatchUpdateAsync

批量更新。

```csharp
// 方法签名
public static Task<int> YBatchUpdateAsync<T>(this IUpdate<T> update, IEnumerable<T> entities, int batchSize = 1000)

// 使用示例
var users = GetUpdatedUserList();
var affected = await YData.Update<User>().YBatchUpdateAsync(users, 500);
```

## 📊 数据模型

### PagedResult<T>

分页结果模型。

```csharp
public class PagedResult<T>
{
    public List<T> Items { get; set; }           // 当前页数据
    public int PageIndex { get; set; }           // 当前页索引（从1开始）
    public int PageSize { get; set; }            // 每页大小
    public long TotalCount { get; set; }         // 总记录数
    public int TotalPages { get; set; }          // 总页数
    public bool HasPreviousPage { get; set; }    // 是否有上一页
    public bool HasNextPage { get; set; }        // 是否有下一页
}
```

### YDatabaseInfo

数据库信息模型。

```csharp
public class YDatabaseInfo
{
    public string Name { get; set; }             // 数据库名称
    public string ConnectionString { get; set; } // 连接字符串
    public YDataType DataType { get; set; }      // 数据库类型
    public bool IsDefault { get; set; }          // 是否为默认数据库
    public DateTime LastUsedTime { get; set; }   // 最后使用时间
}
```

## 🔧 辅助工具 API

### YConfigHelper 类

提供 appsettings.json 配置文件的管理功能。

#### CreateDefaultConfig

自动创建默认的 appsettings.json 文件。

```csharp
// 方法签名
public static bool CreateDefaultConfig(string defaultConnectionString, string filePath = "appsettings.json", bool overwrite = false)

// 使用示例
// 创建基础配置文件
YConfigHelper.CreateDefaultConfig("Data Source=myapp.db");

// 创建 SQL Server 配置
YConfigHelper.CreateDefaultConfig("Server=localhost;Database=MyApp;Integrated Security=true;");

// 覆盖已存在的文件
YConfigHelper.CreateDefaultConfig("Data Source=newapp.db", overwrite: true);
```

#### GetConnectionString / SetConnectionString

读取和设置配置文件中的连接字符串。

```csharp
// 方法签名
public static string? GetConnectionString(string connectionName = "DefaultConnection", string filePath = "appsettings.json")
public static bool SetConnectionString(string connectionString, string connectionName = "DefaultConnection", string filePath = "appsettings.json")

// 使用示例
// 读取连接字符串
var connStr = YConfigHelper.GetConnectionString();
var logConnStr = YConfigHelper.GetConnectionString("LogsConnection");

// 设置连接字符串
YConfigHelper.SetConnectionString("Data Source=newapp.db");
YConfigHelper.SetConnectionString("Data Source=logs.db", "LogsConnection");
```

#### SwitchDatabase

快速切换数据库类型。

```csharp
// 方法签名
public static bool SwitchDatabase(string databaseType, object parameters, string connectionName = "DefaultConnection", string filePath = "appsettings.json")

// 使用示例
// 切换到 SQLite
YConfigHelper.SwitchDatabase("SQLite", new { FilePath = "myapp.db" });

// 切换到 SQL Server
YConfigHelper.SwitchDatabase("SqlServer", new { Server = "localhost", Database = "MyApp" });

// 切换到 MySQL
YConfigHelper.SwitchDatabase("MySQL", new { Server = "localhost", Database = "myapp", UserId = "root", Password = "123456" });
```

#### SQLite 专用便捷方法

##### CreateSQLiteConfig / SwitchToSQLite

专门为 SQLite 设计的超级便捷方法。

```csharp
// 方法签名
public static bool CreateSQLiteConfig(string dbFileName, string? password = null, string filePath = "appsettings.json")
public static bool SwitchToSQLite(string dbFileName, string? password = null, string connectionName = "DefaultConnection", string filePath = "appsettings.json")

// 使用示例
// 创建 SQLite 配置（自动添加 .db 扩展名）
YConfigHelper.CreateSQLiteConfig("myapp");           // 创建 myapp.db 配置
YConfigHelper.CreateSQLiteConfig("secure", "pass123"); // 创建带密码的配置

// 快速切换 SQLite 数据库
YConfigHelper.SwitchToSQLite("dev");    // 切换到 dev.db
YConfigHelper.SwitchToSQLite("test");   // 切换到 test.db
YConfigHelper.SwitchToSQLite("prod");   // 切换到 prod.db
```

##### 多环境管理

管理开发、测试、生产等多个环境。

```csharp
// 方法签名
public static bool CreateMultiEnvironmentSQLiteConfig(Dictionary<string, string> environments, string defaultEnvironment, string filePath = "appsettings.json")
public static bool SwitchEnvironment(string environment, string filePath = "appsettings.json")

// 使用示例
// 创建多环境配置
var environments = new Dictionary<string, string>
{
    ["Development"] = "dev",
    ["Testing"] = "test",
    ["Production"] = "prod"
};
YConfigHelper.CreateMultiEnvironmentSQLiteConfig(environments, "Development");

// 环境切换
YConfigHelper.SwitchEnvironment("Testing");     // 切换到测试环境
YConfigHelper.SwitchEnvironment("Production");  // 切换到生产环境

// 环境查询
var currentEnv = YConfigHelper.GetCurrentEnvironment();
var allEnvs = YConfigHelper.GetAvailableEnvironments();
```

##### SQLite 数据库信息

查询 SQLite 数据库文件的状态信息。

```csharp
// 方法签名
public static bool SQLiteDatabaseExists(string connectionName = "DefaultConnection", string filePath = "appsettings.json")
public static long GetSQLiteDatabaseSize(string connectionName = "DefaultConnection", string filePath = "appsettings.json")

// 使用示例
// 检查数据库文件是否存在
if (YConfigHelper.SQLiteDatabaseExists())
{
    var size = YConfigHelper.GetSQLiteDatabaseSize();
    Console.WriteLine($"数据库大小: {size / 1024 / 1024:F2} MB");
}
else
{
    Console.WriteLine("数据库文件不存在，将自动创建");
}
```

### YConnectionStringHelper 类

提供连接字符串的构建和修改功能。

#### 修改连接字符串

##### ChangeDatabase

修改连接字符串中的数据库名称。

```csharp
// 方法签名
public static string ChangeDatabase(string connectionString, string newDatabase)

// 使用示例
var originalConn = "Server=localhost;Database=OldDB;Integrated Security=true;";
var newConn = YConnectionStringHelper.ChangeDatabase(originalConn, "NewDB");
// 结果: "Server=localhost;Database=NewDB;Integrated Security=true;"
```

##### ExtractDatabase

从连接字符串中提取数据库名称。

```csharp
// 方法签名
public static string ExtractDatabase(string connectionString)

// 使用示例
var connStr = "Server=localhost;Database=MyApp;Integrated Security=true;";
var dbName = YConnectionStringHelper.ExtractDatabase(connStr);
// 结果: "MyApp"
```

#### 快速构建连接字符串

##### BuildSQLite

构建 SQLite 连接字符串。

```csharp
// 方法签名
public static string BuildSQLite(string filePath, string? password = null, int version = 3, bool pooling = true)

// 使用示例
var connStr = YConnectionStringHelper.BuildSQLite("myapp.db");
var secureConn = YConnectionStringHelper.BuildSQLite("secure.db", "password123");
```

##### BuildSqlServer

构建 SQL Server 连接字符串。

```csharp
// 集成认证
public static string BuildSqlServerIntegrated(string server, string database, int timeout = 30, bool trustServerCertificate = true)

// 用户名密码认证
public static string BuildSqlServer(string server, string database, string userId, string password, int timeout = 30, bool trustServerCertificate = true)

// 使用示例
var integratedConn = YConnectionStringHelper.BuildSqlServerIntegrated("localhost", "MyApp");
var userConn = YConnectionStringHelper.BuildSqlServer("localhost", "MyApp", "sa", "password123");
```

### YDataImportExportExtensions 类

提供数据的 JSON 导入导出功能。

#### ExportToJsonAsync

导出数据到 JSON 文件。

```csharp
// 方法签名
public static Task<int> ExportToJsonAsync<T>(string filePath, Expression<Func<T, bool>>? where = null) where T : class

// 使用示例
// 导出所有用户
var count = await YDataImportExportExtensions.ExportToJsonAsync<User>("users.json");

// 导出符合条件的用户
var adultCount = await YDataImportExportExtensions.ExportToJsonAsync<User>("adults.json", u => u.Age >= 18);
```

#### ImportFromJsonAsync

从 JSON 文件导入数据。

```csharp
// 方法签名
public static Task<int> ImportFromJsonAsync<T>(string filePath, bool clearTable = false) where T : class

// 使用示例
// 导入用户数据（追加模式）
var count = await YDataImportExportExtensions.ImportFromJsonAsync<User>("users.json");

// 导入用户数据（替换模式）
var count = await YDataImportExportExtensions.ImportFromJsonAsync<User>("users.json", clearTable: true);
```

### YWpfExtensions 类

提供 WPF 应用的数据绑定扩展。

#### ToObservableCollectionAsync

转换为 ObservableCollection。

```csharp
// 方法签名
public static Task<ObservableCollection<T>> ToObservableCollectionAsync<T>(this ISelect<T> select) where T : class

// 使用示例
var users = await YData.Select<User>()
    .Where(u => u.IsActive)
    .ToObservableCollectionAsync();
```

#### ToPagedObservableCollectionAsync

分页转换为 ObservableCollection。

```csharp
// 方法签名
public static Task<WpfPagedResult<T>> ToPagedObservableCollectionAsync<T>(this ISelect<T> select, int pageIndex, int pageSize) where T : class

// 使用示例
var result = await YData.Select<User>()
    .Where(u => u.IsActive)
    .ToPagedObservableCollectionAsync(1, 20);

// result.Items 是 ObservableCollection<User>
```

---

**📝 提示：** 更多详细信息请参考 [配置指南](配置指南.md) 和 [最佳实践](最佳实践.md)。
