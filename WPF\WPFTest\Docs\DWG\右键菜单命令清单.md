# DWG右键菜单命令清单

## 🎯 概述
DwgManagerTabView的DataGrid右键菜单包含10个功能完整的命令，提供全面的文件管理功能。

## 📋 命令列表

### 1. 文件操作命令

| 序号 | 命令名称 | 方法名 | 参数 | 功能描述 |
|------|----------|--------|------|----------|
| 1 | 📂 打开文件 | `OpenFileCommand` | `DwgFileModel?` | 使用默认程序打开选中的DWG文件 |
| 2 | 📝 重命名 | `RenameFileCommand` | `DwgFileModel?` | 重命名选中的文件（异步） |
| 3 | 📋 复制文件 | `CopyFileCommand` | `DwgFileModel?` | 在当前目录创建文件副本（异步） |
| 4 | 🗑️ 删除文件 | `DeleteFileCommand` | `DwgFileModel?` | 删除选中的文件（异步，带确认） |

### 2. 复制操作命令

| 序号 | 命令名称 | 方法名 | 参数 | 功能描述 |
|------|----------|--------|------|----------|
| 5 | 🖥️ 复制到桌面 | `CopyToDesktopCommand` | `DwgFileModel?` | 将文件复制到桌面（异步） |
| 6 | 📁 复制路径 | `CopyFilePathCommand` | `DwgFileModel?` | 复制文件完整路径到剪贴板 |

### 3. 创建操作命令

| 序号 | 命令名称 | 方法名 | 参数 | 功能描述 |
|------|----------|--------|------|----------|
| 7 | ➕ 新建DWG文件 | `CreateNewDwgCommand` | 无 | 基于模板创建新的DWG文件（异步） |
| 8 | 📄 基于模板创建 | `CreateFromTemplateCommand` | 无 | 选择模板文件创建新文件（异步） |

### 4. 高级操作命令

| 序号 | 命令名称 | 方法名 | 参数 | 功能描述 |
|------|----------|--------|------|----------|
| 9 | 🔍 在资源管理器中显示 | `ShowInExplorerCommand` | `DwgFileModel?` | 在Windows资源管理器中定位文件 |
| 10 | ℹ️ 属性 | `ShowFilePropertiesCommand` | `DwgFileModel?` | 显示文件详细属性信息 |

## 🔧 命令实现特点

### 异步操作
以下命令使用异步实现，避免阻塞UI：
- `RenameFileCommand` - 文件重命名
- `CopyFileCommand` - 文件复制
- `DeleteFileCommand` - 文件删除
- `CopyToDesktopCommand` - 复制到桌面
- `CreateNewDwgCommand` - 新建DWG文件
- `CreateFromTemplateCommand` - 基于模板创建

### 同步操作
以下命令使用同步实现，操作简单快速：
- `OpenFileCommand` - 打开文件
- `CopyFilePathCommand` - 复制路径
- `ShowInExplorerCommand` - 显示在资源管理器
- `ShowFilePropertiesCommand` - 显示属性

## 🛡️ 安全特性

### 确认对话框
- **删除文件** - 显示确认对话框，防止误删
- **重命名文件** - 输入对话框验证新文件名

### 文件名冲突处理
- **复制文件** - 自动添加"_副本"后缀
- **复制到桌面** - 自动添加数字后缀避免覆盖
- **新建文件** - 自动生成不重复的文件名

### 错误处理
- 所有命令都有完整的异常处理
- 错误信息显示在状态栏
- 详细错误记录到日志

## 📦 依赖和模板

### 模板文件
- **默认模板**: `Assets/DWG/Drawing1.dwg`
- **自动创建**: 如果模板不存在，系统自动创建
- **自定义模板**: 支持选择任意DWG文件作为模板

### 外部依赖
- **Microsoft.VisualBasic** - 用于输入对话框
- **System.Diagnostics.Process** - 用于打开外部程序
- **System.Windows.Clipboard** - 用于剪贴板操作

## 🎮 XAML绑定示例

```xml
<!-- 右键菜单中的命令绑定 -->
<MenuItem Header="📂 打开文件" 
          Command="{Binding OpenFileCommand}" 
          CommandParameter="{Binding PlacementTarget.SelectedItem, RelativeSource={RelativeSource AncestorType=ContextMenu}}">
    <MenuItem.Icon>
        <ui:SymbolIcon Symbol="Open24" />
    </MenuItem.Icon>
</MenuItem>

<MenuItem Header="📝 重命名" 
          Command="{Binding RenameFileCommand}" 
          CommandParameter="{Binding PlacementTarget.SelectedItem, RelativeSource={RelativeSource AncestorType=ContextMenu}}">
    <MenuItem.Icon>
        <ui:SymbolIcon Symbol="Rename24" />
    </MenuItem.Icon>
</MenuItem>
```

## 🔍 命令参数说明

### 带参数的命令
大部分命令接受`DwgFileModel?`参数，通过XAML的`CommandParameter`传递：
```xml
CommandParameter="{Binding PlacementTarget.SelectedItem, RelativeSource={RelativeSource AncestorType=ContextMenu}}"
```

### 无参数的命令
创建操作命令不需要参数，直接操作当前选中的专业文件夹：
- `CreateNewDwgCommand`
- `CreateFromTemplateCommand`

## 🚀 性能优化

### 异步操作
- 文件I/O操作使用`Task.Run`在后台线程执行
- UI线程不被阻塞，保持响应性
- 操作完成后自动刷新文件列表

### 智能缓存
- 文件列表自动刷新
- 状态消息实时更新
- 日志记录详细操作信息

## 📝 使用注意事项

1. **文件选择** - 大部分操作需要先选中文件
2. **权限要求** - 确保应用有文件操作权限
3. **文件占用** - 操作前确保文件未被其他程序占用
4. **模板文件** - 新建功能依赖模板文件存在

## 🔄 命令状态管理

所有命令操作后都会：
1. 更新状态栏消息
2. 记录操作日志
3. 刷新文件列表（如需要）
4. 处理异常情况

这确保了用户始终能够了解操作结果和系统状态。
