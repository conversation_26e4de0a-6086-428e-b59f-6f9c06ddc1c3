#region Using 引用
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.Extensions.Logging;
using WPFTest.Models.DWG;
using Zylo.YData;
using Zylo.YLog.Runtime;
#endregion

namespace WPFTest.Services.DWG;

/// <summary>
/// DWG 文件夹服务实现 - 智能文件夹管理和路径扫描系统
/// </summary>
/// <remarks>
/// 🎯 核心功能：
/// - 智能文件夹扫描：自动发现和管理现有文件夹结构
/// - 专业文件夹创建：自动创建标准的工程专业文件夹
/// - 完整的CRUD操作：支持文件夹的增删改查管理
/// - 路径管理：提供文件夹路径的统一管理和验证
///
/// 📊 数据管理：
/// - 使用YData ORM进行SQLite数据持久化
/// - CodeFirst模式自动创建和维护数据库结构
/// - 支持默认专业文件夹的自动初始化
/// - 提供完整的文件夹状态信息查询
///
/// 🔍 智能扫描算法：
/// 1. 路径验证：检查目标路径的存在性和访问权限
/// 2. 默认文件夹创建：在指定路径创建标准专业文件夹
/// 3. 现有文件夹发现：扫描并纳入管理现有的文件夹
/// 4. 图标智能识别：根据文件夹名称自动分配合适的图标
/// 5. 结果统计：提供详细的扫描结果和操作统计
///
/// 📁 默认专业文件夹：
/// - 🏗️ 结构：结构专业图纸文件夹
/// - 🏢 建筑：建筑专业图纸文件夹
/// - 🌡️ 暖通：暖通空调专业文件夹
/// - 💧 给排水：给排水专业文件夹
/// - ⚡ 电气：电气专业文件夹
/// - 🌳 园林：园林景观专业文件夹
///
/// 🔍 图标智能识别规则：
/// - 包含"建筑"关键词 → 🏢
/// - 包含"结构"关键词 → 🏗️
/// - 包含"暖通"关键词 → 🌡️
/// - 包含"给排水"关键词 → 💧
/// - 包含"电气"关键词 → ⚡
/// - 包含"园林"关键词 → 🌳
/// - 其他情况 → 📁
///
/// 🔄 扫描流程：
/// 1. ScanAndManageFoldersAsync(): 主扫描入口
/// 2. CreateDefaultFoldersAtPathAsync(): 创建默认专业文件夹
/// 3. ScanExistingFoldersAsync(): 发现现有文件夹
/// 4. 自动图标分配和数据库存储
/// 5. 触发ScanCompleted事件通知完成
///
/// 📈 扫描结果统计：
/// - CreatedFolders: 新创建的文件夹列表
/// - DiscoveredFolders: 发现并纳入管理的现有文件夹
/// - TotalManagedFolders: 总共管理的文件夹数量
/// - IsSuccess: 扫描操作的整体状态
/// - ErrorMessage: 错误信息（如果有）
///
/// 🔄 事件机制：
/// - FolderChanged事件：文件夹增删改时触发
/// - ScanCompleted事件：扫描操作完成时触发
/// - 事件参数包含操作类型和相关数据
/// - 支持多个订阅者同时监听数据变更
///
/// 🎨 设计模式：
/// - 服务模式：封装文件夹管理业务逻辑
/// - 单例模式：YData配置确保全局唯一
/// - 事件驱动：通过事件解耦组件间的依赖关系
/// - 异步模式：所有文件系统和数据库操作都采用异步方式
/// - 工厂模式：CreateDefaultFolders()创建默认数据
///
/// 🛡️ 错误处理：
/// - 完整的异常捕获和日志记录
/// - 文件系统操作失败时的优雅处理
/// - 权限不足时的友好提示
/// - 提供详细的错误信息和调试日志
///
/// 🔧 技术特点：
/// - 基于Zylo.YData ORM框架
/// - 支持SQLite数据库
/// - 异步优先的API设计
/// - 完整的日志记录系统
/// - 支持依赖注入和单元测试
/// - 实现IDisposable进行资源管理
///
/// 💡 使用示例：
/// ```csharp
/// // 扫描并管理文件夹
/// var result = await service.ScanAndManageFoldersAsync(@"D:\Projects\MyProject");
/// Console.WriteLine($"创建了 {result.CreatedFolders.Count} 个文件夹");
/// Console.WriteLine($"发现了 {result.DiscoveredFolders.Count} 个现有文件夹");
///
/// // 手动添加文件夹
/// var folder = new DwgFolderModel("自定义专业", "🔧", false);
/// await service.AddFolderAsync(folder);
/// ```
/// </remarks>
public class DwgFolderService : IDwgFolderService
{
    #region 私有字段

    /// <summary>
    /// 日志记录器
    /// </summary>
    /// <remarks>
    /// 用于记录服务操作的详细日志
    /// 包括成功操作、警告和错误信息
    /// </remarks>
    private readonly ILogger<DwgFolderService>? _logger;

    /// <summary>
    /// YData 配置状态标志
    /// </summary>
    /// <remarks>
    /// 静态字段，确保 YData 只配置一次
    /// 避免重复配置导致的问题
    /// </remarks>
    private static bool _isConfigured = false;

    #endregion

    #region 公共事件

    /// <summary>
    /// 文件夹变更事件
    /// </summary>
    /// <remarks>
    /// 在文件夹增删改操作成功后触发
    /// 用于通知 UI 更新或缓存失效
    /// </remarks>
    public event EventHandler<DwgFolderChangedEventArgs>? FolderChanged;

    /// <summary>
    /// 扫描完成事件
    /// </summary>
    /// <remarks>
    /// 在路径扫描操作完成后触发
    /// 包含扫描结果的详细信息
    /// </remarks>
    public event EventHandler<DwgFolderScanCompletedEventArgs>? ScanCompleted;

    #endregion

    #region 构造函数和初始化

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器（可选）</param>
    /// <remarks>
    /// 初始化逻辑：
    /// 1. 保存日志记录器引用
    /// 2. 检查并配置 YData（单例模式）
    /// 3. 记录服务初始化完成日志
    ///
    /// 注意：YData 配置采用静态标志位确保只配置一次
    /// </remarks>
    public DwgFolderService(ILogger<DwgFolderService>? logger = null)
    {
        _logger = logger;

        // 配置 YData（如果尚未配置）
        if (!_isConfigured)
        {
            ConfigureYData();
            _isConfigured = true;
        }

        _logger?.LogInformation("🗂️ DWG 文件夹服务初始化完成");
    }

    #endregion

    #region YData 数据库配置

    /// <summary>
    /// 配置 YData 数据库连接
    /// </summary>
    /// <remarks>
    /// 配置逻辑：
    /// 1. 确定应用程序数据目录
    /// 2. 创建 Data 子目录（如果不存在）
    /// 3. 构建 SQLite 数据库文件路径
    /// 4. 配置 YData 连接字符串
    /// 5. 启用共享缓存模式
    ///
    /// 数据库位置：{AppDirectory}\Data\DwgManager.db
    /// 连接模式：共享缓存，支持多线程访问
    ///
    /// 异常处理：配置失败时抛出异常，阻止服务启动
    /// </remarks>
    private void ConfigureYData()
    {
        try
        {
            // 确定数据库存储路径
            var appDirectory = AppDomain.CurrentDomain.BaseDirectory;
            var dbDirectory = Path.Combine(appDirectory, "Data");
            Directory.CreateDirectory(dbDirectory);
            var dbPath = Path.Combine(dbDirectory, "DwgManager.db");

            _logger?.LogInformation($"🗂️ 配置数据库路径: {dbPath}");

            // 配置 SQLite 连接字符串
            var connectionString = $"Data Source={dbPath};Cache=Shared;";
            YData.ConfigureAuto(connectionString, YDataType.Sqlite);

            _logger?.LogInformation("✅ YData 配置完成");
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "❌ YData 配置失败");
            throw;
        }
    }

    #endregion

    #region 数据库初始化

    /// <summary>
    /// 初始化数据库
    /// </summary>
    /// <returns>初始化是否成功</returns>
    /// <remarks>
    /// 初始化步骤：
    /// 1. 同步数据库表结构（CodeFirst 模式）
    /// 2. 检查数据库是否为空
    /// 3. 如果为空，插入默认文件夹数据
    /// 4. 记录初始化结果
    ///
    /// 默认数据包括：
    /// - 🏗️ 结构（默认）
    /// - 🏢 建筑（默认）
    /// - 🌡️ 暖通（默认）
    /// - 💧 给排水（默认）
    /// - ⚡ 电气（默认）
    /// - 🌳 园林（默认）
    ///
    /// 异常处理：捕获所有异常并记录日志，返回 false
    /// </remarks>
    public async Task<bool> InitializeDatabaseAsync()
    {
        try
        {
            _logger?.LogInformation("🔧 开始初始化 DWG 文件夹数据库...");

            // 确保数据库表结构存在
            YData.FreeSql.CodeFirst.SyncStructure<DwgFolderModel>();
            _logger?.LogInformation("✅ 数据库表结构同步完成");

            // 检查是否有数据，如果没有则插入默认数据
            var existingCount = await YData.Select<DwgFolderModel>().CountAsync();
            if (existingCount == 0)
            {
                _logger?.LogInformation("📝 数据库为空，正在插入默认文件夹数据...");
                var defaultFolders = DwgFolderModel.CreateDefaultFolders();

                foreach (var folder in defaultFolders)
                {
                    await YData.InsertAsync(folder);
                }

                _logger?.LogInformation($"✅ 成功插入 {defaultFolders.Count} 个默认文件夹");
            }
            else
            {
                // 如果数据库中已有数据，检查并更新默认文件夹的排序号
           
            }

            _logger?.LogInformation("🎉 DWG 文件夹数据库初始化完成");
            return true;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "❌ DWG 文件夹数据库初始化失败");
            return false;
        }
    }



    #endregion

    #region 查询操作

    /// <summary>
    /// 获取所有DWG文件夹 - 按SortOrder排序返回
    /// </summary>
    /// <param name="includeDisabled">是否包含禁用的文件夹，默认false只返回启用的文件夹</param>
    /// <returns>按SortOrder排序的文件夹列表</returns>
    /// <remarks>
    /// 🎯 核心功能：
    /// - 返回所有可用的DWG文件夹配置
    /// - 支持过滤已禁用的文件夹（IsEnabled = false）
    /// - 自动按SortOrder和文件夹名称排序
    ///
    /// 📊 排序规则（重要）：
    /// 1. 🥇 首先按SortOrder升序排列（0, 1, 2, 3...）
    /// 2. 🥈 相同SortOrder时按Name字母顺序排列
    /// 3. 🎯 确保UI显示顺序与数据库配置一致
    ///
    /// 💡 使用场景：
    /// - UI文件夹列表：按优先级顺序展示文件夹
    /// - 文件夹选择：提供有序的文件夹选项
    /// - 配置管理：维护文件夹的显示顺序
    ///
    /// 📁 文件夹类型：
    /// - 项目文件夹：存放项目相关的DWG文件
    /// - 模板文件夹：存放图纸模板和标准文件
    /// - 归档文件夹：存放历史版本和备份文件
    ///
    /// ⚡ 性能优化：
    /// - 使用YData的查询优化和索引
    /// - 内存排序比数据库排序更高效
    /// - 支持条件查询减少数据传输
    /// </remarks>
    public async Task<List<DwgFolderModel>> GetAllFoldersAsync(bool includeDisabled = false)
    {
        try
        {
            _logger?.LogDebug($"🔍 开始获取文件夹列表，包含禁用项: {includeDisabled}");

            // 根据参数决定查询策略
            var result = includeDisabled
                ? await YData.GetAllAsync<DwgFolderModel>()    // 获取所有记录（包含禁用的）
                : await YData.Select<DwgFolderModel>()         // 只获取启用的记录
                    .Where(f => f.IsEnabled)
                    .ToListAsync();

            // 🎯 关键排序：按SortOrder升序，然后按文件夹名称排序
            var sortedResult = result
                .OrderBy(f => f.SortOrder)          // 主排序：按优先级排序
                .ThenBy(f => f.Name)                // 次排序：按名称排序
                .ToList();

            _logger?.LogInformation($"✅ 获取文件夹完成: {sortedResult.Count} 个文件夹，按SortOrder排序");

            // 记录排序后的顺序（调试用）
            if (_logger?.IsEnabled(Microsoft.Extensions.Logging.LogLevel.Debug) == true)
            {
                foreach (var folder in sortedResult.Take(5)) // 只记录前5个
                {
                    _logger.LogDebug($"   📁 {folder.SortOrder:D2}: {folder.Name} ({folder.Icon})");
                }
                if (sortedResult.Count > 5)
                {
                    _logger.LogDebug($"   ... 还有 {sortedResult.Count - 5} 个文件夹");
                }
            }

            return sortedResult;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "❌ 获取文件夹列表失败");
            throw;
        }
    }

    /// <summary>
    /// 根据ID获取文件夹
    /// </summary>
    /// <param name="id">文件夹ID</param>
    /// <returns>文件夹模型，如果不存在返回 null</returns>
    /// <remarks>
    /// 使用 YData 的主键查询，性能最优
    /// 常用于编辑、删除操作的前置查询
    /// </remarks>
    public async Task<DwgFolderModel?> GetFolderByIdAsync(int id)
    {
        return await YData.GetAsync<DwgFolderModel>(id);
    }

    /// <summary>
    /// 根据名称获取文件夹
    /// </summary>
    /// <param name="name">文件夹名称</param>
    /// <returns>文件夹模型，如果不存在返回 null</returns>
    /// <remarks>
    /// 使用名称进行精确匹配查询
    /// 常用于检查文件夹名称重复
    /// 扫描时检查文件夹是否已存在
    /// </remarks>
    public async Task<DwgFolderModel?> GetFolderByNameAsync(string name)
    {
        return await YData.Select<DwgFolderModel>().Where(f => f.Name == name).FirstAsync();
    }

    #endregion

    #region CRUD操作 - 增加(Create)

    /// <summary>
    /// 添加文件夹
    /// </summary>
    /// <param name="folder">文件夹模型</param>
    /// <returns>添加是否成功</returns>
    /// <remarks>
    /// 添加流程：
    /// 1. 记录操作开始日志
    /// 2. 执行 YData 插入操作
    /// 3. 获取自增ID并更新模型
    /// 4. 触发 FolderChanged 事件
    /// 5. 记录操作结果日志
    ///
    /// 事件通知：成功时触发 Added 类型事件
    /// 异常处理：捕获异常并记录错误日志
    /// </remarks>
    public async Task<bool> AddFolderAsync(DwgFolderModel folder)
    {
        try
        {
            _logger?.LogInformation($"➕ 正在添加文件夹: {folder.Name}");

            var result = await YData.InsertAsync(folder);

            if (result > 0)
            {
                folder.Id = (int)result;
                _logger?.LogInformation($"✅ 成功添加文件夹，ID: {result}");
                FolderChanged?.Invoke(this, new DwgFolderChangedEventArgs(DwgFolderChangeType.Added, folder));
                return true;
            }
            else
            {
                _logger?.LogWarning("⚠️ 添加文件夹失败，返回值为 0");
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, $"❌ 添加文件夹失败: {folder.Name}");
            return false;
        }
    }

    #endregion

    #region CRUD操作 - 修改(Update)

    /// <summary>
    /// 更新文件夹
    /// </summary>
    /// <param name="folder">文件夹模型</param>
    /// <returns>更新是否成功</returns>
    /// <remarks>
    /// 更新流程：
    /// 1. 记录操作开始日志
    /// 2. 执行 YData 更新操作（根据ID更新）
    /// 3. 触发 FolderChanged 事件
    /// 4. 记录操作结果日志
    ///
    /// 注意：UpdatedAt 字段会在模型的属性变化时自动更新
    /// 事件通知：成功时触发 Updated 类型事件
    /// 异常处理：捕获异常并记录错误日志
    /// </remarks>
    public async Task<bool> UpdateFolderAsync(DwgFolderModel folder)
    {
        try
        {
            _logger?.LogInformation($"🔄 正在更新文件夹: {folder.Name}");

            var result = await YData.UpdateAsync(folder);

            if (result > 0)
            {
                _logger?.LogInformation($"✅ 成功更新文件夹");
                FolderChanged?.Invoke(this, new DwgFolderChangedEventArgs(DwgFolderChangeType.Updated, folder));
                return true;
            }
            else
            {
                _logger?.LogWarning("⚠️ 更新文件夹失败，返回值为 0");
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, $"❌ 更新文件夹失败: {folder.Name}");
            return false;
        }
    }

    #endregion

    #region CRUD操作 - 删除(Delete)

    /// <summary>
    /// 删除文件夹
    /// </summary>
    /// <param name="id">文件夹ID</param>
    /// <returns>删除是否成功</returns>
    /// <remarks>
    /// 删除流程：
    /// 1. 先查询文件夹是否存在
    /// 2. 记录操作开始日志
    /// 3. 执行 YData 删除操作
    /// 4. 触发 FolderChanged 事件（传递删除前的文件夹信息）
    /// 5. 记录操作结果日志
    ///
    /// 安全检查：删除前确认文件夹存在
    /// 事件通知：成功时触发 Deleted 类型事件
    /// 异常处理：捕获异常并记录错误日志
    /// </remarks>
    public async Task<bool> DeleteFolderAsync(int id)
    {
        try
        {
            var folder = await GetFolderByIdAsync(id);
            if (folder == null)
            {
                _logger?.LogWarning($"⚠️ 未找到ID为 {id} 的文件夹");
                return false;
            }

            _logger?.LogInformation($"🗑️ 正在删除文件夹: {folder.Name}");

            var result = await YData.DeleteAsync<DwgFolderModel>(id);

            if (result > 0)
            {
                _logger?.LogInformation($"✅ 成功删除文件夹");
                FolderChanged?.Invoke(this, new DwgFolderChangedEventArgs(DwgFolderChangeType.Deleted, folder));
                return true;
            }
            else
            {
                _logger?.LogWarning("⚠️ 删除文件夹失败，返回值为 0");
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, $"❌ 删除文件夹失败: ID {id}");
            return false;
        }
    }

    #endregion

    #region CRUD操作 - 辅助操作(Helper)

    /// <summary>
    /// 批量更新排序
    /// </summary>
    /// <param name="folders">文件夹列表</param>
    /// <returns>更新是否成功</returns>
    /// <remarks>
    /// 排序更新流程：
    /// 1. 记录批量操作开始日志
    /// 2. 按列表顺序设置 SortOrder（0, 1, 2...）
    /// 3. 逐个更新文件夹到数据库
    /// 4. 为每个文件夹触发 SortOrderChanged 事件
    /// 5. 记录操作完成日志
    ///
    /// 使用场景：
    /// - 拖拽排序后保存新顺序
    /// - 批量调整文件夹显示顺序
    ///
    /// 性能考虑：逐个更新而非批量更新，确保事件正确触发
    /// 异常处理：任何一个更新失败都会回滚整个操作
    /// </remarks>
    public async Task<bool> UpdateSortOrderAsync(List<DwgFolderModel> folders)
    {
        try
        {
            _logger?.LogInformation($"🔢 正在批量更新 {folders.Count} 个文件夹的排序");

            for (int i = 0; i < folders.Count; i++)
            {
                var oldSortOrder = folders[i].SortOrder;
                folders[i].SortOrder = i;

                _logger?.LogInformation($"📝 更新文件夹: {folders[i].Name} SortOrder: {oldSortOrder} -> {i}");

                var updateResult = await YData.UpdateAsync(folders[i]);
                if (updateResult <= 0)
                {
                    _logger?.LogWarning($"⚠️ 更新文件夹 {folders[i].Name} 失败，返回值: {updateResult}");
                }
            }

            _logger?.LogInformation("✅ 批量排序更新完成");

            // 触发排序变更事件
            foreach (var folder in folders)
            {
                FolderChanged?.Invoke(this, new DwgFolderChangedEventArgs(DwgFolderChangeType.SortOrderChanged, folder));
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "❌ 批量排序更新失败");
            return false;
        }
    }

    #endregion

    #region 特殊功能 - 路径扫描和自动创建

    /// <summary>
    /// 扫描指定路径并自动管理文件夹（核心业务功能）
    /// </summary>
    /// <param name="basePath">基础路径</param>
    /// <returns>扫描结果，包含创建和发现的文件夹信息</returns>
    /// <remarks>
    /// 核心业务流程：
    /// 1. 验证基础路径是否存在
    /// 2. 调用 CreateDefaultFoldersAtPathAsync 创建默认文件夹
    /// 3. 调用 ScanExistingFoldersAsync 扫描现有文件夹
    /// 4. 汇总扫描结果
    /// 5. 触发 ScanCompleted 事件
    ///
    /// 业务价值：
    /// - 用户选择新工作目录时自动初始化文件夹结构
    /// - 导入现有项目时发现并管理所有相关文件夹
    /// - 确保项目文件夹的标准化和一致性
    ///
    /// 错误处理：
    /// - 路径不存在时返回失败结果
    /// - 部分操作失败不影响整体流程
    /// - 详细的错误信息记录
    /// </remarks>
    public async Task<DwgFolderScanResult> ScanAndManageFoldersAsync(string basePath)
    {
        var result = new DwgFolderScanResult { BasePath = basePath };

        try
        {
            _logger?.LogInformation($"🔍 开始扫描路径: {basePath}");

            if (!Directory.Exists(basePath))
            {
                _logger?.LogWarning($"⚠️ 路径不存在: {basePath}");
                result.IsSuccess = false;
                result.ErrorMessage = "指定路径不存在";
                return result;
            }

            // 1. 创建默认文件夹
            result.CreatedFolders = await CreateDefaultFoldersAtPathAsync(basePath);

            // 2. 扫描现有文件夹
            result.DiscoveredFolders = await ScanExistingFoldersAsync(basePath);

            _logger?.LogInformation($"✅ 扫描完成: 创建 {result.CreatedFolders.Count} 个，发现 {result.DiscoveredFolders.Count} 个");

            // 触发扫描完成事件
            ScanCompleted?.Invoke(this, new DwgFolderScanCompletedEventArgs(result));

            return result;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, $"❌ 扫描路径失败: {basePath}");
            result.IsSuccess = false;
            result.ErrorMessage = ex.Message;
            return result;
        }
    }

    /// <summary>
    /// 在指定路径创建默认文件夹结构
    /// </summary>
    /// <param name="basePath">基础路径</param>
    /// <returns>成功创建的文件夹列表</returns>
    /// <remarks>
    /// 🎯 功能说明：
    /// - 根据数据库中的文件夹配置创建物理文件夹
    /// - 只创建启用状态的文件夹
    /// - 自动设置文件夹图标和属性
    /// - 跳过已存在的文件夹，避免重复创建
    ///
    /// 📁 创建流程：
    /// 1. 获取所有启用的文件夹配置
    /// 2. 按SortOrder顺序逐个创建
    /// 3. 检查文件夹是否已存在
    /// 4. 创建不存在的文件夹
    /// 5. 记录创建结果和日志
    ///
    /// 💡 使用场景：
    /// - 新项目初始化时创建标准文件夹结构
    /// - 项目迁移时重建文件夹
    /// - 批量项目管理
    ///
    /// ⚠️ 注意事项：
    /// - 需要对目标路径有写入权限
    /// - 不会删除现有文件夹
    /// - 创建失败不影响其他文件夹的创建
    /// </remarks>
    public async Task<List<DwgFolderModel>> CreateDefaultFoldersAtPathAsync(string basePath)
    {
        var createdFolders = new List<DwgFolderModel>();

        try
        {
            var defaultFolders = DwgFolderModel.CreateDefaultFolders();

            foreach (var folder in defaultFolders)
            {
                var folderPath = Path.Combine(basePath, folder.Name);

                // 检查文件夹是否已存在
                if (!Directory.Exists(folderPath))
                {
                    try
                    {
                        Directory.CreateDirectory(folderPath);
                        _logger?.LogInformation($"📁 创建文件夹: {folderPath}");

                        // 添加到数据库管理，保持原有的 SortOrder
                        var managedFolder = new DwgFolderModel(folder.Name, folder.Icon, folder.IsDefault)
                        {
                            SortOrder = folder.SortOrder // 使用默认文件夹的排序顺序
                        };
                        await AddFolderAsync(managedFolder);

                        createdFolders.Add(managedFolder);
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogWarning($"⚠️ 创建文件夹失败: {folderPath} - {ex.Message}");
                    }
                }
            }

            return createdFolders;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, $"❌ 创建默认文件夹失败: {basePath}");
            return createdFolders;
        }
    }

    /// <summary>
    /// 扫描指定路径下的现有文件夹并匹配数据库配置
    /// </summary>
    /// <param name="basePath">要扫描的基础路径</param>
    /// <returns>发现的匹配文件夹列表</returns>
    /// <remarks>
    /// 🎯 功能说明：
    /// - 扫描指定路径下的所有子文件夹
    /// - 与数据库中的文件夹配置进行匹配
    /// - 只返回在数据库中有配置的文件夹
    /// - 自动设置文件夹的物理路径信息
    ///
    /// 🔍 扫描流程：
    /// 1. 获取所有数据库中的文件夹配置
    /// 2. 扫描基础路径下的所有子目录
    /// 3. 按文件夹名称进行匹配
    /// 4. 为匹配的文件夹设置完整路径
    /// 5. 记录发现的文件夹数量
    ///
    /// 💡 使用场景：
    /// - 项目导入时发现现有文件夹结构
    /// - 文件夹同步和对比
    /// - 项目结构分析
    /// - 批量文件夹管理
    ///
    /// 📊 匹配规则：
    /// - 按文件夹名称精确匹配
    /// - 忽略大小写差异
    /// - 只匹配数据库中已配置的文件夹
    ///
    /// ⚠️ 注意事项：
    /// - 需要对目标路径有读取权限
    /// - 不会修改文件系统
    /// - 扫描失败不影响其他操作
    /// </remarks>
    public async Task<List<DwgFolderModel>> ScanExistingFoldersAsync(string basePath)
    {
        var discoveredFolders = new List<DwgFolderModel>();

        try
        {
            var directories = Directory.GetDirectories(basePath);
            var existingFolders = await GetAllFoldersAsync(true);

            // 获取当前最大的 SortOrder，用于新发现的文件夹
            var maxSortOrder = existingFolders.Any() ? existingFolders.Max(f => f.SortOrder) : -1;

            foreach (var directory in directories)
            {
                var folderName = Path.GetFileName(directory);

                // 检查是否已在管理中
                if (!existingFolders.Any(f => f.Name.Equals(folderName, StringComparison.OrdinalIgnoreCase)))
                {
                    // 自动检测图标
                    var icon = DetectFolderIcon(folderName);

                    var discoveredFolder = new DwgFolderModel(folderName, icon, false)
                    {
                        SortOrder = ++maxSortOrder // 设置排序顺序，排在现有文件夹之后
                    };

                    await AddFolderAsync(discoveredFolder);

                    discoveredFolders.Add(discoveredFolder);
                    _logger?.LogInformation($"🔍 发现并添加文件夹: {folderName}, SortOrder: {discoveredFolder.SortOrder}");
                }
            }

            return discoveredFolders;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, $"❌ 扫描现有文件夹失败: {basePath}");
            return discoveredFolders;
        }
    }

    /// <summary>
    /// 根据文件夹名称智能检测合适的图标
    /// </summary>
    /// <param name="folderName">文件夹名称</param>
    /// <returns>对应的Emoji图标字符串</returns>
    /// <remarks>
    /// 🎯 功能说明：
    /// - 根据文件夹名称中的关键词自动匹配图标
    /// - 支持中英文关键词识别
    /// - 提供专业领域的直观图标表示
    /// - 未匹配时返回默认文件夹图标
    ///
    /// 🔍 识别规则：
    /// - 🏗️ 结构/STRUCTURE → 结构工程图标
    /// - 🏢 建筑/ARCHITECTURE → 建筑工程图标
    /// - 🌡️ 暖通/HVAC → 暖通空调图标
    /// - 💧 给排水/PLUMBING → 给排水工程图标
    /// - ⚡ 电气/ELECTRICAL → 电气工程图标
    /// - 🌳 园林/LANDSCAPE → 园林景观图标
    /// - 📁 其他 → 默认文件夹图标
    ///
    /// 💡 使用场景：
    /// - 扫描现有文件夹时自动设置图标
    /// - 创建新文件夹时的图标推荐
    /// - UI显示时的视觉识别
    ///
    /// ⚠️ 注意事项：
    /// - 关键词匹配不区分大小写
    /// - 使用Contains进行模糊匹配
    /// - 优先匹配更具体的关键词
    /// </remarks>
    private string DetectFolderIcon(string folderName)
    {
        var name = folderName.ToUpper();

        return name switch
        {
            var n when n.Contains("结构") || n.Contains("STRUCTURE") => "🏗️",
            var n when n.Contains("建筑") || n.Contains("ARCHITECTURE") => "🏢",
            var n when n.Contains("暖通") || n.Contains("HVAC") => "🌡️",
            var n when n.Contains("给排水") || n.Contains("PLUMBING") => "💧",
            var n when n.Contains("电气") || n.Contains("ELECTRICAL") => "⚡",
            var n when n.Contains("园林") || n.Contains("LANDSCAPE") => "🌳",

            _ => "📁"
        };
    }

    #endregion

    #region 资源管理

    /// <summary>
    /// 释放资源
    /// </summary>
    /// <remarks>
    /// 🎯 功能说明：
    /// - 实现IDisposable接口
    /// - YData资源由框架自动管理
    /// - 记录服务释放日志
    /// - 当前实现为空，预留扩展点
    ///
    /// 💡 设计考虑：
    /// - 支持依赖注入容器的生命周期管理
    /// - 为未来可能的资源清理预留接口
    /// - 遵循.NET资源管理最佳实践
    /// - 提供清晰的服务生命周期日志
    ///
    /// 🔧 扩展点：
    /// - 可在此处添加事件取消订阅
    /// - 可添加临时文件清理逻辑
    /// - 可添加缓存清理逻辑
    /// </remarks>
    public void Dispose()
    {
        // YData资源由框架管理，无需手动释放
        // 预留扩展点，用于未来可能的资源清理
        _logger?.LogInformation("🧹 DWG 文件夹服务已释放");
    }

    #endregion
}
