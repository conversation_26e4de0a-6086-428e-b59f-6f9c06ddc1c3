# 🔄 数据库切换策略指南

## 📖 概述

Zylo.YData 提供了两种不同的数据库切换机制，各自适用于不同的开发场景和业务需求：

- **🔧 YConfigHelper** - 配置文件管理专家
- **🏗️ IYDataManager** - 运行时多数据库管理专家

本文档将详细说明两者的使用场景、最佳实践和组合策略。

---

## 🔧 YConfigHelper - 配置文件管理专家

### 🎯 核心特点

- **配置驱动** - 通过修改 appsettings.json 来切换数据库
- **持久化配置** - 配置保存在文件中，应用重启后仍然有效
- **环境管理** - 专门针对开发/测试/生产环境切换优化
- **SQLite 优化** - 为 SQLite 提供超级便捷的使用方法

### 📋 适用场景

#### ✅ 推荐使用场景

1. **开发环境切换**
   ```csharp
   // 开发阶段
   YConfigHelper.SwitchEnvironment("Development");
   
   // 测试阶段  
   YConfigHelper.SwitchEnvironment("Testing");
   
   // 生产部署
   YConfigHelper.SwitchEnvironment("Production");
   ```

2. **部署脚本自动化**
   ```csharp
   // 部署脚本中根据目标环境自动配置
   var targetEnvironment = args[0]; // "Production"
   YConfigHelper.SwitchEnvironment(targetEnvironment);
   ```

3. **SQLite 快速开发**
   ```csharp
   // 超简单的 SQLite 配置
   YConfigHelper.CreateSQLiteConfig("myapp");
   YConfigHelper.SwitchToSQLite("dev");
   YConfigHelper.SwitchToSQLite("test");
   ```

4. **配置文件管理**
   ```csharp
   // 备份和恢复配置
   var backupFile = YConfigHelper.BackupConfig();
   YConfigHelper.RestoreConfig(backupFile);
   ```

#### ❌ 不推荐使用场景

- **运行时频繁切换** - 每次切换都需要重新配置 YData
- **多租户系统** - 会影响全局配置，不适合多租户
- **临时数据操作** - 修改配置文件过于重量级

### 🚀 使用示例

#### 基础使用
```csharp
using Zylo.YData.Helpers;

// 1. 创建 SQLite 配置
YConfigHelper.CreateSQLiteConfig("myapp");

// 2. 配置 YData
YData.ConfigureAuto(); // 自动读取配置文件

// 3. 使用数据库
var user = new User { Name = "测试用户" };
await YData.InsertAsync(user);
```

#### 多环境配置
```csharp
// 1. 创建多环境配置
var environments = new Dictionary<string, string>
{
    ["Development"] = "dev",
    ["Testing"] = "test", 
    ["Production"] = "prod"
};
YConfigHelper.CreateMultiEnvironmentSQLiteConfig(environments, "Development");

// 2. 环境切换
YConfigHelper.SwitchEnvironment("Testing");
YData.ConfigureAuto();

// 3. 查询环境信息
var currentEnv = YConfigHelper.GetCurrentEnvironment();
var allEnvs = YConfigHelper.GetAvailableEnvironments();
Console.WriteLine($"当前环境: {currentEnv}");
```

---

## 🏗️ IYDataManager - 运行时多数据库管理专家

### 🎯 核心特点

- **运行时管理** - 应用运行期间动态注册和切换数据库
- **内存管理** - 数据库连接池和生命周期管理
- **多连接支持** - 同时管理多个数据库连接
- **临时切换** - 不修改配置文件的临时数据库操作

### 📋 适用场景

#### ✅ 推荐使用场景

1. **多租户系统**
   ```csharp
   // 为每个租户动态注册数据库
   public async Task<IActionResult> SwitchTenant(string tenantId)
   {
       var connectionString = $"Data Source=tenant_{tenantId}.db";
       YData.RegisterDatabase(tenantId, connectionString, YDataType.Sqlite);
       YData.UseDatabase(tenantId);
       
       var users = await YData.Select<User>().ToListAsync();
       return Ok(users);
   }
   ```

2. **微服务架构**
   ```csharp
   // 每个服务管理自己的数据库连接
   public class OrderService
   {
       public void Initialize()
       {
           YData.RegisterDatabase("orders", "Data Source=orders.db", YDataType.Sqlite);
           YData.RegisterDatabase("inventory", "Data Source=inventory.db", YDataType.Sqlite);
       }
       
       public async Task ProcessOrder(Order order)
       {
           // 切换到订单数据库
           YData.UseDatabase("orders");
           await YData.InsertAsync(order);
           
           // 切换到库存数据库
           YData.UseDatabase("inventory");
           await UpdateInventory(order.ProductId, order.Quantity);
       }
   }
   ```

3. **数据迁移工具**
   ```csharp
   // 同时连接源数据库和目标数据库
   YData.RegisterDatabase("source", sourceConnectionString, YDataType.SqlServer);
   YData.RegisterDatabase("target", targetConnectionString, YDataType.Sqlite);
   
   // 从源数据库读取数据
   YData.UseDatabase("source");
   var sourceData = await YData.Select<User>().ToListAsync();
   
   // 写入目标数据库
   YData.UseDatabase("target");
   await YData.InsertAsync(sourceData);
   ```

4. **读写分离**
   ```csharp
   // 注册读库和写库
   YData.RegisterDatabase("read", readConnectionString, YDataType.SqlServer);
   YData.RegisterDatabase("write", writeConnectionString, YDataType.SqlServer);
   
   // 读操作使用读库
   public async Task<List<User>> GetUsers()
   {
       YData.UseDatabase("read");
       return await YData.Select<User>().ToListAsync();
   }
   
   // 写操作使用写库
   public async Task CreateUser(User user)
   {
       YData.UseDatabase("write");
       await YData.InsertAsync(user);
   }
   ```

#### ❌ 不推荐使用场景

- **简单的环境切换** - YConfigHelper 更适合
- **配置持久化** - 应用重启后需要重新注册
- **部署配置管理** - 不如配置文件方式直观

### 🚀 使用示例

#### 基础使用
```csharp
// 1. 注册数据库
YData.RegisterDatabase("main", "Data Source=main.db", YDataType.Sqlite);
YData.RegisterDatabase("logs", "Data Source=logs.db", YDataType.Sqlite);

// 2. 切换到主数据库
YData.UseDatabase("main");
var user = new User { Name = "主数据库用户" };
await YData.InsertAsync(user);

// 3. 切换到日志数据库
YData.UseDatabase("logs");
var log = new Log { Message = "用户创建日志" };
await YData.InsertAsync(log);
```

#### 高级使用
```csharp
// 1. 批量注册数据库
var databases = new Dictionary<string, (string ConnectionString, YDataType Type)>
{
    ["tenant1"] = ("Data Source=tenant1.db", YDataType.Sqlite),
    ["tenant2"] = ("Data Source=tenant2.db", YDataType.Sqlite),
    ["shared"] = ("Server=localhost;Database=Shared;Integrated Security=true;", YDataType.SqlServer)
};

foreach (var db in databases)
{
    YData.RegisterDatabase(db.Key, db.Value.ConnectionString, db.Value.Type);
}

// 2. 动态切换和操作
public async Task ProcessTenantData(string tenantId, object data)
{
    // 切换到租户数据库
    YData.UseDatabase(tenantId);
    await YData.InsertAsync(data);
    
    // 同步到共享数据库
    YData.UseDatabase("shared");
    await YData.InsertAsync(CreateSyncRecord(tenantId, data));
}
```

---

## 🔄 组合使用策略

### 🎯 最佳实践：两者结合使用

```csharp
public class Startup
{
    public void ConfigureServices(IServiceCollection services)
    {
        // 1. 使用 YConfigHelper 设置基础环境
        var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
        YConfigHelper.SwitchEnvironment(environment ?? "Development");
        
        // 2. 配置 YData 基础连接
        services.AddYData(options =>
        {
            options.ConfigureAuto(); // 读取配置文件
        });
    }
}

public class TenantService
{
    public async Task InitializeTenants()
    {
        // 3. 使用 IYDataManager 注册租户数据库
        var tenants = await GetAllTenants();
        foreach (var tenant in tenants)
        {
            var connectionString = $"Data Source=tenant_{tenant.Id}.db";
            YData.RegisterDatabase(tenant.Id, connectionString, YDataType.Sqlite);
        }
    }
    
    public async Task SwitchToTenant(string tenantId)
    {
        // 4. 运行时切换租户
        YData.UseDatabase(tenantId);
    }
}
```

### 📊 使用场景决策表

| 场景 | YConfigHelper | IYDataManager | 推荐方案 |
|------|---------------|---------------|----------|
| 开发环境切换 | ✅ 优秀 | ❌ 复杂 | YConfigHelper |
| 生产部署配置 | ✅ 优秀 | ❌ 不持久 | YConfigHelper |
| 多租户系统 | ❌ 全局影响 | ✅ 优秀 | IYDataManager |
| 读写分离 | ❌ 不支持 | ✅ 优秀 | IYDataManager |
| 数据迁移 | ❌ 单连接 | ✅ 优秀 | IYDataManager |
| SQLite 快速开发 | ✅ 专门优化 | ⚠️ 一般 | YConfigHelper |
| 微服务架构 | ⚠️ 一般 | ✅ 优秀 | IYDataManager |
| 临时数据操作 | ❌ 重量级 | ✅ 轻量级 | IYDataManager |

---

## 🚀 实际应用示例

### 示例1：电商多租户系统

```csharp
public class ECommerceSystem
{
    public async Task Initialize()
    {
        // 使用 YConfigHelper 设置基础环境
        YConfigHelper.SwitchEnvironment("Production");
        YData.ConfigureAuto();
        
        // 使用 IYDataManager 注册租户数据库
        var tenants = await GetTenants();
        foreach (var tenant in tenants)
        {
            YData.RegisterDatabase(
                tenant.Id, 
                $"Data Source=tenant_{tenant.Id}.db", 
                YDataType.Sqlite
            );
        }
        
        // 注册共享数据库
        YData.RegisterDatabase(
            "shared", 
            "Server=prod-server;Database=Shared;Integrated Security=true;", 
            YDataType.SqlServer
        );
    }
    
    public async Task ProcessOrder(string tenantId, Order order)
    {
        // 切换到租户数据库处理订单
        YData.UseDatabase(tenantId);
        await YData.InsertAsync(order);
        
        // 切换到共享数据库记录统计
        YData.UseDatabase("shared");
        await YData.InsertAsync(new OrderStatistics 
        { 
            TenantId = tenantId, 
            OrderId = order.Id, 
            Amount = order.TotalAmount 
        });
    }
}
```

### 示例2：开发团队环境管理

```csharp
public class DevelopmentEnvironmentManager
{
    public static void SetupDeveloperEnvironment(string developerName)
    {
        // 为每个开发者创建独立的开发环境
        YConfigHelper.CreateSQLiteConfig($"dev_{developerName}");
        YData.ConfigureAuto();
        
        Console.WriteLine($"开发环境已为 {developerName} 配置完成");
    }
    
    public static void SwitchToTestingEnvironment()
    {
        // 切换到测试环境
        YConfigHelper.SwitchEnvironment("Testing");
        YData.ConfigureAuto();
        
        Console.WriteLine("已切换到测试环境");
    }
    
    public static void PrepareForProduction()
    {
        // 准备生产环境配置
        YConfigHelper.SwitchEnvironment("Production");
        
        // 备份当前配置
        var backupFile = YConfigHelper.BackupConfig();
        Console.WriteLine($"配置已备份到: {backupFile}");
        
        YData.ConfigureAuto();
        Console.WriteLine("生产环境配置完成");
    }
}
```

---

## 📝 总结

### 🎯 选择指南

- **🔧 选择 YConfigHelper** 当你需要：
  - 环境配置管理
  - SQLite 快速开发
  - 部署脚本自动化
  - 配置文件持久化

- **🏗️ 选择 IYDataManager** 当你需要：
  - 多租户数据库管理
  - 运行时动态切换
  - 多连接同时使用
  - 微服务架构支持

- **🔄 组合使用** 当你需要：
  - 复杂的企业级应用
  - 既有环境管理又有运行时切换
  - 最大化灵活性和功能

### 💡 最佳实践

1. **启动时使用 YConfigHelper** 设置基础环境
2. **运行时使用 IYDataManager** 处理动态需求
3. **根据具体场景选择合适的工具**
4. **充分利用两者的组合优势**

通过合理使用这两种数据库切换机制，你可以构建出既灵活又高效的数据访问层！🎉
