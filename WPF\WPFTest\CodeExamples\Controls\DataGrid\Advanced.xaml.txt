<!-- DataGrid 高级用法示例 -->
<StackPanel Margin="20" Spacing="15">

    <!-- 可编辑 DataGrid -->
    <GroupBox Header="可编辑 DataGrid" Padding="15">
        <StackPanel>
            <!-- 控制面板 -->
            <Border Background="{DynamicResource CardBackgroundFillColorSecondaryBrush}"
                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="6"
                    Padding="12"
                    Margin="0,0,0,16">
                <WrapPanel>
                    <ui:Button Content="添加员工"
                               Command="{Binding AddEmployeeCommand}"
                               Appearance="Primary"
                               Margin="0,0,8,0"/>
                    <ui:Button Content="删除选中"
                               Command="{Binding DeleteSelectedEmployeeCommand}"
                               Appearance="Danger"
                               Margin="0,0,8,0"/>
                    <ToggleButton Content="编辑模式"
                                  IsChecked="{Binding IsEditModeEnabled, Mode=TwoWay}"
                                  Command="{Binding ToggleEditModeCommand}"/>
                </WrapPanel>
            </Border>

            <!-- 可编辑数据表格 -->
            <DataGrid ItemsSource="{Binding ProductData}"
                      SelectedItem="{Binding SelectedProduct, Mode=TwoWay}"
                      Style="{StaticResource ModernDataGridStyle}"
                      Height="200"
                      AutoGenerateColumns="False"
                      CanUserAddRows="{Binding IsEditModeEnabled}"
                      CanUserDeleteRows="{Binding IsEditModeEnabled}"
                      IsReadOnly="{Binding IsEditModeEnabled, Converter={StaticResource InverseBooleanConverter}}"
                      GridLinesVisibility="All"
                      HeadersVisibility="All">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="ID" Binding="{Binding Id}" Width="60" IsReadOnly="True"/>
                    <DataGridTextColumn Header="产品名称" Binding="{Binding Name}" Width="150"/>
                    <DataGridTextColumn Header="分类" Binding="{Binding Category}" Width="100"/>
                    <DataGridTextColumn Header="价格" Binding="{Binding Price, StringFormat=C}" Width="100"/>
                    <DataGridTextColumn Header="库存" Binding="{Binding Stock}" Width="80"/>
                    <DataGridCheckBoxColumn Header="启用" Binding="{Binding IsActive}" Width="60"/>
                </DataGrid.Columns>
            </DataGrid>
        </StackPanel>
    </GroupBox>

    <!-- 自定义列模板 -->
    <GroupBox Header="自定义列模板" Padding="15">
        <DataGrid ItemsSource="{Binding EmployeeData}"
                  Style="{StaticResource ModernDataGridStyle}"
                  Height="150"
                  AutoGenerateColumns="False"
                  IsReadOnly="True">
            <DataGrid.Columns>
                <!-- 图标列 -->
                <DataGridTemplateColumn Header="状态" Width="60">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <ui:SymbolIcon Symbol="Person24" 
                                           FontSize="16"
                                           Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                           HorizontalAlignment="Center"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>

                <!-- 文本列 -->
                <DataGridTextColumn Header="姓名" Binding="{Binding Name}" Width="100"/>
                <DataGridTextColumn Header="部门" Binding="{Binding Department}" Width="100"/>

                <!-- 自定义模板列 -->
                <DataGridTemplateColumn Header="薪资等级" Width="120">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Border Background="{DynamicResource AccentFillColorDefaultBrush}"
                                    CornerRadius="12"
                                    Padding="8,4">
                                <TextBlock Text="{Binding Salary, Converter={StaticResource SalaryToLevelConverter}}"
                                           FontSize="10"
                                           Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                           HorizontalAlignment="Center"/>
                            </Border>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>

                <!-- 操作按钮列 -->
                <DataGridTemplateColumn Header="操作" Width="100">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <ui:Button Content="编辑"
                                           Command="{Binding DataContext.EditEmployeeCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                           CommandParameter="{Binding}"
                                           Appearance="Secondary"
                                           FontSize="10"
                                           Padding="6,2"
                                           Margin="2"/>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>
    </GroupBox>

</StackPanel>
