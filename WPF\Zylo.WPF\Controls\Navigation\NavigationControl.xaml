<UserControl x:Class="Zylo.WPF.Controls.Navigation.NavigationControl"
             x:Name="NavigationControlRoot"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
             xmlns:converters="clr-namespace:Zylo.WPF.Converters"
             xmlns:controls="clr-namespace:Zylo.WPF.Controls"
             xmlns:navigation="clr-namespace:Zylo.WPF.Behaviors.Navigation"
             xmlns:navigation1="clr-namespace:Zylo.WPF.Controls.Navigation"
             mc:Ignorable="d"
             d:DataContext="{d:DesignInstance navigation1:NavigationControl}"
             d:DesignHeight="600" d:DesignWidth="800">

    <!-- 不使用ViewModel，直接绑定到UserControl的依赖属性 -->

    <UserControl.Resources>
        <!-- 🎨 引入分类的样式和模板资源 -->
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- 🎨 导航控件样式 -->
                <ResourceDictionary Source="../../Resources/Navigation/NavigationControlStyles.xaml" />
                <!-- 🎨 导航控件模板 -->
                <ResourceDictionary Source="../../Resources/Navigation/NavigationControlTemplates.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- 转换器 -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
            <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter" />
            <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter" />
            <!-- IconConverter已在NavigationControlStyles.xaml中定义 -->
        </ResourceDictionary>








    </UserControl.Resources>

    <Grid Background="{DynamicResource ApplicationBackgroundBrush}"
          Width="{Binding NavigationControlWidth, ElementName=NavigationControlRoot}">
        <!-- 不需要列定义，直接让左右内容占满整个 Grid -->

        <!-- 左侧：HyperlinkButton + 两个ListBox -->
        <Border
                Background="{DynamicResource ControlFillColorDefaultBrush}"
                BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                BorderThickness="0,0,0,0"
                Visibility="{Binding IsLeftVisible, ElementName=NavigationControlRoot, Converter={StaticResource BooleanToVisibilityConverter}}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <!-- HyperlinkButton -->
                <Border Grid.Row="0"
                        BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                        VerticalAlignment="Center"
                        HorizontalAlignment="Center"
                        BorderThickness="0,0,0,0">
                    <Button x:Name="TopNavigationButton"
                            Style="{StaticResource NavigationButtonStyle}"
                            HorizontalAlignment="Left"
                            Command="{Binding ToggleDisplayCommand, ElementName=NavigationControlRoot}"
                            Visibility="{Binding ShowToggleButton, ElementName=NavigationControlRoot, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <ui:SymbolIcon Symbol="Navigation24"
                                       FontSize="20"
                                       Foreground="{DynamicResource TextFillColorPrimaryBrush}" />
                    </Button>
                </Border>

                <!-- 查找 -->
                <Border Grid.Row="1"
                        Padding="2,6"
                        VerticalAlignment="Center"
                        HorizontalAlignment="Center"
                        BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                        BorderThickness="0,0,0,0">
                    <Button Style="{StaticResource NavigationButtonStyle}"
                            Command="{Binding ToggleDisplayCommand, ElementName=NavigationControlRoot}"
                            HorizontalAlignment="Left">
                        <ui:SymbolIcon Symbol="Search24"
                                       FontSize="20"
                                       Foreground="{DynamicResource TextFillColorPrimaryBrush}" />
                    </Button>
                </Border>

                <!-- 第一个ListBox -->
                <Border Grid.Row="2"
                        BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                        BorderThickness="0,0,0,1">
                    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="4,1">
                        <ui:ListView x:Name="TopListView"
                                     ItemsSource="{Binding TopListItems, ElementName=NavigationControlRoot}"
                                     ItemContainerStyle="{StaticResource NavigationListViewItemStyle}"
                                     ItemTemplate="{StaticResource NavigationListViewItemTemplate}"
                                     Background="Transparent"
                                     BorderThickness="0"
                                     Width="40"
                                     HorizontalAlignment="Left">

                            <i:Interaction.Behaviors>
                                <navigation:ListViewSelectionBehavior
                                    SelectedItem="{Binding SelectedListItem, ElementName=NavigationControlRoot,Mode=TwoWay}"
                                    SelectionCommand="{Binding InternalNavigationItemSelectedCommand, ElementName=NavigationControlRoot}"
                                    PassSelectedItemAsParameter="True" />
                            </i:Interaction.Behaviors>
                        </ui:ListView>
                    </ScrollViewer>
                </Border>

                <!-- 第二个ListBox -->
                <Border Grid.Row="3">
                    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="4,1">
                        <ui:ListView x:Name="SecondListView"
                                     ItemsSource="{Binding BottomListItems, ElementName=NavigationControlRoot}"
                                     ItemContainerStyle="{StaticResource NavigationListViewItemStyle}"
                                     ItemTemplate="{StaticResource NavigationListViewItemTemplate}"
                                     Background="Transparent"
                                     BorderThickness="0"
                                     Width="40"
                                     HorizontalAlignment="Left">
                            <i:Interaction.Behaviors>
                                <navigation:ListViewSelectionBehavior
                                    SelectedItem="{Binding SelectedListItem, ElementName=NavigationControlRoot,Mode=TwoWay}"
                                    SelectionCommand="{Binding InternalNavigationItemSelectedCommand, ElementName=NavigationControlRoot}"
                                    PassSelectedItemAsParameter="True" />
                            </i:Interaction.Behaviors>
                        </ui:ListView>
                    </ScrollViewer>
                </Border>
            </Grid>
        </Border>

        <!-- 右侧：搜索和TreeView -->
        <Border
                Background="{DynamicResource ControlFillColorDefaultBrush}"
                BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                BorderThickness="0"
                Visibility="{Binding IsLeftVisible, ElementName=NavigationControlRoot, Converter={StaticResource InverseBooleanToVisibilityConverter}, ConverterParameter=Inverse}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>
                <!-- 右边内容占满整个宽度，不需要列定义 -->

                <!-- 右侧Header -->
                <Border Grid.Row="0"
                        Padding="2,0"
                        BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                        BorderThickness="0,0,0,0">
                    <Button x:Name="RightNavigationButton"
                            Style="{StaticResource NavigationButtonStyle}"
                            HorizontalAlignment="Left"
                            Command="{Binding ToggleDisplayCommand, ElementName=NavigationControlRoot}"
                            Visibility="{Binding ShowToggleButton, ElementName=NavigationControlRoot, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <ui:SymbolIcon Symbol="Navigation24"
                                       FontSize="20"
                                       Foreground="{DynamicResource TextFillColorPrimaryBrush}" />
                    </Button>
                </Border>

                <!-- AutoSuggestBox搜索 -->
                <Border Grid.Row="1"
                        Padding="4,4"
                        BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                        BorderThickness="0,0,0,0"
                        Visibility="{Binding ShowSearchBox, ElementName=NavigationControlRoot, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <ui:AutoSuggestBox x:Name="SearchBox"
                                       PlaceholderText="🔍 搜索导航项..."
                                       Text="{Binding SearchText, ElementName=NavigationControlRoot, UpdateSourceTrigger=PropertyChanged}"
                                       Icon="{ui:SymbolIcon Search16}"
                                       MaxHeight="36">
                        <i:Interaction.Triggers>
                            <i:EventTrigger EventName="TextChanged">
                                <i:InvokeCommandAction Command="{Binding InternalSearchTextChangedCommand, ElementName=NavigationControlRoot}"
                                                       CommandParameter="{Binding Text, ElementName=SearchBox}" />
                            </i:EventTrigger>
                        </i:Interaction.Triggers>
                    </ui:AutoSuggestBox>
                </Border>

                <!-- 第一个TreeView -->
                <Border Grid.Row="2"
                        BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                        BorderThickness="0,0,0,1">
                    <ScrollViewer VerticalScrollBarVisibility="Auto"
                                  Padding="8,8">
                        <TreeView x:Name="TopTreeView"
                                  ItemsSource="{Binding TopTreeItems, ElementName=NavigationControlRoot}"
                                  ItemContainerStyle="{StaticResource NavigationTreeViewItemStyle}"
                                  Background="Transparent"
                                  BorderThickness="0"
                                  AllowDrop="{Binding EnableDragDrop, ElementName=NavigationControlRoot}">

                            <i:Interaction.Behaviors>
                                <navigation:TreeViewSelectionBehavior
                                    SelectedItem="{Binding SelectedTreeItem, ElementName=NavigationControlRoot,Mode=TwoWay}"
                                    SelectionCommand="{Binding InternalNavigationItemSelectedCommand, ElementName=NavigationControlRoot}"
                                    PassSelectedItemAsParameter="True" />
                            </i:Interaction.Behaviors>

                            <TreeView.ItemTemplate>
                                <StaticResource ResourceKey="NavigationTreeViewItemTemplate" />
                            </TreeView.ItemTemplate>
                        </TreeView>
                    </ScrollViewer>
                </Border>

                <!-- 第二个TreeView -->
                <Border Grid.Row="3">
                    <ScrollViewer VerticalScrollBarVisibility="Auto"
                                  Padding="8,8">
                        <TreeView x:Name="SecondTreeView"
                                  ItemsSource="{Binding BottomTreeItems, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                  ItemContainerStyle="{StaticResource NavigationTreeViewItemStyle}"
                                  Background="Transparent"
                                  BorderThickness="0"
                                  AllowDrop="{Binding EnableDragDrop, ElementName=NavigationControlRoot}">

                            <i:Interaction.Behaviors>
                                <navigation:TreeViewSelectionBehavior
                                    SelectedItem="{Binding SelectedTreeItem, ElementName=NavigationControlRoot,Mode=TwoWay}"
                                    SelectionCommand="{Binding InternalNavigationItemSelectedCommand, ElementName=NavigationControlRoot}"
                                    PassSelectedItemAsParameter="True" />
                            </i:Interaction.Behaviors>

                            <TreeView.ItemTemplate>
                                <StaticResource ResourceKey="NavigationTreeViewItemTemplate" />
                            </TreeView.ItemTemplate>
                        </TreeView>
                    </ScrollViewer>
                </Border>
            </Grid>
        </Border>

        <!-- 子菜单弹出窗口 -->
        <Popup x:Name="SubMenuPopup"
               Placement="Right"
               PlacementTarget="{Binding ElementName=NavigationControlRoot}"
               AllowsTransparency="True"
               StaysOpen="False"
               PopupAnimation="Slide">
            <Border Background="{DynamicResource ControlFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="4"
                    Padding="4">
                <Border.Effect>
                    <DropShadowEffect Color="Black"
                                      Opacity="0.2"
                                      ShadowDepth="2"
                                      BlurRadius="8"/>
                </Border.Effect>
                <ItemsControl x:Name="SubMenuItemsControl"
                              ItemsSource="{Binding SubMenuItems, ElementName=NavigationControlRoot}">
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Button Background="Transparent"
                                    BorderThickness="0"
                                    Padding="12,8"
                                    Margin="0,2"
                                    MinWidth="280"
                                    HorizontalContentAlignment="Stretch"
                                    Command="{Binding DataContext.SubMenuItemClickCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                    CommandParameter="{Binding}">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Background" Value="Transparent"/>
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="Button">
                                                    <Border Background="{TemplateBinding Background}"
                                                            CornerRadius="4"
                                                            Padding="{TemplateBinding Padding}">
                                                        <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                                                    </Border>
                                                    <ControlTemplate.Triggers>
                                                        <Trigger Property="IsMouseOver" Value="True">
                                                            <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}"/>
                                                        </Trigger>
                                                    </ControlTemplate.Triggers>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </Button.Style>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- 图标 -->
                                    <ContentPresenter Grid.Column="0"
                                                      Width="24"
                                                      Height="24"
                                                      Margin="0,0,12,0"
                                                      VerticalAlignment="Center">
                                        <ContentPresenter.Content>
                                            <MultiBinding>
                                                <MultiBinding.Converter>
                                                    <StaticResource ResourceKey="SimpleIconConverter" />
                                                </MultiBinding.Converter>
                                                <Binding Path="WpfUiSymbol" />
                                                <Binding Path="ZyloSymbol" />
                                                <Binding Path="Emoji" />
                                            </MultiBinding>
                                        </ContentPresenter.Content>
                                    </ContentPresenter>

                                    <!-- 标题和描述 -->
                                    <StackPanel Grid.Column="1"
                                                VerticalAlignment="Center">
                                        <TextBlock Text="{Binding Name}"
                                                   FontSize="14"
                                                   FontWeight="Medium"
                                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}" />
                                        <TextBlock Text="{Binding Description}"
                                                   FontSize="12"
                                                   Margin="0,2,0,0"
                                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                   TextWrapping="Wrap"
                                                   Visibility="{Binding Description, Converter={StaticResource StringToVisibilityConverter}}" />
                                    </StackPanel>

                                    <!-- 右箭头 -->
                                    <ui:SymbolIcon Grid.Column="2"
                                                   Symbol="ChevronRight24"
                                                   FontSize="16"
                                                   Foreground="{DynamicResource TextFillColorTertiaryBrush}"
                                                   VerticalAlignment="Center"/>
                                </Grid>
                            </Button>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </Border>
        </Popup>
    </Grid>
</UserControl>