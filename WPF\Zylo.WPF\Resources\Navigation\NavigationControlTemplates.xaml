<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
                    xmlns:converters="clr-namespace:Zylo.WPF.Converters">

    <!-- 🎨 NavigationControl 数据模板资源字典 -->
    <!-- ================================================================ -->
    <!-- 📋 功能说明：                                                      -->
    <!-- • 包含所有导航控件相关的数据模板和内容模板                            -->
    <!-- • ListView 模式：纯图标显示（适合 48px 宽度）                       -->
    <!-- • TreeView 模式：图标+文字显示（适合宽屏显示）                       -->
    <!-- • 统一的图标转换器：支持多种图标类型                                 -->
    <!-- ================================================================ -->

    <!-- ================================ -->
    <!-- 🔧 依赖资源 -->
    <!-- ================================ -->
    
    <!-- 引入样式资源 -->
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="NavigationControlStyles.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <!-- ================================================================ -->
    <!-- 📋 ListView 数据模板 - 左侧图标导航                                   -->
    <!-- ================================================================ -->
    <!--
    设计目标：
    • 纯图标显示：适合 48px 窄宽度
    • 居中对齐：美观整洁
    • 多图标支持：ZyloIcon、WpfUiSymbol、FontIcon
    • 20px 图标尺寸：清晰可见
    -->

    <!-- ListView 项目数据模板 - 图标显示 -->
    <DataTemplate x:Key="NavigationListViewItemTemplate">
        <!-- 图标显示 -->
        <ContentPresenter Width="{StaticResource NavigationListIconSize}"
                          Height="{StaticResource NavigationListIconSize}"
                          HorizontalAlignment="Center"
                          VerticalAlignment="Center">
            <ContentPresenter.Content>
                <MultiBinding>
                    <MultiBinding.Converter>
                        <StaticResource ResourceKey="SimpleIconConverter" />
                    </MultiBinding.Converter>
                    <Binding Path="WpfUiSymbol" />
                    <Binding Path="ZyloSymbol" />
                    <Binding Path="Emoji" />
                </MultiBinding>
            </ContentPresenter.Content>
        </ContentPresenter>
    </DataTemplate>

    <!-- ================================================================ -->
    <!-- 🌳 TreeView 数据模板 - 右侧详细导航                                   -->
    <!-- ================================================================ -->
    <!--
    设计目标：
    • 图标+文字：完整信息展示
    • 层级结构：支持 Children 嵌套
    • 水平布局：图标在左，文字在右
    • 适合宽屏：充分利用可用空间
    -->

    <!-- TreeView 项目数据模板 - 图标+文字 -->
    <HierarchicalDataTemplate x:Key="NavigationTreeViewItemTemplate" 
                              ItemsSource="{Binding Children}">
        <Border Background="Transparent"
                CornerRadius="0"
                Padding="{StaticResource NavigationTreeItemPadding}"
                Margin="0">
            <StackPanel Orientation="Horizontal">
                <!-- 图标显示 -->
                <ContentPresenter Width="{StaticResource NavigationTreeIconSize}"
                                  Height="{StaticResource NavigationTreeIconSize}"
                                  Margin="0,0,6,0"
                                  VerticalAlignment="Center">
                    <ContentPresenter.Content>
                        <MultiBinding>
                            <MultiBinding.Converter>
                                <StaticResource ResourceKey="SimpleIconConverter" />
                            </MultiBinding.Converter>
                            <Binding Path="WpfUiSymbol" />
                            <Binding Path="ZyloSymbol" />
                            <Binding Path="Emoji" />
                        </MultiBinding>
                    </ContentPresenter.Content>
                </ContentPresenter>

                <!-- 文字显示 -->
                <TextBlock Text="{Binding Name}"
                           VerticalAlignment="Center"
                           FontSize="12"
                           Foreground="{DynamicResource TextFillColorPrimaryBrush}" />
            </StackPanel>
        </Border>
    </HierarchicalDataTemplate>

    <!-- ================================ -->
    <!-- 🔍 搜索框样式 -->
    <!-- ================================ -->
    
    <!-- 搜索框样式 -->
    <Style x:Key="NavigationSearchBoxStyle" TargetType="ui:AutoSuggestBox">
        <Setter Property="PlaceholderText" Value="搜索子菜单..." />
        <Setter Property="Icon" Value="{ui:SymbolIcon Search16}" />
        <Setter Property="MaxHeight" Value="36" />
        <Setter Property="Margin" Value="8,4" />
        <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}" />
        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}" />
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}" />
    </Style>

    <!-- ================================ -->
    <!-- 📐 布局模板 -->
    <!-- ================================ -->
    
    <!-- 导航面板布局模板 -->
    <ControlTemplate x:Key="NavigationPanelTemplate" TargetType="ContentControl">
        <Border Background="{DynamicResource ApplicationBackgroundBrush}"
                BorderBrush="{DynamicResource DividerStrokeColorDefaultBrush}"
                BorderThickness="0,0,0,0"
                CornerRadius="0">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <!-- 顶部导航区域 -->
                <ContentPresenter x:Name="TopNavigationPresenter"
                                  Grid.Row="0"
                                  Content="{Binding TopNavigationContent, RelativeSource={RelativeSource TemplatedParent}}" />

                <!-- 上方列表区域 -->
                <ContentPresenter x:Name="TopListPresenter"
                                  Grid.Row="1"
                                  Content="{Binding TopListContent, RelativeSource={RelativeSource TemplatedParent}}" />

                <!-- 搜索区域 -->
                <ContentPresenter x:Name="SearchPresenter"
                                  Grid.Row="2"
                                  Content="{Binding SearchContent, RelativeSource={RelativeSource TemplatedParent}}" />

                <!-- 下方列表区域 -->
                <ContentPresenter x:Name="BottomListPresenter"
                                  Grid.Row="3"
                                  Content="{Binding BottomListContent, RelativeSource={RelativeSource TemplatedParent}}" />

                <!-- 底部导航区域 -->
                <ContentPresenter x:Name="BottomNavigationPresenter"
                                  Grid.Row="4"
                                  Content="{Binding BottomNavigationContent, RelativeSource={RelativeSource TemplatedParent}}" />
            </Grid>
        </Border>
    </ControlTemplate>

    <!-- ================================ -->
    <!-- 🎯 图标按钮模板 -->
    <!-- ================================ -->
    
    <!-- 导航图标按钮模板 -->
    <ControlTemplate x:Key="NavigationIconButtonTemplate" TargetType="Button">
        <Border x:Name="ButtonBorder"
                Background="{TemplateBinding Background}"
                BorderBrush="Transparent"
                BorderThickness="0"
                CornerRadius="0"
                Width="{StaticResource NavigationListItemWidth}"
                Height="{StaticResource NavigationListItemHeight}">
            <ui:SymbolIcon x:Name="ButtonIcon"
                           Symbol="{Binding Tag, RelativeSource={RelativeSource TemplatedParent}}"
                           FontSize="{StaticResource NavigationListIconSize}"
                           Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center" />
        </Border>

        <ControlTemplate.Triggers>
            <!-- 悬停效果 -->
            <Trigger Property="IsMouseOver" Value="True">
                <Setter TargetName="ButtonBorder" Property="Background" 
                        Value="{DynamicResource ControlStrongFillColorDefaultBrush}" />
                <Setter TargetName="ButtonBorder" Property="BorderBrush" 
                        Value="{DynamicResource ControlStrokeColorDefaultBrush}" />
                <Setter TargetName="ButtonBorder" Property="BorderThickness" Value="1" />
            </Trigger>

            <!-- 按下效果 -->
            <Trigger Property="IsPressed" Value="True">
                <Setter TargetName="ButtonBorder" Property="Background" 
                        Value="{DynamicResource ControlStrongFillColorDefaultBrush}" />
            </Trigger>

            <!-- 禁用状态 -->
            <Trigger Property="IsEnabled" Value="False">
                <Setter TargetName="ButtonBorder" Property="Opacity" Value="0.5" />
                <Setter TargetName="ButtonIcon" Property="Foreground" 
                        Value="{DynamicResource TextFillColorDisabledBrush}" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!-- ================================ -->
    <!-- 🎨 主题适应样式 -->
    <!-- ================================ -->
    
    <!-- 分隔线样式 -->
    <Style x:Key="NavigationSeparatorStyle" TargetType="Rectangle">
        <Setter Property="Fill" Value="{DynamicResource DividerStrokeColorDefaultBrush}" />
        <Setter Property="Height" Value="1" />
        <Setter Property="Margin" Value="8,4" />
        <Setter Property="HorizontalAlignment" Value="Stretch" />
    </Style>

    <!-- 标题文本样式 -->
    <Style x:Key="NavigationTitleStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="14" />
        <Setter Property="FontWeight" Value="SemiBold" />
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}" />
        <Setter Property="Margin" Value="8,8,8,4" />
        <Setter Property="HorizontalAlignment" Value="Left" />
        <Setter Property="VerticalAlignment" Value="Center" />
    </Style>

    <!-- 副标题文本样式 -->
    <Style x:Key="NavigationSubtitleStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="12" />
        <Setter Property="FontWeight" Value="Normal" />
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorSecondaryBrush}" />
        <Setter Property="Margin" Value="8,2,8,4" />
        <Setter Property="HorizontalAlignment" Value="Left" />
        <Setter Property="VerticalAlignment" Value="Center" />
    </Style>

</ResourceDictionary>
