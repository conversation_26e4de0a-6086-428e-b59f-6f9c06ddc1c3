using System;
using System.IO;
using System.Threading.Tasks;
using Zylo.WPF.Controls.YFile;

namespace WPFTest.Tests
{
    /// <summary>
    /// ArchiveService 压缩解压功能测试
    /// </summary>
    public static class ArchiveServiceTest
    {
        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static async Task RunAllTests()
        {
            Console.WriteLine("🧪 开始 ArchiveService 测试...");
            
            try
            {
                await TestZipCreation();
                await TestZipExtraction();
                await TestChineseFilenames();
                TestFileFormatDetection();
                
                Console.WriteLine("✅ 所有测试通过！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 测试 ZIP 压缩功能
        /// </summary>
        private static async Task TestZipCreation()
        {
            Console.WriteLine("📦 测试 ZIP 压缩功能...");
            
            var testDir = Path.Combine(Path.GetTempPath(), "ArchiveTest");
            Directory.CreateDirectory(testDir);
            
            try
            {
                // 创建测试文件
                var testFile1 = Path.Combine(testDir, "test1.txt");
                var testFile2 = Path.Combine(testDir, "测试文件2.txt");
                
                await File.WriteAllTextAsync(testFile1, "This is test file 1");
                await File.WriteAllTextAsync(testFile2, "这是测试文件2的内容");
                
                // 压缩文件
                var zipPath = Path.Combine(testDir, "test.zip");
                var filePaths = new[] { testFile1, testFile2 };
                
                var success = await ArchiveService.Create360ZipAsync(filePaths, zipPath);
                
                if (!success)
                    throw new Exception("ZIP 压缩失败");
                
                if (!File.Exists(zipPath))
                    throw new Exception("ZIP 文件未创建");
                
                Console.WriteLine("✅ ZIP 压缩测试通过");
            }
            finally
            {
                // 清理测试文件
                if (Directory.Exists(testDir))
                    Directory.Delete(testDir, true);
            }
        }

        /// <summary>
        /// 测试 ZIP 解压功能
        /// </summary>
        private static async Task TestZipExtraction()
        {
            Console.WriteLine("🗜️ 测试 ZIP 解压功能...");
            
            var testDir = Path.Combine(Path.GetTempPath(), "ArchiveTest");
            Directory.CreateDirectory(testDir);
            
            try
            {
                // 创建测试文件并压缩
                var testFile = Path.Combine(testDir, "original.txt");
                await File.WriteAllTextAsync(testFile, "Original content");
                
                var zipPath = Path.Combine(testDir, "test.zip");
                var success = await ArchiveService.Create360ZipAsync(new[] { testFile }, zipPath);
                
                if (!success)
                    throw new Exception("创建测试 ZIP 失败");
                
                // 删除原文件
                File.Delete(testFile);
                
                // 解压测试
                success = await ArchiveService.ExtractHereAsync(zipPath);
                
                if (!success)
                    throw new Exception("ZIP 解压失败");
                
                if (!File.Exists(testFile))
                    throw new Exception("解压后文件不存在");
                
                var content = await File.ReadAllTextAsync(testFile);
                if (content != "Original content")
                    throw new Exception("解压后文件内容不正确");
                
                Console.WriteLine("✅ ZIP 解压测试通过");
            }
            finally
            {
                // 清理测试文件
                if (Directory.Exists(testDir))
                    Directory.Delete(testDir, true);
            }
        }

        /// <summary>
        /// 测试中文文件名支持
        /// </summary>
        private static async Task TestChineseFilenames()
        {
            Console.WriteLine("🀄 测试中文文件名支持...");
            
            var testDir = Path.Combine(Path.GetTempPath(), "中文测试目录");
            Directory.CreateDirectory(testDir);
            
            try
            {
                // 创建中文文件名的测试文件
                var chineseFile = Path.Combine(testDir, "中文文件名测试.txt");
                await File.WriteAllTextAsync(chineseFile, "中文内容测试");
                
                // 压缩
                var zipPath = Path.Combine(testDir, "中文压缩包.zip");
                var success = await ArchiveService.Create360ZipAsync(new[] { chineseFile }, zipPath);
                
                if (!success)
                    throw new Exception("中文文件名压缩失败");
                
                // 删除原文件
                File.Delete(chineseFile);
                
                // 解压
                success = await ArchiveService.ExtractHereAsync(zipPath);
                
                if (!success)
                    throw new Exception("中文文件名解压失败");
                
                if (!File.Exists(chineseFile))
                    throw new Exception("解压后中文文件不存在");
                
                var content = await File.ReadAllTextAsync(chineseFile);
                if (content != "中文内容测试")
                    throw new Exception("解压后中文文件内容不正确");
                
                Console.WriteLine("✅ 中文文件名测试通过");
            }
            finally
            {
                // 清理测试文件
                if (Directory.Exists(testDir))
                    Directory.Delete(testDir, true);
            }
        }

        /// <summary>
        /// 测试文件格式检测
        /// </summary>
        private static void TestFileFormatDetection()
        {
            Console.WriteLine("🔍 测试文件格式检测...");
            
            // 测试 ZIP 文件检测
            if (!ArchiveService.IsZipFile("test.zip"))
                throw new Exception("ZIP 文件检测失败");
            
            if (!ArchiveService.IsArchiveFile("test.zip"))
                throw new Exception("压缩文件检测失败");
            
            // 测试非 ZIP 文件
            if (ArchiveService.IsZipFile("test.txt"))
                throw new Exception("非 ZIP 文件误检测");
            
            // 测试其他压缩格式检测
            if (!ArchiveService.IsArchiveFile("test.rar"))
                throw new Exception("RAR 文件检测失败");
            
            if (!ArchiveService.IsArchiveFile("test.7z"))
                throw new Exception("7Z 文件检测失败");
            
            // 测试库可用性
            if (!ArchiveService.Is360ZipInstalled())
                throw new Exception("压缩库可用性检测失败");
            
            Console.WriteLine("✅ 文件格式检测测试通过");
        }
    }
}
