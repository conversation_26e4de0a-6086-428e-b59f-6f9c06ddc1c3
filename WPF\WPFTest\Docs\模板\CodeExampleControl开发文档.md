# CodeExampleControl 开发文档

## 📋 概述

CodeExampleControl 是一个专业的代码示例展示控件，基于 AvalonEdit 提供 VS 级别的语法高亮和代码显示体验。支持 XAML 和 C# 代码的分离展示，具有选项卡切换、复制功能等特性。

## 🎯 核心特性

### ✅ 已实现功能

#### 1. **双选项卡模式**
- 📄 **XAML选项卡** - 显示纯XAML代码，XML语法高亮
- ⚙️ **C#选项卡** - 显示C#代码，C#语法高亮
- 🔄 **智能切换** - 点击选项卡无缝切换内容

#### 2. **AvalonEdit集成**
- 🎨 **专业语法高亮** - 支持XAML、C#、JavaScript等多种语言
- 📝 **文本选择复制** - 可以选择部分代码或复制整个选项卡内容
- 🚫 **只读模式** - 防止意外编辑，专注代码展示
- 📊 **行号显示** - 清晰的行号标识

#### 3. **界面设计**
- 🔽 **展开收缩** - 默认收缩状态，用户主动展开
- 📋 **一键复制** - 复制按钮支持复制当前选项卡内容
- 🎨 **主题适配** - 自动适应WPF-UI明暗主题
- 📱 **响应式布局** - 适应不同屏幕尺寸

#### 4. **外部滚动支持**
- 🖱️ **鼠标滚轮传播** - 将滚轮事件传播到外部容器
- 📐 **无内部滚动条** - 由外部ScrollViewer统一处理滚动
- 🔄 **事件传播机制** - 确保外部滚动正常工作

## 🔧 技术实现

### 核心组件

#### 1. **依赖属性**
```csharp
// 选项卡模式
public bool ShowTabs { get; set; }

// 代码内容
public string XamlCode { get; set; }
public string CSharpCode { get; set; }
public string CodeContent { get; set; }

// 展开状态
public bool IsExpanded { get; set; } // 默认: false
```

#### 2. **AvalonEdit配置**
```csharp
private void ConfigureAvalonEditor()
{
    // 基本配置
    AvalonCodeEditor.IsReadOnly = true;
    AvalonCodeEditor.ShowLineNumbers = true;
    AvalonCodeEditor.WordWrap = false;
    
    // 隐藏滚动条
    AvalonCodeEditor.HorizontalScrollBarVisibility = ScrollBarVisibility.Hidden;
    AvalonCodeEditor.VerticalScrollBarVisibility = ScrollBarVisibility.Hidden;
    
    // 鼠标滚轮事件传播
    AvalonCodeEditor.PreviewMouseWheel += AvalonCodeEditor_PreviewMouseWheel;
}
```

#### 3. **语法高亮映射**
```csharp
private string MapLanguageToHighlighting(string language)
{
    return language.ToUpper() switch
    {
        "XAML" => "XML",
        "XML" => "XML",
        "C#" => "C#",
        "CSHARP" => "C#",
        "JAVASCRIPT" => "JavaScript",
        "JSON" => "JavaScript",
        _ => "XML"
    };
}
```

### 事件处理

#### 1. **选项卡切换**
```csharp
private void SwitchToTab(string language, string content)
{
    _currentTabLanguage = language;
    _currentTabContent = content ?? string.Empty;
    
    UpdateTabButtonStyles(language);
    UpdateEditorContent();
}
```

#### 2. **外部滚动支持**
```csharp
private void AvalonCodeEditor_PreviewMouseWheel(object sender, MouseWheelEventArgs e)
{
    if (AvalonCodeEditor.VerticalScrollBarVisibility == ScrollBarVisibility.Hidden)
    {
        e.Handled = true;
        var eventArg = new MouseWheelEventArgs(e.MouseDevice, e.Timestamp, e.Delta)
        {
            RoutedEvent = UIElement.MouseWheelEvent,
            Source = this
        };
        var parent = Parent as UIElement;
        parent?.RaiseEvent(eventArg);
    }
}
```

## 📱 使用方法

### 基本用法

```xml
<!-- 选项卡模式 -->
<codeExample:CodeExampleControl 
    Title="NavigationControl 基础用法"
    Description="展示 NavigationControl 的基本 XAML 配置和 ViewModel 实现"
    ShowTabs="True"
    XamlCode="{Binding XamlCodeExample}"
    CSharpCode="{Binding CSharpCodeExample}"/>
```

### ViewModel配置

```csharp
public partial class ExampleViewModel : ObservableObject
{
    [ObservableProperty]
    private string xamlCodeExample = string.Empty;

    [ObservableProperty]
    private string cSharpCodeExample = string.Empty;

    private void InitializeCodeExamples()
    {
        XamlCodeExample = @"<zylo:NavigationControl
    ItemsSource=""{Binding NavigationItems}""
    SelectedItem=""{Binding SelectedItem}""
    NavigationItemSelectedCommand=""{Binding NavigationCommand}""/>";

        CSharpCodeExample = @"[ObservableProperty]
private ObservableCollection<NavigationItemModel> navigationItems = new();

[RelayCommand]
private void Navigation(NavigationItemModel? item)
{
    SelectedNavigationItem = item;
}";
    }
}
```

## 🚀 应用示例

### 1. NavigationBasicExample
- **功能**: 展示NavigationControl基础用法
- **特点**: XAML和C#代码分离展示
- **位置**: `WPF/WPFTest/Views/Navigation/NavigationBasicExample.xaml`

### 2. NavigationAdvancedExample  
- **功能**: 展示NavigationControl高级功能
- **特点**: 包含层级导航、Prism集成、性能监控
- **位置**: `WPF/WPFTest/Views/Navigation/NavigationAdvancedExample.xaml`

## 🔄 开发历程

### 阶段1: WebView2方案 (已废弃)
- **问题**: 下载框弹出、CDN依赖、复杂度高
- **原因**: WebView2加载外部Monaco Editor资源

### 阶段2: 双模式方案 (已简化)
- **特点**: 简单文本模式 + AvalonEdit模式
- **问题**: 用户需要学习模式切换，界面复杂

### 阶段3: 纯AvalonEdit方案 (当前)
- **优势**: 统一专业体验、简洁界面、无学习成本
- **特点**: 只使用AvalonEdit，外部滚动支持

## 📊 性能优化

### 1. **内存管理**
- 只维护一个AvalonEdit实例
- 及时释放不需要的资源
- 避免内存泄漏

### 2. **渲染优化**
- 语法高亮按需加载
- 内容更新时机优化
- 避免不必要的重绘

### 3. **事件处理**
- 高效的事件传播机制
- 异常处理和日志记录
- 防止事件循环

## 🐛 已解决问题

### 1. **下载框问题**
- **原因**: WebView2加载外部资源
- **解决**: 改用AvalonEdit本地方案

### 2. **滚动失效问题**
- **原因**: AvalonEdit阻止鼠标滚轮事件
- **解决**: 添加PreviewMouseWheel事件传播

### 3. **内容不显示问题**
- **原因**: 选项卡初始化时机问题
- **解决**: 优化加载顺序和内容更新机制

### 4. **换行显示问题**
- **原因**: 自动换行导致长代码行显示异常
- **解决**: 禁用WordWrap，启用水平滚动

## 📝 最佳实践

### 1. **代码组织**
- XAML和C#代码分离
- 使用有意义的示例代码
- 保持代码简洁易懂

### 2. **性能考虑**
- 避免过长的代码内容
- 合理使用语法高亮
- 及时更新内容

### 3. **用户体验**
- 默认收缩状态
- 清晰的选项卡标识
- 直观的操作反馈

## 🔧 故障排除

### 常见问题及解决方案

#### 1. **内容不显示**
```
问题: 展开后选项卡内容为空
原因: ViewModel中代码示例属性未初始化
解决: 确保在构造函数中调用InitializeCodeExamples()
```

#### 2. **外部滚动失效**
```
问题: 鼠标滚轮无法滚动页面
原因: AvalonEdit阻止了滚轮事件传播
解决: 已添加PreviewMouseWheel事件处理，确保事件传播到父容器
```

#### 3. **语法高亮错误**
```
问题: 代码显示没有语法高亮
原因: 语言映射错误或AvalonEdit配置问题
解决: 检查MapLanguageToHighlighting方法和语言参数
```

#### 4. **选项卡切换无响应**
```
问题: 点击选项卡按钮没有反应
原因: 事件绑定错误或内容为空
解决: 检查XAML中的Click事件绑定和ViewModel属性
```

## 📚 API参考

### 依赖属性详细说明

#### ShowTabs
```csharp
public bool ShowTabs { get; set; }
// 默认值: false
// 说明: 是否显示选项卡模式，true时显示XAML和C#选项卡
```

#### XamlCode
```csharp
public string XamlCode { get; set; }
// 默认值: string.Empty
// 说明: XAML选项卡显示的代码内容
```

#### CSharpCode
```csharp
public string CSharpCode { get; set; }
// 默认值: string.Empty
// 说明: C#选项卡显示的代码内容
```

#### IsExpanded
```csharp
public bool IsExpanded { get; set; }
// 默认值: false
// 说明: 是否展开代码区域，false时默认收缩
```

#### Title
```csharp
public string Title { get; set; }
// 默认值: string.Empty
// 说明: 代码示例的标题
```

#### Description
```csharp
public string Description { get; set; }
// 默认值: string.Empty
// 说明: 代码示例的描述信息
```

#### Language
```csharp
public string Language { get; set; }
// 默认值: "XAML"
// 说明: 代码语言类型，用于语法高亮
```

### 事件说明

#### 选项卡点击事件
```csharp
private void XamlTabButton_Click(object sender, RoutedEventArgs e)
private void CSharpTabButton_Click(object sender, RoutedEventArgs e)
```

#### 复制按钮事件
```csharp
private void CopyCodeButton_Click(object sender, RoutedEventArgs e)
```

#### 展开收缩事件
```csharp
private void ExpandCollapseButton_Click(object sender, RoutedEventArgs e)
```

## 🎨 样式自定义

### 主题适配
控件自动适配WPF-UI主题，支持明暗模式切换：

```xml
<!-- 主题资源引用 -->
Background="{DynamicResource ControlFillColorSecondaryBrush}"
Foreground="{DynamicResource TextFillColorPrimaryBrush}"
BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
```

### 自定义样式
可以通过重写样式来自定义外观：

```xml
<Style TargetType="codeExample:CodeExampleControl">
    <Setter Property="Background" Value="Transparent"/>
    <Setter Property="BorderThickness" Value="1"/>
    <Setter Property="CornerRadius" Value="8"/>
</Style>
```

## 🔮 未来规划

### 短期目标 (v1.1)
- [ ] 代码搜索功能
- [ ] 行号点击跳转
- [ ] 代码折叠优化
- [ ] 复制反馈动画

### 中期目标 (v1.2)
- [ ] 更多语言支持 (Python, Java, TypeScript)
- [ ] 代码格式化功能
- [ ] 主题自定义选项
- [ ] 导出功能 (PDF, 图片)

### 长期目标 (v2.0)
- [ ] 代码编辑模式
- [ ] 实时预览功能
- [ ] 插件系统
- [ ] 云端同步

## 📄 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request 来改进这个控件。

### 开发环境要求
- .NET 8.0 或更高版本
- Visual Studio 2022 或 JetBrains Rider
- WPF-UI 4.0.3+
- AvalonEdit 6.3.0+

### 提交规范
- 遵循现有代码风格
- 添加必要的单元测试
- 更新相关文档
- 提供清晰的提交信息

---

**开发团队**: Zylo.WPF
**最后更新**: 2025-07-14
**版本**: v1.0.0
**文档版本**: 完整版
