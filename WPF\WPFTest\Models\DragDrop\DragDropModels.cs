using System.Collections.ObjectModel;
using System.ComponentModel;

namespace WPFTest.Models.DragDrop;

/// <summary>
/// DragDrop模块核心模型集合
/// </summary>
/// <remarks>
/// 包含DragDrop功能所需的所有核心模型：
/// - DragDropItem: 通用拖拽项模型
/// - DragDropContainer: 拖拽容器模型
/// - DragDropOperation: 拖拽操作模型
/// </remarks>

/// <summary>
/// 通用拖拽项模型 - 适用于所有拖拽场景
/// </summary>
public partial class DragDropItem : ObservableObject
{
    /// <summary>
    /// 项目ID（唯一标识）
    /// </summary>
    [ObservableProperty]
    [Description("项目ID（唯一标识）")]
    public partial string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 显示名称
    /// </summary>
    [ObservableProperty]
    [Description("显示名称")]
    public partial string Name { get; set; } = string.Empty;

    /// <summary>
    /// 显示图标
    /// </summary>
    [ObservableProperty]
    [Description("显示图标")]
    public partial string Icon { get; set; } = string.Empty;

    /// <summary>
    /// 项目类型
    /// </summary>
    [ObservableProperty]
    [Description("项目类型")]
    public partial string ItemType { get; set; } = string.Empty;

    /// <summary>
    /// 项目数据（可以是任何对象）
    /// </summary>
    [ObservableProperty]
    [Description("项目数据（可以是任何对象）")]
    public partial object? Data { get; set; }

    /// <summary>
    /// 是否被选中
    /// </summary>
    [ObservableProperty]
    [Description("是否被选中")]
    public partial bool IsSelected { get; set; }

    /// <summary>
    /// 是否可拖拽
    /// </summary>
    [ObservableProperty]
    [Description("是否可拖拽")]
    public partial bool IsDraggable { get; set; } = true;

    /// <summary>
    /// 是否可作为拖拽目标
    /// </summary>
    [ObservableProperty]
    [Description("是否可作为拖拽目标")]
    public partial bool IsDropTarget { get; set; } = true;

    /// <summary>
    /// 拖拽预览文本
    /// </summary>
    public string DragPreviewText => $"{Icon} {Name}";

    /// <summary>
    /// 创建拖拽项的工厂方法
    /// </summary>
    public static DragDropItem Create(string name, string icon = "", string itemType = "", object? data = null)
    {
        return new DragDropItem
        {
            Name = name,
            Icon = icon,
            ItemType = itemType,
            Data = data
        };
    }

    /// <summary>
    /// 从DWG文件模型创建拖拽项
    /// </summary>
    public static DragDropItem FromDwgFile(DWG.DwgFileModel dwgFile)
    {
        return new DragDropItem
        {
            Id = dwgFile.FullPath,
            Name = dwgFile.FileName,
            Icon = dwgFile.DrawingIcon,
            ItemType = "DwgFile",
            Data = dwgFile,
            IsDraggable = true,
            IsDropTarget = false
        };
    }

    /// <summary>
    /// 从专业Tab模型创建拖拽项
    /// </summary>
    public static DragDropItem FromProfessionTab(ProfessionTabModel profession)
    {
        return new DragDropItem
        {
            Id = profession.FolderPath,
            Name = profession.Name,
            Icon = profession.Icon,
            ItemType = "Profession",
            Data = profession,
            IsDraggable = false,
            IsDropTarget = true
        };
    }

    /// <summary>
    /// 从文件类型模型创建拖拽项
    /// </summary>
    public static DragDropItem FromFileType(FileTypeModel fileType)
    {
        return new DragDropItem
        {
            Id = fileType.Name,
            Name = fileType.Name,
            Icon = fileType.Icon,
            ItemType = "FileType",
            Data = fileType,
            IsDraggable = false,
            IsDropTarget = !fileType.IsSeparator
        };
    }
}

/// <summary>
/// 拖拽容器模型 - 管理拖拽项的容器
/// </summary>
public partial class DragDropContainer : ObservableObject
{
    /// <summary>
    /// 容器ID
    /// </summary>
    [ObservableProperty]
    [Description("容器ID")]
    public partial string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 容器名称
    /// </summary>
    [ObservableProperty]
    [Description("容器名称")]
    public partial string Name { get; set; } = string.Empty;

    /// <summary>
    /// 容器图标
    /// </summary>
    [ObservableProperty]
    [Description("容器图标")]
    public partial string Icon { get; set; } = string.Empty;

    /// <summary>
    /// 容器类型
    /// </summary>
    [ObservableProperty]
    [Description("容器类型")]
    public partial string ContainerType { get; set; } = string.Empty;

    /// <summary>
    /// 容器中的项目列表
    /// </summary>
    [ObservableProperty]
    [Description("容器中的项目列表")]
    public partial ObservableCollection<DragDropItem> Items { get; set; } = new();

    /// <summary>
    /// 是否接受拖拽
    /// </summary>
    [ObservableProperty]
    [Description("是否接受拖拽")]
    public partial bool AcceptsDrop { get; set; } = true;

    /// <summary>
    /// 允许的拖拽项类型
    /// </summary>
    [ObservableProperty]
    [Description("允许的拖拽项类型")]
    public partial string[] AllowedItemTypes { get; set; } = Array.Empty<string>();

    /// <summary>
    /// 项目数量
    /// </summary>
    public int ItemCount => Items.Count;

    /// <summary>
    /// 容器描述
    /// </summary>
    public string Description => $"{Icon} {Name} ({ItemCount} 项)";

    /// <summary>
    /// 构造函数
    /// </summary>
    public DragDropContainer()
    {
        Items.CollectionChanged += (s, e) =>
        {
            OnPropertyChanged(nameof(ItemCount));
            OnPropertyChanged(nameof(Description));
        };
    }

    /// <summary>
    /// 添加项目到容器
    /// </summary>
    public void AddItem(DragDropItem item)
    {
        if (CanAcceptItem(item))
        {
            Items.Add(item);
        }
    }

    /// <summary>
    /// 从容器移除项目
    /// </summary>
    public void RemoveItem(DragDropItem item)
    {
        Items.Remove(item);
    }

    /// <summary>
    /// 检查是否可以接受指定项目
    /// </summary>
    public bool CanAcceptItem(DragDropItem item)
    {
        if (!AcceptsDrop || item == null)
            return false;

        if (AllowedItemTypes.Length == 0)
            return true;

        return AllowedItemTypes.Contains(item.ItemType);
    }

    /// <summary>
    /// 清空容器
    /// </summary>
    public void Clear()
    {
        Items.Clear();
    }

    /// <summary>
    /// 创建容器的工厂方法
    /// </summary>
    public static DragDropContainer Create(string name, string icon = "", string containerType = "", params string[] allowedItemTypes)
    {
        return new DragDropContainer
        {
            Name = name,
            Icon = icon,
            ContainerType = containerType,
            AllowedItemTypes = allowedItemTypes
        };
    }
}

/// <summary>
/// 拖拽操作模型 - 记录拖拽操作的详细信息
/// </summary>
public partial class DragDropOperation : ObservableObject
{
    /// <summary>
    /// 操作ID
    /// </summary>
    [ObservableProperty]
    [Description("操作ID")]
    public partial string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 拖拽的项目
    /// </summary>
    [ObservableProperty]
    [Description("拖拽的项目")]
    public partial DragDropItem? DraggedItem { get; set; }

    /// <summary>
    /// 源容器
    /// </summary>
    [ObservableProperty]
    [Description("源容器")]
    public partial DragDropContainer? SourceContainer { get; set; }

    /// <summary>
    /// 目标容器
    /// </summary>
    [ObservableProperty]
    [Description("目标容器")]
    public partial DragDropContainer? TargetContainer { get; set; }

    /// <summary>
    /// 操作类型
    /// </summary>
    [ObservableProperty]
    [Description("操作类型")]
    public partial DragDropOperationType OperationType { get; set; } = DragDropOperationType.Move;

    /// <summary>
    /// 操作状态
    /// </summary>
    [ObservableProperty]
    [Description("操作状态")]
    public partial DragDropOperationStatus Status { get; set; } = DragDropOperationStatus.Pending;

    /// <summary>
    /// 操作时间
    /// </summary>
    [ObservableProperty]
    [Description("操作时间")]
    public partial DateTime OperationTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 错误消息
    /// </summary>
    [ObservableProperty]
    [Description("错误消息")]
    public partial string? ErrorMessage { get; set; }

    /// <summary>
    /// 操作描述
    /// </summary>
    public string Description => $"{OperationType}: {DraggedItem?.Name} -> {TargetContainer?.Name}";

    /// <summary>
    /// 操作是否成功
    /// </summary>
    public bool IsSuccessful => Status == DragDropOperationStatus.Completed;

    /// <summary>
    /// 创建拖拽操作的工厂方法
    /// </summary>
    public static DragDropOperation Create(DragDropItem item, DragDropContainer? source, DragDropContainer? target, DragDropOperationType operationType = DragDropOperationType.Move)
    {
        return new DragDropOperation
        {
            DraggedItem = item,
            SourceContainer = source,
            TargetContainer = target,
            OperationType = operationType
        };
    }
}

/// <summary>
/// 拖拽操作类型枚举
/// </summary>
public enum DragDropOperationType
{
    /// <summary>移动</summary>
    Move,
    /// <summary>复制</summary>
    Copy,
    /// <summary>链接</summary>
    Link,
    /// <summary>删除</summary>
    Delete
}

/// <summary>
/// 拖拽操作状态枚举
/// </summary>
public enum DragDropOperationStatus
{
    /// <summary>等待中</summary>
    Pending,
    /// <summary>进行中</summary>
    InProgress,
    /// <summary>已完成</summary>
    Completed,
    /// <summary>已取消</summary>
    Cancelled,
    /// <summary>失败</summary>
    Failed
}
