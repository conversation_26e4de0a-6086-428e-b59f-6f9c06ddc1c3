<UserControl x:Class="WPFTest.Views.ColorShowcaseView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:mvvm="http://prismlibrary.com/"
             xmlns:viewModels="clr-namespace:WPFTest.ViewModels"
             d:DesignHeight="800" d:DesignWidth="800"
             mc:Ignorable="d"
             mvvm:ViewModelLocator.AutoWireViewModel="True"
             d:DataContext="{d:DesignInstance viewModels:ColorShowcaseViewModel}"
             Foreground="{DynamicResource TextFillColorPrimaryBrush}">

    <UserControl.Resources>
        <!-- 颜色块样式 -->
        <Style x:Key="ColorBlockStyle" TargetType="Border">
            <Setter Property="Height" Value="80"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="BorderBrush" Value="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                    <Setter Property="BorderThickness" Value="2"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 颜色名称文本样式 -->
        <Style x:Key="ColorNameStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="Margin" Value="4"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Grid Grid.Row="0" Margin="20,20,20,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <StackPanel Grid.Column="0" Orientation="Horizontal">
                <TextBlock Text="🎨 WPF-UI 颜色展示"
                           FontSize="32"
                           FontWeight="Bold"
                           Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                           VerticalAlignment="Center"/>
                <TextBlock Text="(点击颜色块复制资源名称)"
                           FontSize="14"
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                           VerticalAlignment="Bottom"
                           Margin="15,0,0,5"/>
            </StackPanel>

            <StackPanel Grid.Column="1" Orientation="Horizontal">
                <ui:Button Content="{Binding ThemeButtonText}"
                           FontSize="12"
                           Padding="12,6"
                           Command="{Binding ToggleThemeCommand}"
                           Background="{DynamicResource AccentFillColorSecondaryBrush}"
                           Foreground="{DynamicResource TextFillColorInverseBrush}"
                           ToolTip="切换主题模式"
                           Margin="0,0,10,0"/>
                <ui:Button Content="🔄 刷新"
                           FontSize="12"
                           Padding="12,6"
                           Command="{Binding RefreshColorsCommand}"
                           Background="{DynamicResource ControlFillColorDefaultBrush}"
                           Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                           ToolTip="手动刷新颜色显示"
                           Margin="0,0,10,0"/>
                <ui:Button Content="🎯 测试ListView"
                           FontSize="12"
                           Padding="12,6"
                           Command="{Binding TestListViewColorsCommand}"
                           Background="{DynamicResource AccentFillColorDefaultBrush}"
                           Foreground="{DynamicResource TextFillColorInverseBrush}"
                           ToolTip="测试ListView颜色资源"/>
            </StackPanel>
        </Grid>

        <!-- 当前选中颜色信息显示区域 -->
        <ui:Card Grid.Row="1" Margin="20,0,20,10" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 颜色预览 -->
                <Border Grid.Column="0"
                        Width="80" Height="60"
                        CornerRadius="8"
                        BorderThickness="2"
                        BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                        Background="{Binding SelectedColor.ColorBrush, FallbackValue=Gray}"/>

                <!-- 颜色信息文本 -->
                <StackPanel Grid.Column="1" VerticalAlignment="Center" Margin="15,0">
                    <!-- 第一行：颜色名称 -->
                    <TextBlock Text="{Binding SelectedColor.Name, FallbackValue='🎨 请点击任意颜色块查看详细信息'}"
                               FontSize="18"
                               FontWeight="SemiBold"
                               Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                               TextWrapping="Wrap"/>

                    <!-- 第二行：颜色说明 -->
                    <TextBlock Text="{Binding SelectedColor.Description, FallbackValue='选中的颜色资源名称和十六进制值将显示在这里'}"
                               FontSize="13"
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                               Margin="0,5,0,0"
                               TextWrapping="Wrap"/>

                    <!-- 第三行：资源键 -->
                    <StackPanel Orientation="Horizontal" Margin="0,8,0,0">
                        <TextBlock Text="资源键: "
                                   FontSize="12"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"
                                   Foreground="{DynamicResource TextFillColorTertiaryBrush}"/>
                        <TextBox Text="{Binding SelectedColor.ResourceKey, FallbackValue='未选择'}"
                                   FontSize="10"
                                   FontFamily="Consolas"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                    </StackPanel>

                    <!-- 第四行：十六进制值 -->
                    <StackPanel Orientation="Horizontal" Margin="0,3,0,0">
                        <TextBlock Text="十六进制: "
                                   FontSize="12"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"
                                   Foreground="{DynamicResource TextFillColorTertiaryBrush}"/>
                        <TextBox Text="{Binding SelectedColor.HexValue, FallbackValue='#000000'}"
                                 FontSize="10"
                                 FontFamily="Consolas"
                                 Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                    </StackPanel>
                </StackPanel>

                <!-- 操作按钮 -->
                <!-- <StackPanel Grid.Column="2" Orientation="Horizontal"> -->
                <!--     <ui:Button Content="📋 复制资源" -->
                <!--                FontSize="11" -->
                <!--                Padding="8,4" -->
                <!--                Command="{Binding CopyResourceNameCommand}" -->
                <!--                CommandParameter="{Binding SelectedColor}" -->
                <!--                ToolTip="复制 XAML 资源引用" -->
                <!--                Margin="0,0,5,0"/> -->
                <!--     <ui:Button Content="🎨 复制颜色" -->
                <!--                FontSize="11" -->
                <!--                Padding="8,4" -->
                <!--                Command="{Binding CopyHexValueCommand}" -->
                <!--                CommandParameter="{Binding SelectedColor}" -->
                <!--                ToolTip="复制十六进制颜色值"/> -->
                <!-- </StackPanel> -->
            </Grid>
        </ui:Card>

        <!-- 颜色分组显示区域 -->
        <ScrollViewer Grid.Row="2" Margin="20,0,20,10"
                      VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Disabled">
            <ItemsControl ItemsSource="{Binding ColorGroups}">
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <StackPanel Margin="0,0,0,30">
                            <!-- 分组标题 -->
                            <TextBlock Text="{Binding Name}"
                                       FontSize="20"
                                       FontWeight="SemiBold"
                                       Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                       Margin="0,0,0,8"/>
                            <TextBlock Text="{Binding Description}"
                                       FontSize="13"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,15"
                                       TextWrapping="Wrap"/>

                            <!-- 分组中的颜色网格 -->
                            <ItemsControl ItemsSource="{Binding Colors}">
                                <ItemsControl.ItemsPanel>
                                    <ItemsPanelTemplate>
                                        <UniformGrid Columns="6"/>
                                    </ItemsPanelTemplate>
                                </ItemsControl.ItemsPanel>
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Background="{Binding ColorBrush}"
                                                Style="{StaticResource ColorBlockStyle}"
                                                ToolTip="{Binding Description}">
                                            <i:Interaction.Triggers>
                                                <i:EventTrigger EventName="MouseLeftButtonDown">
                                                    <i:InvokeCommandAction Command="{Binding DataContext.ColorClickCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                                           CommandParameter="{Binding}"/>
                                                </i:EventTrigger>
                                            </i:Interaction.Triggers>
                                         
                                            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                                                <TextBlock Text="{Binding Name}"
                                                           Style="{StaticResource ColorNameStyle}"
                                                           Foreground="{Binding TextBrush}"/>
                                                <TextBlock Text="{Binding ResourceKey}"
                                                           Style="{StaticResource ColorNameStyle}"
                                                           Foreground="{Binding TextBrush}"/>
                                                <TextBlock Text="{Binding HexValue}"
                                                           Style="{StaticResource ColorNameStyle}"
                                                           Foreground="{Binding TextBrush}"/>
                                            </StackPanel>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </StackPanel>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>

        <!-- 状态栏 -->
        <Border Grid.Row="3" 
                Background="{DynamicResource LayerFillColorAltBrush}"
                BorderBrush="{DynamicResource DividerStrokeColorDefaultBrush}"
                BorderThickness="0,1,0,0"
                Padding="20,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0"
                           Text="{Binding StatusMessage}"
                           FontSize="12"
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                           VerticalAlignment="Center"/>

                <TextBlock Grid.Column="1"
                           FontSize="12"
                           Foreground="{DynamicResource TextFillColorTertiaryBrush}"
                           VerticalAlignment="Center">
                    <Run Text="共 "/>
                    <Run Text="{Binding Colors.Count, Mode=OneWay}"/>
                    <Run Text=" 个颜色"/>
                </TextBlock>
            </Grid>
        </Border>
    </Grid>
</UserControl>
