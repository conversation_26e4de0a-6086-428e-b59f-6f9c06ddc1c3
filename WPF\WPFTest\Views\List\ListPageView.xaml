﻿<UserControl x:Class="WPFTest.Views.List.ListPageView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:WPFTest.Views.List"
             xmlns:mvvm="http://prismlibrary.com/"
             xmlns:inputControls="clr-namespace:WPFTest.ViewModels.InputControls"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:codeExample="clr-namespace:Zylo.WPF.Controls.CodeExample;assembly=Zylo.WPF"
             xmlns:list="clr-namespace:WPFTest.ViewModels.List"
             mc:Ignorable="d"
             d:DesignHeight="5000" d:DesignWidth="1200"
             mvvm:ViewModelLocator.AutoWireViewModel="True"
             d:DataContext="{d:DesignInstance list:ListPageViewModel}">
    <UserControl.Resources>
        <!-- 🎨 所有样式来自 Zylo.WPF 项目 -->
        <!-- ListView 样式已在 Zylo.WPF/Resources/ListView/ 目录下定义 -->
        <!-- 基础样式：ListViewBaseStyle, ListViewStyle, SmallListViewStyle, LargeListViewStyle -->
        <!-- 特殊样式：TransparentListViewStyle, AccentListViewStyle, CompactListViewStyle -->
        <!-- 现代化样式：ModernListViewStyle, CardListViewStyle, GridListViewStyle -->
        <!-- 状态样式：SuccessListViewStyle, WarningListViewStyle, DangerListViewStyle -->
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" 
                  HorizontalScrollBarVisibility="Disabled"
                  Padding="24">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>    <!-- 标题区域 -->
                <RowDefinition Height="*"/>       <!-- 主要内容 -->
                <RowDefinition Height="Auto"/>    <!-- 底部操作栏 -->
            </Grid.RowDefinitions>

            <!-- 标题区域 -->
            <StackPanel Grid.Row="0" Margin="0,0,0,20">
                <TextBlock Text="🎨 ListView 控件展示"
                           FontSize="32"
                           FontWeight="Bold"
                           Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                           Margin="0,0,0,8"/>
                <TextBlock Text="展示 ListView 控件的各种样式和交互效果，包括数据绑定、选择模式和自定义模板" 
                           FontSize="16" 
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                           TextWrapping="Wrap"/>
                
                <!-- 状态显示 -->
                <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                        BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Padding="16,12"
                        Margin="0,16,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <ui:SymbolIcon Symbol="Info24"
                                       FontSize="20"
                                       Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                       VerticalAlignment="Center"
                                       Margin="0,0,12,0"/>
                        <TextBlock Grid.Column="1" 
                                   Text="{Binding StatusMessage}" 
                                   FontSize="14"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                   VerticalAlignment="Center"
                                   TextWrapping="Wrap"/>
                    </Grid>
                </Border>
            </StackPanel>

            <!-- 主要内容区域 -->
            <Border Grid.Row="1" Margin="0,0,0,20">
                <StackPanel>

                    <!-- 基础功能展示 -->
                    <ui:CardExpander Header="🎯 基础功能" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 ListView 的基础功能和用法，包括简单列表和基本选择"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>
                            
                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- 简单列表 -->
                                    <StackPanel Grid.Column="0" Margin="0,0,12,0">
                                        <TextBlock Text="🎯 WPFUI标准样式测试：" FontWeight="Medium" Margin="0,0,0,12"/>
                                        <ListView ItemsSource="{Binding SimpleItems}"
                                                  SelectedItem="{Binding SelectedSimpleItem, Mode=TwoWay}"
                                                  Style="{StaticResource CleanListViewStyle}"
                                                  Height="200"
                                                  Margin="0,0,0,12"/>

                                        <TextBlock Text="简单字符串列表（原样式）：" FontWeight="Medium" Margin="0,0,0,12"/>
                                        <ui:ListView ItemsSource="{Binding SimpleItems}"
                                                     SelectedItem="{Binding SelectedSimpleItem, Mode=TwoWay}"
                                                     Height="150"
                                                     Margin="0,0,0,12"/>
                                        <StackPanel Orientation="Horizontal">
                                            <ui:Button Content="添加项目" 
                                                       Command="{Binding AddSimpleItemCommand}"
                                                       Appearance="Secondary"
                                                       Margin="0,0,8,0"/>
                                            <TextBlock Text="{Binding SelectedSimpleItem, StringFormat='选中: {0}'}"
                                                       VerticalAlignment="Center"
                                                       FontSize="12"
                                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                                        </StackPanel>
                                    </StackPanel>

                                    <!-- 不同样式展示 -->
                                    <StackPanel Grid.Column="1" Margin="12,0,0,0">
                                        <TextBlock Text="不同样式展示：" FontWeight="Medium" Margin="0,0,0,12"/>
                                        
                                        <!-- 标准样式 -->
                                        <TextBlock Text="标准样式" FontSize="12" Margin="0,0,0,4"/>
                                        <ui:ListView ItemsSource="{Binding SimpleItems}"
                                                     Height="60"
                                                     Margin="0,0,0,8"/>

                                        <!-- 小型样式 -->
                                        <TextBlock Text="小型样式" FontSize="12" Margin="0,0,0,4"/>
                                        <ui:ListView ItemsSource="{Binding SimpleItems}"
                                                     Height="50"
                                                     Margin="0,0,0,8"/>

                                        <!-- 透明样式 -->
                                        <TextBlock Text="透明样式" FontSize="12" Margin="0,0,0,4"/>
                                        <ui:ListView ItemsSource="{Binding SimpleItems}"
                                                     Height="60"/>
                                    </StackPanel>
                                </Grid>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="基础功能代码示例"
                                Language="XAML"
                                Description="展示 ListView 的基础用法"
                                ShowTabs="True"
                                XamlCode="{Binding BasicXamlExample}"
                                CSharpCode="{Binding BasicCSharpExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 高级功能展示 -->
                    <ui:CardExpander Header="⚡ 高级功能" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 ListView 的高级功能，包括自定义模板、多选模式和复杂数据绑定"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>
                            
                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="复杂数据项列表：" FontWeight="Medium" Margin="0,0,0,16"/>
                                    
                                    <!-- 控制面板 -->
                                    <Border Background="{DynamicResource CardBackgroundFillColorSecondaryBrush}"
                                            BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                            BorderThickness="1"
                                            CornerRadius="6"
                                            Padding="12"
                                            Margin="0,0,0,16">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <ui:TextBox Grid.Column="0"
                                                        Text="{Binding NewItemName, Mode=TwoWay}"
                                                        PlaceholderText="输入新项目名称"
                                                        Margin="0,0,8,0"/>
                                            <ui:Button Grid.Column="1"
                                                       Content="添加项目"
                                                       Command="{Binding AddDataItemCommand}"
                                                       Appearance="Primary"
                                                       Margin="0,0,8,0"/>
                                            <ui:Button Grid.Column="2"
                                                       Content="删除选中"
                                                       Command="{Binding DeleteSelectedItemCommand}"
                                                       Appearance="Danger"
                                                       Margin="0,0,8,0"/>
                                            <ToggleButton Grid.Column="3"
                                                             Content="多选模式"
                                                             IsChecked="{Binding IsMultiSelectEnabled, Mode=TwoWay}"
                                                             Command="{Binding ToggleMultiSelectCommand}"/>
                                        </Grid>
                                    </Border>
                                    
                                    <!-- 复杂数据列表 -->
                                    <ListView ItemsSource="{Binding DataItems}"
                                              SelectedItem="{Binding SelectedDataItem, Mode=TwoWay}"
                                              Style="{StaticResource CleanListViewStyle}"
                                              Height="300"
                                              ScrollViewer.HorizontalScrollBarVisibility="Disabled">
                                        <ListView.ItemTemplate>
                                            <DataTemplate>
                                                <Border Background="Transparent"
                                                        Padding="0"
                                                        Margin="0">
                                                    <Grid>
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="*"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                        </Grid.ColumnDefinitions>
                                                        
                                                        <ui:SymbolIcon Grid.Column="0"
                                                                       Symbol="{Binding Icon}"
                                                                       FontSize="18"
                                                                       Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                                                       VerticalAlignment="Center"
                                                                       Margin="0,0,10,0"/>
                                                        
                                                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                                            <TextBlock Text="{Binding Name}"
                                                                       FontWeight="Normal"
                                                                       FontSize="14"
                                                                       Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                                                            <TextBlock Text="{Binding Description}"
                                                                       FontSize="12"
                                                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                                       Margin="0,2,0,0"/>
                                                        </StackPanel>
                                                        
                                                        <StackPanel Grid.Column="2" VerticalAlignment="Center" Margin="6,0,10,0">
                                                            <!-- 简洁分类标签 -->
                                                            <TextBlock Text="{Binding Category}"
                                                                       FontSize="10"
                                                                       Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                                                       FontWeight="SemiBold"
                                                                       HorizontalAlignment="Right"
                                                                       TextAlignment="Right"/>
                                                            <TextBlock Text="{Binding CreatedDate, StringFormat={}{0:MM/dd}}"
                                                                       FontSize="9"
                                                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                                       HorizontalAlignment="Right"
                                                                       TextAlignment="Right"
                                                                       Margin="0,1,0,0"/>
                                                        </StackPanel>
                                                    </Grid>
                                                </Border>
                                            </DataTemplate>
                                        </ListView.ItemTemplate>
                                    </ListView>
                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="高级功能代码示例"
                                Language="XAML"
                                Description="展示 ListView 的高级用法和自定义模板"
                                ShowTabs="True"
                                XamlCode="{Binding AdvancedXamlExample}"
                                CSharpCode="{Binding AdvancedCSharpExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- WPF-UI 标准样式 -->
                    <ui:CardExpander Header="🎨 WPF-UI 标准样式" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 WPF-UI 库提供的各种 ListView 外观样式"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="不同外观样式：" FontWeight="Medium" Margin="0,0,0,16"/>

                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- 标准样式组 -->
                                        <StackPanel Grid.Column="0" Margin="0,0,12,0">
                                            <TextBlock Text="标准样式" FontSize="12" FontWeight="Medium" Margin="0,0,0,8"/>

                                            <TextBlock Text="Primary (强调)" FontSize="10" Margin="0,0,0,4"/>
                                            <ListView ItemsSource="{Binding SimpleItems}"
                                                      Style="{StaticResource AccentListViewStyle}"
                                                      Height="60"
                                                      Margin="0,0,0,8"
                                                      SelectedItem="{Binding SelectedSimpleItem, Mode=TwoWay}"/>

                                            <TextBlock Text="Success (成功)" FontSize="10" Margin="0,0,0,4"/>
                                            <ListView ItemsSource="{Binding SimpleItems}"
                                                      Style="{StaticResource SuccessListViewStyle}"
                                                      Height="60"
                                                      Margin="0,0,0,8"/>

                                            <TextBlock Text="Transparent (透明)" FontSize="10" Margin="0,0,0,4"/>
                                            <ListView ItemsSource="{Binding SimpleItems}"
                                                      Style="{StaticResource TransparentListViewStyle}"
                                                      Height="60"/>
                                        </StackPanel>

                                        <!-- 尺寸样式组 -->
                                        <StackPanel Grid.Column="1" Margin="12,0">
                                            <TextBlock Text="尺寸样式" FontSize="12" FontWeight="Medium" Margin="0,0,0,8"/>

                                            <TextBlock Text="Small (小型)" FontSize="10" Margin="0,0,0,4"/>
                                            <ListView ItemsSource="{Binding SimpleItems}"
                                                      Style="{StaticResource SmallListViewStyle}"
                                                      Height="50"
                                                      Margin="0,0,0,8"/>

                                            <TextBlock Text="Standard (标准)" FontSize="10" Margin="0,0,0,4"/>
                                            <ListView ItemsSource="{Binding SimpleItems}"
                                                      Style="{StaticResource ListViewStyle}"
                                                      Height="60"
                                                      Margin="0,0,0,8"/>

                                            <TextBlock Text="Large (大型)" FontSize="10" Margin="0,0,0,4"/>
                                            <ListView ItemsSource="{Binding SimpleItems}"
                                                      Style="{StaticResource LargeListViewStyle}"
                                                      Height="70"/>
                                        </StackPanel>

                                        <!-- 特殊样式组 -->
                                        <StackPanel Grid.Column="2" Margin="12,0,0,0">
                                            <TextBlock Text="特殊样式" FontSize="12" FontWeight="Medium" Margin="0,0,0,8"/>

                                            <TextBlock Text="Warning (警告)" FontSize="10" Margin="0,0,0,4"/>
                                            <ListView ItemsSource="{Binding SimpleItems}"
                                                      Style="{StaticResource WarningListViewStyle}"
                                                      Height="60"
                                                      Margin="0,0,0,8"/>

                                            <TextBlock Text="Danger (危险)" FontSize="10" Margin="0,0,0,4"/>
                                            <ListView ItemsSource="{Binding SimpleItems}"
                                                      Style="{StaticResource DangerListViewStyle}"
                                                      Height="60"
                                                      Margin="0,0,0,8"/>

                                            <TextBlock Text="Compact (紧凑)" FontSize="10" Margin="0,0,0,4"/>
                                            <ListView ItemsSource="{Binding SimpleItems}"
                                                      Style="{StaticResource CompactListViewStyle}"
                                                      Height="50"/>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="WPF-UI 标准样式代码示例"
                                Language="XAML"
                                Description="展示如何使用 WPF-UI 库的标准 ListView 样式"
                                ShowTabs="True"
                                XamlCode="{Binding StandardStylesXamlExample}"
                                IsExpanded="False"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 自定义样式展示 -->
                    <ui:CardExpander Header="✨ 自定义样式展示" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示使用自定义样式创建的现代化 ListView 效果"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- 现代化样式 -->
                                    <StackPanel Grid.Column="0" Margin="0,0,12,0">
                                        <TextBlock Text="现代化样式：" FontWeight="Medium" Margin="0,0,0,12"/>

                                        <TextBlock Text="Modern Style" FontSize="12" Margin="0,0,0,4"/>
                                        <ListView ItemsSource="{Binding SimpleItems}"
                                                  Style="{StaticResource ModernListViewStyle}"
                                                  Height="100"
                                                  Margin="0,0,0,12"/>

                                        <TextBlock Text="Card Style" FontSize="12" Margin="0,0,0,4"/>
                                        <ListView ItemsSource="{Binding SimpleItems}"
                                                  Style="{StaticResource CardListViewStyle}"
                                                  Height="100"/>
                                    </StackPanel>

                                    <!-- 布局样式 -->
                                    <StackPanel Grid.Column="1" Margin="12,0,0,0">
                                        <TextBlock Text="布局样式：" FontWeight="Medium" Margin="0,0,0,12"/>

                                        <TextBlock Text="Grid Layout" FontSize="12" Margin="0,0,0,4"/>
                                        <ListView ItemsSource="{Binding DataItems}"
                                                  Style="{StaticResource GridListViewStyle}"
                                                  Height="120">
                                            <ListView.ItemTemplate>
                                                <DataTemplate>
                                                    <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                                                            BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                                            BorderThickness="1"
                                                            CornerRadius="6"
                                                            Padding="8"
                                                            Margin="2">
                                                        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                                            <ui:SymbolIcon Symbol="{Binding Icon}"
                                                                           FontSize="16"
                                                                           Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                                                           HorizontalAlignment="Center"
                                                                           Margin="0,0,0,4"/>
                                                            <TextBlock Text="{Binding Name}"
                                                                       FontSize="10"
                                                                       FontWeight="Normal"
                                                                       HorizontalAlignment="Center"
                                                                       TextAlignment="Center"
                                                                       TextTrimming="CharacterEllipsis"
                                                                       MaxWidth="80"/>
                                                        </StackPanel>
                                                    </Border>
                                                </DataTemplate>
                                            </ListView.ItemTemplate>
                                        </ListView>
                                    </StackPanel>
                                </Grid>
                            </ui:Card>

                            <!-- 瓷砖拼接样式展示 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="瓷砖拼接样式：" FontWeight="Medium" Margin="0,0,0,12"/>
                                    <TextBlock Text="Tile Layout - 矩形瓷砖拼接效果，适合应用程序列表和图标展示"
                                               FontSize="12"
                                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                               Margin="0,0,0,8"/>

                                    <ListView ItemsSource="{Binding DataItems}"
                                              Style="{StaticResource TileListViewStyle}"
                                              Height="160">
                                        <ListView.ItemTemplate>
                                            <DataTemplate>
                                                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                                    <ui:SymbolIcon Symbol="{Binding Icon}"
                                                                   FontSize="24"
                                                                   Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                                                   HorizontalAlignment="Center"
                                                                   Margin="0,0,0,8"/>
                                                    <TextBlock Text="{Binding Name}"
                                                               FontWeight="Normal"
                                                               FontSize="12"
                                                               HorizontalAlignment="Center"
                                                               TextAlignment="Center"
                                                               TextTrimming="CharacterEllipsis"
                                                               MaxWidth="160"/>
                                                    <TextBlock Text="{Binding Category}"
                                                               FontSize="10"
                                                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                               HorizontalAlignment="Center"
                                                               Margin="0,2,0,0"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </ListView.ItemTemplate>
                                    </ListView>
                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="自定义样式代码示例"
                                Language="XAML"
                                Description="展示如何使用 Zylo.WPF 中的自定义 ListView 样式"
                                ShowTabs="True"
                                XamlCode="{Binding CustomStylesXamlExample}"
                                CSharpCode="{Binding CustomStylesCSharpExample}"
                                IsExpanded="False"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 样式展示 -->
                    <ui:CardExpander Header="🎨 样式展示" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示来自 Zylo.WPF 的各种 ListView 样式"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- 左侧样式 -->
                                    <StackPanel Grid.Column="0" Margin="0,0,12,0">
                                        <TextBlock Text="标准样式系列：" FontWeight="Medium" Margin="0,0,0,12"/>

                                        <!-- 标准样式 -->
                                        <TextBlock Text="标准样式" FontSize="12" Margin="0,0,0,4"/>
                                        <ListView ItemsSource="{Binding SimpleItems}"
                                                  Style="{StaticResource ListViewStyle}"
                                                  Height="80"
                                                  Margin="0,0,0,12"/>

                                        <!-- 小型样式 -->
                                        <TextBlock Text="小型样式" FontSize="12" Margin="0,0,0,4"/>
                                        <ListView ItemsSource="{Binding SimpleItems}"
                                                  Style="{StaticResource SmallListViewStyle}"
                                                  Height="70"
                                                  Margin="0,0,0,12"/>

                                        <!-- 大型样式 -->
                                        <TextBlock Text="大型样式" FontSize="12" Margin="0,0,0,4"/>
                                        <ListView ItemsSource="{Binding SimpleItems}"
                                                  Style="{StaticResource LargeListViewStyle}"
                                                  Height="90"/>
                                    </StackPanel>

                                    <!-- 右侧样式 -->
                                    <StackPanel Grid.Column="1" Margin="12,0,0,0">
                                        <TextBlock Text="特殊效果样式：" FontWeight="Medium" Margin="0,0,0,12"/>

                                        <!-- 透明样式 -->
                                        <TextBlock Text="透明样式" FontSize="12" Margin="0,0,0,4"/>
                                        <ListView ItemsSource="{Binding SimpleItems}"
                                                  Style="{StaticResource TransparentListViewStyle}"
                                                  Height="80"
                                                  Margin="0,0,0,12"/>

                                        <!-- 强调色样式 -->
                                        <TextBlock Text="强调色样式" FontSize="12" Margin="0,0,0,4"/>
                                        <ListView ItemsSource="{Binding SimpleItems}"
                                                  Style="{StaticResource AccentListViewStyle}"
                                                  Height="80"
                                                  Margin="0,0,0,12"/>

                                        <!-- 紧凑样式 -->
                                        <TextBlock Text="紧凑样式" FontSize="12" Margin="0,0,0,4"/>
                                        <ListView ItemsSource="{Binding SimpleItems}"
                                                  Style="{StaticResource CompactListViewStyle}"
                                                  Height="60"/>
                                    </StackPanel>
                                </Grid>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="样式代码示例"
                                Language="XAML"
                                Description="展示如何使用 Zylo.WPF 中的 ListView 样式"
                                ShowTabs="True"
                                XamlCode="{Binding StylesXamlExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 现代化MVVM模式 -->
                    <ui:CardExpander Header="🚀 现代化MVVM模式" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示现代化MVVM模式的完整实现，包括多选、单选数据获取和批量操作"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="MVVM数据操作演示：" FontWeight="Medium" Margin="0,0,0,16"/>

                                    <!-- 操作控制面板 -->
                                    <Border Background="{DynamicResource CardBackgroundFillColorSecondaryBrush}"
                                            BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                            BorderThickness="1"
                                            CornerRadius="6"
                                            Padding="16"
                                            Margin="0,0,0,16">
                                        <Grid>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                            </Grid.RowDefinitions>

                                            <!-- 选择模式控制 -->
                                            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,12">
                                                <ToggleButton Content="多选模式"
                                                              IsChecked="{Binding IsMultiSelectEnabled, Mode=TwoWay}"
                                                              Margin="0,0,12,0"/>
                                                <TextBlock Text="{Binding IsMultiSelectEnabled, StringFormat='当前模式: {0}', Converter={StaticResource BoolToSelectionModeTextConverter}}"
                                                           VerticalAlignment="Center"
                                                           FontWeight="Medium"
                                                           Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"/>
                                            </StackPanel>

                                            <!-- 批量操作按钮 -->
                                            <WrapPanel Grid.Row="1">
                                                <ui:Button Content="全选"
                                                           Command="{Binding SelectAllCommand}"
                                                           Appearance="Secondary"
                                                           IsEnabled="{Binding IsMultiSelectEnabled}"
                                                           Margin="0,0,8,0"/>
                                                <ui:Button Content="取消全选"
                                                           Command="{Binding UnselectAllCommand}"
                                                           Appearance="Secondary"
                                                           Margin="0,0,8,0"/>
                                                <ui:Button Content="反选"
                                                           Command="{Binding InvertSelectionCommand}"
                                                           Appearance="Secondary"
                                                           IsEnabled="{Binding IsMultiSelectEnabled}"
                                                           Margin="0,0,8,0"/>
                                                <ui:Button Content="批量删除"
                                                           Command="{Binding DeleteSelectedItemsCommand}"
                                                           Appearance="Danger"
                                                           Margin="0,0,8,0"/>
                                                <ui:Button Content="获取选中数据"
                                                           Command="{Binding GetSelectedDataCommand}"
                                                           Appearance="Primary"
                                                           Margin="0,0,8,0"/>
                                                <ui:Button Content="导出数据"
                                                           Command="{Binding ExportSelectedDataCommand}"
                                                           Appearance="Success"/>
                                            </WrapPanel>
                                        </Grid>
                                    </Border>

                                    <!-- 数据列表 -->
                                    <ListView ItemsSource="{Binding DataItems}"
                                              SelectedItem="{Binding SelectedDataItem, Mode=TwoWay}"
                                              SelectionMode="{Binding IsMultiSelectEnabled, Converter={StaticResource BoolToSelectionModeConverter}}"
                                              Style="{StaticResource ModernListViewStyle}"
                                              Height="250"
                                              ScrollViewer.HorizontalScrollBarVisibility="Disabled">
                                        <ListView.ItemTemplate>
                                            <DataTemplate>
                                                <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                                                        BorderBrush="{Binding IsSelected, Converter={StaticResource BoolToBorderBrushConverter}}"
                                                        BorderThickness="1"
                                                        CornerRadius="6"
                                                        Padding="12"
                                                        Margin="4">
                                                    <Grid>
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="*"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                        </Grid.ColumnDefinitions>

                                                        <!-- 多选复选框 -->
                                                        <CheckBox Grid.Column="0"
                                                                  IsChecked="{Binding IsSelected, Mode=TwoWay}"
                                                                  Command="{Binding DataContext.ItemSelectionChangedCommand, RelativeSource={RelativeSource AncestorType=ListView}}"
                                                                  CommandParameter="{Binding}"
                                                                  Visibility="{Binding DataContext.IsMultiSelectEnabled, RelativeSource={RelativeSource AncestorType=ListView}, Converter={StaticResource BoolToVisibilityConverter}}"
                                                                  VerticalAlignment="Center"
                                                                  Margin="0,0,12,0"/>

                                                        <!-- 图标 -->
                                                        <ui:SymbolIcon Grid.Column="1"
                                                                       Symbol="{Binding Icon}"
                                                                       FontSize="24"
                                                                       Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                                                       VerticalAlignment="Center"
                                                                       Margin="0,0,12,0"/>

                                                        <!-- 内容 -->
                                                        <StackPanel Grid.Column="2" VerticalAlignment="Center">
                                                            <TextBlock Text="{Binding Name}"
                                                                       FontWeight="Medium"
                                                                       FontSize="14"
                                                                       Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                                                            <TextBlock Text="{Binding Description}"
                                                                       FontSize="12"
                                                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                                       Margin="0,2,0,0"/>
                                                            <TextBlock Text="{Binding CreatedDate, StringFormat='创建: {0:MM/dd HH:mm}'}"
                                                                       FontSize="10"
                                                                       Foreground="{DynamicResource TextFillColorTertiaryBrush}"
                                                                       Margin="0,2,0,0"/>
                                                        </StackPanel>

                                                        <!-- 状态标签 -->
                                                        <Border Grid.Column="3"
                                                                Background="{DynamicResource AccentFillColorDefaultBrush}"
                                                                CornerRadius="12"
                                                                HorizontalAlignment="Center"
                                                                VerticalAlignment="Center"
                                                                Padding="8,4"
                                                                
                                                                Margin="0,0,8,0">
                                                            <TextBlock Text="{Binding Category}"
                                                                       FontSize="10"
                                                                       Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                                                       FontWeight="Medium"/>
                                                        </Border>

                                                        <!-- 启用状态 -->
                                                        <ToggleButton Grid.Column="4"
                                                                      IsChecked="{Binding IsEnabled, Mode=TwoWay}"
                                                                      Content="{Binding IsEnabled, Converter={StaticResource BoolToEnabledTextConverter}}"
                                                                      VerticalAlignment="Center"/>
                                                    </Grid>

                                                    <!-- 添加点击处理 -->
                                                    <Border.InputBindings>
                                                        <MouseBinding MouseAction="LeftClick"
                                                                      Command="{Binding DataContext.ItemSelectionChangedCommand, RelativeSource={RelativeSource AncestorType=ListView}}"
                                                                      CommandParameter="{Binding}"/>
                                                    </Border.InputBindings>
                                                </Border>
                                            </DataTemplate>
                                        </ListView.ItemTemplate>
                                    </ListView>

                                    <!-- 密集样式展示 -->
                                    <TextBlock Text="密集样式（无间距、不圆角）："
                                               FontWeight="Medium"
                                               FontSize="14"
                                               Margin="0,16,0,8"/>

                                    <ListView ItemsSource="{Binding DataItems}"
                                              SelectedItem="{Binding SelectedDataItem, Mode=TwoWay}"
                                              SelectionMode="{Binding IsMultiSelectEnabled, Converter={StaticResource BoolToSelectionModeConverter}}"
                                              Style="{StaticResource DenseListViewStyle}"
                                              Height="200"
                                       
                                              ScrollViewer.HorizontalScrollBarVisibility="Disabled">
                                        <ListView.ItemTemplate>
                                            <DataTemplate>
                                                <Grid         Margin="0,0,10,0">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>

                                                    <!-- 多选复选框 -->
                                                    <CheckBox Grid.Column="0"
                                                              IsChecked="{Binding IsSelected, Mode=TwoWay}"
                                                              Command="{Binding DataContext.ItemSelectionChangedCommand, RelativeSource={RelativeSource AncestorType=ListView}}"
                                                              CommandParameter="{Binding}"
                                                              Visibility="{Binding DataContext.IsMultiSelectEnabled, RelativeSource={RelativeSource AncestorType=ListView}, Converter={StaticResource BoolToVisibilityConverter}}"
                                                              VerticalAlignment="Center"
                                                              Margin="0,0,8,0"/>

                                                    <!-- 图标 -->
                                                    <ui:SymbolIcon Grid.Column="1"
                                                                   Symbol="{Binding Icon}"
                                                                   FontSize="16"
                                                                   Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                                                   VerticalAlignment="Center"
                                                                   Margin="0,0,8,0"/>

                                                    <!-- 内容 -->
                                                    <StackPanel Grid.Column="2" VerticalAlignment="Center">
                                                        <TextBlock Text="{Binding Name}"
                                                                   FontWeight="Medium"
                                                                   FontSize="13"
                                                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                                                        <TextBlock Text="{Binding Description}"
                                                                   FontSize="11"
                                                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                                   Margin="0,1,10,0"/>
                                                    </StackPanel>

                                                    <!-- 分类标签 -->
                                                    <TextBlock Grid.Column="3"
                                                               Text="{Binding Category}"
                                                               FontSize="10"
                                                               Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                                               FontWeight="Medium"
                                                               VerticalAlignment="Center"
                                                               Margin="0,0,5,0"/>

                                                    <!-- 启用状态 -->
                                                    <ToggleButton Grid.Column="4"
                                                                  IsChecked="{Binding IsEnabled, Mode=TwoWay}"
                                                                  Content="{Binding IsEnabled, Converter={StaticResource BoolToEnabledTextConverter}}"
                                                                  VerticalAlignment="Center"
                                                                  FontSize="10"
                                                          
                                                                  Padding="10,5,10,2"/>
                                                </Grid>
                                            </DataTemplate>
                                        </ListView.ItemTemplate>
                                    </ListView>

                                    <!-- 密集边框样式展示 -->
                                    <TextBlock Text="密集边框样式（紧密排列，带分隔边框）："
                                               FontWeight="Medium"
                                               FontSize="14"
                                               Margin="0,16,0,8"/>

                                    <ListView ItemsSource="{Binding DataItems}"
                                              SelectedItem="{Binding SelectedDataItem, Mode=TwoWay}"
                                              SelectionMode="{Binding IsMultiSelectEnabled, Converter={StaticResource BoolToSelectionModeConverter}}"
                                              Style="{StaticResource DenseBorderedListViewStyle}"
                                              Height="200"
                                           
                                              ScrollViewer.HorizontalScrollBarVisibility="Disabled">
                                        <ListView.ItemTemplate>
                                            <DataTemplate>
                                                <Grid    Margin="0,0,10,0">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>

                                                    <!-- 多选复选框 -->
                                                    <CheckBox Grid.Column="0"
                                                              IsChecked="{Binding IsSelected, Mode=TwoWay}"
                                                              Command="{Binding DataContext.ItemSelectionChangedCommand, RelativeSource={RelativeSource AncestorType=ListView}}"
                                                              CommandParameter="{Binding}"
                                                              Visibility="{Binding DataContext.IsMultiSelectEnabled, RelativeSource={RelativeSource AncestorType=ListView}, Converter={StaticResource BoolToVisibilityConverter}}"
                                                              VerticalAlignment="Center"
                                                              Margin="0,0,8,0"/>

                                                    <!-- 图标 -->
                                                    <ui:SymbolIcon Grid.Column="1"
                                                                   Symbol="{Binding Icon}"
                                                                   FontSize="16"
                                                                   Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                                                   VerticalAlignment="Center"
                                                                   Margin="0,0,10,0"/>

                                                    <!-- 内容 -->
                                                    <StackPanel Grid.Column="2" VerticalAlignment="Center">
                                                        <TextBlock Text="{Binding Name}"
                                                                   FontWeight="Medium"
                                                                   FontSize="13"
                                                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                                                        <TextBlock Text="{Binding Description}"
                                                                   FontSize="11"
                                                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                                   Margin="0,1,10,0"/>
                                                    </StackPanel>

                                                    <!-- 分类标签 -->
                                                    <TextBlock Grid.Column="3"
                                                               Text="{Binding Category}"
                                                               FontSize="10"
                                                               Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                                               FontWeight="Medium"
                                                               VerticalAlignment="Center"
                                                               Margin="0,0,8,0"/>

                                                    <!-- 启用状态 -->
                                                    <ToggleButton Grid.Column="4"
                                                                  IsChecked="{Binding IsEnabled, Mode=TwoWay}"
                                                                  Content="{Binding IsEnabled, Converter={StaticResource BoolToEnabledTextConverter}}"
                                                                  VerticalAlignment="Center"
                                                                  FontSize="10"
                                                                  Padding="10,2"/>
                                                </Grid>
                                            </DataTemplate>
                                        </ListView.ItemTemplate>
                                    </ListView>

                                    <!-- 密集完整边框样式展示 -->
                                    <TextBlock Text="密集完整边框样式（紧密排列，完整边框包围）："
                                               FontWeight="Medium"
                                               FontSize="14"
                                               Margin="0,16,0,8"/>

                                    <ListView ItemsSource="{Binding DataItems}"
                                              SelectedItem="{Binding SelectedDataItem, Mode=TwoWay}"
                                              SelectionMode="{Binding IsMultiSelectEnabled, Converter={StaticResource BoolToSelectionModeConverter}}"
                                              Style="{StaticResource DenseFullBorderedListViewStyle}"
                                              Height="200"
                                              ScrollViewer.HorizontalScrollBarVisibility="Disabled">
                                        <ListView.ItemTemplate>
                                            <DataTemplate>
                                                <Grid    Margin="0,0,10,0">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>

                                                    <!-- 多选复选框 -->
                                                    <CheckBox Grid.Column="0"
                                                              IsChecked="{Binding IsSelected, Mode=TwoWay}"
                                                              Command="{Binding DataContext.ItemSelectionChangedCommand, RelativeSource={RelativeSource AncestorType=ListView}}"
                                                              CommandParameter="{Binding}"
                                                              Visibility="{Binding DataContext.IsMultiSelectEnabled, RelativeSource={RelativeSource AncestorType=ListView}, Converter={StaticResource BoolToVisibilityConverter}}"
                                                              VerticalAlignment="Center"
                                                              Margin="0,0,8,0"/>

                                                    <!-- 图标 -->
                                                    <ui:SymbolIcon Grid.Column="1"
                                                                   Symbol="{Binding Icon}"
                                                                   FontSize="16"
                                                                   Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                                                   VerticalAlignment="Center"
                                                                   Margin="0,0,8,0"/>

                                                    <!-- 内容 -->
                                                    <StackPanel Grid.Column="2" VerticalAlignment="Center">
                                                        <TextBlock Text="{Binding Name}"
                                                                   FontWeight="Medium"
                                                                   FontSize="13"
                                                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                                                        <TextBlock Text="{Binding Description}"
                                                                   FontSize="11"
                                                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                                   Margin="0,1,0,0"/>
                                                    </StackPanel>

                                                    <!-- 分类标签 -->
                                                    <TextBlock Grid.Column="3"
                                                               Text="{Binding Category}"
                                                               FontSize="10"
                                                               Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                                               FontWeight="Medium"
                                                               VerticalAlignment="Center"
                                                               Margin="0,0,8,0"/>

                                                    <!-- 启用状态 -->
                                                    <ToggleButton Grid.Column="4"
                                                                  IsChecked="{Binding IsEnabled, Mode=TwoWay}"
                                                                  Content="{Binding IsEnabled, Converter={StaticResource BoolToEnabledTextConverter}}"
                                                                  VerticalAlignment="Center"
                                                                  FontSize="10"
                                                                  Padding="6,2"/>
                                                </Grid>
                                            </DataTemplate>
                                        </ListView.ItemTemplate>
                                    </ListView>

                                    <!-- 边框样式展示 -->
                                    <TextBlock Text="边框样式（完整边框包围）："
                                               FontWeight="Medium"
                                               FontSize="14"
                                               Margin="0,16,0,8"/>

                                    <ListView ItemsSource="{Binding DataItems}"
                                              SelectedItem="{Binding SelectedDataItem, Mode=TwoWay}"
                                              SelectionMode="{Binding IsMultiSelectEnabled, Converter={StaticResource BoolToSelectionModeConverter}}"
                                              Style="{StaticResource BorderedListViewStyle}"
                                              Height="200"
                                              ScrollViewer.HorizontalScrollBarVisibility="Disabled">
                                        <ListView.ItemTemplate>
                                            <DataTemplate>
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>

                                                    <!-- 多选复选框 -->
                                                    <CheckBox Grid.Column="0"
                                                              IsChecked="{Binding IsSelected, Mode=TwoWay}"
                                                              Command="{Binding DataContext.ItemSelectionChangedCommand, RelativeSource={RelativeSource AncestorType=ListView}}"
                                                              CommandParameter="{Binding}"
                                                              Visibility="{Binding DataContext.IsMultiSelectEnabled, RelativeSource={RelativeSource AncestorType=ListView}, Converter={StaticResource BoolToVisibilityConverter}}"
                                                              VerticalAlignment="Center"
                                                              Margin="0,0,8,0"/>

                                                    <!-- 图标 -->
                                                    <ui:SymbolIcon Grid.Column="1"
                                                                   Symbol="{Binding Icon}"
                                                                   FontSize="16"
                                                                   Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                                                   VerticalAlignment="Center"
                                                                   Margin="0,0,8,0"/>

                                                    <!-- 内容 -->
                                                    <StackPanel Grid.Column="2" VerticalAlignment="Center">
                                                        <TextBlock Text="{Binding Name}"
                                                                   FontWeight="Medium"
                                                                   FontSize="14"
                                                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                                                        <TextBlock Text="{Binding Description}"
                                                                   FontSize="12"
                                                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                                   Margin="0,2,0,0"/>
                                                    </StackPanel>

                                                    <!-- 分类标签 -->
                                                    <TextBlock Grid.Column="3"
                                                               Text="{Binding Category}"
                                                               FontSize="11"
                                                               Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                                               FontWeight="Medium"
                                                               VerticalAlignment="Center"
                                                               Margin="0,0,8,0"/>

                                                    <!-- 启用状态 -->
                                                    <ToggleButton Grid.Column="4"
                                                                  IsChecked="{Binding IsEnabled, Mode=TwoWay}"
                                                                  Content="{Binding IsEnabled, Converter={StaticResource BoolToEnabledTextConverter}}"
                                                                  VerticalAlignment="Center"
                                                                  FontSize="11"
                                                                  Padding="8,4"/>
                                                </Grid>
                                            </DataTemplate>
                                        </ListView.ItemTemplate>
                                    </ListView>

                                    <!-- 选中内容显示区域 -->
                                    <ui:Card Padding="16" Margin="0,16,0,0">
                                        <StackPanel>
                                            <TextBlock Text="选中内容："
                                                       FontWeight="Medium"
                                                       FontSize="14"
                                                       Margin="0,0,0,8"/>

                                            <!-- 选中内容摘要 -->
                                            <ui:TextBox Text="{Binding SelectedItemsText, Mode=OneWay}"
                                                        IsReadOnly="True"
                                                        FontWeight="Medium"
                                                        Margin="0,0,0,8"/>

                                            <!-- 选中内容详细信息 -->
                                            <ui:TextBox Text="{Binding SelectedItemsDetails, Mode=OneWay}"
                                                        IsReadOnly="True"
                                                        AcceptsReturn="True"
                                                        TextWrapping="Wrap"
                                                        MinHeight="80"
                                                        MaxHeight="150"
                                                        ScrollViewer.VerticalScrollBarVisibility="Auto"
                                                        FontSize="12"
                                                        Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                                        </StackPanel>
                                    </ui:Card>

                                    <!-- 选择状态统计 -->
                                    <Border Background="{DynamicResource CardBackgroundFillColorSecondaryBrush}"
                                            BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                            BorderThickness="1"
                                            CornerRadius="6"
                                            Padding="12"
                                            Margin="0,16,0,0">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                                <TextBlock Text="{Binding DataItems.Count}"
                                                           FontSize="20"
                                                           FontWeight="Bold"
                                                           Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                                           HorizontalAlignment="Center"/>
                                                <TextBlock Text="总项目"
                                                           FontSize="12"
                                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                           HorizontalAlignment="Center"/>
                                            </StackPanel>

                                            <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                                <TextBlock Text="{Binding SelectedItems.Count}"
                                                           FontSize="20"
                                                           FontWeight="Bold"
                                                           Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                                           HorizontalAlignment="Center"/>
                                                <TextBlock Text="已选择"
                                                           FontSize="12"
                                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                           HorizontalAlignment="Center"/>
                                            </StackPanel>

                                            <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                                                <TextBlock Text="{Binding SelectedDataItem.Name, TargetNullValue='无'}"
                                                           FontSize="14"
                                                           FontWeight="Bold"
                                                           Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                                           HorizontalAlignment="Center"
                                                           TextTrimming="CharacterEllipsis"/>
                                                <TextBlock Text="当前选中"
                                                           FontSize="12"
                                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                           HorizontalAlignment="Center"/>
                                            </StackPanel>

                                            <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                                                <TextBlock Text="{Binding InteractionCount}"
                                                           FontSize="20"
                                                           FontWeight="Bold"
                                                           Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                                           HorizontalAlignment="Center"/>
                                                <TextBlock Text="操作次数"
                                                           FontSize="12"
                                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                           HorizontalAlignment="Center"/>
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="现代化MVVM模式代码示例"
                                Language="C#"
                                Description="展示现代化MVVM模式的完整实现，包括数据获取和批量操作"
                                ShowTabs="True"
                                XamlCode="{Binding InteractionXamlExample}"
                                CSharpCode="{Binding InteractionCSharpExample}"
                                IsExpanded="False"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 数据绑定示例 -->
                    <ui:CardExpander Header="📊 数据绑定示例" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 ListView 的数据绑定功能，包括动态数据更新和选择状态管理"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="数据绑定功能演示：" FontWeight="Medium" Margin="0,0,0,16"/>

                                    <!-- 数据统计 -->
                                    <Border Background="{DynamicResource CardBackgroundFillColorSecondaryBrush}"
                                            BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                            BorderThickness="1"
                                            CornerRadius="6"
                                            Padding="12"
                                            Margin="0,0,0,16">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                                <TextBlock Text="{Binding SimpleItems.Count}"
                                                           FontSize="20"
                                                           FontWeight="Bold"
                                                           Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                                           HorizontalAlignment="Center"/>
                                                <TextBlock Text="简单项"
                                                           FontSize="12"
                                                           Foreground="{DynamicResource AccentTextFillColorSecondaryBrush}"
                                                           HorizontalAlignment="Center"/>
                                            </StackPanel>

                                            <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                                <TextBlock Text="{Binding DataItems.Count}"
                                                           FontSize="20"
                                                           FontWeight="Bold"
                                                           Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                                           HorizontalAlignment="Center"/>
                                                <TextBlock Text="数据项"
                                                           FontSize="12"
                                                           Foreground="{DynamicResource AccentTextFillColorSecondaryBrush}"
                                                           HorizontalAlignment="Center"/>
                                            </StackPanel>

                                            <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                                                <TextBlock Text="{Binding SelectedItems.Count}"
                                                           FontSize="20"
                                                           FontWeight="Bold"
                                                           Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                                           HorizontalAlignment="Center"/>
                                                <TextBlock Text="已选择"
                                                           FontSize="12"
                                                           Foreground="{DynamicResource AccentTextFillColorSecondaryBrush}"
                                                           HorizontalAlignment="Center"/>
                                            </StackPanel>

                                            <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                                                <TextBlock Text="{Binding InteractionCount}"
                                                           FontSize="20"
                                                           FontWeight="Bold"
                                                           Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                                           HorizontalAlignment="Center"/>
                                                <TextBlock Text="交互次数"
                                                           FontSize="12"
                                                           Foreground="{DynamicResource AccentTextFillColorSecondaryBrush}"
                                                           HorizontalAlignment="Center"/>
                                            </StackPanel>
                                        </Grid>
                                    </Border>

                                    <!-- 选择状态显示 -->
                                    <Border Background="{DynamicResource CardBackgroundFillColorSecondaryBrush}"
                                            BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                            BorderThickness="1"
                                            CornerRadius="6"
                                            Padding="12"
                                            Margin="0,0,0,16">
                                        <StackPanel>
                                            <TextBlock Text="当前选择状态：" FontWeight="Medium" Margin="0,0,0,8"/>
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>

                                                <StackPanel Grid.Column="0">
                                                    <TextBlock Text="简单项选择：" FontSize="12" Margin="0,0,0,4"/>
                                                    <TextBlock Text="{Binding SelectedSimpleItem, TargetNullValue='未选择'}"
                                                               FontWeight="Medium"
                                                               Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"/>
                                                </StackPanel>

                                                <StackPanel Grid.Column="1">
                                                    <TextBlock Text="数据项选择：" FontSize="12" Margin="0,0,0,4"/>
                                                    <TextBlock Text="{Binding SelectedDataItem.Name, TargetNullValue='未选择'}"
                                                               FontWeight="Medium"
                                                               Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"/>
                                                </StackPanel>
                                            </Grid>
                                        </StackPanel>
                                    </Border>
                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="数据绑定代码示例"
                                Language="XAML"
                                Description="展示 ListView 的数据绑定和动态更新功能"
                                ShowTabs="True"
                                XamlCode="{Binding DataBindingXamlExample}"
                                CSharpCode="{Binding DataBindingCSharpExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                </StackPanel>
            </Border>

            <!-- 底部操作栏 -->
            <Border Grid.Row="2" 
                    Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Padding="16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 统计信息 -->
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock Text="{Binding InteractionCount, StringFormat='交互次数: {0}'}" 
                                   FontSize="14" 
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   Margin="0,0,20,0"/>
                        <TextBlock Text="{Binding LastAction, StringFormat='最后操作: {0}'}" 
                                   FontSize="14" 
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   Margin="0,0,20,0"/>
                        <TextBlock Text="{Binding DataItems.Count, StringFormat='数据项: {0}'}" 
                                   FontSize="14" 
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                    </StackPanel>

                    <!-- 操作按钮 -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <ui:Button Content="重置计数" 
                                   Command="{Binding ResetCountCommand}"
                                   Appearance="Secondary"
                                   Margin="0,0,8,0"/>
                        <ui:Button Content="清除状态" 
                                   Command="{Binding ClearStatusCommand}"
                                   Appearance="Secondary"/>
                    </StackPanel>
                </Grid>
            </Border>

        </Grid>
    </ScrollViewer>
</UserControl>
