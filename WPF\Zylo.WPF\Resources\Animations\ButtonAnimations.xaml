<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 🎯 按钮控件专用动画 -->
    
    <!-- 按钮点击动画 - 快速的缩放反馈 -->
    <Storyboard x:Key="ZyloButtonClickAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                       To="0.95" Duration="0:0:0.1"
                       AutoReverse="True">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                       To="0.95" Duration="0:0:0.1"
                       AutoReverse="True">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 按钮悬停进入动画 -->
    <Storyboard x:Key="ZyloButtonHoverEnterAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                       To="1.05" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="1"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                       To="1.05" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="1"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 按钮悬停退出动画 -->
    <Storyboard x:Key="ZyloButtonHoverExitAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                       To="1" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="1"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                       To="1" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="1"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 按钮禁用状态动画 -->
    <Storyboard x:Key="ZyloButtonDisableAnimation">
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       To="0.5" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="1"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 按钮启用状态动画 -->
    <Storyboard x:Key="ZyloButtonEnableAnimation">
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       To="1" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="1"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 按钮脉冲动画 - 用于吸引注意力 -->
    <Storyboard x:Key="ZyloButtonPulseAnimation" RepeatBehavior="Forever">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                       From="1" To="1.1" Duration="0:0:0.8"
                       AutoReverse="True">
            <DoubleAnimation.EasingFunction>
                <SineEase EasingMode="EaseInOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                       From="1" To="1.1" Duration="0:0:0.8"
                       AutoReverse="True">
            <DoubleAnimation.EasingFunction>
                <SineEase EasingMode="EaseInOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 按钮闪烁动画 - 用于警告或提示 -->
    <Storyboard x:Key="ZyloButtonBlinkAnimation" RepeatBehavior="3x">
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="1" To="0.3" Duration="0:0:0.3"
                       AutoReverse="True">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseInOut" Power="1"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 按钮成功动画 - 绿色闪烁效果 -->
    <Storyboard x:Key="ZyloButtonSuccessAnimation">
        <ColorAnimation Storyboard.TargetProperty="(Control.Background).(SolidColorBrush.Color)"
                       To="#4CAF50" Duration="0:0:0.2"/>
        <ColorAnimation Storyboard.TargetProperty="(Control.Background).(SolidColorBrush.Color)"
                       Duration="0:0:0.8" BeginTime="0:0:0.2">
            <ColorAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </ColorAnimation.EasingFunction>
        </ColorAnimation>
    </Storyboard>

    <!-- 按钮错误动画 - 红色闪烁 + 摇摆 -->
    <Storyboard x:Key="ZyloButtonErrorAnimation">
        <ColorAnimation Storyboard.TargetProperty="(Control.Background).(SolidColorBrush.Color)"
                       To="#F44336" Duration="0:0:0.2"/>
        <ColorAnimation Storyboard.TargetProperty="(Control.Background).(SolidColorBrush.Color)"
                       Duration="0:0:0.8" BeginTime="0:0:0.2">
            <ColorAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </ColorAnimation.EasingFunction>
        </ColorAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                       From="0" To="3" Duration="0:0:0.1"
                       AutoReverse="True" RepeatBehavior="3x"
                       BeginTime="0:0:0.1">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseInOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 按钮加载动画 - 旋转指示器 -->
    <Storyboard x:Key="ZyloButtonLoadingAnimation" RepeatBehavior="Forever">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(RotateTransform.Angle)"
                       From="0" To="360" Duration="0:0:1"/>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                       From="0.6" To="1" Duration="0:0:0.5"
                       AutoReverse="True">
            <DoubleAnimation.EasingFunction>
                <SineEase EasingMode="EaseInOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 按钮弹性点击动画 -->
    <Storyboard x:Key="ZyloButtonElasticClickAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                       To="0.9" Duration="0:0:0.1">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                       To="0.9" Duration="0:0:0.1">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                       To="1.05" Duration="0:0:0.2" BeginTime="0:0:0.1">
            <DoubleAnimation.EasingFunction>
                <ElasticEase EasingMode="EaseOut" Oscillations="2" Springiness="3"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                       To="1.05" Duration="0:0:0.2" BeginTime="0:0:0.1">
            <DoubleAnimation.EasingFunction>
                <ElasticEase EasingMode="EaseOut" Oscillations="2" Springiness="3"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                       To="1" Duration="0:0:0.3" BeginTime="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="1"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                       To="1" Duration="0:0:0.3" BeginTime="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="1"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

</ResourceDictionary>
