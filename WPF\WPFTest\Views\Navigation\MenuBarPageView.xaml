<UserControl
    d:DataContext="{d:DesignInstance navigation:MenuBarPageViewModel}"
    d:DesignHeight="2000"
    d:DesignWidth="1000"
    mc:Ignorable="d"
    mvvm:ViewModelLocator.AutoWireViewModel="True"
    x:Class="WPFTest.Views.Navigation.MenuBarPageView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:codeExample="clr-namespace:Zylo.WPF.Controls.CodeExample;assembly=Zylo.WPF"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:mvvm="http://prismlibrary.com/"
    xmlns:models="clr-namespace:Zylo.WPF.Models.Navigation;assembly=Zylo.WPF"
    xmlns:navigation="clr-namespace:WPFTest.ViewModels.Navigation"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:zylo="clr-namespace:Zylo.WPF.Controls.Navigation;assembly=Zylo.WPF">

    <UserControl.Resources>
        <!--  页面样式资源  -->
        <Style TargetType="ui:Card" x:Key="ExampleCardStyle">
            <Setter Property="Padding" Value="20" />
            <Setter Property="Margin" Value="0,0,0,16" />
            <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}" />
            <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}" />
            <Setter Property="BorderThickness" Value="1" />
        </Style>

        <Style TargetType="TextBlock" x:Key="SectionHeaderStyle">
            <Setter Property="FontSize" Value="18" />
            <Setter Property="FontWeight" Value="SemiBold" />
            <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}" />
            <Setter Property="Margin" Value="0,0,0,12" />
        </Style>

        <Style TargetType="TextBlock" x:Key="DescriptionStyle">
            <Setter Property="FontSize" Value="14" />
            <Setter Property="Foreground" Value="{DynamicResource TextFillColorSecondaryBrush}" />
            <Setter Property="TextWrapping" Value="Wrap" />
            <Setter Property="LineHeight" Value="20" />
            <Setter Property="Margin" Value="0,0,0,16" />
        </Style>

        <Style TargetType="TextBlock" x:Key="StatusStyle">
            <Setter Property="FontSize" Value="13" />
            <Setter Property="Foreground" Value="{DynamicResource TextFillColorTertiaryBrush}" />
            <Setter Property="FontStyle" Value="Italic" />
        </Style>
    </UserControl.Resources>

    <ScrollViewer
        HorizontalScrollBarVisibility="Disabled"
        Padding="20"
        VerticalScrollBarVisibility="Auto">
        <StackPanel>

            <!--  页面标题区域  -->
            <Border
                Background="{DynamicResource AccentFillColorDefaultBrush}"
                CornerRadius="12"
                Margin="0,0,0,20"
                Padding="24">
                <StackPanel>
                    <StackPanel Margin="0,0,0,8" Orientation="Horizontal">
                        <ui:SymbolIcon
                            FontSize="32"
                            Foreground="{DynamicResource TextOnAccentFillColorPrimaryBrush}"
                            Margin="0,0,12,0"
                            Symbol="Navigation24" />
                        <TextBlock
                            FontSize="28"
                            FontWeight="Bold"
                            Foreground="{DynamicResource TextOnAccentFillColorPrimaryBrush}"
                            Text="{Binding PageTitle}"
                            VerticalAlignment="Center" />
                    </StackPanel>
                    <TextBlock
                        FontSize="16"
                        Foreground="{DynamicResource TextOnAccentFillColorSecondaryBrush}"
                        LineHeight="22"
                        Text="{Binding PageDescription}"
                        TextWrapping="Wrap" />
                </StackPanel>
            </Border>

            <!--  状态信息区域  -->
            <ui:Card Margin="0,0,0,20" Style="{StaticResource ExampleCardStyle}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Style="{StaticResource SectionHeaderStyle}" Text="📊 实时状态" />
                        <StackPanel Orientation="Horizontal">
                            <StackPanel Margin="0,0,20,0">
                                <TextBlock
                                    FontWeight="Medium"
                                    Margin="0,0,0,4"
                                    Text="状态消息:" />
                                <TextBlock Style="{StaticResource StatusStyle}" Text="{Binding StatusMessage}" />
                            </StackPanel>
                            <StackPanel Margin="0,0,20,0">
                                <TextBlock
                                    FontWeight="Medium"
                                    Margin="0,0,0,4"
                                    Text="最后操作:" />
                                <TextBlock Style="{StaticResource StatusStyle}" Text="{Binding LastAction}" />
                            </StackPanel>
                            <StackPanel>
                                <TextBlock
                                    FontWeight="Medium"
                                    Margin="0,0,0,4"
                                    Text="点击次数:" />
                                <TextBlock Style="{StaticResource StatusStyle}" Text="{Binding ClickCount}" />
                            </StackPanel>
                        </StackPanel>
                    </StackPanel>

                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <ui:Button
                            Appearance="Primary"
                            Command="{Binding ToggleMenuBarEnabledCommand}"
                            Content="切换启用状态" />
                    </StackPanel>
                </Grid>
            </ui:Card>







            <!--  WPF-UI 原生 Menu 控件展示  -->
            <ui:CardExpander
                Header="🎯 WPF-UI 原生 Menu 控件"
                IsExpanded="True"
                Margin="0,0,0,20">
                <ui:CardExpander.Icon>
                    <ui:SymbolIcon Symbol="Navigation24" />
                </ui:CardExpander.Icon>

                <StackPanel>
                    <TextBlock Style="{StaticResource DescriptionStyle}" Text="WPF-UI 确实提供了原生的 Menu 控件，支持图标、分隔符等功能。" />

                    <!--  WPF-UI 原生 Menu 演示  -->
                    <ui:Card Style="{StaticResource ExampleCardStyle}">
                        <StackPanel>
                            <TextBlock Style="{StaticResource SectionHeaderStyle}" Text="🔧 WPF-UI 原生 Menu 控件" />

                            <!--  WPF-UI 原生 Menu  -->
                            <Border
                                BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                BorderThickness="1"
                                CornerRadius="6"
                                Margin="0,0,0,16">
                                <Menu FontSize="14">
                                    <ui:MenuItem Header="File" Icon="{ui:SymbolIcon Symbol=Document24}">
                                        <ui:MenuItem Header="New" InputGestureText="CTRL+N" Command="{Binding MenuItemClickCommand}" CommandParameter="New"/>
                                        <ui:MenuItem Header="Open..." InputGestureText="CTRL+O" Command="{Binding MenuItemClickCommand}" CommandParameter="Open"/>
                                        <ui:MenuItem Header="Save" InputGestureText="CTRL+S" Command="{Binding MenuItemClickCommand}" CommandParameter="Save"/>
                                        <ui:MenuItem Header="Save As..." InputGestureText="CTRL+SHIFT+S" Command="{Binding MenuItemClickCommand}" CommandParameter="SaveAs"/>
                                        <Separator />
                                        <ui:MenuItem Header="Exit" Command="{Binding MenuItemClickCommand}" CommandParameter="Exit"/>
                                    </ui:MenuItem>
                                    <ui:MenuItem Header="Edit" Icon="{ui:SymbolIcon DocumentEdit20}">
                                        <ui:MenuItem Header="Undo" InputGestureText="CTRL+Z" Command="{Binding MenuItemClickCommand}" CommandParameter="Undo"/>
                                        <Separator />
                                        <ui:MenuItem Header="Cut" InputGestureText="CTRL+X" Command="{Binding MenuItemClickCommand}" CommandParameter="Cut"/>
                                        <ui:MenuItem Header="Copy" InputGestureText="CTRL+C" Command="{Binding MenuItemClickCommand}" CommandParameter="Copy"/>
                                        <ui:MenuItem Header="Paste" InputGestureText="CTRL+V" Command="{Binding MenuItemClickCommand}" CommandParameter="Paste"/>
                                        <Separator />
                                        <ui:MenuItem Header="Word wrap" InputGestureText="CTRL+SHIFT+W" IsCheckable="True" IsChecked="True" Command="{Binding MenuItemClickCommand}" CommandParameter="WordWrap"/>
                                        <ui:MenuItem Header="Find..." InputGestureText="CTRL+F" Command="{Binding MenuItemClickCommand}" CommandParameter="Find"/>
                                        <ui:MenuItem Header="Find next" InputGestureText="F3" Command="{Binding MenuItemClickCommand}" CommandParameter="FindNext"/>
                                        <Separator />
                                        <ui:MenuItem Header="Select All" InputGestureText="CTRL+A" Command="{Binding MenuItemClickCommand}" CommandParameter="SelectAll"/>
                                    </ui:MenuItem>
                                    <Separator />
                                    <ui:MenuItem Icon="{ui:SymbolIcon TextBold20}" Command="{Binding MenuItemClickCommand}" CommandParameter="Bold"/>
                                    <ui:MenuItem Icon="{ui:SymbolIcon TextItalic20}" Command="{Binding MenuItemClickCommand}" CommandParameter="Italic"/>
                                    <ui:MenuItem Icon="{ui:SymbolIcon TextUnderline20}" Command="{Binding MenuItemClickCommand}" CommandParameter="Underline"/>
                                </Menu>
                            </Border>

                            <TextBlock Style="{StaticResource StatusStyle}" Text="💡 这是 WPF-UI 的原生 Menu 控件（静态声明版本）。" />
                        </StackPanel>
                    </ui:Card>

                    <!-- WPF-UI 原生 Menu 数据绑定版本 -->
                    <ui:Card Style="{StaticResource ExampleCardStyle}">
                        <StackPanel>
                            <TextBlock Text="🔗 WPF-UI 原生 Menu 控件（数据绑定版本）" Style="{StaticResource SectionHeaderStyle}"/>

                            <!-- WPF-UI 原生 Menu 数据绑定 -->
                            <Border BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                    BorderThickness="1"
                                    CornerRadius="6"
                                    Margin="0,0,0,16">
                                <Menu FontSize="14">
                                    <!-- 使用 ItemsSource 进行数据绑定 -->
                                    <Menu.Resources>
                                        <!-- 为 WPF-UI MenuItem 定义数据模板 -->
                                        <HierarchicalDataTemplate DataType="{x:Type models:MenuItemModel}" ItemsSource="{Binding Children}">
                                            <StackPanel Orientation="Horizontal">
                                                <ContentPresenter Content="{Binding Icon}" Margin="0,0,8,0"/>
                                                <TextBlock Text="{Binding Header}" VerticalAlignment="Center"/>
                                                <TextBlock Text="{Binding InputGestureText}"
                                                           Margin="20,0,0,0"
                                                           VerticalAlignment="Center"
                                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                           FontSize="12"/>
                                            </StackPanel>
                                        </HierarchicalDataTemplate>
                                    </Menu.Resources>

                                    <!-- 绑定到数据源 -->
                                    <Menu.ItemsSource>
                                        <Binding Path="WpfUiMenuItems"/>
                                    </Menu.ItemsSource>

                                    <!-- 设置 MenuItem 的样式和命令绑定 -->
                                    <Menu.ItemContainerStyle>
                                        <Style TargetType="MenuItem">
                                            <Setter Property="Command" Value="{Binding Command}"/>
                                            <Setter Property="CommandParameter" Value="{Binding CommandParameter}"/>
                                            <Setter Property="IsEnabled" Value="{Binding IsEnabled}"/>
                                            <Setter Property="IsChecked" Value="{Binding IsChecked}"/>
                                            <Style.Triggers>
                                                <!-- 分隔线处理 -->
                                                <DataTrigger Binding="{Binding IsSeparator}" Value="True">
                                                    <Setter Property="Template">
                                                        <Setter.Value>
                                                            <ControlTemplate TargetType="MenuItem">
                                                                <Separator Margin="0,2" Background="{DynamicResource ControlStrokeColorDefaultBrush}"/>
                                                            </ControlTemplate>
                                                        </Setter.Value>
                                                    </Setter>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Menu.ItemContainerStyle>
                                </Menu>
                            </Border>

                            <TextBlock Style="{StaticResource StatusStyle}" Text="💡 这是 WPF-UI 原生 Menu 控件的数据绑定版本，支持动态菜单内容。" />
                        </StackPanel>
                    </ui:Card>
                </StackPanel>
            </ui:CardExpander>

            <!--  WPF-UI 替代方案对比  -->
            <ui:CardExpander
                Header="🆚 WPF-UI 替代方案对比"
                IsExpanded="True"
                Margin="0,0,0,20">
                <ui:CardExpander.Icon>
                    <ui:SymbolIcon Symbol="Code20" />
                </ui:CardExpander.Icon>

                <StackPanel>
                    <TextBlock Style="{StaticResource DescriptionStyle}" Text="对比 WPF-UI 原生 Menu 控件和 ZyloWPF MenuBarControl 的特点和优势。" />

                    <!--  对比说明  -->
                    <ui:Card Style="{StaticResource ExampleCardStyle}">
                        <StackPanel>
                            <TextBlock Style="{StaticResource SectionHeaderStyle}" Text="📊 方案对比" />

                            <!--  对比信息  -->
                            <StackPanel Margin="0,0,0,16">
                                <TextBlock
                                    FontWeight="Medium"
                                    Margin="0,0,0,8"
                                    Text="✅ WPF-UI 原生 Menu 优势：" />
                                <TextBlock Margin="16,0,0,4" Text="• 官方支持，稳定可靠" />
                                <TextBlock Margin="16,0,0,4" Text="• 完美集成 WPF-UI 主题" />
                                <TextBlock Margin="16,0,0,4" Text="• 支持图标和现代化样式" />
                                <TextBlock Margin="16,0,0,4" Text="• 标准的 WPF Menu 功能" />
                                <TextBlock Margin="16,0,0,12" Text="• 轻量级，性能优秀" />

                                <TextBlock
                                    FontWeight="Medium"
                                    Margin="0,0,0,8"
                                    Text="✅ ZyloWPF MenuBarControl 优势：" />
                                <TextBlock Margin="16,0,0,4" Text="• 增强的 MVVM 数据绑定" />
                                <TextBlock Margin="16,0,0,4" Text="• 更丰富的自定义选项" />
                                <TextBlock Margin="16,0,0,4" Text="• 统一的 API 设计" />
                                <TextBlock Margin="16,0,0,4" Text="• 更好的可扩展性" />
                                <TextBlock Margin="16,0,0,12" Text="• 框架级别的集成" />

                                <TextBlock
                                    FontWeight="Medium"
                                    Margin="0,0,0,8"
                                    Text="📊 使用建议：" />
                                <TextBlock Margin="16,0,0,4" Text="• 简单应用：使用 WPF-UI 原生 Menu" />
                                <TextBlock Margin="16,0,0,4" Text="• 复杂应用：使用 ZyloWPF MenuBarControl" />
                                <TextBlock Margin="16,0,0,4" Text="• 框架集成：推荐 ZyloWPF 方案" />
                                <TextBlock Margin="16,0,0,4" Text="• 性能优先：使用 WPF-UI 原生方案" />
                            </StackPanel>

                            <TextBlock Style="{StaticResource StatusStyle}" Text="💡 结论：WPF-UI 有原生 Menu 控件，ZyloWPF MenuBarControl 提供了增强功能，两者各有优势。" />
                        </StackPanel>
                    </ui:Card>

                    <!--  WPF-UI 替代方案示例  -->
                    <ui:Card Style="{StaticResource ExampleCardStyle}">
                        <StackPanel>
                            <TextBlock Style="{StaticResource SectionHeaderStyle}" Text="🔧 WPF-UI 替代方案示例" />

                            <!--  使用 Button 的方案  -->
                            <TextBlock
                                FontWeight="Medium"
                                Margin="0,0,0,8"
                                Text="方案：使用 Button 组合（仅演示外观）" />
                            <Border
                                BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                BorderThickness="1"
                                CornerRadius="6"
                                Margin="0,0,0,16">
                                <StackPanel
                                    Background="{DynamicResource ApplicationBackgroundBrush}"
                                    Margin="8"
                                    Orientation="Horizontal">
                                    <!--  文件菜单  -->
                                    <ui:Button
                                        Appearance="Transparent"
                                        Content="文件(_F)"
                                        Margin="0,0,4,0" />

                                    <!--  编辑菜单  -->
                                    <ui:Button
                                        Appearance="Transparent"
                                        Content="编辑(_E)"
                                        Margin="0,0,4,0" />

                                    <!--  帮助菜单  -->
                                    <ui:Button Appearance="Transparent" Content="帮助(_H)" />
                                </StackPanel>
                            </Border>

                            <TextBlock Style="{StaticResource StatusStyle}" Text="💡 说明：这只是外观模拟，缺少下拉菜单、键盘导航等核心功能。实际使用需要配合 Flyout 等控件。" />
                        </StackPanel>
                    </ui:Card>
                </StackPanel>
            </ui:CardExpander>

        </StackPanel>
    </ScrollViewer>
</UserControl>
