<!-- SplitButton 基础示例 -->
<StackPanel Margin="20" Spacing="15">
    
    <!-- 基础用法 -->
    <GroupBox Header="基础用法 - 保存操作" Padding="15">
        <StackPanel>
            <TextBlock Text="SplitButton 提供主要操作按钮和下拉菜单选项" FontSize="12" Margin="0,0,0,10"/>
            
            <ui:SplitButton Content="保存" 
                            Command="{Binding HandlePrimaryActionCommand}"
                            CommandParameter="保存"
                            Margin="8">
                <ui:SplitButton.Icon>
                    <ui:SymbolIcon Symbol="Save24"/>
                </ui:SplitButton.Icon>
                <ui:SplitButton.Flyout>
                    <ContextMenu>
                        <MenuItem Header="保存" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="保存">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="Save24"/>
                            </MenuItem.Icon>
                        </MenuItem>
                        <MenuItem Header="另存为" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="另存为">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="DocumentSave24"/>
                            </MenuItem.Icon>
                        </MenuItem>
                        <Separator/>
                        <MenuItem Header="保存所有" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="保存所有">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="Save24"/>
                            </MenuItem.Icon>
                        </MenuItem>
                    </ContextMenu>
                </ui:SplitButton.Flyout>
            </ui:SplitButton>
        </StackPanel>
    </GroupBox>

    <!-- 不同外观 -->
    <GroupBox Header="不同外观样式" Padding="15">
        <StackPanel>
            <TextBlock Text="SplitButton 支持多种外观样式：Primary、Secondary、Danger 等" FontSize="12" Margin="0,0,0,10"/>
            
            <WrapPanel>
                <!-- 默认样式 -->
                <ui:SplitButton Content="默认" 
                                Command="{Binding HandlePrimaryActionCommand}"
                                CommandParameter="默认"
                                Margin="8">
                    <ui:SplitButton.Flyout>
                        <ContextMenu>
                            <MenuItem Header="选项 1" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="选项 1"/>
                            <MenuItem Header="选项 2" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="选项 2"/>
                        </ContextMenu>
                    </ui:SplitButton.Flyout>
                </ui:SplitButton>

                <!-- Primary 样式 -->
                <ui:SplitButton Content="Primary" 
                                Command="{Binding HandlePrimaryActionCommand}"
                                CommandParameter="Primary"
                                Appearance="Primary"
                                Margin="8">
                    <ui:SplitButton.Flyout>
                        <ContextMenu>
                            <MenuItem Header="选项 1" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="选项 1"/>
                            <MenuItem Header="选项 2" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="选项 2"/>
                        </ContextMenu>
                    </ui:SplitButton.Flyout>
                </ui:SplitButton>

                <!-- Danger 样式 -->
                <ui:SplitButton Content="Danger" 
                                Command="{Binding HandlePrimaryActionCommand}"
                                CommandParameter="Danger"
                                Appearance="Danger"
                                Margin="8">
                    <ui:SplitButton.Flyout>
                        <ContextMenu>
                            <MenuItem Header="删除" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="删除"/>
                            <MenuItem Header="永久删除" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="永久删除"/>
                        </ContextMenu>
                    </ui:SplitButton.Flyout>
                </ui:SplitButton>
            </WrapPanel>
        </StackPanel>
    </GroupBox>

    <!-- 带图标的 SplitButton -->
    <GroupBox Header="带图标的 SplitButton" Padding="15">
        <StackPanel>
            <TextBlock Text="为 SplitButton 添加图标可以提高用户体验和视觉识别度" FontSize="12" Margin="0,0,0,10"/>
            
            <WrapPanel>
                <ui:SplitButton Content="发送邮件" 
                                Command="{Binding HandlePrimaryActionCommand}"
                                CommandParameter="发送邮件"
                                Appearance="Primary"
                                Margin="8">
                    <ui:SplitButton.Icon>
                        <ui:SymbolIcon Symbol="Mail24"/>
                    </ui:SplitButton.Icon>
                    <ui:SplitButton.Flyout>
                        <ContextMenu>
                            <MenuItem Header="立即发送" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="立即发送">
                                <MenuItem.Icon>
                                    <ui:SymbolIcon Symbol="Send24"/>
                                </MenuItem.Icon>
                            </MenuItem>
                            <MenuItem Header="定时发送" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="定时发送">
                                <MenuItem.Icon>
                                    <ui:SymbolIcon Symbol="Clock24"/>
                                </MenuItem.Icon>
                            </MenuItem>
                            <MenuItem Header="保存草稿" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="保存草稿">
                                <MenuItem.Icon>
                                    <ui:SymbolIcon Symbol="Document24"/>
                                </MenuItem.Icon>
                            </MenuItem>
                        </ContextMenu>
                    </ui:SplitButton.Flyout>
                </ui:SplitButton>

                <ui:SplitButton Content="下载" 
                                Command="{Binding HandlePrimaryActionCommand}"
                                CommandParameter="下载"
                                Margin="8">
                    <ui:SplitButton.Icon>
                        <ui:SymbolIcon Symbol="Download24"/>
                    </ui:SplitButton.Icon>
                    <ui:SplitButton.Flyout>
                        <ContextMenu>
                            <MenuItem Header="下载到默认位置" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="下载到默认位置">
                                <MenuItem.Icon>
                                    <ui:SymbolIcon Symbol="Download24"/>
                                </MenuItem.Icon>
                            </MenuItem>
                            <MenuItem Header="选择下载位置" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="选择下载位置">
                                <MenuItem.Icon>
                                    <ui:SymbolIcon Symbol="Folder24"/>
                                </MenuItem.Icon>
                            </MenuItem>
                            <MenuItem Header="下载历史" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="下载历史">
                                <MenuItem.Icon>
                                    <ui:SymbolIcon Symbol="History24"/>
                                </MenuItem.Icon>
                            </MenuItem>
                        </ContextMenu>
                    </ui:SplitButton.Flyout>
                </ui:SplitButton>
            </WrapPanel>
        </StackPanel>
    </GroupBox>

    <!-- 启用/禁用状态 -->
    <GroupBox Header="启用/禁用状态" Padding="15">
        <StackPanel>
            <TextBlock Text="SplitButton 可以通过 IsEnabled 属性控制启用状态" FontSize="12" Margin="0,0,0,10"/>
            
            <StackPanel Orientation="Horizontal">
                <ui:SplitButton Content="启用状态" 
                                Command="{Binding HandlePrimaryActionCommand}"
                                CommandParameter="启用状态"
                                IsEnabled="True"
                                Margin="8">
                    <ui:SplitButton.Flyout>
                        <ContextMenu>
                            <MenuItem Header="选项 1" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="选项 1"/>
                            <MenuItem Header="选项 2" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="选项 2"/>
                        </ContextMenu>
                    </ui:SplitButton.Flyout>
                </ui:SplitButton>

                <ui:SplitButton Content="禁用状态" 
                                Command="{Binding HandlePrimaryActionCommand}"
                                CommandParameter="禁用状态"
                                IsEnabled="False"
                                Margin="8">
                    <ui:SplitButton.Flyout>
                        <ContextMenu>
                            <MenuItem Header="选项 1" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="选项 1"/>
                            <MenuItem Header="选项 2" Command="{Binding HandleDropdownInteractionCommand}" CommandParameter="选项 2"/>
                        </ContextMenu>
                    </ui:SplitButton.Flyout>
                </ui:SplitButton>
            </StackPanel>
        </StackPanel>
    </GroupBox>

</StackPanel>
