using System.Text.Json.Serialization;
using System.Windows.Media;
using Wpf.Ui.Appearance;

namespace Zylo.WPF.Models;

/// <summary>
/// 主题配置模型 - 支持持久化的主题设置
/// </summary>
public class ThemeConfiguration
{
    /// <summary>
    /// 主题模式：Light, Dark, Auto
    /// </summary>
    [JsonPropertyName("themeMode")]
    public string ThemeMode { get; set; } = "Auto";

    /// <summary>
    /// 自定义强调色（十六进制格式）
    /// </summary>
    [JsonPropertyName("accentColor")]
    public string AccentColor { get; set; } = "#0078D4"; // 默认蓝色

    /// <summary>
    /// 是否启用自定义强调色
    /// </summary>
    [JsonPropertyName("useCustomAccentColor")]
    public bool UseCustomAccentColor { get; set; } = false;

    /// <summary>
    /// 配置文件版本
    /// </summary>
    [JsonPropertyName("version")]
    public string Version { get; set; } = "1.0";

    /// <summary>
    /// 最后更新时间
    /// </summary>
    [JsonPropertyName("lastUpdated")]
    public DateTime LastUpdated { get; set; } = DateTime.Now;

    /// <summary>
    /// 获取ApplicationTheme枚举值
    /// </summary>
    [JsonIgnore]
    public ApplicationTheme ApplicationTheme
    {
        get
        {
            return ThemeMode switch
            {
                "Light" => ApplicationTheme.Light,
                "Dark" => ApplicationTheme.Dark,
                "Auto" => ApplicationTheme.Unknown, // WPF-UI中Unknown表示跟随系统
                _ => ApplicationTheme.Unknown
            };
        }
    }

    /// <summary>
    /// 获取强调色的Color对象
    /// </summary>
    [JsonIgnore]
    public Color AccentColorValue
    {
        get
        {
            try
            {
                return (Color)ColorConverter.ConvertFromString(AccentColor);
            }
            catch
            {
                return (Color)ColorConverter.ConvertFromString("#0078D4"); // 默认蓝色
            }
        }
    }

    /// <summary>
    /// 预定义的强调色选项
    /// </summary>
    [JsonIgnore]
    public static readonly Dictionary<string, string> PredefinedAccentColors = new()
    {
        { "蓝色 (默认)", "#0078D4" },
        { "紫色", "#8B5CF6" },
        { "绿色", "#10B981" },
        { "橙色", "#F59E0B" },
        { "红色", "#EF4444" },
        { "粉色", "#EC4899" },
        { "青色", "#06B6D4" },
        { "靛蓝", "#6366F1" },
        { "石灰绿", "#84CC16" },
        { "琥珀色", "#F59E0B" },
        { "玫瑰红", "#F43F5E" },
        { "天蓝色", "#0EA5E9" }
    };

    /// <summary>
    /// 主题模式选项
    /// </summary>
    [JsonIgnore]
    public static readonly Dictionary<string, string> ThemeModeOptions = new()
    {
        { "🌙 深色", "Dark" },
        { "☀️ 浅色", "Light" },
        { "🔄 跟随系统", "Auto" }
    };

    /// <summary>
    /// 创建默认配置
    /// </summary>
    public static ThemeConfiguration CreateDefault()
    {
        return new ThemeConfiguration
        {
            ThemeMode = "Auto",
            AccentColor = "#0078D4",
            UseCustomAccentColor = false,
            Version = "1.0",
            LastUpdated = DateTime.Now
        };
    }

    /// <summary>
    /// 验证配置是否有效
    /// </summary>
    public bool IsValid()
    {
        try
        {
            // 验证主题模式
            if (!ThemeModeOptions.ContainsValue(ThemeMode))
                return false;

            // 验证强调色格式
            ColorConverter.ConvertFromString(AccentColor);

            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 克隆配置
    /// </summary>
    public ThemeConfiguration Clone()
    {
        return new ThemeConfiguration
        {
            ThemeMode = ThemeMode,
            AccentColor = AccentColor,
            UseCustomAccentColor = UseCustomAccentColor,
            Version = Version,
            LastUpdated = DateTime.Now
        };
    }
}
