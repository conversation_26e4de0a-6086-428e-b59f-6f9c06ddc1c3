using System.Collections.ObjectModel;
using System.ComponentModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using GongSolutions.Wpf.DragDrop;
using WPFTest.Models.DWG;
using WPFTest.Services.DWG;
using Wpf.Ui.Controls;
using Zylo.WPF.Services;
using Zylo.WPF.ViewModels;

namespace WPFTest.ViewModels.DWG;

/// <summary>
/// DWG 文件夹编辑器 ViewModel - 智能文件夹管理系统
/// </summary>
/// <remarks>
/// 🎯 核心功能：
/// - 管理DWG项目文件夹的完整生命周期（增删改查）
/// - 提供智能路径扫描和自动文件夹创建功能
/// - 支持专业文件夹模板（结构、建筑、暖通、给排水、电气、园林）
/// - 实现拖拽排序和可视化文件夹管理界面
///
/// 📋 主要属性：
/// - Folders: 存储所有文件夹的观察集合
/// - SelectedFolder: 当前用户选中的文件夹
/// - EditingFolder: 正在编辑中的文件夹（支持新建/修改）
/// - SearchText: 搜索过滤文本（支持实时过滤）
/// - IsLoading: 加载状态指示器
/// - StatusMessage: 状态栏消息显示
/// - ScanPath: 扫描路径输入
/// - ScanResult: 最近一次扫描的结果信息
///
/// 🎛️ 核心命令：
/// - LoadDataAsync(): 从数据库异步加载所有文件夹
/// - NewFolder(): 创建新文件夹并进入编辑模式
/// - EditFolder(): 编辑选中的文件夹（克隆对象避免直接修改）
/// - SaveAsync(): 保存文件夹到数据库（支持新建和更新）
/// - DeleteAsync(): 删除选中的文件夹（保护系统默认文件夹）
/// - ScanFoldersAsync(): 扫描指定路径并自动管理文件夹
/// - CreateDefaultFoldersAsync(): 在指定路径创建默认专业文件夹
/// - Cancel(): 取消编辑操作并恢复原始状态
///
/// 🔍 智能扫描功能：
/// 1. 路径验证：检查指定路径是否存在和可访问
/// 2. 默认文件夹创建：自动创建专业文件夹（结构、建筑等）
/// 3. 现有文件夹发现：扫描并纳入管理现有的文件夹
/// 4. 图标智能识别：根据文件夹名称自动分配合适的图标
/// 5. 结果统计：提供详细的扫描结果和操作统计
///
/// 🔄 数据流程：
/// 1. 初始化时自动调用LoadDataAsync()加载数据
/// 2. 用户选择扫描路径并触发ScanFoldersAsync()
/// 3. Service层执行文件系统操作和数据库更新
/// 4. 监听Service层事件并更新UI状态
/// 5. 显示扫描结果和操作统计
///
/// 🎨 设计模式：
/// - MVVM模式：清晰的视图-视图模型分离
/// - 命令模式：使用RelayCommand处理用户交互
/// - 观察者模式：通过ObservableProperty实现数据绑定
/// - 事件驱动：监听Service层的FolderChanged和ScanCompleted事件
/// - 拖拽模式：实现IDropTarget支持拖拽排序
///
/// 🛡️ 数据验证：
/// - 继承BaseValidatorViewModel提供验证支持
/// - 文件夹名称重复检查
/// - 路径有效性验证
/// - CanExecute方法控制命令可用性
///
/// 🔧 技术特点：
/// - 使用CommunityToolkit.Mvvm自动生成样板代码
/// - 异步优先的操作模式
/// - 完整的错误处理和日志记录
/// - 支持取消操作和状态恢复
/// - 实现IDisposable进行资源清理
/// - 支持拖拽排序功能
/// </remarks>
public partial class DwgFolderEditorViewModel : BaseValidatorViewModel, IDropTarget, IDisposable
{
    #region 字段和服务
    private readonly YLoggerInstance _logger = YLogger.ForSilent<DwgFolderEditorViewModel>();
    private readonly IDwgFolderService _service;

    #endregion

    #region 构造函数

    /// <summary>
    /// 构造函数 - 初始化 ViewModel 并加载数据
    /// </summary>
    /// <param name="service">文件夹服务</param>
    public DwgFolderEditorViewModel(IDwgFolderService service)
    {
        _service = service;

        // 订阅服务事件
        _service.FolderChanged += OnFolderChanged;
        _service.ScanCompleted += OnScanCompleted;

        // 异步加载数据
        _ = LoadDataAsync();
    }

    #endregion

    #region 可观察属性

    /// <summary>
    /// 文件夹列表（从数据库加载的所有文件夹）
    /// </summary>
    [ObservableProperty]
    private ObservableCollection<DwgFolderModel> folders = new();

    /// <summary>
    /// 选中的文件夹（用户在列表中选择的项）
    /// </summary>
    [ObservableProperty]
    [NotifyCanExecuteChangedFor(nameof(DeleteCommand))]
    [NotifyCanExecuteChangedFor(nameof(EditFolderCommand))]
    private DwgFolderModel? selectedFolder;

    partial void OnSelectedFolderChanged(DwgFolderModel? value)
    {
        _logger.Info($"🔄 FolderEditor.SelectedFolder 变化: {value?.Name ?? "null"}");

        // 更新所有文件夹的选中状态
        foreach (var folder in Folders)
        {
            folder.IsSelected = folder == value;
        }
    }

    /// <summary>
    /// 正在编辑的文件夹（用于新增/编辑）
    /// </summary>
    [ObservableProperty]
    [NotifyCanExecuteChangedFor(nameof(SaveCommand))]
    private DwgFolderModel? editingFolder;

    /// <summary>
    /// 搜索文本（用于过滤文件夹列表）
    /// </summary>
    [ObservableProperty]
    private string searchText = string.Empty;

    /// <summary>
    /// 是否正在加载（显示进度指示器）
    /// </summary>
    [ObservableProperty]
    private bool isLoading;

    /// <summary>
    /// 状态消息（显示在状态栏）
    /// </summary>
    [ObservableProperty]
    private string statusMessage = "就绪";

    /// <summary>
    /// 是否正在编辑（控制编辑表单显示）
    /// </summary>
    [ObservableProperty]
    private bool isEditing;

    /// <summary>
    /// 是否为新建模式（用于区分新建和编辑）
    /// </summary>
    [ObservableProperty]
    private bool isNewMode;

    /// <summary>
    /// 扫描路径（用于路径扫描功能）
    /// </summary>
    [ObservableProperty]
    [NotifyCanExecuteChangedFor(nameof(ScanPathCommand))]
    private string scanPath = string.Empty;

    /// <summary>
    /// 是否正在扫描（显示扫描进度）
    /// </summary>
    [ObservableProperty]
    [NotifyCanExecuteChangedFor(nameof(ScanPathCommand))]
    private bool isScanning;



    #endregion



    #region 计算属性

    /// <summary>
    /// 过滤后的文件夹列表（缓存版本）
    /// </summary>
    [ObservableProperty]
    private ObservableCollection<DwgFolderModel> filteredFolders = new();

    /// <summary>
    /// 显示的文件夹列表 - 根据是否有搜索来决定显示哪个集合
    /// </summary>
    public ObservableCollection<DwgFolderModel> DisplayFolders
    {
        get => string.IsNullOrWhiteSpace(SearchText) ? Folders : FilteredFolders;
    }

    /// <summary>
    /// 更新过滤后的文件夹列表
    /// </summary>
    private void UpdateFilteredFolders()
    {
        FilteredFolders.Clear();

        var itemsToShow = string.IsNullOrWhiteSpace(SearchText)
            ? Folders
            : Folders.Where(f => f.Name.ToLower().Contains(SearchText.ToLower()) ||
                                f.Icon.Contains(SearchText.ToLower()));

        foreach (var folder in itemsToShow)
        {
            FilteredFolders.Add(folder);
        }
    }

    /// <summary>
    /// 可用图标列表
    /// </summary>
    /// <remarks>
    /// 提供常用的文件夹图标选择
    /// 按使用频率和逻辑分组排序
    /// </remarks>
    public static readonly string[] AvailableIcons =
    {
        "📁", // 默认文件夹
        "🏗️", // 结构
        "🏢", // 建筑
        "🌡️", // 暖通
        "💧", // 给排水
        "⚡", // 电气
        "🌳", // 园林
        "🚗", // 交通
        "📋", // 出图
        "🗺️", // 底图
        "🔄", // 变更
        "📊", // 计算书
        "🖼️", // 图框
        "🔗"  // 绑定
    };

    #endregion

    #region 快捷方法

    /// <summary>
    /// 快捷通知方法
    /// </summary>
    private static ISnackbarService Notify => ZyloContainer.Resolve<ISnackbarService>();

    /// <summary>
    /// 显示增强的成功通知 - 支持详细信息和自适应显示
    /// </summary>
    /// <param name="message">主要消息</param>
    /// <param name="title">标题（可选）</param>
    /// <param name="details">详细信息（可选）</param>
    /// <param name="timeout">显示时长（毫秒）</param>
    private static void ShowEnhancedSuccess(string message, string? title = null, string? details = null, int timeout = 4000)
    {
        var fullMessage = details != null ? $"{message}\n{details}" : message;
        var displayTitle = title ?? "操作成功";

        // 根据消息长度调整显示时间
        var adjustedTimeout = CalculateOptimalTimeout(fullMessage, timeout);

        Notify.ShowSuccess(fullMessage, displayTitle, adjustedTimeout);
    }

    /// <summary>
    /// 显示增强的错误通知 - 支持详细错误信息
    /// </summary>
    /// <param name="message">主要错误消息</param>
    /// <param name="title">错误标题（可选）</param>
    /// <param name="details">详细错误信息（可选）</param>
    /// <param name="timeout">显示时长（毫秒）</param>
    private static void ShowEnhancedError(string message, string? title = null, string? details = null, int timeout = 6000)
    {
        var fullMessage = details != null ? $"{message}\n{details}" : message;
        var displayTitle = title ?? "操作失败";

        // 错误消息需要更长的显示时间
        var adjustedTimeout = CalculateOptimalTimeout(fullMessage, timeout, isError: true);

        Notify.ShowError(fullMessage, displayTitle, adjustedTimeout);
    }

    /// <summary>
    /// 显示增强的警告通知 - 支持详细警告信息
    /// </summary>
    /// <param name="message">主要警告消息</param>
    /// <param name="title">警告标题（可选）</param>
    /// <param name="details">详细警告信息（可选）</param>
    /// <param name="timeout">显示时长（毫秒）</param>
    private static void ShowEnhancedWarning(string message, string? title = null, string? details = null, int timeout = 5000)
    {
        var fullMessage = details != null ? $"{message}\n{details}" : message;
        var displayTitle = title ?? "警告";

        var adjustedTimeout = CalculateOptimalTimeout(fullMessage, timeout);

        Notify.ShowWarning(fullMessage, displayTitle, adjustedTimeout);
    }

    /// <summary>
    /// 根据消息长度计算最佳显示时长
    /// </summary>
    /// <param name="message">消息内容</param>
    /// <param name="baseTimeout">基础超时时间</param>
    /// <param name="isError">是否为错误消息（错误消息需要更长时间）</param>
    /// <returns>优化后的超时时间</returns>
    private static int CalculateOptimalTimeout(string message, int baseTimeout, bool isError = false)
    {
        if (string.IsNullOrEmpty(message)) return baseTimeout;

        // 基于字符数计算阅读时间（平均每分钟250字）
        var readingTimeMs = (message.Length / 250.0) * 60 * 1000;

        // 最小显示时间
        var minTimeout = isError ? 3000 : 2000;

        // 最大显示时间
        var maxTimeout = isError ? 12000 : 8000;

        // 计算建议时间：基础时间 + 阅读时间
        var suggestedTimeout = (int)(baseTimeout + readingTimeMs);

        // 限制在合理范围内
        return Math.Max(minTimeout, Math.Min(maxTimeout, suggestedTimeout));
    }

    #endregion



    #region 命令验证方法

    /// <summary>
    /// 检查是否可以保存
    /// </summary>
    private bool CanSave()
    {
        return EditingFolder != null && 
               !string.IsNullOrWhiteSpace(EditingFolder.Name) &&
               !string.IsNullOrWhiteSpace(EditingFolder.Icon);
    }

    /// <summary>
    /// 检查是否可以删除
    /// </summary>
    /// <remarks>
    /// 管理界面删除条件：
    /// 1. 必须选中了文件夹
    /// 2. 不能正在加载状态
    /// 3. 不能正在扫描状态
    /// 4. 文件夹列表中至少要保留一个文件夹
    ///
    /// 注意：管理界面允许删除所有文件夹，包括默认文件夹
    /// </remarks>
    private bool CanDelete()
    {
        return SelectedFolder != null &&
               !IsLoading &&
               !IsScanning &&
               Folders.Count > 1; // 至少保留一个文件夹
    }

    /// <summary>
    /// 检查是否可以扫描
    /// </summary>
    private bool CanScan()
    {
        return !string.IsNullOrWhiteSpace(ScanPath) && 
               Directory.Exists(ScanPath) && 
               !IsScanning;
    }

    #endregion

    #region 命令 - 自动生成

    /// <summary>
    /// 加载数据命令 - 从数据库加载所有文件夹
    /// </summary>
    [RelayCommand]
    private async Task LoadDataAsync()
    {
        try
        {
            IsLoading = true;
            StatusMessage = "正在加载...";

            await _service.InitializeDatabaseAsync();
            var folderList = await _service.GetAllFoldersAsync(includeDisabled: true);

            Folders.Clear();
            foreach (var folder in folderList)
                Folders.Add(folder);

            UpdateFilteredFolders();
            StatusMessage = $"已加载 {Folders.Count} 个文件夹";
        }
        catch (Exception ex)
        {
            StatusMessage = $"加载失败: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 新建文件夹命令 - 直接进入编辑状态
    /// </summary>
    [RelayCommand]
    private void NewFolder()
    {
        // 创建新文件夹对象
        EditingFolder = new DwgFolderModel
        {
            Name = "", // 空名称，用户需要填写
            Icon = AvailableIcons[0], // 默认使用第一个图标
            SortOrder = Folders.Count,
            IsDefault = false
        };

        // 进入编辑状态
        IsEditing = true;
        IsNewMode = true;
        StatusMessage = "请填写新文件夹信息";

        // 清除选中状态，专注于编辑
        SelectedFolder = null;
    }

    /// <summary>
    /// 编辑文件夹命令 - 编辑选中的文件夹
    /// </summary>
    [RelayCommand]
    private void EditFolder()
    {
        if (SelectedFolder == null) return;

        if (IsEditing && EditingFolder != null && EditingFolder.Id == SelectedFolder.Id)
            return;

        EditingFolder = SelectedFolder.Clone();
        IsEditing = true;
        IsNewMode = false;
        StatusMessage = $"正在编辑: {SelectedFolder.Name}";
    }

    /// <summary>
    /// 保存文件夹命令 - 保存编辑中的文件夹到数据库
    /// </summary>
    [RelayCommand(CanExecute = nameof(CanSave))]
    private async Task SaveAsync()
    {
        if (EditingFolder == null) return;

        try
        {
            IsLoading = true;
            StatusMessage = "正在保存...";

            // 检查名称是否重复
            var isDuplicate = IsNewMode
                ? Folders.Any(f => f.Name.Equals(EditingFolder.Name, StringComparison.OrdinalIgnoreCase))
                : Folders.Any(f => f.Id != EditingFolder.Id && f.Name.Equals(EditingFolder.Name, StringComparison.OrdinalIgnoreCase));

            if (isDuplicate)
            {
                ShowEnhancedWarning(
                    $"文件夹名称 '{EditingFolder.Name}' 已存在",
                    "名称重复",
                    "请使用其他名称，或检查现有文件夹列表");
                StatusMessage = "文件夹名称重复";
                return;
            }

            bool success = IsNewMode
                ? await _service.AddFolderAsync(EditingFolder)
                : await _service.UpdateFolderAsync(EditingFolder);

            var operationName = IsNewMode ? "添加" : "修改";
            var folderName = EditingFolder.Name;

            if (success)
            {
                StatusMessage = $"{operationName}成功";
                await LoadDataAsync();
                EditingFolder = null;
                IsEditing = false;
                IsNewMode = false;

                // 显示增强的成功提示
                ShowEnhancedSuccess(
                    $"文件夹 '{folderName}' 已成功{operationName.ToLower()}",
                    "操作完成",
                    $"文件夹信息已保存到数据库，当前共有 {Folders.Count} 个文件夹");
            }
            else
            {
                StatusMessage = $"{operationName}失败";
                ShowEnhancedError(
                    $"{operationName}文件夹 '{folderName}' 失败",
                    "操作失败",
                    "请检查输入信息是否正确，或稍后重试");
            }
        }
        catch (Exception ex)
        {
            var operationName = IsNewMode ? "添加" : "修改";
            var folderName = EditingFolder?.Name ?? "未知";
            StatusMessage = $"保存失败: {ex.Message}";

            Notify.ShowError($"{operationName}文件夹 '{folderName}' 时发生错误：{ex.Message}", "保存出错");
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 删除文件夹命令 - 删除选中的文件夹（管理界面可删除所有文件夹）
    /// </summary>
    [RelayCommand(CanExecute = nameof(CanDelete))]
    private async Task DeleteAsync()
    {
        if (SelectedFolder == null) return;

        // 确认删除操作 - 使用 WPF-UI MessageBox
        var folderName = SelectedFolder.Name;
        var isDefault = SelectedFolder.IsDefault;

        var messageBox = new Wpf.Ui.Controls.MessageBox
        {
            Title = "确认删除",
            Content = isDefault
                ? $"确定要删除默认文件夹 '{folderName}' 吗？\n\n⚠️ 删除默认文件夹可能影响系统功能，建议谨慎操作。"
                : $"确定要删除文件夹 '{folderName}' 吗？\n\n此操作无法撤销。",
            PrimaryButtonText = "删除",
            SecondaryButtonText = "取消",
            ShowTitle = true,
            PrimaryButtonAppearance = isDefault
                ? Wpf.Ui.Controls.ControlAppearance.Caution
                : Wpf.Ui.Controls.ControlAppearance.Danger,
            SecondaryButtonAppearance = Wpf.Ui.Controls.ControlAppearance.Secondary
        };

        var result = await messageBox.ShowDialogAsync();

        if (result != Wpf.Ui.Controls.MessageBoxResult.Primary)
        {
            StatusMessage = "已取消删除操作";
            return;
        }

        var folderToDelete = SelectedFolder;

        try
        {
            IsLoading = true;
            StatusMessage = $"正在删除 {folderName}...";

            // 如果正在编辑该文件夹，先取消编辑
            if (EditingFolder?.Id == folderToDelete.Id)
            {
                EditingFolder = null;
                IsEditing = false;
                IsNewMode = false;
            }

            // 清除选中状态
            SelectedFolder = null;

            var success = await _service.DeleteFolderAsync(folderToDelete.Id);

            if (success)
            {
                StatusMessage = $"已成功删除文件夹 '{folderName}'";
                await LoadDataAsync();

                // 显示成功提示
                Notify.ShowSuccess($"文件夹 '{folderName}' 已成功删除！");
            }
            else
            {
                StatusMessage = $"删除文件夹 '{folderName}' 失败";
                Notify.ShowError($"删除文件夹 '{folderName}' 失败，请稍后重试");

                // 恢复选中状态
                SelectedFolder = Folders.FirstOrDefault(f => f.Id == folderToDelete.Id);
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"删除失败: {ex.Message}";
            Notify.ShowError($"删除文件夹 '{folderName}' 时发生错误：{ex.Message}", "删除出错");

            // 恢复选中状态
            SelectedFolder = Folders.FirstOrDefault(f => f.Id == folderToDelete.Id);
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 取消编辑命令 - 退出编辑模式，丢弃未保存的更改
    /// </summary>
    [RelayCommand]
    private void Cancel()
    {
        EditingFolder = null;
        IsEditing = false;
        IsNewMode = false;
        StatusMessage = "已取消编辑";
    }

    /// <summary>
    /// 扫描路径命令 - 扫描指定路径并自动管理文件夹（特色功能）
    /// </summary>
    [RelayCommand(CanExecute = nameof(CanScan))]
    private async Task ScanPathAsync()
    {
        try
        {
            IsScanning = true;
            StatusMessage = $"正在扫描路径: {ScanPath}";

            var result = await _service.ScanAndManageFoldersAsync(ScanPath);

            if (result.IsSuccess)
            {
                StatusMessage = $"扫描完成: 创建 {result.CreatedFolders.Count} 个，发现 {result.DiscoveredFolders.Count} 个文件夹";
                await LoadDataAsync(); // 重新加载数据

                // 显示详细的扫描结果通知
                var createdCount = result.CreatedFolders.Count;
                var discoveredCount = result.DiscoveredFolders.Count;
                var totalCount = result.TotalManagedFolders;

                var summaryMessage = $"扫描路径: {ScanPath}";
                var detailsMessage = $"✅ 创建了 {createdCount} 个新文件夹\n" +
                                   $"🔍 发现了 {discoveredCount} 个现有文件夹\n" +
                                   $"📁 总共管理 {totalCount} 个文件夹";

                ShowEnhancedSuccess(summaryMessage, "文件夹扫描完成", detailsMessage, 6000);
            }
            else
            {
                StatusMessage = $"扫描失败: {result.ErrorMessage}";
                ShowEnhancedError(
                    "文件夹扫描失败",
                    "扫描错误",
                    $"路径: {ScanPath}\n错误: {result.ErrorMessage}",
                    8000);
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"扫描失败: {ex.Message}";
        }
        finally
        {
            IsScanning = false;
        }
    }

    /// <summary>
    /// 选择路径命令 - 打开文件夹选择对话框
    /// </summary>
    [RelayCommand]
    private void SelectPath()
    {
        var dialog = new Microsoft.Win32.OpenFolderDialog
        {
            Title = "选择要扫描的文件夹",
            InitialDirectory = string.IsNullOrEmpty(ScanPath) ? Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments) : ScanPath
        };

        if (dialog.ShowDialog() == true)
        {
            ScanPath = dialog.FolderName;
            StatusMessage = $"已选择路径: {ScanPath}";
        }
    }



    /// <summary>
    /// 选择图标命令 - 循环切换图标
    /// </summary>
    /// <remarks>
    /// 纯 MVVM 实现：通过命令而非代码后置事件处理图标选择
    /// 简单的循环切换方式，用户友好且无需额外对话框
    /// </remarks>
    [RelayCommand]
    private void SelectIcon()
    {
        if (EditingFolder == null) return;

        var currentIcon = EditingFolder.Icon;

        // 使用静态图标列表进行循环切换
        var currentIndex = Array.IndexOf(AvailableIcons, currentIcon);

        // 如果当前图标不在列表中，从第一个开始
        if (currentIndex == -1)
        {
            currentIndex = 0;
        }
        else
        {
            // 切换到下一个图标，循环到开头
            currentIndex = (currentIndex + 1) % AvailableIcons.Length;
        }

        EditingFolder.Icon = AvailableIcons[currentIndex];
        StatusMessage = $"图标已更改为: {EditingFolder.Icon}";
    }

    /// <summary>
    /// 选择文件夹命令 - 用于专业文件夹Tab点击
    /// </summary>
    [RelayCommand]
    private void SelectFolder(DwgFolderModel folder)
    {
        if (folder == null) return;

        SelectedFolder = folder;
        _logger.Info($"🔄 通过Tab选择文件夹: {folder.Name}");
    }

    #endregion

    #region 属性变化处理

    /// <summary>
    /// 当 EditingFolder 属性变化时自动调用
    /// </summary>
    /// <remarks>
    /// 订阅编辑对象的属性变化事件，以便实时验证保存命令
    /// </remarks>
    partial void OnEditingFolderChanged(DwgFolderModel? value)
    {
        // 取消旧对象的事件订阅（如果有的话）
        // 注意：这里无法获取 oldValue，需要手动管理

        // 订阅新对象的事件
        if (value != null)
        {
            value.PropertyChanged += OnEditingFolderPropertyChanged;
        }
    }

    /// <summary>
    /// 当 SearchText 属性变化时自动调用
    /// </summary>
    partial void OnSearchTextChanged(string value)
    {
        UpdateFilteredFolders();
        OnPropertyChanged(nameof(DisplayFolders)); // 通知DisplayFolders属性变化
    }

    /// <summary>
    /// 编辑对象属性变化事件处理
    /// </summary>
    /// <remarks>
    /// 由于使用了 [NotifyCanExecuteChangedFor]，SaveCommand 会自动更新状态
    /// 这里只需要处理编辑对象内部属性变化的特殊逻辑
    /// </remarks>
    private void OnEditingFolderPropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        // 编辑对象的属性变化时，SaveCommand 会通过 [NotifyCanExecuteChangedFor] 自动更新
        // 这里可以添加其他需要响应的逻辑
    }

    #endregion

    #region 服务事件处理

    /// <summary>
    /// 文件夹变更事件处理
    /// </summary>
    private void OnFolderChanged(object? sender, DwgFolderChangedEventArgs e)
    {
        // 可以在这里处理文件夹变更通知
        // 例如：更新状态消息、刷新列表等
    }

    /// <summary>
    /// 扫描完成事件处理 - 显示增强的通知信息
    /// </summary>
    private void OnScanCompleted(object? sender, DwgFolderScanCompletedEventArgs e)
    {
        var result = e.ScanResult;

        if (result.IsSuccess)
        {
            StatusMessage = $"扫描完成: 总共管理 {result.TotalManagedFolders} 个文件夹";

            // 如果是后台扫描或其他触发的扫描，也显示通知
            if (sender != this) // 不是当前ViewModel触发的扫描
            {
                var message = "后台文件夹扫描已完成";
                var details = $"📁 总共管理 {result.TotalManagedFolders} 个文件夹\n" +
                             $"🔄 数据已自动更新";

                ShowEnhancedSuccess(message, "系统通知", details, 4000);
            }
        }
        else
        {
            StatusMessage = $"扫描失败: {result.ErrorMessage}";

            // 显示错误通知
            ShowEnhancedError(
                "后台文件夹扫描失败",
                "系统错误",
                $"错误详情: {result.ErrorMessage}",
                6000);
        }
    }

    #endregion

    #region 拖拽排序支持

    /// <summary>
    /// 拖拽进入处理
    /// </summary>
    public void DragOver(IDropInfo dropInfo)
    {
        if (dropInfo.Data is DwgFolderModel && dropInfo.TargetItem is DwgFolderModel)
        {
            dropInfo.DropTargetAdorner = DropTargetAdorners.Insert;
            dropInfo.Effects = System.Windows.DragDropEffects.Move;
        }
    }

    /// <summary>
    /// 拖拽放置处理 - 参考 DwgFileTypeEditorViewModel 的实现
    /// </summary>
    public void Drop(IDropInfo dropInfo)
    {
        try
        {
            // 1. 验证拖拽数据
            if (dropInfo.Data is not DwgFolderModel sourceFolder)
            {
                StatusMessage = "❌ 拖拽数据无效";
                return;
            }

            // 2. 检查是否在搜索状态
            if (!string.IsNullOrWhiteSpace(SearchText))
            {
                StatusMessage = "❌ 搜索状态下不支持拖拽排序，请清空搜索";
                return;
            }

            // 3. 获取当前位置和目标位置
            var currentIndex = Folders.IndexOf(sourceFolder);
            var targetIndex = dropInfo.InsertIndex;

            // 4. 验证索引有效性
            if (currentIndex < 0 || targetIndex < 0 || targetIndex > Folders.Count)
            {
                StatusMessage = "❌ 无效的拖拽位置";
                return;
            }

            // 5. 如果位置相同，不需要移动
            if (currentIndex == targetIndex || (currentIndex == targetIndex - 1))
            {
                return;
            }

            _logger.Info($"🔄 拖拽排序: {sourceFolder.Name} 从位置 {currentIndex} 移动到 {targetIndex}");

            // 6. 执行移动操作
            var actualTargetIndex = targetIndex > currentIndex ? targetIndex - 1 : targetIndex;
            Folders.Move(currentIndex, actualTargetIndex);

            _logger.Info($"📝 UI移动完成: {sourceFolder.Name} 移动到位置 {actualTargetIndex}");

            // 7. 更新数据库中的排序号
            UpdateSortOrderInDatabase();

            StatusMessage = $"✅ 已重新排序: {sourceFolder.Name}";
        }
        catch (Exception ex)
        {
            StatusMessage = $"❌ 排序失败: {ex.Message}";
            _logger.Error($"❌ 拖拽排序异常: {ex.Message}");
        }
    }



    /// <summary>
    /// 更新数据库中的排序号 - 参考 DwgFileTypeEditorViewModel 的实现
    /// </summary>
    private async void UpdateSortOrderInDatabase()
    {
        try
        {
            _logger.Info("💾 开始更新文件夹排序到数据库...");

            // 根据当前列表顺序更新排序号
            for (int i = 0; i < Folders.Count; i++)
            {
                var folder = Folders[i];
                var oldSortOrder = folder.SortOrder;
                folder.SortOrder = i; // 排序号从0开始

                _logger.Info($"📝 更新文件夹排序: {folder.Name} SortOrder: {oldSortOrder} -> {i}");

                // 更新到数据库
                await _service.UpdateFolderAsync(folder);
            }

            StatusMessage = "✅ 排序已保存到数据库";
            _logger.Info("✅ 文件夹排序更新完成");
        }
        catch (Exception ex)
        {
            StatusMessage = $"❌ 保存排序失败: {ex.Message}";
            _logger.Error($"❌ 更新文件夹排序失败: {ex.Message}");
        }
    }

    #endregion

    #region 资源清理

    /// <summary>
    /// 释放资源
    /// </summary>
    /// <remarks>
    /// 取消订阅服务事件，避免内存泄漏
    /// 参考 CommunityToolkit.Mvvm 的最佳实践
    /// </remarks>
    public void Dispose()
    {
        // 取消订阅服务事件
        _service.FolderChanged -= OnFolderChanged;
        _service.ScanCompleted -= OnScanCompleted;

        // 取消编辑对象的事件订阅
        if (EditingFolder != null)
        {
            EditingFolder.PropertyChanged -= OnEditingFolderPropertyChanged;
        }
    }

    #endregion



   
}
