using System.Text.Json.Serialization;

namespace WPFTest.Models.SQLite;

/// <summary>
/// SQLite 管理器设置模型 - 支持持久化配置
/// </summary>
/// <remarks>
/// 参考 ThemeConfiguration 的设计模式
/// 支持 JSON 序列化和默认值设置
/// </remarks>
public class SqliteManagerSettings
{
    /// <summary>
    /// 默认 SQLite 数据库文件夹路径
    /// </summary>
    [JsonPropertyName("defaultSqliteFolder")]
    public string DefaultSqliteFolder { get; set; } = string.Empty;

    /// <summary>
    /// 支持的文件扩展名（分号分隔）
    /// </summary>
    [JsonPropertyName("supportedExtensions")]
    public string SupportedExtensions { get; set; } = ".db;.sqlite;.sqlite3";

    /// <summary>
    /// 是否自动扫描子文件夹
    /// </summary>
    [JsonPropertyName("autoScanSubfolders")]
    public bool AutoScanSubfolders { get; set; } = true;

    /// <summary>
    /// 连接超时时间（秒）
    /// </summary>
    [JsonPropertyName("connectionTimeoutSeconds")]
    public int ConnectionTimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// 是否自动测试连接
    /// </summary>
    [JsonPropertyName("autoTestConnection")]
    public bool AutoTestConnection { get; set; } = true;

    /// <summary>
    /// 最大并发连接测试数
    /// </summary>
    [JsonPropertyName("maxConcurrentTests")]
    public int MaxConcurrentTests { get; set; } = 5;

    /// <summary>
    /// 是否启用文件监控
    /// </summary>
    [JsonPropertyName("enableFileWatcher")]
    public bool EnableFileWatcher { get; set; } = true;

    /// <summary>
    /// 文件监控间隔（秒）
    /// </summary>
    [JsonPropertyName("fileWatcherIntervalSeconds")]
    public int FileWatcherIntervalSeconds { get; set; } = 60;

    /// <summary>
    /// 是否显示隐藏文件
    /// </summary>
    [JsonPropertyName("showHiddenFiles")]
    public bool ShowHiddenFiles { get; set; } = false;

    /// <summary>
    /// 最近使用的文件夹列表
    /// </summary>
    [JsonPropertyName("recentFolders")]
    public List<string> RecentFolders { get; set; } = new();

    /// <summary>
    /// 最大最近文件夹数量
    /// </summary>
    [JsonPropertyName("maxRecentFolders")]
    public int MaxRecentFolders { get; set; } = 10;

    /// <summary>
    /// 窗口设置
    /// </summary>
    [JsonPropertyName("windowSettings")]
    public WindowSettings WindowSettings { get; set; } = new();

    /// <summary>
    /// 列表视图设置
    /// </summary>
    [JsonPropertyName("listViewSettings")]
    public ListViewSettings ListViewSettings { get; set; } = new();

    /// <summary>
    /// 配置文件版本
    /// </summary>
    [JsonPropertyName("version")]
    public string Version { get; set; } = "1.0";

    /// <summary>
    /// 最后更新时间
    /// </summary>
    [JsonPropertyName("lastUpdated")]
    public DateTime LastUpdated { get; set; } = DateTime.Now;

    /// <summary>
    /// 获取支持的扩展名数组
    /// </summary>
    [JsonIgnore]
    public string[] SupportedExtensionsArray
    {
        get
        {
            return SupportedExtensions
                .Split(';', StringSplitOptions.RemoveEmptyEntries)
                .Select(ext => ext.Trim())
                .Where(ext => !string.IsNullOrEmpty(ext))
                .ToArray();
        }
    }

    /// <summary>
    /// 添加最近使用的文件夹
    /// </summary>
    /// <param name="folderPath">文件夹路径</param>
    public void AddRecentFolder(string folderPath)
    {
        if (string.IsNullOrEmpty(folderPath) || !Directory.Exists(folderPath))
            return;

        // 移除已存在的相同路径
        RecentFolders.RemoveAll(f => string.Equals(f, folderPath, StringComparison.OrdinalIgnoreCase));

        // 添加到列表开头
        RecentFolders.Insert(0, folderPath);

        // 限制最大数量
        if (RecentFolders.Count > MaxRecentFolders)
        {
            RecentFolders.RemoveRange(MaxRecentFolders, RecentFolders.Count - MaxRecentFolders);
        }

        LastUpdated = DateTime.Now;
    }

    /// <summary>
    /// 移除最近使用的文件夹
    /// </summary>
    /// <param name="folderPath">文件夹路径</param>
    public void RemoveRecentFolder(string folderPath)
    {
        RecentFolders.RemoveAll(f => string.Equals(f, folderPath, StringComparison.OrdinalIgnoreCase));
        LastUpdated = DateTime.Now;
    }

    /// <summary>
    /// 清空最近使用的文件夹
    /// </summary>
    public void ClearRecentFolders()
    {
        RecentFolders.Clear();
        LastUpdated = DateTime.Now;
    }

    /// <summary>
    /// 创建默认设置
    /// </summary>
    /// <returns>默认设置实例</returns>
    public static SqliteManagerSettings CreateDefault()
    {
        var appDirectory = AppDomain.CurrentDomain.BaseDirectory;
        var defaultFolder = Path.Combine(appDirectory, "Databases");

        return new SqliteManagerSettings
        {
            DefaultSqliteFolder = defaultFolder,
            SupportedExtensions = ".db;.sqlite;.sqlite3",
            AutoScanSubfolders = true,
            ConnectionTimeoutSeconds = 30,
            AutoTestConnection = true,
            MaxConcurrentTests = 5,
            EnableFileWatcher = true,
            FileWatcherIntervalSeconds = 60,
            ShowHiddenFiles = false,
            MaxRecentFolders = 10,
            Version = "1.0",
            LastUpdated = DateTime.Now
        };
    }

    /// <summary>
    /// 验证设置的有效性
    /// </summary>
    /// <returns>验证结果</returns>
    public (bool IsValid, List<string> Errors) Validate()
    {
        var errors = new List<string>();

        // 验证默认文件夹
        if (string.IsNullOrEmpty(DefaultSqliteFolder))
        {
            errors.Add("默认 SQLite 文件夹路径不能为空");
        }

        // 验证支持的扩展名
        if (string.IsNullOrEmpty(SupportedExtensions))
        {
            errors.Add("支持的文件扩展名不能为空");
        }

        // 验证超时时间
        if (ConnectionTimeoutSeconds <= 0)
        {
            errors.Add("连接超时时间必须大于 0");
        }

        // 验证并发测试数
        if (MaxConcurrentTests <= 0)
        {
            errors.Add("最大并发连接测试数必须大于 0");
        }

        // 验证文件监控间隔
        if (FileWatcherIntervalSeconds <= 0)
        {
            errors.Add("文件监控间隔必须大于 0");
        }

        // 验证最大最近文件夹数量
        if (MaxRecentFolders <= 0)
        {
            errors.Add("最大最近文件夹数量必须大于 0");
        }

        return (errors.Count == 0, errors);
    }
}

/// <summary>
/// 窗口设置
/// </summary>
public class WindowSettings
{
    /// <summary>
    /// 窗口宽度
    /// </summary>
    [JsonPropertyName("width")]
    public double Width { get; set; } = 1200;

    /// <summary>
    /// 窗口高度
    /// </summary>
    [JsonPropertyName("height")]
    public double Height { get; set; } = 800;

    /// <summary>
    /// 窗口左边距
    /// </summary>
    [JsonPropertyName("left")]
    public double Left { get; set; } = 100;

    /// <summary>
    /// 窗口上边距
    /// </summary>
    [JsonPropertyName("top")]
    public double Top { get; set; } = 100;

    /// <summary>
    /// 窗口状态
    /// </summary>
    [JsonPropertyName("windowState")]
    public string WindowState { get; set; } = "Normal";

    /// <summary>
    /// 是否记住窗口位置
    /// </summary>
    [JsonPropertyName("rememberPosition")]
    public bool RememberPosition { get; set; } = true;
}

/// <summary>
/// 列表视图设置
/// </summary>
public class ListViewSettings
{
    /// <summary>
    /// 排序列名
    /// </summary>
    [JsonPropertyName("sortColumn")]
    public string SortColumn { get; set; } = "DisplayName";

    /// <summary>
    /// 排序方向
    /// </summary>
    [JsonPropertyName("sortDirection")]
    public string SortDirection { get; set; } = "Ascending";

    /// <summary>
    /// 列宽度设置
    /// </summary>
    [JsonPropertyName("columnWidths")]
    public Dictionary<string, double> ColumnWidths { get; set; } = new();

    /// <summary>
    /// 列显示设置
    /// </summary>
    [JsonPropertyName("columnVisibility")]
    public Dictionary<string, bool> ColumnVisibility { get; set; } = new();

    /// <summary>
    /// 每页显示数量
    /// </summary>
    [JsonPropertyName("itemsPerPage")]
    public int ItemsPerPage { get; set; } = 50;

    /// <summary>
    /// 是否启用分页
    /// </summary>
    [JsonPropertyName("enablePaging")]
    public bool EnablePaging { get; set; } = false;
}
