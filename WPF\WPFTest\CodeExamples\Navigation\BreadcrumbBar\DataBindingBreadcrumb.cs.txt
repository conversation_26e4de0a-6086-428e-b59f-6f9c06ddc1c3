using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Diagnostics;
using System.Windows;

namespace WPFTest.ViewModels.Navigation
{
    /// <summary>
    /// 数据绑定面包屑导航示例 ViewModel
    /// </summary>
    public partial class DataBindingBreadcrumbViewModel : ObservableObject
    {
        /// <summary>
        /// 面包屑项集合
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<BreadcrumbItemModel> breadcrumbItems = new();

        /// <summary>
        /// 当前页面标题
        /// </summary>
        [ObservableProperty]
        private string currentPageTitle = "数据绑定面包屑";

        /// <summary>
        /// 完整导航路径
        /// </summary>
        [ObservableProperty]
        private string fullNavigationPath = string.Empty;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        [ObservableProperty]
        private DateTime lastUpdated = DateTime.Now;

        /// <summary>
        /// 导航历史记录
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<string> navigationHistory = new();

        /// <summary>
        /// 构造函数
        /// </summary>
        public DataBindingBreadcrumbViewModel()
        {
            InitializeBreadcrumbItems();
            Debug.WriteLine("数据绑定面包屑 ViewModel 初始化");
        }

        /// <summary>
        /// 导航到指定项命令
        /// </summary>
        [RelayCommand]
        private void NavigateToItem(BreadcrumbItemModel? item)
        {
            if (item == null) return;

            try
            {
                // 找到目标项的索引
                var targetIndex = BreadcrumbItems.IndexOf(item);
                if (targetIndex >= 0)
                {
                    // 移除目标项之后的所有项
                    for (int i = BreadcrumbItems.Count - 1; i > targetIndex; i--)
                    {
                        BreadcrumbItems.RemoveAt(i);
                    }

                    // 更新当前页面
                    CurrentPageTitle = item.Title;
                    UpdateActiveStates();
                    UpdateFullPath();
                    AddToHistory($"导航到: {item.Title}");

                    Debug.WriteLine($"导航到: {item.Title}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"导航失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 添加子页面命令
        /// </summary>
        [RelayCommand]
        private void AddChildPage()
        {
            try
            {
                var childNumber = BreadcrumbItems.Count + 1;
                var newItem = new BreadcrumbItemModel
                {
                    Title = $"子页面 {childNumber}",
                    Icon = "SubdirectoryArrowRight",
                    Path = $"/page{childNumber}",
                    Description = $"动态添加的第 {childNumber} 个页面",
                    IsActive = false,
                    IsNavigable = true,
                    IsLast = true,
                    CanRemove = true
                };

                // 将之前的最后一项设为非最后项
                if (BreadcrumbItems.Any())
                {
                    BreadcrumbItems.Last().IsLast = false;
                }

                BreadcrumbItems.Add(newItem);
                CurrentPageTitle = newItem.Title;
                UpdateActiveStates();
                UpdateFullPath();
                AddToHistory($"添加子页面: {newItem.Title}");

                Debug.WriteLine($"添加子页面: {newItem.Title}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"添加子页面失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 返回上级命令
        /// </summary>
        [RelayCommand]
        private void NavigateUp()
        {
            try
            {
                if (BreadcrumbItems.Count > 1)
                {
                    var removedItem = BreadcrumbItems.Last();
                    BreadcrumbItems.RemoveAt(BreadcrumbItems.Count - 1);

                    // 更新最后一项状态
                    if (BreadcrumbItems.Any())
                    {
                        BreadcrumbItems.Last().IsLast = true;
                        CurrentPageTitle = BreadcrumbItems.Last().Title;
                    }

                    UpdateActiveStates();
                    UpdateFullPath();
                    AddToHistory($"返回上级，移除: {removedItem.Title}");

                    Debug.WriteLine($"返回上级，移除: {removedItem.Title}");
                }
                else
                {
                    Debug.WriteLine("已经是根页面，无法返回上级");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"返回上级失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 返回根页面命令
        /// </summary>
        [RelayCommand]
        private void NavigateToRoot()
        {
            try
            {
                if (BreadcrumbItems.Any())
                {
                    var rootItem = BreadcrumbItems.First();
                    BreadcrumbItems.Clear();
                    BreadcrumbItems.Add(rootItem);

                    rootItem.IsLast = true;
                    CurrentPageTitle = rootItem.Title;
                    UpdateActiveStates();
                    UpdateFullPath();
                    AddToHistory("返回根页面");

                    Debug.WriteLine("返回根页面");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"返回根页面失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 刷新路径命令
        /// </summary>
        [RelayCommand]
        private void RefreshPath()
        {
            try
            {
                UpdateFullPath();
                LastUpdated = DateTime.Now;
                AddToHistory("刷新路径");

                Debug.WriteLine("路径已刷新");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"刷新路径失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 复制路径命令
        /// </summary>
        [RelayCommand]
        private void CopyPath()
        {
            try
            {
                Clipboard.SetText(FullNavigationPath);
                AddToHistory($"复制路径: {FullNavigationPath}");

                Debug.WriteLine($"路径已复制: {FullNavigationPath}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"复制路径失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清空历史命令
        /// </summary>
        [RelayCommand]
        private void ClearHistory()
        {
            try
            {
                NavigationHistory.Clear();
                Debug.WriteLine("导航历史已清空");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"清空历史失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 移除指定项命令
        /// </summary>
        [RelayCommand]
        private void RemoveItem(BreadcrumbItemModel? item)
        {
            if (item == null || !item.CanRemove) return;

            try
            {
                var index = BreadcrumbItems.IndexOf(item);
                if (index >= 0)
                {
                    // 移除指定项及其之后的所有项
                    for (int i = BreadcrumbItems.Count - 1; i >= index; i--)
                    {
                        BreadcrumbItems.RemoveAt(i);
                    }

                    // 更新状态
                    if (BreadcrumbItems.Any())
                    {
                        BreadcrumbItems.Last().IsLast = true;
                        CurrentPageTitle = BreadcrumbItems.Last().Title;
                    }

                    UpdateActiveStates();
                    UpdateFullPath();
                    AddToHistory($"移除项: {item.Title}");

                    Debug.WriteLine($"移除项: {item.Title}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"移除项失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化面包屑项
        /// </summary>
        private void InitializeBreadcrumbItems()
        {
            BreadcrumbItems.Clear();

            var items = new[]
            {
                new BreadcrumbItemModel
                {
                    Title = "首页",
                    Icon = "Home",
                    Path = "/",
                    Description = "应用程序首页",
                    IsActive = false,
                    IsNavigable = true,
                    IsLast = false,
                    CanRemove = false
                },
                new BreadcrumbItemModel
                {
                    Title = "控件示例",
                    Icon = "Apps",
                    Path = "/controls",
                    Description = "各种控件的使用示例",
                    IsActive = false,
                    IsNavigable = true,
                    IsLast = false,
                    CanRemove = true
                },
                new BreadcrumbItemModel
                {
                    Title = "导航控件",
                    Icon = "Navigation",
                    Path = "/controls/navigation",
                    Description = "导航相关的控件示例",
                    IsActive = false,
                    IsNavigable = true,
                    IsLast = false,
                    CanRemove = true
                },
                new BreadcrumbItemModel
                {
                    Title = "数据绑定面包屑",
                    Icon = "DataBinding",
                    Path = "/controls/navigation/databinding-breadcrumb",
                    Description = "使用数据绑定的面包屑导航示例",
                    IsActive = true,
                    IsNavigable = false,
                    IsLast = true,
                    CanRemove = true
                }
            };

            foreach (var item in items)
            {
                BreadcrumbItems.Add(item);
            }

            UpdateFullPath();
        }

        /// <summary>
        /// 更新活动状态
        /// </summary>
        private void UpdateActiveStates()
        {
            for (int i = 0; i < BreadcrumbItems.Count; i++)
            {
                var item = BreadcrumbItems[i];
                item.IsActive = i == BreadcrumbItems.Count - 1;
                item.IsNavigable = !item.IsActive;
            }

            LastUpdated = DateTime.Now;
        }

        /// <summary>
        /// 更新完整路径
        /// </summary>
        private void UpdateFullPath()
        {
            FullNavigationPath = string.Join(" > ", BreadcrumbItems.Select(item => item.Title));
            LastUpdated = DateTime.Now;
        }

        /// <summary>
        /// 添加到历史记录
        /// </summary>
        private void AddToHistory(string action)
        {
            var historyEntry = $"[{DateTime.Now:HH:mm:ss}] {action}";
            NavigationHistory.Insert(0, historyEntry);

            // 保持历史记录在合理数量内
            while (NavigationHistory.Count > 50)
            {
                NavigationHistory.RemoveAt(NavigationHistory.Count - 1);
            }
        }
    }

    /// <summary>
    /// 面包屑项模型
    /// </summary>
    public partial class BreadcrumbItemModel : ObservableObject
    {
        [ObservableProperty]
        private string title = string.Empty;

        [ObservableProperty]
        private string icon = string.Empty;

        [ObservableProperty]
        private string path = string.Empty;

        [ObservableProperty]
        private string description = string.Empty;

        [ObservableProperty]
        private bool isActive = false;

        [ObservableProperty]
        private bool isNavigable = true;

        [ObservableProperty]
        private bool isLast = false;

        [ObservableProperty]
        private bool canRemove = true;

        /// <summary>
        /// 显示文本（包含图标和标题）
        /// </summary>
        public string DisplayText => $"{GetIconChar(Icon)} {Title}";

        /// <summary>
        /// 根据图标名称获取字符
        /// </summary>
        private string GetIconChar(string iconName)
        {
            return iconName switch
            {
                "Home" => "🏠",
                "Apps" => "📦",
                "Navigation" => "🧭",
                "DataBinding" => "🔗",
                "SubdirectoryArrowRight" => "📁",
                _ => "📄"
            };
        }
    }
}
