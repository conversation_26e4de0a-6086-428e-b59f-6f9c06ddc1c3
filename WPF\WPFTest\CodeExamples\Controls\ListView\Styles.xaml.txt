<!-- ListView 样式示例 -->
<!-- 展示如何使用 Zylo.WPF 中的各种 ListView 样式 -->

<StackPanel>
    <!-- 样式说明 -->
    <TextBlock Text="Zylo.WPF ListView 样式库"
               FontSize="18"
               FontWeight="Bold"
               Margin="0,0,0,16"/>

    <!-- 基础样式系列 -->
    <TextBlock Text="基础样式系列" FontSize="16" FontWeight="SemiBold" Margin="0,0,0,12"/>

    <!-- 标准样式 -->
    <TextBlock Text="1. 标准样式 (ListViewStyle)" FontWeight="Medium" Margin="0,0,0,8"/>
    <ListView ItemsSource="{Binding SimpleItems}"
              Style="{StaticResource ListViewStyle}"
              Height="100"
              Margin="0,0,0,16">
        <!--
        特点：
        - 标准的外观和感觉
        - 适中的项目高度
        - 默认的选择效果
        -->
    </ListView>

    <!-- 小型样式 -->
    <TextBlock Text="2. 小型样式 (SmallListViewStyle)" FontWeight="Medium" Margin="0,0,0,8"/>
    <ListView ItemsSource="{Binding SimpleItems}"
              Style="{StaticResource SmallListViewStyle}"
              Height="80"
              Margin="0,0,0,16">
        <!--
        特点：
        - 紧凑的项目高度
        - 适合显示大量数据
        - 节省空间
        -->
    </ListView>

    <!-- 现代化样式系列 -->
    <TextBlock Text="现代化样式系列" FontSize="16" FontWeight="SemiBold" Margin="0,16,0,12"/>

    <!-- 现代样式 -->
    <TextBlock Text="3. 现代样式 (ModernListViewStyle)" FontWeight="Medium" Margin="0,0,0,8"/>
    <ListView ItemsSource="{Binding SimpleItems}"
              Style="{StaticResource ModernListViewStyle}"
              Height="120"
              Margin="0,0,0,16">
        <!--
        特点：
        - 现代化的卡片式外观
        - 圆角边框和阴影效果
        - 优雅的悬停和选择动画
        -->
    </ListView>

    <!-- 密集样式系列 -->
    <TextBlock Text="密集样式系列" FontSize="16" FontWeight="SemiBold" Margin="0,16,0,12"/>

    <!-- 密集样式对比 -->
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- 密集样式（无边框） -->
        <StackPanel Grid.Column="0" Margin="0,0,8,0">
            <TextBlock Text="4. 密集样式 (DenseListViewStyle)" FontWeight="Medium" Margin="0,0,0,8"/>
            <ListView ItemsSource="{Binding SimpleItems}"
                      Style="{StaticResource DenseListViewStyle}"
                      Height="80"
                      Margin="0,0,0,8"/>
            <TextBlock Text="特点：紧密排列，无边框，最小间距" FontSize="10"
                       Foreground="Gray" TextWrapping="Wrap"/>
        </StackPanel>

        <!-- 密集边框样式 -->
        <StackPanel Grid.Column="1" Margin="4,0">
            <TextBlock Text="5. 密集边框样式 (DenseBorderedListViewStyle)" FontWeight="Medium" Margin="0,0,0,8"/>
            <ListView ItemsSource="{Binding SimpleItems}"
                      Style="{StaticResource DenseBorderedListViewStyle}"
                      Height="80"
                      Margin="0,0,0,8"/>
            <TextBlock Text="特点：紧密排列，带分隔线" FontSize="10"
                       Foreground="Gray" TextWrapping="Wrap"/>
        </StackPanel>

        <!-- 密集完整边框样式 -->
        <StackPanel Grid.Column="2" Margin="8,0,0,0">
            <TextBlock Text="6. 密集完整边框样式 (DenseFullBorderedListViewStyle)" FontWeight="Medium" Margin="0,0,0,8"/>
            <ListView ItemsSource="{Binding SimpleItems}"
                      Style="{StaticResource DenseFullBorderedListViewStyle}"
                      Height="80"
                      Margin="0,0,0,8"/>
            <TextBlock Text="特点：紧密排列，完整边框包围" FontSize="10"
                       Foreground="Gray" TextWrapping="Wrap"/>
        </StackPanel>
    </Grid>

    <!-- 特殊样式系列 -->
    <TextBlock Text="特殊样式系列" FontSize="16" FontWeight="SemiBold" Margin="0,24,0,12"/>

    <!-- 瓷砖样式 -->
    <TextBlock Text="8. 瓷砖样式 (TileListViewStyle)" FontWeight="Medium" Margin="0,0,0,8"/>
    <ListView ItemsSource="{Binding SimpleItems}"
              Style="{StaticResource TileListViewStyle}"
              Height="120"
              Margin="0,0,0,16">
        <!--
        特点：
        - 瓷砖式布局
        - 适合图标和文字组合
        - 网格化排列
        -->
    </ListView>

    <!-- 危险样式 -->
    <TextBlock Text="9. 危险样式 (DangerListViewStyle)" FontWeight="Medium" Margin="0,0,0,8"/>
    <ListView ItemsSource="{Binding SimpleItems}"
              Style="{StaticResource DangerListViewStyle}"
              Height="100"
              Margin="0,0,0,16">
        <!--
        特点：
        - 红色主题
        - 用于危险操作或警告
        - 醒目的视觉提示
        -->
    </ListView>

    <!-- 样式使用指南 -->
    <TextBlock Text="样式使用指南" FontSize="16" FontWeight="SemiBold" Margin="0,24,0,12"/>

    <!-- 基本用法 -->
    <TextBlock Text="基本用法：" FontWeight="Medium" Margin="0,0,0,8"/>
    <Border Background="LightGray" Padding="12" CornerRadius="4" Margin="0,0,0,16">
        <TextBlock FontFamily="Consolas" FontSize="12">
            <Run Text="&lt;ListView ItemsSource=&quot;{Binding Items}&quot;"/><LineBreak/>
            <Run Text="          Style=&quot;{StaticResource ModernListViewStyle}&quot;/&gt;"/>
        </TextBlock>
    </Border>

    <!-- 自定义扩展 -->
    <TextBlock Text="自定义扩展：" FontWeight="Medium" Margin="0,0,0,8"/>
    <Border Background="LightGray" Padding="12" CornerRadius="4" Margin="0,0,0,16">
        <TextBlock FontFamily="Consolas" FontSize="12">
            <Run Text="&lt;ListView.Style&gt;"/><LineBreak/>
            <Run Text="    &lt;Style TargetType=&quot;ListView&quot; BasedOn=&quot;{StaticResource ListViewStyle}&quot;&gt;"/><LineBreak/>
            <Run Text="        &lt;Setter Property=&quot;Background&quot; Value=&quot;LightBlue&quot;/&gt;"/><LineBreak/>
            <Run Text="        &lt;Setter Property=&quot;BorderBrush&quot; Value=&quot;DarkBlue&quot;/&gt;"/><LineBreak/>
            <Run Text="    &lt;/Style&gt;"/><LineBreak/>
            <Run Text="&lt;/ListView.Style&gt;"/>
        </TextBlock>
    </Border>

    <!-- 选择建议 -->
    <TextBlock Text="样式选择建议：" FontWeight="Medium" Margin="0,0,0,8"/>
    <StackPanel Margin="0,0,0,16">
        <TextBlock Text="• 数据密集型应用：使用 DenseListViewStyle 或 SmallListViewStyle" Margin="0,2"/>
        <TextBlock Text="• 现代化界面：使用 ModernListViewStyle" Margin="0,2"/>
        <TextBlock Text="• 标准边框容器：使用 BorderedListViewStyle" Margin="0,2"/>
        <TextBlock Text="• 密集分隔线：使用 DenseBorderedListViewStyle" Margin="0,2"/>
        <TextBlock Text="• 密集完整边框：使用 DenseFullBorderedListViewStyle" Margin="0,2"/>
        <TextBlock Text="• 简洁界面：使用 ListViewStyle" Margin="0,2"/>
        <TextBlock Text="• 特殊用途：使用 TileListViewStyle 或 DangerListViewStyle" Margin="0,2"/>
    </StackPanel>
</StackPanel>

<!--
Zylo.WPF ListView 样式库完整列表：

基础样式系列：
- ListViewStyle: 标准样式，适合大多数场景
- SmallListViewStyle: 小型样式，紧凑布局

现代化样式系列：
- ModernListViewStyle: 现代化卡片式外观

边框样式系列：
- BorderedListViewStyle: 标准间距，完整边框包围

密集样式系列：
- DenseListViewStyle: 密集样式，无边框
- DenseBorderedListViewStyle: 密集样式，带分隔线
- DenseFullBorderedListViewStyle: 密集样式，完整边框

特殊样式系列：
- TileListViewStyle: 瓷砖式布局
- DangerListViewStyle: 危险操作样式

样式区别说明：
- BorderedListViewStyle vs DenseFullBorderedListViewStyle: 前者标准间距，后者密集间距
- DenseBorderedListViewStyle vs DenseFullBorderedListViewStyle: 前者只有分隔线，后者有完整边框

使用提示：
1. 所有样式都支持主题切换（浅色/深色）
2. 选中效果统一使用左边框 + 背景色
3. 悬停效果使用半透明强调色
4. 支持多选模式和键盘导航
5. 密集样式适合数据密集型应用
6. 现代样式适合现代化界面设计
-->
