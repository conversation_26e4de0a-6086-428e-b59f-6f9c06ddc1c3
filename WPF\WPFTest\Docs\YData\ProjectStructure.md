# 📁 Zylo.YData 项目结构说明

## 🏗️ 文件夹组织架构

本项目采用功能导向的文件夹结构，将相关功能的文件组织在一起，便于维护和扩展。

### 📂 文件夹说明

```text
Zylo.YData/
├── 📁 Core/                    # 🔥 核心接口和抽象定义
│   ├── IYDataContext.cs        # 数据访问核心接口
│   └── IYDataManager.cs        # 多数据库管理器接口
│
├── 📁 Implementations/         # 🚀 核心功能实现
│   ├── YData.cs               # 静态 API 入口点
│   └── YDataContext.cs        # 数据访问上下文实现
│
├── 📁 Configuration/           # ⚙️ 配置相关
│   └── YDataOptions.cs        # 配置选项和数据模型
│
├── 📁 DependencyInjection/     # 💉 依赖注入支持
│   └── ServiceCollectionExtensions.cs  # 服务注册扩展方法
│
├── 📁 MultiDatabase/           # 🗄️ 多数据库支持
│   ├── YDataManager.cs        # 多数据库管理器实现
│   └── YDataSwitcher.cs       # 数据库切换器
│
├── 📁 Extensions/              # 🔧 扩展方法
│   ├── YDataExtensions.cs     # 核心查询扩展
│   ├── YMultiTableExtensions.cs # 多表操作扩展
│   └── YValidationExtensions.cs # 数据验证扩展
│
├── 📁 Models/                  # 📊 数据模型
│   ├── YValidationModels.cs   # 验证相关模型
│   └── YValidationOptions.cs  # 验证配置选项
│
├── 📁 Examples/                # 📝 示例代码
│   ├── User.cs                # 示例实体类
│   └── UsageExamples.cs       # 使用示例
│
├── 📁 Tests/                   # 🧪 测试代码
│   └── BasicTests.cs          # 基础功能测试
│
├── 📁 Documentation/           # 📚 项目文档
│   ├── README.md              # 项目主要文档
│   ├── YDataValidation_README.md # 验证功能文档
│   ├── Zylo.YData基于FreeSql的现代化方案.md # 设计方案
│   └── ProjectStructure.md    # 本文件 - 项目结构说明
│
├── GlobalUsings.cs             # 全局 using 声明
└── Zylo.YData.csproj          # 项目文件
```

## 🎯 设计原则

### 1. **功能分离**
- 每个文件夹专注于特定的功能领域
- 接口与实现分离（Core vs Implementations）
- 配置与业务逻辑分离

### 2. **易于维护**
- 相关文件集中管理
- 清晰的命名约定
- 文档与代码同步维护

### 3. **扩展友好**
- 新功能可以轻松添加到对应文件夹
- 扩展方法统一管理
- 模块化设计便于功能增强

## 📋 文件分类详解

### 🔥 Core（核心层）
**职责**：定义系统的核心接口和抽象
- `IYDataContext.cs` - 数据访问的核心接口，定义所有基础操作
- `IYDataManager.cs` - 多数据库管理的接口定义

### 🚀 Implementations（实现层）
**职责**：提供核心功能的具体实现
- `YData.cs` - 静态 API 的实现，提供全局访问入口
- `YDataContext.cs` - 数据访问上下文的具体实现

### ⚙️ Configuration（配置层）
**职责**：管理所有配置相关的类和选项
- `YDataOptions.cs` - 数据库配置选项、分页结果模型等

### 💉 DependencyInjection（依赖注入层）
**职责**：提供依赖注入容器的集成支持
- `ServiceCollectionExtensions.cs` - 服务注册的扩展方法

### 🗄️ MultiDatabase（多数据库层）
**职责**：支持多数据库的管理和切换
- `YDataManager.cs` - 多数据库管理器的实现
- `YDataSwitcher.cs` - 数据库切换的工具类

### 🔧 Extensions（扩展层）
**职责**：提供各种扩展方法增强易用性
- `YDataExtensions.cs` - 核心查询扩展（分页、条件查询等）
- `YMultiTableExtensions.cs` - 多表操作扩展
- `YValidationExtensions.cs` - 数据验证扩展

### 📊 Models（模型层）
**职责**：定义数据传输对象和业务模型
- `YValidationModels.cs` - 验证结果等模型
- `YValidationOptions.cs` - 验证配置选项

## 🔄 命名空间策略

**重要说明**：本次整理只调整了文件的物理位置，**保持所有文件的命名空间不变**，确保：

1. ✅ **向后兼容** - 现有代码无需修改
2. ✅ **引用稳定** - using 语句保持不变  
3. ✅ **API 一致** - 公共接口访问方式不变

所有文件仍然使用统一的命名空间：`namespace Zylo.YData;`

## 🚀 后续扩展建议

### 新功能添加指南
1. **缓存功能** → 新建 `Cache/` 文件夹
2. **监控诊断** → 新建 `Monitoring/` 文件夹  
3. **安全功能** → 新建 `Security/` 文件夹
4. **性能优化** → 新建 `Performance/` 文件夹

### 文件命名约定
- 接口文件：`I{功能名}.cs`
- 实现文件：`{功能名}.cs`
- 扩展文件：`{功能名}Extensions.cs`
- 配置文件：`{功能名}Options.cs`
- 模型文件：`{功能名}Models.cs`

## 📈 版本历史

- **v1.0.0** - 初始项目结构
- **v1.1.0** - 文件夹重组，功能分类优化

---

> 💡 **提示**：这种文件夹结构使项目更加清晰和易于维护，同时为未来的功能扩展提供了良好的基础。
