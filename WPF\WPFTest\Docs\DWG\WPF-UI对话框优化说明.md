# WPF-UI对话框优化说明

## 🎯 概述
将DWG管理系统中的传统对话框升级为WPF-UI的现代化对话框，提供更美观、更一致的用户体验。

## 🎨 优化内容

### 1. 删除确认对话框
**优化前：**
```csharp
// 使用传统的MessageBox
var result = MessageBox.Show(
    $"确定要删除文件 '{fileName}' 吗？\n\n此操作不可撤销！",
    "确认删除",
    MessageBoxButton.YesNo,
    MessageBoxImage.Warning);
```

**优化后：**
```csharp
// 使用WPF-UI的MessageBox
var messageBox = new Wpf.Ui.Controls.MessageBox
{
    Title = "确认删除",
    Content = $"确定要删除文件 '{fileName}' 吗？\n\n⚠️ 此操作不可撤销！",
    PrimaryButtonText = "删除",
    SecondaryButtonText = "取消",
    WindowStartupLocation = WindowStartupLocation.CenterOwner
};

var result = await messageBox.ShowDialogAsync();
return result == MessageBoxResult.Primary;
```

### 2. 重命名对话框
**优化前：**
```csharp
// 使用VB.NET的InputBox
var result = Microsoft.VisualBasic.Interaction.InputBox(
    "请输入新的文件名:",
    "重命名文件",
    currentName);
```

**优化后：**
```csharp
// 使用自定义的WPF-UI对话框
var dialog = new RenameFileDialog(currentName);
var result = dialog.ShowDialog();
return result == true ? dialog.NewFileName : null;
```

### 3. 文件属性对话框
**优化前：**
```csharp
// 使用传统的MessageBox显示属性
MessageBox.Show(properties, $"文件属性 - {fileInfo.Name}", 
    MessageBoxButton.OK, MessageBoxImage.Information);
```

**优化后：**
```csharp
// 使用WPF-UI的MessageBox，带图标和格式化内容
var messageBox = new Wpf.Ui.Controls.MessageBox
{
    Title = $"文件属性 - {fileInfo.Name}",
    Content = $"📄 文件名: {fileInfo.Name}\n\n" +
             $"📏 大小: {sizeText}\n\n" +
             $"📅 创建时间: {fileInfo.CreationTime:yyyy-MM-dd HH:mm:ss}\n\n" +
             $"✏️ 修改时间: {fileInfo.LastWriteTime:yyyy-MM-dd HH:mm:ss}\n\n" +
             $"📁 路径: {fileInfo.FullName}",
    PrimaryButtonText = "确定"
};
```

## 🏗️ 自定义重命名对话框

### XAML设计特点
```xml
<ui:FluentWindow
    Title="重命名文件"
    Width="450"
    Height="200"
    WindowStartupLocation="CenterOwner"
    ResizeMode="NoResize">
    
    <!-- 标题区域 -->
    <StackPanel Orientation="Horizontal">
        <ui:SymbolIcon Symbol="Rename24" FontSize="24" />
        <TextBlock Text="重命名文件" FontSize="18" FontWeight="SemiBold" />
    </StackPanel>
    
    <!-- 输入框 -->
    <ui:TextBox
        x:Name="FileNameTextBox"
        PlaceholderText="输入新的文件名..."
        Icon="{ui:SymbolIcon Document24}"
        ClearButtonEnabled="True">
        <ui:TextBox.InputBindings>
            <KeyBinding Key="Enter" Command="{Binding ConfirmCommand}" />
            <KeyBinding Key="Escape" Command="{Binding CancelCommand}" />
        </ui:TextBox.InputBindings>
    </ui:TextBox>
    
    <!-- 按钮区域 -->
    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
        <ui:Button Content="取消" Appearance="Secondary" />
        <ui:Button Content="确定" Appearance="Primary" />
    </StackPanel>
</ui:FluentWindow>
```

### 代码后置功能
```csharp
public partial class RenameFileDialog : FluentWindow
{
    public string? NewFileName { get; private set; }
    
    public RenameFileDialog(string currentFileName)
    {
        InitializeComponent();
        
        // 设置初始值并选中文件名部分
        FileNameTextBox.Text = currentFileName;
        var nameWithoutExtension = Path.GetFileNameWithoutExtension(currentFileName);
        FileNameTextBox.SelectionStart = 0;
        FileNameTextBox.SelectionLength = nameWithoutExtension.Length;
        
        // 设置焦点
        Loaded += (s, e) => FileNameTextBox.Focus();
    }
    
    private void ConfirmRename()
    {
        // 验证输入
        // 检查非法字符
        // 确保有扩展名
        // 设置结果
    }
}
```

## ✨ 优化效果

### 🎨 视觉效果
- **现代化设计** - 符合WPF-UI设计语言
- **一致的样式** - 与应用程序整体风格统一
- **图标支持** - 丰富的视觉提示
- **流畅动画** - 自然的过渡效果

### 🚀 用户体验
- **键盘支持** - Enter确认，Escape取消
- **智能选择** - 自动选中文件名部分
- **输入验证** - 实时验证文件名合法性
- **清除按钮** - 快速清空输入内容

### 🔧 功能增强
- **文件名验证** - 检查非法字符和空值
- **扩展名处理** - 自动添加原始扩展名
- **错误提示** - 友好的错误消息显示
- **父窗口关联** - 正确的模态对话框行为

## 📋 技术实现

### 依赖项
```xml
<!-- 项目文件中的包引用 -->
<PackageReference Include="WPF-UI" Version="4.0.3" />
```

### Using语句
```csharp
using Wpf.Ui.Controls;
using WPFTest.Views.DWG;
```

### 异步调用
```csharp
// 在UI线程中调用对话框
return await Application.Current.Dispatcher.InvokeAsync(async () =>
{
    var dialog = new RenameFileDialog(currentName);
    if (Application.Current.MainWindow != null)
    {
        dialog.Owner = Application.Current.MainWindow;
    }
    var result = dialog.ShowDialog();
    return result == true ? dialog.NewFileName : null;
});
```

## 🎮 使用方法

### 删除确认
1. 右键点击文件选择"删除文件"
2. 弹出现代化的确认对话框
3. 点击"删除"或"取消"按钮

### 文件重命名
1. 右键点击文件选择"重命名"
2. 弹出专用的重命名对话框
3. 输入新文件名（自动选中原文件名）
4. 按Enter确认或Escape取消

### 查看属性
1. 右键点击文件选择"属性"
2. 弹出格式化的属性信息对话框
3. 显示文件大小、时间、路径等信息

## 🔍 对比优势

| 特性 | 传统对话框 | WPF-UI对话框 |
|------|------------|--------------|
| 视觉效果 | 系统默认样式 | 现代化设计 |
| 一致性 | 与应用风格不符 | 完全一致 |
| 功能性 | 基础功能 | 增强功能 |
| 用户体验 | 一般 | 优秀 |
| 可定制性 | 有限 | 高度可定制 |
| 键盘支持 | 基础 | 完整支持 |

## 📝 更新日志

- **2025-01-27**: 完成WPF-UI对话框优化
  - 升级删除确认对话框
  - 创建自定义重命名对话框
  - 优化文件属性显示
  - 添加键盘快捷键支持
  - 完善输入验证功能

通过这次优化，DWG管理系统的对话框体验得到了显著提升，更加符合现代应用程序的设计标准。
