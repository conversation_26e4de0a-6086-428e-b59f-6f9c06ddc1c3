using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Reflection;
using System.Text;

namespace WPFTest.ViewModels.List;

    /// <summary>
    /// ListView 页面的 ViewModel，演示 ListView 控件的各种功能
    /// </summary>
    public partial class ListPageViewModel : ObservableObject
    {
        private readonly YLoggerInstance _logger = YLogger.ForDetailed<ListPageViewModel>();

        #region 属性

        /// <summary>
        /// 交互次数
        /// </summary>
        [ObservableProperty]
        private int interactionCount = 0;

        /// <summary>
        /// 最后执行的操作
        /// </summary>
        [ObservableProperty]
        private string lastAction = "无操作";

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        private string statusMessage = "欢迎使用 ListView 示例库！";

        /// <summary>
        /// 简单数据项集合
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<string> simpleItems = new();

        /// <summary>
        /// 复杂数据项集合
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<ListViewDataItem> dataItems = new();

        /// <summary>
        /// 选择的简单项
        /// </summary>
        [ObservableProperty]
        private string? selectedSimpleItem;

        /// <summary>
        /// 选择的复杂数据项
        /// </summary>
        [ObservableProperty]
        private ListViewDataItem? selectedDataItem;

        /// <summary>
        /// 多选项集合
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<ListViewDataItem> selectedItems = new();

        /// <summary>
        /// 选中内容的文本显示
        /// </summary>
        [ObservableProperty]
        private string selectedItemsText = "未选择任何项目";

        /// <summary>
        /// 选中内容的详细信息
        /// </summary>
        [ObservableProperty]
        private string selectedItemsDetails = string.Empty;

        /// <summary>
        /// 当选择项变化时的处理
        /// </summary>
        partial void OnSelectedDataItemChanged(ListViewDataItem? value)
        {
            if (!IsMultiSelectEnabled && value != null)
            {
                // 单选模式：清除其他项的选择状态
                foreach (var item in DataItems)
                {
                    item.IsSelected = item == value;
                }

                // 更新选择集合
                SelectedItems.Clear();
                if (value.IsSelected)
                {
                    SelectedItems.Add(value);
                }
            }
        }

        /// <summary>
        /// 当多选模式变化时的处理
        /// </summary>
        partial void OnIsMultiSelectEnabledChanged(bool value)
        {
            try
            {
                if (!value)
                {
                    // 切换到单选模式：清除所有选择状态
                    ClearAllSelections();
                }

                InteractionCount++;
                LastAction = "切换多选模式";
                StatusMessage = $"🔄 多选模式: {(value ? "已启用" : "已禁用")}";
                _logger.Info($"🔄 多选模式切换: {value}");

                // 更新选中内容显示
                UpdateSelectedItemsDisplay();
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 处理多选模式变化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 当选中项集合变化时的处理
        /// </summary>
        partial void OnSelectedItemsChanged(ObservableCollection<ListViewDataItem> value)
        {
            UpdateSelectedItemsDisplay();
        }

        /// <summary>
        /// 当单选项变化时的处理
        /// </summary>
        partial void OnSelectedDataItemChanged(ListViewDataItem? oldValue, ListViewDataItem? newValue)
        {
            UpdateSelectedItemsDisplay();
        }

        /// <summary>
        /// 新项目名称
        /// </summary>
        [ObservableProperty]
        private string newItemName = "新项目";

        /// <summary>
        /// 是否启用多选
        /// </summary>
        [ObservableProperty]
        private bool isMultiSelectEnabled = false;

        #region 代码示例属性

        /// <summary>
        /// 基础功能 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 基础功能 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string BasicCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级功能 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 高级功能 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string AdvancedCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 样式 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string StylesXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 数据绑定 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string DataBindingXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 数据绑定 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string DataBindingCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 标准样式 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string StandardStylesXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 自定义样式 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string CustomStylesXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 自定义样式 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string CustomStylesCSharpExample { get; set; } = string.Empty;

        /// <summary>
        /// 交互功能 XAML 示例
        /// </summary>
        [ObservableProperty]
        public partial string InteractionXamlExample { get; set; } = string.Empty;

        /// <summary>
        /// 交互功能 C# 示例
        /// </summary>
        [ObservableProperty]
        public partial string InteractionCSharpExample { get; set; } = string.Empty;

        #endregion

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化 ListPageViewModel
        /// </summary>
        public ListPageViewModel()
        {
            try
            {
                _logger.Info("🚀 ListView 页面 ViewModel 开始初始化");

                StatusMessage = "ListView 示例库已加载！";
                InitializeData();
                InitializeCodeExamples();

                PropertyChanged += OnPropertyChanged;

                // 初始化选中内容显示
                UpdateSelectedItemsDisplay();

                _logger.Info("✅ ListView 页面 ViewModel 初始化完成");
                _logger.Info($"📊 初始状态 - 简单项数: {SimpleItems.Count}, 数据项数: {DataItems.Count}");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ ListView 页面 ViewModel 初始化失败: {ex.Message}");
                _logger.Error($"🔍 异常详情: {ex}");
                StatusMessage = "初始化失败，请检查日志";
            }
        }

        #endregion

        #region 命令

        /// <summary>
        /// 通用交互命令
        /// </summary>
        /// <param name="parameter">操作参数</param>
        [RelayCommand]
        private void HandleInteraction(string? parameter)
        {
            try
            {
                InteractionCount++;
                var action = parameter ?? "未知操作";
                LastAction = action;

                _logger.Info($"🎯 用户交互操作");
                _logger.Info($"📝 操作类型: {action}");
                _logger.Info($"🔢 当前交互次数: {InteractionCount}");

                // 根据不同的操作显示不同的状态消息
                StatusMessage = parameter switch
                {
                    "简单选择" => $"🎯 选择了简单项: {SelectedSimpleItem}",
                    "复杂选择" => $"🎯 选择了数据项: {SelectedDataItem?.Name}",
                    "多选模式" => $"🔄 切换多选模式: {(IsMultiSelectEnabled ? "启用" : "禁用")}",
                    _ => $"🎯 执行了操作: {parameter}"
                };
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 处理用户交互失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 添加简单项命令
        /// </summary>
        [RelayCommand]
        private void AddSimpleItem()
        {
            try
            {
                var itemName = $"简单项 {SimpleItems.Count + 1}";
                SimpleItems.Insert(0, itemName);
                InteractionCount++;
                LastAction = "添加简单项";
                StatusMessage = $"➕ 添加了简单项: {itemName}";
                _logger.Info($"➕ 添加简单项: {itemName}");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 添加简单项失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 添加数据项命令
        /// </summary>
        [RelayCommand]
        private void AddDataItem()
        {
            try
            {
                var newItem = new ListViewDataItem
                {
                    Id = DataItems.Count + 1,
                    Name = NewItemName,
                    Description = $"这是 {NewItemName} 的描述",
                    Icon = GetRandomIcon(),
                    Category = GetRandomCategory(),
                    IsEnabled = true,
                    CreatedDate = DateTime.Now
                };

                DataItems.Insert(0, newItem);
                InteractionCount++;
                LastAction = "添加数据项";
                StatusMessage = $"➕ 添加了数据项: {newItem.Name}";
                _logger.Info($"➕ 添加数据项: {newItem.Name}");

                // 重置输入框
                NewItemName = "新项目";
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 添加数据项失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 删除选中项命令
        /// </summary>
        [RelayCommand]
        private void DeleteSelectedItem()
        {
            try
            {
                if (SelectedDataItem != null)
                {
                    var itemName = SelectedDataItem.Name;
                    DataItems.Remove(SelectedDataItem);
                    SelectedDataItem = null;
                    InteractionCount++;
                    LastAction = "删除项目";
                    StatusMessage = $"🗑️ 删除了项目: {itemName}";
                    _logger.Info($"🗑️ 删除项目: {itemName}");
                }
                else
                {
                    StatusMessage = "⚠️ 请先选择要删除的项目";
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 删除项目失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 切换多选模式命令（备用，现在主要通过属性绑定处理）
        /// </summary>
        [RelayCommand]
        private void ToggleMultiSelect()
        {
            try
            {
                // 简单切换，具体逻辑在 OnIsMultiSelectEnabledChanged 中处理
                IsMultiSelectEnabled = !IsMultiSelectEnabled;
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 切换多选模式失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 全选命令
        /// </summary>
        [RelayCommand]
        private void SelectAll()
        {
            try
            {
                if (!IsMultiSelectEnabled) return;

                foreach (var item in DataItems)
                {
                    item.IsSelected = true;
                    if (!SelectedItems.Contains(item))
                    {
                        SelectedItems.Add(item);
                    }
                }

                InteractionCount++;
                LastAction = "全选";
                StatusMessage = $"✅ 已全选 {SelectedItems.Count} 个项目";
                _logger.Info($"✅ 全选了 {SelectedItems.Count} 个项目");

                // 更新选中内容显示
                UpdateSelectedItemsDisplay();
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 全选操作失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 取消全选命令
        /// </summary>
        [RelayCommand]
        private void UnselectAll()
        {
            try
            {
                ClearAllSelections();
                InteractionCount++;
                LastAction = "取消全选";
                StatusMessage = "❌ 已取消所有选择";
                _logger.Info("❌ 取消了所有选择");

                // 更新选中内容显示
                UpdateSelectedItemsDisplay();
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 取消全选操作失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 反选命令
        /// </summary>
        [RelayCommand]
        private void InvertSelection()
        {
            try
            {
                if (!IsMultiSelectEnabled) return;

                foreach (var item in DataItems)
                {
                    item.IsSelected = !item.IsSelected;

                    if (item.IsSelected && !SelectedItems.Contains(item))
                    {
                        SelectedItems.Add(item);
                    }
                    else if (!item.IsSelected && SelectedItems.Contains(item))
                    {
                        SelectedItems.Remove(item);
                    }
                }

                InteractionCount++;
                LastAction = "反选";
                StatusMessage = $"🔄 反选完成，当前选中 {SelectedItems.Count} 个项目";
                _logger.Info($"🔄 反选完成，当前选中 {SelectedItems.Count} 个项目");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 反选操作失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 批量删除选中项命令
        /// </summary>
        [RelayCommand]
        private void DeleteSelectedItems()
        {
            try
            {
                if (SelectedItems.Count == 0)
                {
                    StatusMessage = "⚠️ 请先选择要删除的项目";
                    return;
                }

                var itemsToDelete = Enumerable.ToList<ListViewDataItem>(SelectedItems);
                var deletedCount = itemsToDelete.Count;

                foreach (var item in itemsToDelete)
                {
                    DataItems.Remove(item);
                }

                SelectedItems.Clear();
                SelectedDataItem = null;

                InteractionCount++;
                LastAction = "批量删除";
                StatusMessage = $"🗑️ 已删除 {deletedCount} 个项目";
                _logger.Info($"🗑️ 批量删除了 {deletedCount} 个项目");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 批量删除失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取选中项数据命令
        /// </summary>
        [RelayCommand]
        private void GetSelectedData()
        {
            try
            {
                var selectedData = GetCurrentSelectionData();

                if (selectedData.Count == 0)
                {
                    StatusMessage = "⚠️ 没有选中任何项目，请先选择项目";
                    return;
                }

                // 将选中数据显示到剪贴板和详细信息中
                var detailText = GenerateSelectedDataReport(selectedData);

                // 复制到剪贴板
                try
                {
                    System.Windows.Clipboard.SetText(detailText);
                    StatusMessage = $"📊 已获取 {selectedData.Count} 个项目的数据并复制到剪贴板";
                }
                catch
                {
                    StatusMessage = $"📊 已获取 {selectedData.Count} 个项目的数据";
                }

                InteractionCount++;
                LastAction = "获取选中数据";
                _logger.Info($"📊 获取选中数据: {selectedData.Summary}");

                // 更新详细信息显示
                SelectedItemsDetails = detailText;

                // 在实际应用中，这里可以将数据传递给其他服务或保存
                ProcessSelectedData(selectedData);
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 获取选中数据失败: {ex.Message}");
                StatusMessage = $"❌ 获取选中数据失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 导出选中数据命令
        /// </summary>
        [RelayCommand]
        private void ExportSelectedData()
        {
            try
            {
                var selectedData = GetCurrentSelectionData();

                if (selectedData.Items.Count == 0)
                {
                    StatusMessage = "⚠️ 没有选中的数据可导出";
                    return;
                }

                // 模拟导出过程
                var exportResult = ExportDataToJson(selectedData);

                InteractionCount++;
                LastAction = "导出数据";
                StatusMessage = $"📤 已导出 {selectedData.Items.Count} 个项目的数据";
                _logger.Info($"📤 导出数据完成: {exportResult}");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 导出数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 项目选择状态变化命令
        /// </summary>
        [RelayCommand]
        private void ItemSelectionChanged(ListViewDataItem item)
        {
            try
            {
                if (item == null) return;

                if (IsMultiSelectEnabled)
                {
                    // 多选模式：切换选择状态
                    item.IsSelected = !item.IsSelected;

                    if (item.IsSelected && !SelectedItems.Contains(item))
                    {
                        SelectedItems.Add(item);
                    }
                    else if (!item.IsSelected && SelectedItems.Contains(item))
                    {
                        SelectedItems.Remove(item);
                    }
                }
                else
                {
                    // 单选模式：清除其他选择，选中当前项
                    foreach (var otherItem in DataItems)
                    {
                        otherItem.IsSelected = otherItem == item;
                    }

                    // 更新选择集合
                    SelectedItems.Clear();
                    SelectedItems.Add(item);
                    SelectedDataItem = item;
                }

                InteractionCount++;
                LastAction = "选择状态变化";
                StatusMessage = $"🔄 项目 '{item.Name}' 选择状态: {(item.IsSelected ? "选中" : "取消选中")}";
                _logger.Info($"🔄 项目选择状态变化: {item.Name} - {item.IsSelected}");

                // 更新选中内容显示
                UpdateSelectedItemsDisplay();
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 处理选择状态变化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 重置计数命令
        /// </summary>
        [RelayCommand]
        private void ResetCount()
        {
            InteractionCount = 0;
            StatusMessage = "计数已重置";
            _logger.Info("重置了交互计数");
        }

        /// <summary>
        /// 清除状态命令
        /// </summary>
        [RelayCommand]
        private void ClearStatus()
        {
            LastAction = "无操作";
            StatusMessage = "状态已清除";
            _logger.Info("清除了状态信息");
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            try
            {
                // 初始化简单项
                SimpleItems.Clear();
                for (int i = 1; i <= 5; i++)
                {
                    SimpleItems.Add($"简单项 {i}");
                }

                // 初始化复杂数据项
                DataItems.Clear();
                var sampleData = new[]
                {
                    new ListViewDataItem { Id = 1, Name = "文档管理", Description = "管理和组织您的文档", Icon = "Document24", Category = "办公" },
                    new ListViewDataItem { Id = 2, Name = "图片编辑", Description = "编辑和处理图片文件", Icon = "Image24", Category = "媒体" },
                    new ListViewDataItem { Id = 3, Name = "音乐播放", Description = "播放您喜爱的音乐", Icon = "MusicNote124", Category = "媒体" },
                    new ListViewDataItem { Id = 4, Name = "任务管理", Description = "跟踪和管理您的任务", Icon = "TaskList24", Category = "效率" },
                    new ListViewDataItem { Id = 5, Name = "日历安排", Description = "管理您的日程安排", Icon = "Calendar24", Category = "效率" },
                    new ListViewDataItem { Id = 6, Name = "邮件处理", Description = "发送和接收电子邮件", Icon = "Mail24", Category = "通讯" },
                    new ListViewDataItem { Id = 7, Name = "联系人", Description = "管理您的联系人信息", Icon = "People24", Category = "通讯" },
                    new ListViewDataItem { Id = 8, Name = "设置配置", Description = "配置应用程序设置", Icon = "Settings24", Category = "系统" }
                };

                foreach (var item in sampleData)
                {
                    DataItems.Add(item);
                }

                _logger.Info($"📊 数据初始化完成 - 简单项: {SimpleItems.Count}, 数据项: {DataItems.Count}");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 数据初始化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取随机图标
        /// </summary>
        private string GetRandomIcon()
        {
            var icons = new[] { "Document24", "Image24", "MusicNote124", "TaskList24", "Calendar24", "Mail24", "People24", "Settings24", "Folder24", "Star24" };
            return icons[Random.Shared.Next(icons.Length)];
        }

        /// <summary>
        /// 获取随机分类
        /// </summary>
        private string GetRandomCategory()
        {
            var categories = new[] { "办公", "媒体", "效率", "通讯", "系统", "工具" };
            return categories[Random.Shared.Next(categories.Length)];
        }

        /// <summary>
        /// 属性变化处理
        /// </summary>
        private void OnPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            try
            {
                switch (e.PropertyName)
                {
                    case nameof(List.ListPageViewModel.SelectedSimpleItem):
                        if (SelectedSimpleItem != null)
                        {
                            InteractionCount++;
                            LastAction = "选择简单项";
                            StatusMessage = $"🎯 选择了简单项: {SelectedSimpleItem}";
                            _logger.Info($"📝 简单项选择变更: {SelectedSimpleItem}");
                        }
                        break;
                    case nameof(List.ListPageViewModel.SelectedDataItem):
                        if (SelectedDataItem != null)
                        {
                            InteractionCount++;
                            LastAction = "选择数据项";
                            StatusMessage = $"🎯 选择了数据项: {SelectedDataItem.Name} ({SelectedDataItem.Category})";
                            _logger.Info($"📝 数据项选择变更: {SelectedDataItem.Name}");
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 处理属性变化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化代码示例
        /// </summary>
        private void InitializeCodeExamples()
        {
            try
            {
                // 获取代码示例文件的基础路径
                var baseDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
                var codeExamplesPath = Path.Combine(baseDirectory, "CodeExamples", "Controls", "ListView");

                _logger.Info($"基础目录: {baseDirectory}");
                _logger.Info($"代码示例路径: {codeExamplesPath}");
                _logger.Info($"目录是否存在: {Directory.Exists(codeExamplesPath)}");

                // 读取各种示例文件
                BasicXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.xaml.txt"));
                BasicCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.cs.txt"));
                AdvancedXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Advanced.xaml.txt"));
                AdvancedCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Advanced.cs.txt"));
                StylesXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Styles.xaml.txt"));
                DataBindingXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "DataBinding.xaml.txt"));
                DataBindingCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "DataBinding.cs.txt"));
                StandardStylesXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "StandardStyles.xaml.txt"));
                CustomStylesXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "CustomStyles.xaml.txt"));
                CustomStylesCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "CustomStyles.cs.txt"));
                InteractionXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Interaction.xaml.txt"));
                InteractionCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Interaction.cs.txt"));

                _logger.Info("ListView 代码示例加载完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"加载 ListView 代码示例失败: {ex.Message}");
                SetDefaultCodeExamples();
            }
        }

        /// <summary>
        /// 读取代码示例文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件内容</returns>
        private string ReadCodeExampleFile(string filePath)
        {
            _logger.Info($"尝试读取文件: {filePath}");

            if (File.Exists(filePath))
            {
                var content = File.ReadAllText(filePath);
                _logger.Info($"成功读取文件，内容长度: {content.Length}");
                return content;
            }
            else
            {
                _logger.Warning($"代码示例文件不存在: {filePath}");
                return $"// 代码示例文件不存在: {Path.GetFileName(filePath)}\n// 完整路径: {filePath}";
            }
        }

        /// <summary>
        /// 设置默认代码示例（当文件读取失败时使用）
        /// </summary>
        private void SetDefaultCodeExamples()
        {
            BasicXamlExample = "<!-- ListView 基础示例 -->\n<!-- 在这里添加基础用法示例 -->";
            BasicCSharpExample = "// ListView C# 基础示例\n// 在这里添加 C# 代码示例";
            AdvancedXamlExample = "<!-- ListView 高级示例 -->\n<!-- 在这里添加高级用法示例 -->";
            AdvancedCSharpExample = "// ListView C# 高级示例\n// 在这里添加高级 C# 代码示例";
            StylesXamlExample = "<!-- ListView 样式示例 -->\n<!-- 在这里添加样式使用示例 -->";
            DataBindingXamlExample = "<!-- ListView 数据绑定示例 -->\n<!-- 在这里添加数据绑定示例 -->";
            DataBindingCSharpExample = "// ListView 数据绑定 C# 示例\n// 在这里添加数据绑定 C# 代码示例";
            StandardStylesXamlExample = "<!-- ListView 标准样式示例 -->\n<!-- 在这里添加标准样式示例 -->";
            CustomStylesXamlExample = "<!-- ListView 自定义样式示例 -->\n<!-- 在这里添加自定义样式示例 -->";
            CustomStylesCSharpExample = "// ListView 自定义样式 C# 示例\n// 在这里添加自定义样式 C# 代码示例";
            InteractionXamlExample = "<!-- ListView 交互功能示例 -->\n<!-- 在这里添加交互功能示例 -->";
            InteractionCSharpExample = "// ListView 交互功能 C# 示例\n// 在这里添加交互功能 C# 代码示例";
        }

        #region 现代化MVVM数据处理方法

        /// <summary>
        /// 清除所有选择状态
        /// </summary>
        private void ClearAllSelections()
        {
            foreach (var item in DataItems)
            {
                item.IsSelected = false;
            }
            SelectedItems.Clear();
            SelectedDataItem = null;
        }

        /// <summary>
        /// 获取当前选择数据
        /// </summary>
        /// <returns>选择数据摘要</returns>
        public SelectionDataSummary GetCurrentSelectionData()
        {
            var summary = new SelectionDataSummary();

            if (IsMultiSelectEnabled)
            {
                // 多选模式：获取所有选中项
                summary.SelectionMode = "MultiSelect";
                summary.Items = Enumerable.ToList<ListViewDataItem>(SelectedItems);
                summary.Count = SelectedItems.Count;
                summary.Summary = $"多选模式：选中 {summary.Count} 个项目";

                // 按分类统计
                summary.CategoryStats = Enumerable
                    .GroupBy<ListViewDataItem, string>(SelectedItems, x => x.Category)
                    .ToDictionary(g => g.Key, g => g.Count());
            }
            else
            {
                // 单选模式：获取当前选中项
                summary.SelectionMode = "SingleSelect";
                if (SelectedDataItem != null)
                {
                    summary.Items = new List<ListViewDataItem> { SelectedDataItem };
                    summary.Count = 1;
                    summary.Summary = $"单选模式：选中项目 '{SelectedDataItem.Name}'";
                    summary.CategoryStats = new Dictionary<string, int> { { SelectedDataItem.Category, 1 } };
                }
                else
                {
                    summary.Items = new List<ListViewDataItem>();
                    summary.Count = 0;
                    summary.Summary = "单选模式：未选中任何项目";
                    summary.CategoryStats = new Dictionary<string, int>();
                }
            }

            summary.Timestamp = DateTime.Now;
            return summary;
        }

        /// <summary>
        /// 处理选中数据
        /// </summary>
        /// <param name="selectionData">选择数据</param>
        private void ProcessSelectedData(SelectionDataSummary selectionData)
        {
            _logger.Info($"📊 处理选中数据:");
            _logger.Info($"   选择模式: {selectionData.SelectionMode}");
            _logger.Info($"   项目数量: {selectionData.Count}");
            _logger.Info($"   时间戳: {selectionData.Timestamp:yyyy-MM-dd HH:mm:ss}");

            foreach (var category in selectionData.CategoryStats)
            {
                _logger.Info($"   分类 '{category.Key}': {category.Value} 个项目");
            }

            foreach (var item in selectionData.Items)
            {
                _logger.Info($"   项目: {item.Name} ({item.Category})");
            }
        }

        /// <summary>
        /// 导出数据为JSON格式
        /// </summary>
        /// <param name="selectionData">选择数据</param>
        /// <returns>导出结果</returns>
        private string ExportDataToJson(SelectionDataSummary selectionData)
        {
            try
            {
                var exportData = new
                {
                    ExportInfo = new
                    {
                        Timestamp = selectionData.Timestamp,
                        SelectionMode = selectionData.SelectionMode,
                        TotalCount = selectionData.Count
                    },
                    CategoryStatistics = selectionData.CategoryStats,
                    Items = selectionData.Items.Select(item => new
                    {
                        item.Id,
                        item.Name,
                        item.Description,
                        item.Category,
                        item.Icon,
                        item.IsEnabled,
                        CreatedDate = item.CreatedDate.ToString("yyyy-MM-dd HH:mm:ss")
                    }).ToArray()
                };

                // 在实际应用中，这里会保存到文件或发送到服务器
                var jsonResult = System.Text.Json.JsonSerializer.Serialize(exportData, new System.Text.Json.JsonSerializerOptions
                {
                    WriteIndented = true
                });

                _logger.Info($"📤 导出JSON数据长度: {jsonResult.Length} 字符");
                return $"成功导出 {selectionData.Count} 个项目";
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ JSON导出失败: {ex.Message}");
                return $"导出失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 根据条件筛选数据
        /// </summary>
        /// <param name="predicate">筛选条件</param>
        /// <returns>筛选结果</returns>
        public List<ListViewDataItem> FilterData(Func<ListViewDataItem, bool> predicate)
        {
            try
            {
                var filteredItems = Enumerable.Where(DataItems, predicate).ToList();
                _logger.Info($"🔍 筛选结果: {filteredItems.Count} 个项目符合条件");
                return filteredItems;
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 数据筛选失败: {ex.Message}");
                return new List<ListViewDataItem>();
            }
        }

        /// <summary>
        /// 批量更新项目状态
        /// </summary>
        /// <param name="items">要更新的项目</param>
        /// <param name="isEnabled">新的启用状态</param>
        public void BatchUpdateItemStatus(IEnumerable<ListViewDataItem> items, bool isEnabled)
        {
            try
            {
                var itemList = items.ToList();
                foreach (var item in itemList)
                {
                    item.IsEnabled = isEnabled;
                }

                InteractionCount++;
                LastAction = "批量更新状态";
                StatusMessage = $"🔄 已更新 {itemList.Count} 个项目的状态为: {(isEnabled ? "启用" : "禁用")}";
                _logger.Info($"🔄 批量更新了 {itemList.Count} 个项目的状态");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 批量更新状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 生成选中数据的详细报告
        /// </summary>
        /// <param name="selectionData">选择数据</param>
        /// <returns>详细报告文本</returns>
        private string GenerateSelectedDataReport(SelectionDataSummary selectionData)
        {
            var report = new StringBuilder();

            // 报告头部
            report.AppendLine("=== ListView 选中数据报告 ===");
            report.AppendLine($"生成时间: {selectionData.Timestamp:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine($"选择模式: {selectionData.SelectionMode}");
            report.AppendLine($"项目总数: {selectionData.Count}");
            report.AppendLine();

            // 分类统计
            if (selectionData.CategoryStats.Count > 0)
            {
                report.AppendLine("=== 分类统计 ===");
                foreach (var category in selectionData.CategoryStats)
                {
                    report.AppendLine($"• {category.Key}: {category.Value} 个项目");
                }
                report.AppendLine();
            }

            // 详细项目列表
            report.AppendLine("=== 详细项目列表 ===");
            for (int i = 0; i < selectionData.Items.Count; i++)
            {
                var item = selectionData.Items[i];
                report.AppendLine($"{i + 1}. {item.Name}");
                report.AppendLine($"   分类: {item.Category}");
                report.AppendLine($"   描述: {item.Description}");
                report.AppendLine($"   状态: {(item.IsEnabled ? "启用" : "禁用")}");
                report.AppendLine($"   ID: {item.Id}");
                report.AppendLine($"   创建时间: {item.CreatedDate:yyyy-MM-dd HH:mm:ss}");

                if (i < selectionData.Items.Count - 1)
                {
                    report.AppendLine();
                }
            }

            // 报告尾部
            report.AppendLine();
            report.AppendLine("=== 报告结束 ===");

            return report.ToString();
        }

        /// <summary>
        /// 更新选中内容的显示
        /// </summary>
        private void UpdateSelectedItemsDisplay()
        {
            try
            {
                if (IsMultiSelectEnabled)
                {
                    // 多选模式
                    if (SelectedItems.Count == 0)
                    {
                        SelectedItemsText = "多选模式：未选择任何项目";
                        SelectedItemsDetails = "请点击复选框或项目来选择多个项目";
                    }
                    else
                    {
                        SelectedItemsText = $"多选模式：已选择 {SelectedItems.Count} 个项目";

                        // 构建详细信息
                        var details = new List<string>();
                        var categoryGroups = Enumerable.GroupBy<ListViewDataItem, string>(SelectedItems, x => x.Category).ToList();

                        foreach (var group in categoryGroups)
                        {
                            details.Add($"【{group.Key}】{string.Join("、", group.Select<ListViewDataItem, string>(x => x.Name))}");
                        }

                        SelectedItemsDetails = string.Join("\n", details);

                        // 如果内容太长，进行截断
                        if (SelectedItemsDetails.Length > 500)
                        {
                            SelectedItemsDetails = SelectedItemsDetails.Substring(0, 500) + "...";
                        }
                    }
                }
                else
                {
                    // 单选模式
                    if (SelectedDataItem == null)
                    {
                        SelectedItemsText = "单选模式：未选择任何项目";
                        SelectedItemsDetails = "请点击项目来选择一个项目";
                    }
                    else
                    {
                        SelectedItemsText = $"单选模式：已选择 '{SelectedDataItem.Name}'";
                        SelectedItemsDetails = $"分类：{SelectedDataItem.Category}\n" +
                                             $"描述：{SelectedDataItem.Description}\n" +
                                             $"状态：{(SelectedDataItem.IsEnabled ? "启用" : "禁用")}\n" +
                                             $"创建时间：{SelectedDataItem.CreatedDate:yyyy-MM-dd HH:mm:ss}";
                    }
                }

                _logger.Debug($"🔄 更新选中内容显示: {SelectedItemsText}");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 更新选中内容显示失败: {ex.Message}");
                SelectedItemsText = "显示错误";
                SelectedItemsDetails = $"错误信息: {ex.Message}";
            }
        }

        #endregion

        #endregion
    }

    /// <summary>
    /// 选择数据摘要
    /// </summary>
    public class SelectionDataSummary
    {
        /// <summary>
        /// 选择模式
        /// </summary>
        public string SelectionMode { get; set; } = string.Empty;

        /// <summary>
        /// 选中的项目列表
        /// </summary>
        public List<ListViewDataItem> Items { get; set; } = new();

        /// <summary>
        /// 选中项目数量
        /// </summary>
        public int Count { get; set; }

        /// <summary>
        /// 摘要信息
        /// </summary>
        public string Summary { get; set; } = string.Empty;

        /// <summary>
        /// 分类统计
        /// </summary>
        public Dictionary<string, int> CategoryStats { get; set; } = new();

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// ListView 数据项模型
    /// </summary>
    public partial  class ListViewDataItem : ObservableObject
    {
        /// <summary>
        /// 数据项 ID
        /// </summary>
        [ObservableProperty]
        private int id;

        /// <summary>
        /// 数据项名称
        /// </summary>
        [ObservableProperty]
        private string name = string.Empty;

        /// <summary>
        /// 数据项描述
        /// </summary>
        [ObservableProperty]
        private string description = string.Empty;

        [ObservableProperty]
        private string icon = "Document24";

        [ObservableProperty]
        private string category = "默认";

        [ObservableProperty]
        private bool isEnabled = true;

        [ObservableProperty]
        private DateTime createdDate = DateTime.Now;

        [ObservableProperty]
        private bool isSelected = false;
    }

