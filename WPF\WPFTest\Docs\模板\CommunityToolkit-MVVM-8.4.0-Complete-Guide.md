# 🎯 CommunityToolkit.Mvvm 8.4.0 完整标签汇总指南

## 📊 所有标签快速参考表

| 标签分类 | 标签名称 | 适用对象 | 主要功能 | 8.4.0 新特性 |
|---------|---------|---------|---------|-------------|
| **属性生成** | `[ObservableProperty]` | Field/Property | 生成可观察属性 | ✅ **支持 Partial Properties** |
| **属性通知** | `[NotifyPropertyChangedFor]` | Field/Property | 通知依赖属性变化 | ✅ 支持 Partial Properties |
| **命令通知** | `[NotifyCanExecuteChangedFor]` | Field/Property | 通知命令状态变化 | ✅ 支持 Partial Properties |
| **数据验证** | `[NotifyDataErrorInfo]` | Field/Property | 启用属性验证 | ✅ 支持 Partial Properties |
| **消息传递** | `[NotifyPropertyChangedRecipients]` | Field/Property | 发送属性变化消息 | ✅ 支持 Partial Properties |
| **命令生成** | `[RelayCommand]` | Method | 生成命令属性 | ✅ 新增多个分析器 |
| **命令参数** | `CanExecute = "方法名"` | RelayCommand | 指定命令可执行条件 | - |
| **异步控制** | `AllowConcurrentExecutions` | RelayCommand | 允许并发执行 | - |
| **异常处理** | `FlowExceptionsToTaskScheduler` | RelayCommand | 异常流向任务调度器 | - |
| **取消支持** | `IncludeCancelCommand` | RelayCommand | 生成取消命令 | - |
| **接口实现** | `[INotifyPropertyChanged]` | Class | 自动实现接口 | ⚠️ **AOT 兼容性警告** |
| **对象生成** | `[ObservableObject]` | Class | 生成可观察对象 | ⚠️ **AOT 兼容性警告** |
| **属性转发** | `[property:]` | Field/Method | 转发自定义标签 | ✅ **支持访问器标签** |

---

## 🆕 8.4.0 重大更新：Partial Properties 支持

### **🎉 全新的 Partial Properties 语法**

**旧写法（Field-based）：**
```csharp
[ObservableProperty]
private string name = string.Empty;
```

**新写法（Partial Properties）：**
```csharp
[ObservableProperty]
public partial string Name { get; set; } = string.Empty;
```

### **🔧 启用 Partial Properties**

在 `.csproj` 文件中添加：
```xml
<PropertyGroup>
    <LangVersion>preview</LangVersion>
</PropertyGroup>
```

### **✨ Partial Properties 优势**

1. **完全 AOT 兼容**：特别是 UWP 和 WinUI 3 应用
2. **更好的语言集成**：支持所有 C# 属性特性
3. **访问修饰符支持**：`public get; private set;` 等
4. **属性修饰符支持**：`new`、`sealed`、`override`、`required`
5. **更好的 IDE 支持**：F12 导航、重构等
6. **改进的可空性注解**：更准确的类型推断

---

## 🏷️ 1. 属性相关标签详解

### 📊 属性标签汇总表

| 标签名称 | 适用版本 | 主要功能 | 8.4.0 新特性 | 示例 |
|---------|---------|---------|-------------|------|
| `[ObservableProperty]` | 全版本 | 生成可观察属性 | ✅ **Partial Properties 支持** | `public partial string Name { get; set; }` |
| `[NotifyPropertyChangedFor]` | 全版本 | 通知依赖属性变化 | ✅ 支持 Partial Properties | `[NotifyPropertyChangedFor(nameof(FullName))]` |
| `[NotifyCanExecuteChangedFor]` | 全版本 | 通知命令状态变化 | ✅ 支持 Partial Properties | `[NotifyCanExecuteChangedFor(nameof(SaveCommand))]` |
| `[NotifyDataErrorInfo]` | 全版本 | 启用属性验证 | ✅ 支持 Partial Properties | `[NotifyDataErrorInfo]` + 验证标签 |
| `[NotifyPropertyChangedRecipients]` | 全版本 | 发送属性变化消息 | ✅ 支持 Partial Properties | `[NotifyPropertyChangedRecipients]` |
| `[property:]` | 全版本 | 转发标签到属性 | ✅ **访问器级别转发** | `[property: JsonPropertyName("name")]` |
| `[field:]` | 8.4.0+ | 转发标签到字段 | 🆕 **新增功能** | `[field: NonSerialized]` |
| `[property: get:]` | 8.4.0+ | 转发标签到 getter | 🆕 **新增功能** | `[property: get: JsonIgnore]` |
| `[property: set:]` | 8.4.0+ | 转发标签到 setter | 🆕 **新增功能** | `[property: set: Obsolete]` |

---

### **`[ObservableProperty]` - 核心属性标签**

#### **Field 写法（传统）：**
```csharp
[ObservableProperty]
private string name = string.Empty;
// 生成: public string Name { get; set; }
```

#### **Partial Property 写法（8.4.0 推荐）：**
```csharp
[ObservableProperty]
public partial string Name { get; set; } = string.Empty;
```

#### **高级用法：**
```csharp
[ObservableProperty]
public partial string Name { get; private set; } = string.Empty;

[ObservableProperty]
[property: JsonPropertyName("user_name")]
public partial string UserName { get; set; } = string.Empty;

[ObservableProperty]
public required partial string RequiredName { get; set; }
```

### **`[NotifyPropertyChangedFor]` - 通知依赖属性**
```csharp
[ObservableProperty]
[NotifyPropertyChangedFor(nameof(FullName))]
[NotifyPropertyChangedFor(nameof(DisplayText))]
public partial string FirstName { get; set; } = string.Empty;

public string FullName => $"{FirstName} {LastName}";
```

### **`[NotifyCanExecuteChangedFor]` - 通知命令状态**
```csharp
[ObservableProperty]
[NotifyCanExecuteChangedFor(nameof(SaveCommand))]
[NotifyCanExecuteChangedFor(nameof(DeleteCommand))]
public partial bool IsDataValid { get; set; }
```

### **`[NotifyDataErrorInfo]` - 启用验证**
```csharp
[ObservableProperty]
[NotifyDataErrorInfo]
[Required(ErrorMessage = "用户名不能为空")]
[StringLength(20, MinimumLength = 3)]
public partial string UserName { get; set; } = string.Empty;
```

### **`[NotifyPropertyChangedRecipients]` - 发送消息**
```csharp
[ObservableProperty]
[NotifyPropertyChangedRecipients]
public partial string SelectedItem { get; set; } = string.Empty;
// 自动发送 PropertyChangedMessage<T>
```

---

## 🏷️ 2. 命令相关标签详解

### 📊 命令标签汇总表

| 标签/参数名称 | 适用版本 | 主要功能 | 8.4.0 新特性 | 示例 |
|-------------|---------|---------|-------------|------|
| `[RelayCommand]` | 全版本 | 生成基础命令 | ✅ **新增多个分析器** | `[RelayCommand] private void Save()` |
| `CanExecute = "方法名"` | 全版本 | 指定可执行条件 | - | `[RelayCommand(CanExecute = nameof(CanSave))]` |
| `AllowConcurrentExecutions` | 全版本 | 允许并发执行 | - | `[RelayCommand(AllowConcurrentExecutions = true)]` |
| `FlowExceptionsToTaskScheduler` | 全版本 | 异常流向任务调度器 | - | `[RelayCommand(FlowExceptionsToTaskScheduler = true)]` |
| `IncludeCancelCommand` | 全版本 | 生成取消命令 | - | `[RelayCommand(IncludeCancelCommand = true)]` |
| `[property:]` 命令转发 | 全版本 | 转发标签到命令属性 | ✅ **改进支持** | `[property: Description("保存命令")]` |

### 📋 命令类型支持表

| 方法签名 | 生成的命令类型 | 支持参数 | 支持异步 | 支持取消 |
|---------|---------------|---------|---------|---------|
| `void Method()` | `IRelayCommand` | ❌ | ❌ | ❌ |
| `void Method(T param)` | `IRelayCommand<T>` | ✅ | ❌ | ❌ |
| `Task MethodAsync()` | `IAsyncRelayCommand` | ❌ | ✅ | ❌ |
| `Task MethodAsync(T param)` | `IAsyncRelayCommand<T>` | ✅ | ✅ | ❌ |
| `Task MethodAsync(CancellationToken)` | `IAsyncRelayCommand` | ❌ | ✅ | ✅ |
| `Task MethodAsync(T, CancellationToken)` | `IAsyncRelayCommand<T>` | ✅ | ✅ | ✅ |

---

### **`[RelayCommand]` - 基础命令**
```csharp
[RelayCommand]
private void SaveData()
{
    // 同步命令
}
// 生成: public IRelayCommand SaveDataCommand { get; }
```

### **`[RelayCommand]` - 带参数命令**
```csharp
[RelayCommand]
private void DeleteItem(string itemId)
{
    // 带参数命令
}
// 生成: public IRelayCommand<string> DeleteItemCommand { get; }
```

### **`[RelayCommand]` - 异步命令**
```csharp
[RelayCommand]
private async Task LoadDataAsync()
{
    await Task.Delay(1000);
}
// 生成: public IAsyncRelayCommand LoadDataCommand { get; }
```

### **`[RelayCommand]` - 完整配置**
```csharp
[RelayCommand(
    CanExecute = nameof(CanSaveData),
    AllowConcurrentExecutions = false,
    FlowExceptionsToTaskScheduler = true,
    IncludeCancelCommand = true
)]
private async Task SaveDataAsync(CancellationToken cancellationToken)
{
    // 完整配置的异步命令
}

private bool CanSaveData()
{
    return !string.IsNullOrEmpty(UserName);
}
```

---

## 🏷️ 3. 基类和接口标签

### 📊 基类和接口汇总表

| 标签/基类名称 | 适用版本 | 主要功能 | 8.4.0 变化 | AOT 兼容性 | 推荐使用 |
|-------------|---------|---------|-----------|-----------|---------|
| `ObservableObject` | 全版本 | 基础属性通知 | - | ✅ 完全兼容 | ✅ **推荐** |
| `ObservableValidator` | 全版本 | 属性通知 + 验证 | - | ✅ 完全兼容 | ✅ **推荐** |
| `ObservableRecipient` | 全版本 | 属性通知 + 消息 | - | ✅ 完全兼容 | ✅ **推荐** |
| `[INotifyPropertyChanged]` | 全版本 | 自动实现接口 | ⚠️ **AOT 警告** | ❌ 不兼容 | ❌ 不推荐 |
| `[ObservableObject]` | 全版本 | 生成可观察对象 | ⚠️ **AOT 警告** | ❌ 不兼容 | ❌ 不推荐 |

### 🎯 8.4.0 重要变化：AOT 兼容性

#### **⚠️ 新增 AOT 兼容性警告**
- **MVVMTK0049**：`[INotifyPropertyChanged]` 在 WinRT 场景下不支持 AOT
- **MVVMTK0050**：`[ObservableObject]` 在 WinRT 场景下不支持 AOT

#### **✅ 推荐的 AOT 兼容写法**
```csharp
// ❌ 8.4.0 中会产生 AOT 警告
[INotifyPropertyChanged]
public partial class MyModel { }

// ✅ 推荐：继承基类，完全 AOT 兼容
public partial class MyModel : ObservableObject { }
```

---

### **继承基类选择**
```csharp
// 基础通知
public partial class MyViewModel : ObservableObject

// 带验证功能  
public partial class MyViewModel : ObservableValidator

// 带消息功能
public partial class MyViewModel : ObservableRecipient
```

### **`[INotifyPropertyChanged]` - 自动实现接口**
```csharp
[INotifyPropertyChanged]
public partial class MyModel
{
    [ObservableProperty]
    public partial string Name { get; set; } = string.Empty;
}
// ⚠️ 8.4.0 警告：AOT 不兼容，推荐继承 ObservableObject
```

---

## 🏷️ 4. 属性转发标签

### 📊 属性转发标签汇总表

| 转发标签 | 适用版本 | 转发目标 | 8.4.0 新特性 | 应用场景 | 示例 |
|---------|---------|---------|-------------|---------|------|
| `[property:]` | 全版本 | 生成的属性 | ✅ **访问器支持** | JSON序列化、验证 | `[property: JsonPropertyName("name")]` |
| `[field:]` | 8.4.0+ | 底层字段 | 🆕 **新增功能** | 字段级控制 | `[field: NonSerialized]` |
| `[property: get:]` | 8.4.0+ | 属性 getter | 🆕 **新增功能** | getter 特定行为 | `[property: get: JsonIgnore]` |
| `[property: set:]` | 8.4.0+ | 属性 setter | 🆕 **新增功能** | setter 特定行为 | `[property: set: Obsolete]` |

### 🆕 8.4.0 重大新功能：访问器级别标签转发

#### **🎯 新功能详细介绍**

**8.4.0 引入了革命性的访问器级别标签转发功能**，允许您精确控制标签应用到属性的哪个部分：

#### **1. `[property: get:]` - Getter 专用标签**
```csharp
[ObservableProperty]
[property: get: JsonIgnore]  // 只在序列化时忽略读取
[property: get: Obsolete("Use GetDisplayName() method")]
public partial string DisplayName { get; set; } = string.Empty;
```

**生成的代码：**
```csharp
public partial string DisplayName
{
    [JsonIgnore]
    [Obsolete("Use GetDisplayName() method")]
    get => field;

    set => SetProperty(ref field, value);
}
```

#### **2. `[property: set:]` - Setter 专用标签**
```csharp
[ObservableProperty]
[property: set: Obsolete("Use UpdateUserName method")]
[property: set: EditorBrowsable(EditorBrowsableState.Never)]
public partial string UserName { get; set; } = string.Empty;
```

**生成的代码：**
```csharp
public partial string UserName
{
    get => field;

    [Obsolete("Use UpdateUserName method")]
    [EditorBrowsable(EditorBrowsableState.Never)]
    set => SetProperty(ref field, value);
}
```

#### **3. `[field:]` - 字段专用标签（8.4.0 新增）**
```csharp
[ObservableProperty]
[field: NonSerialized]  // 字段不参与序列化
[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
public partial string TempData { get; set; } = string.Empty;
```

#### **4. 混合使用示例**
```csharp
[ObservableProperty]
[property: JsonPropertyName("user_data")]  // 属性级别
[property: get: JsonConverter(typeof(CustomConverter))]  // getter 级别
[property: set: JsonIgnore]  // setter 级别：反序列化时忽略
[field: NonSerialized]  // 字段级别
public partial UserData Data { get; set; } = new();
```

### 🎯 实际应用场景

#### **场景1：API 兼容性管理**
```csharp
[ObservableProperty]
[property: get: Obsolete("Use NewProperty instead", false)]
[property: set: Obsolete("Use SetNewProperty method", true)]  // 编译错误
public partial string LegacyProperty { get; set; } = string.Empty;
```

#### **场景2：序列化控制**
```csharp
[ObservableProperty]
[property: JsonPropertyName("secret_data")]
[property: get: JsonIgnore]  // 序列化时不输出
[property: set: JsonRequired]  // 反序列化时必需
public partial string SecretData { get; set; } = string.Empty;
```

#### **场景3：调试和开发工具**
```csharp
[ObservableProperty]
[property: get: DebuggerStepThrough]
[property: set: DebuggerHidden]
[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
public partial string InternalState { get; set; } = string.Empty;
```

### 🎯 8.4.0 新功能完整示例

#### **示例1：电商用户管理系统**

```csharp
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace ECommerce.ViewModels;

/// <summary>
/// 8.4.0 新功能展示：电商用户管理 ViewModel
/// </summary>
public partial class UserManagementViewModel : ObservableValidator
{
    #region 8.4.0 Partial Properties 新功能

    /// <summary>
    /// 用户ID - 展示多种标签转发
    /// </summary>
    [ObservableProperty]
    [property: JsonPropertyName("user_id")]           // JSON 序列化名称
    [property: Required(ErrorMessage = "用户ID不能为空")]
    [property: get: Description("获取用户唯一标识")]    // 🆕 getter 专用标签
    [field: DebuggerBrowsable(DebuggerBrowsableState.Never)]  // 🆕 字段专用标签
    public partial string UserId { get; set; } = string.Empty;

    /// <summary>
    /// 用户名 - 展示访问器级别控制
    /// </summary>
    [ObservableProperty]
    [property: JsonPropertyName("username")]
    [property: StringLength(50, MinimumLength = 3)]
    [property: get: JsonConverter(typeof(UsernameConverter))]  // 🆕 getter 专用转换器
    [property: set: Obsolete("Use UpdateUsername method", false)]  // 🆕 setter 过时警告
    public partial string Username { get; set; } = string.Empty;

    /// <summary>
    /// 密码 - 展示安全性控制
    /// </summary>
    [ObservableProperty]
    [property: JsonIgnore]                           // 整个属性不序列化
    [property: get: EditorBrowsable(EditorBrowsableState.Never)]  // 🆕 getter 在编辑器中隐藏
    [property: set: MethodImpl(MethodImplOptions.NoInlining)]     // 🆕 setter 不内联
    [field: NonSerialized]                          // 🆕 字段不序列化
    public partial string Password { get; set; } = string.Empty;

    /// <summary>
    /// 邮箱 - 展示验证和序列化控制
    /// </summary>
    [ObservableProperty]
    [NotifyPropertyChangedFor(nameof(IsEmailValid))]
    [property: JsonPropertyName("email_address")]
    [property: EmailAddress(ErrorMessage = "请输入有效的邮箱地址")]
    [property: get: JsonRequired]                    // 🆕 getter 必需
    [property: set: JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]  // 🆕 setter 条件忽略
    public partial string Email { get; set; } = string.Empty;

    /// <summary>
    /// 用户状态 - 展示枚举和访问控制
    /// </summary>
    [ObservableProperty]
    [NotifyCanExecuteChangedFor(nameof(ActivateUserCommand))]
    [NotifyCanExecuteChangedFor(nameof(DeactivateUserCommand))]
    [property: JsonConverter(typeof(JsonStringEnumConverter))]
    [property: get: Description("获取用户当前状态")]
    [property: set: RequiresPermission("UserManagement.ChangeStatus")]  // 🆕 自定义权限标签
    public partial UserStatus Status { get; set; } = UserStatus.Inactive;

    /// <summary>
    /// 创建时间 - 展示只读属性
    /// </summary>
    [ObservableProperty]
    [property: JsonPropertyName("created_at")]
    [property: get: JsonConverter(typeof(DateTimeConverter))]
    [property: set: JsonIgnore]                     // 🆕 setter 不参与反序列化
    [property: set: EditorBrowsable(EditorBrowsableState.Never)]  // 🆕 setter 编辑器隐藏
    public partial DateTime CreatedAt { get; private set; } = DateTime.Now;  // 🆕 支持 private set

    /// <summary>
    /// 临时数据 - 展示字段级别控制
    /// </summary>
    [ObservableProperty]
    [property: JsonIgnore]                          // 属性不序列化
    [field: NonSerialized]                         // 🆕 字段也不序列化
    [field: DebuggerBrowsable(DebuggerBrowsableState.Never)]  // 🆕 调试器中隐藏字段
    public partial string TempData { get; set; } = string.Empty;

    #endregion

    #region 计算属性

    /// <summary>
    /// 邮箱是否有效
    /// </summary>
    public bool IsEmailValid => !string.IsNullOrEmpty(Email) && Email.Contains("@");

    /// <summary>
    /// 用户显示名称
    /// </summary>
    public string DisplayName => $"{Username} ({Email})";

    #endregion

    #region 8.4.0 现代化命令

    /// <summary>
    /// 激活用户命令 - 展示新的分析器支持
    /// </summary>
    [RelayCommand(CanExecute = nameof(CanActivateUser))]
    [property: Description("激活用户账户")]          // 🆕 命令属性标签转发
    [property: Category("用户管理")]
    private async Task ActivateUserAsync()
    {
        Status = UserStatus.Active;
        await SaveUserAsync();
    }

    /// <summary>
    /// 停用用户命令
    /// </summary>
    [RelayCommand(CanExecute = nameof(CanDeactivateUser))]
    private async Task DeactivateUserAsync()
    {
        Status = UserStatus.Inactive;
        await SaveUserAsync();
    }

    /// <summary>
    /// 更新用户名 - 替代过时的 setter
    /// </summary>
    [RelayCommand]
    private void UpdateUsername(string newUsername)
    {
        if (!string.IsNullOrWhiteSpace(newUsername))
        {
            // 绕过过时的 setter，直接更新
            SetProperty(ref field, newUsername, nameof(Username));  // 🆕 使用 field 关键字
        }
    }

    /// <summary>
    /// 保存用户 - 展示异步命令配置
    /// </summary>
    [RelayCommand(
        IncludeCancelCommand = true,
        FlowExceptionsToTaskScheduler = true
    )]
    private async Task SaveUserAsync(CancellationToken cancellationToken = default)
    {
        ValidateAllProperties();
        if (HasErrors) return;

        // 模拟保存操作
        await Task.Delay(2000, cancellationToken);
    }

    #endregion

    #region 8.4.0 属性变化处理

    /// <summary>
    /// 用户名变化处理 - 展示新的 partial void 用法
    /// </summary>
    partial void OnUsernameChanged(string value)
    {
        // 用户名变化时自动更新显示名称
        OnPropertyChanged(nameof(DisplayName));

        // 记录变化（避免使用过时的 setter）
        TempData = $"Username changed to: {value} at {DateTime.Now}";
    }

    /// <summary>
    /// 邮箱变化处理 - 展示旧值和新值
    /// </summary>
    partial void OnEmailChanged(string oldValue, string newValue)
    {
        OnPropertyChanged(nameof(DisplayName));
        OnPropertyChanged(nameof(IsEmailValid));

        // 记录邮箱变化历史
        if (!string.IsNullOrEmpty(oldValue))
        {
            TempData = $"Email changed from {oldValue} to {newValue}";
        }
    }

    /// <summary>
    /// 状态变化前处理
    /// </summary>
    partial void OnStatusChanging(UserStatus value)
    {
        // 状态变化前的验证逻辑
        if (value == UserStatus.Deleted && !string.IsNullOrEmpty(Email))
        {
            // 可以在这里添加额外的验证
            TempData = $"Preparing to change status to {value}";
        }
    }

    #endregion

    #region 辅助方法

    private bool CanActivateUser() => Status != UserStatus.Active && !string.IsNullOrEmpty(UserId);
    private bool CanDeactivateUser() => Status == UserStatus.Active;

    #endregion
}

/// <summary>
/// 用户状态枚举
/// </summary>
public enum UserStatus
{
    Inactive,
    Active,
    Suspended,
    Deleted
}

/// <summary>
/// 自定义权限标签示例
/// </summary>
[AttributeUsage(AttributeTargets.Property | AttributeTargets.Method)]
public class RequiresPermissionAttribute : Attribute
{
    public string Permission { get; }
    public RequiresPermissionAttribute(string permission) => Permission = permission;
}
```

#### **示例2：文件管理系统**

```csharp
/// <summary>
/// 文件管理 ViewModel - 展示 8.4.0 高级特性
/// </summary>
public partial class FileManagerViewModel : ObservableObject
{
    #region 文件属性 - 展示不同的标签转发场景

    /// <summary>
    /// 文件路径 - 展示路径验证和序列化
    /// </summary>
    [ObservableProperty]
    [property: JsonPropertyName("file_path")]
    [property: get: JsonConverter(typeof(FilePathConverter))]  // 🆕 自定义路径转换器
    [property: set: ValidatesFilePath]                         // 🆕 自定义验证标签
    [field: DebuggerDisplay("{value}")]                       // 🆕 字段调试显示
    public partial string FilePath { get; set; } = string.Empty;

    /// <summary>
    /// 文件大小 - 展示格式化和只读控制
    /// </summary>
    [ObservableProperty]
    [property: JsonPropertyName("file_size")]
    [property: get: DisplayFormat(DataFormatString = "{0:N0} bytes")]  // 🆕 格式化显示
    [property: set: JsonIgnore]                               // 🆕 不允许反序列化设置
    [property: set: Obsolete("File size is read-only", true)] // 🆕 setter 编译错误
    public partial long FileSize { get; private set; }

    /// <summary>
    /// 文件内容 - 展示大数据处理
    /// </summary>
    [ObservableProperty]
    [property: JsonIgnore]                                    // 不序列化大数据
    [property: get: MethodImpl(MethodImplOptions.NoInlining)] // 🆕 不内联以便调试
    [property: set: MethodImpl(MethodImplOptions.AggressiveInlining)]  // 🆕 setter 内联优化
    [field: NonSerialized]                                   // 🆕 字段不序列化
    public partial byte[] FileContent { get; set; } = Array.Empty<byte>();

    #endregion

    #region 命令 - 展示新的命令特性

    /// <summary>
    /// 加载文件命令 - 展示命令标签转发
    /// </summary>
    [RelayCommand(IncludeCancelCommand = true)]
    [property: Description("从磁盘加载文件")]                  // 🆕 命令描述
    [property: Category("文件操作")]                          // 🆕 命令分类
    [property: get: ToolTip("点击选择并加载文件")]             // 🆕 命令工具提示
    private async Task LoadFileAsync(string path, CancellationToken cancellationToken)
    {
        FilePath = path;

        // 使用新的 field 关键字直接设置
        var info = new FileInfo(path);
        SetProperty(ref field, info.Length, nameof(FileSize));  // 🆕 直接设置只读属性

        FileContent = await File.ReadAllBytesAsync(path, cancellationToken);
    }

    #endregion
}
```

#### **示例3：实时数据监控系统**

```csharp
/// <summary>
/// 数据监控 ViewModel - 展示性能优化特性
/// </summary>
public partial class DataMonitorViewModel : ObservableRecipient
{
    #region 性能优化属性

    /// <summary>
    /// 实时数据 - 展示高频更新优化
    /// </summary>
    [ObservableProperty]
    [property: JsonPropertyName("real_time_value")]
    [property: get: MethodImpl(MethodImplOptions.AggressiveInlining)]  // 🆕 getter 内联优化
    [property: set: MethodImpl(MethodImplOptions.AggressiveInlining)]  // 🆕 setter 内联优化
    [field: FieldOffset(0)]                                           // 🆕 字段内存布局控制
    public partial double RealTimeValue { get; set; }

    /// <summary>
    /// 历史数据 - 展示延迟加载
    /// </summary>
    [ObservableProperty]
    [property: JsonIgnore]                                            // 不序列化大数据
    [property: get: Lazy]                                             // 🆕 延迟加载标签
    [property: get: MethodImpl(MethodImplOptions.NoInlining)]         // 🆕 不内联以便缓存
    [field: ThreadStatic]                                             // 🆕 线程静态字段
    public partial List<DataPoint> HistoryData { get; set; } = new();

    #endregion

    #region 8.4.0 属性变化优化

    /// <summary>
    /// 实时数据变化 - 展示高性能处理
    /// </summary>
    partial void OnRealTimeValueChanged(double oldValue, double newValue)
    {
        // 高频更新时的性能优化
        if (Math.Abs(newValue - oldValue) > 0.01) // 只在显著变化时处理
        {
            // 使用 field 关键字直接操作
            if (field.Count > 1000)
            {
                field.RemoveAt(0); // 保持历史数据大小
            }
            field.Add(new DataPoint(DateTime.Now, newValue));
        }
    }

    #endregion
}

public record DataPoint(DateTime Time, double Value);
```

---

### **`[property:]` - 转发到属性**
```csharp
[ObservableProperty]
[property: JsonPropertyName("user_name")]
[property: JsonRequired]
[property: Description("用户名称")]
public partial string UserName { get; set; } = string.Empty;
```

### **`[field:]` - 转发到字段（8.4.0 新增）**
```csharp
[ObservableProperty]
[field: NonSerialized]
public partial string TempData { get; set; } = string.Empty;
```

### **访问器标签（8.4.0 新增）**
```csharp
[ObservableProperty]
[property: get: JsonIgnore]
[property: set: Obsolete("Use SetUserName method")]
public partial string UserName { get; set; } = string.Empty;
```

---

## 🚨 5. 8.4.0 新增分析器详解

### 📊 新增分析器完整汇总表

| 分析器代码 | 级别 | 分类 | 描述 | 解决方案 | 影响范围 |
|-----------|------|------|------|---------|---------|
| **MVVMTK0041** | Error | 语言版本 | 需要 C# preview 版本 | 添加 `<LangVersion>preview</LangVersion>` | Partial Properties |
| **MVVMTK0042** | Error | 代码现代化 | 建议使用 Partial Properties | 使用代码修复器自动转换 | 所有 Field 属性 |
| **MVVMTK0043** | Error | 属性定义 | Partial Properties 必须是实例属性 | 检查属性定义 | Partial Properties |
| **MVVMTK0044** | Error | 工具版本 | Roslyn 版本过低 | 升级 VS 2022 17.12+ 和 .NET 9 | Partial Properties |
| **MVVMTK0045** | Warning | AOT 兼容性 | Field 方式不支持 WinRT AOT | 转换为 Partial Properties | UWP/WinUI 3 |
| **MVVMTK0046** | Warning | 生成器冲突 | RelayCommand 与 GeneratedBindableCustomProperty 冲突 | 手动声明命令属性 | 特定场景 |
| **MVVMTK0047** | Warning | 生成器冲突 | ObservableProperty 与 GeneratedBindableCustomProperty 冲突 | 使用 Partial Properties | 特定场景 |
| **MVVMTK0048** | Warning | 继承冲突 | 继承的 RelayCommand 与 GeneratedBindableCustomProperty 冲突 | 手动声明命令 | 继承场景 |
| **MVVMTK0049** | Warning | AOT 兼容性 | `[INotifyPropertyChanged]` 不支持 WinRT AOT | 继承 `ObservableObject` | UWP/WinUI 3 |
| **MVVMTK0050** | Warning | AOT 兼容性 | `[ObservableObject]` 不支持 WinRT AOT | 继承 `ObservableObject` | UWP/WinUI 3 |
| **MVVMTK0051** | Info | 项目配置 | 项目有 AOT 警告，建议启用 preview | 设置 `LangVersion=preview` | 项目级别 |
| **MVVMTK0052** | Error | 属性实现 | Partial Properties 不能有实现 | 移除属性实现 | Partial Properties |
| **MVVMTK0053** | Error | 返回类型 | 不能返回引用类型 | 修改返回类型 | 属性定义 |
| **MVVMTK0054** | Error | 返回类型 | 不能返回 byref-like 类型 | 修改返回类型 | 属性定义 |
| **MVVMTK0055** | Error | 返回类型 | 不能返回指针类型 | 修改返回类型 | 属性定义 |

### 🎯 分析器分类详解

#### **🔧 语言和工具版本类（MVVMTK0041, 0044, 0051）**
这类分析器确保您使用了正确的开发环境：

```xml
<!-- 解决方案：项目配置 -->
<PropertyGroup>
    <LangVersion>preview</LangVersion>  <!-- 解决 MVVMTK0041 -->
    <TargetFramework>net9.0</TargetFramework>
</PropertyGroup>
```

#### **⚡ AOT 兼容性类（MVVMTK0045, 0049, 0050）**
这类分析器确保代码在 UWP 和 WinUI 3 应用中支持 Native AOT：

```csharp
// ❌ 会产生 MVVMTK0045 警告
[ObservableProperty]
private string name;

// ✅ AOT 兼容的写法
[ObservableProperty]
public partial string Name { get; set; }

// ❌ 会产生 MVVMTK0049 警告
[INotifyPropertyChanged]
public partial class MyModel { }

// ✅ AOT 兼容的写法
public partial class MyModel : ObservableObject { }
```

#### **🔄 代码现代化类（MVVMTK0042, 0043, 0052-0055）**
这类分析器帮助您编写更现代、更安全的代码：

```csharp
// MVVMTK0042：建议现代化
[ObservableProperty]
private string oldStyle;  // ❌ 旧写法

[ObservableProperty]
public partial string NewStyle { get; set; }  // ✅ 新写法

// MVVMTK0043：属性定义错误
[ObservableProperty]
public static partial string StaticProp { get; set; }  // ❌ 不能是静态

[ObservableProperty]
public partial string CorrectProp { get; set; }  // ✅ 实例属性

// MVVMTK0052：不能有实现
[ObservableProperty]
public partial string BadProp { get => "bad"; set { } }  // ❌ 有实现

[ObservableProperty]
public partial string GoodProp { get; set; }  // ✅ 无实现
```

#### **⚠️ 生成器冲突类（MVVMTK0046, 0047, 0048）**
这类分析器处理多个源生成器之间的冲突：

```csharp
// 当同时使用多个生成器时可能出现冲突
[GeneratedBindableCustomProperty]  // WinUI 3 生成器
public partial class MyControl : UserControl
{
    // ❌ MVVMTK0046：冲突
    [RelayCommand]
    private void DoSomething() { }

    // ✅ 解决方案：手动声明
    public IRelayCommand DoSomethingCommand { get; }
}
```

---

## 🎯 6. 完整示例：8.4.0 现代化 ViewModel

```csharp
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.ComponentModel.DataAnnotations;

namespace MyApp.ViewModels;

public partial class UserProfileViewModel : ObservableValidator
{
    #region 8.4.0 Partial Properties
    
    [ObservableProperty]
    [NotifyPropertyChangedFor(nameof(FullName))]
    [NotifyCanExecuteChangedFor(nameof(SaveCommand))]
    [NotifyDataErrorInfo]
    [Required(ErrorMessage = "姓名不能为空")]
    [property: JsonPropertyName("first_name")]
    public partial string FirstName { get; set; } = string.Empty;

    [ObservableProperty]
    [NotifyPropertyChangedFor(nameof(FullName))]
    [NotifyCanExecuteChangedFor(nameof(SaveCommand))]
    [NotifyDataErrorInfo]
    [Required]
    public partial string LastName { get; set; } = string.Empty;

    [ObservableProperty]
    [NotifyDataErrorInfo]
    [EmailAddress(ErrorMessage = "请输入有效的邮箱地址")]
    public partial string Email { get; set; } = string.Empty;

    [ObservableProperty]
    [NotifyCanExecuteChangedFor(nameof(DeleteCommand))]
    public partial bool IsSelected { get; set; }

    [ObservableProperty]
    public partial bool IsLoading { get; set; }

    // 只读属性
    [ObservableProperty]
    public partial string Status { get; private set; } = "Ready";

    // 必需属性
    [ObservableProperty]
    public required partial string UserId { get; set; }

    #endregion

    #region 计算属性

    public string FullName => $"{FirstName} {LastName}".Trim();

    #endregion

    #region 8.4.0 属性变化处理

    // 使用新值参数
    partial void OnFirstNameChanged(string value)
    {
        OnPropertyChanged(nameof(FullName));
        Status = $"First name changed to: {value}";
    }

    // 使用旧值和新值参数
    partial void OnLastNameChanged(string oldValue, string newValue)
    {
        OnPropertyChanged(nameof(FullName));
        Status = $"Last name changed from '{oldValue}' to '{newValue}'";
    }

    // 变化前处理
    partial void OnEmailChanging(string value)
    {
        // 在邮箱变化前进行预处理
        if (!string.IsNullOrEmpty(value))
        {
            Status = "Validating email...";
        }
    }

    #endregion

    #region 辅助方法

    private bool CanSave()
    {
        return !IsLoading &&
               !string.IsNullOrWhiteSpace(FirstName) &&
               !string.IsNullOrWhiteSpace(LastName) &&
               !HasErrors;
    }

    private bool CanDelete()
    {
        return IsSelected && !IsLoading;
    }

    #endregion
}
```

---

## 🚀 7. 8.4.0 迁移指南

### **自动迁移工具**

8.4.0 提供了强大的代码修复器，可以一键迁移：

1. **启用 C# Preview**：
```xml
<PropertyGroup>
    <LangVersion>preview</LangVersion>
</PropertyGroup>
```

2. **使用代码修复器**：
   - 在 Visual Studio 中，鼠标悬停在 MVVMTK0042 警告上
   - 点击 "💡" 图标
   - 选择 "Convert to partial property"
   - 可选择修复单个或整个解决方案

### **迁移前后对比**

#### **迁移前（Field-based）：**
```csharp
[ObservableProperty]
[NotifyPropertyChangedFor(nameof(FullName))]
private string firstName = string.Empty;

partial void OnFirstNameChanged(string value)
{
    OnPropertyChanged(nameof(FullName));
}
```

#### **迁移后（Partial Properties）：**
```csharp
[ObservableProperty]
[NotifyPropertyChangedFor(nameof(FullName))]
public partial string FirstName { get; set; } = string.Empty;

partial void OnFirstNameChanged(string value)
{
    OnPropertyChanged(nameof(FullName));
}
```

### 🎯 迁移实战示例

#### **示例1：完整的 ViewModel 迁移**

**迁移前（Field-based 写法）：**
```csharp
public partial class ProductViewModel : ObservableValidator
{
    [ObservableProperty]
    [NotifyPropertyChangedFor(nameof(DisplayName))]
    [NotifyCanExecuteChangedFor(nameof(SaveCommand))]
    private string name = string.Empty;

    [ObservableProperty]
    [NotifyPropertyChangedFor(nameof(DisplayName))]
    private string category = string.Empty;

    [ObservableProperty]
    [NotifyCanExecuteChangedFor(nameof(SaveCommand))]
    private decimal price;

    [ObservableProperty]
    private bool isAvailable = true;

    public string DisplayName => $"{Name} ({Category}) - ${Price:F2}";

    [RelayCommand(CanExecute = nameof(CanSave))]
    private async Task SaveAsync()
    {
        // 保存逻辑
    }

    private bool CanSave() => !string.IsNullOrEmpty(Name) && Price > 0;

    partial void OnNameChanged(string value)
    {
        OnPropertyChanged(nameof(DisplayName));
    }

    partial void OnCategoryChanged(string value)
    {
        OnPropertyChanged(nameof(DisplayName));
    }
}
```

**迁移后（8.4.0 Partial Properties）：**
```csharp
public partial class ProductViewModel : ObservableValidator
{
    [ObservableProperty]
    [NotifyPropertyChangedFor(nameof(DisplayName))]
    [NotifyCanExecuteChangedFor(nameof(SaveCommand))]
    [property: JsonPropertyName("product_name")]           // 🆕 可以添加序列化控制
    [property: Required(ErrorMessage = "产品名称不能为空")]   // 🆕 可以添加验证
    public partial string Name { get; set; } = string.Empty;

    [ObservableProperty]
    [NotifyPropertyChangedFor(nameof(DisplayName))]
    [property: JsonPropertyName("category")]
    [property: StringLength(50)]                           // 🆕 可以添加长度限制
    public partial string Category { get; set; } = string.Empty;

    [ObservableProperty]
    [NotifyCanExecuteChangedFor(nameof(SaveCommand))]
    [property: JsonPropertyName("price")]
    [property: Range(0.01, 999999.99)]                     // 🆕 可以添加范围验证
    public partial decimal Price { get; set; }

    [ObservableProperty]
    [property: JsonPropertyName("is_available")]
    public partial bool IsAvailable { get; set; } = true;

    // 🆕 可以添加只读属性
    [ObservableProperty]
    [property: JsonPropertyName("created_at")]
    [property: get: JsonConverter(typeof(DateTimeConverter))]
    public partial DateTime CreatedAt { get; private set; } = DateTime.Now;

    public string DisplayName => $"{Name} ({Category}) - ${Price:F2}";

    [RelayCommand(CanExecute = nameof(CanSave))]
    [property: Description("保存产品信息")]                  // 🆕 可以添加命令描述
    private async Task SaveAsync()
    {
        ValidateAllProperties();  // 🆕 利用新的验证功能
        if (!HasErrors)
        {
            // 保存逻辑
        }
    }

    private bool CanSave() => !string.IsNullOrEmpty(Name) && Price > 0;

    // partial void 方法保持不变
    partial void OnNameChanged(string value)
    {
        OnPropertyChanged(nameof(DisplayName));
    }

    partial void OnCategoryChanged(string value)
    {
        OnPropertyChanged(nameof(DisplayName));
    }

    // 🆕 可以添加新的变化处理
    partial void OnPriceChanged(decimal oldValue, decimal newValue)
    {
        OnPropertyChanged(nameof(DisplayName));

        // 价格变化时的业务逻辑
        if (newValue > oldValue * 1.5m)
        {
            // 价格大幅上涨时的处理
        }
    }
}
```

#### **示例2：复杂属性的迁移**

**迁移前：**
```csharp
[ObservableProperty]
[NotifyPropertyChangedFor(nameof(IsValid))]
private string email = string.Empty;

[ObservableProperty]
private ObservableCollection<string> tags = new();

[ObservableProperty]
private UserSettings settings = new();
```

**迁移后：**
```csharp
[ObservableProperty]
[NotifyPropertyChangedFor(nameof(IsValid))]
[property: JsonPropertyName("email_address")]
[property: EmailAddress(ErrorMessage = "请输入有效的邮箱")]
[property: get: JsonRequired]                              // 🆕 序列化时必需
[property: set: JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]  // 🆕 条件忽略
public partial string Email { get; set; } = string.Empty;

[ObservableProperty]
[property: JsonPropertyName("tags")]
[property: JsonConverter(typeof(StringArrayConverter))]    // 🆕 自定义集合转换器
[field: NonSerialized]                                     // 🆕 字段不序列化（但属性会）
public partial ObservableCollection<string> Tags { get; set; } = new();

[ObservableProperty]
[property: JsonPropertyName("user_settings")]
[property: get: JsonInclude]                               // 🆕 强制包含
[property: set: JsonPropertyOrder(1)]                      // 🆕 序列化顺序
public partial UserSettings Settings { get; set; } = new();
```

#### **示例3：命令的增强**

**迁移前：**
```csharp
[RelayCommand(CanExecute = nameof(CanExecuteOperation))]
private async Task ExecuteOperationAsync()
{
    // 操作逻辑
}
```

**迁移后：**
```csharp
[RelayCommand(
    CanExecute = nameof(CanExecuteOperation),
    IncludeCancelCommand = true,                           // 🆕 包含取消命令
    FlowExceptionsToTaskScheduler = true                   // 🆕 异常处理
)]
[property: Description("执行主要操作")]                     // 🆕 命令描述
[property: Category("主要操作")]                           // 🆕 命令分类
[property: get: ToolTip("点击执行操作，可以取消")]          // 🆕 工具提示
private async Task ExecuteOperationAsync(CancellationToken cancellationToken)
{
    try
    {
        // 支持取消的操作逻辑
        await SomeOperationAsync(cancellationToken);
    }
    catch (OperationCanceledException)
    {
        // 取消处理
    }
}
```

---

## 🔧 8. 高级配置和技巧

### **项目配置**
```xml
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <LangVersion>preview</LangVersion>
    <UseWinUI>true</UseWinUI>
    <EnableMsixTooling>true</EnableMsixTooling>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
  </ItemGroup>
</Project>
```

### **调试 Source Generator**
```csharp
// 查看生成的代码位置：
// obj/Debug/net9.0/generated/CommunityToolkit.Mvvm.SourceGenerators/
```

### **性能优化技巧**
```csharp
// ✅ 推荐：使用 Partial Properties
[ObservableProperty]
public partial string Name { get; set; } = string.Empty;

// ✅ 推荐：合理使用通知
[ObservableProperty]
[NotifyPropertyChangedFor(nameof(FullName))]
public partial string FirstName { get; set; } = string.Empty;

// ❌ 避免：过度使用通知
[ObservableProperty]
[NotifyPropertyChangedFor(nameof(Prop1), nameof(Prop2), nameof(Prop3),
                         nameof(Prop4), nameof(Prop5))] // 太多了
public partial string BaseValue { get; set; } = string.Empty;
```

---

## 🎯 9. 常见问题和解决方案

### **问题1：partial class 要求**
```csharp
// ❌ 错误：缺少 partial 关键字
public class MyViewModel : ObservableObject

// ✅ 正确：必须是 partial class
public partial class MyViewModel : ObservableObject
```

### **问题2：C# 版本要求**
```csharp
// ❌ 错误：MVVMTK0041
// 解决方案：添加到 .csproj
<LangVersion>preview</LangVersion>
```

### **问题3：AOT 兼容性**
```csharp
// ❌ 警告：MVVMTK0045 - Field 方式不支持 AOT
[ObservableProperty]
private string name;

// ✅ 解决：使用 Partial Properties
[ObservableProperty]
public partial string Name { get; set; }
```

### **问题4：访问修饰符**
```csharp
// ✅ 支持各种访问修饰符
[ObservableProperty]
public partial string PublicProperty { get; set; }

[ObservableProperty]
public partial string ReadOnlyProperty { get; private set; }

[ObservableProperty]
internal partial string InternalProperty { get; set; }

[ObservableProperty]
protected partial string ProtectedProperty { get; set; }
```

---

## 📚 10. 参考资源

### **官方文档**
- [CommunityToolkit.Mvvm 8.4.0 发布说明](https://devblogs.microsoft.com/dotnet/announcing-the-dotnet-community-toolkit-840/)
- [Partial Properties 官方文档](https://learn.microsoft.com/en-us/dotnet/communitytoolkit/mvvm/generators/observableproperty)
- [所有分析器列表](https://learn.microsoft.com/en-us/dotnet/communitytoolkit/mvvm/analyzers/)

### **示例项目**
- [官方示例应用](https://aka.ms/mvvmtoolkit/samples)
- [8.4.0 新特性示例](https://github.com/CommunityToolkit/dotnet/tree/main/samples)

### **社区资源**
- [GitHub 仓库](https://github.com/CommunityToolkit/dotnet)
- [NuGet 包](https://www.nuget.org/packages/CommunityToolkit.Mvvm/8.4.0)
- [讨论区](https://github.com/CommunityToolkit/dotnet/discussions)

---

## 🎉 总结

CommunityToolkit.Mvvm 8.4.0 带来了革命性的 **Partial Properties** 支持：

### **🆕 8.4.0 核心特性**
- ✅ **Partial Properties 支持**：现代化的属性声明方式
- ✅ **完全 AOT 兼容**：特别是 UWP 和 WinUI 3 应用
- ✅ **15+ 新分析器**：更好的代码质量保证
- ✅ **自动迁移工具**：一键从 Field 迁移到 Partial Properties
- ✅ **访问器标签支持**：更灵活的属性配置

### **🎯 最佳实践**
- **使用 Partial Properties**：获得最佳性能和 AOT 兼容性
- **启用 C# Preview**：体验最新语言特性
- **遵循分析器建议**：保证代码质量
- **合理组织代码**：使用 `#region` 保持清晰结构

### **🚀 升级建议**
1. **立即升级到 8.4.0**：享受最新特性和性能改进
2. **启用 C# Preview**：使用 Partial Properties
3. **运行代码修复器**：自动迁移现有代码
4. **解决 AOT 警告**：确保应用兼容性

**CommunityToolkit.Mvvm 8.4.0 是现代 .NET MVVM 开发的里程碑版本！** 🎯✨

---

*文档创建时间：2025年1月*
*基于 CommunityToolkit.Mvvm 8.4.0 版本*
*支持 .NET 9 和 C# Preview 特性*
