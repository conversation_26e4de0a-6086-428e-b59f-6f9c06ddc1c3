<!-- Expander 基础用法示例 -->
<StackPanel Margin="20" Spacing="15">

    <!-- 标准 Expander -->
    <GroupBox Header="标准 Expander" Padding="15">
        <StackPanel Spacing="12">
            <Expander Header="标准 Expander" 
                      IsExpanded="False"
                      Background="{DynamicResource ControlFillColorDefaultBrush}"
                      BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                      BorderThickness="1">
                <StackPanel Padding="16">
                    <TextBlock Text="这是 Expander 的内容区域。" Margin="0,0,0,8"/>
                    <TextBlock Text="可以包含任何 WPF 控件和布局。" Margin="0,0,0,8"/>
                    <Button Content="示例按钮" HorizontalAlignment="Left"/>
                </StackPanel>
            </Expander>
            
            <Expander Header="默认展开 Expander" 
                      IsExpanded="True"
                      Background="{DynamicResource ControlFillColorSecondaryBrush}"
                      BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                      BorderThickness="1">
                <StackPanel Padding="16">
                    <TextBlock Text="这个 Expander 默认是展开状态。" Margin="0,0,0,8"/>
                    <CheckBox Content="示例复选框" Margin="0,0,0,8"/>
                    <Slider Minimum="0" Maximum="100" Value="50"/>
                </StackPanel>
            </Expander>
        </StackPanel>
    </GroupBox>

    <!-- 带图标的 Expander -->
    <GroupBox Header="带图标的 Expander" Padding="15">
        <Expander IsExpanded="False"
                  Background="{DynamicResource AccentFillColorDefaultBrush}"
                  BorderBrush="{DynamicResource AccentStrokeColorDefaultBrush}"
                  BorderThickness="1">
            <Expander.Header>
                <StackPanel Orientation="Horizontal">
                    <ui:SymbolIcon Symbol="Settings24" 
                                   FontSize="16" 
                                   Foreground="White"
                                   Margin="0,0,8,0"/>
                    <TextBlock Text="带图标的 Expander" 
                               Foreground="White"
                               VerticalAlignment="Center"/>
                </StackPanel>
            </Expander.Header>
            <StackPanel Padding="16">
                <TextBlock Text="这个 Expander 的标题包含图标。" 
                           Foreground="White" 
                           Margin="0,0,0,8"/>
                <ProgressBar Value="75" Height="20" Margin="0,0,0,8"/>
                <TextBlock Text="进度: 75%" 
                           Foreground="White" 
                           FontSize="12"/>
            </StackPanel>
        </Expander>
    </GroupBox>

    <!-- 不同展开方向 -->
    <GroupBox Header="不同展开方向" Padding="15">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- 向下展开（默认） -->
            <Expander Grid.Column="0" 
                      Header="向下展开" 
                      ExpandDirection="Down"
                      IsExpanded="False"
                      Margin="0,0,8,0">
                <StackPanel Padding="12">
                    <TextBlock Text="向下展开的内容"/>
                    <Button Content="按钮" HorizontalAlignment="Left"/>
                </StackPanel>
            </Expander>
            
            <!-- 向上展开 -->
            <Expander Grid.Column="1" 
                      Header="向上展开" 
                      ExpandDirection="Up"
                      IsExpanded="False"
                      Margin="8,0,0,0">
                <StackPanel Padding="12">
                    <TextBlock Text="向上展开的内容"/>
                    <Button Content="按钮" HorizontalAlignment="Left"/>
                </StackPanel>
            </Expander>
        </Grid>
    </GroupBox>

    <!-- 禁用状态 -->
    <GroupBox Header="禁用状态" Padding="15">
        <StackPanel Spacing="12">
            <Expander Header="正常状态 Expander" 
                      IsExpanded="False"
                      IsEnabled="True">
                <StackPanel Padding="16">
                    <TextBlock Text="这是正常状态的 Expander。"/>
                    <Button Content="可点击按钮" HorizontalAlignment="Left"/>
                </StackPanel>
            </Expander>
            
            <Expander Header="禁用状态 Expander" 
                      IsExpanded="False"
                      IsEnabled="False">
                <StackPanel Padding="16">
                    <TextBlock Text="这是禁用状态的 Expander。"/>
                    <Button Content="不可点击按钮" HorizontalAlignment="Left"/>
                </StackPanel>
            </Expander>
        </StackPanel>
    </GroupBox>

</StackPanel>
