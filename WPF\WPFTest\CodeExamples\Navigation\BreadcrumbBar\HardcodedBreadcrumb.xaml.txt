<!-- 硬编码面包屑导航示例 -->
<Grid>
    <Grid.RowDefinitions>
        <RowDefinition Height="Auto"/>
        <RowDefinition Height="*"/>
    </Grid.RowDefinitions>

    <!-- 硬编码面包屑导航 -->
    <Border Grid.Row="0"
            Background="{DynamicResource LayerFillColorDefaultBrush}"
            BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
            BorderThickness="0,0,0,1"
            Padding="20,10">
        
        <StackPanel Orientation="Horizontal">
            <!-- 首页 -->
            <ui:Button Content="🏠 首页"
                      Background="Transparent"
                      BorderThickness="0"
                      Padding="8,4"
                      FontSize="14"
                      Command="{Binding NavigateToHomeCommand}"/>
            
            <!-- 分隔符 -->
            <zylo:ZyloIcon Icon="ChevronRight"
                          FontSize="12"
                          Margin="8,0"
                          VerticalAlignment="Center"
                          Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
            
            <!-- 控件示例 -->
            <ui:Button Content="📦 控件示例"
                      Background="Transparent"
                      BorderThickness="0"
                      Padding="8,4"
                      FontSize="14"
                      Command="{Binding NavigateToControlsCommand}"/>
            
            <!-- 分隔符 -->
            <zylo:ZyloIcon Icon="ChevronRight"
                          FontSize="12"
                          Margin="8,0"
                          VerticalAlignment="Center"
                          Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
            
            <!-- 导航控件 -->
            <ui:Button Content="🧭 导航控件"
                      Background="Transparent"
                      BorderThickness="0"
                      Padding="8,4"
                      FontSize="14"
                      Command="{Binding NavigateToNavigationCommand}"/>
            
            <!-- 分隔符 -->
            <zylo:ZyloIcon Icon="ChevronRight"
                          FontSize="12"
                          Margin="8,0"
                          VerticalAlignment="Center"
                          Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
            
            <!-- 当前页面 -->
            <TextBlock Text="📋 面包屑导航"
                      FontSize="14"
                      FontWeight="SemiBold"
                      VerticalAlignment="Center"
                      Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"/>
        </StackPanel>
    </Border>

    <!-- 主要内容区域 -->
    <ui:Card Grid.Row="1" Margin="20" Padding="20">
        <ScrollViewer>
            <StackPanel>
                <TextBlock Text="硬编码面包屑导航示例"
                          FontSize="18"
                          FontWeight="SemiBold"
                          Margin="0,0,0,15"
                          Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>

                <TextBlock TextWrapping="Wrap" Margin="0,0,0,15"
                          Foreground="{DynamicResource TextFillColorSecondaryBrush}">
                    这个示例展示了硬编码面包屑导航的实现方式：
                    <LineBreak/>• 固定的导航路径结构
                    <LineBreak/>• 简单的按钮点击导航
                    <LineBreak/>• 静态的分隔符显示
                    <LineBreak/>• 当前页面高亮显示
                    <LineBreak/>• 适合简单的固定导航结构
                </TextBlock>

                <!-- 特点说明 -->
                <Border Background="{DynamicResource ControlFillColorSecondaryBrush}"
                       CornerRadius="4"
                       Padding="15"
                       Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="硬编码方式的特点"
                                  FontWeight="SemiBold"
                                  Margin="0,0,0,10"/>
                        
                        <StackPanel>
                            <TextBlock Text="✅ 优点：" FontWeight="SemiBold" Foreground="Green" Margin="0,0,0,5"/>
                            <TextBlock Text="• 实现简单，代码直观" Margin="15,0,0,3"/>
                            <TextBlock Text="• 性能好，无数据绑定开销" Margin="15,0,0,3"/>
                            <TextBlock Text="• 适合固定不变的导航结构" Margin="15,0,0,10"/>
                            
                            <TextBlock Text="❌ 缺点：" FontWeight="SemiBold" Foreground="Red" Margin="0,0,0,5"/>
                            <TextBlock Text="• 不够灵活，难以动态调整" Margin="15,0,0,3"/>
                            <TextBlock Text="• 代码重复，维护成本高" Margin="15,0,0,3"/>
                            <TextBlock Text="• 不适合复杂的层级结构" Margin="15,0,0,3"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- 操作按钮 -->
                <StackPanel Orientation="Horizontal" Margin="0,20,0,0">
                    <ui:Button Content="🏠 返回首页"
                              Command="{Binding NavigateToHomeCommand}"
                              Appearance="Primary"
                              Margin="0,0,10,0"/>
                    
                    <ui:Button Content="📦 控件示例"
                              Command="{Binding NavigateToControlsCommand}"
                              Appearance="Secondary"
                              Margin="0,0,10,0"/>
                    
                    <ui:Button Content="🧭 导航控件"
                              Command="{Binding NavigateToNavigationCommand}"
                              Appearance="Secondary"/>
                </StackPanel>

                <!-- 代码示例说明 -->
                <Expander Header="实现说明" Margin="0,20,0,0" IsExpanded="False">
                    <StackPanel Margin="0,10,0,0">
                        <TextBlock Text="硬编码实现要点："
                                  FontWeight="SemiBold"
                                  Margin="0,0,0,10"/>
                        
                        <TextBlock TextWrapping="Wrap" Margin="0,0,0,10">
                            1. 使用 StackPanel 水平排列面包屑项
                            <LineBreak/>2. 每个导航项使用 Button 控件
                            <LineBreak/>3. 分隔符使用 ZyloIcon 显示箭头
                            <LineBreak/>4. 当前页面使用 TextBlock 高亮显示
                            <LineBreak/>5. 每个按钮绑定对应的导航命令
                        </TextBlock>
                        
                        <TextBlock Text="适用场景："
                                  FontWeight="SemiBold"
                                  Margin="0,0,0,5"/>
                        
                        <TextBlock TextWrapping="Wrap">
                            • 导航结构固定不变的应用
                            <LineBreak/>• 层级较浅的简单导航
                            <LineBreak/>• 对性能要求较高的场景
                            <LineBreak/>• 快速原型开发
                        </TextBlock>
                    </StackPanel>
                </Expander>
            </StackPanel>
        </ScrollViewer>
    </ui:Card>
</Grid>
