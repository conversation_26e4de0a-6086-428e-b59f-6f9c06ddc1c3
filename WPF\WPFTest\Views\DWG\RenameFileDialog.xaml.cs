using System.IO;
using System.Windows;
using System.Windows.Input;
using Wpf.Ui.Controls;

namespace WPFTest.Views.DWG;

/// <summary>
/// 重命名文件对话框 - 使用WPF-UI美观界面
/// </summary>
/// <remarks>
/// 🎨 特点：
/// - 现代化的WPF-UI界面设计
/// - 支持键盘快捷键（Enter确认，Escape取消）
/// - 自动文件名验证
/// - 清除按钮和占位符文本
/// - 图标和视觉提示
/// </remarks>
public partial class RenameFileDialog : FluentWindow
{
    /// <summary>
    /// 新文件名属性
    /// </summary>
    public string? NewFileName { get; private set; }

    /// <summary>
    /// 原始文件名
    /// </summary>
    private readonly string _originalFileName;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="currentFileName">当前文件名</param>
    public RenameFileDialog(string currentFileName)
    {
        InitializeComponent();
        
        _originalFileName = currentFileName;
        
        // 设置初始值为完整文件名
        FileNameTextBox.Text = currentFileName;

        // 全选文件名，方便用户直接输入新名称
        FileNameTextBox.SelectAll();
        
        // 设置焦点
        Loaded += (s, e) => FileNameTextBox.Focus();
        
        // 绑定命令
        ConfirmCommand = new RelayCommand(ConfirmRename);
        CancelCommand = new RelayCommand(CancelRename);
    }

    /// <summary>
    /// 确认命令
    /// </summary>
    public ICommand ConfirmCommand { get; }

    /// <summary>
    /// 取消命令
    /// </summary>
    public ICommand CancelCommand { get; }

    /// <summary>
    /// 确认按钮点击事件
    /// </summary>
    private void ConfirmButton_Click(object sender, RoutedEventArgs e)
    {
        ConfirmRename();
    }

    /// <summary>
    /// 取消按钮点击事件
    /// </summary>
    private void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        CancelRename();
    }

    /// <summary>
    /// 确认重命名
    /// </summary>
    private void ConfirmRename()
    {
        var newName = FileNameTextBox.Text?.Trim();
        
        // 验证输入
        if (string.IsNullOrEmpty(newName))
        {
            ShowValidationError("文件名不能为空！");
            return;
        }

        if (newName == _originalFileName)
        {
            // 文件名没有改变，直接取消
            CancelRename();
            return;
        }

        // 验证文件名是否包含非法字符
        var invalidChars = Path.GetInvalidFileNameChars();
        if (newName.IndexOfAny(invalidChars) >= 0)
        {
            ShowValidationError("文件名包含非法字符！");
            return;
        }

        // 简单处理：检查是否有.dwg后缀，有就去除，最后统一加上
        if (newName.EndsWith(".dwg", StringComparison.OrdinalIgnoreCase))
        {
            newName = newName.Substring(0, newName.Length - 4);
        }
        newName += ".dwg";

        NewFileName = newName;
        DialogResult = true;
        Close();
    }

    /// <summary>
    /// 取消重命名
    /// </summary>
    private void CancelRename()
    {
        NewFileName = null;
        DialogResult = false;
        Close();
    }

    /// <summary>
    /// 显示验证错误
    /// </summary>
    /// <param name="message">错误消息</param>
    private async void ShowValidationError(string message)
    {
        var messageBox = new Wpf.Ui.Controls.MessageBox
        {
            Title = "输入错误",
            Content = message,
            PrimaryButtonText = "确定",
            Owner = this,
            WindowStartupLocation = WindowStartupLocation.CenterOwner
        };

        await messageBox.ShowDialogAsync();
        
        // 重新聚焦到输入框
        FileNameTextBox.Focus();
        FileNameTextBox.SelectAll();
    }
}

/// <summary>
/// 简单的RelayCommand实现
/// </summary>
public class RelayCommand : ICommand
{
    private readonly Action _execute;
    private readonly Func<bool>? _canExecute;

    public RelayCommand(Action execute, Func<bool>? canExecute = null)
    {
        _execute = execute ?? throw new ArgumentNullException(nameof(execute));
        _canExecute = canExecute;
    }

    public event EventHandler? CanExecuteChanged
    {
        add { CommandManager.RequerySuggested += value; }
        remove { CommandManager.RequerySuggested -= value; }
    }

    public bool CanExecute(object? parameter)
    {
        return _canExecute?.Invoke() ?? true;
    }

    public void Execute(object? parameter)
    {
        _execute();
    }
}
