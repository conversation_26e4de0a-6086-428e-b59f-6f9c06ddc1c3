using System.Collections.ObjectModel;
using WPFTest.Models.DragDrop;
using DwgFileModel = WPFTest.Models.DWG.DwgFileModel;
using ProfessionTabModel = WPFTest.Models.DragDrop.ProfessionTabModel;

namespace WPFTest.Services.DragDrop;

/// <summary>
/// DWG文件服务接口 - 提供DWG文件管理的核心业务逻辑
/// </summary>
/// <remarks>
/// 职责：
/// - DWG文件的加载、保存、删除
/// - 文件分类和类型分析
/// - 专业文件夹管理
/// - 文件搜索和过滤
/// </remarks>
public interface IDwgFileService
{
    #region 文件加载和管理

    /// <summary>
    /// 加载指定目录下的所有DWG文件
    /// </summary>
    /// <param name="directory">DWG文件根目录</param>
    /// <returns>加载的文件列表</returns>
    Task<IList<DwgFileModel>> LoadDwgFilesAsync(string directory);

    /// <summary>
    /// 获取指定专业的文件列表
    /// </summary>
    /// <param name="profession">专业名称</param>
    /// <returns>该专业的文件列表</returns>
    IList<DwgFileModel> GetFilesByProfession(string profession);

    /// <summary>
    /// 获取指定类型的文件列表
    /// </summary>
    /// <param name="fileType">文件类型</param>
    /// <returns>该类型的文件列表</returns>
    IList<DwgFileModel> GetFilesByType(DwgFileType fileType);

    /// <summary>
    /// 搜索文件
    /// </summary>
    /// <param name="keyword">搜索关键词</param>
    /// <param name="files">要搜索的文件列表</param>
    /// <returns>匹配的文件列表</returns>
    IList<DwgFileModel> SearchFiles(string keyword, IList<DwgFileModel> files);

    #endregion

    #region 专业管理

    /// <summary>
    /// 扫描目录获取专业列表
    /// </summary>
    /// <param name="directory">DWG文件根目录</param>
    /// <returns>专业列表</returns>
    IList<ProfessionTabModel> ScanProfessions(string directory);

    /// <summary>
    /// 创建专业文件夹
    /// </summary>
    /// <param name="baseDirectory">基础目录</param>
    /// <param name="professionName">专业名称</param>
    /// <returns>是否创建成功</returns>
    bool CreateProfessionFolder(string baseDirectory, string professionName);

    #endregion

    #region 文件类型分析

    /// <summary>
    /// 分析文件名获取文件类型
    /// </summary>
    /// <param name="fileName">文件名（不含扩展名）</param>
    /// <returns>文件类型</returns>
    DwgFileType AnalyzeFileType(string fileName);

    /// <summary>
    /// 获取文件类型的前缀
    /// </summary>
    /// <param name="fileType">文件类型</param>
    /// <returns>前缀字符串</returns>
    string GetFileTypePrefix(DwgFileType fileType);

    /// <summary>
    /// 获取文件类型的图标
    /// </summary>
    /// <param name="fileType">文件类型</param>
    /// <returns>图标字符串</returns>
    string GetFileTypeIcon(DwgFileType fileType);

    #endregion

    #region 文件操作

    /// <summary>
    /// 重命名文件（改变文件类型）
    /// </summary>
    /// <param name="file">要重命名的文件</param>
    /// <param name="newType">新的文件类型</param>
    /// <returns>是否重命名成功</returns>
    Task<bool> RenameFileAsync(DwgFileModel file, DwgFileType newType);

    /// <summary>
    /// 移动文件到指定专业文件夹
    /// </summary>
    /// <param name="file">要移动的文件</param>
    /// <param name="targetProfession">目标专业</param>
    /// <returns>是否移动成功</returns>
    Task<bool> MoveFileAsync(DwgFileModel file, string targetProfession);

    /// <summary>
    /// 删除文件
    /// </summary>
    /// <param name="file">要删除的文件</param>
    /// <returns>是否删除成功</returns>
    Task<bool> DeleteFileAsync(DwgFileModel file);

    #endregion

    #region 示例文件创建

    /// <summary>
    /// 创建示例文件和文件夹结构
    /// </summary>
    /// <param name="baseDirectory">基础目录</param>
    /// <returns>是否创建成功</returns>
    Task<bool> CreateSampleFilesAsync(string baseDirectory);

    #endregion

    #region 事件

    /// <summary>
    /// 文件加载完成事件
    /// </summary>
    event EventHandler<FilesLoadedEventArgs>? FilesLoaded;

    /// <summary>
    /// 文件操作完成事件
    /// </summary>
    event EventHandler<FileOperationEventArgs>? FileOperationCompleted;

    #endregion
}

/// <summary>
/// 文件加载完成事件参数
/// </summary>
public class FilesLoadedEventArgs : EventArgs
{
    /// <summary>
    /// 加载的文件列表
    /// </summary>
    public IList<DwgFileModel> Files { get; }

    /// <summary>
    /// 加载的专业列表
    /// </summary>
    public IList<ProfessionTabModel> Professions { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    public FilesLoadedEventArgs(IList<DwgFileModel> files, IList<ProfessionTabModel> professions)
    {
        Files = files;
        Professions = professions;
    }
}

/// <summary>
/// 文件操作完成事件参数
/// </summary>
public class FileOperationEventArgs : EventArgs
{
    /// <summary>
    /// 操作类型
    /// </summary>
    public FileOperationType OperationType { get; }

    /// <summary>
    /// 操作的文件
    /// </summary>
    public DwgFileModel File { get; }

    /// <summary>
    /// 操作是否成功
    /// </summary>
    public bool Success { get; }

    /// <summary>
    /// 错误消息（如果操作失败）
    /// </summary>
    public string? ErrorMessage { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    public FileOperationEventArgs(FileOperationType operationType, DwgFileModel file, bool success, string? errorMessage = null)
    {
        OperationType = operationType;
        File = file;
        Success = success;
        ErrorMessage = errorMessage;
    }
}

/// <summary>
/// 文件操作类型枚举
/// </summary>
public enum FileOperationType
{
    /// <summary>重命名</summary>
    Rename,
    /// <summary>移动</summary>
    Move,
    /// <summary>删除</summary>
    Delete,
    /// <summary>创建</summary>
    Create
}
