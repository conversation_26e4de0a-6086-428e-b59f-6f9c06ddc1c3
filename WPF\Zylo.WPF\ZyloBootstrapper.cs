using System;
using System.Windows;
using Prism.DryIoc;
using Prism.Ioc;
using Prism.Modularity;
using Prism.Regions;
using CommunityToolkit.Mvvm.Messaging;
using Zylo.WPF.Services;
using Zylo.WPF.YPrism;

namespace Zylo.WPF;

/// <summary>
/// 🚀 Zylo.WPF 通用 Bootstrapper
/// </summary>
/// <remarks>
/// 这个 Bootstrapper 提供了完整的 Prism 功能，并自动注册 CommunityToolkit.Mvvm 服务。
/// 用户只需要继承这个类并实现必要的方法即可。
/// </remarks>
public abstract class ZyloBootstrapper : PrismBootstrapper
{
    /// <summary>
    /// 获取容器实例
    /// </summary>
    public static IContainerProvider? CurrentContainer { get; private set; }

    #region 核心生命周期方法

    /// <summary>
    /// 创建应用程序的主窗口 (Shell)
    /// </summary>
    /// <returns>主窗口实例</returns>
    protected abstract override DependencyObject CreateShell();

    /// <summary>
    /// 注册应用程序特定的类型
    /// </summary>
    /// <param name="containerRegistry">容器注册器</param>
    protected abstract override void RegisterTypes(IContainerRegistry containerRegistry);

    #endregion

    #region 自动注册 CommunityToolkit.Mvvm 服务

    /// <summary>
    /// 注册 Prism 框架必需的类型（增强版）
    /// </summary>
    /// <param name="containerRegistry">容器注册器</param>
    protected override void RegisterRequiredTypes(IContainerRegistry containerRegistry)
    {
        // 注册 Prism 核心服务
        base.RegisterRequiredTypes(containerRegistry);
        
        // 🚀 自动注册 CommunityToolkit.Mvvm 服务
        RegisterCommunityToolkitServices(containerRegistry);

        // 🔧 注册额外的 CommunityToolkit.Mvvm 服务（子类可重写）
        RegisterAdditionalCommunityToolkitServices(containerRegistry);

        // 保存容器引用
        CurrentContainer = Container;
    }

    /// <summary>
    /// 自动注册 CommunityToolkit.Mvvm 服务
    /// </summary>
    /// <param name="containerRegistry">容器注册器</param>
    private void RegisterCommunityToolkitServices(IContainerRegistry containerRegistry)
    {
        // 🔔 消息传递服务
        containerRegistry.RegisterSingleton<IMessenger, WeakReferenceMessenger>();

        // 🎯 注册UI相关服务
        containerRegistry.RegisterSingleton<Zylo.WPF.Services.ISnackbarService, Zylo.WPF.Services.SnackbarService>(); // 传统Snackbar服务（兼容性）
        containerRegistry.RegisterSingleton<Zylo.WPF.Services.IWindowSnackbarService, Zylo.WPF.Services.WindowSnackbarService>(); // 多窗口安全Snackbar服务
        
        
        
        // 🎯 也可以注册强引用消息器（根据需要选择）
        // containerRegistry.RegisterSingleton<IMessenger, StrongReferenceMessenger>();

        // 📝 验证服务（如果需要）
        // containerRegistry.RegisterSingleton<IValidator, DataAnnotationsValidator>();

        // 🔧 其他 CommunityToolkit.Mvvm 相关服务
        // 注意：RelayCommand 和 AsyncRelayCommand 通常不需要注册到容器
        // 因为它们是通过 [RelayCommand] 源生成器自动创建的

        // 💾 设置服务（如果使用）
        // containerRegistry.RegisterSingleton<ISettingsService, SettingsService>();

        // 🌐 HTTP 客户端服务（如果使用）
        // containerRegistry.RegisterSingleton<IHttpClientService, HttpClientService>();

        // 📱 平台特定服务
        // containerRegistry.RegisterSingleton<IDispatcherService, DispatcherService>();
        // containerRegistry.RegisterSingleton<IFileService, FileService>();
        // containerRegistry.RegisterSingleton<IDialogService, DialogService>();

        // 🎨 UI 服务
        // containerRegistry.RegisterSingleton<IThemeService, ThemeService>();
        // containerRegistry.RegisterSingleton<ILocalizationService, LocalizationService>();
        
        // 🔧 配置服务 - 注释掉，让具体的 ViewModel 自己实现
        // containerRegistry.RegisterSingleton<IConfigureService>();
        
        // 🔧 主题管理服务
        containerRegistry.RegisterSingleton<IThemeManagementService,ThemeManagementService>();
        
    }

    /// <summary>
    /// 注册额外的 CommunityToolkit.Mvvm 服务
    /// </summary>
    /// <param name="containerRegistry">容器注册器</param>
    /// <remarks>
    /// 子类可以重写此方法来注册额外的 CommunityToolkit.Mvvm 相关服务
    /// </remarks>
    protected virtual void RegisterAdditionalCommunityToolkitServices(IContainerRegistry containerRegistry)
    {
        // 子类可以重写此方法来添加更多服务
        // 例如：
        // containerRegistry.RegisterSingleton<ICustomService, CustomService>();
    }

    #endregion

    #region 默认实现（可重写）

    /// <summary>
    /// 配置模块目录
    /// </summary>
    /// <param name="moduleCatalog">模块目录</param>
    protected override void ConfigureModuleCatalog(IModuleCatalog moduleCatalog)
    {
        // 默认空实现，子类可重写
    }

    /// <summary>
    /// 配置区域适配器映射
    /// </summary>
    /// <param name="regionAdapterMappings">区域适配器映射</param>
    protected override void ConfigureRegionAdapterMappings(RegionAdapterMappings regionAdapterMappings)
    {
        base.ConfigureRegionAdapterMappings(regionAdapterMappings);
        // 可以添加自定义区域适配器
    }

    /// <summary>
    /// 配置默认区域行为
    /// </summary>
    /// <param name="regionBehaviors">区域行为工厂</param>
    protected override void ConfigureDefaultRegionBehaviors(IRegionBehaviorFactory regionBehaviors)
    {
        base.ConfigureDefaultRegionBehaviors(regionBehaviors);
        // 可以添加自定义区域行为
    }

    /// <summary>
    /// 初始化 Shell
    /// </summary>
    /// <param name="shell">Shell 实例</param>
    protected override void InitializeShell(DependencyObject shell)
    {
        if (shell is Window window)
        {
            window.Show();
        }
    }

    /// <summary>
    /// 应用程序初始化完成回调
    /// </summary>
    protected override void OnInitialized()
    {
        base.OnInitialized();
        // 可以添加初始化完成后的逻辑
    }

    #endregion
}

/// <summary>
/// 🎯 容器访问助手
/// </summary>
public static class ZyloContainer
{
    /// <summary>
    /// 获取当前容器
    /// </summary>
    public static IContainerProvider Current => ZyloBootstrapper.CurrentContainer 
        ?? throw new InvalidOperationException("容器尚未初始化。请确保已调用 Bootstrapper.Run()。");

    /// <summary>
    /// 解析服务
    /// </summary>
    /// <typeparam name="T">服务类型</typeparam>
    /// <returns>服务实例</returns>
    public static T Resolve<T>() => Current.Resolve<T>();

    /// <summary>
    /// 解析服务
    /// </summary>
    /// <param name="type">服务类型</param>
    /// <returns>服务实例</returns>
    public static object Resolve(Type type) => Current.Resolve(type);

    /// <summary>
    /// 尝试解析服务
    /// </summary>
    /// <typeparam name="T">服务类型</typeparam>
    /// <returns>服务实例或 null</returns>
    public static T? TryResolve<T>() where T : class
    {
        try
        {
            return Current.Resolve<T>();
        }
        catch
        {
            return null;
        }
    }
}
