// 矩形按钮 C# 代码示例
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace WPFTest.ViewModels.ButtonControls
{
    public partial class ButtonPageViewModel : ObservableObject
    {
        /// <summary>
        /// 矩形按钮点击命令
        /// </summary>
        [RelayCommand]
        private void RectangleButtonClick(string parameter)
        {
            var message = parameter switch
            {
                "矩形新建" => "📄 点击了矩形新建按钮",
                "矩形打开" => "📂 点击了矩形打开按钮",
                "矩形保存" => "💾 点击了矩形保存按钮",
                "小型复制" => "📋 点击了小型复制按钮",
                "小型粘贴" => "📋 点击了小型粘贴按钮",
                "大型开始" => "▶️ 点击了大型开始按钮",
                "大型停止" => "⏹️ 点击了大型停止按钮",
                "透明上传" => "⬆️ 点击了透明上传按钮",
                "无边框菜单" => "📋 点击了无边框菜单按钮",
                _ => $"🔲 点击了矩形按钮: {parameter}"
            };
            
            StatusMessage = message;
        }

        /// <summary>
        /// 文件操作命令示例
        /// </summary>
        [RelayCommand]
        private async Task FileOperation(string operation)
        {
            StatusMessage = $"正在执行 {operation} 操作...";
            
            // 模拟异步操作
            await Task.Delay(1000);
            
            StatusMessage = $"{operation} 操作完成！";
        }
    }
}
