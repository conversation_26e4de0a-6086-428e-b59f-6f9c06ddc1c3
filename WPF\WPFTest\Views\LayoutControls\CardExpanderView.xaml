<UserControl x:Class="WPFTest.Views.LayoutControls.CardExpanderView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:codeExample="clr-namespace:Zylo.WPF.Controls.CodeExample;assembly=Zylo.WPF"
             xmlns:mvvm="http://prismlibrary.com/"
             xmlns:layoutControls="clr-namespace:WPFTest.ViewModels.LayoutControls"
             mc:Ignorable="d"
             d:DesignHeight="800" d:DesignWidth="1200"
             mvvm:ViewModelLocator.AutoWireViewModel="True"
             d:DataContext="{d:DesignInstance layoutControls:CardExpanderViewModel}">

    <UserControl.Resources>
        <!-- 🎨 所有样式来自 WPF-UI 官方库 -->
        <!-- CardExpander 是 WPF-UI 的官方控件，结合了 Card 和 Expander 的功能 -->
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" 
                  HorizontalScrollBarVisibility="Disabled"
                  Padding="24">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>    <!-- 标题区域 -->
                <RowDefinition Height="*"/>       <!-- 主要内容 -->
                <RowDefinition Height="Auto"/>    <!-- 底部操作栏 -->
            </Grid.RowDefinitions>

            <!-- 标题区域 -->
            <StackPanel Grid.Row="0" Margin="0,0,0,20">
                <TextBlock Text="📋 CardExpander 控件展示"
                           FontSize="32"
                           FontWeight="Bold"
                           Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                           Margin="0,0,0,8"/>
                <TextBlock Text="展示 WPF-UI 官方的 CardExpander 控件的各种样式和功能" 
                           FontSize="16" 
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                           TextWrapping="Wrap"/>
                
                <!-- 状态显示 -->
                <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                        BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Padding="16,12"
                        Margin="0,16,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <ui:SymbolIcon Symbol="Info24" 
                                       FontSize="20" 
                                       Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                       VerticalAlignment="Center"
                                       Margin="0,0,12,0"/>
                        <TextBlock Grid.Column="1" 
                                   Text="{Binding StatusMessage}" 
                                   FontSize="14"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                   VerticalAlignment="Center"
                                   TextWrapping="Wrap"/>
                    </Grid>
                </Border>
            </StackPanel>

            <!-- 主要内容区域 -->
            <Border Grid.Row="1" Margin="0,0,0,20">
                <StackPanel>

                    <!-- 基础功能展示 -->
                    <ui:CardExpander Header="🎯 基础功能" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 WPF-UI CardExpander 控件的基础功能和用法"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>
                            
                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="基础 CardExpander 示例：" FontWeight="Medium" Margin="0,0,0,16"/>
                                    
                                    <!-- 简单的 CardExpander -->
                                    <ui:CardExpander Header="简单的可展开卡片" 
                                                     IsExpanded="True"
                                                     Margin="0,0,0,16">
                                        <StackPanel Margin="16">
                                            <TextBlock Text="这是一个简单的 CardExpander 示例。" 
                                                       FontWeight="Medium" 
                                                       Margin="0,0,0,8"/>
                                            <TextBlock Text="CardExpander 结合了 Card 的现代化样式和 Expander 的可展开功能。" 
                                                       TextWrapping="Wrap"
                                                       Margin="0,0,0,12"/>
                                            <Button Content="卡片内按钮" 
                                                    Command="{Binding HandleInteractionCommand}"
                                                    CommandParameter="简单CardExpander按钮"
                                                    HorizontalAlignment="Left"/>
                                        </StackPanel>
                                    </ui:CardExpander>

                                    <!-- 带图标的 CardExpander -->
                                    <ui:CardExpander IsExpanded="False" Margin="0,0,0,16">
                                        <ui:CardExpander.Header>
                                            <StackPanel Orientation="Horizontal">
                                                <ui:SymbolIcon Symbol="Settings24" 
                                                               FontSize="20" 
                                                               Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                                               Margin="0,0,12,0"/>
                                                <TextBlock Text="带图标的可展开卡片" 
                                                           FontWeight="Medium" 
                                                           VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </ui:CardExpander.Header>
                                        <StackPanel Margin="16">
                                            <TextBlock Text="这个 CardExpander 在标题中包含了图标。" 
                                                       Margin="0,0,0,8"/>
                                            <TextBlock Text="可以通过自定义 Header 来添加更丰富的标题内容。" 
                                                       TextWrapping="Wrap"
                                                       Margin="0,0,0,12"/>
                                            <StackPanel Orientation="Horizontal">
                                                <Button Content="配置" 
                                                        Command="{Binding HandleInteractionCommand}"
                                                        CommandParameter="配置操作"
                                                        Margin="0,0,8,0"/>
                                                <Button Content="重置" 
                                                        Command="{Binding HandleInteractionCommand}"
                                                        CommandParameter="重置操作"/>
                                            </StackPanel>
                                        </StackPanel>
                                    </ui:CardExpander>

                                    <!-- 信息类型的 CardExpander -->
                                    <ui:CardExpander Header="重要信息" 
                                                     IsExpanded="False"
                                                     Margin="0,0,0,16">
                                        <Grid Margin="16">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <ui:SymbolIcon Grid.Column="0"
                                                           Symbol="Warning24" 
                                                           FontSize="32" 
                                                           Foreground="{DynamicResource SystemFillColorCautionBrush}"
                                                           Margin="0,0,16,0"
                                                           VerticalAlignment="Top"/>
                                            
                                            <StackPanel Grid.Column="1">
                                                <TextBlock Text="系统维护通知" 
                                                           FontWeight="Bold" 
                                                           FontSize="16"
                                                           Margin="0,0,0,8"/>
                                                <TextBlock Text="系统将在今晚 23:00 - 01:00 进行维护升级，期间服务可能会中断。" 
                                                           TextWrapping="Wrap"
                                                           Margin="0,0,0,8"/>
                                                <TextBlock Text="• 请提前保存您的工作" Margin="0,0,0,2"/>
                                                <TextBlock Text="• 维护期间无法访问系统" Margin="0,0,0,2"/>
                                                <TextBlock Text="• 如有紧急情况请联系管理员" Margin="0,0,0,8"/>
                                                <Button Content="我知道了" 
                                                        Command="{Binding HandleInteractionCommand}"
                                                        CommandParameter="确认通知"
                                                        HorizontalAlignment="Left"/>
                                            </StackPanel>
                                        </Grid>
                                    </ui:CardExpander>

                                    <!-- 数据展示的 CardExpander -->
                                    <ui:CardExpander Header="数据统计" 
                                                     IsExpanded="True"
                                                     Margin="0,0,0,16">
                                        <StackPanel Margin="16">
                                            <Grid Margin="0,0,0,16">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>
                                                
                                                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                                    <TextBlock Text="156" 
                                                               FontSize="24" 
                                                               FontWeight="Bold" 
                                                               HorizontalAlignment="Center"
                                                               Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"/>
                                                    <TextBlock Text="总项目" 
                                                               FontSize="12" 
                                                               HorizontalAlignment="Center"
                                                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                                                </StackPanel>
                                                
                                                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                                    <TextBlock Text="89" 
                                                               FontSize="24" 
                                                               FontWeight="Bold" 
                                                               HorizontalAlignment="Center"
                                                               Foreground="{DynamicResource SystemFillColorSuccessBrush}"/>
                                                    <TextBlock Text="已完成" 
                                                               FontSize="12" 
                                                               HorizontalAlignment="Center"
                                                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                                                </StackPanel>
                                                
                                                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                                                    <TextBlock Text="67" 
                                                               FontSize="24" 
                                                               FontWeight="Bold" 
                                                               HorizontalAlignment="Center"
                                                               Foreground="{DynamicResource SystemFillColorCautionBrush}"/>
                                                    <TextBlock Text="进行中" 
                                                               FontSize="12" 
                                                               HorizontalAlignment="Center"
                                                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                                                </StackPanel>
                                            </Grid>
                                            
                                            <ProgressBar Value="57" 
                                                         Height="8" 
                                                         Margin="0,0,0,8"/>
                                            <TextBlock Text="总体完成度: 57%" 
                                                       FontSize="12" 
                                                       HorizontalAlignment="Center"
                                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                                        </StackPanel>
                                    </ui:CardExpander>

                                    <!-- 操作面板的 CardExpander -->
                                    <ui:CardExpander Header="快速操作" 
                                                     IsExpanded="False">
                                        <Grid Margin="16">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                            </Grid.RowDefinitions>
                                            
                                            <Button Grid.Row="0" Grid.Column="0" 
                                                    Content="新建项目" 
                                                    Command="{Binding HandleInteractionCommand}"
                                                    CommandParameter="新建项目"
                                                    Margin="0,0,4,4"/>
                                            <Button Grid.Row="0" Grid.Column="1" 
                                                    Content="导入数据" 
                                                    Command="{Binding HandleInteractionCommand}"
                                                    CommandParameter="导入数据"
                                                    Margin="4,0,0,4"/>
                                            <Button Grid.Row="1" Grid.Column="0" 
                                                    Content="导出报告" 
                                                    Command="{Binding HandleInteractionCommand}"
                                                    CommandParameter="导出报告"
                                                    Margin="0,4,4,4"/>
                                            <Button Grid.Row="1" Grid.Column="1" 
                                                    Content="系统设置" 
                                                    Command="{Binding HandleInteractionCommand}"
                                                    CommandParameter="系统设置"
                                                    Margin="4,4,0,4"/>
                                            <Button Grid.Row="2" Grid.Column="0" 
                                                    Grid.ColumnSpan="2"
                                                    Content="高级选项" 
                                                    Command="{Binding HandleInteractionCommand}"
                                                    CommandParameter="高级选项"
                                                    Margin="0,4,0,0"/>
                                        </Grid>
                                    </ui:CardExpander>
                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="基础功能代码示例"
                                Language="XAML"
                                Description="展示 WPF-UI CardExpander 控件的基础用法"
                                ShowTabs="True"
                                XamlCode="{Binding BasicXamlExample}"
                                CSharpCode="{Binding BasicCSharpExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 高级功能展示 -->
                    <ui:CardExpander Header="⚡ 高级功能" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 WPF-UI CardExpander 控件的高级功能和组合用法"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="高级 CardExpander 示例：" FontWeight="Medium" Margin="0,0,0,16"/>

                                    <!-- 嵌套的 CardExpander -->
                                    <ui:CardExpander Header="嵌套展开卡片"
                                                     IsExpanded="True"
                                                     Margin="0,0,0,16">
                                        <StackPanel Margin="16">
                                            <TextBlock Text="这是一个包含子 CardExpander 的父级容器。"
                                                       Margin="0,0,0,16"/>

                                            <!-- 子 CardExpander 1 -->
                                            <ui:CardExpander Header="子展开卡片 A"
                                                             IsExpanded="False"
                                                             Margin="0,0,0,8"
                                                             Background="{DynamicResource ControlFillColorSecondaryBrush}">
                                                <StackPanel Margin="12">
                                                    <TextBlock Text="这是第一个子 CardExpander 的内容。"
                                                               Margin="0,0,0,8"/>
                                                    <TextBlock Text="可以包含任何类型的内容和控件。"
                                                               FontSize="12"
                                                               Margin="0,0,0,8"/>
                                                    <Button Content="子操作 A"
                                                            Command="{Binding HandleInteractionCommand}"
                                                            CommandParameter="子操作A"
                                                            HorizontalAlignment="Left"/>
                                                </StackPanel>
                                            </ui:CardExpander>

                                            <!-- 子 CardExpander 2 -->
                                            <ui:CardExpander Header="子展开卡片 B"
                                                             IsExpanded="False"
                                                             Background="{DynamicResource ControlFillColorSecondaryBrush}">
                                                <StackPanel Margin="12">
                                                    <TextBlock Text="这是第二个子 CardExpander 的内容。"
                                                               Margin="0,0,0,8"/>
                                                    <Grid>
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="*"/>
                                                            <ColumnDefinition Width="*"/>
                                                        </Grid.ColumnDefinitions>

                                                        <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                                            <TextBlock Text="选项 1" FontSize="12" Margin="0,0,0,4"/>
                                                            <CheckBox Content="启用功能 A" Margin="0,0,0,4"/>
                                                            <CheckBox Content="启用功能 B" Margin="0,0,0,8"/>
                                                        </StackPanel>

                                                        <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                                            <TextBlock Text="选项 2" FontSize="12" Margin="0,0,0,4"/>
                                                            <RadioButton Content="模式 A" GroupName="Mode" Margin="0,0,0,4"/>
                                                            <RadioButton Content="模式 B" GroupName="Mode" Margin="0,0,0,8"/>
                                                        </StackPanel>
                                                    </Grid>
                                                    <Button Content="应用设置"
                                                            Command="{Binding HandleInteractionCommand}"
                                                            CommandParameter="应用设置"
                                                            HorizontalAlignment="Left"/>
                                                </StackPanel>
                                            </ui:CardExpander>
                                        </StackPanel>
                                    </ui:CardExpander>

                                    <!-- 动态内容的 CardExpander -->
                                    <ui:CardExpander Header="动态内容展示"
                                                     IsExpanded="False"
                                                     Margin="0,0,0,16">
                                        <StackPanel Margin="16">
                                            <TextBlock Text="系统状态监控"
                                                       FontWeight="Bold"
                                                       FontSize="16"
                                                       Margin="0,0,0,12"/>

                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <StackPanel Grid.Column="0">
                                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                                        <Ellipse Width="8" Height="8"
                                                                 Fill="{DynamicResource SystemFillColorSuccessBrush}"
                                                                 Margin="0,0,8,0"/>
                                                        <TextBlock Text="服务器状态: 正常" FontSize="12"/>
                                                    </StackPanel>

                                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                                        <Ellipse Width="8" Height="8"
                                                                 Fill="{DynamicResource SystemFillColorCautionBrush}"
                                                                 Margin="0,0,8,0"/>
                                                        <TextBlock Text="数据库连接: 警告" FontSize="12"/>
                                                    </StackPanel>

                                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                                        <Ellipse Width="8" Height="8"
                                                                 Fill="{DynamicResource SystemFillColorSuccessBrush}"
                                                                 Margin="0,0,8,0"/>
                                                        <TextBlock Text="网络连接: 正常" FontSize="12"/>
                                                    </StackPanel>
                                                </StackPanel>

                                                <Button Grid.Column="1"
                                                        Content="刷新状态"
                                                        Command="{Binding HandleInteractionCommand}"
                                                        CommandParameter="刷新状态"
                                                        VerticalAlignment="Top"/>
                                            </Grid>

                                            <Separator Margin="0,8,0,8"/>

                                            <TextBlock Text="性能指标"
                                                       FontWeight="Medium"
                                                       Margin="0,0,0,8"/>

                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <StackPanel Grid.Column="0">
                                                    <TextBlock Text="CPU 使用率" FontSize="12" Margin="0,0,0,4"/>
                                                    <ProgressBar Value="35" Height="6" Margin="0,0,0,8"/>

                                                    <TextBlock Text="内存使用率" FontSize="12" Margin="0,0,0,4"/>
                                                    <ProgressBar Value="68" Height="6" Margin="0,0,0,8"/>

                                                    <TextBlock Text="网络流量" FontSize="12" Margin="0,0,0,4"/>
                                                    <ProgressBar Value="22" Height="6"/>
                                                </StackPanel>

                                                <StackPanel Grid.Column="1" Margin="16,0,0,0">
                                                    <TextBlock Text="35%" FontWeight="Bold" HorizontalAlignment="Right"/>
                                                    <TextBlock Text="68%" FontWeight="Bold" HorizontalAlignment="Right" Margin="0,14,0,0"/>
                                                    <TextBlock Text="22%" FontWeight="Bold" HorizontalAlignment="Right" Margin="0,14,0,0"/>
                                                </StackPanel>
                                            </Grid>
                                        </StackPanel>
                                    </ui:CardExpander>

                                    <!-- 表单类型的 CardExpander -->
                                    <ui:CardExpander Header="用户设置"
                                                     IsExpanded="False">
                                        <StackPanel Margin="16">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                </Grid.RowDefinitions>

                                                <TextBlock Grid.Row="0" Grid.Column="0"
                                                           Text="用户名:"
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,8"/>
                                                <TextBox Grid.Row="0" Grid.Column="1"
                                                         Text="admin"
                                                         Margin="0,0,0,8"/>

                                                <TextBlock Grid.Row="1" Grid.Column="0"
                                                           Text="邮箱:"
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,8"/>
                                                <TextBox Grid.Row="1" Grid.Column="1"
                                                         Text="<EMAIL>"
                                                         Margin="0,0,0,8"/>

                                                <TextBlock Grid.Row="2" Grid.Column="0"
                                                           Text="主题:"
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,8"/>
                                                <ComboBox Grid.Row="2" Grid.Column="1"
                                                          SelectedIndex="0"
                                                          Margin="0,0,0,8">
                                                    <ComboBoxItem Content="浅色主题"/>
                                                    <ComboBoxItem Content="深色主题"/>
                                                    <ComboBoxItem Content="自动"/>
                                                </ComboBox>

                                                <TextBlock Grid.Row="3" Grid.Column="0"
                                                           Text="语言:"
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,8"/>
                                                <ComboBox Grid.Row="3" Grid.Column="1"
                                                          SelectedIndex="0"
                                                          Margin="0,0,0,12">
                                                    <ComboBoxItem Content="中文"/>
                                                    <ComboBoxItem Content="English"/>
                                                    <ComboBoxItem Content="日本語"/>
                                                </ComboBox>

                                                <StackPanel Grid.Row="4" Grid.Column="0" Grid.ColumnSpan="2"
                                                            Orientation="Horizontal">
                                                    <Button Content="保存设置"
                                                            Command="{Binding HandleInteractionCommand}"
                                                            CommandParameter="保存设置"
                                                            Margin="0,0,8,0"/>
                                                    <Button Content="重置"
                                                            Command="{Binding HandleInteractionCommand}"
                                                            CommandParameter="重置设置"/>
                                                </StackPanel>
                                            </Grid>
                                        </StackPanel>
                                    </ui:CardExpander>
                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="高级功能代码示例"
                                Language="XAML"
                                Description="展示 WPF-UI CardExpander 控件的高级用法和组合布局"
                                ShowTabs="True"
                                XamlCode="{Binding AdvancedXamlExample}"
                                CSharpCode="{Binding AdvancedCSharpExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 样式展示 -->
                    <ui:CardExpander Header="🎨 样式展示" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 WPF-UI CardExpander 控件的各种样式和主题适配"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="样式展示示例：" FontWeight="Medium" Margin="0,0,0,16"/>

                                    <!-- 不同样式的 CardExpander -->
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- 左侧样式 -->
                                        <StackPanel Grid.Column="0" Margin="0,0,12,0">
                                            <!-- 成功样式 -->
                                            <ui:CardExpander Header="成功状态"
                                                             IsExpanded="False"
                                                             Background="{DynamicResource SystemFillColorSuccessBrush}"
                                                             Margin="0,0,0,12">
                                                <StackPanel Margin="16">
                                                    <TextBlock Text="操作已成功完成！"
                                                               Foreground="White"
                                                               Margin="0,0,0,8"/>
                                                    <TextBlock Text="所有数据已安全保存。"
                                                               Foreground="White"
                                                               FontSize="12"/>
                                                </StackPanel>
                                            </ui:CardExpander>

                                            <!-- 警告样式 -->
                                            <ui:CardExpander Header="警告信息"
                                                             IsExpanded="False"
                                                             Background="{DynamicResource SystemFillColorCautionBrush}"
                                                             Margin="0,0,0,12">
                                                <StackPanel Margin="16">
                                                    <TextBlock Text="请注意以下事项："
                                                               Foreground="White"
                                                               Margin="0,0,0,8"/>
                                                    <TextBlock Text="• 磁盘空间不足"
                                                               Foreground="White"
                                                               FontSize="12"
                                                               Margin="0,0,0,2"/>
                                                    <TextBlock Text="• 建议清理临时文件"
                                                               Foreground="White"
                                                               FontSize="12"/>
                                                </StackPanel>
                                            </ui:CardExpander>

                                            <!-- 错误样式 -->
                                            <ui:CardExpander Header="错误信息"
                                                             IsExpanded="False"
                                                             Background="{DynamicResource SystemFillColorCriticalBrush}">
                                                <StackPanel Margin="16">
                                                    <TextBlock Text="发生了严重错误！"
                                                               Foreground="White"
                                                               Margin="0,0,0,8"/>
                                                    <TextBlock Text="请联系系统管理员。"
                                                               Foreground="White"
                                                               FontSize="12"/>
                                                </StackPanel>
                                            </ui:CardExpander>
                                        </StackPanel>

                                        <!-- 右侧样式 -->
                                        <StackPanel Grid.Column="1" Margin="12,0,0,0">
                                            <!-- 强调样式 -->
                                            <ui:CardExpander Header="重要通知"
                                                             IsExpanded="False"
                                                             Background="{DynamicResource AccentFillColorDefaultBrush}"
                                                             Margin="0,0,0,12">
                                                <StackPanel Margin="16">
                                                    <TextBlock Text="系统更新可用"
                                                               Foreground="White"
                                                               FontWeight="Bold"
                                                               Margin="0,0,0,8"/>
                                                    <TextBlock Text="新版本包含重要的安全更新。"
                                                               Foreground="White"
                                                               FontSize="12"/>
                                                </StackPanel>
                                            </ui:CardExpander>

                                            <!-- 透明样式 -->
                                            <ui:CardExpander Header="透明卡片"
                                                             IsExpanded="False"
                                                             Background="Transparent"
                                                             BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                                             BorderThickness="1"
                                                             Margin="0,0,0,12">
                                                <StackPanel Margin="16">
                                                    <TextBlock Text="这是一个透明背景的 CardExpander。"
                                                               Margin="0,0,0,8"/>
                                                    <TextBlock Text="只有边框可见。"
                                                               FontSize="12"/>
                                                </StackPanel>
                                            </ui:CardExpander>

                                            <!-- 自定义样式 -->
                                            <ui:CardExpander Header="自定义样式"
                                                             IsExpanded="False">
                                                <ui:CardExpander.Background>
                                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                                        <GradientStop Color="#FF6B73FF" Offset="0"/>
                                                        <GradientStop Color="#FF9DD5FF" Offset="1"/>
                                                    </LinearGradientBrush>
                                                </ui:CardExpander.Background>
                                                <StackPanel Margin="16">
                                                    <TextBlock Text="渐变背景样式"
                                                               Foreground="White"
                                                               FontWeight="Bold"
                                                               Margin="0,0,0,8"/>
                                                    <TextBlock Text="使用自定义渐变背景。"
                                                               Foreground="White"
                                                               FontSize="12"/>
                                                </StackPanel>
                                            </ui:CardExpander>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="样式代码示例"
                                Language="XAML"
                                Description="展示如何使用不同的 CardExpander 样式和主题"
                                ShowTabs="True"
                                XamlCode="{Binding StylesXamlExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                </StackPanel>
            </Border>

            <!-- 底部操作栏 -->
            <Border Grid.Row="2" 
                    Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Padding="16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 统计信息 -->
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock Text="{Binding InteractionCount, StringFormat='交互次数: {0}'}" 
                                   FontSize="14" 
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   Margin="0,0,20,0"/>
                        <TextBlock Text="{Binding LastAction, StringFormat='最后操作: {0}'}" 
                                   FontSize="14" 
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                    </StackPanel>

                    <!-- 操作按钮 -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <ui:Button Content="重置计数" 
                                   Command="{Binding ResetCountCommand}"
                                   Appearance="Secondary"
                                   Margin="0,0,8,0"/>
                        <ui:Button Content="清除状态" 
                                   Command="{Binding ClearStatusCommand}"
                                   Appearance="Secondary"/>
                    </StackPanel>
                </Grid>
            </Border>

        </Grid>
    </ScrollViewer>
</UserControl>
