using WPFTest.Models.DWG;

namespace WPFTest.Services.DWG;

/// <summary>
/// DWG 文件类型服务接口 - 智能文件类型分析和管理
/// </summary>
/// <remarks>
/// 设计目的：
/// - 提供 DWG 文件类型的完整管理功能
/// - 支持基于文件名前缀的智能类型分析
/// - 支持基础 CRUD 操作（增删改查）
/// - 支持文件类型的启用/禁用管理
/// - 支持排序权重的批量更新
/// - 使用 YData 进行数据持久化
///
/// 核心功能：
/// - 智能文件类型分析：根据文件名前缀自动识别文件类型
/// - 可配置的文件类型规则：支持用户自定义文件类型和前缀
/// - 完整的数据管理：增删改查、启用禁用、排序管理
/// - 数据库管理：初始化、重置、信息查询
/// </remarks>
public interface IDwgFileTypeService : IDisposable
{
    #region 数据库初始化

    /// <summary>
    /// 初始化数据库
    /// </summary>
    /// <returns>初始化是否成功</returns>
    /// <remarks>
    /// 功能：
    /// - 创建数据库表结构
    /// - 插入默认文件类型数据（如果数据库为空）
    /// - 确保服务可以正常工作
    ///
    /// 默认文件类型包括：
    /// - 📁 全部、📋 出图、🏗️ 施工图、📐 模板图
    /// - 🗺️ 底图、🔄 变更、📊 计算书、🖼️ 图框
    /// - 🏢 建筑图、🔗 绑定、📄 其他
    /// </remarks>
    Task<bool> InitializeDatabaseAsync();

    /// <summary>
    /// 获取数据库信息
    /// </summary>
    /// <returns>数据库详细信息</returns>
    /// <remarks>
    /// 返回信息包括：
    /// - 数据库文件路径和大小
    /// - 最后修改时间
    /// - 文件类型数量
    /// - 初始化状态
    ///
    /// 用途：
    /// - 系统状态监控
    /// - 数据库健康检查
    /// - 管理界面信息显示
    /// </remarks>
    Task<DatabaseInfo> GetDatabaseInfoAsync();

    /// <summary>
    /// 重置为默认数据
    /// </summary>
    /// <returns>重置是否成功</returns>
    /// <remarks>
    /// 重置操作：
    /// 1. 清空现有所有文件类型数据
    /// 2. 重新插入默认文件类型数据
    /// 3. 重置排序权重
    ///
    /// 警告：此操作会丢失所有用户自定义的文件类型
    /// 建议在重置前提醒用户确认
    /// </remarks>
    Task<bool> ResetToDefaultAsync();

    #endregion

    #region 查询操作

    /// <summary>
    /// 获取所有文件类型
    /// </summary>
    /// <param name="includeDisabled">是否包含禁用的文件类型</param>
    /// <returns>文件类型列表，按排序权重和英文名称排序</returns>
    /// <remarks>
    /// 查询逻辑：
    /// - includeDisabled = false：只返回启用的文件类型
    /// - includeDisabled = true：返回所有文件类型（包括禁用的）
    /// - 结果按 SortOrder 升序，然后按 EnglishName 升序排列
    ///
    /// 性能优化：使用 YData 的查询缓存
    /// </remarks>
    Task<IList<DwgFileTypeModel>> GetAllFileTypesAsync(bool includeDisabled = false);

    /// <summary>
    /// 根据ID获取文件类型
    /// </summary>
    /// <param name="id">文件类型ID</param>
    /// <returns>文件类型模型，如果不存在返回 null</returns>
    /// <remarks>
    /// 用途：
    /// - 编辑文件类型时获取详细信息
    /// - 删除前确认文件类型存在
    /// - 更新操作的前置检查
    ///
    /// 性能：使用主键查询，性能最优
    /// </remarks>
    Task<DwgFileTypeModel?> GetFileTypeByIdAsync(int id);

    /// <summary>
    /// 根据英文名称获取文件类型
    /// </summary>
    /// <param name="englishName">英文名称</param>
    /// <returns>文件类型模型，如果不存在返回 null</returns>
    /// <remarks>
    /// 用途：
    /// - 检查英文名称是否重复
    /// - 按英文名称查找特定文件类型
    /// - 文件类型规则匹配
    ///
    /// 注意：英文名称是唯一标识，不允许重复
    /// </remarks>
    Task<DwgFileTypeModel?> GetFileTypeByEnglishNameAsync(string englishName);

    #endregion

    #region 智能分析

    /// <summary>
    /// 根据文件名分析文件类型（核心功能）
    /// </summary>
    /// <param name="fileName">文件名（不含路径）</param>
    /// <returns>匹配的文件类型，如果无匹配返回"其他"类型</returns>
    /// <remarks>
    /// 分析逻辑：
    /// 1. 将文件名转换为大写进行匹配
    /// 2. 遍历所有启用的文件类型（排除"全部"类型）
    /// 3. 检查文件名是否以任何前缀开头
    /// 4. 返回第一个匹配的文件类型
    /// 5. 如果无匹配，返回"其他"类型
    ///
    /// 匹配规则：
    /// - 支持下划线和横线分隔符：GS_、GS-
    /// - 大小写不敏感匹配
    /// - 前缀优先级按 SortOrder 排序
    ///
    /// 使用场景：
    /// - DwgManagerTabView 中替换硬编码分析
    /// - 文件导入时自动分类
    /// - 批量文件处理
    /// </remarks>
    Task<DwgFileTypeModel> AnalyzeFileTypeAsync(string fileName);

    #endregion

    #region 增删改操作

    /// <summary>
    /// 添加新文件类型
    /// </summary>
    /// <param name="fileType">文件类型模型</param>
    /// <returns>添加是否成功</returns>
    /// <remarks>
    /// 添加逻辑：
    /// 1. 验证文件类型数据的有效性
    /// 2. 检查英文名称是否重复
    /// 3. 插入到数据库并获取自增ID
    /// 4. 触发 FileTypeChanged 事件
    /// 5. 记录操作日志
    ///
    /// 验证规则：
    /// - 英文名称不能为空且不能重复
    /// - 中文名称不能为空
    /// - 前缀列表格式正确
    ///
    /// 事件通知：成功时触发 Added 类型事件
    /// </remarks>
    Task<bool> AddFileTypeAsync(DwgFileTypeModel fileType);

    /// <summary>
    /// 更新文件类型
    /// </summary>
    /// <param name="fileType">文件类型模型</param>
    /// <returns>更新是否成功</returns>
    /// <remarks>
    /// 更新逻辑：
    /// 1. 根据 ID 更新文件类型信息
    /// 2. 验证英文名称唯一性（排除自身）
    /// 3. 自动更新 UpdatedAt 时间戳
    /// 4. 触发 FileTypeChanged 事件
    /// 5. 记录操作日志
    ///
    /// 注意：UpdatedAt 字段会在模型的属性变化时自动更新
    /// 事件通知：成功时触发 Updated 类型事件
    /// </remarks>
    Task<bool> UpdateFileTypeAsync(DwgFileTypeModel fileType);

    /// <summary>
    /// 删除文件类型
    /// </summary>
    /// <param name="id">文件类型ID</param>
    /// <returns>删除是否成功</returns>
    /// <remarks>
    /// 删除逻辑：
    /// 1. 先查询文件类型是否存在
    /// 2. 检查是否为系统预设类型（不允许删除）
    /// 3. 执行物理删除操作
    /// 4. 触发 FileTypeChanged 事件
    /// 5. 记录操作日志
    ///
    /// 保护机制：
    /// - 系统预设的文件类型不允许删除
    /// - 删除前确认文件类型存在
    ///
    /// 事件通知：成功时触发 Deleted 类型事件
    /// </remarks>
    Task<bool> DeleteFileTypeAsync(int id);

    /// <summary>
    /// 启用/禁用文件类型
    /// </summary>
    /// <param name="id">文件类型ID</param>
    /// <param name="enabled">是否启用</param>
    /// <returns>操作是否成功</returns>
    /// <remarks>
    /// 启用/禁用逻辑：
    /// 1. 查询文件类型是否存在
    /// 2. 更新 IsEnabled 字段
    /// 3. 触发 FileTypeChanged 事件
    /// 4. 记录操作日志
    ///
    /// 业务影响：
    /// - 禁用的文件类型不参与智能分析
    /// - 禁用的文件类型在查询时可选择性排除
    /// - 不影响已分析的文件类型结果
    ///
    /// 事件通知：成功时触发 EnabledChanged 类型事件
    /// </remarks>
    Task<bool> SetFileTypeEnabledAsync(int id, bool enabled);

    /// <summary>
    /// 批量更新排序权重
    /// </summary>
    /// <param name="sortOrders">文件类型ID和排序权重的映射</param>
    /// <returns>更新是否成功</returns>
    /// <remarks>
    /// 批量更新逻辑：
    /// 1. 验证所有ID是否存在
    /// 2. 批量更新排序权重
    /// 3. 为每个文件类型触发 Updated 事件
    /// 4. 记录操作日志
    ///
    /// 排序影响：
    /// - 影响智能分析的匹配优先级
    /// - 影响查询结果的排序
    /// - 影响 UI 界面的显示顺序
    ///
    /// 使用场景：
    /// - 拖拽排序后保存新顺序
    /// - 批量调整文件类型优先级
    ///
    /// 性能考虑：使用事务确保数据一致性
    /// </remarks>
    Task<bool> UpdateSortOrdersAsync(Dictionary<int, int> sortOrders);

    #endregion

    #region 事件通知

    /// <summary>
    /// 文件类型数据变更事件
    /// </summary>
    /// <remarks>
    /// 触发时机：
    /// - 添加文件类型时：ChangeType = Added
    /// - 更新文件类型时：ChangeType = Updated
    /// - 删除文件类型时：ChangeType = Deleted
    /// - 启用状态变更时：ChangeType = EnabledChanged
    ///
    /// 用途：
    /// - UI 实时更新文件类型列表
    /// - 缓存失效通知
    /// - 审计日志记录
    /// - 智能分析规则重新加载
    /// </remarks>
    event EventHandler<FileTypeChangedEventArgs>? FileTypeChanged;

    #endregion

    #region 数据传输对象

    /// <summary>
    /// 文件类型变更事件参数
    /// </summary>
    /// <remarks>
    /// 用途：当文件类型发生增删改操作时，通过事件传递变更信息
    /// 订阅者可以根据变更类型执行相应的处理逻辑
    /// </remarks>
    public class FileTypeChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 变更类型
        /// </summary>
        /// <remarks>
        /// 指示文件类型发生了什么类型的变更
        /// 用于订阅者判断如何处理这个事件
        /// </remarks>
        public FileTypeChangeType ChangeType { get; }

        /// <summary>
        /// 发生变更的文件类型
        /// </summary>
        /// <remarks>
        /// 包含文件类型的完整信息
        /// 对于删除操作，这是删除前的文件类型信息
        /// </remarks>
        public DwgFileTypeModel FileType { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="changeType">变更类型</param>
        /// <param name="fileType">文件类型模型</param>
        public FileTypeChangedEventArgs(FileTypeChangeType changeType, DwgFileTypeModel fileType)
        {
            ChangeType = changeType;
            FileType = fileType;
        }
    }

    #endregion

    #region 枚举定义

    /// <summary>
    /// 文件类型变更类型枚举
    /// </summary>
    /// <remarks>
    /// 定义文件类型可能发生的各种变更操作类型
    /// 用于事件通知和日志记录
    /// </remarks>
    public enum FileTypeChangeType
    {
        /// <summary>
        /// 添加文件类型
        /// </summary>
        /// <remarks>
        /// 触发时机：调用 AddFileTypeAsync 成功时
        /// </remarks>
        Added,

        /// <summary>
        /// 更新文件类型
        /// </summary>
        /// <remarks>
        /// 触发时机：调用 UpdateFileTypeAsync 成功时
        /// </remarks>
        Updated,

        /// <summary>
        /// 删除文件类型
        /// </summary>
        /// <remarks>
        /// 触发时机：调用 DeleteFileTypeAsync 成功时
        /// </remarks>
        Deleted,

        /// <summary>
        /// 启用状态变更
        /// </summary>
        /// <remarks>
        /// 触发时机：调用 SetFileTypeEnabledAsync 成功时
        /// </remarks>
        EnabledChanged
    }

    /// <summary>
    /// 数据库信息模型
    /// </summary>
    /// <remarks>
    /// 用途：封装数据库的状态和统计信息
    /// 提供给管理界面显示数据库健康状态
    /// </remarks>
    public class DatabaseInfo
    {
        /// <summary>
        /// 数据库文件路径
        /// </summary>
        /// <remarks>
        /// SQLite 数据库文件的完整路径
        /// 例如：D:\App\Data\DwgManager.db
        /// </remarks>
        public string DatabasePath { get; set; } = string.Empty;

        /// <summary>
        /// 数据库文件大小（字节）
        /// </summary>
        /// <remarks>
        /// 用于监控数据库文件大小增长
        /// 可以通过 FileSizeText 属性获取格式化的显示文本
        /// </remarks>
        public long FileSize { get; set; }

        /// <summary>
        /// 数据库最后修改时间
        /// </summary>
        /// <remarks>
        /// 反映数据库最后一次数据变更的时间
        /// 用于监控数据库活跃度
        /// </remarks>
        public DateTime LastModified { get; set; }

        /// <summary>
        /// 文件类型数量
        /// </summary>
        /// <remarks>
        /// 当前数据库中存储的文件类型总数
        /// 包括启用和禁用的文件类型
        /// </remarks>
        public int FileTypeCount { get; set; }

        /// <summary>
        /// 数据库是否已初始化
        /// </summary>
        /// <remarks>
        /// 标识数据库是否已完成初始化
        /// - true：数据库表结构存在且包含默认数据
        /// - false：数据库未初始化或初始化失败
        /// </remarks>
        public bool IsInitialized { get; set; }

        /// <summary>
        /// 格式化的文件大小文本
        /// </summary>
        /// <remarks>
        /// 将字节数转换为人类可读的格式
        /// 例如：1024 → "1.00 KB"，1048576 → "1.00 MB"
        /// </remarks>
        public string FileSizeText
        {
            get
            {
                if (FileSize == 0) return "0 B";

                string[] sizes = { "B", "KB", "MB", "GB" };
                double len = FileSize;
                int order = 0;

                while (len >= 1024 && order < sizes.Length - 1)
                {
                    order++;
                    len = len / 1024;
                }

                return $"{len:0.##} {sizes[order]}";
            }
        }

        /// <summary>
        /// 数据库状态描述文本
        /// </summary>
        /// <remarks>
        /// 提供数据库状态的简要描述
        /// 用于 UI 界面的状态显示
        /// </remarks>
        public string StatusDescription => IsInitialized
            ? $"已初始化 - {FileTypeCount} 个文件类型"
            : "未初始化";
    }
}

#endregion
