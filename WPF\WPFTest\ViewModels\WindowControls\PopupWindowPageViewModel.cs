using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using Wpf.Ui.Controls;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels.WindowControls;

/// <summary>
/// 弹出窗口示例页面 ViewModel
/// </summary>
public partial class PopupWindowPageViewModel : ObservableObject
{
    #region 私有字段

    private readonly YLoggerInstance _logger = YLogger.ForDebug<PopupWindowPageViewModel>();

    #endregion

    #region 属性

    [ObservableProperty]
    private ObservableCollection<string> openWindows = new();

    [ObservableProperty]
    private bool hasNoOpenWindows = true;

    [ObservableProperty]
    private string lastOperation = "暂无操作";

    #endregion

    #region 构造函数

    /// <summary>
    /// 构造函数
    /// </summary>
    public PopupWindowPageViewModel()
    {
        try
        {
            _logger.Info("🚀 PopupWindowPageViewModel 构造函数开始");
            
            UpdateLastOperation("弹出窗口示例页面已初始化");
            
            _logger.Info("✅ PopupWindowPageViewModel 构造完成");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ PopupWindowPageViewModel 构造失败: {ex}");
        }
    }

    #endregion

    #region 基础弹出窗口命令

    [RelayCommand]
    private async Task ShowModalWindow()
    {
        try
        {
            var window = new FluentWindow
            {
                Title = "模态窗口示例",
                Width = 400,
                Height = 300,
                WindowStartupLocation = WindowStartupLocation.CenterOwner,
                Owner = Application.Current.MainWindow,
                Content = CreateBasicWindowContent("这是一个模态窗口", "模态窗口会阻塞父窗口，直到关闭为止。")
            };

            AddWindow("模态窗口示例");
            UpdateLastOperation("显示模态窗口");
            _logger.Info("📱 显示模态窗口");

            var result = window.ShowDialog();
            
            RemoveWindow("模态窗口示例");
            UpdateLastOperation($"模态窗口关闭，结果: {result}");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 显示模态窗口失败: {ex}");
            UpdateLastOperation($"显示模态窗口失败: {ex.Message}");
        }
    }

    [RelayCommand]
    private async Task ShowModelessWindow()
    {
        try
        {
            var window = new FluentWindow
            {
                Title = "非模态窗口示例",
                Width = 400,
                Height = 300,
                WindowStartupLocation = WindowStartupLocation.CenterScreen,
                Content = CreateBasicWindowContent("这是一个非模态窗口", "非模态窗口不会阻塞父窗口，可以同时操作多个窗口。")
            };

            window.Closed += (s, e) => RemoveWindow("非模态窗口示例");

            AddWindow("非模态窗口示例");
            UpdateLastOperation("显示非模态窗口");
            _logger.Info("📱 显示非模态窗口");

            window.Show();
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 显示非模态窗口失败: {ex}");
            UpdateLastOperation($"显示非模态窗口失败: {ex.Message}");
        }
    }

    [RelayCommand]
    private async Task ShowCustomSizeWindow()
    {
        try
        {
            var window = new FluentWindow
            {
                Title = "自定义大小窗口",
                Width = 600,
                Height = 450,
                MinWidth = 400,
                MinHeight = 300,
                WindowStartupLocation = WindowStartupLocation.CenterOwner,
                Owner = Application.Current.MainWindow,
                Content = CreateBasicWindowContent("自定义大小窗口", "这个窗口有自定义的大小和最小尺寸限制。\n宽度: 600px, 高度: 450px\n最小宽度: 400px, 最小高度: 300px")
            };

            window.Closed += (s, e) => RemoveWindow("自定义大小窗口");

            AddWindow("自定义大小窗口");
            UpdateLastOperation("显示自定义大小窗口");
            _logger.Info("📱 显示自定义大小窗口");

            window.Show();
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 显示自定义大小窗口失败: {ex}");
            UpdateLastOperation($"显示自定义大小窗口失败: {ex.Message}");
        }
    }

    [RelayCommand]
    private async Task ShowCenterWindow()
    {
        try
        {
            var window = new FluentWindow
            {
                Title = "居中显示窗口",
                Width = 500,
                Height = 350,
                WindowStartupLocation = WindowStartupLocation.CenterScreen,
                Content = CreateBasicWindowContent("居中显示窗口", "这个窗口总是在屏幕中央显示。")
            };

            window.Closed += (s, e) => RemoveWindow("居中显示窗口");

            AddWindow("居中显示窗口");
            UpdateLastOperation("显示居中窗口");
            _logger.Info("📱 显示居中窗口");

            window.Show();
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 显示居中窗口失败: {ex}");
            UpdateLastOperation($"显示居中窗口失败: {ex.Message}");
        }
    }

    [RelayCommand]
    private async Task ShowFeatureWindow()
    {
        try
        {
            var window = CreateFeatureWindow();

            AddWindow("功能完整窗口");
            UpdateLastOperation("显示功能完整窗口");
            _logger.Info("📱 显示功能完整窗口");

            window.Show();
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 显示功能完整窗口失败: {ex}");
            UpdateLastOperation($"显示功能完整窗口失败: {ex.Message}");
        }
    }

    #endregion

    #region 自定义内容窗口命令

    [RelayCommand]
    private async Task ShowFormWindow()
    {
        try
        {
            // 这里将创建一个包含表单的窗口
            UpdateLastOperation("表单窗口功能开发中...");
            _logger.Info("📱 表单窗口功能开发中");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 显示表单窗口失败: {ex}");
            UpdateLastOperation($"显示表单窗口失败: {ex.Message}");
        }
    }

    [RelayCommand]
    private async Task ShowDataWindow()
    {
        try
        {
            // 这里将创建一个数据展示窗口
            UpdateLastOperation("数据展示窗口功能开发中...");
            _logger.Info("📱 数据展示窗口功能开发中");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 显示数据窗口失败: {ex}");
            UpdateLastOperation($"显示数据窗口失败: {ex.Message}");
        }
    }

    [RelayCommand]
    private async Task ShowImageWindow()
    {
        try
        {
            // 这里将创建一个图片预览窗口
            UpdateLastOperation("图片预览窗口功能开发中...");
            _logger.Info("📱 图片预览窗口功能开发中");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 显示图片窗口失败: {ex}");
            UpdateLastOperation($"显示图片窗口失败: {ex.Message}");
        }
    }

    [RelayCommand]
    private async Task ShowSettingsWindow()
    {
        try
        {
            // 这里将创建一个设置配置窗口
            UpdateLastOperation("设置配置窗口功能开发中...");
            _logger.Info("📱 设置配置窗口功能开发中");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 显示设置窗口失败: {ex}");
            UpdateLastOperation($"显示设置窗口失败: {ex.Message}");
        }
    }

    #endregion

    #region 特殊功能窗口命令

    [RelayCommand]
    private async Task ShowProgressWindow()
    {
        try
        {
            // 这里将创建一个进度显示窗口
            UpdateLastOperation("进度显示窗口功能开发中...");
            _logger.Info("📱 进度显示窗口功能开发中");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 显示进度窗口失败: {ex}");
            UpdateLastOperation($"显示进度窗口失败: {ex.Message}");
        }
    }

    [RelayCommand]
    private async Task ShowLoginWindow()
    {
        try
        {
            // 这里将创建一个登录验证窗口
            UpdateLastOperation("登录验证窗口功能开发中...");
            _logger.Info("📱 登录验证窗口功能开发中");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 显示登录窗口失败: {ex}");
            UpdateLastOperation($"显示登录窗口失败: {ex.Message}");
        }
    }

    [RelayCommand]
    private async Task ShowFilePickerWindow()
    {
        try
        {
            // 这里将创建一个文件选择窗口
            UpdateLastOperation("文件选择窗口功能开发中...");
            _logger.Info("📱 文件选择窗口功能开发中");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 显示文件选择窗口失败: {ex}");
            UpdateLastOperation($"显示文件选择窗口失败: {ex.Message}");
        }
    }

    [RelayCommand]
    private async Task ShowAboutWindow()
    {
        try
        {
            // 这里将创建一个关于窗口
            UpdateLastOperation("关于窗口功能开发中...");
            _logger.Info("📱 关于窗口功能开发中");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 显示关于窗口失败: {ex}");
            UpdateLastOperation($"显示关于窗口失败: {ex.Message}");
        }
    }

    #endregion

    #region 窗口管理命令

    [RelayCommand]
    private async Task CloseAllWindows()
    {
        try
        {
            var windowsToClose = Application.Current.Windows.Cast<Window>()
                .Where(w => w != Application.Current.MainWindow)
                .ToList();

            foreach (var window in windowsToClose)
            {
                window.Close();
            }

            OpenWindows.Clear();
            UpdateHasNoOpenWindows();
            UpdateLastOperation($"已关闭 {windowsToClose.Count} 个弹出窗口");
            _logger.Info($"🗑️ 关闭了 {windowsToClose.Count} 个弹出窗口");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 关闭所有窗口失败: {ex}");
            UpdateLastOperation($"关闭所有窗口失败: {ex.Message}");
        }
    }

    [RelayCommand]
    private async Task ShowWindowList()
    {
        try
        {
            var windowCount = OpenWindows.Count;
            var windowList = string.Join("\n", OpenWindows);
            
            UpdateLastOperation($"当前打开 {windowCount} 个窗口:\n{windowList}");
            _logger.Info($"📋 显示窗口列表: {windowCount} 个窗口");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 显示窗口列表失败: {ex}");
            UpdateLastOperation($"显示窗口列表失败: {ex.Message}");
        }
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 创建基础窗口内容 - 支持拖拽移动
    /// </summary>
    private object CreateBasicWindowContent(string title, string description)
    {
        // 创建主容器
        var mainGrid = new System.Windows.Controls.Grid();
        mainGrid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = System.Windows.GridLength.Auto });
        mainGrid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = new System.Windows.GridLength(1, System.Windows.GridUnitType.Star) });

        // 创建透明拖拽标题栏
        var dragBar = new System.Windows.Controls.Border
        {
            Height = 35,
            Background = System.Windows.Media.Brushes.Transparent,
            Cursor = System.Windows.Input.Cursors.SizeAll,
            Margin = new Thickness(-20, -20, -20, 0) // 扩展到窗口边缘
        };

        // 不添加任何子元素，保持完全透明

        // 添加拖拽功能
        dragBar.MouseLeftButtonDown += (s, e) =>
        {
            if (e.LeftButton == System.Windows.Input.MouseButtonState.Pressed)
            {
                var window = Window.GetWindow(dragBar);
                window?.DragMove();
            }
        };

        System.Windows.Controls.Grid.SetRow(dragBar, 0);
        mainGrid.Children.Add(dragBar);

        // 创建内容区域
        var stackPanel = new System.Windows.Controls.StackPanel
        {
            Margin = new Thickness(20),
            VerticalAlignment = VerticalAlignment.Center
        };

        var titleBlock = new System.Windows.Controls.TextBlock
        {
            Text = title,
            FontSize = 18,
            FontWeight = FontWeights.Bold,
            Margin = new Thickness(0, 0, 0, 10),
            HorizontalAlignment = HorizontalAlignment.Center
        };

        var descBlock = new System.Windows.Controls.TextBlock
        {
            Text = description,
            FontSize = 14,
            TextWrapping = TextWrapping.Wrap,
            HorizontalAlignment = HorizontalAlignment.Center,
            TextAlignment = TextAlignment.Center,
            Margin = new Thickness(0, 0, 0, 10)
        };

        var tipBlock = new System.Windows.Controls.TextBlock
        {
            Text = "💡 提示：拖拽顶部标题区域可以移动窗口",
            FontSize = 11,
            FontStyle = FontStyles.Italic,
            HorizontalAlignment = HorizontalAlignment.Center,
            Opacity = 0.6,
            Margin = new Thickness(0, 0, 0, 15)
        };

        var closeButton = new System.Windows.Controls.Button
        {
            Content = "关闭窗口",
            Margin = new Thickness(0, 10, 0, 0),
            Padding = new Thickness(20, 8, 20, 8),
            HorizontalAlignment = HorizontalAlignment.Center
        };

        closeButton.Click += (s, e) =>
        {
            var window = Window.GetWindow(closeButton);
            window?.Close();
        };

        stackPanel.Children.Add(titleBlock);
        stackPanel.Children.Add(descBlock);
        stackPanel.Children.Add(tipBlock);
        stackPanel.Children.Add(closeButton);

        System.Windows.Controls.Grid.SetRow(stackPanel, 1);
        mainGrid.Children.Add(stackPanel);

        return mainGrid;
    }

    /// <summary>
    /// 创建功能完整的窗口 - 使用 ui:TitleBar
    /// </summary>
    private FluentWindow CreateFeatureWindow()
    {
        var window = new FluentWindow
        {
            Title = "功能完整窗口",
            Width = 800,
            Height = 600,
            MinWidth = 600,
            MinHeight = 400,
            WindowStartupLocation = WindowStartupLocation.CenterOwner,
            Owner = Application.Current.MainWindow,
            ResizeMode = ResizeMode.CanResize,
            WindowState = WindowState.Normal
        };

        // 创建主网格
        var mainGrid = new System.Windows.Controls.Grid();
        mainGrid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = System.Windows.GridLength.Auto });
        mainGrid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = new System.Windows.GridLength(1, System.Windows.GridUnitType.Star) });

        // 创建 TitleBar - 参考 MainView
        var titleBar = new Wpf.Ui.Controls.TitleBar
        {
            Title = "功能完整窗口 - 可移动、可调整大小",
            Height = 35,
            CanMaximize = true,
            ShowMaximize = true,
            ShowMinimize = true
        };

        // 设置 TitleBar 图标
        titleBar.Icon = new Wpf.Ui.Controls.SymbolIcon { Symbol = Wpf.Ui.Controls.SymbolRegular.WindowNew24 };

        System.Windows.Controls.Grid.SetRow(titleBar, 0);
        mainGrid.Children.Add(titleBar);

        // 创建内容区域
        var content = CreateSimpleFeatureContent();
        System.Windows.Controls.Grid.SetRow((System.Windows.UIElement)content, 1);
        mainGrid.Children.Add((System.Windows.UIElement)content);

        window.Content = mainGrid;

        // 窗口关闭事件
        window.Closed += (s, e) => RemoveWindow("功能完整窗口");

        return window;
    }

    /// <summary>
    /// 创建简化的功能窗口内容
    /// </summary>
    private object CreateSimpleFeatureContent()
    {
        var scrollViewer = new System.Windows.Controls.ScrollViewer
        {
            VerticalScrollBarVisibility = System.Windows.Controls.ScrollBarVisibility.Auto,
            Padding = new Thickness(20)
        };

        var stackPanel = new System.Windows.Controls.StackPanel();

        // 标题区域
        var titlePanel = new System.Windows.Controls.StackPanel
        {
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 0, 0, 20)
        };

        var titleBlock = new System.Windows.Controls.TextBlock
        {
            Text = "功能完整的弹出窗口",
            FontSize = 24,
            FontWeight = FontWeights.Bold,
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 0, 0, 10)
        };

        var subtitleBlock = new System.Windows.Controls.TextBlock
        {
            Text = "这个窗口使用了 ui:TitleBar，具有完整的标题栏功能",
            FontSize = 14,
            HorizontalAlignment = HorizontalAlignment.Center,
            TextWrapping = TextWrapping.Wrap,
            Margin = new Thickness(0, 0, 0, 0)
        };

        titlePanel.Children.Add(titleBlock);
        titlePanel.Children.Add(subtitleBlock);
        stackPanel.Children.Add(titlePanel);

        // 功能列表
        var featuresGroup = new System.Windows.Controls.GroupBox
        {
            Header = "窗口功能",
            Margin = new Thickness(0, 0, 0, 20)
        };

        var featuresPanel = new System.Windows.Controls.StackPanel
        {
            Margin = new Thickness(10)
        };

        var features = new[]
        {
            "✅ 自定义标题栏 (ui:TitleBar)",
            "✅ 可移动窗口 - 拖拽标题栏",
            "✅ 可调整大小 - 拖拽边框",
            "✅ 最小化/最大化按钮",
            "✅ Snap Layout 支持",
            "✅ 现代化 WPF-UI 样式",
            "✅ 响应式布局"
        };

        foreach (var feature in features)
        {
            var featureBlock = new System.Windows.Controls.TextBlock
            {
                Text = feature,
                Margin = new Thickness(0, 3, 0, 3),
                FontSize = 13
            };
            featuresPanel.Children.Add(featureBlock);
        }

        featuresGroup.Content = featuresPanel;
        stackPanel.Children.Add(featuresGroup);

        // 操作按钮区域
        var actionsGroup = new System.Windows.Controls.GroupBox
        {
            Header = "窗口操作",
            Margin = new Thickness(0, 0, 0, 20)
        };

        var buttonsPanel = new System.Windows.Controls.WrapPanel
        {
            Margin = new Thickness(10)
        };

        // 创建操作按钮
        var buttons = new[]
        {
            ("置顶窗口", "TopMost"),
            ("取消置顶", "Normal"),
            ("居中显示", "Center"),
            ("最小化", "Minimize"),
            ("最大化", "Maximize"),
            ("还原", "Restore")
        };

        foreach (var (text, action) in buttons)
        {
            var button = new System.Windows.Controls.Button
            {
                Content = text,
                Margin = new Thickness(0, 0, 8, 8),
                Padding = new Thickness(12, 6, 12, 6),
                MinWidth = 80
            };

            button.Click += (s, e) => HandleSimpleWindowAction(action, button);
            buttonsPanel.Children.Add(button);
        }

        actionsGroup.Content = buttonsPanel;
        stackPanel.Children.Add(actionsGroup);

        // 关闭按钮
        var closePanel = new System.Windows.Controls.StackPanel
        {
            Orientation = System.Windows.Controls.Orientation.Horizontal,
            HorizontalAlignment = HorizontalAlignment.Right,
            Margin = new Thickness(0, 20, 0, 0)
        };

        var closeButton = new System.Windows.Controls.Button
        {
            Content = "关闭窗口",
            Padding = new Thickness(20, 8, 20, 8),
            IsDefault = true
        };

        closeButton.Click += (s, e) =>
        {
            var window = Window.GetWindow(closeButton);
            window?.Close();
        };

        closePanel.Children.Add(closeButton);
        stackPanel.Children.Add(closePanel);

        scrollViewer.Content = stackPanel;
        return scrollViewer;
    }

    /// <summary>
    /// 处理简化的窗口操作
    /// </summary>
    private void HandleSimpleWindowAction(string action, System.Windows.Controls.Button button)
    {
        try
        {
            var window = Window.GetWindow(button);
            if (window == null) return;

            switch (action)
            {
                case "TopMost":
                    window.Topmost = true;
                    UpdateLastOperation("窗口已置顶");
                    break;
                case "Normal":
                    window.Topmost = false;
                    UpdateLastOperation("窗口已取消置顶");
                    break;
                case "Center":
                    var screenWidth = SystemParameters.PrimaryScreenWidth;
                    var screenHeight = SystemParameters.PrimaryScreenHeight;
                    window.Left = (screenWidth - window.Width) / 2;
                    window.Top = (screenHeight - window.Height) / 2;
                    UpdateLastOperation("窗口已居中");
                    break;
                case "Minimize":
                    window.WindowState = WindowState.Minimized;
                    UpdateLastOperation("窗口已最小化");
                    break;
                case "Maximize":
                    window.WindowState = WindowState.Maximized;
                    UpdateLastOperation("窗口已最大化");
                    break;
                case "Restore":
                    window.WindowState = WindowState.Normal;
                    UpdateLastOperation("窗口已还原");
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 窗口操作失败: {ex}");
            UpdateLastOperation($"窗口操作失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 创建功能窗口的内容（旧版本，保留备用）
    /// </summary>
    private object CreateFeatureWindowContent()
    {
        var mainGrid = new System.Windows.Controls.Grid
        {
            Margin = new Thickness(20)
        };

        // 定义行
        mainGrid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = System.Windows.GridLength.Auto });
        mainGrid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = new System.Windows.GridLength(1, System.Windows.GridUnitType.Star) });
        mainGrid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = System.Windows.GridLength.Auto });

        // 标题区域
        var titlePanel = new System.Windows.Controls.StackPanel
        {
            Orientation = System.Windows.Controls.Orientation.Vertical,
            Margin = new Thickness(0, 0, 0, 20)
        };

        var titleBlock = new System.Windows.Controls.TextBlock
        {
            Text = "功能完整的弹出窗口",
            FontSize = 24,
            FontWeight = FontWeights.Bold,
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 0, 0, 10)
        };

        var subtitleBlock = new System.Windows.Controls.TextBlock
        {
            Text = "这个窗口具有完整的功能：可移动、可调整大小、有标题栏",
            FontSize = 14,
            HorizontalAlignment = HorizontalAlignment.Center,
            TextWrapping = TextWrapping.Wrap,
            Opacity = 0.8
        };

        titlePanel.Children.Add(titleBlock);
        titlePanel.Children.Add(subtitleBlock);
        System.Windows.Controls.Grid.SetRow(titlePanel, 0);
        mainGrid.Children.Add(titlePanel);

        // 主内容区域
        var contentArea = CreateFeatureContentArea();
        System.Windows.Controls.Grid.SetRow((System.Windows.UIElement)contentArea, 1);
        mainGrid.Children.Add((System.Windows.UIElement)contentArea);

        // 底部按钮区域
        var buttonPanel = CreateFeatureButtonPanel();
        System.Windows.Controls.Grid.SetRow((System.Windows.UIElement)buttonPanel, 2);
        mainGrid.Children.Add((System.Windows.UIElement)buttonPanel);

        return mainGrid;
    }

    /// <summary>
    /// 添加窗口到列表
    /// </summary>
    private void AddWindow(string windowTitle)
    {
        OpenWindows.Add($"• {windowTitle} - {DateTime.Now:HH:mm:ss}");
        UpdateHasNoOpenWindows();
    }

    /// <summary>
    /// 从列表移除窗口
    /// </summary>
    private void RemoveWindow(string windowTitle)
    {
        var itemToRemove = OpenWindows.FirstOrDefault(w => w.Contains(windowTitle));
        if (itemToRemove != null)
        {
            OpenWindows.Remove(itemToRemove);
        }
        UpdateHasNoOpenWindows();
    }

    /// <summary>
    /// 更新是否没有打开的窗口
    /// </summary>
    private void UpdateHasNoOpenWindows()
    {
        HasNoOpenWindows = OpenWindows.Count == 0;
    }

    /// <summary>
    /// 创建功能窗口的内容区域
    /// </summary>
    private object CreateFeatureContentArea()
    {
        var scrollViewer = new System.Windows.Controls.ScrollViewer
        {
            VerticalScrollBarVisibility = System.Windows.Controls.ScrollBarVisibility.Auto,
            HorizontalScrollBarVisibility = System.Windows.Controls.ScrollBarVisibility.Auto
        };

        var stackPanel = new System.Windows.Controls.StackPanel();

        // 功能演示区域
        var featuresGroup = CreateFeaturesGroup();
        stackPanel.Children.Add((System.Windows.UIElement)featuresGroup);

        // 信息显示区域
        var infoGroup = CreateInfoGroup();
        stackPanel.Children.Add((System.Windows.UIElement)infoGroup);

        // 操作区域
        var actionsGroup = CreateActionsGroup();
        stackPanel.Children.Add((System.Windows.UIElement)actionsGroup);

        scrollViewer.Content = stackPanel;
        return scrollViewer;
    }

    /// <summary>
    /// 创建功能演示组
    /// </summary>
    private object CreateFeaturesGroup()
    {
        var groupBox = new System.Windows.Controls.GroupBox
        {
            Header = "窗口功能演示",
            Margin = new Thickness(0, 0, 0, 15)
        };

        var stackPanel = new System.Windows.Controls.StackPanel
        {
            Margin = new Thickness(10)
        };

        // 功能列表
        var features = new[]
        {
            "✅ 可移动：拖拽标题栏可移动窗口",
            "✅ 可调整大小：拖拽边框可调整窗口大小",
            "✅ 最小化/最大化：标题栏按钮功能完整",
            "✅ 模态/非模态：支持两种显示模式",
            "✅ 现代化样式：WPF-UI 主题支持",
            "✅ 响应式布局：内容自适应窗口大小"
        };

        foreach (var feature in features)
        {
            var textBlock = new System.Windows.Controls.TextBlock
            {
                Text = feature,
                Margin = new Thickness(0, 2, 0, 2),
                FontSize = 13
            };
            stackPanel.Children.Add(textBlock);
        }

        groupBox.Content = stackPanel;
        return groupBox;
    }

    /// <summary>
    /// 创建信息显示组
    /// </summary>
    private object CreateInfoGroup()
    {
        var groupBox = new System.Windows.Controls.GroupBox
        {
            Header = "窗口信息",
            Margin = new Thickness(0, 0, 0, 15)
        };

        var grid = new System.Windows.Controls.Grid
        {
            Margin = new Thickness(10)
        };

        // 定义列
        grid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition { Width = System.Windows.GridLength.Auto });
        grid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition { Width = new System.Windows.GridLength(1, System.Windows.GridUnitType.Star) });

        // 信息项
        var infoItems = new[]
        {
            ("窗口类型:", "FluentWindow (WPF-UI)"),
            ("初始大小:", "800 x 600"),
            ("最小大小:", "600 x 400"),
            ("启动位置:", "相对于父窗口居中"),
            ("调整模式:", "可调整大小"),
            ("创建时间:", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
        };

        for (int i = 0; i < infoItems.Length; i++)
        {
            grid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = System.Windows.GridLength.Auto });

            var labelBlock = new System.Windows.Controls.TextBlock
            {
                Text = infoItems[i].Item1,
                FontWeight = FontWeights.SemiBold,
                Margin = new Thickness(0, 2, 10, 2),
                VerticalAlignment = VerticalAlignment.Top
            };
            System.Windows.Controls.Grid.SetRow(labelBlock, i);
            System.Windows.Controls.Grid.SetColumn(labelBlock, 0);
            grid.Children.Add(labelBlock);

            var valueBlock = new System.Windows.Controls.TextBlock
            {
                Text = infoItems[i].Item2,
                Margin = new Thickness(0, 2, 0, 2),
                TextWrapping = TextWrapping.Wrap,
                VerticalAlignment = VerticalAlignment.Top
            };
            System.Windows.Controls.Grid.SetRow(valueBlock, i);
            System.Windows.Controls.Grid.SetColumn(valueBlock, 1);
            grid.Children.Add(valueBlock);
        }

        groupBox.Content = grid;
        return groupBox;
    }

    /// <summary>
    /// 创建操作区域组
    /// </summary>
    private object CreateActionsGroup()
    {
        var groupBox = new System.Windows.Controls.GroupBox
        {
            Header = "窗口操作",
            Margin = new Thickness(0, 0, 0, 15)
        };

        var wrapPanel = new System.Windows.Controls.WrapPanel
        {
            Margin = new Thickness(10),
            Orientation = System.Windows.Controls.Orientation.Horizontal
        };

        // 操作按钮
        var buttons = new[]
        {
            ("最小化窗口", "Minimize"),
            ("最大化窗口", "Maximize"),
            ("还原窗口", "Restore"),
            ("置顶显示", "TopMost"),
            ("取消置顶", "Normal"),
            ("居中显示", "Center")
        };

        foreach (var (text, action) in buttons)
        {
            var button = new System.Windows.Controls.Button
            {
                Content = text,
                Margin = new Thickness(0, 0, 8, 8),
                Padding = new Thickness(12, 6, 12, 6),
                MinWidth = 100
            };

            button.Click += (s, e) => HandleWindowAction(action, button);
            wrapPanel.Children.Add(button);
        }

        groupBox.Content = wrapPanel;
        return groupBox;
    }

    /// <summary>
    /// 创建底部按钮面板
    /// </summary>
    private object CreateFeatureButtonPanel()
    {
        var panel = new System.Windows.Controls.StackPanel
        {
            Orientation = System.Windows.Controls.Orientation.Horizontal,
            HorizontalAlignment = HorizontalAlignment.Right,
            Margin = new Thickness(0, 20, 0, 0)
        };

        var closeButton = new System.Windows.Controls.Button
        {
            Content = "关闭窗口",
            Padding = new Thickness(20, 8, 20, 8),
            Margin = new Thickness(8, 0, 0, 0),
            IsDefault = true
        };

        closeButton.Click += (s, e) =>
        {
            var window = Window.GetWindow(closeButton);
            window?.Close();
        };

        panel.Children.Add(closeButton);
        return panel;
    }

    /// <summary>
    /// 处理窗口操作
    /// </summary>
    private void HandleWindowAction(string action, System.Windows.Controls.Button button)
    {
        try
        {
            var window = Window.GetWindow(button);
            if (window == null) return;

            switch (action)
            {
                case "Minimize":
                    window.WindowState = WindowState.Minimized;
                    UpdateLastOperation("窗口已最小化");
                    break;
                case "Maximize":
                    window.WindowState = WindowState.Maximized;
                    UpdateLastOperation("窗口已最大化");
                    break;
                case "Restore":
                    window.WindowState = WindowState.Normal;
                    UpdateLastOperation("窗口已还原");
                    break;
                case "TopMost":
                    window.Topmost = true;
                    UpdateLastOperation("窗口已置顶");
                    break;
                case "Normal":
                    window.Topmost = false;
                    UpdateLastOperation("窗口已取消置顶");
                    break;
                case "Center":
                    window.WindowStartupLocation = WindowStartupLocation.CenterScreen;
                    var screenWidth = SystemParameters.PrimaryScreenWidth;
                    var screenHeight = SystemParameters.PrimaryScreenHeight;
                    window.Left = (screenWidth - window.Width) / 2;
                    window.Top = (screenHeight - window.Height) / 2;
                    UpdateLastOperation("窗口已居中");
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 窗口操作失败: {ex}");
            UpdateLastOperation($"窗口操作失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 更新最后操作状态
    /// </summary>
    private void UpdateLastOperation(string operation)
    {
        LastOperation = $"[{DateTime.Now:HH:mm:ss}] {operation}";
    }

    #endregion
}
