<UserControl x:Class="Zylo.WPF.Controls.CodeExample.CodeExampleControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:avalon="http://icsharpcode.net/sharpdevelop/avalonedit"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="600">
    
    <UserControl.Resources>
        <!-- 代码示例样式 -->
        <Style x:Key="CodeExampleCardStyle" TargetType="ui:Card">
            <Setter Property="Padding" Value="0"/>
            <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
        </Style>

        <!-- 代码编辑器样式 -->
        <Style x:Key="CodeEditorStyle" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource ControlFillColorInputActiveBrush}"/>
            <Setter Property="CornerRadius" Value="0,0,8,8"/>
            <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
            <Setter Property="BorderThickness" Value="0,1,0,0"/>
        </Style>

        <!-- 标题栏样式 -->
        <Style x:Key="TitleBarStyle" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}"/>
            <Setter Property="CornerRadius" Value="8,8,0,0"/>
            <Setter Property="Padding" Value="15,10"/>
        </Style>
    </UserControl.Resources>

    <ui:Card Style="{StaticResource CodeExampleCardStyle}">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 标题栏 - 可点击展开/收缩 -->
            <Button Grid.Row="0"
                    x:Name="TitleBarButton"
                    Style="{DynamicResource DefaultButtonStyle}"
                    Background="{DynamicResource ControlFillColorDefaultBrush}"
                    BorderThickness="0"
                    Padding="15,10"
                    HorizontalContentAlignment="Stretch"
                    Click="TitleBarButton_Click"
                    Cursor="Hand">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 图标和标题 -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <ui:SymbolIcon Symbol="Code24"
                                      FontSize="16"
                                      Margin="0,0,8,0"
                                      Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                        <TextBlock Text="{Binding Title, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                  FontWeight="SemiBold"
                                  VerticalAlignment="Center"
                                  Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                    </StackPanel>

                    <!-- 语言标签 -->
                    <Border Grid.Column="1"
                           Background="{DynamicResource AccentFillColorDefaultBrush}"
                           CornerRadius="12"
                           Padding="8,4"
                           Margin="10,0,0,0"
                           HorizontalAlignment="Left">
                        <TextBlock Text="{Binding Language, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                  FontSize="11"
                                  FontWeight="Medium"
                                  Foreground="{DynamicResource TextOnAccentFillColorPrimaryBrush}"/>
                    </Border>

                    <!-- 复制按钮 -->
                    <ui:Button Grid.Column="2"
                              x:Name="CopyButton"
                              ToolTip="复制代码"
                              FontSize="14"
                              Padding="8,4"
                              Margin="5,0"
                              Click="CopyCodeButton_Click"
                              Appearance="Secondary">
                        <ui:SymbolIcon Symbol="Copy24" FontSize="14"/>
                    </ui:Button>

                    <!-- 展开/收缩指示器 -->
                    <ui:SymbolIcon Grid.Column="3"
                                  x:Name="ExpandCollapseIndicator"
                                  Symbol="ChevronDown24"
                                  FontSize="14"
                                  VerticalAlignment="Center"
                                  Margin="10,0,0,0"
                                  Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                </Grid>
            </Button>

            <!-- 代码内容区域 -->
            <Border Grid.Row="1" 
                   x:Name="CodeContentBorder"
                   Style="{StaticResource CodeEditorStyle}"
                   Visibility="Visible">
                
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 选项卡区域 -->
                    <Border Grid.Row="0"
                           x:Name="TabsContainer"
                           Background="{DynamicResource ControlFillColorDefaultBrush}"
                           BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                           BorderThickness="0,0,0,1"
                           Visibility="Collapsed">
                        <StackPanel Orientation="Horizontal" Margin="15,8">
                            <ui:Button x:Name="XamlTabButton"
                                      Content="📄 XAML"
                                      FontSize="12"
                                      Padding="12,6"
                                      Margin="0,0,5,0"
                                      Click="XamlTabButton_Click">
                                <ui:Button.Style>
                                    <Style TargetType="ui:Button" BasedOn="{StaticResource {x:Type ui:Button}}">
                                        <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}"/>
                                        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
                                        <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
                                        <Setter Property="BorderThickness" Value="1"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}"/>
                                                <Setter Property="BorderBrush" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </ui:Button.Style>
                            </ui:Button>

                            <ui:Button x:Name="CSharpTabButton"
                                      Content="⚙️ C#"
                                      FontSize="12"
                                      Padding="12,6"
                                      Margin="5,0,0,0"
                                      Click="CSharpTabButton_Click">
                                <ui:Button.Style>
                                    <Style TargetType="ui:Button" BasedOn="{StaticResource {x:Type ui:Button}}">
                                        <Setter Property="Background" Value="Transparent"/>
                                        <Setter Property="Foreground" Value="{DynamicResource TextFillColorSecondaryBrush}"/>
                                        <Setter Property="BorderThickness" Value="0"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}"/>
                                                <Setter Property="BorderBrush" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
                                                <Setter Property="BorderThickness" Value="1"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </ui:Button.Style>
                            </ui:Button>
                        </StackPanel>
                    </Border>

                    <!-- AvalonEdit代码编辑器区域 -->
                    <Grid Grid.Row="1">
                        <!-- AvalonEdit语法高亮编辑器 -->
                        <Border Background="{DynamicResource ControlFillColorInputActiveBrush}"
                               CornerRadius="4"
                               Margin="15"
                               ClipToBounds="True">
                            <avalon:TextEditor x:Name="AvalonCodeEditor"
                                              Background="Transparent"
                                              Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                              FontFamily="Consolas, 'Courier New', monospace"
                                              FontSize="13"
                                              IsReadOnly="True"
                                              ShowLineNumbers="True"
                                              WordWrap="False"
                                              HorizontalScrollBarVisibility="Hidden"
                                              VerticalScrollBarVisibility="Hidden"
                                              IsManipulationEnabled="False"/>
                        </Border>
                    </Grid>

                    <!-- 底部信息栏 -->
                    <Border Grid.Row="2"
                           Background="{DynamicResource ControlFillColorSecondaryBrush}"
                           BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                           BorderThickness="0,1,0,0"
                           Padding="15,8">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- 描述信息 -->
                            <TextBlock Grid.Column="0"
                                      Text="{Binding Description, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                      FontSize="11"
                                      Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                      VerticalAlignment="Center"/>

                            <!-- 行数信息 -->
                            <TextBlock Grid.Column="1"
                                      x:Name="LineCountTextBlock"
                                      FontSize="11"
                                      Foreground="{DynamicResource TextFillColorTertiaryBrush}"
                                      VerticalAlignment="Center"/>
                        </Grid>
                    </Border>
                </Grid>
            </Border>
        </Grid>
    </ui:Card>
</UserControl>
