<UserControl
    d:DesignHeight="800"
    d:DesignWidth="1200"
    mc:Ignorable="d"
    x:Class="WPFTest.Views.MediaControls.InfoBadgePageView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:codeExample="clr-namespace:Zylo.WPF.Controls.CodeExample;assembly=Zylo.WPF"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:WPFTest.Views.MediaControls"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <UserControl.Resources>
        <!--  转换器  -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />

        <!--  动画资源  -->
        <Storyboard RepeatBehavior="Forever" x:Key="PulseAnimation">
            <DoubleAnimation
                AutoReverse="True"
                Duration="0:0:1"
                From="1.0"
                Storyboard.TargetProperty="Opacity"
                To="0.5" />
        </Storyboard>
    </UserControl.Resources>

    <Grid>
        <ScrollViewer Padding="20" VerticalScrollBarVisibility="Auto">
            <StackPanel>

                <!--  页面标题  -->
                <StackPanel Margin="0,0,0,32">
                    <StackPanel Margin="0,0,0,8" Orientation="Horizontal">
                        <ui:SymbolIcon
                            FontSize="32"
                            Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                            Margin="0,0,12,0"
                            Symbol="Info24" />
                        <TextBlock
                            FontSize="28"
                            FontWeight="Bold"
                            Text="InfoBadge 控件示例"
                            VerticalAlignment="Center" />
                    </StackPanel>
                    <TextBlock
                        FontSize="14"
                        Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                        Text="InfoBadge 是一个轻量级的通知徽章控件，支持点状、数字和图标三种类型，用于显示状态、计数和通知信息。" />
                </StackPanel>

                <!--  状态栏  -->
                <Border
                    Background="{DynamicResource ControlFillColorDefaultBrush}"
                    CornerRadius="6"
                    Margin="0,0,0,24"
                    Padding="12,8">
                    <StackPanel Orientation="Horizontal">
                        <ui:SymbolIcon
                            FontSize="16"
                            Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                            Margin="0,0,8,0"
                            Symbol="Info24" />
                        <TextBlock
                            FontWeight="Medium"
                            Text="{Binding StatusMessage}"
                            VerticalAlignment="Center" />
                    </StackPanel>
                </Border>

                <!--  基础功能演示  -->
                <ui:CardExpander
                    Header="基础功能演示"
                    Icon="{ui:SymbolIcon Info24}"
                    IsExpanded="True"
                    Margin="0,0,0,20"
                    Style="{StaticResource ZyloDemoCardStyle}">
                    <StackPanel>

                        <!--  主要演示区域  -->
                        <TextBlock
                            FontSize="16"
                            FontWeight="SemiBold"
                            Margin="0,0,0,16"
                            Text="InfoBadge 基础用法" />

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="2*" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <!--  演示区域  -->
                            <Border
                                Background="{DynamicResource ControlFillColorDefaultBrush}"
                                CornerRadius="8"
                                Grid.Column="0"
                                MinHeight="200"
                                Padding="20">
                                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">

                                    <!--  基础 InfoBadge 演示  -->
                                    <StackPanel HorizontalAlignment="Center" Margin="0,0,0,20">
                                        <TextBlock
                                            FontWeight="Medium"
                                            HorizontalAlignment="Center"
                                            Margin="0,0,0,12"
                                            Text="基础 InfoBadge" />

                                        <StackPanel HorizontalAlignment="Center" Orientation="Horizontal">
                                            <!--  点状 InfoBadge  -->
                                            <Grid Margin="0,0,12,0">
                                                <ui:Button Content="消息" />
                                                <ui:InfoBadge
                                                    Background="Red"
                                                    Height="8"
                                                    HorizontalAlignment="Right"
                                                    Margin="0,-5,-5,0"
                                                    VerticalAlignment="Top"
                                                    Visibility="{Binding ShowInfoBadge, Converter={StaticResource BooleanToVisibilityConverter}}"
                                                    Width="8" />
                                            </Grid>

                                            <!--  数字 InfoBadge  -->
                                            <Grid Margin="0,0,12,0">
                                                <ui:Button Content="通知" />
                                                <ui:InfoBadge
                                                    Background="{Binding BadgeBackground}"
                                                    Foreground="{Binding BadgeForeground}"
                                                    Height="{Binding BadgeHeight}"
                                                    HorizontalAlignment="Right"
                                                    Margin="0,-5,-5,0"
                                                    Value="{Binding BadgeValue}"
                                                    VerticalAlignment="Top"
                                                    Visibility="{Binding ShowInfoBadge, Converter={StaticResource BooleanToVisibilityConverter}}"
                                                    Width="{Binding BadgeWidth}" />
                                            </Grid>

                                            <!--  图标 InfoBadge  -->
                                            <Grid>
                                                <ui:Button Content="邮件" />
                                                <ui:InfoBadge
                                                    Background="Blue"
                                                    HorizontalAlignment="Right"
                                                    Margin="0,-5,-5,0"
                                                    Value="!"
                                                    VerticalAlignment="Top"
                                                    Visibility="{Binding ShowInfoBadge, Converter={StaticResource BooleanToVisibilityConverter}}" />
                                            </Grid>
                                        </StackPanel>
                                    </StackPanel>

                                    <!--  不同类型演示  -->
                                    <StackPanel HorizontalAlignment="Center">
                                        <TextBlock
                                            FontWeight="Medium"
                                            HorizontalAlignment="Center"
                                            Margin="0,0,0,12"
                                            Text="不同类型的 InfoBadge" />

                                        <StackPanel HorizontalAlignment="Center" Orientation="Horizontal">
                                            <!--  通知计数  -->
                                            <Grid Margin="0,0,12,0">
                                                <ui:Button Content="通知" Icon="{ui:SymbolIcon Alert24}" />
                                                <ui:InfoBadge
                                                    Background="Orange"
                                                    Foreground="White"
                                                    HorizontalAlignment="Right"
                                                    Margin="0,-5,-5,0"
                                                    Value="{Binding NotificationCount}"
                                                    VerticalAlignment="Top" />
                                            </Grid>

                                            <!--  邮件计数  -->
                                            <Grid Margin="0,0,12,0">
                                                <ui:Button Content="邮件" Icon="{ui:SymbolIcon Mail24}" />
                                                <ui:InfoBadge
                                                    Background="Green"
                                                    Foreground="White"
                                                    HorizontalAlignment="Right"
                                                    Margin="0,-5,-5,0"
                                                    Value="{Binding MailCount}"
                                                    VerticalAlignment="Top" />
                                            </Grid>

                                            <!--  消息计数  -->
                                            <Grid>
                                                <ui:Button Content="消息" Icon="{ui:SymbolIcon ChatBubblesQuestion24}" />
                                                <ui:InfoBadge
                                                    Background="Purple"
                                                    Foreground="White"
                                                    HorizontalAlignment="Right"
                                                    Margin="0,-5,-5,0"
                                                    Value="{Binding MessageCount}"
                                                    VerticalAlignment="Top" />
                                            </Grid>
                                        </StackPanel>
                                    </StackPanel>

                                </StackPanel>
                            </Border>

                            <!--  控制面板  -->
                            <StackPanel Grid.Column="1" Margin="20,0,0,0">
                                <TextBlock
                                    FontWeight="SemiBold"
                                    Margin="0,0,0,12"
                                    Text="控制面板" />

                                <!--  InfoBadge 类型选择  -->
                                <StackPanel Margin="0,0,0,16">
                                    <TextBlock
                                        FontWeight="Medium"
                                        Margin="0,0,0,8"
                                        Text="InfoBadge 类型:" />
                                    <ComboBox
                                        DisplayMemberPath="Description"
                                        ItemsSource="{Binding BadgeTypeOptions}"
                                        SelectedValue="{Binding BadgeType}"
                                        SelectedValuePath="Type" />
                                    <ui:Button
                                        Command="{Binding ApplyBadgeTypeCommand}"
                                        Content="应用类型"
                                        Margin="0,8,0,0" />
                                </StackPanel>

                                <!--  数值控制  -->
                                <StackPanel Margin="0,0,0,16">
                                    <TextBlock
                                        FontWeight="Medium"
                                        Margin="0,0,0,8"
                                        Text="数值控制:" />
                                    <TextBox Margin="0,0,0,8" Text="{Binding BadgeValue, UpdateSourceTrigger=PropertyChanged}" />
                                    <StackPanel Orientation="Horizontal">
                                        <ui:Button
                                            Command="{Binding IncreaseValueCommand}"
                                            Content="+"
                                            Margin="0,0,4,0"
                                            Width="30" />
                                        <ui:Button
                                            Command="{Binding DecreaseValueCommand}"
                                            Content="-"
                                            Margin="0,0,4,0"
                                            Width="30" />
                                        <ui:Button
                                            Command="{Binding ResetValueCommand}"
                                            Content="重置"
                                            Width="50" />
                                    </StackPanel>
                                </StackPanel>

                                <!--  图标选择  -->
                                <StackPanel Margin="0,0,0,16">
                                    <TextBlock
                                        FontWeight="Medium"
                                        Margin="0,0,0,8"
                                        Text="图标选择:" />
                                    <ComboBox
                                        DisplayMemberPath="Name"
                                        ItemsSource="{Binding IconOptions}"
                                        SelectedValue="{Binding BadgeIconSource}"
                                        SelectedValuePath="Icon" />
                                </StackPanel>

                                <!--  背景色选择  -->
                                <StackPanel Margin="0,0,0,16">
                                    <TextBlock
                                        FontWeight="Medium"
                                        Margin="0,0,0,8"
                                        Text="背景色:" />
                                    <ComboBox
                                        DisplayMemberPath="Name"
                                        ItemsSource="{Binding BackgroundColorOptions}"
                                        SelectedValue="{Binding BadgeBackground}"
                                        SelectedValuePath="Color" />
                                </StackPanel>

                                <!--  显示控制  -->
                                <StackPanel Margin="0,0,0,16">
                                    <CheckBox
                                        Content="显示 InfoBadge"
                                        IsChecked="{Binding ShowInfoBadge}"
                                        Margin="0,0,0,8" />
                                    <CheckBox Content="启用动画" IsChecked="{Binding IsAnimationEnabled}" />
                                </StackPanel>

                                <!--  操作按钮  -->
                                <StackPanel>
                                    <ui:Button
                                        Command="{Binding ToggleVisibilityCommand}"
                                        Content="切换显示"
                                        Margin="0,0,0,8" />
                                    <ui:Button Command="{Binding ToggleAnimationCommand}" Content="切换动画" />
                                </StackPanel>

                            </StackPanel>

                        </Grid>

                        <!--  代码示例  -->
                        <codeExample:CodeExampleControl
                            CSharpCode="{Binding BasicInfoBadgeCSharpExample}"
                            Description="基础 InfoBadge 控件的使用方法"
                            IsExpanded="False"
                            ShowTabs="True"
                            Title="基础用法"
                            XamlCode="{Binding BasicInfoBadgeXamlExample}" />
                    </StackPanel>
                </ui:CardExpander>

                <!--  高级功能演示  -->
                <ui:CardExpander
                    Header="高级功能演示"
                    Icon="{ui:SymbolIcon Settings24}"
                    IsExpanded="True"
                    Margin="0,0,0,20"
                    Style="{StaticResource ZyloDemoCardStyle}">
                    <StackPanel>

                        <!--  多种样式的 InfoBadge 展示  -->
                        <TextBlock
                            FontSize="16"
                            FontWeight="SemiBold"
                            Margin="0,0,0,16"
                            Text="不同样式和用途的 InfoBadge" />

                        <!--  实际应用场景演示  -->
                        <Border
                            Background="{DynamicResource ControlFillColorDefaultBrush}"
                            CornerRadius="8"
                            Margin="0,0,0,20"
                            Padding="20">
                            <StackPanel>

                                <!--  社交媒体应用  -->
                                <StackPanel Margin="0,0,0,20">
                                    <TextBlock
                                        FontWeight="Medium"
                                        Margin="0,0,0,12"
                                        Text="社交媒体应用:" />
                                    <StackPanel Orientation="Horizontal">
                                        <Grid Margin="0,0,12,0">
                                            <ui:Button Content="朋友圈" Icon="{ui:SymbolIcon People24}" />
                                            <ui:InfoBadge
                                                Background="Red"
                                                Foreground="White"
                                                HorizontalAlignment="Right"
                                                Margin="0,-5,-5,0"
                                                Value="6"
                                                VerticalAlignment="Top" />
                                        </Grid>
                                        <Grid Margin="0,0,12,0">
                                            <ui:Button Content="私信" Icon="{ui:SymbolIcon ChatBubblesQuestion24}" />
                                            <ui:InfoBadge
                                                Background="Green"
                                                Foreground="White"
                                                HorizontalAlignment="Right"
                                                Margin="0,-5,-5,0"
                                                Value="4"
                                                VerticalAlignment="Top" />
                                        </Grid>
                                        <Grid>
                                            <ui:Button Content="点赞" Icon="{ui:SymbolIcon Heart24}" />
                                            <ui:InfoBadge
                                                Background="Pink"
                                                Foreground="White"
                                                HorizontalAlignment="Right"
                                                Margin="0,-5,-5,0"
                                                Value="23"
                                                VerticalAlignment="Top" />
                                        </Grid>
                                    </StackPanel>
                                </StackPanel>

                                <!--  工作协作应用  -->
                                <StackPanel Margin="0,0,0,20">
                                    <TextBlock
                                        FontWeight="Medium"
                                        Margin="0,0,0,12"
                                        Text="工作协作应用:" />
                                    <StackPanel Orientation="Horizontal">
                                        <Grid Margin="0,0,12,0">
                                            <ui:Button Content="待办" Icon="{ui:SymbolIcon Square20}" />
                                            <ui:InfoBadge
                                                Background="Orange"
                                                Foreground="White"
                                                HorizontalAlignment="Right"
                                                Margin="0,-5,-5,0"
                                                Value="9"
                                                VerticalAlignment="Top" />
                                        </Grid>
                                        <Grid Margin="0,0,12,0">
                                            <ui:Button Content="审批" Icon="{ui:SymbolIcon DocumentCheckmark24}" />
                                            <ui:InfoBadge
                                                Background="Purple"
                                                Foreground="White"
                                                HorizontalAlignment="Right"
                                                Margin="0,-5,-5,0"
                                                Value="2"
                                                VerticalAlignment="Top" />
                                        </Grid>
                                        <Grid>
                                            <ui:Button Content="会议" Icon="{ui:SymbolIcon VideoClip24}" />
                                            <ui:InfoBadge
                                                Background="Red"
                                                Height="8"
                                                HorizontalAlignment="Right"
                                                Margin="0,-5,-5,0"
                                                VerticalAlignment="Top"
                                                Width="8" />
                                        </Grid>
                                    </StackPanel>
                                </StackPanel>

                                <!--  电商应用  -->
                                <StackPanel>
                                    <TextBlock
                                        FontWeight="Medium"
                                        Margin="0,0,0,12"
                                        Text="电商应用:" />
                                    <StackPanel Orientation="Horizontal">
                                        <Grid Margin="0,0,12,0">
                                            <ui:Button
                                                Content="购物车"
                                                Icon="{ui:SymbolIcon Cart24}"/>
                                            <ui:InfoBadge
                                                Background="Red"
                                                Foreground="White"
                                                Value="3"
                                                HorizontalAlignment="Right"
                                                VerticalAlignment="Top"
                                                Margin="0,-5,-5,0" />
                                        </Grid>
                                        <Grid Margin="0,0,12,0">
                                            <ui:Button
                                                Content="收藏"
                                                Icon="{ui:SymbolIcon Heart24}"/>
                                            <ui:InfoBadge
                                                Background="Pink"
                                                Foreground="White"
                                                Value="18"
                                                HorizontalAlignment="Right"
                                                VerticalAlignment="Top"
                                                Margin="0,-5,-5,0" />
                                        </Grid>
                                        <Grid>
                                            <ui:Button Content="优惠券" Icon="{ui:SymbolIcon TicketDiagonal24}"/>
                                            <ui:InfoBadge
                                                Background="Gold"
                                                Foreground="Black"
                                                Value="5"
                                                HorizontalAlignment="Right"
                                                VerticalAlignment="Top"
                                                Margin="0,-5,-5,0" />
                                        </Grid>
                                    </StackPanel>
                                </StackPanel>

                            </StackPanel>
                        </Border>

                        <!--  自定义样式演示  -->
                        <TextBlock
                            FontSize="16"
                            FontWeight="SemiBold"
                            Margin="0,0,0,16"
                            Text="自定义样式 InfoBadge" />

                        <Border
                            Background="{DynamicResource ControlFillColorDefaultBrush}"
                            CornerRadius="8"
                            Margin="0,0,0,20"
                            Padding="20">
                            <StackPanel>

                                <!--  渐变背景  -->
                                <StackPanel Margin="0,0,0,16">
                                    <TextBlock
                                        FontWeight="Medium"
                                        Margin="0,0,0,8"
                                        Text="渐变背景:" />
                                    <StackPanel Orientation="Horizontal">
                                        <Grid Margin="0,0,12,0">
                                            <ui:Button Content="渐变1"/>
                                            <ui:InfoBadge Foreground="White" Value="5"
                                                          HorizontalAlignment="Right"
                                                          VerticalAlignment="Top"
                                                          Margin="0,-5,-5,0">
                                                <ui:InfoBadge.Background>
                                                    <LinearGradientBrush EndPoint="1,1" StartPoint="0,0">
                                                        <GradientStop Color="#FF6B73FF" Offset="0" />
                                                        <GradientStop Color="#FF9644FF" Offset="1" />
                                                    </LinearGradientBrush>
                                                </ui:InfoBadge.Background>
                                            </ui:InfoBadge>
                                        </Grid>
                                        <Grid Margin="0,0,12,0">
                                            <ui:Button Content="渐变2"/>
                                            <ui:InfoBadge Foreground="White" Value="12"
                                                          HorizontalAlignment="Right"
                                                          VerticalAlignment="Top"
                                                          Margin="0,-5,-5,0">
                                                <ui:InfoBadge.Background>
                                                    <LinearGradientBrush EndPoint="1,1" StartPoint="0,0">
                                                        <GradientStop Color="#FFFF6B6B" Offset="0" />
                                                        <GradientStop Color="#FFFF8E53" Offset="1" />
                                                    </LinearGradientBrush>
                                                </ui:InfoBadge.Background>
                                            </ui:InfoBadge>
                                        </Grid>
                                        <Grid>
                                            <ui:Button Content="渐变3"/>
                                            <ui:InfoBadge Foreground="White" Value="8"
                                                          HorizontalAlignment="Right"
                                                          VerticalAlignment="Top"
                                                          Margin="0,-5,-5,0">
                                                <ui:InfoBadge.Background>
                                                    <LinearGradientBrush EndPoint="1,1" StartPoint="0,0">
                                                        <GradientStop Color="#FF4ECDC4" Offset="0" />
                                                        <GradientStop Color="#FF44A08D" Offset="1" />
                                                    </LinearGradientBrush>
                                                </ui:InfoBadge.Background>
                                            </ui:InfoBadge>
                                        </Grid>
                                    </StackPanel>
                                </StackPanel>

                                <!--  阴影效果  -->
                                <StackPanel>
                                    <TextBlock
                                        FontWeight="Medium"
                                        Margin="0,0,0,8"
                                        Text="阴影效果:" />
                                    <Grid>
                                        <ui:Button Content="阴影徽章"/>
                                        <ui:InfoBadge
                                            Background="DarkBlue"
                                            Foreground="White"
                                            Value="7"
                                            HorizontalAlignment="Right"
                                            VerticalAlignment="Top"
                                            Margin="0,-5,-5,0">
                                            <ui:InfoBadge.Effect>
                                                <DropShadowEffect
                                                    BlurRadius="4"
                                                    Color="Black"
                                                    Direction="315"
                                                    Opacity="0.5"
                                                    ShadowDepth="2" />
                                            </ui:InfoBadge.Effect>
                                        </ui:InfoBadge>
                                    </Grid>
                                </StackPanel>

                            </StackPanel>
                        </Border>

                        <!--  代码示例  -->
                        <codeExample:CodeExampleControl
                            CSharpCode="{Binding AdvancedInfoBadgeCSharpExample}"
                            Description="高级 InfoBadge 控件的使用方法和自定义样式"
                            IsExpanded="False"
                            ShowTabs="True"
                            Title="高级用法"
                            XamlCode="{Binding AdvancedInfoBadgeXamlExample}" />
                    </StackPanel>
                </ui:CardExpander>

            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>