// DatePicker C# 基础用法示例 - MVVM模式
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace WPFTest.ViewModels.InputControls
{
    /// <summary>
    /// DatePicker 基础示例 ViewModel - 展示正确的MVVM模式
    /// 关键点：使用 DateTime? 类型支持空值，Mode=TwoWay 确保双向绑定
    /// </summary>
    public partial class CalendarDatePickerPageViewModel : ObservableObject
    {
        #region 日期属性

        /// <summary>
        /// 选择的日期 - 使用 DateTime? 支持空值
        /// </summary>
        [ObservableProperty]
        private DateTime? selectedDate = DateTime.Today;

        /// <summary>
        /// 生日日期
        /// </summary>
        [ObservableProperty]
        private DateTime? birthDate = new DateTime(1990, 1, 1);

        /// <summary>
        /// 活动日期
        /// </summary>
        [ObservableProperty]
        private DateTime? eventDate = DateTime.Today.AddDays(7);

        /// <summary>
        /// 交互次数
        /// </summary>
        [ObservableProperty]
        private int interactionCount = 0;

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        private string statusMessage = "欢迎使用 CalendarDatePicker！";

        #endregion

        #region 构造函数

        public CalendarDatePickerPageViewModel()
        {
            // 监听属性变化
            PropertyChanged += OnPropertyChanged;
            
            StatusMessage = "CalendarDatePicker 示例已加载，开始选择日期吧！";
        }

        #endregion

        #region 命令 - MVVM模式的核心

        /// <summary>
        /// 设置今天日期命令
        /// </summary>
        [RelayCommand]
        private void SetToday()
        {
            SelectedDate = DateTime.Today;
            InteractionCount++;
            StatusMessage = $"📅 已设置为今天: {DateTime.Today:yyyy-MM-dd}";
        }

        /// <summary>
        /// 清除日期命令
        /// </summary>
        [RelayCommand]
        private void ClearDate()
        {
            SelectedDate = null;
            InteractionCount++;
            StatusMessage = "🗑️ 已清除选择的日期";
        }

        /// <summary>
        /// 随机日期命令
        /// </summary>
        [RelayCommand]
        private void RandomDate()
        {
            var random = new Random();
            var startDate = DateTime.Today.AddYears(-2);
            var endDate = DateTime.Today.AddYears(1);
            var range = (endDate - startDate).Days;
            var randomDate = startDate.AddDays(random.Next(range));
            
            SelectedDate = randomDate;
            InteractionCount++;
            StatusMessage = $"🎲 随机生成日期: {randomDate:yyyy-MM-dd}";
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 属性变化处理 - 响应数据绑定的变化
        /// </summary>
        private void OnPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            switch (e.PropertyName)
            {
                case nameof(SelectedDate):
                    HandleDateChanged("主要日期", SelectedDate);
                    break;
                case nameof(BirthDate):
                    HandleDateChanged("生日", BirthDate);
                    break;
                case nameof(EventDate):
                    HandleDateChanged("活动日期", EventDate);
                    break;
            }
        }

        /// <summary>
        /// 处理日期变化
        /// </summary>
        private void HandleDateChanged(string dateType, DateTime? newDate)
        {
            InteractionCount++;
            
            if (newDate.HasValue)
            {
                StatusMessage = $"📅 {dateType}已更改为: {newDate.Value:yyyy-MM-dd}";
            }
            else
            {
                StatusMessage = $"🗑️ {dateType}已清除";
            }
        }

        #endregion

        #region 实用方法

        /// <summary>
        /// 格式化日期显示
        /// </summary>
        public string FormatDate(DateTime? date)
        {
            return date?.ToString("yyyy年MM月dd日") ?? "未选择";
        }

        /// <summary>
        /// 获取日期的星期几
        /// </summary>
        public string GetDayOfWeek(DateTime? date)
        {
            if (!date.HasValue) return "";
            
            return date.Value.DayOfWeek switch
            {
                DayOfWeek.Monday => "星期一",
                DayOfWeek.Tuesday => "星期二",
                DayOfWeek.Wednesday => "星期三",
                DayOfWeek.Thursday => "星期四",
                DayOfWeek.Friday => "星期五",
                DayOfWeek.Saturday => "星期六",
                DayOfWeek.Sunday => "星期日",
                _ => ""
            };
        }

        /// <summary>
        /// 计算日期间隔
        /// </summary>
        public int CalculateDaysBetween(DateTime? startDate, DateTime? endDate)
        {
            if (!startDate.HasValue || !endDate.HasValue) return 0;
            return (endDate.Value - startDate.Value).Days;
        }

        #endregion
    }
}
