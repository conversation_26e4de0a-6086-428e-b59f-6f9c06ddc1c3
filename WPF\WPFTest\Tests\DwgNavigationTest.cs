using Prism.Regions;
using System;
using System.IO;
using WPFTest.Examples;

namespace WPFTest.Tests;

/// <summary>
/// DWG导航功能测试类
/// </summary>
/// <remarks>
/// 用于测试DwgManagerTabViewModel的导航参数功能是否正常工作
/// </remarks>
public class DwgNavigationTest
{
    private readonly IRegionManager _regionManager;
    private readonly DwgNavigationExample _navigationExample;

    public DwgNavigationTest(IRegionManager regionManager)
    {
        _regionManager = regionManager ?? throw new ArgumentNullException(nameof(regionManager));
        _navigationExample = new DwgNavigationExample(_regionManager);
    }

    /// <summary>
    /// 测试基本导航功能
    /// </summary>
    public void TestBasicNavigation()
    {
        try
        {
            Console.WriteLine("🧪 测试基本导航...");
            _navigationExample.BasicNavigation();
            Console.WriteLine("✅ 基本导航测试通过");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 基本导航测试失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 测试带参数的导航
    /// </summary>
    public void TestParameterNavigation()
    {
        try
        {
            Console.WriteLine("🧪 测试参数导航...");
            
            // 创建测试路径
            var testPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "TestDWG");
            Directory.CreateDirectory(testPath);

            _navigationExample.NavigateWithCustomPath(testPath);
            Console.WriteLine("✅ 参数导航测试通过");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 参数导航测试失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 测试默认选择项设置
    /// </summary>
    public void TestDefaultSelection()
    {
        try
        {
            Console.WriteLine("🧪 测试默认选择项...");
            
            var testPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "TestDWG");
            _navigationExample.NavigateWithDefaults(testPath, "建筑", "出图");
            
            Console.WriteLine("✅ 默认选择项测试通过");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 默认选择项测试失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 测试强制新实例
    /// </summary>
    public void TestForceNewInstance()
    {
        try
        {
            Console.WriteLine("🧪 测试强制新实例...");
            
            var testPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "TestDWG");
            _navigationExample.NavigateWithNewInstance(testPath);
            
            Console.WriteLine("✅ 强制新实例测试通过");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 强制新实例测试失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 测试项目切换功能
    /// </summary>
    public void TestProjectSwitch()
    {
        try
        {
            Console.WriteLine("🧪 测试项目切换...");
            
            var projectInfo = new ProjectInfo
            {
                Name = "测试项目",
                DwgFolderPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "TestDWG"),
                DefaultProfession = "建筑"
            };

            _navigationExample.SwitchProject(projectInfo);
            Console.WriteLine("✅ 项目切换测试通过");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 项目切换测试失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 测试快速访问功能
    /// </summary>
    public void TestQuickAccess()
    {
        try
        {
            Console.WriteLine("🧪 测试快速访问...");
            
            _navigationExample.QuickAccessArchitecture();
            _navigationExample.QuickAccessStructure();
            
            Console.WriteLine("✅ 快速访问测试通过");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 快速访问测试失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 运行所有测试
    /// </summary>
    public void RunAllTests()
    {
        Console.WriteLine("🚀 开始DWG导航功能测试...");
        Console.WriteLine(new string('=', 50));

        TestBasicNavigation();
        TestParameterNavigation();
        TestDefaultSelection();
        TestForceNewInstance();
        TestProjectSwitch();
        TestQuickAccess();

        Console.WriteLine(new string('=', 50));
        Console.WriteLine("🏁 所有测试完成");
    }

    /// <summary>
    /// 清理测试数据
    /// </summary>
    public void CleanupTestData()
    {
        try
        {
            var testPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "TestDWG");
            if (Directory.Exists(testPath))
            {
                Directory.Delete(testPath, true);
                Console.WriteLine("🧹 测试数据清理完成");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"⚠️ 清理测试数据失败: {ex.Message}");
        }
    }
}

/// <summary>
/// 测试运行器 - 提供静态方法来运行测试
/// </summary>
public static class DwgNavigationTestRunner
{
    /// <summary>
    /// 运行DWG导航测试
    /// </summary>
    /// <param name="regionManager">区域管理器</param>
    public static void RunTests(IRegionManager regionManager)
    {
        var tester = new DwgNavigationTest(regionManager);
        
        try
        {
            tester.RunAllTests();
        }
        finally
        {
            tester.CleanupTestData();
        }
    }

    /// <summary>
    /// 快速测试 - 只测试基本功能
    /// </summary>
    /// <param name="regionManager">区域管理器</param>
    public static void QuickTest(IRegionManager regionManager)
    {
        var tester = new DwgNavigationTest(regionManager);
        
        Console.WriteLine("⚡ 快速测试模式");
        tester.TestBasicNavigation();
        tester.TestQuickAccess();
        Console.WriteLine("⚡ 快速测试完成");
    }
}
