using System;
using System.Collections.ObjectModel;
using System.Windows.Media;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Wpf.Ui.Controls;

namespace WPFTest.ViewModels.WindowControls
{
    /// <summary>
    /// 高级 TitleBar 示例 ViewModel
    /// </summary>
    public partial class AdvancedTitleBarViewModel : ObservableObject
    {
        #region 基础属性

        /// <summary>
        /// 标题栏标题
        /// </summary>
        [ObservableProperty]
        public partial string TitleBarTitle { get; set; } = "高级标题栏演示";

        /// <summary>
        /// 标题栏高度
        /// </summary>
        [ObservableProperty]
        public partial double TitleBarHeight { get; set; } = 35;

        /// <summary>
        /// 是否显示最小化按钮
        /// </summary>
        [ObservableProperty]
        public partial bool ShowMinimize { get; set; } = true;

        /// <summary>
        /// 是否显示最大化按钮
        /// </summary>
        [ObservableProperty]
        public partial bool ShowMaximize { get; set; } = true;

        /// <summary>
        /// 是否显示关闭按钮
        /// </summary>
        [ObservableProperty]
        public partial bool ShowClose { get; set; } = true;

        /// <summary>
        /// 是否可以最大化
        /// </summary>
        [ObservableProperty]
        public partial bool CanMaximize { get; set; } = true;

        #endregion

        #region 高级功能属性

        /// <summary>
        /// 是否使用自定义图标
        /// </summary>
        [ObservableProperty]
        public partial bool UseCustomIcon { get; set; } = true;

        /// <summary>
        /// 当前选中的图标
        /// </summary>
        [ObservableProperty]
        public partial SymbolRegular SelectedIcon { get; set; } = SymbolRegular.Window24;

        /// <summary>
        /// 连接状态文本
        /// </summary>
        [ObservableProperty]
        public partial string ConnectionStatus { get; set; } = "在线";

        /// <summary>
        /// 状态颜色
        /// </summary>
        [ObservableProperty]
        public partial Brush StatusColor { get; set; } = Brushes.Green;

        #endregion

        #region 集合属性

        /// <summary>
        /// 可用图标集合
        /// </summary>
        public ObservableCollection<IconItem> AvailableIcons { get; } = new();

        /// <summary>
        /// 操作历史记录
        /// </summary>
        public ObservableCollection<OperationRecord> OperationHistory { get; } = new();

        #endregion

        /// <summary>
        /// 构造函数
        /// </summary>
        public AdvancedTitleBarViewModel()
        {
            InitializeAvailableIcons();
            AddOperationRecord("高级 TitleBar 初始化完成");
        }

        #region 标题栏操作命令

        /// <summary>
        /// 生成随机标题命令
        /// </summary>
        [RelayCommand]
        private void GenerateRandomTitle()
        {
            try
            {
                var titles = new[]
                {
                    "🚀 创新应用",
                    "💼 商务工具",
                    "🎨 设计软件",
                    "📊 数据分析",
                    "🔧 系统工具",
                    "🌟 明星产品",
                    "⚡ 高效办公",
                    "🎯 目标管理"
                };

                var random = new Random();
                TitleBarTitle = titles[random.Next(titles.Length)];
                AddOperationRecord($"生成随机标题: {TitleBarTitle}");
            }
            catch (Exception ex)
            {
                AddOperationRecord($"生成随机标题失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 标题栏双击命令
        /// </summary>
        [RelayCommand]
        private void TitleBarDoubleClick()
        {
            try
            {
                // 模拟最大化/还原切换
                var action = CanMaximize ? "最大化" : "还原";
                AddOperationRecord($"标题栏双击 - {action}");
            }
            catch (Exception ex)
            {
                AddOperationRecord($"标题栏双击处理失败: {ex.Message}");
            }
        }

        #endregion

        #region 自定义按钮命令

        /// <summary>
        /// 刷新命令
        /// </summary>
        [RelayCommand]
        private void Refresh()
        {
            try
            {
                AddOperationRecord("执行刷新操作");
                // 这里可以添加实际的刷新逻辑
            }
            catch (Exception ex)
            {
                AddOperationRecord($"刷新失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示设置命令
        /// </summary>
        [RelayCommand]
        private void ShowSettings()
        {
            try
            {
                AddOperationRecord("打开设置面板");
                // 这里可以添加打开设置窗口的逻辑
            }
            catch (Exception ex)
            {
                AddOperationRecord($"打开设置失败: {ex.Message}");
            }
        }

        #endregion

        #region 状态控制命令

        /// <summary>
        /// 设置在线状态命令
        /// </summary>
        [RelayCommand]
        private void SetOnlineStatus()
        {
            try
            {
                ConnectionStatus = "在线";
                StatusColor = Brushes.Green;
                AddOperationRecord("状态设置为: 在线");
            }
            catch (Exception ex)
            {
                AddOperationRecord($"设置在线状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置忙碌状态命令
        /// </summary>
        [RelayCommand]
        private void SetBusyStatus()
        {
            try
            {
                ConnectionStatus = "忙碌";
                StatusColor = Brushes.Orange;
                AddOperationRecord("状态设置为: 忙碌");
            }
            catch (Exception ex)
            {
                AddOperationRecord($"设置忙碌状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置离线状态命令
        /// </summary>
        [RelayCommand]
        private void SetOfflineStatus()
        {
            try
            {
                ConnectionStatus = "离线";
                StatusColor = Brushes.Red;
                AddOperationRecord("状态设置为: 离线");
            }
            catch (Exception ex)
            {
                AddOperationRecord($"设置离线状态失败: {ex.Message}");
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化可用图标
        /// </summary>
        private void InitializeAvailableIcons()
        {
            var icons = new[]
            {
                new IconItem("窗口", SymbolRegular.Window24),
                new IconItem("应用", SymbolRegular.Apps24),
                new IconItem("设置", SymbolRegular.Settings24),
                new IconItem("文档", SymbolRegular.Document24),
                new IconItem("文件夹", SymbolRegular.Folder24),
                new IconItem("图片", SymbolRegular.Image24),
                new IconItem("音乐", SymbolRegular.MusicNote124),
                new IconItem("视频", SymbolRegular.Video24),
                new IconItem("工具", SymbolRegular.Wrench24),
                new IconItem("信息", SymbolRegular.Info24),
                new IconItem("邮件", SymbolRegular.Mail24),
                new IconItem("日历", SymbolRegular.Calendar24),
                new IconItem("联系人", SymbolRegular.Person24),
                new IconItem("搜索", SymbolRegular.Search24),
                new IconItem("收藏", SymbolRegular.Star24)
            };

            AvailableIcons.Clear();
            foreach (var icon in icons)
            {
                AvailableIcons.Add(icon);
            }
        }

        /// <summary>
        /// 添加操作记录
        /// </summary>
        private void AddOperationRecord(string operation)
        {
            try
            {
                OperationHistory.Insert(0, new OperationRecord
                {
                    Timestamp = DateTime.Now,
                    Operation = operation
                });

                // 保持最多50条记录
                while (OperationHistory.Count > 50)
                {
                    OperationHistory.RemoveAt(OperationHistory.Count - 1);
                }
            }
            catch (Exception ex)
            {
                // 记录日志但不抛出异常，避免影响主要功能
                System.Diagnostics.Debug.WriteLine($"添加操作记录失败: {ex.Message}");
            }
        }

        #endregion
    }

    /// <summary>
    /// 图标项模型
    /// </summary>
    public class IconItem
    {
        public string Name { get; set; }
        public SymbolRegular Symbol { get; set; }

        public IconItem(string name, SymbolRegular symbol)
        {
            Name = name;
            Symbol = symbol;
        }
    }

    /// <summary>
    /// 操作记录模型
    /// </summary>
    public class OperationRecord
    {
        public DateTime Timestamp { get; set; }
        public string Operation { get; set; } = string.Empty;
    }
}
