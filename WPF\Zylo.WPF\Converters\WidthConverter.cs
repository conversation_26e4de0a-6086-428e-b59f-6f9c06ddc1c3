using System.Globalization;
using System.Windows.Data;

namespace Zylo.WPF.Converters
{
    /// <summary>
    /// 宽度转换器
    /// </summary>
    /// <remarks>
    /// 用于处理 NavigationControl 的右侧宽度逻辑：
    /// - 输入值 > 0：返回固定宽度
    /// - 输入值 = 0：返回 Double.NaN（自动宽度）
    /// </remarks>
    public class WidthConverter : IValueConverter
    {
        /// <summary>
        /// 将宽度值转换为实际的宽度
        /// </summary>
        /// <param name="value">输入的宽度值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">参数</param>
        /// <param name="culture">文化信息</param>
        /// <returns>转换后的宽度值</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is double width)
            {
                // 如果宽度 > 0，使用固定宽度
                if (width > 0)
                {
                    return width;
                }
                // 如果宽度 = 0，返回 NaN（自动宽度，占满可用空间）
                else
                {
                    return double.NaN;
                }
            }
            
            // 默认返回自动宽度
            return double.NaN;
        }

        /// <summary>
        /// 反向转换（不支持）
        /// </summary>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException("WidthConverter 不支持反向转换");
        }
    }
}
