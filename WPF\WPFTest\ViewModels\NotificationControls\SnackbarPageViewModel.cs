using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Threading;
using Wpf.Ui.Controls;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels.NotificationControls;

/// <summary>
/// Snackbar 页面 ViewModel - 展示 WPF 依赖属性深度解析和 CodeExampleControl 开发
/// </summary>
public partial class SnackbarPageViewModel : ObservableObject
{
    #region 私有字段

    private readonly YLoggerInstance _logger = YLogger.ForDebug<SnackbarPageViewModel>();
    private readonly DispatcherTimer _statusTimer;
    private readonly Queue<SnackbarMessage> _messageQueue = new();
    private int _messageCounter = 0;

    #endregion

    #region 基础属性

    [ObservableProperty]
    private string snackbarTitle = "消息标题";

    [ObservableProperty]
    private string snackbarMessage = "这是一条 Snackbar 消息";

    [ObservableProperty]
    private SymbolRegular snackbarIcon = SymbolRegular.Info24;

    [ObservableProperty]
    private ControlAppearance snackbarAppearance = ControlAppearance.Primary;

    [ObservableProperty]
    private int snackbarTimeout = 3000;

    [ObservableProperty]
    private bool isSnackbarVisible = false;

    [ObservableProperty]
    private bool isAnySnackbarVisible = false;

    [ObservableProperty]
    private string customMessage = "自定义消息内容";

    [ObservableProperty]
    private int customTimeout = 3000;

    #endregion

    #region 依赖属性演示属性

    [ObservableProperty]
    private string demoTitle = "依赖属性演示";

    [ObservableProperty]
    private string demoMessage = "这是依赖属性的实时演示";

    [ObservableProperty]
    private ControlAppearance demoAppearance = ControlAppearance.Secondary;

    [ObservableProperty]
    private int demoTimeout = 4000;

    [ObservableProperty]
    private bool isDemoSnackbarVisible = false;

    [ObservableProperty]
    private string propertyInfo = "修改上方属性值，点击预览按钮查看依赖属性的实时变化效果";

    [ObservableProperty]
    private ObservableCollection<ControlAppearance> availableAppearances = new()
    {
        ControlAppearance.Primary,
        ControlAppearance.Secondary,
        ControlAppearance.Success,
        ControlAppearance.Info,
        ControlAppearance.Caution,
        ControlAppearance.Danger,
        ControlAppearance.Light,
        ControlAppearance.Dark,
        ControlAppearance.Transparent
    };

    #endregion

    #region 高级功能属性

    [ObservableProperty]
    private bool enableQueue = true;

    [ObservableProperty]
    private bool enableAnimation = true;

    [ObservableProperty]
    private bool enableSound = false;

    [ObservableProperty]
    private bool enableAutoClose = true;

    [ObservableProperty]
    private string statusInfo = "就绪 - 等待操作";

    #endregion

    #region 代码示例属性

    [ObservableProperty]
    private string basicXamlExample = string.Empty;

    [ObservableProperty]
    private string basicCSharpExample = string.Empty;

    [ObservableProperty]
    private string dependencyPropertyXamlExample = string.Empty;

    [ObservableProperty]
    private string dependencyPropertyCSharpExample = string.Empty;

    [ObservableProperty]
    private string advancedXamlExample = string.Empty;

    [ObservableProperty]
    private string advancedCSharpExample = string.Empty;

    #endregion

    #region 构造函数

    /// <summary>
    /// 构造函数
    /// </summary>
    public SnackbarPageViewModel()
    {
        try
        {
            _logger.Info("🚀 SnackbarPageViewModel 构造函数开始");

            // 初始化状态定时器
            _statusTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            _statusTimer.Tick += OnStatusTimerTick;

            // 初始化代码示例
            InitializeCodeExamples();

            _logger.Info("✅ SnackbarPageViewModel 构造完成");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ SnackbarPageViewModel 构造失败: {ex}");
        }
    }

    #endregion

    #region 基础控制命令

    [RelayCommand]
    private void ShowInfoSnackbar()
    {
        ShowSnackbar("信息提示", "这是一条信息类型的 Snackbar 消息", SymbolRegular.Info24, ControlAppearance.Info);
        UpdateStatus("显示信息 Snackbar");
        _logger.Info("📢 显示信息类型 Snackbar");
    }

    [RelayCommand]
    private void ShowSuccessSnackbar()
    {
        ShowSnackbar("操作成功", "文件已成功保存到指定位置", SymbolRegular.CheckmarkCircle24, ControlAppearance.Success);
        UpdateStatus("显示成功 Snackbar");
        _logger.Info("✅ 显示成功类型 Snackbar");
    }

    [RelayCommand]
    private void ShowWarningSnackbar()
    {
        ShowSnackbar("警告提示", "磁盘空间不足，建议清理临时文件", SymbolRegular.Warning24, ControlAppearance.Caution);
        UpdateStatus("显示警告 Snackbar");
        _logger.Info("⚠️ 显示警告类型 Snackbar");
    }

    [RelayCommand]
    private void ShowErrorSnackbar()
    {
        ShowSnackbar("错误信息", "网络连接失败，请检查网络设置后重试", SymbolRegular.ErrorCircle24, ControlAppearance.Danger);
        UpdateStatus("显示错误 Snackbar");
        _logger.Info("❌ 显示错误类型 Snackbar");
    }

    [RelayCommand]
    private void ShowCustomSnackbar()
    {
        var message = string.IsNullOrWhiteSpace(CustomMessage) ? "自定义消息内容" : CustomMessage;
        ShowSnackbar("自定义消息", message, SymbolRegular.Settings24, ControlAppearance.Secondary, CustomTimeout);
        UpdateStatus($"显示自定义 Snackbar - 超时: {CustomTimeout}ms");
        _logger.Info($"🔧 显示自定义 Snackbar: {message}");
    }

    [RelayCommand]
    private void HideSnackbar()
    {
        IsSnackbarVisible = false;
        IsDemoSnackbarVisible = false;
        IsAnySnackbarVisible = false;
        UpdateStatus("隐藏 Snackbar");
        _logger.Info("🙈 隐藏 Snackbar");
    }

    #endregion

    #region 依赖属性演示命令

    [RelayCommand]
    private void ShowDemoSnackbar()
    {
        IsDemoSnackbarVisible = true;
        IsAnySnackbarVisible = true;
        
        var info = $"Title: '{DemoTitle}', Message: '{DemoMessage}', Appearance: {DemoAppearance}, Timeout: {DemoTimeout}ms";
        PropertyInfo = $"依赖属性实时更新 - {info}";
        UpdateStatus("显示依赖属性演示 Snackbar");
        _logger.Info($"🔧 依赖属性演示: {info}");

        // 自动隐藏
        Task.Delay(DemoTimeout).ContinueWith(_ =>
        {
            App.Current.Dispatcher.Invoke(() =>
            {
                IsDemoSnackbarVisible = false;
                if (!IsSnackbarVisible)
                {
                    IsAnySnackbarVisible = false;
                }
            });
        });
    }

    #endregion

    #region 高级功能命令

    [RelayCommand]
    private async Task ShowQueueMessages()
    {
        if (!EnableQueue)
        {
            ShowSnackbar("队列功能", "请先启用队列管理功能", SymbolRegular.Warning24, ControlAppearance.Caution);
            return;
        }

        UpdateStatus("显示队列消息测试");
        _logger.Info("📋 开始队列消息测试");

        var messages = new[]
        {
            new SnackbarMessage("队列消息 1", "第一条队列消息", SymbolRegular.Circle24, ControlAppearance.Primary),
            new SnackbarMessage("队列消息 2", "第二条队列消息", SymbolRegular.Circle24, ControlAppearance.Secondary),
            new SnackbarMessage("队列消息 3", "第三条队列消息", SymbolRegular.Circle24, ControlAppearance.Success),
        };

        foreach (var msg in messages)
        {
            ShowSnackbar(msg.Title, msg.Message, msg.Icon, msg.Appearance);
            await Task.Delay(1500); // 队列间隔
        }
    }

    [RelayCommand]
    private void ShowLongMessage()
    {
        var longMessage = "这是一条很长的消息内容，用于测试 Snackbar 控件在处理长文本时的显示效果和自动换行功能。" +
                         "消息内容可能包含多行文本，需要确保用户界面能够正确显示和处理这些内容。" +
                         "同时也要考虑到不同屏幕尺寸下的显示效果。";
        
        ShowSnackbar("长文本消息", longMessage, SymbolRegular.Document24, ControlAppearance.Info, 6000);
        UpdateStatus("显示长文本消息");
        _logger.Info("📄 显示长文本消息");
    }

    [RelayCommand]
    private void ShowActionSnackbar()
    {
        ShowSnackbar("操作确认", "文件已删除，是否要撤销此操作？", SymbolRegular.Delete24, ControlAppearance.Caution, 8000);
        UpdateStatus("显示带操作按钮的 Snackbar");
        _logger.Info("🎯 显示带操作按钮的 Snackbar");
    }

    [RelayCommand]
    private void ShowCustomStyle()
    {
        ShowSnackbar("自定义样式", "这是一个自定义样式的 Snackbar 消息", SymbolRegular.ColorBackground24, ControlAppearance.Dark);
        UpdateStatus("显示自定义样式 Snackbar");
        _logger.Info("🎨 显示自定义样式 Snackbar");
    }

    [RelayCommand]
    private void ClearQueue()
    {
        _messageQueue.Clear();
        HideSnackbar();
        UpdateStatus("已清空消息队列");
        _logger.Info("🗑️ 清空消息队列");
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 显示 Snackbar
    /// </summary>
    private void ShowSnackbar(string title, string message, SymbolRegular icon, ControlAppearance appearance, int timeout = 3000)
    {
        SnackbarTitle = title;
        SnackbarMessage = message;
        SnackbarIcon = icon;
        SnackbarAppearance = appearance;
        SnackbarTimeout = timeout;
        IsSnackbarVisible = true;
        IsAnySnackbarVisible = true;

        _messageCounter++;

        // 自动隐藏
        if (EnableAutoClose)
        {
            Task.Delay(timeout).ContinueWith(_ =>
            {
                App.Current.Dispatcher.Invoke(() =>
                {
                    IsSnackbarVisible = false;
                    if (!IsDemoSnackbarVisible)
                    {
                        IsAnySnackbarVisible = false;
                    }
                });
            });
        }
    }

    /// <summary>
    /// 更新状态信息
    /// </summary>
    private void UpdateStatus(string status)
    {
        StatusInfo = $"{DateTime.Now:HH:mm:ss} - {status} (消息计数: {_messageCounter})";
        
        // 重启状态定时器
        _statusTimer.Stop();
        _statusTimer.Start();
    }

    /// <summary>
    /// 状态定时器回调
    /// </summary>
    private void OnStatusTimerTick(object? sender, EventArgs e)
    {
        _statusTimer.Stop();
        StatusInfo = $"就绪 - 总消息数: {_messageCounter}";
    }

    #endregion

    #region 代码示例初始化

    /// <summary>
    /// 初始化代码示例
    /// </summary>
    private void InitializeCodeExamples()
    {
        // 基础 XAML 示例
        BasicXamlExample = @"<!-- Snackbar 基础用法 -->
<ui:Snackbar x:Name=""BasicSnackbar""
             Title=""{Binding SnackbarTitle}""
             Message=""{Binding SnackbarMessage}""
             Icon=""{Binding SnackbarIcon}""
             Appearance=""{Binding SnackbarAppearance}""
             Timeout=""{Binding SnackbarTimeout}""
             IsShown=""{Binding IsSnackbarVisible}""/>

<!-- 控制按钮 -->
<ui:Button Content=""显示消息""
           Icon=""{ui:SymbolIcon Info24}""
           Command=""{Binding ShowInfoSnackbarCommand}""/>";

        // 基础 C# 示例
        BasicCSharpExample = @"// Snackbar 基础控制 - 使用 CommunityToolkit.Mvvm
public partial class SnackbarPageViewModel : ObservableObject
{
    [ObservableProperty]
    private string snackbarTitle = ""消息标题"";

    [ObservableProperty]
    private string snackbarMessage = ""这是一条 Snackbar 消息"";

    [ObservableProperty]
    private ControlAppearance snackbarAppearance = ControlAppearance.Primary;

    [ObservableProperty]
    private bool isSnackbarVisible = false;

    [RelayCommand]
    private void ShowInfoSnackbar()
    {
        SnackbarTitle = ""信息提示"";
        SnackbarMessage = ""这是一条信息类型的消息"";
        SnackbarAppearance = ControlAppearance.Info;
        IsSnackbarVisible = true;
    }
}";

        // 依赖属性 XAML 示例
        DependencyPropertyXamlExample = @"<!-- 自定义 Snackbar 控件的依赖属性使用 -->
<local:CustomSnackbar x:Name=""CustomSnackbar""
                      Title=""{Binding CustomTitle}""
                      Message=""{Binding CustomMessage}""
                      AutoHideDelay=""{Binding AutoHideDelay}""
                      ShowCloseButton=""{Binding ShowCloseButton}""
                      CustomIcon=""{Binding CustomIcon}""
                      IsAnimated=""{Binding IsAnimated}""
                      Position=""{Binding Position}""
                      MaxWidth=""{Binding MaxWidth}""
                      CornerRadius=""{Binding CornerRadius}""
                      BackgroundOpacity=""{Binding BackgroundOpacity}""/>

<!-- 依赖属性绑定示例 -->
<StackPanel>
    <TextBox Text=""{Binding CustomTitle, Mode=TwoWay}""
             PlaceholderText=""标题""/>
    <TextBox Text=""{Binding CustomMessage, Mode=TwoWay}""
             PlaceholderText=""消息内容""/>
    <Slider Value=""{Binding AutoHideDelay, Mode=TwoWay}""
            Minimum=""1000"" Maximum=""10000""/>
    <CheckBox IsChecked=""{Binding ShowCloseButton, Mode=TwoWay}""
              Content=""显示关闭按钮""/>
</StackPanel>";

        // 依赖属性 C# 示例
        DependencyPropertyCSharpExample = @"// 自定义 Snackbar 控件 - 依赖属性深度实现
public class CustomSnackbar : Control
{
    #region 依赖属性定义

    // 1. 基础依赖属性 - Title
    public static readonly DependencyProperty TitleProperty =
        DependencyProperty.Register(
            nameof(Title),                    // 属性名
            typeof(string),                   // 属性类型
            typeof(CustomSnackbar),           // 所有者类型
            new PropertyMetadata(             // 属性元数据
                string.Empty,                 // 默认值
                OnTitleChanged,               // 属性变化回调
                CoerceTitleValue              // 值强制回调
            ),
            ValidateTitleValue                // 值验证回调
        );

    public string Title
    {
        get => (string)GetValue(TitleProperty);
        set => SetValue(TitleProperty, value);
    }

    // 2. 带验证的依赖属性 - AutoHideDelay
    public static readonly DependencyProperty AutoHideDelayProperty =
        YDependencyPropertyHelper.Create<int, CustomSnackbar>()
            .WithName(nameof(AutoHideDelay))
            .WithDefaultValue(3000)
            .WithPropertyChanged(OnAutoHideDelayChanged)
            .WithCoerceValue(CoerceAutoHideDelay)
            .WithValidateValue(ValidateAutoHideDelay)
            .Build();

    public int AutoHideDelay
    {
        get => (int)GetValue(AutoHideDelayProperty);
        set => SetValue(AutoHideDelayProperty, value);
    }

    // 3. 只读依赖属性 - IsShowing
    private static readonly DependencyPropertyKey IsShowingPropertyKey =
        DependencyProperty.RegisterReadOnly(
            nameof(IsShowing),
            typeof(bool),
            typeof(CustomSnackbar),
            new PropertyMetadata(false, OnIsShowingChanged));

    public static readonly DependencyProperty IsShowingProperty =
        IsShowingPropertyKey.DependencyProperty;

    public bool IsShowing
    {
        get => (bool)GetValue(IsShowingProperty);
        private set => SetValue(IsShowingPropertyKey, value);
    }

    // 4. 附加依赖属性 - Position
    public static readonly DependencyProperty PositionProperty =
        DependencyProperty.RegisterAttached(
            ""Position"",
            typeof(SnackbarPosition),
            typeof(CustomSnackbar),
            new PropertyMetadata(SnackbarPosition.Bottom));

    public static SnackbarPosition GetPosition(DependencyObject obj) =>
        (SnackbarPosition)obj.GetValue(PositionProperty);

    public static void SetPosition(DependencyObject obj, SnackbarPosition value) =>
        obj.SetValue(PositionProperty, value);

    #endregion

    #region 属性变化回调

    // Title 属性变化回调
    private static void OnTitleChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is CustomSnackbar snackbar)
        {
            var oldTitle = (string)e.OldValue;
            var newTitle = (string)e.NewValue;

            snackbar.OnTitleChanged(oldTitle, newTitle);
        }
    }

    protected virtual void OnTitleChanged(string oldTitle, string newTitle)
    {
        // 触发标题变化事件
        TitleChanged?.Invoke(this, new PropertyChangedEventArgs<string>(oldTitle, newTitle));

        // 更新 UI
        UpdateTitleDisplay();
    }

    // AutoHideDelay 属性变化回调
    private static void OnAutoHideDelayChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is CustomSnackbar snackbar)
        {
            snackbar.RestartAutoHideTimer();
        }
    }

    #endregion

    #region 值强制和验证

    // Title 值强制回调
    private static object CoerceTitleValue(DependencyObject d, object baseValue)
    {
        var title = (string)baseValue;

        // 限制标题长度
        if (title?.Length > 50)
        {
            return title.Substring(0, 50) + ""..."";
        }

        return title ?? string.Empty;
    }

    // Title 值验证回调
    private static bool ValidateTitleValue(object value)
    {
        // 验证标题不能为 null
        return value is string;
    }

    // AutoHideDelay 值强制回调
    private static object CoerceAutoHideDelay(DependencyObject d, object baseValue)
    {
        var delay = (int)baseValue;

        // 限制延迟时间范围
        return Math.Max(1000, Math.Min(30000, delay));
    }

    // AutoHideDelay 值验证回调
    private static bool ValidateAutoHideDelay(object value)
    {
        return value is int delay && delay > 0;
    }

    #endregion

    #region 事件定义

    // 标题变化事件
    public event EventHandler<PropertyChangedEventArgs<string>>? TitleChanged;

    // 自动隐藏事件
    public event EventHandler? AutoHideCompleted;

    #endregion
}

// 辅助类 - 属性变化事件参数
public class PropertyChangedEventArgs<T> : EventArgs
{
    public T OldValue { get; }
    public T NewValue { get; }

    public PropertyChangedEventArgs(T oldValue, T newValue)
    {
        OldValue = oldValue;
        NewValue = newValue;
    }
}

// 枚举 - Snackbar 位置
public enum SnackbarPosition
{
    Top,
    Bottom,
    Left,
    Right,
    Center
}";

        // 高级功能 XAML 示例
        AdvancedXamlExample = @"<!-- 高级 Snackbar 功能演示 -->
<Grid>
    <!-- Snackbar 容器 -->
    <local:SnackbarContainer x:Name=""SnackbarContainer""
                             MaxQueueSize=""{Binding MaxQueueSize}""
                             EnableAnimation=""{Binding EnableAnimation}""
                             EnableSound=""{Binding EnableSound}""
                             QueueMode=""{Binding QueueMode}""
                             Position=""{Binding ContainerPosition}"">

        <!-- 自定义 Snackbar 模板 -->
        <local:SnackbarContainer.SnackbarTemplate>
            <DataTemplate>
                <Border Background=""{Binding BackgroundBrush}""
                        CornerRadius=""8""
                        Padding=""16""
                        Margin=""8"">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width=""Auto""/>
                            <ColumnDefinition Width=""*""/>
                            <ColumnDefinition Width=""Auto""/>
                        </Grid.ColumnDefinitions>

                        <!-- 图标 -->
                        <ui:SymbolIcon Grid.Column=""0""
                                       Symbol=""{Binding Icon}""
                                       FontSize=""20""
                                       Margin=""0,0,12,0""/>

                        <!-- 内容 -->
                        <StackPanel Grid.Column=""1"">
                            <TextBlock Text=""{Binding Title}""
                                       FontWeight=""Bold""
                                       Margin=""0,0,0,4""/>
                            <TextBlock Text=""{Binding Message}""
                                       TextWrapping=""Wrap""/>
                        </StackPanel>

                        <!-- 操作按钮 -->
                        <StackPanel Grid.Column=""2""
                                    Orientation=""Horizontal""
                                    Margin=""12,0,0,0"">
                            <ui:Button Content=""操作""
                                       Command=""{Binding ActionCommand}""
                                       Appearance=""Secondary""
                                       Padding=""8,4""
                                       Margin=""0,0,8,0""
                                       Visibility=""{Binding HasAction, Converter={StaticResource BooleanToVisibilityConverter}}""/>
                            <ui:Button Content=""✕""
                                       Command=""{Binding CloseCommand}""
                                       Appearance=""Transparent""
                                       Padding=""4""
                                       FontSize=""12""/>
                        </StackPanel>
                    </Grid>
                </Border>
            </DataTemplate>
        </local:SnackbarContainer.SnackbarTemplate>
    </local:SnackbarContainer>

    <!-- 队列状态显示 -->
    <Border Background=""{DynamicResource LayerFillColorDefaultBrush}""
            CornerRadius=""4""
            Padding=""8""
            HorizontalAlignment=""Right""
            VerticalAlignment=""Top""
            Margin=""16"">
        <StackPanel Orientation=""Horizontal"">
            <TextBlock Text=""队列: ""/>
            <TextBlock Text=""{Binding QueueCount}""/>
            <TextBlock Text=""/""/>
            <TextBlock Text=""{Binding MaxQueueSize}""/>
        </StackPanel>
    </Border>
</Grid>";

        // 高级功能 C# 示例
        AdvancedCSharpExample = @"// 高级 Snackbar 功能实现
public partial class AdvancedSnackbarViewModel : ObservableObject
{
    private readonly Queue<SnackbarMessage> _messageQueue = new();
    private readonly DispatcherTimer _queueTimer;
    private readonly SoundPlayer _soundPlayer;

    [ObservableProperty]
    private bool enableQueue = true;

    [ObservableProperty]
    private bool enableAnimation = true;

    [ObservableProperty]
    private bool enableSound = false;

    [ObservableProperty]
    private int maxQueueSize = 5;

    [ObservableProperty]
    private int queueCount = 0;

    public AdvancedSnackbarViewModel()
    {
        // 初始化队列定时器
        _queueTimer = new DispatcherTimer
        {
            Interval = TimeSpan.FromMilliseconds(500)
        };
        _queueTimer.Tick += ProcessMessageQueue;

        // 初始化声音播放器
        _soundPlayer = new SoundPlayer();
    }

    [RelayCommand]
    private async Task ShowQueueMessages()
    {
        var messages = new[]
        {
            new SnackbarMessage(""消息 1"", ""第一条队列消息"", SymbolRegular.Number1Circle24, ControlAppearance.Primary),
            new SnackbarMessage(""消息 2"", ""第二条队列消息"", SymbolRegular.Number2Circle24, ControlAppearance.Secondary),
            new SnackbarMessage(""消息 3"", ""第三条队列消息"", SymbolRegular.Number3Circle24, ControlAppearance.Success),
        };

        foreach (var message in messages)
        {
            await EnqueueMessage(message);
        }
    }

    private async Task EnqueueMessage(SnackbarMessage message)
    {
        if (!EnableQueue)
        {
            await ShowMessage(message);
            return;
        }

        if (_messageQueue.Count >= MaxQueueSize)
        {
            // 队列已满，移除最旧的消息
            _messageQueue.Dequeue();
        }

        _messageQueue.Enqueue(message);
        QueueCount = _messageQueue.Count;

        // 启动队列处理
        if (!_queueTimer.IsEnabled)
        {
            _queueTimer.Start();
        }
    }

    private void ProcessMessageQueue(object? sender, EventArgs e)
    {
        if (_messageQueue.Count == 0)
        {
            _queueTimer.Stop();
            return;
        }

        var message = _messageQueue.Dequeue();
        QueueCount = _messageQueue.Count;

        _ = ShowMessage(message);
    }

    private async Task ShowMessage(SnackbarMessage message)
    {
        // 播放声音提示
        if (EnableSound)
        {
            PlayNotificationSound(message.Appearance);
        }

        // 显示消息
        await DisplaySnackbar(message);

        // 启用动画效果
        if (EnableAnimation)
        {
            await AnimateSnackbar(message);
        }
    }

    private void PlayNotificationSound(ControlAppearance appearance)
    {
        try
        {
            var soundFile = appearance switch
            {
                ControlAppearance.Success => ""success.wav"",
                ControlAppearance.Warning => ""warning.wav"",
                ControlAppearance.Danger => ""error.wav"",
                _ => ""info.wav""
            };

            _soundPlayer.SoundLocation = $""Sounds/{soundFile}"";
            _soundPlayer.Play();
        }
        catch (Exception ex)
        {
            // 声音播放失败，记录日志但不影响功能
            Debug.WriteLine($""播放声音失败: {ex.Message}"");
        }
    }

    private async Task DisplaySnackbar(SnackbarMessage message)
    {
        // 实现 Snackbar 显示逻辑
        // 这里可以使用自定义的 Snackbar 控件或第三方库
        await Task.Delay(100); // 模拟显示延迟
    }

    private async Task AnimateSnackbar(SnackbarMessage message)
    {
        // 实现动画效果
        // 可以使用 WPF 的 Storyboard 或第三方动画库
        await Task.Delay(300); // 模拟动画时间
    }
}

// Snackbar 消息模型
public record SnackbarMessage(
    string Title,
    string Message,
    SymbolRegular Icon,
    ControlAppearance Appearance,
    int Timeout = 3000,
    bool HasAction = false,
    ICommand? ActionCommand = null,
    string ActionText = ""操作"");

// Snackbar 队列模式
public enum QueueMode
{
    FIFO,    // 先进先出
    LIFO,    // 后进先出
    Priority // 按优先级排序
}";
    }

    #endregion
}

/// <summary>
/// Snackbar 消息模型
/// </summary>
public record SnackbarMessage(string Title, string Message, SymbolRegular Icon, ControlAppearance Appearance);
