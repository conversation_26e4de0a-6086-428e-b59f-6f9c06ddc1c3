// Image 特效和样式 C# 代码示例
using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace WPFTest.ViewModels.MediaControls
{
    public partial class ImagePageViewModel : ObservableObject
    {
        /// <summary>
        /// 当前特效类型
        /// </summary>
        [ObservableProperty]
        public partial string CurrentEffectType { get; set; } = "None";

        /// <summary>
        /// 模糊半径
        /// </summary>
        [ObservableProperty]
        public partial double BlurRadius { get; set; } = 5.0;

        /// <summary>
        /// 阴影深度
        /// </summary>
        [ObservableProperty]
        public partial double ShadowDepth { get; set; } = 5.0;

        /// <summary>
        /// 阴影方向
        /// </summary>
        [ObservableProperty]
        public partial double ShadowDirection { get; set; } = 315.0;

        /// <summary>
        /// 阴影透明度
        /// </summary>
        [ObservableProperty]
        public partial double ShadowOpacity { get; set; } = 0.5;

        /// <summary>
        /// 是否启用动画
        /// </summary>
        [ObservableProperty]
        public partial bool IsAnimationEnabled { get; set; } = true;

        /// <summary>
        /// 动画持续时间（秒）
        /// </summary>
        [ObservableProperty]
        public partial double AnimationDuration { get; set; } = 2.0;

        /// <summary>
        /// 应用模糊效果命令
        /// </summary>
        [RelayCommand]
        private void ApplyBlurEffect(Image targetImage)
        {
            if (targetImage == null) return;

            var blurEffect = new BlurEffect
            {
                Radius = BlurRadius
            };

            targetImage.Effect = blurEffect;
            CurrentEffectType = "Blur";
            StatusMessage = $"已应用模糊效果，半径: {BlurRadius}";
        }

        /// <summary>
        /// 应用阴影效果命令
        /// </summary>
        [RelayCommand]
        private void ApplyDropShadowEffect(Image targetImage)
        {
            if (targetImage == null) return;

            var shadowEffect = new DropShadowEffect
            {
                Color = Colors.Black,
                Direction = ShadowDirection,
                ShadowDepth = ShadowDepth,
                Opacity = ShadowOpacity,
                BlurRadius = 3
            };

            targetImage.Effect = shadowEffect;
            CurrentEffectType = "DropShadow";
            StatusMessage = $"已应用阴影效果，深度: {ShadowDepth}，方向: {ShadowDirection}°";
        }

        /// <summary>
        /// 清除所有效果命令
        /// </summary>
        [RelayCommand]
        private void ClearEffects(Image targetImage)
        {
            if (targetImage == null) return;

            targetImage.Effect = null;
            CurrentEffectType = "None";
            StatusMessage = "已清除所有特效";
        }

        /// <summary>
        /// 开始旋转动画命令
        /// </summary>
        [RelayCommand]
        private void StartRotationAnimation(Image targetImage)
        {
            if (targetImage == null || !IsAnimationEnabled) return;

            var rotateTransform = new RotateTransform();
            targetImage.RenderTransform = rotateTransform;
            targetImage.RenderTransformOrigin = new Point(0.5, 0.5);

            var animation = new DoubleAnimation
            {
                From = 0,
                To = 360,
                Duration = TimeSpan.FromSeconds(AnimationDuration),
                RepeatBehavior = RepeatBehavior.Forever
            };

            rotateTransform.BeginAnimation(RotateTransform.AngleProperty, animation);
            StatusMessage = $"已开始旋转动画，持续时间: {AnimationDuration}秒";
        }

        /// <summary>
        /// 开始缩放动画命令
        /// </summary>
        [RelayCommand]
        private void StartScaleAnimation(Image targetImage)
        {
            if (targetImage == null || !IsAnimationEnabled) return;

            var scaleTransform = new ScaleTransform();
            targetImage.RenderTransform = scaleTransform;
            targetImage.RenderTransformOrigin = new Point(0.5, 0.5);

            var scaleXAnimation = new DoubleAnimation
            {
                From = 1.0,
                To = 1.2,
                Duration = TimeSpan.FromSeconds(AnimationDuration / 2),
                AutoReverse = true,
                RepeatBehavior = RepeatBehavior.Forever
            };

            var scaleYAnimation = new DoubleAnimation
            {
                From = 1.0,
                To = 1.2,
                Duration = TimeSpan.FromSeconds(AnimationDuration / 2),
                AutoReverse = true,
                RepeatBehavior = RepeatBehavior.Forever
            };

            scaleTransform.BeginAnimation(ScaleTransform.ScaleXProperty, scaleXAnimation);
            scaleTransform.BeginAnimation(ScaleTransform.ScaleYProperty, scaleYAnimation);
            StatusMessage = $"已开始缩放动画，持续时间: {AnimationDuration}秒";
        }

        /// <summary>
        /// 开始透明度动画命令
        /// </summary>
        [RelayCommand]
        private void StartOpacityAnimation(Image targetImage)
        {
            if (targetImage == null || !IsAnimationEnabled) return;

            var opacityAnimation = new DoubleAnimation
            {
                From = 1.0,
                To = 0.3,
                Duration = TimeSpan.FromSeconds(AnimationDuration),
                AutoReverse = true,
                RepeatBehavior = RepeatBehavior.Forever
            };

            targetImage.BeginAnimation(UIElement.OpacityProperty, opacityAnimation);
            StatusMessage = $"已开始透明度动画，持续时间: {AnimationDuration}秒";
        }

        /// <summary>
        /// 停止所有动画命令
        /// </summary>
        [RelayCommand]
        private void StopAllAnimations(Image targetImage)
        {
            if (targetImage == null) return;

            // 停止变换动画
            if (targetImage.RenderTransform is RotateTransform rotateTransform)
            {
                rotateTransform.BeginAnimation(RotateTransform.AngleProperty, null);
            }
            else if (targetImage.RenderTransform is ScaleTransform scaleTransform)
            {
                scaleTransform.BeginAnimation(ScaleTransform.ScaleXProperty, null);
                scaleTransform.BeginAnimation(ScaleTransform.ScaleYProperty, null);
            }

            // 停止透明度动画
            targetImage.BeginAnimation(UIElement.OpacityProperty, null);

            // 重置变换
            targetImage.RenderTransform = null;
            targetImage.Opacity = 1.0;

            StatusMessage = "已停止所有动画";
        }

        /// <summary>
        /// 设置动画参数命令
        /// </summary>
        [RelayCommand]
        private void SetAnimationParameters(string parameters)
        {
            var parts = parameters.Split(',');
            if (parts.Length >= 2)
            {
                if (double.TryParse(parts[0], out double duration))
                {
                    AnimationDuration = Math.Max(0.1, Math.Min(10.0, duration));
                }

                if (bool.TryParse(parts[1], out bool enabled))
                {
                    IsAnimationEnabled = enabled;
                }

                StatusMessage = $"动画参数已更新 - 持续时间: {AnimationDuration}秒, 启用: {IsAnimationEnabled}";
            }
        }

        /// <summary>
        /// 创建复合效果命令
        /// </summary>
        [RelayCommand]
        private void CreateCompositeEffect(Image targetImage)
        {
            if (targetImage == null) return;

            // 创建效果组合
            var effectGroup = new BitmapEffectGroup();
            
            // 添加模糊效果
            effectGroup.Children.Add(new BlurBitmapEffect { Radius = BlurRadius / 2 });
            
            // 添加阴影效果
            effectGroup.Children.Add(new DropShadowBitmapEffect
            {
                Color = Colors.Black,
                Direction = ShadowDirection,
                ShadowDepth = ShadowDepth,
                Opacity = ShadowOpacity
            });

            targetImage.BitmapEffect = effectGroup;
            CurrentEffectType = "Composite";
            StatusMessage = "已应用复合特效（模糊 + 阴影）";
        }
    }
}
