<!-- 高级 ProgressRing 示例 -->
<UserControl x:Class="WPFTest.Examples.AdvancedProgressRingExample"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:converters="clr-namespace:Zylo.WPF.Converters;assembly=Zylo.WPF">

    <UserControl.Resources>
        <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
        
        <!-- 自定义进度环样式 -->
        <Style x:Key="CustomProgressRingStyle" TargetType="ui:ProgressRing">
            <Setter Property="Foreground" Value="{DynamicResource SystemAccentColorPrimaryBrush}" />
            <Setter Property="Background" Value="{DynamicResource ControlStrokeColorDefaultBrush}" />
        </Style>
        
        <!-- 成功状态样式 -->
        <Style x:Key="SuccessProgressRingStyle" TargetType="ui:ProgressRing" BasedOn="{StaticResource CustomProgressRingStyle}">
            <Setter Property="Foreground" Value="#28A745" />
        </Style>
        
        <!-- 警告状态样式 -->
        <Style x:Key="WarningProgressRingStyle" TargetType="ui:ProgressRing" BasedOn="{StaticResource CustomProgressRingStyle}">
            <Setter Property="Foreground" Value="#FFC107" />
        </Style>
        
        <!-- 错误状态样式 -->
        <Style x:Key="ErrorProgressRingStyle" TargetType="ui:ProgressRing" BasedOn="{StaticResource CustomProgressRingStyle}">
            <Setter Property="Foreground" Value="#DC3545" />
        </Style>
    </UserControl.Resources>

    <ScrollViewer>
        <StackPanel Margin="20" Spacing="20">
            
            <!-- 标题 -->
            <TextBlock Text="高级 ProgressRing 示例" 
                       FontSize="20" 
                       FontWeight="Bold" />
            
            <!-- 控制面板 -->
            <ui:Card Padding="16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>
                    
                    <!-- 基础控制 -->
                    <StackPanel Grid.Column="0" Spacing="8">
                        <TextBlock Text="基础控制" FontWeight="SemiBold" />
                        <CheckBox Content="不确定模式" 
                                 IsChecked="{Binding IsIndeterminate}" />
                        <CheckBox Content="显示进度文本" 
                                 IsChecked="{Binding ShowProgressText}" />
                        <Button Content="开始模拟" 
                               Command="{Binding StartSimulationCommand}" />
                        <Button Content="停止模拟" 
                               Command="{Binding StopSimulationCommand}" />
                        <Button Content="重置" 
                               Command="{Binding ResetCommand}" />
                    </StackPanel>
                    
                    <!-- 进度控制 -->
                    <StackPanel Grid.Column="1" Spacing="8" Margin="16,0">
                        <TextBlock Text="进度控制" FontWeight="SemiBold" />
                        <TextBlock Text="当前进度:" />
                        <ui:NumberBox Value="{Binding ProgressValue}" 
                                     Maximum="{Binding MaximumValue}"
                                     Minimum="0"
                                     IsEnabled="{Binding IsIndeterminate, Converter={converters:InverseBooleanConverter}}" />
                        <TextBlock Text="最大值:" />
                        <ui:NumberBox Value="{Binding MaximumValue}" 
                                     Maximum="1000"
                                     Minimum="1" />
                        <Slider Value="{Binding ProgressValue}" 
                               Maximum="{Binding MaximumValue}"
                               IsEnabled="{Binding IsIndeterminate, Converter={converters:InverseBooleanConverter}}" />
                    </StackPanel>
                    
                    <!-- 外观控制 -->
                    <StackPanel Grid.Column="2" Spacing="8">
                        <TextBlock Text="外观控制" FontWeight="SemiBold" />
                        <TextBlock Text="大小:" />
                        <ui:NumberBox Value="{Binding RingSize}" 
                                     Maximum="200"
                                     Minimum="16" />
                        <TextBlock Text="厚度:" />
                        <ui:NumberBox Value="{Binding StrokeThickness}" 
                                     Maximum="20"
                                     Minimum="1" />
                        <ComboBox SelectedItem="{Binding SelectedStyle}"
                                 ItemsSource="{Binding StyleOptions}" />
                    </StackPanel>
                </Grid>
            </ui:Card>
            
            <!-- 主要演示区域 -->
            <ui:Card Padding="32">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    
                    <!-- 进度环展示 -->
                    <StackPanel Grid.Row="0" 
                               HorizontalAlignment="Center" 
                               VerticalAlignment="Center"
                               Spacing="16">
                        
                        <!-- 主进度环 -->
                        <ui:ProgressRing x:Name="MainProgressRing"
                                        Width="{Binding RingSize}"
                                        Height="{Binding RingSize}"
                                        IsIndeterminate="{Binding IsIndeterminate}"
                                        Progress="{Binding ProgressValue}"
                                        Style="{Binding SelectedProgressRingStyle}" />
                        
                        <!-- 进度文本 -->
                        <TextBlock Text="{Binding ProgressText}"
                                  FontSize="18"
                                  FontWeight="SemiBold"
                                  HorizontalAlignment="Center"
                                  Visibility="{Binding ShowProgressText, Converter={StaticResource BooleanToVisibilityConverter}}" />
                        
                        <!-- 状态信息 -->
                        <TextBlock Text="{Binding StatusMessage}"
                                  FontSize="14"
                                  HorizontalAlignment="Center"
                                  Foreground="{DynamicResource TextFillColorSecondaryBrush}" />
                    </StackPanel>
                    
                    <!-- 快速设置按钮 -->
                    <StackPanel Grid.Row="1" 
                               Orientation="Horizontal" 
                               HorizontalAlignment="Center"
                               Spacing="8"
                               Margin="0,16,0,0">
                        <ui:Button Content="0%" 
                                  Command="{Binding SetProgressCommand}" 
                                  CommandParameter="0" />
                        <ui:Button Content="25%" 
                                  Command="{Binding SetProgressCommand}" 
                                  CommandParameter="25" />
                        <ui:Button Content="50%" 
                                  Command="{Binding SetProgressCommand}" 
                                  CommandParameter="50" />
                        <ui:Button Content="75%" 
                                  Command="{Binding SetProgressCommand}" 
                                  CommandParameter="75" />
                        <ui:Button Content="100%" 
                                  Command="{Binding SetProgressCommand}" 
                                  CommandParameter="100" />
                    </StackPanel>
                </Grid>
            </ui:Card>
            
            <!-- 多样式展示 -->
            <GroupBox Header="不同样式的进度环">
                <UniformGrid Columns="4" Rows="2">
                    
                    <!-- 默认样式 -->
                    <StackPanel HorizontalAlignment="Center" Spacing="8" Margin="8">
                        <ui:ProgressRing IsIndeterminate="False"
                                        Width="48"
                                        Height="48"
                                        Progress="30" />
                        <TextBlock Text="默认 (30%)"
                                  HorizontalAlignment="Center"
                                  FontSize="12" />
                    </StackPanel>

                    <!-- 成功样式 -->
                    <StackPanel HorizontalAlignment="Center" Spacing="8" Margin="8">
                        <ui:ProgressRing IsIndeterminate="False"
                                        Width="48"
                                        Height="48"
                                        Progress="100"
                                        Style="{StaticResource SuccessProgressRingStyle}" />
                        <TextBlock Text="成功 (100%)"
                                  HorizontalAlignment="Center"
                                  FontSize="12" />
                    </StackPanel>

                    <!-- 警告样式 -->
                    <StackPanel HorizontalAlignment="Center" Spacing="8" Margin="8">
                        <ui:ProgressRing IsIndeterminate="False"
                                        Width="48"
                                        Height="48"
                                        Progress="65"
                                        Style="{StaticResource WarningProgressRingStyle}" />
                        <TextBlock Text="警告 (65%)"
                                  HorizontalAlignment="Center"
                                  FontSize="12" />
                    </StackPanel>

                    <!-- 错误样式 -->
                    <StackPanel HorizontalAlignment="Center" Spacing="8" Margin="8">
                        <ui:ProgressRing IsIndeterminate="False"
                                        Width="48"
                                        Height="48"
                                        Progress="15"
                                        Style="{StaticResource ErrorProgressRingStyle}" />
                        <TextBlock Text="错误 (15%)"
                                  HorizontalAlignment="Center"
                                  FontSize="12" />
                    </StackPanel>
                    
                    <!-- 小型进度环 -->
                    <StackPanel HorizontalAlignment="Center" Spacing="8" Margin="8">
                        <ui:ProgressRing IsIndeterminate="True" 
                                        Width="24" 
                                        Height="24" />
                        <TextBlock Text="小型 (24px)" 
                                  HorizontalAlignment="Center" 
                                  FontSize="12" />
                    </StackPanel>
                    
                    <!-- 中型进度环 -->
                    <StackPanel HorizontalAlignment="Center" Spacing="8" Margin="8">
                        <ui:ProgressRing IsIndeterminate="True" 
                                        Width="48" 
                                        Height="48" />
                        <TextBlock Text="中型 (48px)" 
                                  HorizontalAlignment="Center" 
                                  FontSize="12" />
                    </StackPanel>
                    
                    <!-- 大型进度环 -->
                    <StackPanel HorizontalAlignment="Center" Spacing="8" Margin="8">
                        <ui:ProgressRing IsIndeterminate="True" 
                                        Width="72" 
                                        Height="72" />
                        <TextBlock Text="大型 (72px)" 
                                  HorizontalAlignment="Center" 
                                  FontSize="12" />
                    </StackPanel>
                    
                    <!-- 超大进度环 -->
                    <StackPanel HorizontalAlignment="Center" Spacing="8" Margin="8">
                        <ui:ProgressRing IsIndeterminate="True" 
                                        Width="96" 
                                        Height="96" />
                        <TextBlock Text="超大 (96px)" 
                                  HorizontalAlignment="Center" 
                                  FontSize="12" />
                    </StackPanel>
                    
                </UniformGrid>
            </GroupBox>
            
            <!-- 实际应用场景 -->
            <GroupBox Header="实际应用场景">
                <StackPanel Spacing="16">
                    
                    <!-- 文件上传场景 -->
                    <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                            CornerRadius="8"
                            Padding="16">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            
                            <ui:ProgressRing Grid.Column="0"
                                            IsIndeterminate="False"
                                            Width="32"
                                            Height="32"
                                            Progress="80" />
                            
                            <StackPanel Grid.Column="1" Margin="12,0">
                                <TextBlock Text="正在上传文件..." FontWeight="SemiBold" />
                                <TextBlock Text="document.pdf (2.5MB / 3.8MB)" 
                                          FontSize="12"
                                          Foreground="{DynamicResource TextFillColorSecondaryBrush}" />
                            </StackPanel>
                            
                            <TextBlock Grid.Column="2" 
                                      Text="65%" 
                                      VerticalAlignment="Center"
                                      FontWeight="SemiBold" />
                        </Grid>
                    </Border>
                    
                    <!-- 数据加载场景 -->
                    <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                            CornerRadius="8"
                            Padding="16">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            
                            <ui:ProgressRing Grid.Column="0"
                                            IsIndeterminate="True"
                                            Width="24"
                                            Height="24" />
                            
                            <TextBlock Grid.Column="1" 
                                      Margin="12,0"
                                      Text="正在加载数据，请稍候..."
                                      VerticalAlignment="Center" />
                        </Grid>
                    </Border>
                    
                </StackPanel>
            </GroupBox>
            
        </StackPanel>
    </ScrollViewer>
</UserControl>
