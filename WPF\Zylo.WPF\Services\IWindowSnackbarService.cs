using System.Windows;
using Wpf.Ui.Controls;

namespace Zylo.WPF.Services;

/// <summary>
/// 多窗口安全的 Snackbar 服务接口
/// </summary>
/// <remarks>
/// 🎯 设计目标：
/// - 支持多窗口场景，每个窗口有独立的Snackbar
/// - 自动识别当前活动窗口
/// - 避免单例服务的冲突问题
/// - 提供窗口级别的通知管理
/// 
/// 🔧 使用方式：
/// 1. 每个窗口注册自己的Snackbar实例
/// 2. 服务自动路由通知到正确的窗口
/// 3. 支持全局通知和窗口特定通知
/// </remarks>
public interface IWindowSnackbarService
{
    /// <summary>
    /// 注册窗口的 Snackbar 控件
    /// </summary>
    /// <param name="window">窗口实例</param>
    /// <param name="snackbar">Snackbar控件</param>
    void RegisterSnackbar(Window window, Controls.ZyloSnackbar snackbar);

    /// <summary>
    /// 注销窗口的 Snackbar 控件
    /// </summary>
    /// <param name="window">窗口实例</param>
    void UnregisterSnackbar(Window window);

    /// <summary>
    /// 在指定窗口显示成功消息
    /// </summary>
    /// <param name="window">目标窗口</param>
    /// <param name="message">消息内容</param>
    /// <param name="title">标题</param>
    /// <param name="timeout">超时时间</param>
    void ShowSuccess(Window window, string message, string title = "操作成功", int timeout = 3000);

    /// <summary>
    /// 在当前活动窗口显示成功消息
    /// </summary>
    /// <param name="message">消息内容</param>
    /// <param name="title">标题</param>
    /// <param name="timeout">超时时间</param>
    void ShowSuccess(string message, string title = "操作成功", int timeout = 3000);

    /// <summary>
    /// 在指定窗口显示错误消息
    /// </summary>
    /// <param name="window">目标窗口</param>
    /// <param name="message">消息内容</param>
    /// <param name="title">标题</param>
    /// <param name="timeout">超时时间</param>
    void ShowError(Window window, string message, string title = "操作失败", int timeout = 5000);

    /// <summary>
    /// 在当前活动窗口显示错误消息
    /// </summary>
    /// <param name="message">消息内容</param>
    /// <param name="title">标题</param>
    /// <param name="timeout">超时时间</param>
    void ShowError(string message, string title = "操作失败", int timeout = 5000);

    /// <summary>
    /// 在指定窗口显示警告消息
    /// </summary>
    /// <param name="window">目标窗口</param>
    /// <param name="message">消息内容</param>
    /// <param name="title">标题</param>
    /// <param name="timeout">超时时间</param>
    void ShowWarning(Window window, string message, string title = "警告", int timeout = 4000);

    /// <summary>
    /// 在当前活动窗口显示警告消息
    /// </summary>
    /// <param name="message">消息内容</param>
    /// <param name="title">标题</param>
    /// <param name="timeout">超时时间</param>
    void ShowWarning(string message, string title = "警告", int timeout = 4000);

    /// <summary>
    /// 在指定窗口显示信息消息
    /// </summary>
    /// <param name="window">目标窗口</param>
    /// <param name="message">消息内容</param>
    /// <param name="title">标题</param>
    /// <param name="timeout">超时时间</param>
    void ShowInfo(Window window, string message, string title = "提示", int timeout = 3000);

    /// <summary>
    /// 在当前活动窗口显示信息消息
    /// </summary>
    /// <param name="message">消息内容</param>
    /// <param name="title">标题</param>
    /// <param name="timeout">超时时间</param>
    void ShowInfo(string message, string title = "提示", int timeout = 3000);

    /// <summary>
    /// 在指定窗口显示自定义消息
    /// </summary>
    /// <param name="window">目标窗口</param>
    /// <param name="title">标题</param>
    /// <param name="message">消息内容</param>
    /// <param name="icon">图标</param>
    /// <param name="appearance">外观</param>
    /// <param name="timeout">超时时间</param>
    void Show(Window window, string title, string message, SymbolRegular icon = SymbolRegular.Info24, 
              ControlAppearance appearance = ControlAppearance.Primary, int timeout = 3000);

    /// <summary>
    /// 在当前活动窗口显示自定义消息
    /// </summary>
    /// <param name="title">标题</param>
    /// <param name="message">消息内容</param>
    /// <param name="icon">图标</param>
    /// <param name="appearance">外观</param>
    /// <param name="timeout">超时时间</param>
    void Show(string title, string message, SymbolRegular icon = SymbolRegular.Info24, 
              ControlAppearance appearance = ControlAppearance.Primary, int timeout = 3000);

    /// <summary>
    /// 隐藏指定窗口的 Snackbar
    /// </summary>
    /// <param name="window">目标窗口</param>
    void Hide(Window window);

    /// <summary>
    /// 隐藏当前活动窗口的 Snackbar
    /// </summary>
    void Hide();

    /// <summary>
    /// 获取当前活动窗口
    /// </summary>
    /// <returns>活动窗口实例，如果没有则返回null</returns>
    Window? GetActiveWindow();

    /// <summary>
    /// 获取已注册的窗口数量
    /// </summary>
    /// <returns>已注册窗口的数量</returns>
    int GetRegisteredWindowCount();

    /// <summary>
    /// 检查指定窗口是否已注册Snackbar
    /// </summary>
    /// <param name="window">要检查的窗口</param>
    /// <returns>如果已注册返回true</returns>
    bool IsWindowRegistered(Window window);
}
