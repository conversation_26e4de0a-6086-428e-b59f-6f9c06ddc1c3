<!-- GroupBox 基础用法示例 -->
<StackPanel Margin="20">

    <!-- 标准 GroupBox -->
    <GroupBox Header="用户信息" 
              Background="{DynamicResource ControlFillColorDefaultBrush}"
              BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
              BorderThickness="1"
              Margin="0,0,0,16">
        <Grid Margin="12">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <TextBlock Grid.Row="0" Grid.Column="0" Text="姓名:" Margin="0,0,8,8" VerticalAlignment="Center"/>
            <TextBox Grid.Row="0" Grid.Column="1" Text="张三" Margin="0,0,0,8"/>
            
            <TextBlock Grid.Row="1" Grid.Column="0" Text="年龄:" Margin="0,0,8,8" VerticalAlignment="Center"/>
            <TextBox Grid.Row="1" Grid.Column="1" Text="25" Margin="0,0,0,8"/>
            
            <TextBlock Grid.Row="2" Grid.Column="0" Text="邮箱:" Margin="0,0,8,0" VerticalAlignment="Center"/>
            <TextBox Grid.Row="2" Grid.Column="1" Text="<EMAIL>"/>
        </Grid>
    </GroupBox>

    <!-- 设置选项 GroupBox -->
    <GroupBox Header="设置选项" 
              Background="{DynamicResource ControlFillColorSecondaryBrush}"
              BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
              BorderThickness="1"
              Margin="0,0,0,16">
        <StackPanel Margin="12">
            <CheckBox Content="启用通知" IsChecked="True" Margin="0,0,0,8"/>
            <CheckBox Content="自动保存" IsChecked="False" Margin="0,0,0,8"/>
            <CheckBox Content="深色主题" IsChecked="True" Margin="0,0,0,8"/>
            <Button Content="应用设置" HorizontalAlignment="Left"/>
        </StackPanel>
    </GroupBox>

    <!-- 带图标的 GroupBox -->
    <GroupBox Background="{DynamicResource AccentFillColorDefaultBrush}"
              BorderBrush="{DynamicResource AccentStrokeColorDefaultBrush}"
              BorderThickness="2"
              Margin="0,0,0,16">
        <GroupBox.Header>
            <StackPanel Orientation="Horizontal">
                <ui:SymbolIcon Symbol="Star24" 
                               FontSize="16" 
                               Foreground="White"
                               Margin="0,0,8,0"/>
                <TextBlock Text="重要功能" 
                           Foreground="White"
                           FontWeight="Bold"/>
            </StackPanel>
        </GroupBox.Header>
        <StackPanel Margin="12">
            <TextBlock Text="这是一个重要的功能区域。" 
                       Foreground="White" 
                       Margin="0,0,0,12"/>
            <ProgressBar Value="75" Height="20" Margin="0,0,0,8"/>
            <TextBlock Text="进度: 75%" 
                       Foreground="White" 
                       FontSize="12" 
                       Margin="0,0,0,8"/>
            <Button Content="执行操作"/>
        </StackPanel>
    </GroupBox>

    <!-- 统计信息 GroupBox -->
    <GroupBox Header="统计信息" 
              Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
              BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
              BorderThickness="1"
              Margin="0,0,0,16">
        <Grid Margin="12">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <TextBlock Grid.Row="0" Grid.Column="0" Text="总数:" FontWeight="Bold"/>
            <TextBlock Grid.Row="0" Grid.Column="1" Text="1,234" HorizontalAlignment="Right"/>
            
            <TextBlock Grid.Row="1" Grid.Column="0" Text="活跃:" FontWeight="Bold"/>
            <TextBlock Grid.Row="1" Grid.Column="1" Text="987" HorizontalAlignment="Right"/>
            
            <TextBlock Grid.Row="2" Grid.Column="0" Text="新增:" FontWeight="Bold"/>
            <TextBlock Grid.Row="2" Grid.Column="1" Text="45" HorizontalAlignment="Right"/>
        </Grid>
    </GroupBox>

    <!-- 单选按钮分组 -->
    <GroupBox Header="选择选项" 
              Background="{DynamicResource ControlFillColorDefaultBrush}"
              BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
              BorderThickness="1"
              Margin="0,0,0,16">
        <StackPanel Margin="12">
            <RadioButton Content="选项 1" GroupName="Options" IsChecked="True" Margin="0,0,0,4"/>
            <RadioButton Content="选项 2" GroupName="Options" Margin="0,0,0,4"/>
            <RadioButton Content="选项 3" GroupName="Options" Margin="0,0,0,4"/>
            <RadioButton Content="选项 4" GroupName="Options"/>
        </StackPanel>
    </GroupBox>

    <!-- 禁用状态 -->
    <GroupBox Header="禁用状态" 
              Background="{DynamicResource ControlFillColorDefaultBrush}"
              BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
              BorderThickness="1"
              IsEnabled="False">
        <StackPanel Margin="12">
            <TextBlock Text="这是禁用状态的 GroupBox。"/>
            <Button Content="不可点击按钮" HorizontalAlignment="Left" Margin="0,8,0,0"/>
        </StackPanel>
    </GroupBox>

</StackPanel>
