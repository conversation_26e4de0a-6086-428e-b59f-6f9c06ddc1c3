using System.Windows.Controls;
using System.Windows.Threading;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Wpf.Ui.Controls;

namespace WPFTest.Examples
{
    /// <summary>
    /// 高级 Badge 控件示例
    /// 使用 CommunityToolkit.MVVM 8.4.0 的 Partial Properties 语法
    /// </summary>
    public partial class AdvancedBadgeExample : UserControl
    {
        public AdvancedBadgeExample()
        {
            InitializeComponent();
            DataContext = new AdvancedBadgeViewModel();
        }
    }

    /// <summary>
    /// 高级 Badge 示例 ViewModel
    /// </summary>
    public partial class AdvancedBadgeViewModel : ObservableObject
    {
        #region 私有字段

        private readonly DispatcherTimer _progressTimer;
        private readonly Random _random = new();

        #endregion

        #region Badge 属性

        /// <summary>
        /// Badge 内容
        /// </summary>
        [ObservableProperty]
        public partial string BadgeContent { get; set; } = "动态";

        /// <summary>
        /// Badge 外观
        /// </summary>
        [ObservableProperty]
        public partial ControlAppearance BadgeAppearance { get; set; } = ControlAppearance.Primary;

        /// <summary>
        /// 是否显示 Badge
        /// </summary>
        [ObservableProperty]
        public partial bool ShowBadge { get; set; } = true;

        /// <summary>
        /// 通知计数
        /// </summary>
        [ObservableProperty]
        public partial int NotificationCount { get; set; } = 3;

        /// <summary>
        /// 是否有通知
        /// </summary>
        [ObservableProperty]
        public partial bool HasNotifications { get; set; } = true;

        /// <summary>
        /// 进度值
        /// </summary>
        [ObservableProperty]
        public partial double ProgressValue { get; set; } = 65;

        /// <summary>
        /// 用户状态
        /// </summary>
        [ObservableProperty]
        public partial string UserStatus { get; set; } = "在线";

        /// <summary>
        /// 用户状态外观
        /// </summary>
        [ObservableProperty]
        public partial ControlAppearance UserStatusAppearance { get; set; } = ControlAppearance.Success;

        /// <summary>
        /// 是否显示可关闭 Badge
        /// </summary>
        [ObservableProperty]
        public partial bool ShowClosableBadge { get; set; } = true;

        /// <summary>
        /// 购物车商品数量
        /// </summary>
        [ObservableProperty]
        public partial int CartItemCount { get; set; } = 2;

        /// <summary>
        /// 动画状态
        /// </summary>
        [ObservableProperty]
        public partial bool IsAnimating { get; set; } = false;

        #endregion

        #region 构造函数

        public AdvancedBadgeViewModel()
        {
            // 初始化进度计时器
            _progressTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(100)
            };
            _progressTimer.Tick += OnProgressTimerTick;
            
            // 初始化默认值
            InitializeDefaults();
            
            // 启动进度动画
            _progressTimer.Start();
        }

        #endregion

        #region 命令

        /// <summary>
        /// 更改 Badge 内容命令
        /// </summary>
        [RelayCommand]
        private void ChangeBadgeContent()
        {
            var contents = new[] { "新", "热门", "促销", "VIP", "专业版", "测试版", "限时", "推荐" };
            BadgeContent = contents[_random.Next(contents.Length)];
        }

        /// <summary>
        /// 更改 Badge 外观命令
        /// </summary>
        [RelayCommand]
        private void ChangeBadgeAppearance()
        {
            var appearances = new[]
            {
                ControlAppearance.Primary,
                ControlAppearance.Secondary,
                ControlAppearance.Success,
                ControlAppearance.Danger,
                ControlAppearance.Caution,
                ControlAppearance.Info,
                ControlAppearance.Light,
                ControlAppearance.Dark
            };
            
            BadgeAppearance = appearances[_random.Next(appearances.Length)];
        }

        /// <summary>
        /// 切换 Badge 显示命令
        /// </summary>
        [RelayCommand]
        private void ToggleBadgeVisibility()
        {
            ShowBadge = !ShowBadge;
        }

        /// <summary>
        /// 增加通知计数命令
        /// </summary>
        [RelayCommand]
        private void IncreaseNotificationCount()
        {
            NotificationCount++;
            UpdateNotificationStatus();
        }

        /// <summary>
        /// 减少通知计数命令
        /// </summary>
        [RelayCommand]
        private void DecreaseNotificationCount()
        {
            if (NotificationCount > 0)
            {
                NotificationCount--;
                UpdateNotificationStatus();
            }
        }

        /// <summary>
        /// 重置通知计数命令
        /// </summary>
        [RelayCommand]
        private void ResetNotificationCount()
        {
            NotificationCount = 0;
            UpdateNotificationStatus();
        }

        /// <summary>
        /// Badge 点击命令
        /// </summary>
        [RelayCommand]
        private void BadgeClick()
        {
            // 模拟点击效果
            var originalAppearance = BadgeAppearance;
            BadgeAppearance = ControlAppearance.Success;
            
            // 延迟恢复原始外观
            var timer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(200)
            };
            timer.Tick += (s, e) =>
            {
                BadgeAppearance = originalAppearance;
                timer.Stop();
            };
            timer.Start();
        }

        /// <summary>
        /// 关闭 Badge 命令
        /// </summary>
        [RelayCommand]
        private void CloseBadge()
        {
            ShowClosableBadge = false;
            
            // 3秒后重新显示
            var timer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(3)
            };
            timer.Tick += (s, e) =>
            {
                ShowClosableBadge = true;
                timer.Stop();
            };
            timer.Start();
        }

        /// <summary>
        /// 更改用户状态命令
        /// </summary>
        [RelayCommand]
        private void ChangeUserStatus()
        {
            var statuses = new[]
            {
                ("在线", ControlAppearance.Success),
                ("忙碌", ControlAppearance.Warning),
                ("离开", ControlAppearance.Secondary),
                ("离线", ControlAppearance.Danger),
                ("隐身", ControlAppearance.Dark)
            };
            
            var status = statuses[_random.Next(statuses.Length)];
            UserStatus = status.Item1;
            UserStatusAppearance = status.Item2;
        }

        /// <summary>
        /// 添加到购物车命令
        /// </summary>
        [RelayCommand]
        private void AddToCart()
        {
            CartItemCount++;
        }

        /// <summary>
        /// 从购物车移除命令
        /// </summary>
        [RelayCommand]
        private void RemoveFromCart()
        {
            if (CartItemCount > 0)
            {
                CartItemCount--;
            }
        }

        /// <summary>
        /// 清空购物车命令
        /// </summary>
        [RelayCommand]
        private void ClearCart()
        {
            CartItemCount = 0;
        }

        /// <summary>
        /// 切换动画命令
        /// </summary>
        [RelayCommand]
        private void ToggleAnimation()
        {
            IsAnimating = !IsAnimating;
        }

        /// <summary>
        /// 模拟通知命令
        /// </summary>
        [RelayCommand]
        private void SimulateNotification()
        {
            // 随机增加通知数量
            var increase = _random.Next(1, 6);
            NotificationCount += increase;
            UpdateNotificationStatus();
            
            // 随机更改 Badge 内容和外观
            ChangeBadgeContent();
            ChangeBadgeAppearance();
        }

        /// <summary>
        /// 重置所有状态命令
        /// </summary>
        [RelayCommand]
        private void ResetAllStates()
        {
            BadgeContent = "动态";
            BadgeAppearance = ControlAppearance.Primary;
            ShowBadge = true;
            NotificationCount = 3;
            ProgressValue = 65;
            UserStatus = "在线";
            UserStatusAppearance = ControlAppearance.Success;
            ShowClosableBadge = true;
            CartItemCount = 2;
            IsAnimating = false;
            
            UpdateNotificationStatus();
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化默认值
        /// </summary>
        private void InitializeDefaults()
        {
            UpdateNotificationStatus();
        }

        /// <summary>
        /// 更新通知状态
        /// </summary>
        private void UpdateNotificationStatus()
        {
            HasNotifications = NotificationCount > 0;
        }

        /// <summary>
        /// 进度计时器事件处理
        /// </summary>
        private void OnProgressTimerTick(object? sender, EventArgs e)
        {
            // 模拟进度变化
            ProgressValue += _random.NextDouble() * 2 - 1; // -1 到 1 之间的随机变化
            
            // 限制进度值范围
            if (ProgressValue < 0) ProgressValue = 0;
            if (ProgressValue > 100) ProgressValue = 100;
            
            // 偶尔随机更改一些属性
            if (_random.Next(0, 100) < 2) // 2% 概率
            {
                ChangeBadgeContent();
            }
            
            if (_random.Next(0, 200) < 1) // 0.5% 概率
            {
                ChangeBadgeAppearance();
            }
        }

        #endregion

        #region 析构函数

        /// <summary>
        /// 清理资源
        /// </summary>
        ~AdvancedBadgeViewModel()
        {
            _progressTimer?.Stop();
        }

        #endregion
    }
}
