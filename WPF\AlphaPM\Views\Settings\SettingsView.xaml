<UserControl
    d:DataContext="{d:DesignInstance settings:SettingsViewModel}"
    d:DesignHeight="600"
    d:DesignWidth="1000"
    mc:Ignorable="d"
    prism:ViewModelLocator.AutoWireViewModel="True"
    x:Class="AlphaPM.Views.Settings.SettingsView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:ext="clr-namespace:Zylo.WPF.YPrism;assembly=Zylo.WPF"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:prism="http://prismlibrary.com/"
    xmlns:settings="clr-namespace:AlphaPM.ViewModels.Settings"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:viewModels="clr-namespace:AlphaPM.ViewModels"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:zylo="clr-namespace:Zylo.WPF.Controls.Navigation;assembly=Zylo.WPF">

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="300" />
            <!--  左侧设置导航  -->
            <ColumnDefinition Width="*" />
            <!--  右侧设置内容  -->
        </Grid.ColumnDefinitions>

        <!--  左侧设置导航  -->
        <Border
            Background="{DynamicResource ApplicationBackgroundBrush}"
            BorderBrush="{DynamicResource ControlElevationBorderBrush}"
            BorderThickness="0,0,1,0"
            Grid.Column="0">

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <!--  标题  -->
                    <RowDefinition Height="*" />
                    <!--  导航控件  -->
                </Grid.RowDefinitions>

                <!--  设置页面标题  -->
                <Border
                    Background="{DynamicResource CardBackgroundBrush}"
                    Grid.Row="0"
                    Padding="20,16">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>

                        <!--  标题文本  -->
                        <StackPanel Grid.Column="0">
                            <TextBlock
                                FontSize="24"
                                FontWeight="SemiBold"
                                Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                Text="设置" />
                            <TextBlock
                                FontSize="12"
                                Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                Margin="0,4,0,0"
                                Text="配置应用程序选项" />
                        </StackPanel>

                        <!--  显示/隐藏切换按钮 - 设置页面中隐藏  -->
                        <Button
                            Background="Transparent"
                            BorderThickness="0"
                            Command="{Binding ToggleNavigationCommand}"
                            Grid.Column="1"
                            Height="32"
                            ToolTip="显示/隐藏导航面板"
                            VerticalAlignment="Center"
                            Visibility="Collapsed"
                            Width="32">
                            <ui:SymbolIcon
                                FontSize="16"
                                Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                Symbol="Navigation24" />
                        </Button>
                    </Grid>
                </Border>

                <!--  设置导航控件  -->
                <zylo:NavigationControl
                    AllowCollapse="False"
                    DefaultPosition="Right"
                    Grid.Row="1"
                    Margin="0,8,0,0"
                    NavigationItemSelectedCommand="{Binding NavigateToSettingCommand}"
                    RightColumnWidth="300"
                    ShowToggleButton="False"
                    TopNavigationItems="{Binding SettingsNavigationItems}" />
            </Grid>
        </Border>

        <!--  右侧设置内容区域  -->
        <Grid Grid.Column="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <!--  内容标题  -->
                <RowDefinition Height="*" />
                <!--  内容区域  -->
            </Grid.RowDefinitions>

            <!--  当前设置页面标题  -->
            <Border
                Background="{DynamicResource CardBackgroundBrush}"
                BorderBrush="{DynamicResource ControlElevationBorderBrush}"
                BorderThickness="0,0,0,1"
                Grid.Row="0"
                Padding="24,16">
                <TextBlock
                    FontSize="20"
                    FontWeight="Medium"
                    Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                    Text="{Binding CurrentSettingTitle}" />
            </Border>

            <!--  设置内容区域  -->
            <ContentControl
                Grid.Row="1"
                Margin="24,20,24,24"
                prism:RegionManager.RegionName="{x:Static ext:PrismManager.SettingsRegionName}" />
        </Grid>
    </Grid>
</UserControl>
