using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Diagnostics;

namespace WPFTest.ViewModels.Navigation
{
    /// <summary>
    /// 硬编码面包屑导航示例 ViewModel
    /// </summary>
    public partial class HardcodedBreadcrumbViewModel : ObservableObject
    {
        /// <summary>
        /// 当前页面标题
        /// </summary>
        [ObservableProperty]
        private string currentPageTitle = "面包屑导航";

        /// <summary>
        /// 构造函数
        /// </summary>
        public HardcodedBreadcrumbViewModel()
        {
            Debug.WriteLine("硬编码面包屑 ViewModel 初始化");
        }

        /// <summary>
        /// 导航到首页命令
        /// </summary>
        [RelayCommand]
        private void NavigateToHome()
        {
            try
            {
                // 硬编码导航逻辑
                CurrentPageTitle = "首页";
                
                // 这里可以添加实际的导航逻辑
                // 例如：_navigationService.NavigateTo("HomeView");
                
                Debug.WriteLine("导航到首页");
                ShowNavigationMessage("已导航到首页");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"导航到首页失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 导航到控件示例命令
        /// </summary>
        [RelayCommand]
        private void NavigateToControls()
        {
            try
            {
                CurrentPageTitle = "控件示例";
                
                // 硬编码导航逻辑
                Debug.WriteLine("导航到控件示例");
                ShowNavigationMessage("已导航到控件示例");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"导航到控件示例失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 导航到导航控件命令
        /// </summary>
        [RelayCommand]
        private void NavigateToNavigation()
        {
            try
            {
                CurrentPageTitle = "导航控件";
                
                // 硬编码导航逻辑
                Debug.WriteLine("导航到导航控件");
                ShowNavigationMessage("已导航到导航控件");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"导航到导航控件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 刷新当前页面命令
        /// </summary>
        [RelayCommand]
        private void RefreshCurrentPage()
        {
            try
            {
                Debug.WriteLine($"刷新当前页面: {CurrentPageTitle}");
                ShowNavigationMessage($"已刷新 {CurrentPageTitle} 页面");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"刷新页面失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 返回上一页命令
        /// </summary>
        [RelayCommand]
        private void GoBack()
        {
            try
            {
                // 硬编码的返回逻辑
                var previousPage = GetPreviousPage(CurrentPageTitle);
                if (!string.IsNullOrEmpty(previousPage))
                {
                    CurrentPageTitle = previousPage;
                    Debug.WriteLine($"返回到: {previousPage}");
                    ShowNavigationMessage($"已返回到 {previousPage}");
                }
                else
                {
                    Debug.WriteLine("已经是首页，无法返回");
                    ShowNavigationMessage("已经是首页，无法返回");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"返回上一页失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取当前页面的上一页
        /// </summary>
        private string GetPreviousPage(string currentPage)
        {
            // 硬编码的页面层级关系
            return currentPage switch
            {
                "面包屑导航" => "导航控件",
                "导航控件" => "控件示例",
                "控件示例" => "首页",
                "首页" => string.Empty,
                _ => "首页"
            };
        }

        /// <summary>
        /// 获取完整的导航路径
        /// </summary>
        [RelayCommand]
        private void ShowFullPath()
        {
            try
            {
                var fullPath = GetFullNavigationPath(CurrentPageTitle);
                Debug.WriteLine($"完整路径: {fullPath}");
                ShowNavigationMessage($"完整路径: {fullPath}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取完整路径失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取完整的导航路径
        /// </summary>
        private string GetFullNavigationPath(string currentPage)
        {
            // 硬编码的路径构建
            return currentPage switch
            {
                "首页" => "首页",
                "控件示例" => "首页 > 控件示例",
                "导航控件" => "首页 > 控件示例 > 导航控件",
                "面包屑导航" => "首页 > 控件示例 > 导航控件 > 面包屑导航",
                _ => "首页"
            };
        }

        /// <summary>
        /// 显示导航消息
        /// </summary>
        private void ShowNavigationMessage(string message)
        {
            // 这里可以显示 Toast 消息或状态栏消息
            // 目前只是输出到调试窗口
            Debug.WriteLine($"导航消息: {message}");
        }

        /// <summary>
        /// 检查是否可以返回上一页
        /// </summary>
        public bool CanGoBack => !string.IsNullOrEmpty(GetPreviousPage(CurrentPageTitle));

        /// <summary>
        /// 获取当前页面的图标
        /// </summary>
        public string CurrentPageIcon => CurrentPageTitle switch
        {
            "首页" => "Home",
            "控件示例" => "Apps",
            "导航控件" => "Navigation",
            "面包屑导航" => "Breadcrumb",
            _ => "Page"
        };

        /// <summary>
        /// 获取面包屑项列表（用于显示）
        /// </summary>
        public List<BreadcrumbDisplayItem> GetBreadcrumbItems()
        {
            var items = new List<BreadcrumbDisplayItem>();
            var path = GetFullNavigationPath(CurrentPageTitle);
            var parts = path.Split(" > ");

            for (int i = 0; i < parts.Length; i++)
            {
                items.Add(new BreadcrumbDisplayItem
                {
                    Title = parts[i],
                    Icon = GetIconForPage(parts[i]),
                    IsLast = i == parts.Length - 1,
                    Command = GetNavigationCommandForPage(parts[i])
                });
            }

            return items;
        }

        /// <summary>
        /// 根据页面名称获取图标
        /// </summary>
        private string GetIconForPage(string pageName)
        {
            return pageName switch
            {
                "首页" => "Home",
                "控件示例" => "Apps",
                "导航控件" => "Navigation",
                "面包屑导航" => "Breadcrumb",
                _ => "Page"
            };
        }

        /// <summary>
        /// 根据页面名称获取导航命令
        /// </summary>
        private IRelayCommand GetNavigationCommandForPage(string pageName)
        {
            return pageName switch
            {
                "首页" => NavigateToHomeCommand,
                "控件示例" => NavigateToControlsCommand,
                "导航控件" => NavigateToNavigationCommand,
                _ => RefreshCurrentPageCommand
            };
        }
    }

    /// <summary>
    /// 面包屑显示项
    /// </summary>
    public class BreadcrumbDisplayItem
    {
        public string Title { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public bool IsLast { get; set; }
        public IRelayCommand? Command { get; set; }
    }
}
