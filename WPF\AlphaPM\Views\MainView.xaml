﻿<ui:FluentWindow
    ExtendsContentIntoTitleBar="True"
    Height="1000"
    Title="WPF-UI + Prism 导航测试"
    Width="1400"
    WindowBackdropType="Mica"
    WindowStartupLocation="CenterScreen"
    d:DataContext="{d:DesignInstance viewModels1:MainViewModel}"
    mc:Ignorable="d"
    prism:ViewModelLocator.AutoWireViewModel="True"
    x:Class="AlphaPM.Views.MainView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:ext="clr-namespace:Zylo.WPF.YPrism;assembly=Zylo.WPF"
    xmlns:local="clr-namespace:AlphaPM"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:navigation="clr-namespace:Zylo.WPF.Controls.Navigation;assembly=Zylo.WPF"
    xmlns:prism="http://prismlibrary.com/"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:viewModels1="clr-namespace:AlphaPM.ViewModels"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">


    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!--  ContentDialog 支持  -->
        <ContentPresenter
            Grid.RowSpan="2"
            Panel.ZIndex="999"
            x:Name="RootContentDialogPresenter" />

        <!--  🎯 标题栏  -->
        <ui:TitleBar
            Grid.Row="0"
            Height="35"
            Title="项目管理">
            <ui:TitleBar.Header>
                <DockPanel>
                    <Button
                        Command="{Binding GoBackCommand}"
                        DockPanel.Dock="Left"
                        Style="{StaticResource NavigationButtonStyle}"
                        Visibility="{Binding ShowToggleButton, ElementName=NavigationControlRoot, Converter={StaticResource BooleanToVisibilityConverter}}"
                        Width="48">
                        <ui:SymbolIcon
                            FontSize="20"
                            Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                            Symbol="ArrowLeft20" />
                    </Button>
                </DockPanel>
            </ui:TitleBar.Header>
        </ui:TitleBar>
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <!--  🎯 NavigationControl 完整功能测试  -->
            <navigation:NavigationControl
                BottomNavigationItems="{Binding DnavigationItems}"
                DefaultPosition="Left"
                EnableDragDrop="True"
                EnableResponsiveLayout="{Binding EnableResponsiveLayout}"
                Grid.Column="0"
                HighlightSearchResults="{Binding HighlightSearchMatches}"
                ItemContextMenuCommand="{Binding NavigationItemContextMenuCommand}"
                ItemDoubleClickCommand="{Binding NavigationItemDoubleClickCommand}"
                NavigationItemSelectedCommand="{Binding NavigateToItemCommand}"
                NavigationToggleCommand="{Binding NavigationToggleCommand}"
                RightColumnWidth="250"
                SearchText="{Binding NavigationSearchText, Mode=TwoWay}"
                SearchTextChangedCommand="{Binding SearchTextChangedCommand}"
                SelectedListItem="{Binding ZyloNavigationItemT, Mode=TwoWay}"
                ShowSearchBox="{Binding ShowNavigationSearch}"
                TopNavigationItems="{Binding TnavigationItems}"
                TreeViewColumnWidth="{Binding NavigationTreeViewColumnWidth, Mode=TwoWay}"
                x:Name="NavigationSidebar" />

            <!--  🎯 主内容区域 - 显示选中的内容  -->
            <Border Grid.Column="1" Style="{DynamicResource YBorderStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <!--  Prism 区域 (主要内容显示区域)  -->
                    <ContentControl
                        AllowDrop="True"
                        Grid.Row="0"
                        Margin="5"
                        Visibility="Visible"
                        prism:RegionManager.RegionName="{x:Static ext:PrismManager.MainViewRegionName}" />
                </Grid>
            </Border>
        </Grid>

        <!--  🖱️ 四个角的拖拽区域 - 设置最高层级  -->
        <!--  左上角  -->
        <Border
            Background="Transparent"
            Cursor="SizeAll"
            Grid.Row="0"
            Grid.RowSpan="2"
            Height="5"
            HorizontalAlignment="Left"
            MouseLeftButtonDown="CornerDrag_MouseLeftButtonDown"
            Panel.ZIndex="1000"
            ToolTip="拖拽移动窗口"
            VerticalAlignment="Top"
            Width="5" />

        <!--  右上角  -->
        <Border
            Background="Transparent"
            Cursor="SizeAll"
            Grid.Row="0"
            Grid.RowSpan="2"
            Height="5"
            HorizontalAlignment="Right"
            MouseLeftButtonDown="CornerDrag_MouseLeftButtonDown"
            Panel.ZIndex="1000"
            ToolTip="拖拽移动窗口"
            VerticalAlignment="Top"
            Width="5" />

        <!--  左下角  -->
        <Border
            Background="Transparent"
            Cursor="SizeAll"
            Grid.Row="0"
            Grid.RowSpan="2"
            Height="5"
            HorizontalAlignment="Left"
            MouseLeftButtonDown="CornerDrag_MouseLeftButtonDown"
            Panel.ZIndex="1000"
            ToolTip="拖拽移动窗口"
            VerticalAlignment="Bottom"
            Width="5" />

        <!--  右下角  -->
        <Border
            Background="Transparent"
            Cursor="SizeAll"
            Grid.Row="0"
            Grid.RowSpan="2"
            Height="500"
            HorizontalAlignment="Right"
            MouseLeftButtonDown="CornerDrag_MouseLeftButtonDown"
            Panel.ZIndex="1000"
            ToolTip="拖拽移动窗口"
            VerticalAlignment="Bottom"
            Width="10" />

    </Grid>

</ui:FluentWindow>
