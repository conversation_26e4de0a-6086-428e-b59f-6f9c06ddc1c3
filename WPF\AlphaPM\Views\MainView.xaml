﻿<ui:FluentWindow
    x:Class="AlphaPM.Views.MainView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:ext="clr-namespace:Zylo.WPF.YPrism;assembly=Zylo.WPF"
    xmlns:local="clr-namespace:AlphaPM"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:navigation="clr-namespace:Zylo.WPF.Controls.Navigation;assembly=Zylo.WPF"
    xmlns:prism="http://prismlibrary.com/"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:viewModels1="clr-namespace:AlphaPM.ViewModels"
    Title="项目管理软件"
    Width="1400"
    Height="1000"
    d:DataContext="{d:DesignInstance viewModels1:MainViewModel}"
    prism:ViewModelLocator.AutoWireViewModel="True"
    ExtendsContentIntoTitleBar="True"
    WindowBackdropType="Mica"
    WindowStartupLocation="CenterScreen"
    mc:Ignorable="d">


    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!--  ContentDialog 支持  -->
        <ContentPresenter
            x:Name="RootContentDialogPresenter"
            Grid.RowSpan="2"
            Panel.ZIndex="999" />

        <!--  🎯 标题栏  -->
        <ui:TitleBar
            Title="项目管理"
            Grid.Row="0"
            Height="35">
            <!--  移除图标占位符  -->
            <ui:TitleBar.Icon>
                <ui:SymbolIcon Symbol="Empty" Visibility="Collapsed" />
            </ui:TitleBar.Icon>
            <ui:TitleBar.Header>
                <DockPanel>
                    <Button
                        Width="48"
                        Margin="-30,0,0,0"
                        Command="{Binding GoBackCommand}"
                        DockPanel.Dock="Left"
                        Style="{StaticResource NavigationButtonStyle}"
                        Visibility="{Binding ShowToggleButton, ElementName=NavigationControlRoot, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <ui:SymbolIcon
                            FontSize="20"
                            Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                            Symbol="ArrowLeft20" />
                    </Button>
                </DockPanel>
            </ui:TitleBar.Header>
        </ui:TitleBar>
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <!--  🎯 NavigationControl 完整功能导航  -->
            <navigation:NavigationControl
                x:Name="NavigationSidebar"
                Grid.Column="0"
                BottomNavigationItems="{Binding DnavigationItems}"
                DefaultPosition="Left"
                EnableDragDrop="True"
                EnableResponsiveLayout="{Binding EnableResponsiveLayout}"
                HighlightSearchResults="{Binding HighlightSearchMatches}"
                ItemContextMenuCommand="{Binding NavigationItemContextMenuCommand}"
                ItemDoubleClickCommand="{Binding NavigationItemDoubleClickCommand}"
                NavigationItemSelectedCommand="{Binding NavigateToItemCommand}"
                NavigationToggleCommand="{Binding NavigationToggleCommand}"
                RightColumnWidth="250"
                SearchText="{Binding NavigationSearchText, Mode=TwoWay}"
                SearchTextChangedCommand="{Binding SearchTextChangedCommand}"
                SelectedListItem="{Binding ZyloNavigationItemT, Mode=TwoWay}"
                ShowSearchBox="{Binding ShowNavigationSearch}"
                TopNavigationItems="{Binding TnavigationItems}"
                TreeViewColumnWidth="{Binding NavigationTreeViewColumnWidth, Mode=TwoWay}" />

            <!--  🎯 主内容区域 - 显示选中的内容  -->
            <Border Grid.Column="1" Style="{DynamicResource YBorderStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <!--  Prism 区域 (主要内容显示区域)  -->
                    <ContentControl
                        Grid.Row="0"
                        Margin="5"
                        prism:RegionManager.RegionName="{x:Static ext:PrismManager.MainViewRegionName}"
                        AllowDrop="True"
                        Visibility="Visible" />
                </Grid>
            </Border>
        </Grid>

        <!--  🖱️ 四个角的拖拽区域 - 设置最高层级  -->
        <!--  左上角  -->
        <Border
            Grid.Row="0"
            Grid.RowSpan="2"
            Width="5"
            Height="5"
            HorizontalAlignment="Left"
            VerticalAlignment="Top"
            Panel.ZIndex="1000"
            Background="Transparent"
            Cursor="SizeAll"
            MouseLeftButtonDown="CornerDrag_MouseLeftButtonDown"
            ToolTip="拖拽移动窗口" />

        <!--  右上角  -->
        <Border
            Grid.Row="0"
            Grid.RowSpan="2"
            Width="5"
            Height="5"
            HorizontalAlignment="Right"
            VerticalAlignment="Top"
            Panel.ZIndex="1000"
            Background="Transparent"
            Cursor="SizeAll"
            MouseLeftButtonDown="CornerDrag_MouseLeftButtonDown"
            ToolTip="拖拽移动窗口" />

        <!--  左下角  -->
        <Border
            Grid.Row="0"
            Grid.RowSpan="2"
            Width="5"
            Height="5"
            HorizontalAlignment="Left"
            VerticalAlignment="Bottom"
            Panel.ZIndex="1000"
            Background="Transparent"
            Cursor="SizeAll"
            MouseLeftButtonDown="CornerDrag_MouseLeftButtonDown"
            ToolTip="拖拽移动窗口" />

        <!--  右下角  -->
        <Border
            Grid.Row="0"
            Grid.RowSpan="2"
            Width="10"
            Height="500"
            HorizontalAlignment="Right"
            VerticalAlignment="Bottom"
            Panel.ZIndex="1000"
            Background="Transparent"
            Cursor="SizeAll"
            MouseLeftButtonDown="CornerDrag_MouseLeftButtonDown"
            ToolTip="拖拽移动窗口" />

    </Grid>

</ui:FluentWindow>
