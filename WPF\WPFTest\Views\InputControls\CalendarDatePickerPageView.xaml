<UserControl x:Class="WPFTest.Views.InputControls.CalendarDatePickerPageView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:codeExample="clr-namespace:Zylo.WPF.Controls.CodeExample;assembly=Zylo.WPF"
             xmlns:mvvm="http://prismlibrary.com/"
             xmlns:inputControls="clr-namespace:WPFTest.ViewModels.InputControls"
             mc:Ignorable="d"
             d:DesignHeight="2000" d:DesignWidth="1200"
             mvvm:ViewModelLocator.AutoWireViewModel="True"
             d:DataContext="{d:DesignInstance inputControls:CalendarDatePickerPageViewModel}">

    <UserControl.Resources>
        <!-- 🎨 所有样式来自 Zylo.WPF 项目 -->
        <!-- CalendarDatePicker 样式已在 Zylo.WPF/Resources/CalendarDatePicker/ 目录下定义 -->
        <!-- 可用样式：CalendarDatePickerStyle, SmallCalendarDatePickerStyle, LargeCalendarDatePickerStyle -->
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" 
                  HorizontalScrollBarVisibility="Disabled"
                  Padding="24">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>    <!-- 标题区域 -->
                <RowDefinition Height="*"/>       <!-- 主要内容 -->
                <RowDefinition Height="Auto"/>    <!-- 底部操作栏 -->
            </Grid.RowDefinitions>

            <!-- 标题区域 -->
            <StackPanel Grid.Row="0" Margin="0,0,0,20">
                <TextBlock Text="📅 CalendarDatePicker 控件展示"
                           FontSize="32"
                           FontWeight="Bold"
                           Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                           Margin="0,0,0,8"/>
                <TextBlock Text="展示 CalendarDatePicker 控件的日期选择功能和各种交互效果"
                           FontSize="16"
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                           TextWrapping="Wrap"/>
                
                <!-- 状态显示 -->
                <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                        BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Padding="16,12"
                        Margin="0,16,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <ui:SymbolIcon Symbol="Info24" 
                                       FontSize="20" 
                                       Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                       VerticalAlignment="Center"
                                       Margin="0,0,12,0"/>
                        <TextBlock Grid.Column="1" 
                                   Text="{Binding StatusMessage}" 
                                   FontSize="14"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                   VerticalAlignment="Center"
                                   TextWrapping="Wrap"/>
                    </Grid>
                </Border>
            </StackPanel>

            <!-- 主要内容区域 -->
            <Border Grid.Row="1" Margin="0,0,0,20">
                <StackPanel>

                    <!-- 基础功能展示 -->
                    <ui:CardExpander Header="🎯 基础功能" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 CalendarDatePicker 的基础日期选择功能"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>
                            
                            <!-- 基础功能示例 -->
                            <ui:Card Padding="32" Margin="0,0,0,24">
                                <StackPanel>
                                    <!-- 标题区域 -->
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,24">
                                        <ui:SymbolIcon Symbol="Calendar24"
                                                       FontSize="20"
                                                       Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,8,0"/>
                                        <TextBlock Text="基础日期选择示例"
                                                   FontWeight="SemiBold"
                                                   FontSize="18"
                                                   VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <!-- 使用卡片式布局 -->
                                    <UniformGrid Columns="3" Margin="0,0,0,24">

                                        <!-- 标准日期选择器卡片 -->
                                        <Border Background="{DynamicResource CardBackgroundFillColorSecondaryBrush}"
                                                BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                                BorderThickness="1"
                                                CornerRadius="8"
                                                Padding="20"
                                                Margin="0,0,12,0">
                                            <StackPanel>
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                                    <Ellipse Width="8" Height="8"
                                                             Fill="{DynamicResource SystemAccentColorPrimaryBrush}"
                                                             VerticalAlignment="Center"
                                                             Margin="0,0,8,0"/>
                                                    <TextBlock Text="标准日期选择器"
                                                               FontWeight="Medium"
                                                               FontSize="14"/>
                                                </StackPanel>

                                                <DatePicker SelectedDate="{Binding SelectedDate, Mode=TwoWay}"
                                                            HorizontalAlignment="Stretch"
                                                            Height="40"
                                                            FontSize="14"
                                                            SelectedDateFormat="Long"
                                                            Margin="0,0,0,12"/>

                                                <Border Background="{DynamicResource SystemFillColorAttentionBackgroundBrush}"
                                                        CornerRadius="4"
                                                        Padding="8,6">
                                                    <TextBlock Text="{Binding SelectedDate, StringFormat='选择结果: {0:yyyy年MM月dd日}'}"
                                                               FontSize="12"
                                                               FontWeight="Medium"
                                                               Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                                               TextAlignment="Center"/>
                                                </Border>
                                            </StackPanel>
                                        </Border>

                                        <!-- 生日日期选择器卡片 -->
                                        <Border Background="{DynamicResource CardBackgroundFillColorSecondaryBrush}"
                                                BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                                BorderThickness="1"
                                                CornerRadius="8"
                                                Padding="20"
                                                Margin="6,0,6,0">
                                            <StackPanel>
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                                    <Ellipse Width="8" Height="8"
                                                             Fill="#FF6B46C1"
                                                             VerticalAlignment="Center"
                                                             Margin="0,0,8,0"/>
                                                    <TextBlock Text="生日日期选择器"
                                                               FontWeight="Medium"
                                                               FontSize="14"/>
                                                </StackPanel>

                                                <DatePicker SelectedDate="{Binding BirthDate, Mode=TwoWay}"
                                                            HorizontalAlignment="Stretch"
                                                            Height="40"
                                                            FontSize="14"
                                                            SelectedDateFormat="Long"
                                                            Margin="0,0,0,12"/>

                                                <Border Background="{DynamicResource SystemFillColorAttentionBackgroundBrush}"
                                                        CornerRadius="4"
                                                        Padding="8,6">
                                                    <TextBlock Text="{Binding BirthDate, StringFormat='生日: {0:yyyy年MM月dd日}'}"
                                                               FontSize="12"
                                                               FontWeight="Medium"
                                                               Foreground="#FF6B46C1"
                                                               TextAlignment="Center"/>
                                                </Border>
                                            </StackPanel>
                                        </Border>

                                        <!-- 活动日期选择器卡片 -->
                                        <Border Background="{DynamicResource CardBackgroundFillColorSecondaryBrush}"
                                                BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                                BorderThickness="1"
                                                CornerRadius="8"
                                                Padding="20"
                                                Margin="12,0,0,0">
                                            <StackPanel>
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                                    <Ellipse Width="8" Height="8"
                                                             Fill="#FF10B981"
                                                             VerticalAlignment="Center"
                                                             Margin="0,0,8,0"/>
                                                    <TextBlock Text="活动日期选择器"
                                                               FontWeight="Medium"
                                                               FontSize="14"/>
                                                </StackPanel>

                                                <DatePicker SelectedDate="{Binding EventDate, Mode=TwoWay}"
                                                            HorizontalAlignment="Stretch"
                                                            Height="40"
                                                            FontSize="14"
                                                            SelectedDateFormat="Long"
                                                            Margin="0,0,0,12"/>

                                                <Border Background="{DynamicResource SystemFillColorAttentionBackgroundBrush}"
                                                        CornerRadius="4"
                                                        Padding="8,6">
                                                    <TextBlock Text="{Binding EventDate, StringFormat='活动日期: {0:yyyy年MM月dd日}'}"
                                                               FontSize="12"
                                                               FontWeight="Medium"
                                                               Foreground="#FF10B981"
                                                               TextAlignment="Center"/>
                                                </Border>
                                            </StackPanel>
                                        </Border>
                                    </UniformGrid>

                                    <!-- 操作按钮区域 -->
                                    <Border Background="{DynamicResource CardBackgroundFillColorSecondaryBrush}"
                                            BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                            BorderThickness="1"
                                            CornerRadius="8"
                                            Padding="20">
                                        <StackPanel>
                                            <TextBlock Text="快速操作"
                                                       FontWeight="Medium"
                                                       FontSize="14"
                                                       Margin="0,0,0,12"
                                                       HorizontalAlignment="Center"/>
                                            <WrapPanel HorizontalAlignment="Center">
                                                <ui:Button Content="设置今天"
                                                           Command="{Binding SetTodayCommand}"
                                                           Appearance="Primary"
                                                           Icon="{ui:SymbolIcon Calendar24}"
                                                           Margin="0,0,12,0"
                                                           Padding="16,8"/>
                                                <ui:Button Content="清除日期"
                                                           Command="{Binding ClearDateCommand}"
                                                           Appearance="Secondary"
                                                           Icon="{ui:SymbolIcon Delete24}"
                                                           Margin="0,0,12,0"
                                                           Padding="16,8"/>
                                                <ui:Button Content="随机日期"
                                                           Command="{Binding RandomDateCommand}"
                                                           Appearance="Secondary"
                                                           Icon="{ui:SymbolIcon ArrowClockwise24}"
                                                           Padding="16,8"/>
                                            </WrapPanel>
                                        </StackPanel>
                                    </Border>
                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="基础功能代码示例"
                                Language="XAML"
                                Description="展示 CalendarDatePicker 的基础用法"
                                ShowTabs="True"
                                XamlCode="{Binding BasicXamlExample}"
                                CSharpCode="{Binding BasicCSharpExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 高级功能展示 -->
                    <ui:CardExpander Header="⚡ 高级功能" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 CalendarDatePicker 的高级功能和自定义配置"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 高级功能示例 -->
                            <ui:Card Padding="24" Margin="0,0,0,20">
                                <StackPanel>
                                    <TextBlock Text="高级功能示例" FontWeight="SemiBold" FontSize="16" Margin="0,0,0,20"/>

                                    <!-- 日期范围选择 -->
                                    <GroupBox Header="📅 日期范围选择" Margin="0,0,0,20" Padding="16">
                                        <StackPanel>
                                            <Grid Margin="0,0,0,12">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>

                                                <TextBlock Grid.Column="0"
                                                           Text="开始日期："
                                                           VerticalAlignment="Center"
                                                           FontSize="14"
                                                           Margin="0,0,12,0"/>
                                                <DatePicker Grid.Column="1"
                                                            SelectedDate="{Binding StartDate, Mode=TwoWay}"
                                                            Width="250"
                                                            Height="36"
                                                            FontSize="14"
                                                            SelectedDateFormat="Long"
                                                            Margin="0,0,20,0"/>

                                                <TextBlock Grid.Column="2"
                                                           Text="结束日期："
                                                           VerticalAlignment="Center"
                                                           FontSize="14"
                                                           Margin="0,0,12,0"/>
                                                <DatePicker Grid.Column="3"
                                                            SelectedDate="{Binding EndDate, Mode=TwoWay}"
                                                            Width="250"
                                                            Height="36"
                                                            FontSize="14"
                                                            SelectedDateFormat="Long"/>
                                            </Grid>

                                            <!-- 范围信息显示 -->
                                            <Border Background="{DynamicResource CardBackgroundFillColorSecondaryBrush}"
                                                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                                    BorderThickness="1"
                                                    CornerRadius="6"
                                                    Padding="12,8">
                                                <StackPanel Orientation="Horizontal">
                                                    <ui:SymbolIcon Symbol="CalendarClock24"
                                                                   FontSize="16"
                                                                   Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                                                   VerticalAlignment="Center"
                                                                   Margin="0,0,8,0"/>
                                                    <TextBlock Text="{Binding DateRangeDays, StringFormat='日期间隔: {0} 天'}"
                                                               VerticalAlignment="Center"
                                                               FontSize="13"
                                                               FontWeight="Medium"
                                                               Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                                                </StackPanel>
                                            </Border>
                                        </StackPanel>
                                    </GroupBox>

                                    <!-- 程序化控制 -->
                                    <GroupBox Header="🎛️ 程序化控制" Margin="0,0,0,0" Padding="16">
                                        <StackPanel>
                                            <Grid Margin="0,0,0,12">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <TextBlock Grid.Column="0"
                                                           Text="程序化日期："
                                                           VerticalAlignment="Center"
                                                           FontSize="14"
                                                           Margin="0,0,12,0"/>
                                                <DatePicker Grid.Column="1"
                                                            SelectedDate="{Binding ProgrammaticDate, Mode=TwoWay}"
                                                            IsDropDownOpen="{Binding IsCalendarOpen, Mode=TwoWay}"
                                                            Width="250"
                                                            Height="36"
                                                            FontSize="14"
                                                            SelectedDateFormat="Long"
                                                            Margin="0,0,20,0"/>

                                                <StackPanel Grid.Column="2" Orientation="Horizontal">
                                                    <ui:Button Content="打开日历"
                                                               Command="{Binding OpenCalendarCommand}"
                                                               Appearance="Secondary"
                                                               Icon="{ui:SymbolIcon ChevronDown24}"
                                                               Margin="0,0,8,0"/>
                                                    <ui:Button Content="关闭日历"
                                                               Command="{Binding CloseCalendarCommand}"
                                                               Appearance="Secondary"
                                                               Icon="{ui:SymbolIcon ChevronUp24}"/>
                                                </StackPanel>
                                            </Grid>

                                            <!-- 当前日期显示 -->
                                            <Border Background="{DynamicResource CardBackgroundFillColorSecondaryBrush}"
                                                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                                    BorderThickness="1"
                                                    CornerRadius="6"
                                                    Padding="12,8">
                                                <StackPanel Orientation="Horizontal">
                                                    <ui:SymbolIcon Symbol="Calendar24"
                                                                   FontSize="16"
                                                                   Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                                                   VerticalAlignment="Center"
                                                                   Margin="0,0,8,0"/>
                                                    <TextBlock Text="{Binding ProgrammaticDate, StringFormat='当前选择: {0:yyyy年MM月dd日 dddd}'}"
                                                               VerticalAlignment="Center"
                                                               FontSize="13"
                                                               FontWeight="Medium"
                                                               Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"/>
                                                </StackPanel>
                                            </Border>
                                        </StackPanel>
                                    </GroupBox>

                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="高级功能代码示例"
                                Language="XAML"
                                Description="展示 CalendarDatePicker 的高级用法和自定义配置"
                                ShowTabs="True"
                                XamlCode="{Binding AdvancedXamlExample}"
                                CSharpCode="{Binding AdvancedCSharpExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 样式展示 -->
                    <ui:CardExpander Header="🎨 样式展示" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示来自 Zylo.WPF 的各种 CalendarDatePicker 样式"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 样式展示区域 -->
                            <ui:Card Padding="24" Margin="0,0,0,20">
                                <StackPanel>
                                    <TextBlock Text="样式展示示例" FontWeight="SemiBold" FontSize="16" Margin="0,0,0,20"/>

                                    <!-- 基础样式系列 -->
                                    <GroupBox Header="📏 不同尺寸样式" Margin="0,0,0,20" Padding="16">
                                        <Grid>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                            </Grid.RowDefinitions>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- 紧凑样式 -->
                                            <TextBlock Grid.Row="0" Grid.Column="0"
                                                       Text="紧凑样式："
                                                       VerticalAlignment="Center"
                                                       FontSize="14"
                                                       Margin="0,0,12,0"/>
                                            <DatePicker Grid.Row="0" Grid.Column="1"
                                                        SelectedDate="{Binding SelectedDate, Mode=TwoWay}"
                                                        Width="250"
                                                        Height="28"
                                                        FontSize="11"
                                                        SelectedDateFormat="Long"
                                                        Margin="0,0,20,8"/>
                                            <TextBlock Grid.Row="0" Grid.Column="2"
                                                       Text="适用于空间紧张的界面"
                                                       VerticalAlignment="Center"
                                                       FontSize="11"
                                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                       Margin="0,0,0,8"/>

                                            <!-- 小型样式 -->
                                            <TextBlock Grid.Row="1" Grid.Column="0"
                                                       Text="小型样式："
                                                       VerticalAlignment="Center"
                                                       FontSize="14"
                                                       Margin="0,0,12,0"/>
                                            <DatePicker Grid.Row="1" Grid.Column="1"
                                                        SelectedDate="{Binding SelectedDate, Mode=TwoWay}"
                                                        Width="160"
                                                        Height="32"
                                                        FontSize="12"
                                                        SelectedDateFormat="Long"
                                                        Margin="0,0,20,8"/>
                                            <TextBlock Grid.Row="1" Grid.Column="2"
                                                       Text="适用于表单和对话框"
                                                       VerticalAlignment="Center"
                                                       FontSize="11"
                                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                       Margin="0,0,0,8"/>

                                            <!-- 标准样式 -->
                                            <TextBlock Grid.Row="2" Grid.Column="0"
                                                       Text="标准样式："
                                                       VerticalAlignment="Center"
                                                       FontSize="14"
                                                       Margin="0,0,12,0"/>
                                            <DatePicker Grid.Row="2" Grid.Column="1"
                                                        SelectedDate="{Binding SelectedDate, Mode=TwoWay}"
                                                        Width="180"
                                                        Height="36"
                                                        FontSize="14"
                                                        SelectedDateFormat="Long"
                                                        Margin="0,0,20,8"/>
                                            <TextBlock Grid.Row="2" Grid.Column="2"
                                                       Text="推荐的默认样式"
                                                       VerticalAlignment="Center"
                                                       FontSize="11"
                                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                       Margin="0,0,0,8"/>

                                            <!-- 大型样式 -->
                                            <TextBlock Grid.Row="3" Grid.Column="0"
                                                       Text="大型样式："
                                                       VerticalAlignment="Center"
                                                       FontSize="14"
                                                       Margin="0,0,12,0"/>
                                            <DatePicker Grid.Row="3" Grid.Column="1"
                                                        SelectedDate="{Binding SelectedDate, Mode=TwoWay}"
                                                        Width="200"
                                                        Height="44"
                                                        FontSize="16"
                                                        SelectedDateFormat="Long"
                                                        Margin="0,0,20,0"/>
                                            <TextBlock Grid.Row="3" Grid.Column="2"
                                                       Text="适用于重要的日期选择"
                                                       VerticalAlignment="Center"
                                                       FontSize="11"
                                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                                        </Grid>
                                    </GroupBox>

                                    <!-- 特殊样式系列 -->
                                    <GroupBox Header="🎨 特殊效果样式" Margin="0,0,0,0" Padding="16">
                                        <Grid>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                            </Grid.RowDefinitions>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- 强调色样式 -->
                                            <TextBlock Grid.Row="0" Grid.Column="0"
                                                       Text="强调色样式："
                                                       VerticalAlignment="Center"
                                                       FontSize="14"
                                                       Margin="0,0,12,0"/>
                                            <DatePicker Grid.Row="0" Grid.Column="1"
                                                        SelectedDate="{Binding SelectedDate, Mode=TwoWay}"
                                                        Width="180"
                                                        Height="36"
                                                        FontSize="14"
                                                        SelectedDateFormat="Long"
                                                        BorderBrush="{DynamicResource SystemAccentColorPrimaryBrush}"
                                                        BorderThickness="2"
                                                        Margin="0,0,20,8"/>
                                            <TextBlock Grid.Row="0" Grid.Column="2"
                                                       Text="突出显示重要日期"
                                                       VerticalAlignment="Center"
                                                       FontSize="11"
                                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                       Margin="0,0,0,8"/>

                                            <!-- 透明背景样式 -->
                                            <TextBlock Grid.Row="1" Grid.Column="0"
                                                       Text="透明背景样式："
                                                       VerticalAlignment="Center"
                                                       FontSize="14"
                                                       Margin="0,0,12,0"/>
                                            <DatePicker Grid.Row="1" Grid.Column="1"
                                                        SelectedDate="{Binding SelectedDate, Mode=TwoWay}"
                                                        Width="180"
                                                        Height="36"
                                                        FontSize="14"
                                                        SelectedDateFormat="Long"
                                                        Background="Transparent"
                                                        Margin="0,0,20,8"/>
                                            <TextBlock Grid.Row="1" Grid.Column="2"
                                                       Text="融入背景的简洁样式"
                                                       VerticalAlignment="Center"
                                                       FontSize="11"
                                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                       Margin="0,0,0,8"/>

                                            <!-- 禁用状态样式 -->
                                            <TextBlock Grid.Row="2" Grid.Column="0"
                                                       Text="禁用状态样式："
                                                       VerticalAlignment="Center"
                                                       FontSize="14"
                                                       Margin="0,0,12,0"/>
                                            <DatePicker Grid.Row="2" Grid.Column="1"
                                                        SelectedDate="{Binding SelectedDate, Mode=TwoWay}"
                                                        Width="180"
                                                        Height="36"
                                                        FontSize="14"
                                                        SelectedDateFormat="Long"
                                                        IsEnabled="False"
                                                        Margin="0,0,20,0"/>
                                            <TextBlock Grid.Row="2" Grid.Column="2"
                                                       Text="只读或禁用状态显示"
                                                       VerticalAlignment="Center"
                                                       FontSize="11"
                                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                                        </Grid>
                                    </GroupBox>

                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="样式代码示例"
                                Language="XAML"
                                Description="展示如何使用 Zylo.WPF 中的 CalendarDatePicker 样式"
                                ShowTabs="True"
                                XamlCode="{Binding StylesXamlExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                </StackPanel>
            </Border>

            <!-- 底部操作栏 -->
            <Border Grid.Row="2" 
                    Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Padding="16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 统计信息 -->
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock Text="{Binding InteractionCount, StringFormat='交互次数: {0}'}" 
                                   FontSize="14" 
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   Margin="0,0,20,0"/>
                        <TextBlock Text="{Binding LastAction, StringFormat='最后操作: {0}'}" 
                                   FontSize="14" 
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                    </StackPanel>

                    <!-- 操作按钮 -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <ui:Button Content="重置计数" 
                                   Command="{Binding ResetCountCommand}"
                                   Appearance="Secondary"
                                   Margin="0,0,8,0"/>
                        <ui:Button Content="清除状态" 
                                   Command="{Binding ClearStatusCommand}"
                                   Appearance="Secondary"/>
                    </StackPanel>
                </Grid>
            </Border>

        </Grid>
    </ScrollViewer>
</UserControl>
