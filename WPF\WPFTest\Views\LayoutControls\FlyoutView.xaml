<UserControl x:Class="WPFTest.Views.LayoutControls.FlyoutView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:codeExample="clr-namespace:Zylo.WPF.Controls.CodeExample;assembly=Zylo.WPF"
             xmlns:mvvm="http://prismlibrary.com/"
             xmlns:layoutControls="clr-namespace:WPFTest.ViewModels.LayoutControls"
             mc:Ignorable="d"
             d:DesignHeight="800" d:DesignWidth="1200"
             mvvm:ViewModelLocator.AutoWireViewModel="True"
             d:DataContext="{d:DesignInstance layoutControls:FlyoutViewModel}">

    <ScrollViewer VerticalScrollBarVisibility="Auto" 
                  HorizontalScrollBarVisibility="Disabled"
                  Padding="24">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>    <!-- 标题区域 -->
                <RowDefinition Height="*"/>       <!-- 主要内容 -->
                <RowDefinition Height="Auto"/>    <!-- 底部操作栏 -->
            </Grid.RowDefinitions>

            <!-- 标题区域 -->
            <StackPanel Grid.Row="0" Margin="0,0,0,20">
                <TextBlock Text="💬 Flyout 控件展示"
                           FontSize="32"
                           FontWeight="Bold"
                           Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                           Margin="0,0,0,8"/>
                <TextBlock Text="展示 WPF-UI 官方的 Flyout 控件的各种样式和功能" 
                           FontSize="16" 
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                           TextWrapping="Wrap"/>
                
                <!-- 状态显示 -->
                <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                        BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Padding="16,12"
                        Margin="0,16,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <ui:SymbolIcon Symbol="Info24" 
                                       FontSize="20" 
                                       Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                       VerticalAlignment="Center"
                                       Margin="0,0,12,0"/>
                        <TextBlock Grid.Column="1" 
                                   Text="{Binding StatusMessage}" 
                                   FontSize="14"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                   VerticalAlignment="Center"
                                   TextWrapping="Wrap"/>
                    </Grid>
                </Border>
            </StackPanel>

            <!-- 主要内容区域 -->
            <Border Grid.Row="1" Margin="0,0,0,20">
                <StackPanel>

                    <!-- 基础功能展示 -->
                    <ui:CardExpander Header="🎯 基础功能" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 WPF-UI Flyout 控件的基础功能和用法"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>
                            
                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="基础 Flyout 示例：" FontWeight="Medium" Margin="0,0,0,16"/>
                                    
                                    <!-- 简单的 Flyout 按钮 -->
                                    <TextBlock Text="简单 Flyout" FontWeight="Bold" Margin="0,0,0,8"/>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                                        <ui:Button Content="显示简单 Flyout" 
                                                   Command="{Binding ShowSimpleFlyoutCommand}"
                                                   Margin="0,0,8,0"/>
                                        
                                        <ui:Button Content="显示信息 Flyout" 
                                                   Command="{Binding ShowInfoFlyoutCommand}"/>
                                    </StackPanel>

                                    <!-- 不同位置的 Flyout 按钮 -->
                                    <TextBlock Text="不同位置的 Flyout" FontWeight="Bold" Margin="0,0,0,8"/>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                                        <ui:Button Content="顶部" 
                                                   Command="{Binding ShowTopFlyoutCommand}"
                                                   Margin="0,0,4,0"/>
                                        <ui:Button Content="底部" 
                                                   Command="{Binding ShowBottomFlyoutCommand}"
                                                   Margin="4,0,4,0"/>
                                        <ui:Button Content="左侧" 
                                                   Command="{Binding ShowLeftFlyoutCommand}"
                                                   Margin="4,0,4,0"/>
                                        <ui:Button Content="右侧" 
                                                   Command="{Binding ShowRightFlyoutCommand}"
                                                   Margin="4,0,0,0"/>
                                    </StackPanel>

                                    <!-- 菜单类型的 Flyout -->
                                    <TextBlock Text="菜单类型的 Flyout" FontWeight="Bold" Margin="0,0,0,8"/>
                                    <ui:Button Content="操作菜单" 
                                               Command="{Binding ShowMenuFlyoutCommand}"
                                               Margin="0,0,0,16"/>

                                    <!-- 说明文字 -->
                                    <TextBlock Text="注意：WPF-UI 的 Flyout 控件是一个独立的浮出面板控件，通过 IsOpen 属性控制显示/隐藏。" 
                                               FontSize="12"
                                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                               TextWrapping="Wrap"
                                               Margin="0,0,0,8"/>
                                    <TextBlock Text="它提供了现代化的浮出面板功能，适用于显示临时内容、菜单、通知等场景。" 
                                               FontSize="12"
                                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                               TextWrapping="Wrap"/>
                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="基础功能代码示例"
                                Language="XAML"
                                Description="展示 WPF-UI Flyout 控件的基础用法"
                                ShowTabs="True"
                                XamlCode="{Binding BasicXamlExample}"
                                CSharpCode="{Binding BasicCSharpExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 高级功能展示 -->
                    <ui:CardExpander Header="⚡ 高级功能" IsExpanded="False" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 WPF-UI Flyout 控件的高级功能和组合用法"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>
                            
                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="高级 Flyout 示例：" FontWeight="Medium" Margin="0,0,0,16"/>
                                    
                                    <TextBlock Text="Flyout 控件的高级功能包括：" Margin="0,0,0,8"/>
                                    <TextBlock Text="• 支持不同的显示位置（Top、Bottom、Left、Right）" Margin="0,0,0,4"/>
                                    <TextBlock Text="• 可以包含任何类型的内容（文本、图片、表单等）" Margin="0,0,0,4"/>
                                    <TextBlock Text="• 支持事件处理（Opened、Closed）" Margin="0,0,0,4"/>
                                    <TextBlock Text="• 可以通过编程方式控制显示和隐藏" Margin="0,0,0,4"/>
                                    <TextBlock Text="• 适用于创建上下文菜单、工具提示、通知面板等" Margin="0,0,0,16"/>
                                    
                                    <TextBlock Text="在实际应用中，Flyout 常用于：" Margin="0,0,0,8"/>
                                    <TextBlock Text="• 用户菜单和设置面板" Margin="0,0,0,4"/>
                                    <TextBlock Text="• 快速操作和上下文菜单" Margin="0,0,0,4"/>
                                    <TextBlock Text="• 通知和消息提示" Margin="0,0,0,4"/>
                                    <TextBlock Text="• 表单输入和数据选择" Margin="0,0,0,4"/>
                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="高级功能代码示例"
                                Language="XAML"
                                Description="展示 WPF-UI Flyout 控件的高级用法"
                                ShowTabs="True"
                                XamlCode="{Binding AdvancedXamlExample}"
                                CSharpCode="{Binding AdvancedCSharpExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 样式展示 -->
                    <ui:CardExpander Header="🎨 样式展示" IsExpanded="False" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 WPF-UI Flyout 控件的各种样式和主题适配"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>
                            
                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="样式代码示例"
                                Language="XAML"
                                Description="展示如何使用不同的 Flyout 样式和主题"
                                ShowTabs="False"
                                XamlCode="{Binding StylesXamlExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                </StackPanel>
            </Border>

            <!-- 底部操作栏 -->
            <Border Grid.Row="2" 
                    Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Padding="16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 统计信息 -->
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock Text="{Binding InteractionCount, StringFormat='交互次数: {0}'}" 
                                   FontSize="14" 
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   Margin="0,0,20,0"/>
                        <TextBlock Text="{Binding LastAction, StringFormat='最后操作: {0}'}" 
                                   FontSize="14" 
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                    </StackPanel>

                    <!-- 操作按钮 -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <ui:Button Content="重置计数" 
                                   Command="{Binding ResetCountCommand}"
                                   Appearance="Secondary"
                                   Margin="0,0,8,0"/>
                        <ui:Button Content="清除状态" 
                                   Command="{Binding ClearStatusCommand}"
                                   Appearance="Secondary"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Flyout 控件定义 -->
            <ui:Flyout x:Name="SimpleFlyout"
                       Placement="Bottom"
                       IsOpen="{Binding IsSimpleFlyoutOpen}">
                <StackPanel Margin="16" MinWidth="200">
                    <TextBlock Text="这是一个简单的 Flyout"
                               FontWeight="Medium"
                               Margin="0,0,0,8"/>
                    <TextBlock Text="Flyout 提供了轻量级的浮出面板功能。"
                               TextWrapping="Wrap"
                               Margin="0,0,0,12"/>
                    <Button Content="关闭"
                            Command="{Binding HandleInteractionCommand}"
                            CommandParameter="关闭简单Flyout"
                            HorizontalAlignment="Left"/>
                </StackPanel>
            </ui:Flyout>

            <ui:Flyout x:Name="InfoFlyout"
                       Placement="Bottom"
                       IsOpen="{Binding IsInfoFlyoutOpen}">
                <Grid Margin="16" MinWidth="250">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <ui:SymbolIcon Grid.Column="0"
                                   Symbol="Info24"
                                   FontSize="24"
                                   Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                   Margin="0,0,12,0"
                                   VerticalAlignment="Top"/>

                    <StackPanel Grid.Column="1">
                        <TextBlock Text="信息提示"
                                   FontWeight="Bold"
                                   Margin="0,0,0,8"/>
                        <TextBlock Text="这是一个包含图标的信息 Flyout，用于显示重要提示。"
                                   TextWrapping="Wrap"
                                   Margin="0,0,0,12"/>
                        <Button Content="我知道了"
                                Command="{Binding HandleInteractionCommand}"
                                CommandParameter="确认信息"
                                HorizontalAlignment="Left"/>
                    </StackPanel>
                </Grid>
            </ui:Flyout>

            <ui:Flyout x:Name="TopFlyout"
                       Placement="Top"
                       IsOpen="{Binding IsTopFlyoutOpen}">
                <StackPanel Margin="12">
                    <TextBlock Text="顶部 Flyout" FontWeight="Medium"/>
                    <TextBlock Text="显示在按钮上方" FontSize="12"/>
                </StackPanel>
            </ui:Flyout>

            <ui:Flyout x:Name="BottomFlyout"
                       Placement="Bottom"
                       IsOpen="{Binding IsBottomFlyoutOpen}">
                <StackPanel Margin="12">
                    <TextBlock Text="底部 Flyout" FontWeight="Medium"/>
                    <TextBlock Text="显示在按钮下方" FontSize="12"/>
                </StackPanel>
            </ui:Flyout>

            <ui:Flyout x:Name="LeftFlyout"
                       Placement="Left"
                       IsOpen="{Binding IsLeftFlyoutOpen}">
                <StackPanel Margin="12">
                    <TextBlock Text="左侧 Flyout" FontWeight="Medium"/>
                    <TextBlock Text="显示在按钮左侧" FontSize="12"/>
                </StackPanel>
            </ui:Flyout>

            <ui:Flyout x:Name="RightFlyout"
                       Placement="Right"
                       IsOpen="{Binding IsRightFlyoutOpen}">
                <StackPanel Margin="12">
                    <TextBlock Text="右侧 Flyout" FontWeight="Medium"/>
                    <TextBlock Text="显示在按钮右侧" FontSize="12"/>
                </StackPanel>
            </ui:Flyout>

            <ui:Flyout x:Name="MenuFlyout"
                       Placement="Bottom"
                       IsOpen="{Binding IsMenuFlyoutOpen}">
                <StackPanel Margin="8" MinWidth="150">
                    <ui:Button Content="新建"
                               Command="{Binding HandleInteractionCommand}"
                               CommandParameter="菜单-新建"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Appearance="Transparent"
                               Margin="0,0,0,4"/>
                    <ui:Button Content="编辑"
                               Command="{Binding HandleInteractionCommand}"
                               CommandParameter="菜单-编辑"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Appearance="Transparent"
                               Margin="0,0,0,4"/>
                    <ui:Button Content="删除"
                               Command="{Binding HandleInteractionCommand}"
                               CommandParameter="菜单-删除"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Appearance="Transparent"
                               Margin="0,0,0,4"/>
                    <Separator Margin="0,4,0,4"/>
                    <ui:Button Content="设置"
                               Command="{Binding HandleInteractionCommand}"
                               CommandParameter="菜单-设置"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Appearance="Transparent"/>
                </StackPanel>
            </ui:Flyout>

        </Grid>
    </ScrollViewer>
</UserControl>
