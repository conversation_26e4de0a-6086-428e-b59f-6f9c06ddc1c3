<UserControl
    d:DesignHeight="600"
    d:DesignWidth="800"
    mc:Ignorable="d"
    x:Class="WPFTest.Views.SubMenuView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:converters="clr-namespace:Zylo.WPF.Converters;assembly=Zylo.WPF"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <UserControl.Resources>
        <!--  转换器  -->
        <converters:SimpleIconConverter x:Key="SimpleIconConverter" />
        <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter" />
        <converters:BreadcrumbStyleConverter x:Key="BreadcrumbStyleConverter" />
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />




    </UserControl.Resources>
    <Border
        Background="{DynamicResource ControlFillColorDefaultBrush}"
        BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
        BorderThickness="1"
        CornerRadius="8"
        Cursor="Hand"
        Margin="0">

        <!--  触发器已移除 - 取消加载动画以提升性能  -->

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="200" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <!--  顶部横幅 - 仿照WPF-UI官方设计  -->
            <Border
                Background="{DynamicResource ControlFillColorDefaultBrush}"
                BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                BorderThickness="1"
                CornerRadius="8"
                Grid.Row="0"
                Margin="15,15,15,5"
                Opacity="1"
                x:Name="TopBanner">

                <Grid>
                    <!--  装饰性圆点 - 带动画效果  -->
                    <Canvas>
                        <Ellipse
                            Canvas.Left="50"
                            Canvas.Top="30"
                            Fill="{DynamicResource SystemAccentColorSecondaryBrush}"
                            Height="8"
                            Opacity="0.3"
                            Width="8"
                            x:Name="Dot1" />
                        <Ellipse
                            Canvas.Left="150"
                            Canvas.Top="50"
                            Fill="{DynamicResource SystemAccentColorSecondaryBrush}"
                            Height="12"
                            Opacity="0.2"
                            Width="12"
                            x:Name="Dot2" />
                        <Ellipse
                            Canvas.Left="250"
                            Canvas.Top="25"
                            Fill="{DynamicResource SystemAccentColorSecondaryBrush}"
                            Height="6"
                            Opacity="0.4"
                            Width="6"
                            x:Name="Dot3" />
                        <Ellipse
                            Canvas.Left="350"
                            Canvas.Top="40"
                            Fill="{DynamicResource SystemAccentColorSecondaryBrush}"
                            Height="10"
                            Opacity="0.25"
                            Width="10"
                            x:Name="Dot4" />
                        <Ellipse
                            Canvas.Left="450"
                            Canvas.Top="35"
                            Fill="{DynamicResource SystemAccentColorSecondaryBrush}"
                            Height="14"
                            Opacity="0.15"
                            Width="14" />
                        <Ellipse
                            Canvas.Left="550"
                            Canvas.Top="45"
                            Fill="{DynamicResource SystemAccentColorSecondaryBrush}"
                            Height="8"
                            Opacity="0.3"
                            Width="8" />
                        <Ellipse
                            Canvas.Left="650"
                            Canvas.Top="30"
                            Fill="{DynamicResource SystemAccentColorSecondaryBrush}"
                            Height="6"
                            Opacity="0.4"
                            Width="6" />
                        <Ellipse
                            Canvas.Left="750"
                            Canvas.Top="50"
                            Fill="{DynamicResource SystemAccentColorSecondaryBrush}"
                            Height="10"
                            Opacity="0.2"
                            Width="10" />
                    </Canvas>

                    <!--  内容区域  -->
                    <Grid Margin="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <!--  大图标  -->
                        <Border
                            BorderBrush="{DynamicResource SystemAccentColorPrimaryBrush}"
                            BorderThickness="2"
                            CornerRadius="16"
                            Grid.Column="0"
                            Height="90"
                            Margin="5"
                            VerticalAlignment="Center"
                            Width="90">
                            <ContentPresenter
                                Height="48"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Width="48">
                                <ContentPresenter.Content>
                                    <MultiBinding>
                                        <MultiBinding.Converter>
                                            <StaticResource ResourceKey="SimpleIconConverter" />
                                        </MultiBinding.Converter>
                                        <Binding Path="ParentItem.WpfUiSymbol" />
                                        <Binding Path="ParentItem.ZyloSymbol" />
                                        <Binding Path="ParentItem.Emoji" />
                                        <Binding Source="32" />
                                        <!--  字体大小  -->
                                        <Binding Source="SystemAccentColorPrimaryBrush" />
                                        <!--  字体颜色  -->
                                    </MultiBinding>
                                </ContentPresenter.Content>
                            </ContentPresenter>
                        </Border>

                        <!--  标题和描述  -->
                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                            <TextBlock
                                FontSize="32"
                                FontWeight="Bold"
                                Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                Text="{Binding ParentItemName}" />
                            <!--  <TextBlock Text="WPF UI provides the fluent experience in your known and loved WPF framework. Intuitive design, themes, navigation and new immersive controls. All natively and effortlessly. Library changes the base elements like Page, ToggleButton or List, and also includes additional controls like Navigation, NumberBox, Dialog or Snackbar."  -->
                            <!--  FontSize="14"  -->
                            <!--  Margin="0,8,0,0"  -->
                            <!--  Foreground="White"  -->
                            <!--  Opacity="0.9"  -->
                            <!--  TextWrapping="Wrap"  -->
                            <!--  LineHeight="20" />  -->
                            <!--  <TextBlock Text="Support the development of WPF UI and other innovative projects by becoming a sponsor on GitHub! Your monthly or one-time contributions help us continue to deliver high-quality, open-source solutions that empower developers worldwide."  -->
                            <!--  FontSize="12"  -->
                            <!--  Margin="0,8,0,0"  -->
                            <!--  Foreground="White"  -->
                            <!--  Opacity="0.8"  -->
                            <!--  TextWrapping="Wrap"  -->
                            <!--  LineHeight="18" />  -->
                        </StackPanel>
                    </Grid>
                </Grid>
            </Border>


            <!--  面包屑导航  -->
            <ui:BreadcrumbBar
                FontSize="14"
                Grid.Row="1"
                ItemsSource="{Binding BreadcrumbItems}"
                Margin="20,10,20,10"
                Opacity="1"
                x:Name="BreadcrumbSection">
                <ui:BreadcrumbBar.ItemTemplate>
                    <DataTemplate>
                        <Button
                            Background="Transparent"
                            BorderThickness="0"
                            Command="{Binding DataContext.BreadcrumbClickCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                            CommandParameter="{Binding}"
                            Content="{Binding Name}"
                            Cursor="Hand"
                            FontSize="14"
                            Padding="4,2">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}" Padding="{TemplateBinding Padding}">
                                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                                                </Border>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                    <!--  动态颜色：当前层级用强调色，其他用次要色  -->
                                    <Setter Property="Foreground">
                                        <Setter.Value>
                                            <MultiBinding Converter="{StaticResource BreadcrumbStyleConverter}">
                                                <Binding />
                                                <Binding Path="DataContext.ParentItem" RelativeSource="{RelativeSource AncestorType=UserControl}" />
                                            </MultiBinding>
                                        </Setter.Value>
                                    </Setter>
                                    <!--  动态字体粗细：当前层级用半粗体  -->
                                    <Setter Property="FontWeight">
                                        <Setter.Value>
                                            <MultiBinding Converter="{StaticResource BreadcrumbStyleConverter}">
                                                <Binding />
                                                <Binding Path="DataContext.ParentItem" RelativeSource="{RelativeSource AncestorType=UserControl}" />
                                            </MultiBinding>
                                        </Setter.Value>
                                    </Setter>
                                    <Style.Triggers>
                                        <!--  悬停效果  -->
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Foreground" Value="{DynamicResource SystemAccentColorPrimaryBrush}" />
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Button.Style>
                        </Button>
                    </DataTemplate>
                </ui:BreadcrumbBar.ItemTemplate>
            </ui:BreadcrumbBar>

            <!--  子项目列表 - 美观的卡片式网格布局  -->
            <ScrollViewer
                Grid.Row="2"
                HorizontalScrollBarVisibility="Disabled"
                Margin="20,10,20,20"
                Opacity="1"
                VerticalScrollBarVisibility="Auto"
                x:Name="ContentSection">
                <ItemsControl ItemsSource="{Binding FilteredSubMenuItems}">
                    <!--  网格布局 - 自适应列数  -->
                    <ItemsControl.ItemsPanel>
                        <ItemsPanelTemplate>
                            <WrapPanel Orientation="Horizontal" />
                        </ItemsPanelTemplate>
                    </ItemsControl.ItemsPanel>

                    <!--  卡片样式  -->
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Border
                                Background="{DynamicResource ControlFillColorDefaultBrush}"
                                BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                BorderThickness="1"
                                CornerRadius="8"
                                Cursor="Hand"
                                Height="100"
                                Margin="8"
                                Width="320">

                                <!--  卡片动画变换  -->
                                <Border.RenderTransform>
                                    <TransformGroup>
                                        <ScaleTransform ScaleX="1" ScaleY="1" />
                                        <TranslateTransform Y="0" />
                                    </TransformGroup>
                                </Border.RenderTransform>
                                <Border.RenderTransformOrigin>
                                    <Point X="0.5" Y="0.5" />
                                </Border.RenderTransformOrigin>

                                <!--  卡片样式和动画触发器  -->
                                <Border.Style>
                                    <Style TargetType="Border">
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}" />
                                                <Setter Property="BorderBrush" Value="{DynamicResource SystemAccentColorSecondaryBrush}" />
                                                <Trigger.EnterActions>
                                                    <BeginStoryboard Storyboard="{StaticResource ZyloCardHoverEnterStrongAnimation}" />
                                                </Trigger.EnterActions>
                                                <Trigger.ExitActions>
                                                    <BeginStoryboard Storyboard="{StaticResource ZyloCardHoverExitStrongAnimation}" />
                                                </Trigger.ExitActions>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Border.Style>

                                <Border.InputBindings>
                                    <MouseBinding
                                        Command="{Binding DataContext.SubMenuItemClickCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                        CommandParameter="{Binding}"
                                        MouseAction="LeftClick" />
                                </Border.InputBindings>

                                <!--  卡片内容  -->
                                <Grid Margin="16">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>

                                    <!--  左侧图标区域  -->
                                    <StackPanel
                                        Grid.Column="0"
                                        HorizontalAlignment="Center"
                                        Margin="0,0,16,0"
                                        VerticalAlignment="Top">
                                        <!--  图标  -->
                                        <ContentPresenter
                                            Height="32"
                                            HorizontalAlignment="Center"
                                            Width="32">
                                            <ContentPresenter.Content>
                                                <MultiBinding>
                                                    <MultiBinding.Converter>
                                                        <StaticResource ResourceKey="SimpleIconConverter" />
                                                    </MultiBinding.Converter>
                                                    <Binding Path="WpfUiSymbol" />
                                                    <Binding Path="ZyloSymbol" />
                                                    <Binding Path="Emoji" />
                                                </MultiBinding>
                                            </ContentPresenter.Content>
                                        </ContentPresenter>

                                        <!--  子项目数量指示器  -->
                                        <Border
                                            Background="{DynamicResource SystemAccentColorSecondaryBrush}"
                                            CornerRadius="10"
                                            HorizontalAlignment="Center"
                                            Margin="0,4,0,0"
                                            Padding="6,2"
                                            Visibility="{Binding HasChildren, Converter={StaticResource BooleanToVisibilityConverter}}">
                                            <TextBlock
                                                FontSize="10"
                                                FontWeight="Bold"
                                                Foreground="White"
                                                Text="{Binding Children.Count}" />
                                        </Border>
                                    </StackPanel>

                                    <!--  中间内容  -->
                                    <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                        <TextBlock
                                            FontSize="16"
                                            FontWeight="SemiBold"
                                            Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                            Margin="0,0,0,4"
                                            Text="{Binding Name}"
                                            TextWrapping="Wrap" />
                                        <TextBlock
                                            FontSize="12"
                                            Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                            Text="{Binding Description}"
                                            TextWrapping="Wrap"
                                            Visibility="{Binding Description, Converter={StaticResource StringToVisibilityConverter}}" />
                                    </StackPanel>

                                    <!--  右侧箭头  -->
                                    <ui:SymbolIcon
                                        FontSize="16"
                                        Foreground="{DynamicResource TextFillColorTertiaryBrush}"
                                        Grid.Column="2"
                                        Symbol="ChevronRight24"
                                        VerticalAlignment="Center" />
                                </Grid>
                            </Border>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </ScrollViewer>
        </Grid>
    </Border>

</UserControl>