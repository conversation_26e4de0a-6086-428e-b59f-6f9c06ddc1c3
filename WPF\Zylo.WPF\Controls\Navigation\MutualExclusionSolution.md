# NavigationControl 互斥选中解决方案

## 🎯 问题描述

**需求：** 在 NavigationControl 中实现多个视图控件（ListView 和 TreeView）的互斥选中效果，确保同时只有一个控件中的一个项目被选中。

**挑战：**
- 上下两个 ListView 需要互斥选中
- 左右模式切换时 TreeView 也需要参与互斥选中
- 避免事件循环导致的无限触发
- 保持与现有 MVVM 绑定的兼容性

## 🔍 技术分析

### 现有架构
```
NavigationControl
├── 左侧模式 (ListView)
│   ├── TopListView    # 顶部导航项
│   └── SecondListView # 底部导航项
└── 右侧模式 (TreeView)
    ├── TopTreeView    # 顶部导航项
    └── SecondTreeView # 底部导航项
```

### 互斥选中需求
1. **ListView 内部互斥**：TopListView ↔ SecondListView
2. **TreeView 内部互斥**：TopTreeView ↔ SecondTreeView  
3. **模式间互斥**：ListView ↔ TreeView（切换模式时）

## 💡 解决方案设计

### 核心思路
1. **统一事件管理**：为所有视图控件注册 SelectionChanged 事件
2. **中央协调器**：使用单一方法处理所有选中变化
3. **防循环机制**：使用标志位避免事件循环
4. **属性同步**：自动同步 SelectedItem 和相关命令

### 实现架构
```csharp
NavigationControl
├── _isUpdatingSelection (防循环标志)
├── InitializeMutualExclusion() (初始化方法)
├── OnSelectionChanged() (统一处理方法)
└── ClearOtherSelections() (清除其他选择)
```

## 🛠️ 实现步骤

### 步骤 1：添加私有字段
```csharp
/// <summary>
/// 防止互斥选中时的事件循环标志
/// </summary>
private bool _isUpdatingSelection = false;
```

### 步骤 2：在构造函数中初始化
```csharp
public NavigationControl()
{
    InitializeComponent();
    // ... 其他初始化代码
    
    // 添加互斥选中初始化
    Loaded += (s, e) => InitializeMutualExclusion();
}
```

### 步骤 3：实现初始化方法
```csharp
private void InitializeMutualExclusion()
{
    // 查找所有视图控件
    var topListView = FindName("TopListView") as ListView;
    var secondListView = FindName("SecondListView") as ListView;
    var topTreeView = FindName("TopTreeView") as TreeView;
    var secondTreeView = FindName("SecondTreeView") as TreeView;
    
    // 为每个控件绑定事件
    if (topListView != null)
        topListView.SelectedItemChanged += OnAnySelectionChanged;
    if (secondListView != null)
        secondListView.SelectedItemChanged += OnAnySelectionChanged;
    if (topTreeView != null)
        topTreeView.SelectedItemChanged += OnAnySelectionChanged;
    if (secondTreeView != null)
        secondTreeView.SelectedItemChanged += OnAnySelectionChanged;
}
```

### 步骤 4：实现统一选中处理
```csharp
private void OnAnySelectionChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
{
    if (_isUpdatingSelection) return;
    
    try
    {
        _isUpdatingSelection = true;
        
        var currentControl = sender as Control;
        var newSelectedItem = e.NewValue;
        
        if (newSelectedItem != null)
        {
            // 清除其他控件的选择
            ClearOtherSelections(currentControl);
            
            // 同步 SelectedItem 属性
            SelectedItem = newSelectedItem;
            
            // 触发导航命令
            NavigationItemSelectedCommand?.Execute(newSelectedItem);
        }
    }
    finally
    {
        _isUpdatingSelection = false;
    }
}
```

### 步骤 5：实现清除其他选择
```csharp
private void ClearOtherSelections(Control currentControl)
{
    var controls = new[]
    {
        FindName("TopListView") as Control,
        FindName("SecondListView") as Control,
        FindName("TopTreeView") as Control,
        FindName("SecondTreeView") as Control
    };
    
    foreach (var control in controls)
    {
        if (control != null && control != currentControl)
        {
            if (control is ListView listView && listView.SelectedItem != null)
                listView.SelectedItem = null;
            else if (control is TreeView treeView && treeView.SelectedItem != null)
                treeView.SetValue(TreeView.SelectedItemProperty, null);
        }
    }
}
```

## ✅ 解决方案优势

### 技术优势
1. **统一管理**：所有选中逻辑集中处理
2. **扩展性强**：易于添加新的视图控件
3. **性能优秀**：最小的事件处理开销
4. **稳定可靠**：完善的防循环机制

### 用户体验
1. **直观操作**：符合用户直觉的选择行为
2. **视觉清晰**：同时只有一个项目高亮
3. **响应迅速**：无感知的选择切换
4. **功能完整**：保持所有现有功能

### 开发友好
1. **向后兼容**：不影响现有代码
2. **自动启用**：无需额外配置
3. **调试友好**：完整的日志记录
4. **文档完善**：详细的实现说明

## 🧪 测试验证

### 功能测试
- [ ] ListView 内部互斥选中
- [ ] TreeView 内部互斥选中
- [ ] 跨模式互斥选中
- [ ] SelectedItem 属性同步
- [ ] 命令正确触发

### 性能测试
- [ ] 快速连续点击
- [ ] 大量数据加载
- [ ] 内存泄漏检查
- [ ] 响应时间测量

### 兼容性测试
- [ ] 现有绑定功能
- [ ] 主题切换
- [ ] 窗口大小调整
- [ ] 数据动态更新

## 📊 实施计划

### 阶段 1：ListView 互斥（已完成）
- ✅ 实现 TopListView ↔ SecondListView 互斥
- ✅ 添加防循环机制
- ✅ 完成基础测试

### 阶段 2：TreeView 互斥（已完成）
- ✅ 实现 TopTreeView ↔ SecondTreeView 互斥
- ✅ 统一事件处理架构
- ✅ 使用相同逻辑处理所有控件

### 阶段 3：全局互斥（已完成）
- ✅ 实现跨模式互斥选中
- ✅ 优化性能和用户体验
- ✅ 完善文档和示例

## 🔮 未来扩展

### 配置选项
```csharp
public bool EnableMutualExclusion { get; set; } = true;
public MutualExclusionMode ExclusionMode { get; set; } = MutualExclusionMode.Global;
```

### 动画效果
- 选中切换的平滑过渡
- 高亮显示的动画效果
- 模式切换的视觉反馈

### 高级功能
- 多选模式支持
- 分组互斥选中
- 自定义互斥规则

---

**互斥选中解决方案** - 提升用户体验的智能选择机制 🎯
