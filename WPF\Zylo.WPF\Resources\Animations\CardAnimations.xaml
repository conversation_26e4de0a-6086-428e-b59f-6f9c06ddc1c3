<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 🎴 卡片控件专用动画 -->
    
    <!-- 标准卡片悬停进入动画 - 柔和的缩放和上移效果 -->
    <Storyboard x:Key="ZyloCardHoverEnterAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleX)"
                       To="1.02" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="1"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleY)"
                       To="1.02" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="1"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(TranslateTransform.Y)"
                       To="-2" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>
    
    <!-- 标准卡片悬停退出动画 -->
    <Storyboard x:Key="ZyloCardHoverExitAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleX)"
                       To="1" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleY)"
                       To="1" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(TranslateTransform.Y)"
                       To="0" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 强调卡片悬停进入动画 - 更强的效果，用于重要内容 -->
    <Storyboard x:Key="ZyloCardHoverEnterStrongAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleX)"
                       To="1.03" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleY)"
                       To="1.03" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(TranslateTransform.Y)"
                       To="-3" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>
    
    <!-- 强调卡片悬停退出动画 -->
    <Storyboard x:Key="ZyloCardHoverExitStrongAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleX)"
                       To="1" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleY)"
                       To="1" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(TranslateTransform.Y)"
                       To="0" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 卡片点击动画 -->
    <Storyboard x:Key="ZyloCardClickAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleX)"
                       To="0.98" Duration="0:0:0.1"
                       AutoReverse="True">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleY)"
                       To="0.98" Duration="0:0:0.1"
                       AutoReverse="True">
            <DoubleAnimation.EasingFunction>
                <PowerEase EasingMode="EaseOut" Power="2"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

</ResourceDictionary>
