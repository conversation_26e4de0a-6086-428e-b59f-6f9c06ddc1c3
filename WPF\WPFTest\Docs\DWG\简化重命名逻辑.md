# 简化重命名逻辑

## 🎯 设计思路
按照用户建议，采用最简单直接的方式处理文件重命名：

1. **记录原始全名**
2. **用户输入新名字**  
3. **检查新名字是否有.dwg后缀**
4. **有就去除，没有就加上**

## ✅ 简化后的实现

### 对话框逻辑
```csharp
// 显示完整文件名，全选方便用户输入
FileNameTextBox.Text = currentFileName;
FileNameTextBox.SelectAll();

// 处理用户输入
private void ConfirmRename()
{
    var newName = FileNameTextBox.Text?.Trim();
    
    // 简单处理：检查是否有.dwg后缀，有就去除，最后统一加上
    if (newName.EndsWith(".dwg", StringComparison.OrdinalIgnoreCase))
    {
        newName = newName.Substring(0, newName.Length - 4);
    }
    newName += ".dwg";
    
    NewFileName = newName;
}
```

### ViewModel逻辑
```csharp
// 简单处理：检查是否有.dwg后缀，有就去除，最后统一加上
if (newName.EndsWith(".dwg", StringComparison.OrdinalIgnoreCase))
{
    newName = newName.Substring(0, newName.Length - 4);
}
newName += ".dwg";
```

## 📊 处理效果

| 用户输入 | 处理结果 | 说明 |
|----------|----------|------|
| `新文件名` | `新文件名.dwg` | 没有后缀，直接加上 |
| `新文件名.dwg` | `新文件名.dwg` | 有后缀，去除后再加上 |
| `新文件名.DWG` | `新文件名.dwg` | 大小写不敏感，统一为小写 |
| `新文件名.dwg.dwg` | `新文件名.dwg.dwg` | 只处理最后的.dwg |

## 🎯 优势

### 1. 简单直接
- **逻辑清晰** - 一看就懂的处理方式
- **代码简洁** - 只有几行代码
- **易于维护** - 不容易出错

### 2. 用户友好
- **显示全名** - 用户看到完整的文件名
- **全选内容** - 方便用户直接输入新名称
- **自动处理** - 系统自动处理扩展名

### 3. 结果可预期
- **统一格式** - 最终都是`.dwg`格式
- **避免重复** - 不会出现`.dwg.dwg`
- **大小写统一** - 统一为小写`.dwg`

## 🔍 对比复杂方案

### 之前的复杂逻辑
```csharp
// 获取原始扩展名
var originalExtension = Path.GetExtension(_originalFileName);
if (!string.IsNullOrEmpty(originalExtension))
{
    // 检查是否已包含扩展名
    if (newName.EndsWith(originalExtension, StringComparison.OrdinalIgnoreCase))
    {
        newName = newName.Substring(0, newName.Length - originalExtension.Length);
    }
    // 添加原始扩展名
    newName += originalExtension;
}
```

### 现在的简单逻辑
```csharp
// 检查是否有.dwg后缀，有就去除，最后统一加上
if (newName.EndsWith(".dwg", StringComparison.OrdinalIgnoreCase))
{
    newName = newName.Substring(0, newName.Length - 4);
}
newName += ".dwg";
```

## 📝 实际使用场景

### 场景1：完全重命名
```
原文件: 复杂的文件名.dwg
用户输入: 简单名
结果: 简单名.dwg ✅
```

### 场景2：带扩展名输入
```
原文件: 复杂的文件名.dwg  
用户输入: 简单名.dwg
结果: 简单名.dwg ✅
```

### 场景3：大小写混合
```
原文件: 复杂的文件名.dwg
用户输入: 简单名.DWG
结果: 简单名.dwg ✅
```

## 🚀 总结

用户的建议非常正确！简单的方案往往是最好的方案：

1. **不需要复杂的Path类方法**
2. **不需要考虑各种边界情况**
3. **专门针对.dwg文件优化**
4. **逻辑清晰，易于理解和维护**

这就是"Keep It Simple, Stupid (KISS)"原则的完美体现！
