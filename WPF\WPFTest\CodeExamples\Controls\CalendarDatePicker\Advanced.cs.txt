// CalendarDatePicker C# 高级用法示例
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.ComponentModel;

namespace WPFTest.ViewModels.InputControls
{
    /// <summary>
    /// CalendarDatePicker 高级示例 ViewModel - 展示高级功能和程序化控制
    /// </summary>
    public partial class CalendarDatePickerPageViewModel : ObservableObject
    {
        #region 高级日期属性

        /// <summary>
        /// 开始日期（用于日期范围选择）
        /// </summary>
        [ObservableProperty]
        private DateTime? startDate = DateTime.Today;

        /// <summary>
        /// 结束日期（用于日期范围选择）
        /// </summary>
        [ObservableProperty]
        private DateTime? endDate = DateTime.Today.AddDays(7);

        /// <summary>
        /// 程序化控制的日期
        /// </summary>
        [ObservableProperty]
        private DateTime? programmaticDate = DateTime.Today;

        /// <summary>
        /// 日历是否打开 - 程序化控制
        /// </summary>
        [ObservableProperty]
        private bool isCalendarOpen = false;

        /// <summary>
        /// 验证的生日日期（必须满18岁）
        /// </summary>
        [ObservableProperty]
        private DateTime? validatedBirthDate;

        /// <summary>
        /// 预约日期（不能是过去的日期）
        /// </summary>
        [ObservableProperty]
        private DateTime? appointmentDate = DateTime.Today.AddDays(1);

        /// <summary>
        /// 日期范围间隔天数 - 计算属性
        /// </summary>
        public int DateRangeDays
        {
            get
            {
                if (StartDate.HasValue && EndDate.HasValue)
                {
                    return Math.Abs((EndDate.Value - StartDate.Value).Days);
                }
                return 0;
            }
        }

        /// <summary>
        /// 年龄验证消息
        /// </summary>
        [ObservableProperty]
        private string ageValidationMessage = string.Empty;

        /// <summary>
        /// 预约验证消息
        /// </summary>
        [ObservableProperty]
        private string appointmentValidationMessage = string.Empty;

        #endregion

        #region 高级命令

        /// <summary>
        /// 打开日历命令 - 程序化控制
        /// </summary>
        [RelayCommand]
        private void OpenCalendar()
        {
            IsCalendarOpen = true;
            StatusMessage = "📅 程序化打开日历";
            InteractionCount++;
        }

        /// <summary>
        /// 关闭日历命令 - 程序化控制
        /// </summary>
        [RelayCommand]
        private void CloseCalendar()
        {
            IsCalendarOpen = false;
            StatusMessage = "📅 程序化关闭日历";
            InteractionCount++;
        }

        /// <summary>
        /// 设置生日命令
        /// </summary>
        [RelayCommand]
        private void SetBirthday()
        {
            ProgrammaticDate = new DateTime(1990, 6, 15);
            StatusMessage = "🎂 设置为示例生日: 1990-06-15";
            InteractionCount++;
        }

        /// <summary>
        /// 设置明年同一天命令
        /// </summary>
        [RelayCommand]
        private void SetNextYear()
        {
            if (ProgrammaticDate.HasValue)
            {
                ProgrammaticDate = ProgrammaticDate.Value.AddYears(1);
            }
            else
            {
                ProgrammaticDate = DateTime.Today.AddYears(1);
            }
            StatusMessage = $"📅 设置为明年: {ProgrammaticDate.Value:yyyy-MM-dd}";
            InteractionCount++;
        }

        /// <summary>
        /// 设置日期范围命令
        /// </summary>
        [RelayCommand]
        private void SetDateRange(string? rangeType)
        {
            switch (rangeType)
            {
                case "week":
                    StartDate = DateTime.Today;
                    EndDate = DateTime.Today.AddDays(7);
                    StatusMessage = "📅 设置为一周范围";
                    break;
                case "month":
                    StartDate = DateTime.Today;
                    EndDate = DateTime.Today.AddMonths(1);
                    StatusMessage = "📅 设置为一个月范围";
                    break;
                case "quarter":
                    StartDate = DateTime.Today;
                    EndDate = DateTime.Today.AddMonths(3);
                    StatusMessage = "📅 设置为一个季度范围";
                    break;
                default:
                    StartDate = DateTime.Today;
                    EndDate = DateTime.Today.AddDays(1);
                    StatusMessage = "📅 设置为默认范围";
                    break;
            }
            InteractionCount++;
        }

        #endregion

        #region 高级事件处理

        /// <summary>
        /// 属性变化处理 - 包含高级功能
        /// </summary>
        private void OnPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            switch (e.PropertyName)
            {
                case nameof(StartDate):
                case nameof(EndDate):
                    // 通知日期范围间隔天数变化
                    OnPropertyChanged(nameof(DateRangeDays));
                    HandleDateRangeChanged();
                    break;
                case nameof(ValidatedBirthDate):
                    ValidateAge();
                    break;
                case nameof(AppointmentDate):
                    ValidateAppointmentDate();
                    break;
                case nameof(IsCalendarOpen):
                    HandleCalendarOpenChanged();
                    break;
            }
        }

        /// <summary>
        /// 处理日期范围变化
        /// </summary>
        private void HandleDateRangeChanged()
        {
            if (StartDate.HasValue && EndDate.HasValue)
            {
                var days = DateRangeDays;
                StatusMessage = $"📅 日期范围: {days} 天 ({StartDate.Value:MM-dd} 到 {EndDate.Value:MM-dd})";
                
                // 确保开始日期不晚于结束日期
                if (StartDate > EndDate)
                {
                    var temp = StartDate;
                    StartDate = EndDate;
                    EndDate = temp;
                    StatusMessage += " (已自动调整顺序)";
                }
            }
        }

        /// <summary>
        /// 验证年龄（必须满18岁）
        /// </summary>
        private void ValidateAge()
        {
            if (ValidatedBirthDate.HasValue)
            {
                var age = DateTime.Today.Year - ValidatedBirthDate.Value.Year;
                if (ValidatedBirthDate.Value.Date > DateTime.Today.AddYears(-age))
                {
                    age--;
                }

                if (age >= 18)
                {
                    AgeValidationMessage = $"✅ 年龄验证通过 (年龄: {age}岁)";
                }
                else
                {
                    AgeValidationMessage = $"❌ 年龄不足18岁 (当前: {age}岁)";
                }
            }
            else
            {
                AgeValidationMessage = "请选择出生日期";
            }
        }

        /// <summary>
        /// 验证预约日期（不能是过去的日期）
        /// </summary>
        private void ValidateAppointmentDate()
        {
            if (AppointmentDate.HasValue)
            {
                if (AppointmentDate.Value.Date >= DateTime.Today)
                {
                    var daysFromNow = (AppointmentDate.Value.Date - DateTime.Today).Days;
                    AppointmentValidationMessage = $"✅ 预约日期有效 ({daysFromNow}天后)";
                }
                else
                {
                    AppointmentValidationMessage = "❌ 预约日期不能是过去的日期";
                }
            }
            else
            {
                AppointmentValidationMessage = "请选择预约日期";
            }
        }

        /// <summary>
        /// 处理日历打开状态变化
        /// </summary>
        private void HandleCalendarOpenChanged()
        {
            var status = IsCalendarOpen ? "打开" : "关闭";
            StatusMessage = $"📅 日历状态变化: {status}";
        }

        #endregion

        #region 实用方法

        /// <summary>
        /// 计算两个日期之间的工作日数量
        /// </summary>
        public int CalculateWorkdays(DateTime? startDate, DateTime? endDate)
        {
            if (!startDate.HasValue || !endDate.HasValue) return 0;

            var start = startDate.Value;
            var end = endDate.Value;
            
            if (start > end)
            {
                var temp = start;
                start = end;
                end = temp;
            }

            int workdays = 0;
            for (var date = start; date <= end; date = date.AddDays(1))
            {
                if (date.DayOfWeek != DayOfWeek.Saturday && date.DayOfWeek != DayOfWeek.Sunday)
                {
                    workdays++;
                }
            }

            return workdays;
        }

        /// <summary>
        /// 获取下一个工作日
        /// </summary>
        public DateTime GetNextWorkday(DateTime date)
        {
            do
            {
                date = date.AddDays(1);
            }
            while (date.DayOfWeek == DayOfWeek.Saturday || date.DayOfWeek == DayOfWeek.Sunday);

            return date;
        }

        /// <summary>
        /// 检查是否为节假日（示例实现）
        /// </summary>
        public bool IsHoliday(DateTime date)
        {
            // 简单的节假日检查示例
            return date.Month == 1 && date.Day == 1 || // 元旦
                   date.Month == 5 && date.Day == 1 || // 劳动节
                   date.Month == 10 && date.Day == 1;  // 国庆节
        }

        #endregion
    }
}
