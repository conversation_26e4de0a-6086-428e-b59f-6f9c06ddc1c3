namespace WPFTest.Models.DragDrop;

/// <summary>
/// 拖拽配置模型 - 定义DWG管理系统的拖拽行为配置
/// </summary>
/// <remarks>
/// 包含：
/// - 专业图标映射
/// - 文件类型图标映射
/// - 文件前缀映射
/// - 拖拽策略配置
/// </remarks>
public class DragDropConfiguration
{
    /// <summary>
    /// 专业图标映射
    /// </summary>
    public Dictionary<string, string> ProfessionIcons { get; set; } = new()
    {
        { "给排水", "💧" },
        { "建筑", "🏢" },
        { "水电", "⚡" },
        { "暖通", "🌡️" },
        { "结构", "🏗️" },
        { "交通", "🚗" },
        { "园林", "🌳" },
        { "其他", "📁" }
    };

    /// <summary>
    /// 文件类型图标映射
    /// </summary>
    public Dictionary<DwgFileType, string> FileTypeIcons { get; set; } = new()
    {
        { DwgFileType.All, "📁" },
        { DwgFileType.Drawing, "📋" },
        { DwgFileType.Construction, "🏗️" },
        { DwgFileType.Template, "📐" },
        { DwgFileType.Base, "🗺️" },
        { DwgFileType.Change, "🔄" },
        { DwgFileType.Calculation, "📊" },
        { DwgFileType.Frame, "🖼️" },
        { DwgFileType.Architecture, "🏢" },
        { DwgFileType.Binding, "🔗" },
        { DwgFileType.Other, "📄" }
    };

    /// <summary>
    /// 文件类型前缀映射
    /// </summary>
    public Dictionary<DwgFileType, string[]> FileTypePrefixes { get; set; } = new()
    {
        { DwgFileType.Drawing, new[] { "S_", "S-" } },
        { DwgFileType.Construction, new[] { "GS_", "GS-" } },
        { DwgFileType.Template, new[] { "M_", "M-" } },
        { DwgFileType.Base, new[] { "D_", "D-" } },
        { DwgFileType.Change, new[] { "Y_", "Y-" } },
        { DwgFileType.Calculation, new[] { "J_", "J-" } },
        { DwgFileType.Frame, new[] { "T_", "T-" } },
        { DwgFileType.Architecture, new[] { "JS_", "JS-", "JZ_", "JZ-" } },
        { DwgFileType.Binding, new[] { "B_", "B-" } }
    };

    /// <summary>
    /// 文件名关键词映射
    /// </summary>
    public Dictionary<DwgFileType, string[]> FileTypeKeywords { get; set; } = new()
    {
        { DwgFileType.Construction, new[] { "施工图", "施工" } },
        { DwgFileType.Drawing, new[] { "平面图", "立面图", "剖面图", "系统图", "详图", "设计图" } },
        { DwgFileType.Template, new[] { "模板" } },
        { DwgFileType.Base, new[] { "底图" } },
        { DwgFileType.Change, new[] { "变更" } },
        { DwgFileType.Calculation, new[] { "计算" } },
        { DwgFileType.Frame, new[] { "图框" } },
        { DwgFileType.Binding, new[] { "绑定" } }
    };

    /// <summary>
    /// 文件类型优先级（用于排序）
    /// </summary>
    public Dictionary<DwgFileType, int> FileTypePriority { get; set; } = new()
    {
        { DwgFileType.All, 0 },
        { DwgFileType.Drawing, 1 },
        { DwgFileType.Construction, 2 },
        { DwgFileType.Template, 3 },
        { DwgFileType.Base, 4 },
        { DwgFileType.Change, 5 },
        { DwgFileType.Calculation, 6 },
        { DwgFileType.Frame, 7 },
        { DwgFileType.Architecture, 8 },
        { DwgFileType.Binding, 9 },
        { DwgFileType.Other, 10 }
    };

    /// <summary>
    /// 拖拽策略配置
    /// </summary>
    public DragDropStrategy DragStrategy { get; set; } = DragDropStrategy.Move;

    /// <summary>
    /// 是否启用拖拽功能
    /// </summary>
    public bool EnableDragDrop { get; set; } = true;

    /// <summary>
    /// 是否显示拖拽预览
    /// </summary>
    public bool ShowDragPreview { get; set; } = true;

    /// <summary>
    /// 是否启用文件重命名
    /// </summary>
    public bool EnableFileRename { get; set; } = true;

    /// <summary>
    /// 是否启用文件移动
    /// </summary>
    public bool EnableFileMove { get; set; } = true;

    /// <summary>
    /// 默认DWG文件目录
    /// </summary>
    public string DefaultDwgDirectory { get; set; } = "Assets/DWG";

    /// <summary>
    /// 支持的文件扩展名
    /// </summary>
    public string[] SupportedExtensions { get; set; } = { ".dwg", ".dxf" };

    /// <summary>
    /// 获取专业图标
    /// </summary>
    /// <param name="professionName">专业名称</param>
    /// <returns>图标字符串</returns>
    public string GetProfessionIcon(string professionName)
    {
        return ProfessionIcons.TryGetValue(professionName, out var icon) ? icon : ProfessionIcons["其他"];
    }

    /// <summary>
    /// 获取文件类型图标
    /// </summary>
    /// <param name="fileType">文件类型</param>
    /// <returns>图标字符串</returns>
    public string GetFileTypeIcon(DwgFileType fileType)
    {
        return FileTypeIcons.TryGetValue(fileType, out var icon) ? icon : FileTypeIcons[DwgFileType.Other];
    }

    /// <summary>
    /// 获取文件类型前缀
    /// </summary>
    /// <param name="fileType">文件类型</param>
    /// <returns>前缀数组</returns>
    public string[] GetFileTypePrefixes(DwgFileType fileType)
    {
        return FileTypePrefixes.TryGetValue(fileType, out var prefixes) ? prefixes : Array.Empty<string>();
    }

    /// <summary>
    /// 获取文件类型关键词
    /// </summary>
    /// <param name="fileType">文件类型</param>
    /// <returns>关键词数组</returns>
    public string[] GetFileTypeKeywords(DwgFileType fileType)
    {
        return FileTypeKeywords.TryGetValue(fileType, out var keywords) ? keywords : Array.Empty<string>();
    }

    /// <summary>
    /// 获取文件类型优先级
    /// </summary>
    /// <param name="fileType">文件类型</param>
    /// <returns>优先级数值</returns>
    public int GetFileTypePriority(DwgFileType fileType)
    {
        return FileTypePriority.TryGetValue(fileType, out var priority) ? priority : 99;
    }

    /// <summary>
    /// 创建默认配置
    /// </summary>
    /// <returns>默认配置实例</returns>
    public static DragDropConfiguration CreateDefault()
    {
        return new DragDropConfiguration();
    }

    /// <summary>
    /// 验证配置有效性
    /// </summary>
    /// <returns>配置是否有效</returns>
    public bool IsValid()
    {
        return ProfessionIcons.Count > 0 &&
               FileTypeIcons.Count > 0 &&
               FileTypePrefixes.Count > 0 &&
               !string.IsNullOrEmpty(DefaultDwgDirectory) &&
               SupportedExtensions.Length > 0;
    }
}

/// <summary>
/// 拖拽策略枚举
/// </summary>
public enum DragDropStrategy
{
    /// <summary>移动文件</summary>
    Move,
    /// <summary>复制文件</summary>
    Copy,
    /// <summary>仅重命名</summary>
    RenameOnly,
    /// <summary>禁用拖拽</summary>
    Disabled
}
