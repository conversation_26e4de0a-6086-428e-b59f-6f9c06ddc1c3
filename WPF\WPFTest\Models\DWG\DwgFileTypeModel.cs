using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using CommunityToolkit.Mvvm.ComponentModel;

namespace WPFTest.Models.DWG;

/// <summary>
/// DWG文件类型模型 - 智能文件类型识别和管理的核心数据模型
/// </summary>
/// <remarks>
/// 🎯 核心功能：
/// - 定义DWG文件类型的完整数据结构
/// - 支持基于前缀的智能文件类型识别
/// - 提供可视化的图标和中英文名称支持
/// - 实现SQLite数据库的持久化存储
///
/// 📊 数据字段说明：
/// - Id: 主键，自增整数，唯一标识文件类型
/// - EnglishName: 英文名称，系统内部标识，要求唯一
/// - ChineseName: 中文显示名称，用户界面显示
/// - Prefixes: 前缀字符串，逗号分隔，用于智能识别
/// - Icon: 显示图标，支持Emoji和Unicode字符
/// - SortOrder: 排序权重，控制显示顺序
/// - IsEnabled: 启用状态，控制是否参与识别
/// - IsDefault: 默认标志，标识系统预设类型
/// - CreatedAt/UpdatedAt: 时间戳，记录创建和更新时间
///
/// 🔍 智能识别机制：
/// - 前缀匹配：根据Prefixes字段中的前缀列表进行匹配
/// - 支持多前缀：一个文件类型可以有多个前缀（逗号分隔）
/// - 灵活格式：支持下划线和横线的前缀变体
/// - 优先级排序：按SortOrder权重排序匹配结果
///
/// 📋 默认文件类型示例：
/// ```
/// EnglishName: "ALL", ChineseName: "📁 全部", Prefixes: ""
/// EnglishName: "CONSTRUCTION", ChineseName: "🏗️ 施工图", Prefixes: "GS_,施工_"
/// EnglishName: "ARCHITECTURE", ChineseName: "🏢 建筑图", Prefixes: "JT_,建筑_"
/// EnglishName: "BASEMAP", ChineseName: "🗺️ 底图", Prefixes: "底图_,DT_"
/// ```
///
/// 🎨 设计模式：
/// - 数据模型：纯数据承载，无业务逻辑
/// - 观察者模式：继承ObservableValidator支持属性变更通知
/// - 验证模式：使用DataAnnotations进行数据验证
/// - ORM映射：通过Table特性映射到数据库表
///
/// 🛡️ 数据验证：
/// - Required: 必填字段验证
/// - MaxLength: 字符串长度限制
/// - 自动时间戳：PropertyChanged事件自动更新UpdatedAt
///
/// 🔧 技术特点：
/// - 使用CommunityToolkit.Mvvm的ObservableProperty
/// - 支持Entity Framework Core的CodeFirst模式
/// - 实现INotifyPropertyChanged接口
/// - 提供Clone()方法支持对象复制
/// - 静态工厂方法CreateDefaultTypes()创建默认数据
///
/// 💡 使用示例：
/// ```csharp
/// // 创建自定义文件类型
/// var customType = new DwgFileTypeModel
/// {
///     EnglishName = "CUSTOM",
///     ChineseName = "🔧 自定义类型",
///     Prefixes = "自定义_,CUSTOM_,ZDY_",
///     Icon = "🔧",
///     SortOrder = 100,
///     IsEnabled = true,
///     IsDefault = false
/// };
///
/// // 克隆对象用于编辑
/// var editCopy = customType.Clone();
/// ```
/// </remarks>
[Table("DwgFileTypes")]
public partial class DwgFileTypeModel : ObservableValidator
{
    #region 数据库字段

    /// <summary>
    /// 主键ID（自增）
    /// </summary>
    [Key] // 主键
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)] // 自增
    [ObservableProperty]
    [Description("主键ID（自增）")]
    public partial int Id { get; set; }

    /// <summary>
    /// 英文简化名称（唯一标识）
    /// </summary>
    [ObservableProperty]
    [Required]
    [MaxLength(50)]
    [Description("英文简化名称（唯一标识）")]
    public partial string EnglishName { get; set; } = string.Empty;

    /// <summary>
    /// 中文显示名称
    /// </summary>
    [ObservableProperty]
    [Required]
    [MaxLength(50)]
    [Description("中文显示名称")]
    public partial string ChineseName { get; set; } = string.Empty;

    /// <summary>
    /// 文件前缀（逗号分隔存储）
    /// </summary>
    [ObservableProperty]
    [MaxLength(200)]
    [Description("文件前缀（逗号分隔存储）")]
    public partial string Prefixes { get; set; } = string.Empty;

    /// <summary>
    /// 显示图标（Emoji）
    /// </summary>
    [ObservableProperty]
    [MaxLength(10)]
    [Description("显示图标（Emoji）")]
    public partial string Icon { get; set; } = string.Empty;

    /// <summary>
    /// 显示颜色（十六进制）
    /// </summary>
    [ObservableProperty]
    [MaxLength(10)]
    [Description("显示颜色（十六进制）")]
    public partial string Color { get; set; } = "#808080";

    /// <summary>
    /// 排序权重
    /// </summary>
    [ObservableProperty]
    [Description("排序权重")]
    public partial int SortOrder { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    [ObservableProperty]
    [Description("是否启用")]
    public partial bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 描述信息
    /// </summary>
    [ObservableProperty]
    [MaxLength(200)]
    [Description("描述信息")]
    public partial string Description { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    [ObservableProperty]
    [Description("创建时间")]
    public partial DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 更新时间
    /// </summary>
    [ObservableProperty]
    [Description("更新时间")]
    public partial DateTime UpdatedAt { get; set; } = DateTime.Now;

    #endregion

    #region 计算属性

    /// <summary>
    /// 显示文本（用于UI绑定）
    /// </summary>
    public string DisplayText => $"{Icon} {ChineseName}";

    /// <summary>
    /// 完整显示文本（包含英文名）
    /// </summary>
    public string FullDisplayText => $"{Icon} {ChineseName} ({EnglishName})";

    /// <summary>
    /// 前缀数组（从逗号分隔字符串解析）
    /// </summary>
    [NotMapped]
    public string[] PrefixArray
    {
        get
        {
            if (string.IsNullOrEmpty(Prefixes))
                return Array.Empty<string>();

            return Prefixes.Split(',', StringSplitOptions.RemoveEmptyEntries)
                          .Select(p => p.Trim())
                          .Where(p => !string.IsNullOrEmpty(p))
                          .ToArray();
        }
        set
        {
            Prefixes = string.Join(", ", value.Where(p => !string.IsNullOrEmpty(p)));
            OnPropertyChanged();
        }
    }

    /// <summary>
    /// 前缀字符串（用于UI绑定，逗号分隔）
    /// </summary>
    [NotMapped]
    public string PrefixesString
    {
        get => Prefixes;
        set
        {
            Prefixes = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(PrefixArray));
        }
    }

    #endregion

    #region 构造函数

    /// <summary>
    /// 默认构造函数
    /// </summary>
    public DwgFileTypeModel()
    {
        // 监听属性变化，自动更新UpdatedAt
        PropertyChanged += (s, e) =>
        {
            if (e.PropertyName != nameof(UpdatedAt))
            {
                UpdatedAt = DateTime.Now;
            }
        };
    }

    /// <summary>
    /// 完整构造函数
    /// </summary>
    /// <param name="englishName">英文名称</param>
    /// <param name="chineseName">中文名称</param>
    /// <param name="prefixes">前缀数组</param>
    /// <param name="icon">图标</param>
    /// <param name="color">颜色</param>
    /// <param name="description">描述</param>
    public DwgFileTypeModel(string englishName, string chineseName, string[] prefixes, 
                           string icon = "📄", string color = "#808080", string description = "")
        : this()
    {
        EnglishName = englishName;
        ChineseName = chineseName;
        PrefixArray = prefixes;
        Icon = icon;
        Color = color;
        Description = description;
    }

    #endregion

    #region 静态工厂方法

    /// <summary>
    /// 创建默认文件类型数据
    /// </summary>
    /// <returns>默认文件类型列表</returns>
    public static List<DwgFileTypeModel> CreateDefaultTypes()
    {
        var defaultTypes = new List<DwgFileTypeModel>
        {
            new("All", " 全部", Array.Empty<string>(), "📁", "#4CAF50", "显示所有文件类型"),
            new("Pictured", "出图", new[] { "S" }, "🔗", "#E91E63", "施工图文件") ,
            new("Base", "底图", new[] {"D", "DT"}, "🗺️", "#607D8B", "底图参考文件"),
            new("Template", "模板图", new[] {  "M","MB"}, "📋", "#9C27B0", "图纸模板文件") ,

            new("Change", "变更图", new[] { "Y",  "BG"}, "🔄", "#F44336", "变更图纸文件"),
            new("Pictured", "施工图", new[] { "GS" }, "🔄", "#F44336", "出图图纸文件"),
            // 其他类型（空前缀，用于匹配没有前缀的文件）
            new("Calculation", "计算书", new[] { "J" }, "📊", "#795548", "计算书文件") ,
    
            new("Architecture", "建筑图", new[] {"W",  "JS","JZ" }, "🏢", "#009688", "建筑专业图纸"),
            new("Binding", "绑定", new[] { "B"}, "🔗", "#E91E63", "绑定参考文件") ,
            new("Frame", "图框", new[] { "T" }, "🖼️", "#3F51B5", "图框模板文件") ,
            new("Other", "其他", Array.Empty<string>(), "📄", "#9E9E9E", "其他类型文件") 
        };
        
        for (int i = 0; i < defaultTypes.Count; i++)
        {
            defaultTypes[i].SortOrder = i;
        }

        return defaultTypes;
    }

    /// <summary>
    /// 根据文件名前缀查找文件类型
    /// </summary>
    /// <param name="fileName">文件名</param>
    /// <param name="fileTypes">文件类型列表</param>
    /// <returns>匹配的文件类型，未找到返回"其他"</returns>
    public static DwgFileTypeModel FindByFileName(string fileName, IEnumerable<DwgFileTypeModel> fileTypes)
    {
        if (string.IsNullOrEmpty(fileName)) // 文件名为空
            return fileTypes.FirstOrDefault(ft => ft.EnglishName == "Other") ?? new DwgFileTypeModel(); // 返回"其他"类型

        var upperFileName = fileName.ToUpper(); // 转换为大写进行匹配

        foreach (var fileType in fileTypes.Where(ft => ft.IsEnabled && ft.EnglishName != "All")) // 遍历启用的文件类型（排除"全部"）
        {
            foreach (var prefix in fileType.PrefixArray) // 遍历该类型的所有前缀
            {
                if (!string.IsNullOrEmpty(prefix) && upperFileName.StartsWith(prefix.ToUpper())) // 检查文件名是否以前缀开头
                {
                    return fileType; // 找到匹配的类型
                }
            }
        }

        return fileTypes.FirstOrDefault(ft => ft.EnglishName == "Other") ?? new DwgFileTypeModel(); // 未找到匹配，返回"其他"类型
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 添加前缀
    /// </summary>
    /// <param name="prefix">新前缀</param>
    public void AddPrefix(string prefix)
    {
        if (string.IsNullOrWhiteSpace(prefix)) return; // 无效前缀
        
        var prefixes = PrefixArray.ToList(); // 创建副本
        if (!prefixes.Contains(prefix, StringComparer.OrdinalIgnoreCase))
        {
            prefixes.Add(prefix);
            PrefixArray = prefixes.ToArray();
        }
    }

    /// <summary>
    /// 移除前缀
    /// </summary>
    /// <param name="prefix">要移除的前缀</param>
    public void RemovePrefix(string prefix)
    {
        if (string.IsNullOrWhiteSpace(prefix)) return;
        
        var prefixes = PrefixArray.ToList();
        prefixes.RemoveAll(p => string.Equals(p, prefix, StringComparison.OrdinalIgnoreCase));
        PrefixArray = prefixes.ToArray();
    }

    /// <summary>
    /// 检查是否匹配指定文件名
    /// </summary>
    /// <param name="fileName">文件名</param>
    /// <returns>是否匹配</returns>
    public bool MatchesFileName(string fileName)
    {
        if (string.IsNullOrEmpty(fileName) || EnglishName == "All") return true; // 空文件名或"全部"类型总是匹配
        if (EnglishName == "Other") return false; // "其他"类型不主动匹配

        var upperFileName = fileName.ToUpper(); // 转换为大写进行匹配
        return PrefixArray.Any(prefix => // 检查是否有任何前缀匹配
            !string.IsNullOrEmpty(prefix) && upperFileName.StartsWith(prefix.ToUpper())); // 文件名以前缀开头
    }

    /// <summary>
    /// 克隆当前对象
    /// </summary>
    /// <returns>克隆的对象</returns>
    public DwgFileTypeModel Clone()
    {
        return new DwgFileTypeModel(EnglishName, ChineseName, PrefixArray, Icon, Color, Description) // 使用构造函数创建新对象
        {
            Id = Id, // 保留原始ID（用于更新操作）
            SortOrder = SortOrder, // 复制排序权重
            IsEnabled = IsEnabled, // 复制启用状态
            CreatedAt = CreatedAt, // 复制创建时间
            UpdatedAt = UpdatedAt // 复制更新时间
        };
    }

    #endregion

    #region 重写方法

    /// <summary>
    /// 重写ToString方法，返回完整显示文本
    /// </summary>
    /// <returns>格式化的显示文本，包含图标、中文名和英文名</returns>
    public override string ToString() => FullDisplayText; // 返回完整显示文本用于调试和日志

    /// <summary>
    /// 重写Equals方法，基于英文名称进行比较
    /// </summary>
    /// <param name="obj">要比较的对象</param>
    /// <returns>如果英文名称相同则返回true</returns>
    public override bool Equals(object? obj)
    {
        return obj is DwgFileTypeModel other && EnglishName == other.EnglishName; // 使用英文名称作为唯一标识进行比较
    }

    /// <summary>
    /// 重写GetHashCode方法，基于英文名称生成哈希码
    /// </summary>
    /// <returns>英文名称的哈希码</returns>
    public override int GetHashCode() => EnglishName.GetHashCode(); // 使用英文名称的哈希码，确保与Equals方法一致

    #endregion
}
