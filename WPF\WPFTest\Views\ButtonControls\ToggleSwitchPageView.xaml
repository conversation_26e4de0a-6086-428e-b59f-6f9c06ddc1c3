<UserControl x:Class="WPFTest.Views.ButtonControls.ToggleSwitchPageView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:WPFTest.Views.ButtonControls"
             xmlns:buttonControls="clr-namespace:WPFTest.ViewModels.ButtonControls"
             xmlns:mvvm="http://prismlibrary.com/"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:codeExample="clr-namespace:Zylo.WPF.Controls.CodeExample;assembly=Zylo.WPF"
             mc:Ignorable="d"
             d:DataContext="{d:DesignInstance buttonControls:ToggleSwitchPageViewModel}"
             mvvm:ViewModelLocator.AutoWireViewModel="True"
             d:DesignHeight="1500" d:DesignWidth="1200">

    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 标题区域 -->
            <StackPanel Grid.Row="0" Margin="0,0,0,20">
                <TextBlock Text="🔄 ToggleSwitch 开关控件示例"
                           FontSize="32"
                           FontWeight="Bold"
                           Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                           Margin="0,0,0,8"/>
                <TextBlock Text="展示各种 ToggleSwitch 开关控件的样式和功能"
                           FontSize="14"
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                           Margin="0,0,0,16"/>

                <!-- 状态信息 -->
                <ui:Card Padding="16" Margin="0,0,0,16">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <TextBlock Text="📊 当前状态" FontWeight="Bold" FontSize="14" Margin="0,0,0,8"/>
                            <TextBlock Text="{Binding StatusMessage}"
                                       FontSize="12"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                            <TextBlock Text="{Binding ToggleCount, StringFormat='切换次数: {0}'}"
                                       FontSize="11"
                                       Foreground="{DynamicResource TextFillColorTertiaryBrush}"
                                       Margin="0,4,0,0"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <ui:Button Content="全部启用"
                                       Appearance="Success"
                                       Command="{Binding EnableAllCommand}"
                                       FontSize="11"
                                       Padding="8,4"
                                       Margin="0,0,8,0"/>
                            <ui:Button Content="全部禁用"
                                       Appearance="Caution"
                                       Command="{Binding DisableAllCommand}"
                                       FontSize="11"
                                       Padding="8,4"
                                       Margin="0,0,8,0"/>
                            <ui:Button Content="重置计数"
                                       Appearance="Transparent"
                                       Command="{Binding ResetCountCommand}"
                                       FontSize="11"
                                       Padding="8,4"/>
                        </StackPanel>
                    </Grid>
                </ui:Card>
            </StackPanel>

            <!-- 主要内容区域 -->
            <StackPanel Grid.Row="1">

                <!-- 基础 ToggleSwitch 示例 -->
                <ui:CardExpander Header="🔄 基础 ToggleSwitch" IsExpanded="True" Margin="0,0,0,24">
                    <StackPanel>
                        <TextBlock Text="基本的开关控件，支持开启/关闭状态切换" 
                                   FontSize="12" 
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- 示例区域 -->
                            <ui:Card Grid.Column="0" Padding="20" Margin="0,0,10,0">
                                <StackPanel>
                                    <TextBlock Text="基础开关示例：" FontWeight="Medium" Margin="0,0,0,16"/>

                                    <StackPanel Orientation="Horizontal">
                                        <StackPanel Margin="0,0,20,0">
                                            <TextBlock Text="默认开关" FontSize="12" Margin="0,0,0,8"/>
                                            <ui:ToggleSwitch Command="{Binding HandleToggleSwitchCommand}"
                                                           CommandParameter="基础开关"/>
                                        </StackPanel>

                                        <StackPanel Margin="0,0,20,0">
                                            <TextBlock Text="预设开启" FontSize="12" Margin="0,0,0,8"/>
                                            <ui:ToggleSwitch IsChecked="True"
                                                           Command="{Binding HandleToggleSwitchCommand}"
                                                           CommandParameter="预设开关"/>
                                        </StackPanel>

                                        <StackPanel>
                                            <TextBlock Text="禁用状态" FontSize="12" Margin="0,0,0,8"/>
                                            <ui:ToggleSwitch IsEnabled="False"
                                                           IsChecked="True"/>
                                        </StackPanel>
                                    </StackPanel>
                                </StackPanel>
                            </ui:Card>
                            
                    
                        </Grid>
                        <!-- 代码示例 -->
                        <codeExample:CodeExampleControl Grid.Column="1"
                                                        Title="基础 ToggleSwitch 代码示例"
                                                        Language="C#"
                                                        Description="展示如何使用基础的 ToggleSwitch 开关控件"
                                                        ShowTabs="True"
                                                        XamlCode="{Binding BasicToggleSwitchXamlExample}"
                                                        CSharpCode="{Binding BasicToggleSwitchCSharpExample}"/>
                    </StackPanel>
                </ui:CardExpander>

                <!-- 带标签的 ToggleSwitch -->
                <ui:CardExpander Header="🏷️ 带标签的 ToggleSwitch" IsExpanded="True" Margin="0,0,0,24">
                    <StackPanel>
                        <TextBlock Text="带有描述标签的开关控件，提供更好的用户体验"
                                   FontSize="12"
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   Margin="0,0,0,16"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- 示例区域 -->
                            <ui:Card Grid.Column="0" Padding="20" Margin="0,0,10,0">
                                <StackPanel>
                                    <TextBlock Text="带标签开关示例：" FontWeight="Medium" Margin="0,0,0,16"/>

                                    <StackPanel>
                                        <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                            <ui:ToggleSwitch IsChecked="{Binding IsNotificationEnabled}"
                                                           Command="{Binding HandleToggleSwitchCommand}"
                                                           CommandParameter="通知"
                                                           Margin="0,0,12,0"/>
                                            <TextBlock Text="启用通知" VerticalAlignment="Center"/>
                                        </StackPanel>

                                        <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                            <ui:ToggleSwitch IsChecked="{Binding IsDarkModeEnabled}"
                                                           Command="{Binding HandleToggleSwitchCommand}"
                                                           CommandParameter="深色模式"
                                                           Margin="0,0,12,0"/>
                                            <TextBlock Text="深色模式" VerticalAlignment="Center"/>
                                        </StackPanel>

                                        <StackPanel Orientation="Horizontal">
                                            <ui:ToggleSwitch IsChecked="{Binding IsAutoSaveEnabled}"
                                                           Command="{Binding HandleToggleSwitchCommand}"
                                                           CommandParameter="自动保存"
                                                           Margin="0,0,12,0"/>
                                            <TextBlock Text="自动保存" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </StackPanel>
                                </StackPanel>
                            </ui:Card>

               
                        </Grid>
                        <!-- 代码示例 -->
                        <codeExample:CodeExampleControl Grid.Column="1"
                                                        Title="带标签 ToggleSwitch 代码示例"
                                                        Language="C#"
                                                        Description="展示如何创建带有描述标签的开关控件"
                                                        ShowTabs="True"
                                                        XamlCode="{Binding LabeledToggleSwitchXamlExample}"
                                                        CSharpCode="{Binding LabeledToggleSwitchCSharpExample}"/>
                    </StackPanel>
                </ui:CardExpander>

                <!-- 自定义样式 ToggleSwitch -->
                <ui:CardExpander Header="🎨 自定义样式 ToggleSwitch" IsExpanded="True" Margin="0,0,0,24">
                    <StackPanel>
                        <TextBlock Text="展示不同尺寸和颜色的开关控件"
                                   FontSize="12"
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   Margin="0,0,0,16"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- 示例区域 -->
                            <ui:Card Grid.Column="0" Padding="20" Margin="0,0,10,0">
                                <StackPanel>
                                    <TextBlock Text="不同尺寸的开关：" FontWeight="Medium" Margin="0,0,0,16"/>

                                    <StackPanel Orientation="Horizontal">
                                        <StackPanel Margin="0,0,30,0">
                                            <TextBlock Text="小尺寸" FontSize="12" Margin="0,0,0,8"/>
                                            <ui:ToggleSwitch Width="40" Height="20"
                                                           Command="{Binding HandleToggleSwitchCommand}"
                                                           CommandParameter="小尺寸"/>
                                        </StackPanel>

                                        <StackPanel Margin="0,0,30,0">
                                            <TextBlock Text="标准尺寸" FontSize="12" Margin="0,0,0,8"/>
                                            <ui:ToggleSwitch Command="{Binding HandleToggleSwitchCommand}"
                                                           CommandParameter="标准尺寸"/>
                                        </StackPanel>

                                        <StackPanel>
                                            <TextBlock Text="大尺寸" FontSize="12" Margin="0,0,0,8"/>
                                            <ui:ToggleSwitch Width="60" Height="30"
                                                           Command="{Binding HandleToggleSwitchCommand}"
                                                           CommandParameter="大尺寸"/>
                                        </StackPanel>
                                    </StackPanel>
                                </StackPanel>
                            </ui:Card>

                    
                        </Grid>
                        <!-- 代码示例 -->
                        <codeExample:CodeExampleControl Grid.Column="1"
                                                        Title="自定义样式 ToggleSwitch 代码示例"
                                                        Language="C#"
                                                        Description="展示如何创建不同尺寸的自定义开关控件"
                                                        ShowTabs="True"
                                                        XamlCode="{Binding CustomToggleSwitchXamlExample}"
                                                        CSharpCode="{Binding CustomToggleSwitchCSharpExample}"/>
                    </StackPanel>
                </ui:CardExpander>

                <!-- 实际应用场景 -->
                <ui:CardExpander Header="💼 实际应用场景" IsExpanded="True">
                    <StackPanel>
                        <TextBlock Text="模拟真实应用中的开关控件使用场景"
                                   FontSize="12"
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   Margin="0,0,0,16"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- 示例区域 -->
                            <ui:Card Grid.Column="0" Padding="20" Margin="0,0,10,0">
                                <StackPanel>
                                    <TextBlock Text="应用设置面板：" FontWeight="Medium" Margin="0,0,0,16"/>

                                    <StackPanel Orientation="Horizontal">
                                        <ui:ToggleSwitch IsChecked="{Binding IsEmailNotificationEnabled}"
                                                       Command="{Binding HandleToggleSwitchCommand}"
                                                       CommandParameter="邮件通知"
                                                       Margin="0,0,12,0"/>
                                        <TextBlock Text="邮件通知" VerticalAlignment="Center" Margin="0,0,30,0"/>

                                        <ui:ToggleSwitch IsChecked="{Binding IsSoundNotificationEnabled}"
                                                       Command="{Binding HandleToggleSwitchCommand}"
                                                       CommandParameter="声音提醒"
                                                       Margin="0,0,12,0"/>
                                        <TextBlock Text="声音提醒" VerticalAlignment="Center" Margin="0,0,30,0"/>

                                        <ui:ToggleSwitch IsChecked="{Binding IsAutoUpdateEnabled}"
                                                       Command="{Binding HandleToggleSwitchCommand}"
                                                       CommandParameter="自动更新"
                                                       Margin="0,0,12,0"/>
                                        <TextBlock Text="自动更新" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </StackPanel>
                            </ui:Card>

                         
                        </Grid>
                        
                        <!-- 代码示例 -->
                        <codeExample:CodeExampleControl Grid.Column="1"
                                                        Title="实际应用场景 MVVM 示例"
                                                        Language="C#"
                                                        Description="展示在真实应用中如何使用 MVVM 模式管理开关状态"
                                                        ShowTabs="True"
                                                        XamlCode="{Binding ApplicationScenarioXamlExample}"
                                                        CSharpCode="{Binding ApplicationScenarioCSharpExample}"/>
                    </StackPanel>
                </ui:CardExpander>

            </StackPanel>

            <!-- 底部信息 -->
            <ui:Card Grid.Row="2" Padding="16" Margin="0,20,0,0">
                <StackPanel>
                    <TextBlock Text="💡 使用提示" FontWeight="Bold" FontSize="14" Margin="0,0,0,8"/>
                    <TextBlock TextWrapping="Wrap" FontSize="12" Foreground="{DynamicResource TextFillColorSecondaryBrush}">
                        • ToggleSwitch 适用于二元状态切换（开/关、是/否、启用/禁用）<LineBreak/>
                        • 相比 CheckBox，ToggleSwitch 更适合表示即时生效的设置<LineBreak/>
                        • 建议为开关添加清晰的标签说明其功能<LineBreak/>
                        • 可以通过 IsChecked 属性绑定到 ViewModel 中的布尔属性<LineBreak/>
                        • 使用 Toggled 事件处理状态变化
                    </TextBlock>
                </StackPanel>
            </ui:Card>
        </Grid>
    </ScrollViewer>
</UserControl>
