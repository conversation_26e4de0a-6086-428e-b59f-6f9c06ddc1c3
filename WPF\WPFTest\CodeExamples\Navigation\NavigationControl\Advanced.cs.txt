using System.Collections.ObjectModel;
using System.Diagnostics;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Prism.Regions;
using Zylo.WPF.Models.Navigation;
using System.Text.Json;
using System.IO;
using System.Linq;

namespace WPFTest.ViewModels.Navigation
{
    /// <summary>
    /// NavigationControl 高级功能示例 ViewModel
    /// </summary>
    public partial class NavigationAdvancedExampleViewModel : ObservableObject
    {
        private readonly IRegionManager _regionManager;

        /// <summary>
        /// 层级导航项集合
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<NavigationItemModel> hierarchicalNavigationItems = new();

        /// <summary>
        /// 当前选中的导航项
        /// </summary>
        [ObservableProperty]
        private NavigationItemModel? selectedNavigationItem;

        /// <summary>
        /// 搜索文本
        /// </summary>
        [ObservableProperty]
        private string searchText = string.Empty;

        /// <summary>
        /// 是否显示搜索框
        /// </summary>
        [ObservableProperty]
        private bool isSearchVisible = true;

        /// <summary>
        /// 点击时显示子菜单
        /// </summary>
        [ObservableProperty]
        private bool showSubMenuOnClick = true;

        /// <summary>
        /// 导航是否折叠
        /// </summary>
        [ObservableProperty]
        private bool isNavigationCollapsed = false;

        /// <summary>
        /// 启用拖拽排序
        /// </summary>
        [ObservableProperty]
        private bool enableDragDrop = false;

        /// <summary>
        /// 总项数
        /// </summary>
        [ObservableProperty]
        private int totalItemsCount;

        /// <summary>
        /// 已选中项数
        /// </summary>
        [ObservableProperty]
        private int selectedItemsCount;

        /// <summary>
        /// 筛选结果项数
        /// </summary>
        [ObservableProperty]
        private int filteredItemsCount;

        /// <summary>
        /// 导航历史记录
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<NavigationHistoryItem> navigationHistory = new();

        /// <summary>
        /// 构造函数
        /// </summary>
        public NavigationAdvancedExampleViewModel(IRegionManager regionManager)
        {
            _regionManager = regionManager;
            InitializeAdvancedNavigationItems();
            UpdateStatistics();
        }

        /// <summary>
        /// 导航命令
        /// </summary>
        [RelayCommand]
        private void Navigation(NavigationItemModel? item)
        {
            if (item == null) return;

            try
            {
                SelectedNavigationItem = item;

                // 添加到导航历史
                AddToNavigationHistory(item);

                // 执行导航
                if (!string.IsNullOrEmpty(item.NavigationParameter))
                {
                    _regionManager.RequestNavigate("ContentRegion", item.NavigationParameter);
                }

                UpdateStatistics();
                Debug.WriteLine($"高级导航到: {item.Title}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"高级导航失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 添加根项命令
        /// </summary>
        [RelayCommand]
        private void AddRootItem()
        {
            var newItem = new NavigationItemModel
            {
                Title = $"新项目 {HierarchicalNavigationItems.Count + 1}",
                Icon = "NewFolder",
                Description = "新添加的项目",
                Children = new ObservableCollection<NavigationItemModel>()
            };

            HierarchicalNavigationItems.Add(newItem);
            UpdateStatistics();
        }

        /// <summary>
        /// 添加子项命令
        /// </summary>
        [RelayCommand]
        private void AddChildItem(NavigationItemModel? parentItem)
        {
            if (parentItem?.Children == null) return;

            var newChild = new NavigationItemModel
            {
                Title = $"子项 {parentItem.Children.Count + 1}",
                Icon = "SubdirectoryArrowRight",
                Description = $"{parentItem.Title} 的子项",
                Parent = parentItem
            };

            parentItem.Children.Add(newChild);
            parentItem.IsExpanded = true;
            UpdateStatistics();
        }

        /// <summary>
        /// 编辑项命令
        /// </summary>
        [RelayCommand]
        private void EditItem(NavigationItemModel? item)
        {
            if (item == null) return;

            // 这里可以打开编辑对话框
            Debug.WriteLine($"编辑项: {item.Title}");
        }

        /// <summary>
        /// 删除项命令
        /// </summary>
        [RelayCommand]
        private void DeleteItem(NavigationItemModel? item)
        {
            if (item == null) return;

            if (item.Parent?.Children != null)
            {
                item.Parent.Children.Remove(item);
            }
            else
            {
                HierarchicalNavigationItems.Remove(item);
            }

            UpdateStatistics();
            Debug.WriteLine($"删除项: {item.Title}");
        }

        /// <summary>
        /// 复制项命令
        /// </summary>
        [RelayCommand]
        private void CopyItem(NavigationItemModel? item)
        {
            if (item == null) return;

            var copiedItem = new NavigationItemModel
            {
                Title = $"{item.Title} (副本)",
                Icon = item.Icon,
                Description = item.Description,
                NavigationParameter = item.NavigationParameter,
                Children = new ObservableCollection<NavigationItemModel>()
            };

            if (item.Parent?.Children != null)
            {
                item.Parent.Children.Add(copiedItem);
                copiedItem.Parent = item.Parent;
            }
            else
            {
                HierarchicalNavigationItems.Add(copiedItem);
            }

            UpdateStatistics();
            Debug.WriteLine($"复制项: {item.Title}");
        }

        /// <summary>
        /// 展开所有命令
        /// </summary>
        [RelayCommand]
        private void ExpandAll()
        {
            foreach (var item in HierarchicalNavigationItems)
            {
                ExpandItemRecursively(item);
            }
        }

        /// <summary>
        /// 折叠所有命令
        /// </summary>
        [RelayCommand]
        private void CollapseAll()
        {
            foreach (var item in HierarchicalNavigationItems)
            {
                CollapseItemRecursively(item);
            }
        }

        /// <summary>
        /// 导出数据命令
        /// </summary>
        [RelayCommand]
        private async Task ExportData()
        {
            try
            {
                var json = JsonSerializer.Serialize(HierarchicalNavigationItems, new JsonSerializerOptions 
                { 
                    WriteIndented = true 
                });
                
                var fileName = $"NavigationData_{DateTime.Now:yyyyMMdd_HHmmss}.json";
                await File.WriteAllTextAsync(fileName, json);
                
                Debug.WriteLine($"数据已导出到: {fileName}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"导出失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 导入数据命令
        /// </summary>
        [RelayCommand]
        private async Task ImportData()
        {
            try
            {
                // 这里应该打开文件选择对话框
                // 示例代码假设有一个固定的文件
                var fileName = "NavigationData.json";
                if (File.Exists(fileName))
                {
                    var json = await File.ReadAllTextAsync(fileName);
                    var importedItems = JsonSerializer.Deserialize<ObservableCollection<NavigationItemModel>>(json);
                    
                    if (importedItems != null)
                    {
                        HierarchicalNavigationItems = importedItems;
                        UpdateStatistics();
                        Debug.WriteLine("数据导入成功");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"导入失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 重置数据命令
        /// </summary>
        [RelayCommand]
        private void ResetData()
        {
            InitializeAdvancedNavigationItems();
            UpdateStatistics();
            Debug.WriteLine("数据已重置");
        }

        /// <summary>
        /// 初始化高级导航项
        /// </summary>
        private void InitializeAdvancedNavigationItems()
        {
            HierarchicalNavigationItems.Clear();

            // 创建复杂的层级结构
            var items = CreateAdvancedNavigationStructure();
            
            foreach (var item in items)
            {
                HierarchicalNavigationItems.Add(item);
            }
        }

        /// <summary>
        /// 创建高级导航结构
        /// </summary>
        private NavigationItemModel[] CreateAdvancedNavigationStructure()
        {
            return new[]
            {
                new NavigationItemModel
                {
                    Title = "项目管理",
                    Icon = "FolderOpen",
                    Description = "管理所有项目",
                    Children = new ObservableCollection<NavigationItemModel>
                    {
                        new NavigationItemModel { Title = "活跃项目", Icon = "PlayArrow", Description = "当前活跃的项目" },
                        new NavigationItemModel { Title = "已完成项目", Icon = "CheckCircle", Description = "已完成的项目" },
                        new NavigationItemModel { Title = "暂停项目", Icon = "Pause", Description = "暂停的项目" }
                    }
                },
                new NavigationItemModel
                {
                    Title = "团队协作",
                    Icon = "Group",
                    Description = "团队协作工具",
                    Children = new ObservableCollection<NavigationItemModel>
                    {
                        new NavigationItemModel { Title = "成员管理", Icon = "Person", Description = "管理团队成员" },
                        new NavigationItemModel { Title = "权限设置", Icon = "Security", Description = "设置访问权限" },
                        new NavigationItemModel { Title = "消息中心", Icon = "Message", Description = "团队消息" }
                    }
                },
                new NavigationItemModel
                {
                    Title = "数据分析",
                    Icon = "Analytics",
                    Description = "数据分析和报告",
                    Children = new ObservableCollection<NavigationItemModel>
                    {
                        new NavigationItemModel { Title = "实时监控", Icon = "Monitor", Description = "实时数据监控" },
                        new NavigationItemModel { Title = "历史报告", Icon = "History", Description = "历史数据报告" },
                        new NavigationItemModel { Title = "趋势分析", Icon = "TrendingUp", Description = "数据趋势分析" }
                    }
                }
            };
        }

        /// <summary>
        /// 递归展开项
        /// </summary>
        private void ExpandItemRecursively(NavigationItemModel item)
        {
            item.IsExpanded = true;
            if (item.Children != null)
            {
                foreach (var child in item.Children)
                {
                    ExpandItemRecursively(child);
                }
            }
        }

        /// <summary>
        /// 递归折叠项
        /// </summary>
        private void CollapseItemRecursively(NavigationItemModel item)
        {
            item.IsExpanded = false;
            if (item.Children != null)
            {
                foreach (var child in item.Children)
                {
                    CollapseItemRecursively(child);
                }
            }
        }

        /// <summary>
        /// 添加到导航历史
        /// </summary>
        private void AddToNavigationHistory(NavigationItemModel item)
        {
            var historyItem = new NavigationHistoryItem
            {
                Title = item.Title,
                Icon = item.Icon,
                NavigatedAt = DateTime.Now,
                NavigationParameter = item.NavigationParameter
            };

            NavigationHistory.Insert(0, historyItem);

            // 保持历史记录在合理数量内
            while (NavigationHistory.Count > 20)
            {
                NavigationHistory.RemoveAt(NavigationHistory.Count - 1);
            }
        }

        /// <summary>
        /// 更新统计信息
        /// </summary>
        private void UpdateStatistics()
        {
            TotalItemsCount = CountItemsRecursively(HierarchicalNavigationItems);
            SelectedItemsCount = SelectedNavigationItem != null ? 1 : 0;
            FilteredItemsCount = string.IsNullOrEmpty(SearchText) ? TotalItemsCount : 
                CountFilteredItems(HierarchicalNavigationItems, SearchText);
        }

        /// <summary>
        /// 递归计算项目数量
        /// </summary>
        private int CountItemsRecursively(ObservableCollection<NavigationItemModel> items)
        {
            int count = items.Count;
            foreach (var item in items)
            {
                if (item.Children != null)
                {
                    count += CountItemsRecursively(item.Children);
                }
            }
            return count;
        }

        /// <summary>
        /// 计算筛选后的项目数量
        /// </summary>
        private int CountFilteredItems(ObservableCollection<NavigationItemModel> items, string filter)
        {
            int count = 0;
            foreach (var item in items)
            {
                if (item.Title.Contains(filter, StringComparison.OrdinalIgnoreCase))
                {
                    count++;
                }
                if (item.Children != null)
                {
                    count += CountFilteredItems(item.Children, filter);
                }
            }
            return count;
        }
    }

    /// <summary>
    /// 导航历史项
    /// </summary>
    public class NavigationHistoryItem
    {
        public string Title { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public DateTime NavigatedAt { get; set; }
        public string? NavigationParameter { get; set; }
    }
}
