<!-- WPF-UI Flyout 样式和主题 XAML 示例 -->

<!-- 1. 不同位置的 Flyout -->
<StackPanel Orientation="Horizontal">
    <!-- 顶部 Flyout -->
    <ui:Button Content="顶部" Margin="4">
        <ui:Flyout Placement="Top">
            <StackPanel Margin="12">
                <TextBlock Text="顶部 Flyout" FontWeight="Medium"/>
                <TextBlock Text="显示在按钮上方" FontSize="12"/>
            </StackPanel>
        </ui:Flyout>
    </ui:Button>
    
    <!-- 底部 Flyout -->
    <ui:Button Content="底部" Margin="4">
        <ui:Flyout Placement="Bottom">
            <StackPanel Margin="12">
                <TextBlock Text="底部 Flyout" FontWeight="Medium"/>
                <TextBlock Text="显示在按钮下方" FontSize="12"/>
            </StackPanel>
        </ui:Flyout>
    </ui:Button>
    
    <!-- 左侧 Flyout -->
    <ui:Button Content="左侧" Margin="4">
        <ui:Flyout Placement="Left">
            <StackPanel Margin="12">
                <TextBlock Text="左侧 Flyout" FontWeight="Medium"/>
                <TextBlock Text="显示在按钮左侧" FontSize="12"/>
            </StackPanel>
        </ui:Flyout>
    </ui:Button>
    
    <!-- 右侧 Flyout -->
    <ui:Button Content="右侧" Margin="4">
        <ui:Flyout Placement="Right">
            <StackPanel Margin="12">
                <TextBlock Text="右侧 Flyout" FontWeight="Medium"/>
                <TextBlock Text="显示在按钮右侧" FontSize="12"/>
            </StackPanel>
        </ui:Flyout>
    </ui:Button>
</StackPanel>

<!-- 2. 自定义样式的 Flyout -->
<ui:Button Content="自定义样式">
    <ui:Flyout>
        <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                BorderBrush="{DynamicResource AccentFillColorDefaultBrush}"
                BorderThickness="2"
                CornerRadius="12"
                Padding="20">
            <StackPanel>
                <TextBlock Text="自定义样式 Flyout" 
                           FontWeight="Bold"
                           FontSize="16"
                           Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                           Margin="0,0,0,8"/>
                <TextBlock Text="这个 Flyout 使用了自定义的边框和颜色样式。" 
                           TextWrapping="Wrap"
                           Margin="0,0,0,12"/>
                <ui:Button Content="确定" 
                           Appearance="Primary"
                           HorizontalAlignment="Center"/>
            </StackPanel>
        </Border>
    </ui:Flyout>
</ui:Button>

<!-- 3. 带图标的 Flyout -->
<ui:Button Content="带图标">
    <ui:Flyout>
        <Grid Margin="16" MinWidth="200">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <ui:SymbolIcon Grid.Column="0"
                           Symbol="CheckmarkCircle24" 
                           FontSize="32" 
                           Foreground="{DynamicResource SystemFillColorSuccessBrush}"
                           Margin="0,0,12,0"
                           VerticalAlignment="Top"/>
            
            <StackPanel Grid.Column="1">
                <TextBlock Text="操作成功" 
                           FontWeight="Bold" 
                           FontSize="14"
                           Margin="0,0,0,4"/>
                <TextBlock Text="您的操作已成功完成。" 
                           TextWrapping="Wrap"
                           FontSize="12"
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
            </StackPanel>
        </Grid>
    </ui:Flyout>
</ui:Button>

<!-- 4. 渐变背景的 Flyout -->
<ui:Button Content="渐变背景">
    <ui:Flyout>
        <Border CornerRadius="8" Padding="16">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#FF6B73FF" Offset="0"/>
                    <GradientStop Color="#FF9B59B6" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <StackPanel>
                <TextBlock Text="渐变背景 Flyout" 
                           FontWeight="Bold"
                           Foreground="White"
                           Margin="0,0,0,8"/>
                <TextBlock Text="使用渐变背景创建更吸引人的视觉效果。" 
                           TextWrapping="Wrap"
                           Foreground="White"
                           Opacity="0.9"/>
            </StackPanel>
        </Border>
    </ui:Flyout>
</ui:Button>

<!-- 5. 卡片样式的 Flyout -->
<ui:Button Content="卡片样式">
    <ui:Flyout>
        <ui:Card Margin="0" Padding="16">
            <StackPanel>
                <Grid Margin="0,0,0,12">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <ui:SymbolIcon Grid.Column="0"
                                   Symbol="Mail24" 
                                   FontSize="20" 
                                   Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"/>
                    
                    <TextBlock Grid.Column="1"
                               Text="新消息" 
                               FontWeight="Bold"
                               VerticalAlignment="Center"/>
                    
                    <TextBlock Grid.Column="2"
                               Text="刚刚" 
                               FontSize="11"
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                               VerticalAlignment="Center"/>
                </Grid>
                
                <TextBlock Text="您有一条新的消息需要查看。" 
                           TextWrapping="Wrap"
                           Margin="0,0,0,12"/>
                
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                    <ui:Button Content="忽略" 
                               Appearance="Secondary"
                               Margin="0,0,8,0"/>
                    <ui:Button Content="查看" 
                               Appearance="Primary"/>
                </StackPanel>
            </StackPanel>
        </ui:Card>
    </ui:Flyout>
</ui:Button>

<!-- 6. 响应式 Flyout -->
<ui:Button Content="响应式">
    <ui:Flyout>
        <Grid Margin="16">
            <Grid.Style>
                <Style TargetType="Grid">
                    <Setter Property="MinWidth" Value="200"/>
                    <Style.Triggers>
                        <!-- 当窗口较小时调整最小宽度 -->
                        <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType=Window}, Path=ActualWidth, Converter={StaticResource LessThanConverter}, ConverterParameter=800}" Value="True">
                            <Setter Property="MinWidth" Value="150"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Grid.Style>
            
            <StackPanel>
                <TextBlock Text="响应式 Flyout" 
                           FontWeight="Bold"
                           Margin="0,0,0,8"/>
                <TextBlock Text="这个 Flyout 会根据窗口大小调整自己的尺寸。" 
                           TextWrapping="Wrap"/>
            </StackPanel>
        </Grid>
    </ui:Flyout>
</ui:Button>

<!-- 7. 动画效果的 Flyout -->
<ui:Button Content="动画效果">
    <ui:Flyout>
        <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                CornerRadius="8"
                Padding="16">
            <Border.Triggers>
                <EventTrigger RoutedEvent="Border.Loaded">
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation Storyboard.TargetProperty="Opacity"
                                           From="0" To="1" Duration="0:0:0.3"/>
                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                           From="0.8" To="1" Duration="0:0:0.3"/>
                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                           From="0.8" To="1" Duration="0:0:0.3"/>
                        </Storyboard>
                    </BeginStoryboard>
                </EventTrigger>
            </Border.Triggers>
            <Border.RenderTransform>
                <ScaleTransform/>
            </Border.RenderTransform>
            
            <StackPanel>
                <TextBlock Text="动画效果 Flyout" 
                           FontWeight="Bold"
                           Margin="0,0,0,8"/>
                <TextBlock Text="这个 Flyout 在显示时会有淡入和缩放动画效果。" 
                           TextWrapping="Wrap"/>
            </StackPanel>
        </Border>
    </ui:Flyout>
</ui:Button>

<!-- 8. 主题适配的 Flyout -->
<ui:Button Content="主题适配">
    <ui:Flyout>
        <Border Padding="16">
            <Border.Style>
                <Style TargetType="Border">
                    <!-- 浅色主题样式 -->
                    <Setter Property="Background" Value="{DynamicResource CardBackgroundFillColorDefaultBrush}"/>
                    <Setter Property="BorderBrush" Value="{DynamicResource CardStrokeColorDefaultBrush}"/>
                    <Setter Property="BorderThickness" Value="1"/>
                    <Setter Property="CornerRadius" Value="8"/>
                    
                    <!-- 深色主题时的样式调整 -->
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding Source={x:Static ui:ApplicationThemeManager.Current}, Path=ApplicationTheme}" Value="Dark">
                            <Setter Property="BorderBrush" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Border.Style>
            
            <StackPanel>
                <TextBlock Text="主题适配 Flyout" 
                           FontWeight="Bold"
                           Margin="0,0,0,8"/>
                <TextBlock Text="这个 Flyout 会根据当前主题（浅色/深色）自动调整样式。" 
                           TextWrapping="Wrap"/>
            </StackPanel>
        </Border>
    </ui:Flyout>
</ui:Button>
