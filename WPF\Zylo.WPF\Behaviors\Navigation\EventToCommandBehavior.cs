using System.Reflection;
using System.Windows.Input;
using Microsoft.Xaml.Behaviors;

namespace Zylo.WPF.Behaviors.Navigation;

/// <summary>
/// 事件到命令行为 - 将任何UI元素的事件转换为MVVM命令
/// 这是一个通用的行为，可以将任何事件绑定到ViewModel中的命令
/// </summary>
public class EventToCommandBehavior : Behavior<FrameworkElement>
{
    #region 依赖属性

    /// <summary>
    /// 事件名称 - 要监听的事件名称
    /// </summary>
    public static readonly DependencyProperty EventNameProperty =
        DependencyProperty.Register(
            nameof(EventName),
            typeof(string),
            typeof(EventToCommandBehavior),
            new PropertyMetadata(string.Empty, OnEventNameChanged));

    /// <summary>
    /// 命令 - 事件触发时要执行的命令
    /// </summary>
    public static readonly DependencyProperty CommandProperty =
        DependencyProperty.Register(
            nameof(Command),
            typeof(ICommand),
            typeof(EventToCommandBehavior),
            new PropertyMetadata(null));

    /// <summary>
    /// 命令参数 - 传递给命令的参数
    /// </summary>
    public static readonly DependencyProperty CommandParameterProperty =
        DependencyProperty.Register(
            nameof(CommandParameter),
            typeof(object),
            typeof(EventToCommandBehavior),
            new PropertyMetadata(null));

    /// <summary>
    /// 是否传递事件参数作为命令参数
    /// </summary>
    public static readonly DependencyProperty PassEventArgsAsParameterProperty =
        DependencyProperty.Register(
            nameof(PassEventArgsAsParameter),
            typeof(bool),
            typeof(EventToCommandBehavior),
            new PropertyMetadata(false));

    #endregion

    #region 私有字段

    private EventInfo? _eventInfo;
    private Delegate? _eventHandler;

    #endregion

    #region 公共属性

    /// <summary>
    /// 获取或设置事件名称
    /// </summary>
    public string EventName
    {
        get => (string)GetValue(EventNameProperty);
        set => SetValue(EventNameProperty, value);
    }

    /// <summary>
    /// 获取或设置命令
    /// </summary>
    public ICommand? Command
    {
        get => (ICommand?)GetValue(CommandProperty);
        set => SetValue(CommandProperty, value);
    }

    /// <summary>
    /// 获取或设置命令参数
    /// </summary>
    public object? CommandParameter
    {
        get => GetValue(CommandParameterProperty);
        set => SetValue(CommandParameterProperty, value);
    }

    /// <summary>
    /// 获取或设置是否传递事件参数作为命令参数
    /// </summary>
    public bool PassEventArgsAsParameter
    {
        get => (bool)GetValue(PassEventArgsAsParameterProperty);
        set => SetValue(PassEventArgsAsParameterProperty, value);
    }

    #endregion

    #region 行为生命周期

    /// <summary>
    /// 当行为附加到元素时调用
    /// </summary>
    protected override void OnAttached()
    {
        base.OnAttached();
        AttachEvent();
    }

    /// <summary>
    /// 当行为从元素分离时调用
    /// </summary>
    protected override void OnDetaching()
    {
        DetachEvent();
        base.OnDetaching();
    }

    #endregion

    #region 事件处理

    /// <summary>
    /// 当事件名称改变时调用
    /// </summary>
    private static void OnEventNameChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is EventToCommandBehavior behavior)
        {
            behavior.DetachEvent();
            behavior.AttachEvent();
        }
    }

    /// <summary>
    /// 附加事件处理程序
    /// </summary>
    private void AttachEvent()
    {
        if (AssociatedObject == null || string.IsNullOrEmpty(EventName))
            return;

        // 获取事件信息
        _eventInfo = AssociatedObject.GetType().GetEvent(EventName);
        if (_eventInfo == null)
        {
            throw new ArgumentException($"Event '{EventName}' not found on type '{AssociatedObject.GetType().Name}'");
        }

        // 创建事件处理程序
        var methodInfo = GetType().GetMethod(nameof(OnEventRaised), BindingFlags.NonPublic | BindingFlags.Instance);
        if (methodInfo != null)
        {
            _eventHandler = Delegate.CreateDelegate(_eventInfo.EventHandlerType!, this, methodInfo);
            _eventInfo.AddEventHandler(AssociatedObject, _eventHandler);
        }
    }

    /// <summary>
    /// 分离事件处理程序
    /// </summary>
    private void DetachEvent()
    {
        if (_eventInfo != null && _eventHandler != null && AssociatedObject != null)
        {
            _eventInfo.RemoveEventHandler(AssociatedObject, _eventHandler);
            _eventInfo = null;
            _eventHandler = null;
        }
    }

    /// <summary>
    /// 事件被触发时调用
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private void OnEventRaised(object sender, EventArgs e)
    {
        if (Command == null)
            return;

        // 确定要传递给命令的参数
        object? parameter = PassEventArgsAsParameter ? e : CommandParameter;

        // 检查命令是否可以执行
        if (Command.CanExecute(parameter))
        {
            Command.Execute(parameter);
        }
    }

    #endregion
}
