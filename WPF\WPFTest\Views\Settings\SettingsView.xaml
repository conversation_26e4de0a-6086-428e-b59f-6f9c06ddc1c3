<UserControl x:Class="WPFTest.Views.SettingsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:prism="http://prismlibrary.com/"
             xmlns:zylo="clr-namespace:Zylo.WPF.Controls.Navigation;assembly=Zylo.WPF"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             mc:Ignorable="d" 
             xmlns:ext="clr-namespace:Zylo.WPF.YPrism;assembly=Zylo.WPF"
             xmlns:viewModels="clr-namespace:WPFTest.ViewModels"
             
             d:DataContext="{d:DesignInstance viewModels:SettingsViewModel}"
             prism:ViewModelLocator.AutoWireViewModel="True"
             
             d:DesignHeight="600" d:DesignWidth="1000">
    
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="300"/>  <!-- 左侧设置导航 -->
            <ColumnDefinition Width="*"/>    <!-- 右侧设置内容 -->
        </Grid.ColumnDefinitions>
        
        <!-- 左侧设置导航 -->
        <Border Grid.Column="0" 
                Background="{DynamicResource ApplicationBackgroundBrush}"
                BorderBrush="{DynamicResource ControlElevationBorderBrush}"
                BorderThickness="0,0,1,0">
            
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>  <!-- 标题 -->
                    <RowDefinition Height="*"/>     <!-- 导航控件 -->
                </Grid.RowDefinitions>
                
                <!-- 设置页面标题 -->
                <Border Grid.Row="0"
                        Padding="20,16"
                        Background="{DynamicResource CardBackgroundBrush}">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- 标题文本 -->
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="设置"
                                       FontSize="24"
                                       FontWeight="SemiBold"
                                       Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
                            <TextBlock Text="配置应用程序选项"
                                       FontSize="12"
                                       Margin="0,4,0,0"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                        </StackPanel>

                        <!-- 显示/隐藏切换按钮 - 设置页面中隐藏 -->
                        <Button Grid.Column="1"
                                Width="32"
                                Height="32"
                                Background="Transparent"
                                BorderThickness="0"
                                ToolTip="显示/隐藏导航面板"
                                Command="{Binding ToggleNavigationCommand}"
                                VerticalAlignment="Center"
                                Visibility="Collapsed">
                            <ui:SymbolIcon Symbol="Navigation24"
                                           FontSize="16"
                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                        </Button>
                    </Grid>
                </Border>
                
                <!-- 设置导航控件 -->
                <zylo:NavigationControl Grid.Row="1"
                                       DefaultPosition="Right"
                                       RightColumnWidth="300"
                                       AllowCollapse="False"
                                       ShowToggleButton="False"
                                       TopNavigationItems="{Binding SettingsNavigationItems}"
                                       NavigationItemSelectedCommand="{Binding NavigateToSettingCommand}"
                                       Margin="0,8,0,0"/>
            </Grid>
        </Border>
        
        <!-- 右侧设置内容区域 -->
        <Grid Grid.Column="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>  <!-- 内容标题 -->
                <RowDefinition Height="*"/>     <!-- 内容区域 -->
            </Grid.RowDefinitions>
            
            <!-- 当前设置页面标题 -->
            <Border Grid.Row="0" 
                    Padding="24,16"
                    Background="{DynamicResource CardBackgroundBrush}"
                    BorderBrush="{DynamicResource ControlElevationBorderBrush}"
                    BorderThickness="0,0,0,1">
                <TextBlock Text="{Binding CurrentSettingTitle}" 
                           FontSize="20" 
                           FontWeight="Medium"
                           Foreground="{DynamicResource TextFillColorPrimaryBrush}"/>
            </Border>
            
            <!-- 设置内容区域 -->
            <ContentControl Grid.Row="1" 
                            prism:RegionManager.RegionName="{x:Static ext:PrismManager.SettingsRegionName}"
                           Margin="24,20,24,24"/>
        </Grid>
    </Grid>
</UserControl>
