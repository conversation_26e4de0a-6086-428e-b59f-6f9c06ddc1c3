using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using WPFTest.ViewModels.InputControls;

namespace WPFTest.Views.InputControls
{
    /// <summary>
    /// TimePickerPageView.xaml 的交互逻辑
    /// </summary>
    public partial class TimePickerPageView : UserControl
    {
        public TimePickerPageView()
        {
            InitializeComponent();
        }

        private void TestButton_Click(object sender, RoutedEventArgs e)
        {
            var debugInfo = "🔍 调试信息:\n";

            try
            {
                debugInfo += $"DataContext 类型: {DataContext?.GetType().Name ?? "null"}\n";

                // 测试数据绑定是否工作
                if (DataContext is TimePickerPageViewModel viewModel)
                {
                    debugInfo += "✅ DataContext 绑定正常\n";
                    debugInfo += $"当前时间: {viewModel.SelectedTime}\n";
                    debugInfo += $"交互次数: {viewModel.InteractionCount}\n";
                    debugInfo += $"状态消息: {viewModel.StatusMessage}\n";

                    // 检查命令是否存在
                    debugInfo += $"SetCurrentTimeCommand 是否为空: {viewModel.SetCurrentTimeCommand == null}\n";
                    debugInfo += $"ClearTimeCommand 是否为空: {viewModel.ClearTimeCommand == null}\n";
                    debugInfo += $"ResetCountCommand 是否为空: {viewModel.ResetCountCommand == null}\n";

                    if (viewModel.SetCurrentTimeCommand != null)
                    {
                        debugInfo += $"SetCurrentTimeCommand.CanExecute: {viewModel.SetCurrentTimeCommand.CanExecute(null)}\n";

                        // 手动调用命令
                        try
                        {
                            viewModel.SetCurrentTimeCommand.Execute(null);
                            debugInfo += "✅ 命令执行成功！\n";
                        }
                        catch (Exception ex)
                        {
                            debugInfo += $"❌ 命令执行失败: {ex.Message}\n";
                        }
                    }
                    else
                    {
                        debugInfo += "❌ SetCurrentTimeCommand 为空！\n";
                    }
                }
                else
                {
                    debugInfo += "❌ DataContext 不是 TimePickerPageViewModel！\n";
                }
            }
            catch (Exception ex)
            {
                debugInfo += $"❌ 测试过程中发生异常: {ex.Message}\n";
            }

            MessageBox.Show(debugInfo, "调试信息", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 测试TimePicker点击响应
        /// </summary>
        private void TestTimePickerClick(object sender, RoutedEventArgs e)
        {
            try
            {
                var debugInfo = "🔍 TimePicker 点击测试:\n\n";

                // 直接访问命名的TimePicker
                if (TestTimePicker != null)
                {
                    debugInfo += "✅ 找到命名的 TestTimePicker\n";
                    debugInfo += $"  IsEnabled: {TestTimePicker.IsEnabled}\n";
                    debugInfo += $"  IsVisible: {TestTimePicker.IsVisible}\n";
                    debugInfo += $"  Visibility: {TestTimePicker.Visibility}\n";
                    debugInfo += $"  SelectedTime: {TestTimePicker.SelectedTime}\n";
                    debugInfo += $"  Width: {TestTimePicker.ActualWidth}\n";
                    debugInfo += $"  Height: {TestTimePicker.ActualHeight}\n";
                    debugInfo += $"  Background: {TestTimePicker.Background}\n";
                    debugInfo += $"  IsLoaded: {TestTimePicker.IsLoaded}\n";
                    debugInfo += $"  IsHitTestVisible: {TestTimePicker.IsHitTestVisible}\n\n";

                    // 尝试设置时间
                    var newTime = TimeSpan.FromHours(14) + TimeSpan.FromMinutes(30);
                    TestTimePicker.SelectedTime = newTime;
                    debugInfo += $"  设置时间为: {newTime}\n";
                    debugInfo += $"  设置后的值: {TestTimePicker.SelectedTime}\n\n";

                    // 尝试触发Focus
                    TestTimePicker.Focus();
                    debugInfo += $"  Focus后 IsFocused: {TestTimePicker.IsFocused}\n\n";
                }
                else
                {
                    debugInfo += "❌ 未找到命名的 TestTimePicker\n\n";
                }

                // 查找所有TimePicker控件
                var timePickers = FindVisualChildren<Wpf.Ui.Controls.TimePicker>(this).ToList();
                debugInfo += $"通过可视化树找到 {timePickers.Count} 个 TimePicker 控件\n\n";

                // 检查ViewModel绑定
                if (DataContext is TimePickerPageViewModel viewModel)
                {
                    debugInfo += "✅ ViewModel 绑定正常\n";
                    debugInfo += $"  ViewModel.SelectedTime: {viewModel.SelectedTime}\n";
                    debugInfo += $"  ViewModel.InteractionCount: {viewModel.InteractionCount}\n";
                    debugInfo += $"  ViewModel.StatusMessage: {viewModel.StatusMessage}\n\n";
                }
                else
                {
                    debugInfo += "❌ ViewModel 绑定异常\n\n";
                }

                MessageBox.Show(debugInfo, "TimePicker 测试结果", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"测试失败: {ex.Message}\n\n堆栈跟踪:\n{ex.StackTrace}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 从ComboBox设置时间
        /// </summary>
        private void SetTimeFromComboBoxes(object sender, RoutedEventArgs e)
        {
            try
            {
                if (HourComboBox.SelectedItem is ComboBoxItem hourItem &&
                    MinuteComboBox.SelectedItem is ComboBoxItem minuteItem)
                {
                    var hour = int.Parse(hourItem.Content.ToString() ?? "0");
                    var minute = int.Parse(minuteItem.Content.ToString() ?? "0");

                    var today = DateTime.Today;
                    var dateTime = new DateTime(today.Year, today.Month, today.Day, hour, minute, 0);

                    if (DataContext is TimePickerPageViewModel viewModel)
                    {
                        viewModel.SelectedTime = dateTime;
                        viewModel.InteractionCount++;
                        viewModel.LastAction = "通过ComboBox设置时间";
                        viewModel.StatusMessage = $"✅ 通过ComboBox设置时间: {dateTime:HH:mm}";

                        MessageBox.Show($"✅ 成功设置时间: {dateTime:HH:mm}", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置时间失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 从TextBox设置时间
        /// </summary>
        private void SetTimeFromTextBox(object sender, RoutedEventArgs e)
        {
            try
            {
                var timeText = TimeTextBox.Text;

                if (TimeSpan.TryParse(timeText, out var timeSpan))
                {
                    var today = DateTime.Today;
                    var dateTime = today.Add(timeSpan);

                    if (DataContext is TimePickerPageViewModel viewModel)
                    {
                        viewModel.SelectedTime = dateTime;
                        viewModel.InteractionCount++;
                        viewModel.LastAction = "通过TextBox设置时间";
                        viewModel.StatusMessage = $"✅ 通过TextBox设置时间: {dateTime:HH:mm}";

                        MessageBox.Show($"✅ 成功设置时间: {dateTime:HH:mm}", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                else
                {
                    MessageBox.Show($"❌ 无效的时间格式: {timeText}\n请使用 HH:mm 格式，例如: 14:30", "格式错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置时间失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 查找可视化树中的子控件
        /// </summary>
        private static IEnumerable<T> FindVisualChildren<T>(DependencyObject depObj) where T : DependencyObject
        {
            if (depObj != null)
            {
                for (int i = 0; i < VisualTreeHelper.GetChildrenCount(depObj); i++)
                {
                    DependencyObject child = VisualTreeHelper.GetChild(depObj, i);
                    if (child != null && child is T)
                    {
                        yield return (T)child;
                    }

                    foreach (T childOfChild in FindVisualChildren<T>(child))
                    {
                        yield return childOfChild;
                    }
                }
            }
        }
    }
}
