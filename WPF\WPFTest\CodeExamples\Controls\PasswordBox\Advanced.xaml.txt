<!-- PasswordBox 高级功能示例 -->
<!-- 注意：需要添加命名空间 xmlns:attachedProps="clr-namespace:Zylo.WPF.AttachedProperties;assembly=Zylo.WPF" -->
<StackPanel Margin="20" Spacing="15">

    <!-- 密码验证表单（完整实现） -->
    <GroupBox Header="密码验证表单（完整实现）" Padding="15">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="16"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左列：密码输入 -->
            <StackPanel Grid.Column="0">
                <TextBlock Text="新密码" FontWeight="Bold" Margin="0,0,0,4"/>
                <PasswordBox Style="{StaticResource PasswordBoxStyle}"
                             PlaceholderText="请输入新密码"
                             attachedProps:PasswordBoxProperties.EnablePasswordBinding="True"
                             attachedProps:PasswordBoxProperties.BoundPassword="{Binding BasicPassword, UpdateSourceTrigger=PropertyChanged}"
                             Margin="0,0,0,8"/>

                <TextBlock Text="确认密码" FontWeight="Bold" Margin="0,0,0,4"/>
                <PasswordBox Style="{StaticResource PasswordBoxStyle}"
                             PlaceholderText="请再次输入密码"
                             attachedProps:PasswordBoxProperties.EnablePasswordBinding="True"
                             attachedProps:PasswordBoxProperties.BoundPassword="{Binding ConfirmPassword, UpdateSourceTrigger=PropertyChanged}"
                             Margin="0,0,0,8"/>

                <!-- 密码匹配状态 -->
                <TextBlock Text="{Binding PasswordMatchMessage}"
                           Foreground="{Binding PasswordMatchColor}"
                           FontSize="12"
                           Margin="0,0,0,8"/>

                <ui:Button Content="验证密码"
                           Appearance="Primary"
                           HorizontalAlignment="Stretch"
                           Command="{Binding ValidatePasswordCommand}"/>

                <!-- 验证结果显示 -->
                <Border Background="{DynamicResource ControlFillColorTertiaryBrush}"
                        CornerRadius="4"
                        Padding="12,8"
                        Margin="0,8,0,0"
                        Visibility="{Binding ValidationMessage, Converter={StaticResource StringToVisibilityConverter}}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                            <TextBlock Text="验证结果" FontWeight="Medium" FontSize="12"/>
                            <ui:Button Content="👁️ 查看密码"
                                       FontSize="10"
                                       Padding="4,2"
                                       Margin="8,0,0,0"
                                       Appearance="Secondary"
                                       Command="{Binding ShowPasswordCommand}"/>
                        </StackPanel>

                        <TextBlock Text="{Binding ValidationMessage}"
                                   FontSize="11"
                                   Foreground="{Binding ValidationMessageColor}"
                                   TextWrapping="Wrap"/>

                        <!-- 密码查看区域 -->
                        <Border Background="{DynamicResource ControlFillColorDefaultBrush}"
                                CornerRadius="4"
                                Padding="8"
                                Margin="0,8,0,0"
                                Visibility="{Binding ShowPasswordDetails, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="新密码:" FontSize="10" Margin="0,0,8,2"/>
                                <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding BasicPassword}" FontSize="10" FontFamily="Consolas" Margin="0,0,0,2"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="确认密码:" FontSize="10" Margin="0,0,8,2"/>
                                <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding ConfirmPassword}" FontSize="10" FontFamily="Consolas" Margin="0,0,0,2"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="匹配状态:" FontSize="10" Margin="0,0,8,0"/>
                                <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding PasswordMatchMessage}" FontSize="10" Foreground="{Binding PasswordMatchColor}"/>
                            </Grid>
                        </Border>
                    </StackPanel>
                </Border>
            </StackPanel>
            
            <!-- 右列：密码要求和强度 -->
            <StackPanel Grid.Column="2">
                <TextBlock Text="密码要求" FontWeight="Bold" Margin="0,0,0,8"/>
                <StackPanel>
                    <TextBlock Text="✓ 至少8个字符" FontSize="12" Foreground="Green"/>
                    <TextBlock Text="✓ 包含大写字母" FontSize="12" Foreground="Green"/>
                    <TextBlock Text="✓ 包含小写字母" FontSize="12" Foreground="Green"/>
                    <TextBlock Text="✗ 包含数字" FontSize="12" Foreground="Red"/>
                    <TextBlock Text="✗ 包含特殊字符" FontSize="12" Foreground="Red"/>
                </StackPanel>
                
                <TextBlock Text="密码强度" FontWeight="Bold" Margin="0,16,0,4"/>
                <ProgressBar Value="60" Maximum="100" Height="8" Margin="0,0,0,4"/>
                <TextBlock Text="良好" FontSize="12" Foreground="Orange"/>
            </StackPanel>
        </Grid>
    </GroupBox>

    <!-- 密码生成器 -->
    <GroupBox Header="密码生成器" Padding="15">
        <StackPanel>
            <TextBlock Text="自动生成安全密码" FontWeight="Bold" Margin="0,0,0,8"/>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <PasswordBox Grid.Column="0"
                             Style="{StaticResource PasswordBoxStyle}"
                             ui:TextBoxHelper.PlaceholderText="生成的密码将显示在这里"
                             IsReadOnly="True"
                             Margin="0,0,8,0"/>
                
                <ui:Button Grid.Column="1"
                           Content="生成"
                           Appearance="Secondary"
                           Margin="0,0,4,0"/>
                
                <ui:Button Grid.Column="2"
                           Content="复制"
                           Appearance="Secondary"/>
            </Grid>
            
            <!-- 生成选项 -->
            <StackPanel Orientation="Horizontal" Margin="0,8,0,0">
                <TextBlock Text="长度:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                <Slider Value="12" Minimum="8" Maximum="32" Width="100" VerticalAlignment="Center"/>
                <TextBlock Text="12" VerticalAlignment="Center" Margin="8,0,16,0"/>
                
                <CheckBox Content="包含符号" IsChecked="True" Margin="0,0,8,0"/>
                <CheckBox Content="包含数字" IsChecked="True" Margin="0,0,8,0"/>
                <CheckBox Content="避免相似字符" IsChecked="False"/>
            </StackPanel>
        </StackPanel>
    </GroupBox>

    <!-- 密码历史记录 -->
    <GroupBox Header="密码历史记录" Padding="15">
        <StackPanel>
            <TextBlock Text="最近使用的密码（已加密显示）" FontWeight="Bold" Margin="0,0,0,8"/>
            
            <ListBox Height="120">
                <ListBoxItem>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Column="0" Text="●●●●●●●●●●●●" VerticalAlignment="Center"/>
                        <TextBlock Grid.Column="1" Text="2024-01-15" FontSize="10" Foreground="Gray" VerticalAlignment="Center" Margin="8,0"/>
                        <ui:Button Grid.Column="2" Content="删除" Appearance="Danger" FontSize="10" Padding="4,2"/>
                    </Grid>
                </ListBoxItem>
                
                <ListBoxItem>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Column="0" Text="●●●●●●●●●●" VerticalAlignment="Center"/>
                        <TextBlock Grid.Column="1" Text="2024-01-10" FontSize="10" Foreground="Gray" VerticalAlignment="Center" Margin="8,0"/>
                        <ui:Button Grid.Column="2" Content="删除" Appearance="Danger" FontSize="10" Padding="4,2"/>
                    </Grid>
                </ListBoxItem>
                
                <ListBoxItem>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Column="0" Text="●●●●●●●●●●●●●●" VerticalAlignment="Center"/>
                        <TextBlock Grid.Column="1" Text="2024-01-05" FontSize="10" Foreground="Gray" VerticalAlignment="Center" Margin="8,0"/>
                        <ui:Button Grid.Column="2" Content="删除" Appearance="Danger" FontSize="10" Padding="4,2"/>
                    </Grid>
                </ListBoxItem>
            </ListBox>
            
            <ui:Button Content="清除所有历史记录"
                       Appearance="Danger"
                       HorizontalAlignment="Left"
                       Margin="0,8,0,0"/>
        </StackPanel>
    </GroupBox>

    <!-- 双因素认证 -->
    <GroupBox Header="双因素认证" Padding="15">
        <StackPanel>
            <TextBlock Text="输入密码和验证码" FontWeight="Bold" Margin="0,0,0,8"/>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="16"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <PasswordBox Grid.Column="0"
                             Style="{StaticResource PasswordBoxStyle}"
                             ui:TextBoxHelper.PlaceholderText="请输入密码"/>
                
                <ui:TextBox Grid.Column="2"
                            PlaceholderText="验证码"
                            MaxLength="6"
                            HorizontalContentAlignment="Center"/>
                
                <ui:Button Grid.Column="3"
                           Content="验证"
                           Appearance="Primary"
                           Margin="8,0,0,0"/>
            </Grid>
            
            <TextBlock Text="验证码已发送到您的手机 +86 138****1234"
                       FontSize="11"
                       Foreground="Gray"
                       Margin="0,4,0,0"/>
        </StackPanel>
    </GroupBox>

    <!-- 密码核对演示 -->
    <GroupBox Header="密码核对演示（哈希验证）" Padding="15">
        <StackPanel>
            <TextBlock Text="演示密码哈希存储和验证过程：" FontWeight="Bold" Margin="0,0,0,8"/>

            <TextBlock Text="模拟密码" FontWeight="Medium" Margin="0,0,0,4"/>
            <ui:TextBox Text="{Binding DemoPassword, UpdateSourceTrigger=PropertyChanged}"
                        PlaceholderText="输入要核对的密码"
                        Margin="0,0,0,8"/>

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <ui:Button Grid.Column="0" Content="核对密码"
                           Appearance="Secondary"
                           HorizontalAlignment="Stretch"
                           Command="{Binding VerifyDemoPasswordCommand}"
                           Margin="0,0,4,0"/>

                <ui:Button Grid.Column="1" Content="生成测试密码"
                           Appearance="Primary"
                           FontSize="10"
                           Padding="8,4"
                           Command="{Binding GenerateTestPasswordCommand}"/>
            </Grid>

            <!-- 核对结果 -->
            <Border Background="{DynamicResource ControlFillColorTertiaryBrush}"
                    CornerRadius="4"
                    Padding="8"
                    Margin="0,8,0,0"
                    Visibility="{Binding DemoVerificationResult, Converter={StaticResource StringToVisibilityConverter}}">
                <TextBlock Text="{Binding DemoVerificationResult}"
                           FontSize="11"
                           Foreground="{Binding DemoVerificationColor}"
                           TextWrapping="Wrap"/>
            </Border>
        </StackPanel>
    </GroupBox>

</StackPanel>
