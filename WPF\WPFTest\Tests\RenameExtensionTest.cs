using System;

namespace WPFTest.Tests;

/// <summary>
/// 重命名扩展名处理测试
/// </summary>
public static class RenameExtensionTest
{
    /// <summary>
    /// 测试扩展名处理逻辑
    /// </summary>
    public static void TestExtensionHandling()
    {
        Console.WriteLine("🧪 测试重命名扩展名处理逻辑...");
        Console.WriteLine(new string('=', 80));

        var testCases = new[]
        {
            // 格式: (用户输入, 原始扩展名, 期望结果)
            ("新文件名", ".dwg", "新文件名.dwg"),
            ("新文件名.dwg", ".dwg", "新文件名.dwg"),
            ("新文件名.DWG", ".dwg", "新文件名.dwg"),
            ("新文件名.txt", ".dwg", "新文件名.txt.dwg"),
            ("新文件名.dwg.dwg", ".dwg", "新文件名.dwg"),
            ("包含.点号.的文件名", ".dwg", "包含.点号.的文件名.dwg"),
            ("包含.点号.的文件名.dwg", ".dwg", "包含.点号.的文件名.dwg"),
            ("", ".dwg", ".dwg"),
            ("   ", ".dwg", "   .dwg")
        };

        foreach (var (userInput, originalExt, expected) in testCases)
        {
            var result = ProcessFileName(userInput, originalExt);
            var status = result == expected ? "✅" : "❌";
            
            Console.WriteLine($"{status} 输入: '{userInput}' + '{originalExt}' → '{result}'");
            if (result != expected)
            {
                Console.WriteLine($"   期望: '{expected}'");
            }
        }

        Console.WriteLine(new string('=', 80));
        Console.WriteLine("✅ 扩展名处理逻辑测试完成");
    }

    /// <summary>
    /// 模拟扩展名处理逻辑
    /// </summary>
    /// <param name="newName">用户输入的新文件名</param>
    /// <param name="originalExtension">原始扩展名</param>
    /// <returns>处理后的文件名</returns>
    private static string ProcessFileName(string newName, string originalExtension)
    {
        if (string.IsNullOrEmpty(originalExtension))
        {
            return newName;
        }

        // 如果用户输入的文件名已经包含该扩展名，先去除
        if (newName.EndsWith(originalExtension, StringComparison.OrdinalIgnoreCase))
        {
            newName = newName.Substring(0, newName.Length - originalExtension.Length);
        }
        
        // 然后统一添加正确的扩展名
        newName += originalExtension;
        
        return newName;
    }

    /// <summary>
    /// 测试边界情况
    /// </summary>
    public static void TestEdgeCases()
    {
        Console.WriteLine("\n🔍 测试边界情况...");
        Console.WriteLine(new string('=', 80));

        var edgeCases = new[]
        {
            // 特殊情况
            ("file.dwg.dwg.dwg", ".dwg", "file.dwg.dwg.dwg"),  // 多重扩展名
            ("file.DWG", ".dwg", "file.dwg"),                   // 大小写不同
            ("file.dwg.backup", ".dwg", "file.dwg.backup.dwg"), // 其他扩展名
            ("file.", ".dwg", "file..dwg"),                     // 末尾有点
            (".dwg", ".dwg", ".dwg"),                           // 只有扩展名
            ("file.dwg.txt", ".dwg", "file.dwg.txt.dwg")        // 复合扩展名
        };

        foreach (var (userInput, originalExt, expected) in edgeCases)
        {
            var result = ProcessFileName(userInput, originalExt);
            var status = result == expected ? "✅" : "❌";
            
            Console.WriteLine($"{status} 边界: '{userInput}' + '{originalExt}' → '{result}'");
            if (result != expected)
            {
                Console.WriteLine($"   期望: '{expected}'");
            }
        }

        Console.WriteLine(new string('=', 80));
        Console.WriteLine("✅ 边界情况测试完成");
    }

    /// <summary>
    /// 演示问题和解决方案
    /// </summary>
    public static void DemonstrateImprovement()
    {
        Console.WriteLine("\n🎯 演示改进效果...");
        Console.WriteLine(new string('=', 80));

        var problemCases = new[]
        {
            "新文件名",
            "新文件名.dwg",
            "新文件名.DWG",
            "新文件名.dwg.dwg"
        };

        Console.WriteLine("📝 用户可能的输入情况:");
        foreach (var input in problemCases)
        {
            var oldResult = OldLogic(input, ".dwg");
            var newResult = ProcessFileName(input, ".dwg");
            
            Console.WriteLine($"   输入: '{input}'");
            Console.WriteLine($"   旧逻辑: '{oldResult}' {(oldResult.EndsWith(".dwg.dwg") ? "❌" : "✅")}");
            Console.WriteLine($"   新逻辑: '{newResult}' ✅");
            Console.WriteLine();
        }

        Console.WriteLine("🎯 改进效果:");
        Console.WriteLine("   ✅ 避免了 .dwg.dwg 的重复扩展名");
        Console.WriteLine("   ✅ 处理了大小写不敏感的情况");
        Console.WriteLine("   ✅ 统一了扩展名格式");

        Console.WriteLine(new string('=', 80));
        Console.WriteLine("✅ 改进效果演示完成");
    }

    /// <summary>
    /// 模拟旧的逻辑（仅添加扩展名）
    /// </summary>
    private static string OldLogic(string newName, string originalExtension)
    {
        if (!newName.EndsWith(originalExtension, StringComparison.OrdinalIgnoreCase))
        {
            newName += originalExtension;
        }
        return newName;
    }

    /// <summary>
    /// 运行所有测试
    /// </summary>
    public static void RunAllTests()
    {
        Console.WriteLine("🚀 开始重命名扩展名处理测试...");
        
        TestExtensionHandling();
        TestEdgeCases();
        DemonstrateImprovement();
        
        Console.WriteLine("\n🏁 所有测试完成");
    }
}
