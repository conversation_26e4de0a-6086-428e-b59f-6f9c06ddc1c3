using System.Collections.ObjectModel;
using AlphaPM.Views.Settings;
using Wpf.Ui.Abstractions.Controls;
using Wpf.Ui.Controls;
using Zylo.WPF.Models.Navigation;
using INavigationAware = Prism.Regions.INavigationAware;


namespace AlphaPM.ViewModels.Settings
{
    /// <summary>
    /// 设置页面视图模型
    /// 管理设置导航和内容区域的显示
    /// </summary>
    public partial class SettingsViewModel : ViewModelBase,IConfigureService,INavigationAware
    {
        public readonly YLoggerInstance _logger = YLogger.ForSilent<SettingsViewModel>();
        #region 私有字段

        private readonly IRegionManager _regionManager;
        
        /// <summary>
        /// 导航日志对象 - 用于记录和管理区域导航的历史记录
        /// </summary>
        private IRegionNavigationJournal journal;

        private INavigationAware navigationAwareImplementation;

        #endregion

        #region 构造函数

        public SettingsViewModel(IRegionManager regionManager)
        {
            _regionManager = regionManager;
            InitializeSettingsNavigation();
            
            // 默认显示主题设置
            CurrentSettingTitle = "主题设置";

         
        }

        #endregion

        #region 属性

        /// <summary>
        /// 设置导航项集合
        /// </summary>
        [ObservableProperty]
        public partial ObservableCollection<ZyloNavigationItemModel> SettingsNavigationItems { get; set; } = new();

        /// <summary>
        /// 当前设置页面标题
        /// </summary>
        [ObservableProperty]
        public partial string CurrentSettingTitle { get; set; } = "设置";

        #endregion

        #region 命令

        /// <summary>
        /// 导航到设置页面命令
        /// </summary>
        [RelayCommand]
        private void NavigateToSetting(ZyloNavigationItemModel item)
        {
            if (item == null || string.IsNullOrEmpty(item.NavigationTarget))
            {
                _logger.Debug("导航项为空或没有导航目标");
                return;
            }

            try
            {
                // 更新当前页面标题
                CurrentSettingTitle = item.Name;
                
                // 导航到具体设置页面
                _regionManager.RequestNavigate(PrismManager.SettingsRegionName, item.NavigationTarget);
                
                _logger.Debug($"导航到设置页面: {item.Name} -> {item.NavigationTarget}");
            }
            catch (Exception ex)
            {
                _logger.Error($"导航到设置页面失败: {ex.Message}");
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化设置导航数据
        /// </summary>
        private void InitializeSettingsNavigation()
        {
            SettingsNavigationItems = new ObservableCollection<ZyloNavigationItemModel>
            {
                // 外观设置分组
                new ZyloNavigationItemModel
                {
                    Number = "1",
                    Name = "外观设置",
                    WpfUiSymbol= SymbolRegular.ColorBackground24,
                    IsExpanded = true,
                 
                    Children = new ObservableCollection<ZyloNavigationItemModel>
                    {
                        new ZyloNavigationItemModel
                        {
                            Number = "1.1",
                            ParentNumber = "1",
                            Name = "主题设置",
                            NavigationTarget = "ThemeSettingsView",
                            WpfUiSymbol = SymbolRegular.DarkTheme24,
                          
                            
                        },
                        new ZyloNavigationItemModel
                        {
                            Number = "1.2",
                            ParentNumber = "1",
                            Name = "颜色设置",
                            NavigationTarget = "ColorSettingsView",
                            WpfUiSymbol = SymbolRegular.Color24
                        },
                        new ZyloNavigationItemModel
                        {
                            Number = "1.3",
                            ParentNumber = "1",
                            Name = "字体设置",
                            NavigationTarget = "FontSettingsView",
                            WpfUiSymbol = SymbolRegular.TextFont24
                        }
                    }
                },
                
                // 系统设置分组
                new ZyloNavigationItemModel
                {
                    Number = "2",
                    Name = "系统设置",
                    WpfUiSymbol = SymbolRegular.Settings24,
                    IsExpanded = true,
                    Children = new ObservableCollection<ZyloNavigationItemModel>
                    {
                        new ZyloNavigationItemModel
                        {
                            Number = "2.1",
                            ParentNumber = "2",
                            Name = "语言设置",
                            NavigationTarget = "LanguageSettingsView",
                            WpfUiSymbol =  SymbolRegular.LocalLanguage24
                        },
                        new ZyloNavigationItemModel
                        {
                            Number = "2.2",
                            ParentNumber = "2",
                            Name = "启动设置",
                            NavigationTarget = "StartupSettingsView",
                            WpfUiSymbol = SymbolRegular.Rocket24
                        },
                        new ZyloNavigationItemModel
                        {
                            Number = "2.3",
                            ParentNumber = "2",
                            Name = "更新设置",
                            NavigationTarget = "UpdateSettingsView",
                            WpfUiSymbol =  SymbolRegular.ArrowSync24
                        }
                    }
                },
                
                // 开发设置分组
                new ZyloNavigationItemModel
                {
                    Number = "3",
                    Name = "开发设置",
                    IsExpanded = true,
                    WpfUiSymbol =  SymbolRegular.DeveloperBoard24,
                    Children = new ObservableCollection<ZyloNavigationItemModel>
                    {
                        new ZyloNavigationItemModel
                        {
                            Number = "3.1",
                            ParentNumber = "3",
                            Name = "调试选项",
                            NavigationTarget = "DebugSettingsView",
                            WpfUiSymbol = SymbolRegular.Bug24
                        },
                        new ZyloNavigationItemModel
                        {
                            Number = "3.2",
                            ParentNumber = "3",
                            Name = "日志设置",
                            NavigationTarget = "LogSettingsView",
                            WpfUiSymbol =  SymbolRegular.DocumentText24
                        }
                    }
                }
            };
        }

        
        private void NavigateToDefaultView(string regionName, string viewName)
        {
            _regionManager.Regions[regionName].RequestNavigate(viewName,
                navigationCallback =>
                {
                    // 📝 记录导航日志，支持前进后退功能
                    journal = navigationCallback.Context.NavigationService.Journal;
                    _logger.Debug($"🎯 导航到默认视图: {viewName}");
                });
        }
        #endregion

      
        /// <summary>
        /// 配置应用程序设置，导航到默认的主题设置视图
        /// </summary>
        public void Configure()
        {
           
        }

        /// <summary>
        /// 当导航到该视图模型时执行
        /// </summary>
        /// <param name="navigationContext">导航上下文</param>
        public void OnNavigatedTo(NavigationContext navigationContext)
        {
            
            NavigateToDefaultView(PrismManager.SettingsRegionName, nameof(ThemeSettingsView));
        }

        /// <summary>
        /// 确定是否可以导航到该视图模型
        /// </summary>
        /// <param name="navigationContext">导航上下文</param>
        /// <returns>如果可以导航则为true，否则为false</returns>
        public bool IsNavigationTarget(NavigationContext navigationContext)
        {
            return true;
        }

        /// <summary>
        /// 当从该视图模型导航离开时执行
        /// </summary>
        /// <param name="navigationContext">导航上下文</param>
        public void OnNavigatedFrom(NavigationContext navigationContext)
        {
            
        }

    }
}
