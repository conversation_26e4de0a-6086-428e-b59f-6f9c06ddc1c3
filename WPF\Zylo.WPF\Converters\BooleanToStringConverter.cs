using System.Globalization;
using System.Windows.Data;

namespace Zylo.WPF.Converters
{
    /// <summary>
    /// 布尔值到字符串转换器
    /// </summary>
    public class BooleanToStringConverter : IValueConverter
    {
        /// <summary>
        /// 将布尔值转换为字符串
        /// </summary>
        /// <param name="value">布尔值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">参数格式："TrueString|FalseString"</param>
        /// <param name="culture">文化信息</param>
        /// <returns>转换后的字符串</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue && parameter is string paramStr)
            {
                var parts = paramStr.Split('|');
                if (parts.Length == 2)
                {
                    return boolValue ? parts[0] : parts[1];
                }
            }

            // 默认返回
            return value is bool b ? (b ? "True" : "False") : "False";
        }

        /// <summary>
        /// 将字符串转换回布尔值
        /// </summary>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string str && parameter is string paramStr)
            {
                var parts = paramStr.Split('|');
                if (parts.Length == 2)
                {
                    return str == parts[0];
                }
            }

            return bool.TryParse(value?.ToString(), out bool result) && result;
        }
    }

    /// <summary>
    /// 反向布尔值转换器
    /// </summary>
    public class InverseBooleanConverter : IValueConverter
    {
        /// <summary>
        /// 将布尔值反转
        /// </summary>
        /// <param name="value">布尔值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">参数</param>
        /// <param name="culture">文化信息</param>
        /// <returns>反转后的布尔值</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return !boolValue;
            }
            return true; // 默认返回true
        }

        /// <summary>
        /// 将反转的布尔值转换回原值
        /// </summary>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return !boolValue;
            }
            return false; // 默认返回false
        }
    }
}
