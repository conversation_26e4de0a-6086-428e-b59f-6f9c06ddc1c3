<!-- AutoSuggestBox 样式展示示例 -->
<StackPanel Margin="20" Spacing="15">

    <!-- 基础样式 -->
    <GroupBox Header="基础样式" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <StackPanel Margin="8">
                <TextBlock Text="标准样式" FontWeight="Medium" Margin="0,0,0,4"/>
                <ui:AutoSuggestBox PlaceholderText="标准搜索框"
                                   Icon="{ui:SymbolIcon Search16}"
                                   Style="{StaticResource AutoSuggestBoxStyle}"/>
            </StackPanel>
            
            <StackPanel Margin="8">
                <TextBlock Text="小型样式" FontWeight="Medium" Margin="0,0,0,4"/>
                <ui:AutoSuggestBox PlaceholderText="小型搜索框"
                                   Icon="{ui:SymbolIcon Search16}"
                                   Style="{StaticResource SmallAutoSuggestBoxStyle}"/>
            </StackPanel>
            
            <StackPanel Margin="8">
                <TextBlock Text="大型样式" FontWeight="Medium" Margin="0,0,0,4"/>
                <ui:AutoSuggestBox PlaceholderText="大型搜索框"
                                   Icon="{ui:SymbolIcon Search16}"
                                   Style="{StaticResource LargeAutoSuggestBoxStyle}"/>
            </StackPanel>
        </WrapPanel>
    </GroupBox>

    <!-- 特殊样式 -->
    <GroupBox Header="特殊样式" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <StackPanel Margin="8">
                <TextBlock Text="透明样式" FontWeight="Medium" Margin="0,0,0,4"/>
                <ui:AutoSuggestBox PlaceholderText="透明背景"
                                   Icon="{ui:SymbolIcon Search16}"
                                   Style="{StaticResource TransparentAutoSuggestBoxStyle}"/>
            </StackPanel>
            
            <StackPanel Margin="8">
                <TextBlock Text="圆角样式" FontWeight="Medium" Margin="0,0,0,4"/>
                <ui:AutoSuggestBox PlaceholderText="圆角搜索框"
                                   Icon="{ui:SymbolIcon Search16}"
                                   Style="{StaticResource RoundedAutoSuggestBoxStyle}"/>
            </StackPanel>
            
            <StackPanel Margin="8">
                <TextBlock Text="无边框样式" FontWeight="Medium" Margin="0,0,0,4"/>
                <ui:AutoSuggestBox PlaceholderText="无边框"
                                   Icon="{ui:SymbolIcon Search16}"
                                   Style="{StaticResource BorderlessAutoSuggestBoxStyle}"/>
            </StackPanel>
        </WrapPanel>
    </GroupBox>

    <!-- 专用样式 -->
    <GroupBox Header="专用样式" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <StackPanel Margin="8">
                <TextBlock Text="搜索框样式" FontWeight="Medium" Margin="0,0,0,4"/>
                <ui:AutoSuggestBox PlaceholderText="专用搜索框"
                                   Style="{StaticResource SearchAutoSuggestBoxStyle}"/>
            </StackPanel>
            
            <StackPanel Margin="8">
                <TextBlock Text="紧凑样式" FontWeight="Medium" Margin="0,0,0,4"/>
                <ui:AutoSuggestBox PlaceholderText="紧凑搜索"
                                   Icon="{ui:SymbolIcon Search16}"
                                   Style="{StaticResource CompactAutoSuggestBoxStyle}"/>
            </StackPanel>
        </WrapPanel>
    </GroupBox>

    <!-- 不同图标样式 -->
    <GroupBox Header="不同图标样式" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <StackPanel Margin="8">
                <TextBlock Text="文档搜索" FontWeight="Medium" Margin="0,0,0,4"/>
                <ui:AutoSuggestBox PlaceholderText="搜索文档..."
                                   Icon="{ui:SymbolIcon Document16}"
                                   Style="{StaticResource AutoSuggestBoxStyle}"/>
            </StackPanel>
            
            <StackPanel Margin="8">
                <TextBlock Text="用户搜索" FontWeight="Medium" Margin="0,0,0,4"/>
                <ui:AutoSuggestBox PlaceholderText="搜索用户..."
                                   Icon="{ui:SymbolIcon Person16}"
                                   Style="{StaticResource AutoSuggestBoxStyle}"/>
            </StackPanel>
            
            <StackPanel Margin="8">
                <TextBlock Text="邮件搜索" FontWeight="Medium" Margin="0,0,0,4"/>
                <ui:AutoSuggestBox PlaceholderText="搜索邮件..."
                                   Icon="{ui:SymbolIcon Mail16}"
                                   Style="{StaticResource AutoSuggestBoxStyle}"/>
            </StackPanel>
            
            <StackPanel Margin="8">
                <TextBlock Text="设置搜索" FontWeight="Medium" Margin="0,0,0,4"/>
                <ui:AutoSuggestBox PlaceholderText="搜索设置..."
                                   Icon="{ui:SymbolIcon Settings16}"
                                   Style="{StaticResource AutoSuggestBoxStyle}"/>
            </StackPanel>
        </WrapPanel>
    </GroupBox>

    <!-- 状态样式 -->
    <GroupBox Header="状态样式" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <StackPanel Margin="8">
                <TextBlock Text="正常状态" FontWeight="Medium" Margin="0,0,0,4"/>
                <ui:AutoSuggestBox PlaceholderText="正常状态"
                                   Icon="{ui:SymbolIcon Search16}"
                                   Style="{StaticResource AutoSuggestBoxStyle}"/>
            </StackPanel>
            
            <StackPanel Margin="8">
                <TextBlock Text="禁用状态" FontWeight="Medium" Margin="0,0,0,4"/>
                <ui:AutoSuggestBox PlaceholderText="禁用状态"
                                   Icon="{ui:SymbolIcon Search16}"
                                   Style="{StaticResource AutoSuggestBoxStyle}"
                                   IsEnabled="False"/>
            </StackPanel>
            
            <StackPanel Margin="8">
                <TextBlock Text="只读状态" FontWeight="Medium" Margin="0,0,0,4"/>
                <ui:AutoSuggestBox Text="只读内容"
                                   Icon="{ui:SymbolIcon Lock16}"
                                   Style="{StaticResource AutoSuggestBoxStyle}"
                                   IsReadOnly="True"/>
            </StackPanel>
        </WrapPanel>
    </GroupBox>

    <!-- 自定义颜色样式 -->
    <GroupBox Header="自定义颜色样式" Padding="15">
        <WrapPanel Orientation="Horizontal">
            <StackPanel Margin="8">
                <TextBlock Text="成功样式" FontWeight="Medium" Margin="0,0,0,4"/>
                <ui:AutoSuggestBox PlaceholderText="成功状态"
                                   Icon="{ui:SymbolIcon Checkmark16}"
                                   Style="{StaticResource AutoSuggestBoxStyle}"
                                   BorderBrush="Green"/>
            </StackPanel>
            
            <StackPanel Margin="8">
                <TextBlock Text="警告样式" FontWeight="Medium" Margin="0,0,0,4"/>
                <ui:AutoSuggestBox PlaceholderText="警告状态"
                                   Icon="{ui:SymbolIcon Warning16}"
                                   Style="{StaticResource AutoSuggestBoxStyle}"
                                   BorderBrush="Orange"/>
            </StackPanel>
            
            <StackPanel Margin="8">
                <TextBlock Text="错误样式" FontWeight="Medium" Margin="0,0,0,4"/>
                <ui:AutoSuggestBox PlaceholderText="错误状态"
                                   Icon="{ui:SymbolIcon ErrorCircle16}"
                                   Style="{StaticResource AutoSuggestBoxStyle}"
                                   BorderBrush="Red"/>
            </StackPanel>
        </WrapPanel>
    </GroupBox>

</StackPanel>
