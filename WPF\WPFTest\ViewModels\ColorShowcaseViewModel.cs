using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media;
using WPFTest.Models;
using WPFTest.Services;
using Wpf.Ui.Appearance;
using Zylo.WPF;
using Zylo.WPF.Services;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels
{
    /// <summary>
    /// 🎨 颜色展示页面 ViewModel - 基于完整的 WPF-UI 颜色系统
    /// </summary>
    /// <remarks>
    /// <para>🎯 <strong>主要功能</strong></para>
    /// <list type="bullet">
    ///   <item><description>🎨 展示所有 WPF-UI 主题颜色资源</description></item>
    ///   <item><description>🔄 支持深色/浅色主题实时切换</description></item>
    ///   <item><description>📋 提供颜色资源名称和十六进制值</description></item>
    ///   <item><description>📱 点击复制、颜色预览、状态反馈</description></item>
    /// </list>
    ///
    /// <para>🔧 <strong>技术特点</strong></para>
    /// <list type="bullet">
    ///   <item><description>🎯 基于 MVVM 模式</description></item>
    ///   <item><description>📊 数据绑定展示</description></item>
    ///   <item><description>🚀 命令模式交互</description></item>
    ///   <item><description>🎨 主题响应式设计</description></item>
    /// </list>
    /// </remarks>
    public partial class ColorShowcaseViewModel : ObservableObject
    {
        #region 🔧 私有字段

        private readonly YLoggerInstance _logger = YLogger.ForSimple<ColorShowcaseViewModel>();
        private readonly IThemeManagementService _themeService;

        #endregion

        #region 📊 公共属性

        /// <summary>
        /// 🎨 所有颜色分组集合 - 按功能分类展示
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<ColorGroup> colorGroups = new();

        /// <summary>
        /// 🎯 选中的颜色信息
        /// </summary>
        [ObservableProperty]
        private ColorItem? selectedColor;

        /// <summary>
        /// 📝 状态消息
        /// </summary>
        [ObservableProperty]
        private string statusMessage = "准备就绪 - 点击任意颜色查看详情";

        /// <summary>
        /// 🌙 是否为深色主题
        /// </summary>
        [ObservableProperty]
        private bool isDarkTheme = false;

        /// <summary>
        /// 🔄 主题按钮文本
        /// </summary>
        [ObservableProperty]
        private string themeButtonText = "🌙 切换到深色";

        #endregion

        #region 🎯 构造函数

        /// <summary>
        /// 🏗️ 初始化 ColorShowcaseViewModel
        /// </summary>
        public ColorShowcaseViewModel()
        {
            _logger.Info("🎨 初始化颜色展示 ViewModel");

            // 🔧 获取主题管理服务
            _themeService = ZyloContainer.Resolve<IThemeManagementService>();

            // 📡 监听主题变化
            _themeService.ThemeConfigurationChanged += OnThemeChanged;

            // 🎨 初始化主题状态
            InitializeThemeState();

            // 🎯 加载颜色数据
            LoadColors();
        }

        #endregion

        #region 🔧 私有方法

        /// <summary>
        /// 🎨 初始化主题状态
        /// </summary>
        private void InitializeThemeState()
        {
            try
            {
                var currentSettings = _themeService.GetCurrentThemeSettings();
                IsDarkTheme = currentSettings?.Theme == ApplicationTheme.Dark;
                UpdateThemeButtonText();
                _logger.Info($"🎨 当前主题: {(IsDarkTheme ? "深色" : "浅色")}");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 初始化主题状态失败: {ex.Message}");
                IsDarkTheme = false;
                UpdateThemeButtonText();
            }
        }

        /// <summary>
        /// 🎨 加载完整的 WPF-UI 颜色系统 - 按分组展示
        /// </summary>
        private void LoadColors()
        {
            try
            {
                ColorGroups.Clear();

                // 🎯 系统强调色组
                var systemAccentGroup = new ColorGroup
                {
                    Name = "🎯 系统强调色",
                    Description = "系统级别的主要强调色，用于突出显示重要元素",
                    Colors = new ObservableCollection<ColorItem>
                    {
                        new("SystemAccentColorPrimaryBrush", "主要系统强调色", "用于最重要的交互元素", true),
                        new("SystemAccentColorSecondaryBrush", "次要系统强调色", "用于次要的交互元素", true),
                        new("SystemAccentColorTertiaryBrush", "第三级系统强调色", "用于辅助的交互元素", true),
                    }
                };
                ColorGroups.Add(systemAccentGroup);

                // ✨ 应用强调色组
                var accentGroup = new ColorGroup
                {
                    Name = "✨ 应用强调色",
                    Description = "应用级别的强调色，用于品牌和重点突出",
                    Colors = new ObservableCollection<ColorItem>
                    {
                        new("AccentFillColorDefaultBrush", "默认强调色", "主要的应用强调色", true),
                        new("AccentFillColorSecondaryBrush", "次要强调色", "次要的应用强调色", true),
                        new("AccentFillColorTertiaryBrush", "第三级强调色", "第三级的应用强调色", true),
                        new("AccentFillColorDisabledBrush", "禁用强调色", "禁用状态的强调色", true),
                        new("AccentTextFillColorPrimaryBrush", "主要强调文本色", "强调色背景上的主要文本", true),
                        new("AccentTextFillColorSecondaryBrush", "次要强调文本色", "强调色背景上的次要文本", true),
                        new("AccentTextFillColorTertiaryBrush", "第三级强调文本色", "强调色背景上的第三级文本", true),
                    }
                };
                ColorGroups.Add(accentGroup);

                // 📝 文本颜色组
                var textGroup = new ColorGroup
                {
                    Name = "📝 文本颜色",
                    Description = "用于各种文本显示的颜色",
                    Colors = new ObservableCollection<ColorItem>
                    {
                        new("TextFillColorPrimaryBrush", "主要文本色", "用于标题和重要文本"),
                        new("TextFillColorSecondaryBrush", "次要文本色", "用于副标题和说明文本"),
                        new("TextFillColorTertiaryBrush", "第三级文本色", "用于辅助信息文本"),
                        new("TextFillColorDisabledBrush", "禁用文本色", "用于禁用状态的文本"),
                        new("TextFillColorInverseBrush", "反色文本色", "用于深色背景上的文本", true),
                        new("TextPlaceholderColorBrush", "占位符文本色", "用于输入框占位符文本"),
                    }
                };
                ColorGroups.Add(textGroup);

                // 🏠 背景颜色组
                var backgroundGroup = new ColorGroup
                {
                    Name = "🏠 背景颜色",
                    Description = "用于各种背景的颜色",
                    Colors = new ObservableCollection<ColorItem>
                    {
                        new("ApplicationBackgroundBrush", "应用程序背景", "主要的应用程序背景色"),
                        new("LayerFillColorDefaultBrush", "默认图层填充", "标准的图层背景色"),
                        new("LayerFillColorAltBrush", "备用图层填充", "备用的图层背景色"),
                        new("SolidBackgroundFillColorBaseBrush", "基础实心背景", "基础的实心背景色"),
                        new("SolidBackgroundFillColorSecondaryBrush", "次要实心背景", "次要的实心背景色"),
                        new("SolidBackgroundFillColorTertiaryBrush", "第三级实心背景", "第三级的实心背景色"),
                        new("SolidBackgroundFillColorQuarternaryBrush", "第四级实心背景", "第四级的实心背景色"),
                        new("CardBackgroundFillColorDefaultBrush", "卡片背景", "卡片组件的背景色"),
                        new("CardBackgroundFillColorSecondaryBrush", "次要卡片背景", "次要卡片的背景色"),
                    }
                };
                ColorGroups.Add(backgroundGroup);

                // 🎛️ 控件颜色组
                var controlGroup = new ColorGroup
                {
                    Name = "🎛️ 控件颜色",
                    Description = "用于各种控件的颜色",
                    Colors = new ObservableCollection<ColorItem>
                    {
                        new("ControlFillColorDefaultBrush", "默认控件填充", "标准控件的填充色"),
                        new("ControlFillColorSecondaryBrush", "次要控件填充", "次要控件的填充色"),
                        new("ControlFillColorTertiaryBrush", "第三级控件填充", "第三级控件的填充色"),
                        new("ControlFillColorDisabledBrush", "禁用控件填充", "禁用状态控件的填充色"),
                        new("ControlFillColorTransparentBrush", "透明控件填充", "透明控件的填充色"),
                        new("ControlFillColorInputActiveBrush", "激活输入控件填充", "激活状态输入控件的填充色"),
                        new("ControlStrongFillColorDefaultBrush", "强调控件填充", "强调控件的填充色"),
                        new("ControlStrongFillColorDisabledBrush", "禁用强调控件填充", "禁用状态强调控件的填充色"),
                    }
                };
                ColorGroups.Add(controlGroup);

                // 🖼️ 描边颜色组
                var strokeGroup = new ColorGroup
                {
                    Name = "🖼️ 描边颜色",
                    Description = "用于边框和分割线的颜色",
                    Colors = new ObservableCollection<ColorItem>
                    {
                        new("ControlStrokeColorDefaultBrush", "默认控件描边", "标准控件的边框色"),
                        new("ControlStrokeColorSecondaryBrush", "次要控件描边", "次要控件的边框色"),
                        new("ControlStrokeColorOnAccentDefaultBrush", "强调色上的描边", "强调色背景上的边框色", true),
                        new("CardStrokeColorDefaultBrush", "卡片描边", "卡片组件的边框色"),
                        new("DividerStrokeColorDefaultBrush", "分割线颜色", "用于分割线的颜色"),
                        new("FocusStrokeColorOuterBrush", "外层焦点描边", "焦点状态的外层边框色"),
                        new("FocusStrokeColorInnerBrush", "内层焦点描边", "焦点状态的内层边框色"),
                    }
                };
                ColorGroups.Add(strokeGroup);

                // 🚦 系统状态颜色组
                var statusGroup = new ColorGroup
                {
                    Name = "🚦 系统状态色",
                    Description = "用于表示不同状态的系统颜色",
                    Colors = new ObservableCollection<ColorItem>
                    {
                        new("SystemFillColorSuccessBrush", "成功状态色", "表示成功操作的颜色"),
                        new("SystemFillColorCautionBrush", "警告状态色", "表示警告信息的颜色"),
                        new("SystemFillColorCriticalBrush", "错误状态色", "表示错误或危险的颜色"),
                        new("SystemFillColorNeutralBrush", "中性状态色", "表示中性信息的颜色"),
                        new("SystemFillColorSuccessBackgroundBrush", "成功背景色", "成功状态的背景色"),
                        new("SystemFillColorCautionBackgroundBrush", "警告背景色", "警告状态的背景色"),
                        new("SystemFillColorCriticalBackgroundBrush", "错误背景色", "错误状态的背景色"),
                        new("SystemFillColorNeutralBackgroundBrush", "中性背景色", "中性状态的背景色"),
                    }
                };
                ColorGroups.Add(statusGroup);

                // 🎯 控件强调色组 - ListView, ComboBox, NavigationView 等
                var controlAccentGroup = new ColorGroup
                {
                    Name = "🎯 控件强调色",
                    Description = "各种控件使用的强调色和选中指示器颜色",
                    Colors = new ObservableCollection<ColorItem>
                    {
                        // ListView 相关
                        new("ListViewItemPillFillBrush", "ListView选中指示器", "ListView项目的左侧选中指示器颜色", true),
                        new("ListViewItemBackgroundPointerOver", "ListView悬停背景", "ListView项目鼠标悬停时的背景色"),
                        new("ListViewItemForeground", "ListView文字颜色", "ListView项目的文字颜色"),

                        // ComboBox 相关
                        new("ComboBoxItemPillFillBrush", "ComboBox选中指示器", "ComboBox项目的选中指示器颜色", true),
                        new("ComboBoxItemBackgroundSelected", "ComboBox选中背景", "ComboBox项目选中时的背景色"),
                        new("ComboBoxBorderBrushFocused", "ComboBox焦点边框", "ComboBox获得焦点时的边框颜色", true),

                        // NavigationView 相关
                        new("NavigationViewSelectionIndicatorForeground", "导航选中指示器", "NavigationView的选中指示器颜色", true),
                        new("NavigationViewItemBackgroundPointerOver", "导航悬停背景", "NavigationView项目悬停时的背景色"),
                        new("NavigationViewItemBackgroundSelected", "导航选中背景", "NavigationView项目选中时的背景色"),

                        // TreeViewPageView 相关
                        new("TreeViewItemSelectionIndicatorForeground", "TreeView选中指示器", "TreeView项目的选中指示器颜色", true),
                        new("TreeViewItemBackgroundPointerOver", "TreeView悬停背景", "TreeView项目悬停时的背景色"),
                        new("TreeViewItemBackgroundSelected", "TreeView选中背景", "TreeView项目选中时的背景色"),
                    }
                };
                ColorGroups.Add(controlAccentGroup);

                // 🎛️ 输入控件强调色组 - CheckBox, RadioButton, ToggleSwitch 等
                var inputAccentGroup = new ColorGroup
                {
                    Name = "🎛️ 输入控件强调色",
                    Description = "输入控件的强调色和选中状态颜色",
                    Colors = new ObservableCollection<ColorItem>
                    {
                        // CheckBox 相关
                        new("CheckBoxCheckBackgroundFillChecked", "CheckBox选中背景", "CheckBox选中时的背景色", true),
                        new("CheckBoxCheckBackgroundFillCheckedPointerOver", "CheckBox选中悬停", "CheckBox选中状态悬停时的背景色", true),
                        new("CheckBoxCheckGlyphForeground", "CheckBox勾选符号", "CheckBox勾选符号的颜色"),

                        // RadioButton 相关
                        new("RadioButtonOuterEllipseCheckedStroke", "RadioButton选中边框", "RadioButton选中时的外圈边框颜色", true),
                        new("RadioButtonOuterEllipseCheckedStrokePointerOver", "RadioButton选中悬停边框", "RadioButton选中状态悬停时的边框颜色", true),
                        new("RadioButtonCheckGlyphFill", "RadioButton内圈填充", "RadioButton选中时内圈的填充颜色"),

                        // ToggleSwitch 相关
                        new("ToggleSwitchStrokeOn", "ToggleSwitch开启边框", "ToggleSwitch开启状态的边框颜色", true),
                        new("ToggleSwitchStrokeOnPointerOver", "ToggleSwitch开启悬停边框", "ToggleSwitch开启状态悬停时的边框颜色", true),
                        new("ToggleSwitchFillOn", "ToggleSwitch开启填充", "ToggleSwitch开启状态的填充颜色", true),
                        new("ToggleSwitchFillOnPointerOver", "ToggleSwitch开启悬停填充", "ToggleSwitch开启状态悬停时的填充颜色", true),
                        new("ToggleSwitchKnobFillOn", "ToggleSwitch开启滑块", "ToggleSwitch开启状态滑块的颜色"),
                        new("ToggleSwitchKnobFillOnPointerOver", "ToggleSwitch开启滑块悬停", "ToggleSwitch开启状态滑块悬停时的颜色"),

                        // TextBox 相关
                        new("TextControlFocusedBorderBrush", "TextBox焦点边框", "TextBox获得焦点时的边框颜色", true),
                    }
                };
                ColorGroups.Add(inputAccentGroup);

                // 🎨 按钮和交互控件强调色组
                var buttonAccentGroup = new ColorGroup
                {
                    Name = "🎨 按钮强调色",
                    Description = "按钮和交互控件的强调色",
                    Colors = new ObservableCollection<ColorItem>
                    {
                        // AccentButton 相关
                        new("AccentButtonBackground", "强调按钮背景", "强调按钮的背景色", true),
                        new("AccentButtonBackgroundPointerOver", "强调按钮悬停背景", "强调按钮悬停时的背景色", true),
                        new("AccentButtonBackgroundPressed", "强调按钮按下背景", "强调按钮按下时的背景色", true),
                        new("AccentButtonForeground", "强调按钮文字", "强调按钮的文字颜色"),
                        new("AccentButtonForegroundPointerOver", "强调按钮悬停文字", "强调按钮悬停时的文字颜色"),
                        new("AccentButtonForegroundPressed", "强调按钮按下文字", "强调按钮按下时的文字颜色"),

                        // ToggleButton 相关
                        new("ToggleButtonBackgroundChecked", "ToggleButton选中背景", "ToggleButton选中时的背景色", true),
                        new("ToggleButtonForegroundCheckedPointerOver", "ToggleButton选中悬停", "ToggleButton选中状态悬停时的颜色", true),
                        new("ToggleButtonBackgroundCheckedPressed", "ToggleButton选中按下", "ToggleButton选中状态按下时的背景色", true),
                        new("ToggleButtonForegroundChecked", "ToggleButton选中文字", "ToggleButton选中时的文字颜色"),
                        new("ToggleButtonForegroundCheckedPressed", "ToggleButton选中按下文字", "ToggleButton选中状态按下时的文字颜色"),

                        // HyperlinkButton 相关
                        new("HyperlinkButtonForeground", "超链接按钮文字", "超链接按钮的文字颜色", true),
                        new("HyperlinkButtonForegroundPointerOver", "超链接按钮悬停文字", "超链接按钮悬停时的文字颜色", true),
                        new("HyperlinkButtonForegroundPressed", "超链接按钮按下文字", "超链接按钮按下时的文字颜色", true),
                    }
                };
                ColorGroups.Add(buttonAccentGroup);

                // 📊 进度和评级控件强调色组
                var progressAccentGroup = new ColorGroup
                {
                    Name = "📊 进度和评级强调色",
                    Description = "进度条、滑块、评级等控件的强调色",
                    Colors = new ObservableCollection<ColorItem>
                    {
                        // ProgressBar 相关
                        new("ProgressBarForeground", "进度条前景", "进度条的前景色（进度部分）", true),
                        new("ProgressRingForegroundThemeBrush", "进度环前景", "进度环的前景色", true),

                        // Slider 相关
                        new("SliderThumbBackground", "滑块背景", "滑块（拖拽部分）的背景色", true),
                        new("SliderThumbBackgroundPointerOver", "滑块悬停背景", "滑块悬停时的背景色", true),

                        // RatingControl 相关
                        new("RatingControlSelectedForeground", "评级选中前景", "评级控件选中星星的颜色", true),

                        // ThumbRate 相关
                        new("ThumbRateForeground", "点赞评级前景", "点赞评级控件的前景色", true),
                    }
                };
                ColorGroups.Add(progressAccentGroup);

                // 📅 日期时间控件强调色组
                var dateTimeAccentGroup = new ColorGroup
                {
                    Name = "📅 日期时间强调色",
                    Description = "日历、时间选择器等控件的强调色",
                    Colors = new ObservableCollection<ColorItem>
                    {
                        // Calendar 相关
                        new("CalendarViewSelectedBackground", "日历选中背景", "日历选中日期的背景色", true),
                        new("CalendarViewSelectedBorderBrush", "日历选中边框", "日历选中日期的边框色", true),
                        new("CalendarViewTodayBackground", "日历今日背景", "日历今日日期的背景色", true),
                        new("CalendarViewTodayForeground", "日历今日文字", "日历今日日期的文字颜色"),
                    }
                };
                ColorGroups.Add(dateTimeAccentGroup);

                // 📋 列表和选择控件强调色组
                var listAccentGroup = new ColorGroup
                {
                    Name = "📋 列表选择强调色",
                    Description = "ListBox等列表选择控件的强调色",
                    Colors = new ObservableCollection<ColorItem>
                    {
                        // ListBox 相关
                        new("ListBoxItemSelectedBackgroundThemeBrush", "ListBox选中背景", "ListBox项目选中时的背景色", true),
                        new("ListBoxItemSelectedForegroundThemeBrush", "ListBox选中文字", "ListBox项目选中时的文字颜色"),
                        new("ListBoxItemForeground", "ListBox文字颜色", "ListBox项目的文字颜色"),
                    }
                };
                ColorGroups.Add(listAccentGroup);

                var totalColors = ColorGroups.Sum(g => g.Colors.Count);
                _logger.Info($"✅ 已加载 {ColorGroups.Count} 个分组，共 {totalColors} 个颜色");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 加载颜色失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 主题变化事件处理
        /// </summary>
        private void OnThemeChanged(object? sender, EventArgs e)
        {
            try
            {
                InitializeThemeState();
                // 通知所有颜色项刷新
                foreach (var group in ColorGroups)
                {
                    foreach (var color in group.Colors)
                    {
                        color.RefreshColor();
                    }
                }
                _logger.Info("🔄 主题变化，已刷新所有颜色");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 处理主题变化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新主题按钮文本
        /// </summary>
        private void UpdateThemeButtonText()
        {
            ThemeButtonText = IsDarkTheme ? "☀️ 切换到浅色" : "🌙 切换到深色";
        }

        #region 🚀 命令

        /// <summary>
        /// 🎨 颜色点击命令 - 选择并显示颜色信息
        /// </summary>
        [RelayCommand]
        private void ColorClick(ColorItem ColorItem)
        {
            try
            {
                SelectedColor = ColorItem;
                StatusMessage = $"✅ 已选择: {ColorItem.Name}";
                _logger.Info($"🎨 选中颜色: {ColorItem.Name}");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 颜色点击失败: {ex.Message}");
                StatusMessage = "❌ 选择颜色失败";
            }
        }

        /// <summary>
        /// 📋 复制颜色资源名称命令 - 复制 XAML 资源引用
        /// </summary>
        [RelayCommand]
        private void CopyResourceName(ColorItem? ColorItem)
        {
            if (ColorItem == null) return;

            try
            {
                var resourceText = $"{{DynamicResource {ColorItem.ResourceKey}}}";
                SetClipboardText(resourceText);
                StatusMessage = $"📋 已复制: {resourceText}";
                _logger.Info($"📋 已复制资源: {ColorItem.ResourceKey}");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 复制资源名称失败: {ex.Message}");
                StatusMessage = "❌ 复制失败";
            }
        }

        /// <summary>
        /// 🎨 复制颜色十六进制值命令
        /// </summary>
        [RelayCommand]
        private void CopyHexValue(ColorItem? ColorItem)
        {
            if (ColorItem == null || string.IsNullOrEmpty(ColorItem.HexValue)) return;

            try
            {
                SetClipboardText(ColorItem.HexValue);
                StatusMessage = $"🎨 已复制: {ColorItem.HexValue}";
                _logger.Info($"🎨 已复制十六进制值: {ColorItem.HexValue}");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 复制十六进制值失败: {ex.Message}");
                StatusMessage = "❌ 复制失败";
            }
        }

        /// <summary>
        /// 主题切换命令
        /// </summary>
        [RelayCommand]
        private async Task ToggleTheme()
        {
            try
            {
                IsDarkTheme = !IsDarkTheme;
                var themeMode = IsDarkTheme ? "Dark" : "Light";
                
                var success = await _themeService.UpdateThemeModeAsync(themeMode);
                
                if (success)
                {
                    UpdateThemeButtonText();
                    StatusMessage = $"🎨 已切换到{(IsDarkTheme ? "深色" : "浅色")}主题";
                    _logger.Info($"✅ 主题切换成功: {themeMode}");
                }
                else
                {
                    IsDarkTheme = !IsDarkTheme; // 恢复状态
                    StatusMessage = "❌ 主题切换失败";
                    _logger.Error("❌ 主题切换失败");
                }
            }
            catch (Exception ex)
            {
                IsDarkTheme = !IsDarkTheme; // 恢复状态
                _logger.Error($"❌ 主题切换失败: {ex.Message}");
                StatusMessage = "❌ 主题切换失败";
            }
        }

        /// <summary>
        /// 🔄 刷新所有颜色命令
        /// </summary>
        [RelayCommand]
        private void RefreshColors()
        {
            try
            {
                foreach (var group in ColorGroups)
                {
                    foreach (var color in group.Colors)
                    {
                        color.RefreshColor();
                    }
                }
                StatusMessage = "🔄 已刷新所有颜色";
                _logger.Info("🔄 手动刷新所有颜色");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 刷新颜色失败: {ex.Message}");
                StatusMessage = "❌ 刷新失败";
            }
        }

        /// <summary>
        /// 🎯 测试ListView颜色命令
        /// </summary>
        [RelayCommand]
        private void TestListViewColors()
        {
            try
            {
                var app = System.Windows.Application.Current;
                if (app?.Resources != null)
                {
                    _logger.Info("🔍 当前ListView颜色资源状态：");

                    // 检查ListView相关资源
                    var resources = new[]
                    {
                        "ListViewItemPillFillBrush",
                        "ListViewItemBackgroundPointerOver",
                        "ListViewItemForeground",
                        "SystemAccentColorPrimary",
                        "SystemAccentColorPrimaryBrush"
                    };

                    foreach (var resourceKey in resources)
                    {
                        if (app.Resources.Contains(resourceKey))
                        {
                            var resource = app.Resources[resourceKey];
                            if (resource is SolidColorBrush brush)
                            {
                                var color = brush.Color;
                                _logger.Info($"   {resourceKey}: #{color.A:X2}{color.R:X2}{color.G:X2}{color.B:X2}");
                            }
                            else if (resource is Color colorResource)
                            {
                                _logger.Info($"   {resourceKey}: #{colorResource.A:X2}{colorResource.R:X2}{colorResource.G:X2}{colorResource.B:X2}");
                            }
                            else
                            {
                                _logger.Info($"   {resourceKey}: {resource?.GetType().Name ?? "null"}");
                            }
                        }
                        else
                        {
                            _logger.Warning($"   {resourceKey}: 未找到");
                        }
                    }

                    // 强制重新应用强调色
                    var currentAccentColor = _themeService.GetCurrentAccentColor();
                    _themeService.ApplyAccentColor(currentAccentColor);

                    StatusMessage = "🎯 ListView颜色测试完成，请查看日志";
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 测试ListView颜色失败: {ex.Message}");
                StatusMessage = "❌ 测试失败";
            }
        }



        /// <summary>
        /// 📋 简单直接的剪贴板复制 - 不要过度复杂化
        /// </summary>
        private void SetClipboardText(string text)
        {
            try
            {
                Clipboard.SetText(text);
            }
            catch (System.Runtime.InteropServices.COMException)
            {
                // 如果失败，简单重试一次
                try
                {
                    System.Threading.Thread.Sleep(100);
                    Clipboard.SetText(text);
                }
                catch
                {
                    throw new InvalidOperationException("剪贴板当前不可用，请稍后再试");
                }
            }
        }

        #endregion

        #endregion
    }

   /// <summary>
    /// 颜色项模型
    /// </summary>
    public partial class ColorItem : ObservableObject
    {
        /// <summary>
        /// 资源键名
        /// </summary>
        [ObservableProperty]
        private string resourceKey = string.Empty;

        /// <summary>
        /// 显示名称
        /// </summary>
        [ObservableProperty]
        private string name = string.Empty;

        /// <summary>
        /// 描述信息
        /// </summary>
        [ObservableProperty]
        private string description = string.Empty;

        /// <summary>
        /// 十六进制颜色值
        /// </summary>
        [ObservableProperty]
        private string hexValue = string.Empty;

        /// <summary>
        /// 实际颜色画刷
        /// </summary>
        [ObservableProperty]
        private Brush? colorBrush;

        /// <summary>
        /// 是否为深色背景（需要浅色文字）
        /// </summary>
        [ObservableProperty]
        private bool isDarkBackground = false;

        /// <summary>
        /// 文字颜色画刷
        /// </summary>
        [ObservableProperty]
        private Brush? textBrush;

        public ColorItem(string resourceKey, string name, string description, bool isDarkBackground = false)
        {
            ResourceKey = resourceKey;
            Name = name;
            Description = description;
            IsDarkBackground = isDarkBackground;
            
            RefreshColor();
        }

        /// <summary>
        /// 刷新颜色信息
        /// </summary>
        public void RefreshColor()
        {
            try
            {
                // 获取颜色画刷
                if (Application.Current.TryFindResource(ResourceKey) is Brush brush)
                {
                    ColorBrush = brush;
                    
                    // 尝试获取十六进制值
                    if (brush is SolidColorBrush solidBrush)
                    {
                        var color = solidBrush.Color;
                        HexValue = $"#{color.R:X2}{color.G:X2}{color.B:X2}";
                    }
                    else
                    {
                        HexValue = "渐变色";
                    }
                }
                else
                {
                    ColorBrush = new SolidColorBrush(Colors.Gray);
                    HexValue = "#808080";
                }

                // 设置文字颜色
                var textResourceKey = IsDarkBackground ? "TextFillColorInverseBrush" : "TextFillColorPrimaryBrush";
                if (Application.Current.TryFindResource(textResourceKey) is Brush textBrush)
                {
                    TextBrush = textBrush;
                }
                else
                {
                    TextBrush = IsDarkBackground ? new SolidColorBrush(Colors.White) : new SolidColorBrush(Colors.Black);
                }
            }
            catch (Exception)
            {
                // 设置默认值
                ColorBrush = new SolidColorBrush(Colors.Gray);
                HexValue = "#808080";
                TextBrush = new SolidColorBrush(Colors.Black);
            }
        }
    }

    /// <summary>
    /// 颜色分组模型
    /// </summary>
    public partial class ColorGroup : ObservableObject
    {
        /// <summary>
        /// 分组名称
        /// </summary>
        [ObservableProperty]
        private string name = string.Empty;

        /// <summary>
        /// 分组描述
        /// </summary>
        [ObservableProperty]
        private string description = string.Empty;

        /// <summary>
        /// 颜色列表
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<ColorItem> colors = new();

        /// <summary>
        /// 是否展开
        /// </summary>
        [ObservableProperty]
        private bool isExpanded = true;
    }
}
