using System;
using System.Windows;
using Zylo.YLog.Runtime;

namespace WPFTest.Services;

/// <summary>
/// 资源验证服务 - 验证 Zylo.WPF 资源是否正确加载
/// </summary>
public interface IResourceVerificationService
{
    /// <summary>
    /// 验证 Zylo.WPF 资源加载状态
    /// </summary>
    void VerifyZyloResources();
}

/// <summary>
/// 资源验证服务实现
/// </summary>
public class ResourceVerificationService : IResourceVerificationService
{
    public readonly YLoggerInstance _logger = YLogger.ForSilent<MainViewModel>();
    /// <summary>
    /// 验证 Zylo.WPF 资源加载状态
    /// </summary>
    public void VerifyZyloResources()
    {
       _logger.Info("🔍 开始验证 Zylo.WPF 资源加载状态...");

        try
        {
            // 验证字符串资源
            var testMessage = Application.Current.TryFindResource("ZyloTestMessage");
            if (testMessage == null)
            {
                _logger.Warning("⚠️ ZyloTestMessage 未找到");
            }

            // 验证画刷资源
            var testBrush = Application.Current.TryFindResource("ZyloTestBrush");
            if (testBrush == null)
            {
                _logger.Warning("⚠️ ZyloTestBrush 未找到");
            }

            // 验证样式资源
            var testStyle = Application.Current.TryFindResource("ZyloTestButtonStyle");
            if (testStyle == null)
            {
                _logger.Warning("⚠️ ZyloTestButtonStyle 未找到");
            }

            // 验证字体资源
            var iconFont = Application.Current.TryFindResource("ZyloIconFont");
            if (iconFont == null)
            {
                _logger.Warning("⚠️ ZyloIconFont 未找到");
            }

            // 统计结果
            var loadedCount = 0;
            if (testMessage != null) loadedCount++;
            if (testBrush != null) loadedCount++;
            if (testStyle != null) loadedCount++;
            if (iconFont != null) loadedCount++;

            if (loadedCount < 4)
            {
                _logger.Warning($"⚠️ 部分资源加载失败。成功: {loadedCount}/4");
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 资源验证过程中发生异常: {ex.Message}");
        }
    }
}
