<!-- NumberBox 高级功能示例 -->
<StackPanel Margin="20">

    <!-- 数值验证和范围控制 -->
    <GroupBox Header="数值验证和范围控制" Padding="15" Margin="0,0,0,15">
        <StackPanel>
            <TextBlock Text="高级验证功能：" FontWeight="Bold" Margin="0,0,0,8"/>

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="16"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 左列：输入控件 -->
                <StackPanel Grid.Column="0">
                    <TextBlock Text="验证数值输入" FontWeight="Medium" Margin="0,0,0,4"/>
                    <ui:NumberBox Value="{Binding BasicValue, UpdateSourceTrigger=PropertyChanged}"
                                  Minimum="{Binding MinimumValue}"
                                  Maximum="{Binding MaximumValue}"
                                  SmallChange="{Binding StepValue}"
                                  PlaceholderText="输入数值进行验证"
                                  Margin="0,0,0,8"/>

                    <!-- 验证选项 -->
                    <StackPanel>
                        <CheckBox Content="启用范围验证"
                                  IsChecked="{Binding EnableRangeValidation}"
                                  Margin="0,0,0,4"/>
                        <CheckBox Content="启用格式验证"
                                  IsChecked="{Binding EnableFormatValidation}"
                                  Margin="0,0,0,4"/>
                        <ui:Button Content="验证数值"
                                   Appearance="Primary"
                                   Command="{Binding ValidateNumberCommand}"
                                   HorizontalAlignment="Left"/>
                    </StackPanel>
                </StackPanel>

                <!-- 右列：验证结果 -->
                <StackPanel Grid.Column="2">
                    <TextBlock Text="验证结果" FontWeight="Medium" Margin="0,0,0,4"/>

                    <!-- 验证状态显示 -->
                    <Border Background="{DynamicResource ControlFillColorTertiaryBrush}"
                            CornerRadius="4"
                            Padding="8"
                            Margin="0,0,0,8">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                                <TextBlock Text="📊" FontSize="12" Margin="0,0,4,0"/>
                                <TextBlock Text="{Binding ValidationResult.Summary}" FontSize="11"/>
                            </StackPanel>

                            <!-- 验证消息 -->
                            <TextBlock Text="{Binding ValidationMessage}"
                                       FontSize="11"
                                       Foreground="{Binding ValidationMessageColor}"
                                       TextWrapping="Wrap"/>
                        </StackPanel>
                    </Border>

                    <!-- 范围设置 -->
                    <TextBlock Text="范围设置" FontSize="12" Margin="0,0,0,4"/>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Margin="0,0,2,0">
                            <TextBlock Text="最小值" FontSize="10" Margin="0,0,0,1"/>
                            <ui:NumberBox Value="{Binding MinimumValue}"
                                          FontSize="10"
                                          Height="24"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Margin="2,0,0,0">
                            <TextBlock Text="最大值" FontSize="10" Margin="0,0,0,1"/>
                            <ui:NumberBox Value="{Binding MaximumValue}"
                                          FontSize="10"
                                          Height="24"/>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </Grid>
        </StackPanel>
    </GroupBox>

    <!-- 单位转换 -->
    <GroupBox Header="单位转换" Padding="15" Margin="0,0,0,15">
        <StackPanel>
            <TextBlock Text="单位转换功能：" FontWeight="Bold" Margin="0,0,0,8"/>

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="16"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 左列：温度和重量输入 -->
                <StackPanel Grid.Column="0">
                    <!-- 温度输入 -->
                    <StackPanel Margin="0,0,0,12">
                        <TextBlock Text="温度 (°C)" FontWeight="Medium" Margin="0,0,0,4"/>
                        <Grid>
                            <ui:NumberBox Value="{Binding TemperatureValue, UpdateSourceTrigger=PropertyChanged}"
                                          PlaceholderText="温度值"
                                          Minimum="-273.15"
                                          Maximum="1000"/>
                            <TextBlock Text="°C"
                                       HorizontalAlignment="Right"
                                       VerticalAlignment="Center"
                                       Margin="0,0,8,0"
                                       FontSize="12"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                        </Grid>
                    </StackPanel>

                    <!-- 重量输入 -->
                    <StackPanel>
                        <TextBlock Text="重量 (kg)" FontWeight="Medium" Margin="0,0,0,4"/>
                        <Grid>
                            <ui:NumberBox Value="{Binding WeightValue, UpdateSourceTrigger=PropertyChanged}"
                                          PlaceholderText="重量值"
                                          Minimum="0"
                                          Maximum="1000"/>
                            <TextBlock Text="kg"
                                       HorizontalAlignment="Right"
                                       VerticalAlignment="Center"
                                       Margin="0,0,8,0"
                                       FontSize="12"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                        </Grid>
                    </StackPanel>
                </StackPanel>

                <!-- 右列：转换结果 -->
                <StackPanel Grid.Column="2">
                    <TextBlock Text="转换结果" FontWeight="Medium" Margin="0,0,0,8"/>

                    <Border Background="{DynamicResource ControlFillColorTertiaryBrush}"
                            CornerRadius="4" Padding="8">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                                <TextBlock Text="华氏度:" FontSize="12" Margin="0,0,4,0"/>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="{Binding TemperatureValue, StringFormat='{}{0:F1}°C'}" FontSize="12"/>
                                    <TextBlock Text=" = " FontSize="12" Margin="4,0"/>
                                    <TextBlock Text="{Binding FahrenheitValue, StringFormat='{}{0:F1}°F'}" FontSize="12"/>
                                </StackPanel>
                            </StackPanel>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="磅:" FontSize="12" Margin="0,0,4,0"/>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="{Binding WeightValue, StringFormat='{}{0:F1}kg'}" FontSize="12"/>
                                    <TextBlock Text=" = " FontSize="12" Margin="4,0"/>
                                    <TextBlock Text="{Binding PoundsValue, StringFormat='{}{0:F1}lbs'}" FontSize="12"/>
                                </StackPanel>
                            </StackPanel>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Grid>
        </StackPanel>
    </GroupBox>

</StackPanel>
