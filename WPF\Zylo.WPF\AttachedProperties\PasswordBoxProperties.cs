using System.Windows;
using System.Windows.Controls;

namespace Zylo.WPF.AttachedProperties
{
    /// <summary>
    /// PasswordBox 附加属性
    /// 解决 PasswordBox.Password 不能绑定的问题
    /// </summary>
    public static class PasswordBoxProperties
    {
        #region BoundPassword 附加属性

        /// <summary>
        /// BoundPassword 附加属性
        /// </summary>
        public static readonly DependencyProperty BoundPasswordProperty =
            DependencyProperty.RegisterAttached(
                "BoundPassword",
                typeof(string),
                typeof(PasswordBoxProperties),
                new FrameworkPropertyMetadata(
                    string.Empty,
                    FrameworkPropertyMetadataOptions.BindsTwoWayByDefault,
                    OnBoundPasswordChanged));

        /// <summary>
        /// 获取绑定的密码
        /// </summary>
        public static string GetBoundPassword(DependencyObject obj)
        {
            return (string)obj.GetValue(BoundPasswordProperty);
        }

        /// <summary>
        /// 设置绑定的密码
        /// </summary>
        public static void SetBoundPassword(DependencyObject obj, string value)
        {
            obj.SetValue(BoundPasswordProperty, value);
        }

        /// <summary>
        /// BoundPassword 属性变化处理
        /// </summary>
        private static void OnBoundPasswordChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is PasswordBox passwordBox)
            {
                // 移除事件处理器以避免循环
                passwordBox.PasswordChanged -= OnPasswordChanged;

                var newPassword = (string)e.NewValue;
                if (!string.Equals(passwordBox.Password, newPassword))
                {
                    passwordBox.Password = newPassword ?? string.Empty;
                }

                // 重新添加事件处理器
                passwordBox.PasswordChanged += OnPasswordChanged;
            }
        }

        #endregion

        #region IsMonitoring 附加属性

        /// <summary>
        /// IsMonitoring 附加属性 - 用于标记是否正在监控密码变化
        /// </summary>
        public static readonly DependencyProperty IsMonitoringProperty =
            DependencyProperty.RegisterAttached(
                "IsMonitoring",
                typeof(bool),
                typeof(PasswordBoxProperties),
                new PropertyMetadata(false, OnIsMonitoringChanged));

        /// <summary>
        /// 获取是否正在监控
        /// </summary>
        public static bool GetIsMonitoring(DependencyObject obj)
        {
            return (bool)obj.GetValue(IsMonitoringProperty);
        }

        /// <summary>
        /// 设置是否正在监控
        /// </summary>
        public static void SetIsMonitoring(DependencyObject obj, bool value)
        {
            obj.SetValue(IsMonitoringProperty, value);
        }

        /// <summary>
        /// IsMonitoring 属性变化处理
        /// </summary>
        private static void OnIsMonitoringChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is PasswordBox passwordBox)
            {
                if ((bool)e.NewValue)
                {
                    passwordBox.PasswordChanged += OnPasswordChanged;
                }
                else
                {
                    passwordBox.PasswordChanged -= OnPasswordChanged;
                }
            }
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// PasswordBox 密码变化事件处理
        /// </summary>
        private static void OnPasswordChanged(object sender, RoutedEventArgs e)
        {
            if (sender is PasswordBox passwordBox)
            {
                SetBoundPassword(passwordBox, passwordBox.Password);
            }
        }

        #endregion

        #region PasswordStrength 附加属性

        /// <summary>
        /// PasswordStrength 附加属性 - 用于显示密码强度
        /// </summary>
        public static readonly DependencyProperty PasswordStrengthProperty =
            DependencyProperty.RegisterAttached(
                "PasswordStrength",
                typeof(double),
                typeof(PasswordBoxProperties),
                new PropertyMetadata(0.0));

        /// <summary>
        /// 获取密码强度
        /// </summary>
        public static double GetPasswordStrength(DependencyObject obj)
        {
            return (double)obj.GetValue(PasswordStrengthProperty);
        }

        /// <summary>
        /// 设置密码强度
        /// </summary>
        public static void SetPasswordStrength(DependencyObject obj, double value)
        {
            obj.SetValue(PasswordStrengthProperty, value);
        }

        #endregion

        #region EnablePasswordBinding 附加属性

        /// <summary>
        /// EnablePasswordBinding 附加属性 - 一键启用密码绑定
        /// </summary>
        public static readonly DependencyProperty EnablePasswordBindingProperty =
            DependencyProperty.RegisterAttached(
                "EnablePasswordBinding",
                typeof(bool),
                typeof(PasswordBoxProperties),
                new PropertyMetadata(false, OnEnablePasswordBindingChanged));

        /// <summary>
        /// 获取是否启用密码绑定
        /// </summary>
        public static bool GetEnablePasswordBinding(DependencyObject obj)
        {
            return (bool)obj.GetValue(EnablePasswordBindingProperty);
        }

        /// <summary>
        /// 设置是否启用密码绑定
        /// </summary>
        public static void SetEnablePasswordBinding(DependencyObject obj, bool value)
        {
            obj.SetValue(EnablePasswordBindingProperty, value);
        }

        /// <summary>
        /// EnablePasswordBinding 属性变化处理
        /// </summary>
        private static void OnEnablePasswordBindingChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is PasswordBox passwordBox)
            {
                if ((bool)e.NewValue)
                {
                    // 启用密码绑定
                    SetIsMonitoring(passwordBox, true);
                }
                else
                {
                    // 禁用密码绑定
                    SetIsMonitoring(passwordBox, false);
                }
            }
        }

        #endregion
    }
}
