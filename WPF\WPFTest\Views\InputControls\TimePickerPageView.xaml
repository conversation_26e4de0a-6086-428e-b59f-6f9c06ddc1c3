<UserControl x:Class="WPFTest.Views.InputControls.TimePickerPageView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:codeExample="clr-namespace:Zylo.WPF.Controls.CodeExample;assembly=Zylo.WPF"
             xmlns:mvvm="http://prismlibrary.com/"
             xmlns:inputControls="clr-namespace:WPFTest.ViewModels.InputControls"
             mc:Ignorable="d"
             d:DesignHeight="800" d:DesignWidth="1200"
             mvvm:ViewModelLocator.AutoWireViewModel="True"
             d:DataContext="{d:DesignInstance inputControls:TimePickerPageViewModel}">

    <UserControl.Resources>
        <!-- 🎨 所有样式来自 Zylo.WPF 项目 -->
        <!-- TimePicker 样式已在 Zylo.WPF/Resources/TimePicker/ 目录下定义 -->
        <!-- 可用样式：TimePickerStyle, SmallTimePickerStyle, LargeTimePickerStyle -->
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" 
                  HorizontalScrollBarVisibility="Disabled"
                  Padding="24">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>    <!-- 标题区域 -->
                <RowDefinition Height="*"/>       <!-- 主要内容 -->
                <RowDefinition Height="Auto"/>    <!-- 底部操作栏 -->
            </Grid.RowDefinitions>

            <!-- 标题区域 -->
            <StackPanel Grid.Row="0" Margin="0,0,0,20">
                <TextBlock Text="🕐 TimePicker 控件展示"
                           FontSize="32"
                           FontWeight="Bold"
                           Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                           Margin="0,0,0,8"/>
                <TextBlock Text="展示 TimePicker 控件的时间选择功能和各种交互效果" 
                           FontSize="16" 
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                           TextWrapping="Wrap"/>
                
                <!-- 状态显示 -->
                <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                        BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Padding="16,12"
                        Margin="0,16,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <ui:SymbolIcon Symbol="Info24" 
                                       FontSize="20" 
                                       Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                       VerticalAlignment="Center"
                                       Margin="0,0,12,0"/>
                        <TextBlock Grid.Column="1" 
                                   Text="{Binding StatusMessage}" 
                                   FontSize="14"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                   VerticalAlignment="Center"
                                   TextWrapping="Wrap"/>
                    </Grid>
                </Border>
            </StackPanel>

            <!-- 主要内容区域 -->
            <Border Grid.Row="1" Margin="0,0,0,20">
                <StackPanel>

                    <!-- 基础功能展示 -->
                    <ui:CardExpander Header="🎯 基础功能" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 TimePicker 的基础时间选择功能"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>
                            
                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="基础功能示例：" FontWeight="Medium" Margin="0,0,0,16"/>
                                    
                                    <!-- 基础TimePicker示例 -->
                                    <UniformGrid Columns="3">
                                        
                                        <!-- 标准时间选择器卡片 -->
                                        <Border Background="{DynamicResource CardBackgroundFillColorSecondaryBrush}"
                                                BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                                BorderThickness="1"
                                                CornerRadius="8"
                                                Padding="20"
                                                Margin="0,0,12,0">
                                            <StackPanel>
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                                    <Ellipse Width="8" Height="8"
                                                             Fill="{DynamicResource SystemAccentColorPrimaryBrush}"
                                                             VerticalAlignment="Center"
                                                             Margin="0,0,8,0"/>
                                                    <TextBlock Text="标准时间选择器"
                                                               FontWeight="Medium"
                                                               FontSize="14"/>
                                                </StackPanel>

                                                <!-- WPF UI TimePicker (可能有问题) -->
                                                <ui:TimePicker x:Name="TestTimePicker"
                                                               SelectedTime="{Binding SelectedTime, Mode=TwoWay}"
                                                               HorizontalAlignment="Stretch"
                                                               Height="40"
                                                               FontSize="14"
                                                               Margin="0,0,0,8"
                                                               IsEnabled="True"
                                                               Background="LightYellow"
                                                               BorderBrush="Blue"
                                                               BorderThickness="2"
                                                               ToolTip="WPF UI TimePicker - 可能有问题"/>

                                                <!-- 替代方案：自定义时间选择器 -->
                                                <Border Background="LightGreen"
                                                        BorderBrush="DarkGreen"
                                                        BorderThickness="2"
                                                        CornerRadius="4"
                                                        Padding="8"
                                                        Margin="0,0,0,8">
                                                    <StackPanel Orientation="Horizontal">
                                                        <TextBlock Text="替代方案: "
                                                                   VerticalAlignment="Center"
                                                                   FontWeight="Bold"/>
                                                        <ComboBox x:Name="HourComboBox"
                                                                  Width="60"
                                                                  Margin="4,0"
                                                                  SelectedIndex="9">
                                                            <ComboBoxItem Content="00"/>
                                                            <ComboBoxItem Content="01"/>
                                                            <ComboBoxItem Content="02"/>
                                                            <ComboBoxItem Content="03"/>
                                                            <ComboBoxItem Content="04"/>
                                                            <ComboBoxItem Content="05"/>
                                                            <ComboBoxItem Content="06"/>
                                                            <ComboBoxItem Content="07"/>
                                                            <ComboBoxItem Content="08"/>
                                                            <ComboBoxItem Content="09"/>
                                                            <ComboBoxItem Content="10"/>
                                                            <ComboBoxItem Content="11"/>
                                                            <ComboBoxItem Content="12"/>
                                                            <ComboBoxItem Content="13"/>
                                                            <ComboBoxItem Content="14"/>
                                                            <ComboBoxItem Content="15"/>
                                                            <ComboBoxItem Content="16"/>
                                                            <ComboBoxItem Content="17"/>
                                                            <ComboBoxItem Content="18"/>
                                                            <ComboBoxItem Content="19"/>
                                                            <ComboBoxItem Content="20"/>
                                                            <ComboBoxItem Content="21"/>
                                                            <ComboBoxItem Content="22"/>
                                                            <ComboBoxItem Content="23"/>
                                                        </ComboBox>
                                                        <TextBlock Text=":"
                                                                   VerticalAlignment="Center"
                                                                   FontWeight="Bold"
                                                                   FontSize="16"/>
                                                        <ComboBox x:Name="MinuteComboBox"
                                                                  Width="60"
                                                                  Margin="4,0"
                                                                  SelectedIndex="0">
                                                            <ComboBoxItem Content="00"/>
                                                            <ComboBoxItem Content="15"/>
                                                            <ComboBoxItem Content="30"/>
                                                            <ComboBoxItem Content="45"/>
                                                        </ComboBox>
                                                        <Button Content="设置时间"
                                                                Margin="8,0,0,0"
                                                                Padding="8,4"
                                                                Click="SetTimeFromComboBoxes"
                                                                Background="Orange"/>
                                                    </StackPanel>
                                                </Border>

                                                <!-- 简单的文本输入方案 -->
                                                <Border Background="LightBlue"
                                                        BorderBrush="DarkBlue"
                                                        BorderThickness="2"
                                                        CornerRadius="4"
                                                        Padding="8"
                                                        Margin="0,0,0,8">
                                                    <StackPanel Orientation="Horizontal">
                                                        <TextBlock Text="文本输入: "
                                                                   VerticalAlignment="Center"
                                                                   FontWeight="Bold"/>
                                                        <TextBox x:Name="TimeTextBox"
                                                                 Width="100"
                                                                 Margin="4,0"
                                                                 Text="14:30"
                                                                 ToolTip="格式: HH:mm"/>
                                                        <Button Content="设置时间"
                                                                Margin="8,0,0,0"
                                                                Padding="8,4"
                                                                Click="SetTimeFromTextBox"
                                                                Background="Cyan"/>
                                                    </StackPanel>
                                                </Border>

                                                <TextBlock Text="{Binding SelectedTime, StringFormat='选择时间: {0:HH:mm:ss}'}"
                                                           FontSize="12"
                                                           Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                                           TextAlignment="Center"
                                                           Background="{DynamicResource AccentFillColorDefaultBrush}"
                                                           Padding="8,4"
                                                           />

                                                <!-- 测试按钮 -->
                                                <Button Content="测试TimePicker点击"
                                                        Click="TestTimePickerClick"
                                                        Margin="0,8,0,0"
                                                        Padding="8,4"
                                                        Background="LightBlue"/>
                                            </StackPanel>
                                        </Border>

                                        <!-- 工作时间选择器卡片 -->
                                        <Border Background="{DynamicResource CardBackgroundFillColorSecondaryBrush}"
                                                BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                                BorderThickness="1"
                                                CornerRadius="8"
                                                Padding="20"
                                                Margin="6,0,6,0">
                                            <StackPanel>
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                                    <Ellipse Width="8" Height="8"
                                                             Fill="#FF6B46C1"
                                                             VerticalAlignment="Center"
                                                             Margin="0,0,8,0"/>
                                                    <TextBlock Text="工作时间选择器"
                                                               FontWeight="Medium"
                                                               FontSize="14"/>
                                                </StackPanel>

                                                <ui:TimePicker SelectedTime="{Binding WorkTime, Mode=TwoWay}"
                                                               HorizontalAlignment="Stretch"
                                                               Height="40"
                                                               FontSize="14"
                                                               Margin="0,0,0,12"/>

                                                <TextBlock Text="{Binding WorkTime, StringFormat='工作时间: {0:HH:mm}'}"
                                                           FontSize="12"
                                                           Foreground="#FF6B46C1"
                                                           TextAlignment="Center"
                                                           Background="#1A6B46C1"
                                                           Padding="8,4"
                                                           />
                                            </StackPanel>
                                        </Border>

                                        <!-- 提醒时间选择器卡片 -->
                                        <Border Background="{DynamicResource CardBackgroundFillColorSecondaryBrush}"
                                                BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                                BorderThickness="1"
                                                CornerRadius="8"
                                                Padding="20"
                                                Margin="12,0,0,0">
                                            <StackPanel>
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                                    <Ellipse Width="8" Height="8"
                                                             Fill="#FF10B981"
                                                             VerticalAlignment="Center"
                                                             Margin="0,0,8,0"/>
                                                    <TextBlock Text="提醒时间选择器"
                                                               FontWeight="Medium"
                                                               FontSize="14"/>
                                                </StackPanel>

                                                <ui:TimePicker SelectedTime="{Binding ReminderTime, Mode=TwoWay}"
                                                               HorizontalAlignment="Stretch"
                                                               Height="40"
                                                               FontSize="14"
                                                               Margin="0,0,0,12"/>

                                                <TextBlock Text="{Binding ReminderTime, StringFormat='提醒时间: {0:HH:mm}'}"
                                                           FontSize="12"
                                                           Foreground="#FF10B981"
                                                           TextAlignment="Center"
                                                           Background="#1A10B981"
                                                           Padding="8,4"
                                                           />
                                            </StackPanel>
                                        </Border>
                                    </UniformGrid>

                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="基础功能代码示例"
                                Language="XAML"
                                Description="展示 TimePicker 的基础用法"
                                ShowTabs="True"
                                XamlCode="{Binding BasicXamlExample}"
                                CSharpCode="{Binding BasicCSharpExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 高级功能展示 -->
                    <ui:CardExpander Header="⚡ 高级功能" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 TimePicker 的高级功能和时间范围选择"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="高级功能示例：" FontWeight="Medium" Margin="0,0,0,16"/>

                                    <!-- 时间范围选择 -->
                                    <GroupBox Header="📅 时间范围选择" Margin="0,0,0,20" Padding="16">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <TextBlock Grid.Column="0"
                                                       Text="开始时间："
                                                       VerticalAlignment="Center"
                                                       FontSize="14"
                                                       Margin="0,0,12,0"/>
                                            <ui:TimePicker Grid.Column="1"
                                                           SelectedTime="{Binding StartTime, Mode=TwoWay}"
                                                           Width="200"
                                                           Height="36"
                                                           FontSize="14"
                                                           Margin="0,0,20,0"/>

                                            <TextBlock Grid.Column="2"
                                                       Text="结束时间："
                                                       VerticalAlignment="Center"
                                                       FontSize="14"
                                                       Margin="0,0,12,0"/>
                                            <ui:TimePicker Grid.Column="3"
                                                           SelectedTime="{Binding EndTime, Mode=TwoWay}"
                                                           Width="200"
                                                           Height="36"
                                                           FontSize="14"/>
                                        </Grid>
                                    </GroupBox>

                                    <!-- 程序化控制 -->
                                    <GroupBox Header="🎛️ 程序化控制" Margin="0,0,0,20" Padding="16">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <TextBlock Grid.Column="0"
                                                       Text="程序化时间："
                                                       VerticalAlignment="Center"
                                                       FontSize="14"
                                                       Margin="0,0,12,0"/>
                                            <ui:TimePicker Grid.Column="1"
                                                           SelectedTime="{Binding ProgrammaticTime, Mode=TwoWay}"
                                                           Width="200"
                                                           Height="36"
                                                           FontSize="14"
                                                           Margin="0,0,20,0"/>

                                            <StackPanel Grid.Column="2" Orientation="Horizontal">
                                                <ui:Button Content="设置9:00"
                                                           Command="{Binding SetSpecificTimeCommand}"
                                                           CommandParameter="09:00"
                                                           Appearance="Secondary"
                                                           FontSize="12"
                                                           Margin="0,0,4,0"/>
                                                <ui:Button Content="设置12:30"
                                                           Command="{Binding SetSpecificTimeCommand}"
                                                           CommandParameter="12:30"
                                                           Appearance="Secondary"
                                                           FontSize="12"
                                                           Margin="0,0,4,0"/>
                                                <ui:Button Content="设置18:00"
                                                           Command="{Binding SetSpecificTimeCommand}"
                                                           CommandParameter="18:00"
                                                           Appearance="Secondary"
                                                           FontSize="12"/>
                                            </StackPanel>
                                        </Grid>
                                    </GroupBox>

                                    <!-- 时间格式展示 -->
                                    <GroupBox Header="🕐 时间格式展示" Padding="16">
                                        <Grid>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                            </Grid.RowDefinitions>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <TextBlock Grid.Row="0" Grid.Column="0"
                                                       Text="24小时格式："
                                                       VerticalAlignment="Center"
                                                       FontSize="14"
                                                       Margin="0,0,12,8"/>
                                            <TextBlock Grid.Row="0" Grid.Column="1"
                                                       Text="{Binding SelectedTime, StringFormat='{}{0:HH:mm:ss}'}"
                                                       FontSize="14"
                                                       FontWeight="Medium"
                                                       Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                                       Margin="0,0,0,8"/>

                                            <TextBlock Grid.Row="1" Grid.Column="0"
                                                       Text="12小时格式："
                                                       VerticalAlignment="Center"
                                                       FontSize="14"
                                                       Margin="0,0,12,8"/>
                                            <TextBlock Grid.Row="1" Grid.Column="1"
                                                       Text="{Binding SelectedTime, StringFormat='12小时格式暂不支持 TimeSpan'}"
                                                       FontSize="14"
                                                       FontWeight="Medium"
                                                       Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                                       Margin="0,0,0,8"/>

                                            <TextBlock Grid.Row="2" Grid.Column="0"
                                                       Text="简短格式："
                                                       VerticalAlignment="Center"
                                                       FontSize="14"
                                                       Margin="0,0,12,0"/>
                                            <TextBlock Grid.Row="2" Grid.Column="1"
                                                       Text="{Binding SelectedTime, StringFormat='{}{0:HH:mm}'}"
                                                       FontSize="14"
                                                       FontWeight="Medium"
                                                       Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"/>
                                        </Grid>
                                    </GroupBox>

                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="高级功能代码示例"
                                Language="XAML"
                                Description="展示 TimePicker 的高级用法和程序化控制"
                                ShowTabs="True"
                                XamlCode="{Binding AdvancedXamlExample}"
                                CSharpCode="{Binding AdvancedCSharpExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 样式展示 -->
                    <ui:CardExpander Header="🎨 样式展示" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示来自 Zylo.WPF 的各种 TimePicker 样式"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 示例展示区域 -->
                            <ui:Card Padding="32" Margin="0,0,0,16">
                                <StackPanel>
                                    <!-- 标题区域 -->
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,24">
                                        <ui:SymbolIcon Symbol="ColorBackground24"
                                                       FontSize="20"
                                                       Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,8,0"/>
                                        <TextBlock Text="样式展示示例"
                                                   FontWeight="SemiBold"
                                                   FontSize="18"
                                                   VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <!-- 使用四列布局展示不同样式 -->
                                    <UniformGrid Columns="4">

                                        <!-- 紧凑样式卡片 -->
                                        <Border Background="{DynamicResource CardBackgroundFillColorSecondaryBrush}"
                                                BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                                BorderThickness="1"
                                                CornerRadius="8"
                                                Padding="16"
                                                Margin="0,0,8,0">
                                            <StackPanel>
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                                    <Ellipse Width="6" Height="6"
                                                             Fill="#FF8B5CF6"
                                                             VerticalAlignment="Center"
                                                             Margin="0,0,6,0"/>
                                                    <TextBlock Text="紧凑样式"
                                                               FontWeight="Medium"
                                                               FontSize="12"/>
                                                </StackPanel>

                                                <ui:TimePicker SelectedTime="{Binding SelectedTime, Mode=TwoWay}"
                                                               HorizontalAlignment="Stretch"
                                                               Height="28"
                                                               FontSize="11"/>

                                                <TextBlock Text="空间紧张场景"
                                                           FontSize="10"
                                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                           TextAlignment="Center"
                                                           Margin="0,8,0,0"/>
                                            </StackPanel>
                                        </Border>

                                        <!-- 小型样式卡片 -->
                                        <Border Background="{DynamicResource CardBackgroundFillColorSecondaryBrush}"
                                                BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                                BorderThickness="1"
                                                CornerRadius="8"
                                                Padding="16"
                                                Margin="4,0,4,0">
                                            <StackPanel>
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                                    <Ellipse Width="6" Height="6"
                                                             Fill="#FF06B6D4"
                                                             VerticalAlignment="Center"
                                                             Margin="0,0,6,0"/>
                                                    <TextBlock Text="小型样式"
                                                               FontWeight="Medium"
                                                               FontSize="12"/>
                                                </StackPanel>

                                                <ui:TimePicker SelectedTime="{Binding SelectedTime, Mode=TwoWay}"
                                                               HorizontalAlignment="Stretch"
                                                               Height="32"
                                                               FontSize="12"/>

                                                <TextBlock Text="表单对话框"
                                                           FontSize="10"
                                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                           TextAlignment="Center"
                                                           Margin="0,8,0,0"/>
                                            </StackPanel>
                                        </Border>

                                        <!-- 标准样式卡片 -->
                                        <Border Background="{DynamicResource CardBackgroundFillColorSecondaryBrush}"
                                                BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                                BorderThickness="1"
                                                CornerRadius="8"
                                                Padding="16"
                                                Margin="4,0,4,0">
                                            <StackPanel>
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                                    <Ellipse Width="6" Height="6"
                                                             Fill="{DynamicResource SystemAccentColorPrimaryBrush}"
                                                             VerticalAlignment="Center"
                                                             Margin="0,0,6,0"/>
                                                    <TextBlock Text="标准样式"
                                                               FontWeight="Medium"
                                                               FontSize="12"/>
                                                </StackPanel>

                                                <ui:TimePicker SelectedTime="{Binding SelectedTime, Mode=TwoWay}"
                                                               HorizontalAlignment="Stretch"
                                                               Height="36"
                                                               FontSize="14"/>

                                                <TextBlock Text="推荐默认"
                                                           FontSize="10"
                                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                           TextAlignment="Center"
                                                           Margin="0,8,0,0"/>
                                            </StackPanel>
                                        </Border>

                                        <!-- 大型样式卡片 -->
                                        <Border Background="{DynamicResource CardBackgroundFillColorSecondaryBrush}"
                                                BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                                BorderThickness="1"
                                                CornerRadius="8"
                                                Padding="16"
                                                Margin="8,0,0,0">
                                            <StackPanel>
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                                    <Ellipse Width="6" Height="6"
                                                             Fill="#FFEF4444"
                                                             VerticalAlignment="Center"
                                                             Margin="0,0,6,0"/>
                                                    <TextBlock Text="大型样式"
                                                               FontWeight="Medium"
                                                               FontSize="12"/>
                                                </StackPanel>

                                                <ui:TimePicker SelectedTime="{Binding SelectedTime, Mode=TwoWay}"
                                                               HorizontalAlignment="Stretch"
                                                               Height="44"
                                                               FontSize="16"/>

                                                <TextBlock Text="重要选择"
                                                           FontSize="10"
                                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                                           TextAlignment="Center"
                                                           Margin="0,8,0,0"/>
                                            </StackPanel>
                                        </Border>
                                    </UniformGrid>

                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="样式代码示例"
                                Language="XAML"
                                Description="展示如何使用 Zylo.WPF 中的 TimePicker 样式"
                                ShowTabs="True"
                                XamlCode="{Binding StylesXamlExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                </StackPanel>
            </Border>

            <!-- 底部操作栏 -->
            <Border Grid.Row="2" 
                    Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Padding="16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 统计信息 -->
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock Text="{Binding InteractionCount, StringFormat='交互次数: {0}'}" 
                                   FontSize="14" 
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   Margin="0,0,20,0"/>
                        <TextBlock Text="{Binding LastAction, StringFormat='最后操作: {0}'}" 
                                   FontSize="14" 
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                    </StackPanel>

                    <!-- 操作按钮 -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <ui:Button Content="设置当前时间"
                                   Command="{Binding SetCurrentTimeCommand}"
                                   Appearance="Primary"
                                   Icon="{ui:SymbolIcon Clock24}"
                                   Margin="0,0,8,0"/>
                        <ui:Button Content="清除时间"
                                   Command="{Binding ClearTimeCommand}"
                                   Appearance="Secondary"
                                   Icon="{ui:SymbolIcon Delete24}"
                                   Margin="0,0,8,0"/>
                        <ui:Button Content="重置计数"
                                   Command="{Binding ResetCountCommand}"
                                   Appearance="Secondary"
                                   Margin="0,0,8,0"/>
                        <!-- 测试按钮 -->
                        <ui:Button Content="测试"
                                   Click="TestButton_Click"
                                   Appearance="Caution"
                                   FontSize="12"
                                   Margin="0,0,4,0"/>
                        <!-- 简单命令测试 -->
                        <ui:Button Content="简单测试"
                                   Command="{Binding SimpleTestCommand}"
                                   Appearance="Success"
                                   FontSize="12"/>
                    </StackPanel>
                </Grid>
            </Border>

        </Grid>
    </ScrollViewer>
</UserControl>
