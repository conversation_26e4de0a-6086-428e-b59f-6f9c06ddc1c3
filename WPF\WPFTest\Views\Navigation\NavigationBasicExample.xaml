<UserControl x:Class="WPFTest.Views.Navigation.NavigationBasicExample"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:zylo="clr-namespace:Zylo.WPF.Controls.Navigation;assembly=Zylo.WPF"
             xmlns:codeExample="clr-namespace:Zylo.WPF.Controls.CodeExample;assembly=Zylo.WPF"
             xmlns:navigation="clr-namespace:WPFTest.ViewModels.Navigation"
             xmlns:mvvm="http://prismlibrary.com/"
             mc:Ignorable="d" 
             
             mvvm:ViewModelLocator.AutoWireViewModel="True"
             d:DataContext="{d:DesignInstance navigation:NavigationBasicExampleViewModel}"
             
             d:DesignHeight="800" d:DesignWidth="1200">
    
    <UserControl.Resources>
        <!-- 示例样式 -->
        <Style x:Key="ExampleCardStyle" TargetType="ui:Card">
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Padding" Value="20"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 标题区域 -->
        <ui:Card Grid.Row="0" Style="{StaticResource ExampleCardStyle}">
            <StackPanel>
                <TextBlock Text="🎯 NavigationControl 基础示例" 
                          FontSize="24" 
                          FontWeight="Bold"
                          Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                          Margin="0,0,0,10"/>
                
                <TextBlock Text="展示 NavigationControl 的基本功能：简单导航、图标显示、主题适配" 
                          FontSize="14"
                          Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                          TextWrapping="Wrap"/>
            </StackPanel>
        </ui:Card>

        <!-- 主要内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧：NavigationControl 演示 -->
            <ui:Card Grid.Column="0" Style="{StaticResource ExampleCardStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                     
                    </Grid.RowDefinitions>

                    <!-- 导航标题 -->
                    <TextBlock Grid.Row="0" 
                              Text="📱 基础导航菜单" 
                              FontSize="16" 
                              FontWeight="SemiBold"
                              Margin="0,0,0,15"/>

                    <!-- NavigationControl -->
                    <zylo:NavigationControl Grid.Row="1"
                                           x:Name="BasicNavigationControl"
                                           ItemsSource="{Binding NavigationItems}"
                                           SelectedItem="{Binding SelectedNavigationItem, Mode=TwoWay}"
                                           NavigationItemSelectedCommand="{Binding NavigateToItemCommand}"
                                           ShowSubMenuOnClick="True"

                                           Background="Transparent"/>

                 
                </Grid>
            </ui:Card>

            <!-- 分隔符 -->
            <GridSplitter Grid.Column="1"
                         Width="5"
                         HorizontalAlignment="Stretch"
                         VerticalAlignment="Stretch"
                         Background="{DynamicResource ControlStrokeColorDefaultBrush}"
                         ResizeBehavior="PreviousAndNext"
                         ResizeDirection="Columns"/>

            <!-- 右侧：内容展示区域 -->
            <ui:Card Grid.Column="1" Style="{StaticResource ExampleCardStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 内容标题 -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
                        <ui:SymbolIcon Symbol="WindowApps24" FontSize="20" Margin="0,0,10,0"/>
                        <TextBlock Text="📄 内容展示区域" 
                                  FontSize="18" 
                                  FontWeight="SemiBold"
                                  VerticalAlignment="Center"/>
                    </StackPanel>

                    <!-- 动态内容区域 -->
                    <ScrollViewer Grid.Row="1">
                        <StackPanel>
                            <!-- 导航状态信息 -->
                            <ui:Card Margin="0,0,0,15" Padding="15">
                                <StackPanel>
                                    <TextBlock Text="🧭 导航状态" FontWeight="SemiBold" Margin="0,0,0,10"/>
                                    <TextBlock Text="{Binding NavigationStatus}"
                                              Margin="0,2"
                                              FontWeight="Medium"
                                              Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"/>
                                    <TextBlock Text="{Binding LastNavigationTarget, StringFormat='最后目标: {0}'}"
                                              Margin="0,2"/>
                                </StackPanel>
                            </ui:Card>

                            <!-- 当前选中项信息 -->
                            <ui:Card Margin="0,0,0,15" Padding="15">
                                <StackPanel>
                                    <TextBlock Text="🎯 当前选中项" FontWeight="SemiBold" Margin="0,0,0,10"/>
                                    <TextBlock Text="{Binding SelectedNavigationItem.Name, StringFormat='名称: {0}'}"
                                              Margin="0,2"/>
                                    <TextBlock Text="{Binding SelectedNavigationItem.Number, StringFormat='编号: {0}'}"
                                              Margin="0,2"/>
                                    <TextBlock Text="{Binding SelectedNavigationItem.NavigationTarget, StringFormat='目标: {0}'}"
                                              Margin="0,2"/>
                                </StackPanel>
                            </ui:Card>

                            <!-- 功能演示 -->
                            <ui:Card Margin="0,0,0,15" Padding="15">
                                <StackPanel>
                                    <TextBlock Text="⚡ 功能演示" FontWeight="SemiBold" Margin="0,0,0,10"/>
                                    
                                    <ui:Button Content="🔄 刷新导航数据" 
                                              Command="{Binding RefreshNavigationCommand}"
                                              Margin="0,5"/>
                                    
                                    <ui:Button Content="➕ 添加导航项"
                                              Command="{Binding AddNavigationItemCommand}"
                                              Margin="0,5"/>

                                    <ui:Button Content="✏️ 修改选中项"
                                              Command="{Binding EditNavigationItemCommand}"
                                              Margin="0,5"/>

                                    <ui:Button Content="🗑️ 移除选中项"
                                              Command="{Binding RemoveNavigationItemCommand}"
                                              Margin="0,5"/>
                                </StackPanel>
                            </ui:Card>

                 

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="NavigationControl 完整功能示例"
                                Language=" "
                                Description="展示 NavigationControl 的完整 CRUD 操作、静态帮助方法和现代化 MVVM 实现"
                                ShowTabs="True"
                                XamlCode="{Binding XamlCodeExample}"
                                CSharpCode="{Binding CSharpCodeExample}"/>
                        </StackPanel>
                    </ScrollViewer>
                </Grid>
            </ui:Card>
        </Grid>
    </Grid>
</UserControl>
