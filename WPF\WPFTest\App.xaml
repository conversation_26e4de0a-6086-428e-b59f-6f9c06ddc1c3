﻿<Application
    ShutdownMode="OnMainWindowClose"
    x:Class="WPFTest.App"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:local="clr-namespace:WPFTest"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Application.Resources>
        <!--  🚀 完整的 Prism + WPF-UI 资源配置  -->
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!--  WPF-UI 主题资源 - 使用 Dark 主题进行测试  -->
                <ui:ThemesDictionary Theme="Dark" />
                <ui:ControlsDictionary />

                <!--  🎯 Zylo.MVVM 资源字典  -->
                <ResourceDictionary Source="pack://application:,,,/Zylo.WPF;component/Themes/Generic.xaml" />

                <!--  Prism 资源字典  -->
                <!--  注意：由于我们使用普通 Application，这些资源需要手动包含  -->

            </ResourceDictionary.MergedDictionaries>

            <!--  🎯 Prism 相关的转换器和样式  -->

            <!--  基础转换器  -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />

            <!--  ListView 专用转换器现在在 Zylo.WPF 中定义  -->


        </ResourceDictionary>
    </Application.Resources>
</Application>
