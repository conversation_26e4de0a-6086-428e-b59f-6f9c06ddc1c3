# DragDrop 模块架构文档

## 📁 模块结构

```
Models/DragDrop/
├── README.md                    # 本文档
├── DwgFileModel.cs             # ✅ DWG文件模型（核心）
├── ProfessionTabModel.cs       # ✅ 专业Tab模型
├── FileTypeModel.cs            # ✅ 文件类型模型
├── DragDropConfiguration.cs    # ✅ 拖拽配置模型
└── DragDropModels.cs           # ✅ 通用拖拽模型集合

ViewModels/DragDrop/
├── DwgManagerTabViewModel.cs   # ✅ DWG管理主ViewModel（推荐）
├── DwgManagerViewModel.cs      # 🔄 旧版DWG管理ViewModel（待重构）
├── ListDragDropExampleViewModel.cs    # ✅ 列表拖拽示例
├── TreeDragDropExampleViewModel.cs    # ✅ 树形拖拽示例
├── FileDragExampleViewModel.cs        # ✅ 文件拖拽示例
└── CrossControlDragExampleViewModel.cs # ✅ 跨控件拖拽示例

Services/DragDrop/
└── IDwgFileService.cs          # ✅ DWG文件服务接口

Views/DragDrop/
└── DwgManagerTabView.xaml      # ✅ DWG管理主视图
```

## 🎯 模型职责划分

### 核心业务模型

#### **DwgFileModel** 📄
- **职责**: DWG文件的完整信息模型
- **用途**: 文件管理、显示、操作的核心数据结构
- **状态**: ✅ 核心模型，必须保留

#### **ProfessionTabModel** 🏢
- **职责**: 专业分类管理（建筑、暖通、水电、结构）
- **用途**: 顶部专业Tab显示和专业文件夹管理
- **状态**: ✅ 新架构核心模型

#### **FileTypeModel** 📋
- **职责**: 文件类型分类管理（出图、施工图、模板图等）
- **用途**: 左侧文件类型列表和文件分类
- **状态**: ✅ 新架构核心模型

### 配置和通用模型

#### **DragDropConfiguration** ⚙️
- **职责**: 拖拽行为配置和图标映射
- **用途**: 系统配置、图标管理、拖拽策略
- **状态**: ✅ 配置模型

#### **DragDropModels** 🔄
- **职责**: 通用拖拽功能模型集合
- **包含**: DragDropItem, DragDropContainer, DragDropOperation
- **用途**: 通用拖拽场景，可复用于其他模块
- **状态**: ✅ 通用模型

## 🗑️ 已移除的模型

#### **DwgGroupModel** ❌ (已移除)
- **原因**: 功能与 ProfessionTabModel + FileTypeModel 重复
- **替代方案**: 使用 ProfessionTabModel 进行专业分组，FileTypeModel 进行类型分组
- **迁移**: DwgManagerViewModel 需要重构使用新模型

## 🔄 ViewModel 使用建议

### 推荐使用
- **DwgManagerTabViewModel**: 新架构，功能完整，推荐用于生产环境
- **ListDragDropExampleViewModel**: 基础拖拽示例，适合学习参考

### 需要重构
- **DwgManagerViewModel**: 使用旧的DwgGroupModel，需要迁移到新架构

### 示例参考
- **TreeDragDropExampleViewModel**: 树形拖拽参考
- **FileDragExampleViewModel**: 文件拖拽参考
- **CrossControlDragExampleViewModel**: 跨控件拖拽参考

## 🚀 使用指南

### 1. DWG文件管理场景
```csharp
// 使用 DwgManagerTabViewModel
var viewModel = new DwgManagerTabViewModel();

// 专业管理
var profession = ProfessionTabModel.Create("建筑", "🏢", "建筑");

// 文件类型管理
var fileType = FileTypeModel.Create("出图", "📋", "S_");

// 文件模型
var dwgFile = DwgFileModel.FromFilePath("path/to/file.dwg");
```

### 2. 通用拖拽场景
```csharp
// 创建拖拽项
var dragItem = DragDropItem.Create("项目名称", "🔧", "Tool");

// 创建拖拽容器
var container = DragDropContainer.Create("工具箱", "🧰", "ToolBox", "Tool");

// 执行拖拽操作
var operation = DragDropOperation.Create(dragItem, sourceContainer, targetContainer);
```

### 3. 配置管理
```csharp
// 获取默认配置
var config = DragDropConfiguration.CreateDefault();

// 获取专业图标
var icon = config.GetProfessionIcon("建筑"); // 返回 "🏢"

// 获取文件类型图标
var typeIcon = config.GetFileTypeIcon(DwgFileType.Drawing); // 返回 "📋"
```

## 📈 模块完善程度

### ✅ 已完成 (90%)
- 核心模型定义
- 配置管理
- 服务接口
- 基础拖拽功能
- 文件分类逻辑

### 🔄 进行中 (10%)
- 服务层具体实现
- 高级拖拽行为
- 旧ViewModel重构

### 📋 待规划
- 单元测试覆盖
- 性能优化
- 国际化支持

## 🎯 下一步计划

1. **立即执行**: 完成 DwgManagerViewModel 重构
2. **短期目标**: 实现 IDwgFileService 具体实现
3. **中期目标**: 添加高级拖拽功能
4. **长期目标**: 完善测试和文档

## 💡 最佳实践

1. **模型选择**: 新项目使用 DwgManagerTabViewModel + 新模型架构
2. **拖拽实现**: 使用 DragDropModels 中的通用模型
3. **配置管理**: 通过 DragDropConfiguration 统一管理图标和行为
4. **服务调用**: 通过 IDwgFileService 接口进行文件操作

---

**更新时间**: 2025-01-22  
**版本**: v2.0  
**维护者**: DragDrop模块团队
