// DropDownButton 数据绑定 C# 示例
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;

namespace WPFTest.ViewModels.ButtonControls
{
    public partial class DropDownButtonPageViewModel : ObservableObject
    {
        #region 数据绑定属性

        /// <summary>
        /// 最近文件列表（数据绑定示例）
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<RecentFileItem> recentFiles = new();

        /// <summary>
        /// 导出格式选项（数据绑定示例）
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<ExportFormatItem> exportFormats = new();

        /// <summary>
        /// 工具选项（数据绑定示例）
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<ToolItem> toolItems = new();

        /// <summary>
        /// 当前选择的导出格式
        /// </summary>
        [ObservableProperty]
        private ExportFormatItem? selectedExportFormat;

        #endregion

        #region 数据绑定命令

        /// <summary>
        /// 添加最近文件命令
        /// </summary>
        [RelayCommand]
        private void AddRecentFile(string fileName)
        {
            var newFile = new RecentFileItem 
            { 
                Name = fileName, 
                Path = $@"C:\Documents\{fileName}",
                Icon = GetIconByExtension(fileName)
            };
            
            RecentFiles.Insert(0, newFile);
            
            // 限制最近文件数量
            while (RecentFiles.Count > 10)
            {
                RecentFiles.RemoveAt(RecentFiles.Count - 1);
            }
            
            StatusMessage = $"📁 添加了新的最近文件: {fileName}";
        }

        /// <summary>
        /// 清空最近文件列表命令
        /// </summary>
        [RelayCommand]
        private void ClearRecentFiles()
        {
            RecentFiles.Clear();
            StatusMessage = "🗑️ 已清空最近文件列表";
        }

        /// <summary>
        /// 处理菜单项选择命令
        /// </summary>
        [RelayCommand]
        private void HandleMenuItemSelection(object item)
        {
            string message = item switch
            {
                RecentFileItem file => $"📂 打开了文件: {file.Name}",
                ExportFormatItem format => $"📤 选择了导出格式: {format.Name}",
                ToolItem tool => $"🔧 启动了工具: {tool.Name}",
                _ => $"🎯 选择了项目: {item}"
            };
            
            StatusMessage = message;
            InteractionCount++;
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化数据绑定示例
        /// </summary>
        private void InitializeDataBindingExamples()
        {
            // 初始化最近文件列表
            RecentFiles = new ObservableCollection<RecentFileItem>
            {
                new RecentFileItem { Name = "项目文档.docx", Path = @"C:\Documents\项目文档.docx", Icon = "Document24" },
                new RecentFileItem { Name = "数据分析.xlsx", Path = @"C:\Documents\数据分析.xlsx", Icon = "Table24" },
                new RecentFileItem { Name = "设计稿.psd", Path = @"C:\Documents\设计稿.psd", Icon = "Image24" },
                new RecentFileItem { Name = "会议记录.txt", Path = @"C:\Documents\会议记录.txt", Icon = "DocumentText24" }
            };

            // 初始化导出格式选项
            ExportFormats = new ObservableCollection<ExportFormatItem>
            {
                new ExportFormatItem { Name = "PDF 文档", Extension = ".pdf", Icon = "Document24", Description = "便携式文档格式，适合分享和打印" },
                new ExportFormatItem { Name = "Word 文档", Extension = ".docx", Icon = "DocumentText24", Description = "Microsoft Word 格式，可编辑文档" },
                new ExportFormatItem { Name = "Excel 表格", Extension = ".xlsx", Icon = "Table24", Description = "Microsoft Excel 格式，数据分析" },
                new ExportFormatItem { Name = "PNG 图像", Extension = ".png", Icon = "Image24", Description = "高质量图像格式，支持透明" }
            };

            // 初始化工具选项
            ToolItems = new ObservableCollection<ToolItem>
            {
                new ToolItem { Name = "代码编辑器", Icon = "Code24", Category = "开发工具", Description = "高级代码编辑和语法高亮" },
                new ToolItem { Name = "调试器", Icon = "Bug24", Category = "开发工具", Description = "断点调试和变量监视" },
                new ToolItem { Name = "UI 设计器", Icon = "Design24", Category = "设计工具", Description = "可视化界面设计工具" },
                new ToolItem { Name = "颜色选择器", Icon = "Color24", Category = "设计工具", Description = "颜色选择和调色板工具" }
            };

            // 设置默认选择
            SelectedExportFormat = ExportFormats.FirstOrDefault();
        }

        /// <summary>
        /// 根据文件扩展名获取图标
        /// </summary>
        private string GetIconByExtension(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLower();
            return extension switch
            {
                ".txt" => "DocumentText24",
                ".docx" => "Document24",
                ".xlsx" => "Table24",
                ".png" or ".jpg" => "Image24",
                ".pdf" => "Document24",
                _ => "Document24"
            };
        }

        #endregion
    }

    #region 数据模型

    /// <summary>
    /// 最近文件项模型
    /// </summary>
    public class RecentFileItem
    {
        public string Name { get; set; } = string.Empty;
        public string Path { get; set; } = string.Empty;
        public string Icon { get; set; } = "Document24";
        public DateTime LastAccessed { get; set; } = DateTime.Now;
        public string DisplayTime => LastAccessed.ToString("yyyy-MM-dd HH:mm");
    }

    /// <summary>
    /// 导出格式项模型
    /// </summary>
    public class ExportFormatItem
    {
        public string Name { get; set; } = string.Empty;
        public string Extension { get; set; } = string.Empty;
        public string Icon { get; set; } = "Document24";
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// 工具项模型
    /// </summary>
    public class ToolItem
    {
        public string Name { get; set; } = string.Empty;
        public string Icon { get; set; } = "Wrench24";
        public string Category { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
    }

    #endregion
}
