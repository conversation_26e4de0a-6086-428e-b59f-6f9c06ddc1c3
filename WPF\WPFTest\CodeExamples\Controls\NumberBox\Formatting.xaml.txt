<!-- NumberBox 格式化功能示例 -->
<StackPanel Margin="20">

    <!-- 格式化选项 -->
    <GroupBox Header="格式化选项" Padding="15" Margin="0,0,0,15">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="16"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左列：格式化控制 -->
            <StackPanel Grid.Column="0">
                <TextBlock Text="格式化类型" FontWeight="Medium" Margin="0,0,0,4"/>
                <ComboBox ItemsSource="{Binding FormatOptions}"
                          SelectedItem="{Binding SelectedFormatItem}"
                          DisplayMemberPath="Name"
                          Margin="0,0,0,8"/>

                <ui:Button Content="应用格式"
                           Appearance="Primary"
                           HorizontalAlignment="Stretch"
                           Command="{Binding FormatNumberCommand}"/>
            </StackPanel>

            <!-- 右列：格式化示例 -->
            <StackPanel Grid.Column="2">
                <TextBlock Text="格式化示例" FontWeight="Medium" Margin="0,0,0,8"/>

                <!-- 货币格式 -->
                <StackPanel Margin="0,0,0,8">
                    <TextBlock Text="货币格式" FontSize="12" Margin="0,0,0,2"/>
                    <ui:NumberBox Value="{Binding CurrencyValue, UpdateSourceTrigger=PropertyChanged}"
                                  PlaceholderText="货币金额"
                                  Minimum="0"
                                  Maximum="999999"
                                  SmallChange="0.01"/>
                    <TextBlock Text="{Binding CurrencyValue, StringFormat='货币: ¥{0:F2}'}"
                               FontSize="11"
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                               Margin="0,2,0,0"/>
                </StackPanel>

                <!-- 百分比格式 -->
                <StackPanel Margin="0,0,0,8">
                    <TextBlock Text="百分比格式" FontSize="12" Margin="0,0,0,2"/>
                    <ui:NumberBox Value="{Binding PercentageValue, UpdateSourceTrigger=PropertyChanged}"
                                  PlaceholderText="百分比值"
                                  Minimum="0"
                                  Maximum="100"
                                  SmallChange="0.1"/>
                    <TextBlock Text="{Binding PercentageValue, StringFormat='百分比: {0:F1}%'}"
                               FontSize="11"
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                               Margin="0,2,0,0"/>
                </StackPanel>

                <!-- 科学计数法 -->
                <StackPanel>
                    <TextBlock Text="科学计数法" FontSize="12" Margin="0,0,0,2"/>
                    <ui:NumberBox Value="{Binding ScientificValue, UpdateSourceTrigger=PropertyChanged}"
                                  PlaceholderText="科学计数值"
                                  Minimum="0.001"
                                  Maximum="999999999"
                                  SmallChange="1000"/>
                    <TextBlock Text="{Binding ScientificValue, StringFormat='科学计数: {0:E2}'}"
                               FontSize="11"
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                               Margin="0,2,0,0"/>
                </StackPanel>
            </StackPanel>
        </Grid>
    </GroupBox>

</StackPanel>
                        <TextBlock Text="%" 
                                   HorizontalAlignment="Right" 
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"
                                   FontSize="12"
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                    </Grid>
                </StackPanel>
            </StackPanel>
            
            <!-- 右列：科学计数法和其他格式 -->
            <StackPanel Grid.Column="1" Margin="8,0,0,0">
                <!-- 科学计数法 -->
                <StackPanel>
                    <TextBlock Text="科学计数法" FontWeight="Bold" Margin="0,0,0,4"/>
                    <ui:NumberBox Value="123456789"
                                  PlaceholderText="1.23E+08"/>
                </StackPanel>
                
                <!-- 固定小数点 -->
                <StackPanel>
                    <TextBlock Text="固定小数点 (F4)" FontWeight="Bold" Margin="0,0,0,4"/>
                    <ui:NumberBox Value="3.14159"
                                  DecimalPlaces="4"
                                  PlaceholderText="3.1416"/>
                </StackPanel>
                
                <!-- 十六进制显示 -->
                <StackPanel>
                    <TextBlock Text="十六进制显示" FontWeight="Bold" Margin="0,0,0,4"/>
                    <ui:NumberBox Value="255"
                                  DecimalPlaces="0"
                                  PlaceholderText="FF (十六进制)"/>
                </StackPanel>
            </StackPanel>
        </Grid>
    </GroupBox>

    <!-- 单位和测量 -->
    <GroupBox Header="单位和测量" Padding="15">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- 左列：长度和重量 -->
            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                <!-- 长度 -->
                <StackPanel>
                    <TextBlock Text="长度 (米)" FontWeight="Bold" Margin="0,0,0,4"/>
                    <Grid>
                        <ui:NumberBox Value="1.75"
                                      Minimum="0"
                                      Maximum="10"
                                      DecimalPlaces="2"
                                      PlaceholderText="长度值"/>
                        <TextBlock Text="m" 
                                   HorizontalAlignment="Right" 
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"
                                   FontSize="12"
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                    </Grid>
                </StackPanel>
                
                <!-- 重量 -->
                <StackPanel>
                    <TextBlock Text="重量 (千克)" FontWeight="Bold" Margin="0,0,0,4"/>
                    <Grid>
                        <ui:NumberBox Value="68.5"
                                      Minimum="0"
                                      Maximum="500"
                                      DecimalPlaces="1"
                                      PlaceholderText="重量值"/>
                        <TextBlock Text="kg" 
                                   HorizontalAlignment="Right" 
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"
                                   FontSize="12"
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                    </Grid>
                </StackPanel>
            </StackPanel>
            
            <!-- 右列：温度和速度 -->
            <StackPanel Grid.Column="1" Margin="8,0,0,0">
                <!-- 温度 -->
                <StackPanel>
                    <TextBlock Text="温度 (摄氏度)" FontWeight="Bold" Margin="0,0,0,4"/>
                    <Grid>
                        <ui:NumberBox Value="25.5"
                                      Minimum="-50"
                                      Maximum="100"
                                      DecimalPlaces="1"
                                      PlaceholderText="温度值"/>
                        <TextBlock Text="°C" 
                                   HorizontalAlignment="Right" 
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"
                                   FontSize="12"
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                    </Grid>
                </StackPanel>
                
                <!-- 速度 -->
                <StackPanel>
                    <TextBlock Text="速度 (公里/小时)" FontWeight="Bold" Margin="0,0,0,4"/>
                    <Grid>
                        <ui:NumberBox Value="60"
                                      Minimum="0"
                                      Maximum="300"
                                      DecimalPlaces="0"
                                      PlaceholderText="速度值"/>
                        <TextBlock Text="km/h" 
                                   HorizontalAlignment="Right" 
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"
                                   FontSize="12"
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                    </Grid>
                </StackPanel>
            </StackPanel>
        </Grid>
    </GroupBox>

    <!-- 本地化格式 -->
    <GroupBox Header="本地化格式" Padding="15">
        <StackPanel>
            <TextBlock Text="不同地区的数字格式：" FontWeight="Bold" Margin="0,0,0,8"/>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <!-- 中文格式 -->
                <StackPanel Grid.Column="0" Margin="0,0,8,0">
                    <TextBlock Text="中文格式" FontSize="12" Margin="0,0,0,2"/>
                    <ui:NumberBox Value="12345.67"
                                  PlaceholderText="12,345.67"/>
                    <TextBlock Text="示例：12,345.67" 
                               FontSize="10" 
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                               Margin="0,2,0,0"/>
                </StackPanel>
                
                <!-- 欧洲格式 -->
                <StackPanel Grid.Column="1" Margin="4,0,4,0">
                    <TextBlock Text="欧洲格式" FontSize="12" Margin="0,0,0,2"/>
                    <ui:NumberBox Value="12345.67"
                                  PlaceholderText="12.345,67"/>
                    <TextBlock Text="示例：12.345,67" 
                               FontSize="10" 
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                               Margin="0,2,0,0"/>
                </StackPanel>
                
                <!-- 印度格式 -->
                <StackPanel Grid.Column="2" Margin="8,0,0,0">
                    <TextBlock Text="印度格式" FontSize="12" Margin="0,0,0,2"/>
                    <ui:NumberBox Value="12345.67"
                                  PlaceholderText="12,345.67"/>
                    <TextBlock Text="示例：12,345.67" 
                               FontSize="10" 
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                               Margin="0,2,0,0"/>
                </StackPanel>
            </Grid>
        </StackPanel>
    </GroupBox>

    <!-- 自定义格式化 -->
    <GroupBox Header="自定义格式化" Padding="15">
        <StackPanel>
            <TextBlock Text="自定义数字格式化：" FontWeight="Bold" Margin="0,0,0,8"/>
            
            <!-- 格式化选择器 -->
            <Grid Margin="0,0,0,8">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="格式:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                <ComboBox Grid.Column="1" 
                          SelectedIndex="0"
                          Margin="0,0,8,0">
                    <ComboBoxItem Content="标准数字 (N2)"/>
                    <ComboBoxItem Content="货币 (C2)"/>
                    <ComboBoxItem Content="百分比 (P2)"/>
                    <ComboBoxItem Content="科学计数法 (E2)"/>
                    <ComboBoxItem Content="固定小数点 (F2)"/>
                </ComboBox>
                <ui:Button Grid.Column="2" Content="应用" Appearance="Primary"/>
            </Grid>
            
            <!-- 格式化结果显示 -->
            <Border Background="{DynamicResource ControlFillColorTertiaryBrush}"
                    CornerRadius="4"
                    Padding="8">
                <StackPanel>
                    <TextBlock Text="格式化结果：" FontSize="12" FontWeight="Bold" Margin="0,0,0,4"/>
                    <TextBlock Text="原始值: 1234.5678" FontSize="11" Margin="0,0,0,2"/>
                    <TextBlock Text="格式化后: 1,234.57" FontSize="11" FontFamily="Consolas"/>
                </StackPanel>
            </Border>
        </StackPanel>
    </GroupBox>

</StackPanel>
