using System.Collections.ObjectModel;
using System.IO;
using System.Windows;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using GongSolutions.Wpf.DragDrop;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels.DragDrop
{
    /// <summary>
    /// 树形拖拽示例 ViewModel
    /// 展示 gong-wpf-dragdrop 库在 TreeView 控件中的应用
    /// 
    /// 核心功能：
    /// 1. 实现 IDragSource 接口 - 控制树节点拖拽行为
    /// 2. 实现 IDropTarget 接口 - 控制树节点放置行为
    /// 3. 支持父子节点间的拖拽操作
    /// 4. 支持跨分支的节点移动
    /// 5. 提供层级结构的验证和维护
    /// 6. 展示树形结构拖拽的最佳实践
    /// </summary>
    public partial class TreeDragDropExampleViewModel : ObservableObject, IDragSource, IDropTarget
    {
        #region 字段

        /// <summary>
        /// 日志记录器 - 用于记录树形拖拽操作的详细信息
        /// </summary>
        private readonly YLoggerInstance _logger = YLogger.ForSilent<TreeDragDropExampleViewModel>();

        #endregion

        #region 属性

        /// <summary>
        /// 状态消息 - 显示当前操作状态和结果
        /// </summary>
        [ObservableProperty]
        private string statusMessage = "欢迎使用树形拖拽示例！";

        /// <summary>
        /// 交互次数 - 统计用户的拖拽操作次数
        /// </summary>
        [ObservableProperty]
        private int interactionCount = 0;

        /// <summary>
        /// 最后操作 - 记录最近一次执行的操作
        /// </summary>
        [ObservableProperty]
        private string lastAction = "无";

        /// <summary>
        /// 树形数据根节点集合
        /// 包含整个树形结构的根级节点
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<TreeNodeItem> treeNodes = new();

        /// <summary>
        /// 选中的树节点 - 当前被选中的节点
        /// </summary>
        [ObservableProperty]
        private TreeNodeItem? selectedTreeNode;

        /// <summary>
        /// 展开的节点数量 - 统计当前展开的节点数
        /// </summary>
        [ObservableProperty]
        private int expandedNodesCount = 0;

        /// <summary>
        /// 总节点数量 - 统计树中所有节点的数量
        /// </summary>
        [ObservableProperty]
        private int totalNodesCount = 0;

        #region 代码示例属性

        /// <summary>
        /// 基础功能 XAML 示例代码
        /// </summary>
        [ObservableProperty]
        private string basicXamlExample = string.Empty;

        /// <summary>
        /// 基础功能 C# 示例代码
        /// </summary>
        [ObservableProperty]
        private string basicCSharpExample = string.Empty;

        /// <summary>
        /// 高级功能 XAML 示例代码
        /// </summary>
        [ObservableProperty]
        private string advancedXamlExample = string.Empty;

        /// <summary>
        /// 高级功能 C# 示例代码
        /// </summary>
        [ObservableProperty]
        private string advancedCSharpExample = string.Empty;

        #endregion

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// 初始化 ViewModel 并加载示例数据和代码示例
        /// </summary>
        public TreeDragDropExampleViewModel()
        {
            _logger.Info("🌳 TreeDragDropExampleViewModel 初始化开始");
            
            // 初始化树形数据
            InitializeTreeData();
            
            // 加载代码示例
            LoadCodeExamples();
            
            // 更新统计信息
            UpdateStatistics();
            
            _logger.Info("✅ TreeDragDropExampleViewModel 初始化完成");
        }

        #endregion

        #region 命令

        /// <summary>
        /// 重置数据命令 - 恢复到初始状态
        /// </summary>
        [RelayCommand]
        private void ResetData()
        {
            InitializeTreeData();
            UpdateStatistics();
            InteractionCount++;
            LastAction = "重置数据";
            StatusMessage = "🔄 树形数据已重置";
            _logger.Info("重置树形数据");
        }

        /// <summary>
        /// 展开所有节点命令
        /// </summary>
        [RelayCommand]
        private void ExpandAll()
        {
            ExpandAllNodes(TreeNodes);
            UpdateStatistics();
            InteractionCount++;
            LastAction = "展开所有节点";
            StatusMessage = "📂 所有节点已展开";
            _logger.Info("展开所有树节点");
        }

        /// <summary>
        /// 折叠所有节点命令
        /// </summary>
        [RelayCommand]
        private void CollapseAll()
        {
            CollapseAllNodes(TreeNodes);
            UpdateStatistics();
            InteractionCount++;
            LastAction = "折叠所有节点";
            StatusMessage = "📁 所有节点已折叠";
            _logger.Info("折叠所有树节点");
        }

        /// <summary>
        /// 添加根节点命令
        /// </summary>
        [RelayCommand]
        private void AddRootNode()
        {
            var newNode = new TreeNodeItem
            {
                Id = GenerateNodeId(),
                Name = $"新根节点 {TreeNodes.Count + 1}",
                Description = "这是一个新添加的根节点",
                NodeType = TreeNodeType.Folder,
                IsExpanded = true,
                Level = 0
            };
            
            TreeNodes.Add(newNode);
            UpdateStatistics();
            InteractionCount++;
            LastAction = "添加根节点";
            StatusMessage = $"➕ 添加了新的根节点: {newNode.Name}";
            _logger.Info($"添加根节点: {newNode.Name}");
        }

        /// <summary>
        /// 删除选中节点命令
        /// </summary>
        [RelayCommand]
        private void DeleteSelectedNode()
        {
            if (SelectedTreeNode == null)
            {
                StatusMessage = "⚠️ 请先选择要删除的节点";
                return;
            }

            var nodeName = SelectedTreeNode.Name;
            RemoveNodeFromTree(SelectedTreeNode);
            SelectedTreeNode = null;
            
            UpdateStatistics();
            InteractionCount++;
            LastAction = "删除节点";
            StatusMessage = $"🗑️ 已删除节点: {nodeName}";
            _logger.Info($"删除节点: {nodeName}");
        }

        /// <summary>
        /// 重置计数命令
        /// </summary>
        [RelayCommand]
        private void ResetCount()
        {
            InteractionCount = 0;
            LastAction = "重置计数";
            StatusMessage = "🔄 计数已重置";
            _logger.Info("重置交互计数");
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 生成唯一的节点ID
        /// </summary>
        /// <returns>唯一的节点ID</returns>
        private string GenerateNodeId()
        {
            return Guid.NewGuid().ToString("N")[..8].ToUpper();
        }

        /// <summary>
        /// 展开所有节点
        /// </summary>
        /// <param name="nodes">要展开的节点集合</param>
        private void ExpandAllNodes(ObservableCollection<TreeNodeItem> nodes)
        {
            foreach (var node in nodes)
            {
                node.IsExpanded = true;
                if (node.Children.Count > 0)
                {
                    ExpandAllNodes(node.Children);
                }
            }
        }

        /// <summary>
        /// 折叠所有节点
        /// </summary>
        /// <param name="nodes">要折叠的节点集合</param>
        private void CollapseAllNodes(ObservableCollection<TreeNodeItem> nodes)
        {
            foreach (var node in nodes)
            {
                node.IsExpanded = false;
                if (node.Children.Count > 0)
                {
                    CollapseAllNodes(node.Children);
                }
            }
        }

        /// <summary>
        /// 更新统计信息
        /// </summary>
        private void UpdateStatistics()
        {
            TotalNodesCount = CountAllNodes(TreeNodes);
            ExpandedNodesCount = CountExpandedNodes(TreeNodes);
        }

        /// <summary>
        /// 统计所有节点数量
        /// </summary>
        /// <param name="nodes">节点集合</param>
        /// <returns>节点总数</returns>
        private int CountAllNodes(ObservableCollection<TreeNodeItem> nodes)
        {
            int count = nodes.Count;
            foreach (var node in nodes)
            {
                count += CountAllNodes(node.Children);
            }
            return count;
        }

        /// <summary>
        /// 统计展开的节点数量
        /// </summary>
        /// <param name="nodes">节点集合</param>
        /// <returns>展开的节点数量</returns>
        private int CountExpandedNodes(ObservableCollection<TreeNodeItem> nodes)
        {
            int count = 0;
            foreach (var node in nodes)
            {
                if (node.IsExpanded) count++;
                count += CountExpandedNodes(node.Children);
            }
            return count;
        }

        /// <summary>
        /// 从树中移除节点
        /// </summary>
        /// <param name="nodeToRemove">要移除的节点</param>
        private void RemoveNodeFromTree(TreeNodeItem nodeToRemove)
        {
            // 从根节点中查找并移除
            if (TreeNodes.Contains(nodeToRemove))
            {
                TreeNodes.Remove(nodeToRemove);
                return;
            }

            // 递归查找并移除
            RemoveNodeFromCollection(TreeNodes, nodeToRemove);
        }

        /// <summary>
        /// 从节点集合中递归移除指定节点
        /// </summary>
        /// <param name="nodes">节点集合</param>
        /// <param name="nodeToRemove">要移除的节点</param>
        /// <returns>是否找到并移除了节点</returns>
        private bool RemoveNodeFromCollection(ObservableCollection<TreeNodeItem> nodes, TreeNodeItem nodeToRemove)
        {
            foreach (var node in nodes)
            {
                if (node.Children.Contains(nodeToRemove))
                {
                    node.Children.Remove(nodeToRemove);
                    return true;
                }

                if (RemoveNodeFromCollection(node.Children, nodeToRemove))
                {
                    return true;
                }
            }
            return false;
        }

        #endregion

        #region IDragSource 实现

        /// <summary>
        /// 开始拖拽操作
        /// 当用户开始拖拽树节点时调用
        /// </summary>
        /// <param name="dragInfo">拖拽信息</param>
        public void StartDrag(IDragInfo dragInfo)
        {
            try
            {
                var sourceNode = dragInfo.SourceItem as TreeNodeItem;
                if (sourceNode != null)
                {
                    dragInfo.Data = sourceNode;
                    dragInfo.Effects = DragDropEffects.Move;

                    InteractionCount++;
                    LastAction = $"开始拖拽节点: {sourceNode.Name}";
                    StatusMessage = $"🎯 开始拖拽节点: {sourceNode.Name}";
                    _logger.Info($"开始拖拽树节点: {sourceNode.Name} (ID: {sourceNode.Id})");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"开始拖拽失败: {ex.Message}");
                StatusMessage = $"❌ 拖拽失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 判断是否可以开始拖拽
        /// </summary>
        /// <param name="dragInfo">拖拽信息</param>
        /// <returns>是否可以拖拽</returns>
        public bool CanStartDrag(IDragInfo dragInfo)
        {
            var node = dragInfo.SourceItem as TreeNodeItem;
            if (node == null) return false;

            // 可以添加业务规则，例如：
            // - 根节点不能拖拽
            // - 特定类型的节点不能拖拽
            // - 锁定的节点不能拖拽

            _logger.Debug($"拖拽权限检查: {node.Name} - 允许拖拽");
            return true;
        }

        /// <summary>
        /// 拖拽完成后的处理
        /// </summary>
        /// <param name="dropInfo">放置信息</param>
        public void Dropped(IDropInfo dropInfo)
        {
            try
            {
                var sourceNode = dropInfo.Data as TreeNodeItem;
                if (sourceNode != null)
                {
                    InteractionCount++;
                    LastAction = $"拖拽完成: {sourceNode.Name}";
                    StatusMessage = $"✅ 拖拽完成: {sourceNode.Name}";
                    _logger.Info($"拖拽完成: {sourceNode.Name}");

                    UpdateStatistics();
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"拖拽完成处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 拖拽操作完成
        /// </summary>
        /// <param name="operationResult">操作结果</param>
        /// <param name="dragInfo">拖拽信息</param>
        public void DragDropOperationFinished(DragDropEffects operationResult, IDragInfo dragInfo)
        {
            try
            {
                var sourceNode = dragInfo.Data as TreeNodeItem;
                if (sourceNode != null)
                {
                    _logger.Info($"树节点拖拽操作完成: {sourceNode.Name}, 结果: {operationResult}");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"拖拽操作完成处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理拖拽过程中的异常
        /// </summary>
        /// <param name="exception">异常信息</param>
        /// <returns>是否已处理异常</returns>
        public bool TryCatchOccurredException(Exception exception)
        {
            _logger.Error($"树形拖拽过程中发生异常: {exception.Message}");
            StatusMessage = $"❌ 拖拽异常: {exception.Message}";

            InteractionCount++;
            LastAction = "拖拽异常";

            return true;
        }

        /// <summary>
        /// 拖拽被取消
        /// </summary>
        public void DragCancelled()
        {
            InteractionCount++;
            LastAction = "拖拽被取消";
            StatusMessage = "⚠️ 拖拽操作被取消";
            _logger.Info("树形拖拽操作被取消");
        }

        #endregion

        #region IDropTarget 实现

        /// <summary>
        /// 拖拽悬停时的处理
        /// 验证是否可以在目标位置放置节点
        /// </summary>
        /// <param name="dropInfo">放置信息</param>
        public void DragOver(IDropInfo dropInfo)
        {
            try
            {
                var sourceNode = dropInfo.Data as TreeNodeItem;
                var targetNode = dropInfo.TargetItem as TreeNodeItem;

                if (sourceNode == null)
                {
                    dropInfo.Effects = DragDropEffects.None;
                    return;
                }

                // 验证拖拽规则
                if (IsValidTreeDrop(sourceNode, targetNode))
                {
                    dropInfo.DropTargetAdorner = DropTargetAdorners.Highlight;
                    dropInfo.Effects = DragDropEffects.Move;
                }
                else
                {
                    dropInfo.Effects = DragDropEffects.None;
                }
            }
            catch (Exception ex)
            {
                dropInfo.Effects = DragDropEffects.None;
                _logger.Error($"树形拖拽悬停处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 执行放置操作
        /// </summary>
        /// <param name="dropInfo">放置信息</param>
        public void Drop(IDropInfo dropInfo)
        {
            try
            {
                var sourceNode = dropInfo.Data as TreeNodeItem;
                var targetNode = dropInfo.TargetItem as TreeNodeItem;

                if (sourceNode == null)
                {
                    _logger.Warning("放置操作失败：源节点无效");
                    StatusMessage = "⚠️ 放置失败：源节点无效";
                    return;
                }

                if (!IsValidTreeDrop(sourceNode, targetNode))
                {
                    _logger.Warning("放置操作失败：不符合树形结构规则");
                    StatusMessage = "⚠️ 放置失败：不符合树形结构规则";
                    return;
                }

                // 执行节点移动
                MoveTreeNode(sourceNode, targetNode);

                InteractionCount++;
                LastAction = $"移动节点: {sourceNode.Name}";
                StatusMessage = $"✅ 节点 '{sourceNode.Name}' 移动完成";
                _logger.Info($"树节点移动完成: {sourceNode.Name} -> {targetNode?.Name ?? "根级"}");

                UpdateStatistics();
            }
            catch (Exception ex)
            {
                _logger.Error($"树形放置操作失败: {ex.Message}");
                StatusMessage = $"❌ 放置失败: {ex.Message}";
            }
        }

        #endregion

        #region 树形拖拽逻辑

        /// <summary>
        /// 验证树形拖拽是否有效
        /// </summary>
        /// <param name="sourceNode">源节点</param>
        /// <param name="targetNode">目标节点</param>
        /// <returns>是否有效</returns>
        private bool IsValidTreeDrop(TreeNodeItem sourceNode, TreeNodeItem? targetNode)
        {
            // 1. 不能拖拽到自己身上
            if (sourceNode == targetNode) return false;

            // 2. 不能拖拽到自己的子节点上（避免循环引用）
            if (targetNode != null && IsDescendantOf(targetNode, sourceNode)) return false;

            // 3. 可以添加更多业务规则
            // 例如：只允许特定类型的节点作为父节点
            // if (targetNode != null && targetNode.NodeType != TreeNodeType.Folder) return false;

            return true;
        }

        /// <summary>
        /// 检查节点是否是另一个节点的后代
        /// </summary>
        /// <param name="node">要检查的节点</param>
        /// <param name="potentialAncestor">潜在的祖先节点</param>
        /// <returns>是否是后代</returns>
        private bool IsDescendantOf(TreeNodeItem node, TreeNodeItem potentialAncestor)
        {
            return IsDescendantOfRecursive(node, potentialAncestor, potentialAncestor.Children);
        }

        /// <summary>
        /// 递归检查后代关系
        /// </summary>
        private bool IsDescendantOfRecursive(TreeNodeItem node, TreeNodeItem ancestor, ObservableCollection<TreeNodeItem> children)
        {
            foreach (var child in children)
            {
                if (child == node) return true;
                if (IsDescendantOfRecursive(node, ancestor, child.Children)) return true;
            }
            return false;
        }

        /// <summary>
        /// 移动树节点
        /// </summary>
        /// <param name="sourceNode">源节点</param>
        /// <param name="targetNode">目标节点（null表示移动到根级）</param>
        private void MoveTreeNode(TreeNodeItem sourceNode, TreeNodeItem? targetNode)
        {
            // 1. 从原位置移除节点
            RemoveNodeFromTree(sourceNode);

            // 2. 添加到新位置
            if (targetNode == null)
            {
                // 移动到根级
                sourceNode.Level = 0;
                TreeNodes.Add(sourceNode);
            }
            else
            {
                // 移动到目标节点下
                sourceNode.Level = targetNode.Level + 1;
                targetNode.Children.Add(sourceNode);
                targetNode.IsExpanded = true; // 展开目标节点以显示新添加的子节点
            }

            // 3. 更新子节点的层级
            UpdateChildrenLevels(sourceNode);
        }

        /// <summary>
        /// 更新子节点的层级
        /// </summary>
        /// <param name="parentNode">父节点</param>
        private void UpdateChildrenLevels(TreeNodeItem parentNode)
        {
            foreach (var child in parentNode.Children)
            {
                child.Level = parentNode.Level + 1;
                UpdateChildrenLevels(child);
            }
        }

        #endregion

        #region 数据初始化

        /// <summary>
        /// 初始化树形数据
        /// 创建示例的树形结构用于演示拖拽功能
        /// </summary>
        private void InitializeTreeData()
        {
            TreeNodes.Clear();

            // 创建根节点1：项目管理
            var projectRoot = new TreeNodeItem
            {
                Id = "ROOT001",
                Name = "项目管理",
                Description = "项目管理根节点",
                NodeType = TreeNodeType.Folder,
                IsExpanded = true,
                Level = 0
            };

            // 添加项目子节点
            projectRoot.Children.Add(new TreeNodeItem
            {
                Id = "PROJ001",
                Name = "WPF 应用开发",
                Description = "WPF 桌面应用程序开发项目",
                NodeType = TreeNodeType.Project,
                IsExpanded = true,
                Level = 1
            });

            projectRoot.Children.Add(new TreeNodeItem
            {
                Id = "PROJ002",
                Name = "Web API 开发",
                Description = "RESTful Web API 开发项目",
                NodeType = TreeNodeType.Project,
                IsExpanded = false,
                Level = 1
            });

            // 为 WPF 项目添加子任务
            var wpfProject = projectRoot.Children.First(x => x.Id == "PROJ001");
            wpfProject.Children.Add(new TreeNodeItem
            {
                Id = "TASK001",
                Name = "UI 设计",
                Description = "用户界面设计任务",
                NodeType = TreeNodeType.Task,
                IsExpanded = false,
                Level = 2
            });

            wpfProject.Children.Add(new TreeNodeItem
            {
                Id = "TASK002",
                Name = "业务逻辑开发",
                Description = "核心业务逻辑实现",
                NodeType = TreeNodeType.Task,
                IsExpanded = false,
                Level = 2
            });

            wpfProject.Children.Add(new TreeNodeItem
            {
                Id = "TASK003",
                Name = "测试用例编写",
                Description = "单元测试和集成测试",
                NodeType = TreeNodeType.Task,
                IsExpanded = false,
                Level = 2
            });

            TreeNodes.Add(projectRoot);

            // 创建根节点2：文档管理
            var docRoot = new TreeNodeItem
            {
                Id = "ROOT002",
                Name = "文档管理",
                Description = "文档管理根节点",
                NodeType = TreeNodeType.Folder,
                IsExpanded = true,
                Level = 0
            };

            docRoot.Children.Add(new TreeNodeItem
            {
                Id = "DOC001",
                Name = "技术文档",
                Description = "技术相关文档",
                NodeType = TreeNodeType.Folder,
                IsExpanded = false,
                Level = 1
            });

            docRoot.Children.Add(new TreeNodeItem
            {
                Id = "DOC002",
                Name = "用户手册",
                Description = "用户操作手册",
                NodeType = TreeNodeType.Document,
                IsExpanded = false,
                Level = 1
            });

            docRoot.Children.Add(new TreeNodeItem
            {
                Id = "DOC003",
                Name = "API 文档",
                Description = "接口文档说明",
                NodeType = TreeNodeType.Document,
                IsExpanded = false,
                Level = 1
            });

            TreeNodes.Add(docRoot);

            // 创建根节点3：资源库
            var resourceRoot = new TreeNodeItem
            {
                Id = "ROOT003",
                Name = "资源库",
                Description = "资源文件管理",
                NodeType = TreeNodeType.Folder,
                IsExpanded = false,
                Level = 0
            };

            resourceRoot.Children.Add(new TreeNodeItem
            {
                Id = "RES001",
                Name = "图片资源",
                Description = "图片和图标资源",
                NodeType = TreeNodeType.Folder,
                IsExpanded = false,
                Level = 1
            });

            resourceRoot.Children.Add(new TreeNodeItem
            {
                Id = "RES002",
                Name = "样式文件",
                Description = "CSS 和 XAML 样式",
                NodeType = TreeNodeType.File,
                IsExpanded = false,
                Level = 1
            });

            TreeNodes.Add(resourceRoot);

            _logger.Info($"树形数据初始化完成 - 根节点: {TreeNodes.Count}");
        }

        /// <summary>
        /// 加载代码示例
        /// </summary>
        private void LoadCodeExamples()
        {
            try
            {
                var baseDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "CodeExamples", "DragDrop", "TreeDragDrop");

                // 设置默认示例（如果文件不存在）
                BasicXamlExample = @"<!-- TreeView 拖拽基础示例 -->
<TreeView ItemsSource=""{Binding TreeNodes}""
          dd:DragDrop.IsDragSource=""True""
          dd:DragDrop.IsDropTarget=""True""
          dd:DragDrop.DragHandler=""{Binding}""
          dd:DragDrop.DropHandler=""{Binding}"">
    <TreeView.ItemTemplate>
        <HierarchicalDataTemplate ItemsSource=""{Binding Children}"">
            <StackPanel Orientation=""Horizontal"">
                <TextBlock Text=""{Binding Name}"" />
            </StackPanel>
        </HierarchicalDataTemplate>
    </TreeView.ItemTemplate>
</TreeView>";

                BasicCSharpExample = @"// TreeView 拖拽基础 C# 示例
public class TreeDragDropViewModel : ObservableObject, IDragSource, IDropTarget
{
    public ObservableCollection<TreeNodeItem> TreeNodes { get; set; }

    public void StartDrag(IDragInfo dragInfo)
    {
        dragInfo.Data = dragInfo.SourceItem;
        dragInfo.Effects = DragDropEffects.Move;
    }

    public void Drop(IDropInfo dropInfo)
    {
        var sourceNode = dropInfo.Data as TreeNodeItem;
        var targetNode = dropInfo.TargetItem as TreeNodeItem;
        // 实现节点移动逻辑
    }
}";

                _logger.Info("代码示例加载完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"加载代码示例失败: {ex.Message}");
            }
        }

        #endregion
    }

    /// <summary>
    /// 树节点类型枚举
    /// </summary>
    public enum TreeNodeType
    {
        /// <summary>文件夹</summary>
        Folder,
        /// <summary>项目</summary>
        Project,
        /// <summary>任务</summary>
        Task,
        /// <summary>文档</summary>
        Document,
        /// <summary>文件</summary>
        File
    }

    /// <summary>
    /// 树节点数据模型
    /// 表示树形结构中的一个节点
    /// </summary>
    public partial class TreeNodeItem : ObservableObject
    {
        /// <summary>
        /// 节点唯一标识符
        /// </summary>
        [ObservableProperty]
        private string id = string.Empty;

        /// <summary>
        /// 节点显示名称
        /// </summary>
        [ObservableProperty]
        private string name = string.Empty;

        /// <summary>
        /// 节点描述
        /// </summary>
        [ObservableProperty]
        private string description = string.Empty;

        /// <summary>
        /// 节点类型
        /// </summary>
        [ObservableProperty]
        private TreeNodeType nodeType = TreeNodeType.Folder;

        /// <summary>
        /// 是否展开
        /// </summary>
        [ObservableProperty]
        private bool isExpanded = false;

        /// <summary>
        /// 是否被选中
        /// </summary>
        [ObservableProperty]
        private bool isSelected = false;

        /// <summary>
        /// 节点层级（0为根节点）
        /// </summary>
        [ObservableProperty]
        private int level = 0;

        /// <summary>
        /// 子节点集合
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<TreeNodeItem> children = new();

        /// <summary>
        /// 重写ToString方法，便于调试
        /// </summary>
        /// <returns>节点的字符串表示</returns>
        public override string ToString()
        {
            return $"{Name} (ID: {Id}, Type: {NodeType}, Level: {Level})";
        }
    }
}
