// ListView 自定义样式 C# 代码示例
// 展示如何在代码中动态应用和管理自定义样式

using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

/// <summary>
/// 自定义样式 ListView ViewModel
/// </summary>
public partial class CustomStylesListViewViewModel : ObservableObject
{
    /// <summary>
    /// 数据项集合
    /// </summary>
    [ObservableProperty]
    private ObservableCollection<StyleDataItem> dataItems = new();

    /// <summary>
    /// 当前选择的样式
    /// </summary>
    [ObservableProperty]
    private string currentStyle = "ModernListViewStyle";

    /// <summary>
    /// 可用样式列表
    /// </summary>
    [ObservableProperty]
    private ObservableCollection<string> availableStyles = new();

    /// <summary>
    /// 构造函数
    /// </summary>
    public CustomStylesListViewViewModel()
    {
        InitializeData();
        InitializeStyles();
    }

    /// <summary>
    /// 初始化数据
    /// </summary>
    private void InitializeData()
    {
        DataItems.Clear();
        var sampleData = new[]
        {
            new StyleDataItem { Name = "现代化设计", Description = "采用现代化设计理念", Icon = "Design24", Category = "设计" },
            new StyleDataItem { Name = "卡片布局", Description = "使用卡片式布局展示", Icon = "Card24", Category = "布局" },
            new StyleDataItem { Name = "网格视图", Description = "网格式排列显示", Icon = "Grid24", Category = "视图" },
            new StyleDataItem { Name = "列表模式", Description = "传统列表显示模式", Icon = "List24", Category = "视图" }
        };

        foreach (var item in sampleData)
        {
            DataItems.Add(item);
        }
    }

    /// <summary>
    /// 初始化样式列表
    /// </summary>
    private void InitializeStyles()
    {
        AvailableStyles.Clear();
        AvailableStyles.Add("ModernListViewStyle");
        AvailableStyles.Add("CardListViewStyle");
        AvailableStyles.Add("GridListViewStyle");
        AvailableStyles.Add("ListViewStyle");
        AvailableStyles.Add("TransparentListViewStyle");
    }

    /// <summary>
    /// 切换样式命令
    /// </summary>
    [RelayCommand]
    private void SwitchStyle(string styleName)
    {
        CurrentStyle = styleName;
        Console.WriteLine($"切换到样式: {styleName}");
    }

    /// <summary>
    /// 动态应用样式到 ListView
    /// </summary>
    /// <param name="listView">目标 ListView</param>
    /// <param name="styleName">样式名称</param>
    public void ApplyStyleToListView(ListView listView, string styleName)
    {
        try
        {
            // 从资源中查找样式
            var style = Application.Current.FindResource(styleName) as Style;
            if (style != null)
            {
                listView.Style = style;
                Console.WriteLine($"成功应用样式: {styleName}");
            }
            else
            {
                Console.WriteLine($"未找到样式: {styleName}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"应用样式失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 创建自定义样式
    /// </summary>
    /// <returns>自定义样式</returns>
    public Style CreateCustomStyle()
    {
        var style = new Style(typeof(ListView));
        
        // 设置基础属性
        style.Setters.Add(new Setter(ListView.BackgroundProperty, 
            Application.Current.FindResource("CardBackgroundFillColorDefaultBrush")));
        style.Setters.Add(new Setter(ListView.BorderBrushProperty, 
            Application.Current.FindResource("AccentFillColorDefaultBrush")));
        style.Setters.Add(new Setter(ListView.BorderThicknessProperty, new Thickness(2)));
        style.Setters.Add(new Setter(ListView.PaddingProperty, new Thickness(12)));
        
        return style;
    }

    /// <summary>
    /// 根据条件选择样式
    /// </summary>
    /// <param name="itemCount">项目数量</param>
    /// <param name="displayMode">显示模式</param>
    /// <returns>推荐的样式名称</returns>
    public string GetRecommendedStyle(int itemCount, string displayMode)
    {
        return displayMode switch
        {
            "Compact" when itemCount > 20 => "CompactListViewStyle",
            "Card" => "CardListViewStyle",
            "Grid" => "GridListViewStyle",
            "Modern" => "ModernListViewStyle",
            _ => "ListViewStyle"
        };
    }

    /// <summary>
    /// 样式性能优化
    /// </summary>
    /// <param name="listView">目标 ListView</param>
    public void OptimizeListViewPerformance(ListView listView)
    {
        // 启用虚拟化
        VirtualizingPanel.SetIsVirtualizing(listView, true);
        VirtualizingPanel.SetVirtualizationMode(listView, VirtualizationMode.Recycling);
        
        // 设置滚动模式
        ScrollViewer.SetCanContentScroll(listView, true);
        
        Console.WriteLine("ListView 性能优化已应用");
    }
}

/// <summary>
/// 样式数据项模型
/// </summary>
public partial class StyleDataItem : ObservableObject
{
    [ObservableProperty]
    private string name = string.Empty;

    [ObservableProperty]
    private string description = string.Empty;

    [ObservableProperty]
    private string icon = "Document24";

    [ObservableProperty]
    private string category = "默认";

    [ObservableProperty]
    private bool isHighlighted = false;
}

/*
自定义样式开发要点：

1. 样式继承：
   - 基于现有样式扩展
   - 保持一致的设计语言
   - 避免重复定义

2. 动态样式应用：
   - 运行时切换样式
   - 根据条件选择样式
   - 样式资源管理

3. 性能优化：
   - 启用虚拟化
   - 合理使用模板
   - 避免复杂的绑定

4. 响应式设计：
   - 适配不同屏幕尺寸
   - 支持主题切换
   - 考虑可访问性

5. 最佳实践：
   - 样式命名规范
   - 资源组织结构
   - 文档和注释
*/
