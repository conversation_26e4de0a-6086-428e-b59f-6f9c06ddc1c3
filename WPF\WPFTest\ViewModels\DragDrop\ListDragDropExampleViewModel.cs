using System.Collections.ObjectModel;
using System.IO;
using System.Windows;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using GongSolutions.Wpf.DragDrop;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels.DragDrop
{
    /// <summary>
    /// 列表拖拽示例 ViewModel
    /// 展示 gong-wpf-dragdrop 库在列表控件中的应用
    /// 
    /// 核心功能：
    /// 1. 实现 IDragSource 接口 - 控制拖拽行为
    /// 2. 实现 IDropTarget 接口 - 控制放置行为
    /// 3. 支持多列表间的拖拽操作
    /// 4. 提供详细的日志记录和状态反馈
    /// 5. 展示各种拖拽场景和最佳实践
    /// </summary>
    public partial class ListDragDropExampleViewModel : ObservableObject, IDragSource, IDropTarget
    {
        #region 字段

        /// <summary>
        /// 日志记录器 - 用于记录拖拽操作的详细信息
        /// </summary>
        private readonly YLoggerInstance _logger = YLogger.ForSilent<ListDragDropExampleViewModel>();

        #endregion

        #region 属性

        /// <summary>
        /// 状态消息 - 显示当前操作状态和结果
        /// </summary>
        [ObservableProperty]
        private string statusMessage = "欢迎使用列表拖拽示例！";

        /// <summary>
        /// 交互次数 - 统计用户的拖拽操作次数
        /// </summary>
        [ObservableProperty]
        private int interactionCount = 0;

        /// <summary>
        /// 最后操作 - 记录最近一次执行的操作
        /// </summary>
        [ObservableProperty]
        private string lastAction = "无";

        /// <summary>
        /// 源列表 - 可拖拽的项目集合
        /// 这是拖拽操作的起始点，用户可以从这里拖拽项目到其他列表
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<DragDropItem> sourceItems = new();

        /// <summary>
        /// 目标列表 - 可接收拖拽的项目集合
        /// 这是拖拽操作的目标之一，可以接收从源列表或其他列表拖拽过来的项目
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<DragDropItem> targetItems = new();

        /// <summary>
        /// 可排序列表 - 支持内部排序的项目集合
        /// 这个列表主要用于演示列表内部的重新排序功能
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<DragDropItem> sortableItems = new();

        /// <summary>
        /// 选中的源项目 - 当前在源列表中选中的项目
        /// </summary>
        [ObservableProperty]
        private DragDropItem? selectedSourceItem;

        /// <summary>
        /// 选中的目标项目 - 当前在目标列表中选中的项目
        /// </summary>
        [ObservableProperty]
        private DragDropItem? selectedTargetItem;

        /// <summary>
        /// 选中的可排序项目 - 当前在可排序列表中选中的项目
        /// </summary>
        [ObservableProperty]
        private DragDropItem? selectedSortableItem;

        #region 代码示例属性

        /// <summary>
        /// 基础功能 XAML 示例代码
        /// </summary>
        [ObservableProperty]
        private string basicXamlExample = string.Empty;

        /// <summary>
        /// 基础功能 C# 示例代码
        /// </summary>
        [ObservableProperty]
        private string basicCSharpExample = string.Empty;

        /// <summary>
        /// 高级功能 XAML 示例代码
        /// </summary>
        [ObservableProperty]
        private string advancedXamlExample = string.Empty;

        /// <summary>
        /// 高级功能 C# 示例代码
        /// </summary>
        [ObservableProperty]
        private string advancedCSharpExample = string.Empty;

        /// <summary>
        /// 样式 XAML 示例代码
        /// </summary>
        [ObservableProperty]
        private string stylesXamlExample = string.Empty;

        #endregion

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// 初始化 ViewModel 并加载示例数据和代码示例
        /// </summary>
        public ListDragDropExampleViewModel()
        {
            _logger.Info("🎯 ListDragDropExampleViewModel 初始化开始");
            
            // 初始化示例数据
            InitializeData();
            
            // 加载代码示例
            LoadCodeExamples();
            
            _logger.Info("✅ ListDragDropExampleViewModel 初始化完成");
        }

        #endregion

        #region 命令

        /// <summary>
        /// 重置数据命令 - 恢复到初始状态
        /// </summary>
        [RelayCommand]
        private void ResetData()
        {
            InitializeData();
            InteractionCount++;
            LastAction = "重置数据";
            StatusMessage = "🔄 数据已重置";
            _logger.Info("重置列表数据");
        }

        /// <summary>
        /// 添加源项目命令 - 动态添加新的可拖拽项目
        /// </summary>
        [RelayCommand]
        private void AddSourceItem()
        {
            var newItem = new DragDropItem
            {
                Id = SourceItems.Count + 1,
                Name = $"源项目 {SourceItems.Count + 1}",
                Description = $"这是第 {SourceItems.Count + 1} 个源项目",
                Category = "源类别",
                Priority = Random.Shared.Next(1, 6)
            };
            
            SourceItems.Add(newItem);
            InteractionCount++;
            LastAction = "添加源项目";
            StatusMessage = $"➕ 添加了新的源项目: {newItem.Name}";
            _logger.Info($"添加源项目: {newItem.Name}");
        }

        /// <summary>
        /// 添加目标项目命令 - 动态添加新的目标项目
        /// </summary>
        [RelayCommand]
        private void AddTargetItem()
        {
            var newItem = new DragDropItem
            {
                Id = TargetItems.Count + 100,
                Name = $"目标项目 {TargetItems.Count + 1}",
                Description = $"这是第 {TargetItems.Count + 1} 个目标项目",
                Category = "目标类别",
                Priority = Random.Shared.Next(1, 6)
            };
            
            TargetItems.Add(newItem);
            InteractionCount++;
            LastAction = "添加目标项目";
            StatusMessage = $"➕ 添加了新的目标项目: {newItem.Name}";
            _logger.Info($"添加目标项目: {newItem.Name}");
        }

        /// <summary>
        /// 清除目标列表命令 - 清空目标列表中的所有项目
        /// </summary>
        [RelayCommand]
        private void ClearTargetList()
        {
            TargetItems.Clear();
            InteractionCount++;
            LastAction = "清除目标列表";
            StatusMessage = "🗑️ 目标列表已清空";
            _logger.Info("清除目标列表");
        }

        /// <summary>
        /// 重置计数命令 - 重置交互计数器
        /// </summary>
        [RelayCommand]
        private void ResetCount()
        {
            InteractionCount = 0;
            LastAction = "重置计数";
            StatusMessage = "🔄 计数已重置";
            _logger.Info("重置交互计数");
        }

        /// <summary>
        /// 清除状态命令 - 清除状态消息
        /// </summary>
        [RelayCommand]
        private void ClearStatus()
        {
            StatusMessage = "状态已清除";
            LastAction = "清除状态";
            _logger.Info("清除状态信息");
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 获取集合的友好名称，用于日志记录和调试
        /// </summary>
        /// <param name="collection">集合对象</param>
        /// <returns>集合的友好名称</returns>
        private string GetCollectionName(object collection)
        {
            if (collection == SourceItems) return "源列表";
            if (collection == TargetItems) return "目标列表";
            if (collection == SortableItems) return "排序列表";
            return collection?.GetType().Name ?? "未知集合";
        }

        /// <summary>
        /// 验证是否为有效的放置目标
        /// 可以根据业务规则进行自定义验证
        /// </summary>
        /// <param name="sourceItem">源项目</param>
        /// <param name="targetItem">目标项目</param>
        /// <returns>是否允许放置</returns>
        private bool IsValidDropTarget(DragDropItem sourceItem, DragDropItem targetItem)
        {
            // 基础验证
            if (sourceItem == null) return false;
            
            // 业务规则示例：
            // 1. 不能拖拽到自己身上
            if (sourceItem == targetItem) return false;
            
            // 2. 可以添加更多业务规则，例如：
            // - 只允许相同类别的项目放置在一起
            // - 检查优先级限制
            // - 验证用户权限
            
            // 示例：只允许相同类别的项目放置在一起
            // return targetItem == null || targetItem.Category == sourceItem.Category;
            
            return true; // 默认允许所有放置操作
        }

        #endregion

        #region IDragSource 实现

        /// <summary>
        /// 开始拖拽操作
        /// 当用户开始拖拽某个项目时，gong-wpf-dragdrop 框架会调用此方法
        /// 在这里我们需要：
        /// 1. 验证拖拽的源项目是否有效
        /// 2. 设置拖拽数据（dragInfo.Data）
        /// 3. 设置拖拽效果（Move、Copy、Link 等）
        /// 4. 更新 UI 状态和日志
        /// </summary>
        /// <param name="dragInfo">
        /// 拖拽信息对象，包含：
        /// - SourceItem: 被拖拽的源项目
        /// - SourceCollection: 源集合
        /// - SourceIndex: 源项目在集合中的索引
        /// - Data: 要传递的拖拽数据（我们需要设置）
        /// - Effects: 拖拽效果（我们需要设置）
        /// </param>
        public void StartDrag(IDragInfo dragInfo)
        {
            try
            {
                // 1. 获取被拖拽的源项目，并进行类型检查
                var sourceItem = dragInfo.SourceItem as DragDropItem;
                if (sourceItem != null)
                {
                    // 2. 设置拖拽数据 - 这是实际被传递的数据
                    // 可以是单个对象、多个对象的集合，或者任何序列化的数据
                    dragInfo.Data = sourceItem;

                    // 3. 设置拖拽效果
                    // Move: 移动操作（从源位置移除，添加到目标位置）
                    // Copy: 复制操作（保留源位置，在目标位置创建副本）
                    // Link: 链接操作（创建引用或快捷方式）
                    // None: 不允许拖拽
                    dragInfo.Effects = DragDropEffects.Move;

                    // 4. 更新应用程序状态
                    InteractionCount++;
                    LastAction = $"开始拖拽: {sourceItem.Name}";
                    StatusMessage = $"🎯 开始拖拽项目: {sourceItem.Name}";

                    // 5. 记录日志用于调试和监控
                    _logger.Info($"开始拖拽项目: {sourceItem.Name} (ID: {sourceItem.Id})");
                }
                else
                {
                    // 如果源项目无效，记录警告
                    _logger.Warning("尝试拖拽无效的项目");
                    StatusMessage = "⚠️ 无法拖拽：项目无效";
                }
            }
            catch (Exception ex)
            {
                // 异常处理：记录错误并更新 UI 状态
                _logger.Error($"开始拖拽失败: {ex.Message}");
                StatusMessage = $"❌ 拖拽失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 判断是否可以开始拖拽
        /// 这是一个重要的验证方法，在用户尝试拖拽之前被调用
        /// 用于实现业务规则和权限控制
        /// </summary>
        /// <param name="dragInfo">
        /// 拖拽信息对象，包含：
        /// - SourceItem: 要拖拽的项目
        /// - SourceCollection: 源集合
        /// - SourceIndex: 项目索引
        /// </param>
        /// <returns>
        /// true: 允许拖拽，用户可以开始拖拽操作
        /// false: 禁止拖拽，拖拽操作不会开始
        /// </returns>
        public bool CanStartDrag(IDragInfo dragInfo)
        {
            // 1. 基础类型检查：确保是我们支持的数据类型
            if (!(dragInfo.SourceItem is DragDropItem item))
            {
                return false;
            }

            // 2. 业务规则验证：可以根据具体需求添加更多条件
            // 例如：
            // - 检查用户权限
            // - 检查项目状态（是否锁定、只读等）
            // - 检查业务逻辑约束

            // 示例业务规则（可以根据实际需求修改）：
            // if (item.IsLocked) return false;        // 锁定的项目不能拖拽
            // if (item.IsReadOnly) return false;      // 只读项目不能拖拽
            // if (!CurrentUser.CanEdit) return false; // 用户没有编辑权限

            // 3. 记录调试信息
            _logger.Debug($"拖拽权限检查: {item.Name} - 允许拖拽");

            return true; // 默认允许拖拽
        }

        /// <summary>
        /// 拖拽完成后的清理工作
        /// 当拖拽操作完成（无论成功还是失败）后调用
        /// </summary>
        /// <param name="dropInfo">放置信息</param>
        public void Dropped(IDropInfo dropInfo)
        {
            try
            {
                var sourceItem = dropInfo.Data as DragDropItem;
                if (sourceItem != null)
                {
                    InteractionCount++;
                    LastAction = $"拖拽完成: {sourceItem.Name}";
                    StatusMessage = $"✅ 拖拽完成: {sourceItem.Name}";
                    _logger.Info($"拖拽完成: {sourceItem.Name}");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"拖拽完成处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 拖拽操作完成
        /// 在整个拖拽操作结束后调用，提供操作结果信息
        /// </summary>
        /// <param name="operationResult">操作结果（Move、Copy、None等）</param>
        /// <param name="dragInfo">原始拖拽信息</param>
        public void DragDropOperationFinished(DragDropEffects operationResult, IDragInfo dragInfo)
        {
            try
            {
                var sourceItem = dragInfo.Data as DragDropItem;
                if (sourceItem != null)
                {
                    InteractionCount++;
                    LastAction = $"拖拽操作完成: {sourceItem.Name} ({operationResult})";
                    StatusMessage = $"🎯 拖拽操作完成: {sourceItem.Name}";
                    _logger.Info($"拖拽操作完成: {sourceItem.Name}, 结果: {operationResult}");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"拖拽操作完成处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理拖拽过程中的异常
        /// 当拖拽过程中发生异常时调用
        /// </summary>
        /// <param name="exception">异常信息</param>
        /// <returns>是否已处理异常（true表示已处理，false表示继续抛出）</returns>
        public bool TryCatchOccurredException(Exception exception)
        {
            _logger.Error($"拖拽过程中发生异常: {exception.Message}");
            StatusMessage = $"❌ 拖拽异常: {exception.Message}";

            InteractionCount++;
            LastAction = "拖拽异常";

            // 返回 true 表示异常已被处理，不会再向上抛出
            return true;
        }

        /// <summary>
        /// 拖拽被取消
        /// 当用户取消拖拽操作时调用（例如按ESC键）
        /// </summary>
        public void DragCancelled()
        {
            InteractionCount++;
            LastAction = "拖拽被取消";
            StatusMessage = "⚠️ 拖拽操作被取消";
            _logger.Info("拖拽操作被取消");
        }

        #endregion

        #region IDropTarget 实现

        /// <summary>
        /// 拖拽悬停时的处理
        /// 当用户拖拽项目悬停在放置目标上时，框架会持续调用此方法
        /// 这里需要：
        /// 1. 验证是否允许在当前位置放置
        /// 2. 设置视觉反馈（装饰器）
        /// 3. 设置允许的拖拽效果
        ///
        /// 注意：此方法会被频繁调用，应避免执行耗时操作
        /// </summary>
        /// <param name="dropInfo">
        /// 放置信息对象，包含：
        /// - Data: 被拖拽的数据（来自 StartDrag 设置的 dragInfo.Data）
        /// - TargetCollection: 目标集合
        /// - TargetItem: 目标项目（悬停位置的项目）
        /// - InsertIndex: 插入位置索引
        /// - DropTargetAdorner: 视觉装饰器（我们需要设置）
        /// - Effects: 拖拽效果（我们需要设置）
        /// </param>
        public void DragOver(IDropInfo dropInfo)
        {
            try
            {
                // 1. 获取被拖拽的数据并进行类型检查
                var sourceItem = dropInfo.Data as DragDropItem;
                if (sourceItem == null)
                {
                    // 如果数据类型不匹配，禁止放置
                    dropInfo.Effects = DragDropEffects.None;
                    return;
                }

                // 2. 获取目标集合，确定放置位置
                var targetCollection = dropInfo.TargetCollection;

                // 3. 根据目标集合设置不同的视觉反馈和拖拽效果
                if (targetCollection == TargetItems)
                {
                    // 放置到目标列表：使用高亮装饰器
                    dropInfo.DropTargetAdorner = DropTargetAdorners.Highlight;
                    dropInfo.Effects = DragDropEffects.Move;
                    _logger.Debug($"拖拽悬停: {sourceItem.Name} -> 目标列表");
                }
                else if (targetCollection == SortableItems)
                {
                    // 放置到可排序列表：使用插入装饰器显示插入位置
                    dropInfo.DropTargetAdorner = DropTargetAdorners.Insert;
                    dropInfo.Effects = DragDropEffects.Move;
                    _logger.Debug($"拖拽悬停: {sourceItem.Name} -> 排序列表 (索引: {dropInfo.InsertIndex})");
                }
                else if (targetCollection == SourceItems)
                {
                    // 放置到源列表：允许内部重新排序
                    dropInfo.DropTargetAdorner = DropTargetAdorners.Insert;
                    dropInfo.Effects = DragDropEffects.Move;
                    _logger.Debug($"拖拽悬停: {sourceItem.Name} -> 源列表重排序 (索引: {dropInfo.InsertIndex})");
                }
                else
                {
                    // 未知目标：禁止放置
                    dropInfo.Effects = DragDropEffects.None;
                    _logger.Debug($"拖拽悬停: {sourceItem.Name} -> 未知目标，禁止放置");
                }

                // 4. 可选：添加额外的业务规则验证
                // 例如：检查是否允许跨类别拖拽
                // if (!IsValidDropTarget(sourceItem, dropInfo.TargetItem as DragDropItem))
                // {
                //     dropInfo.Effects = DragDropEffects.None;
                // }
            }
            catch (Exception ex)
            {
                // 异常处理：禁止放置并记录错误
                dropInfo.Effects = DragDropEffects.None;
                _logger.Error($"拖拽悬停处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 执行放置操作
        /// 当用户释放鼠标完成拖拽时，框架会调用此方法
        /// 这是拖拽操作的核心逻辑，需要：
        /// 1. 验证放置操作的有效性
        /// 2. 执行实际的数据移动/复制操作
        /// 3. 更新 UI 状态和用户反馈
        /// 4. 处理各种边界情况和异常
        /// </summary>
        /// <param name="dropInfo">
        /// 放置信息对象，包含：
        /// - Data: 被拖拽的数据
        /// - TargetCollection: 目标集合
        /// - TargetItem: 目标项目
        /// - InsertIndex: 插入位置索引
        /// - DragInfo: 原始拖拽信息（包含源集合等）
        /// </param>
        public void Drop(IDropInfo dropInfo)
        {
            try
            {
                // 1. 数据验证：确保拖拽数据有效
                var sourceItem = dropInfo.Data as DragDropItem;
                if (sourceItem == null)
                {
                    _logger.Warning("放置操作失败：拖拽数据无效");
                    StatusMessage = "⚠️ 放置失败：数据无效";
                    return;
                }

                // 2. 获取源集合和目标集合
                var targetCollection = dropInfo.TargetCollection;
                var sourceCollection = dropInfo.DragInfo?.SourceCollection;

                _logger.Info($"开始放置操作: {sourceItem.Name} (源: {GetCollectionName(sourceCollection)} -> 目标: {GetCollectionName(targetCollection)})");

                // 3. 根据目标集合执行不同的放置逻辑
                if (targetCollection == TargetItems)
                {
                    HandleDropToTargetList(sourceItem, sourceCollection, dropInfo);
                }
                else if (targetCollection == SortableItems)
                {
                    HandleDropToSortableList(sourceItem, sourceCollection, dropInfo);
                }
                else if (targetCollection == SourceItems)
                {
                    HandleDropToSourceList(sourceItem, sourceCollection, dropInfo);
                }
                else
                {
                    // 未知目标集合
                    _logger.Warning($"放置到未知目标集合: {targetCollection?.GetType().Name}");
                    StatusMessage = "⚠️ 放置失败：未知目标";
                }
            }
            catch (Exception ex)
            {
                // 异常处理：记录错误并提供用户反馈
                _logger.Error($"放置操作失败: {ex.Message}");
                StatusMessage = $"❌ 放置失败: {ex.Message}";
            }
        }

        #endregion

        #region 放置处理方法

        /// <summary>
        /// 处理放置到目标列表的操作
        /// 支持从其他列表移动到目标列表，以及在目标列表内部重新排序
        /// </summary>
        /// <param name="sourceItem">被拖拽的项目</param>
        /// <param name="sourceCollection">源集合</param>
        /// <param name="dropInfo">放置信息</param>
        private void HandleDropToTargetList(DragDropItem sourceItem, object sourceCollection, IDropInfo dropInfo)
        {
            if (sourceCollection == SourceItems)
            {
                // 场景1：从源列表移动到目标列表
                SourceItems.Remove(sourceItem);
                TargetItems.Add(sourceItem);

                // 更新状态
                InteractionCount++;
                LastAction = $"移动到目标列表: {sourceItem.Name}";
                StatusMessage = $"✅ 项目 '{sourceItem.Name}' 已从源列表移动到目标列表";
                _logger.Info($"项目从源列表移动到目标列表: {sourceItem.Name}");
            }
            else if (sourceCollection == SortableItems)
            {
                // 场景2：从排序列表移动到目标列表
                SortableItems.Remove(sourceItem);
                TargetItems.Add(sourceItem);

                InteractionCount++;
                LastAction = $"从排序列表移动到目标列表: {sourceItem.Name}";
                StatusMessage = $"✅ 项目 '{sourceItem.Name}' 已从排序列表移动到目标列表";
                _logger.Info($"项目从排序列表移动到目标列表: {sourceItem.Name}");
            }
            else if (sourceCollection == TargetItems)
            {
                // 场景3：在目标列表内部重新排序
                var currentIndex = TargetItems.IndexOf(sourceItem);
                var insertIndex = dropInfo.InsertIndex;

                // 验证插入索引的有效性
                if (insertIndex >= 0 && insertIndex <= TargetItems.Count && currentIndex != insertIndex)
                {
                    // 执行移动操作
                    TargetItems.Move(currentIndex, insertIndex);

                    InteractionCount++;
                    LastAction = $"目标列表重排序: {sourceItem.Name}";
                    StatusMessage = $"🔄 目标列表中 '{sourceItem.Name}' 已重新排序 (位置: {insertIndex + 1})";
                    _logger.Info($"目标列表重新排序: {sourceItem.Name} (从位置 {currentIndex + 1} 移动到 {insertIndex + 1})");
                }
                else
                {
                    _logger.Debug($"目标列表排序操作被跳过: 无效的插入位置或相同位置 (当前: {currentIndex}, 目标: {insertIndex})");
                }
            }
        }

        /// <summary>
        /// 处理放置到可排序列表的操作
        /// 支持从其他列表移动到可排序列表，以及在可排序列表内部重新排序
        /// </summary>
        /// <param name="sourceItem">被拖拽的项目</param>
        /// <param name="sourceCollection">源集合</param>
        /// <param name="dropInfo">放置信息</param>
        private void HandleDropToSortableList(DragDropItem sourceItem, object sourceCollection, IDropInfo dropInfo)
        {
            if (sourceCollection != SortableItems)
            {
                // 从其他列表移动到可排序列表
                RemoveFromSourceCollection(sourceItem, sourceCollection);

                var insertIndex = dropInfo.InsertIndex;
                if (insertIndex >= 0 && insertIndex <= SortableItems.Count)
                {
                    SortableItems.Insert(insertIndex, sourceItem);
                }
                else
                {
                    SortableItems.Add(sourceItem);
                }

                InteractionCount++;
                LastAction = $"移动到排序列表: {sourceItem.Name}";
                StatusMessage = $"✅ 项目 '{sourceItem.Name}' 已移动到排序列表";
                _logger.Info($"项目移动到排序列表: {sourceItem.Name}");
            }
            else
            {
                // 在可排序列表内部重新排序
                var currentIndex = SortableItems.IndexOf(sourceItem);
                var insertIndex = dropInfo.InsertIndex;

                if (insertIndex >= 0 && insertIndex < SortableItems.Count && currentIndex != insertIndex)
                {
                    SortableItems.Move(currentIndex, insertIndex);

                    InteractionCount++;
                    LastAction = $"排序列表重排序: {sourceItem.Name}";
                    StatusMessage = $"🔄 排序列表中 '{sourceItem.Name}' 已重新排序";
                    _logger.Info($"排序列表重新排序: {sourceItem.Name}");
                }
            }
        }

        /// <summary>
        /// 处理放置到源列表的操作
        /// 支持从其他列表移动回源列表，以及在源列表内部重新排序
        /// </summary>
        /// <param name="sourceItem">被拖拽的项目</param>
        /// <param name="sourceCollection">源集合</param>
        /// <param name="dropInfo">放置信息</param>
        private void HandleDropToSourceList(DragDropItem sourceItem, object sourceCollection, IDropInfo dropInfo)
        {
            if (sourceCollection != SourceItems)
            {
                // 从其他列表移动回源列表
                RemoveFromSourceCollection(sourceItem, sourceCollection);
                SourceItems.Add(sourceItem);

                InteractionCount++;
                LastAction = $"移回源列表: {sourceItem.Name}";
                StatusMessage = $"↩️ 项目 '{sourceItem.Name}' 已移回源列表";
                _logger.Info($"项目移回源列表: {sourceItem.Name}");
            }
            else
            {
                // 在源列表内部重新排序
                var currentIndex = SourceItems.IndexOf(sourceItem);
                var insertIndex = dropInfo.InsertIndex;

                if (insertIndex >= 0 && insertIndex < SourceItems.Count && currentIndex != insertIndex)
                {
                    SourceItems.Move(currentIndex, insertIndex);

                    InteractionCount++;
                    LastAction = $"源列表重排序: {sourceItem.Name}";
                    StatusMessage = $"🔄 源列表中 '{sourceItem.Name}' 已重新排序";
                    _logger.Info($"源列表重新排序: {sourceItem.Name}");
                }
            }
        }

        /// <summary>
        /// 从源集合中移除项目
        /// </summary>
        /// <param name="item">要移除的项目</param>
        /// <param name="sourceCollection">源集合</param>
        private void RemoveFromSourceCollection(DragDropItem item, object sourceCollection)
        {
            if (sourceCollection == SourceItems)
            {
                SourceItems.Remove(item);
            }
            else if (sourceCollection == TargetItems)
            {
                TargetItems.Remove(item);
            }
            else if (sourceCollection == SortableItems)
            {
                SortableItems.Remove(item);
            }
        }

        #endregion

        #region 数据初始化

        /// <summary>
        /// 初始化示例数据
        /// 创建各种类型的测试项目用于演示拖拽功能
        /// </summary>
        private void InitializeData()
        {
            // 清空现有数据
            SourceItems.Clear();
            TargetItems.Clear();
            SortableItems.Clear();

            // 初始化源列表数据
            for (int i = 1; i <= 5; i++)
            {
                SourceItems.Add(new DragDropItem
                {
                    Id = i,
                    Name = $"源项目 {i}",
                    Description = $"这是第 {i} 个可拖拽的源项目",
                    Category = "源类别",
                    Priority = i,
                    IsSelected = false
                });
            }

            // 初始化可排序列表数据
            for (int i = 1; i <= 3; i++)
            {
                SortableItems.Add(new DragDropItem
                {
                    Id = i + 100,
                    Name = $"排序项目 {i}",
                    Description = $"这是第 {i} 个可排序的项目",
                    Category = "排序类别",
                    Priority = i,
                    IsSelected = false
                });
            }

            // 目标列表初始为空，等待拖拽项目

            _logger.Info($"数据初始化完成 - 源项目: {SourceItems.Count}, 排序项目: {SortableItems.Count}");
        }

        /// <summary>
        /// 加载代码示例
        /// 从文件中读取各种代码示例用于展示
        /// </summary>
        private void LoadCodeExamples()
        {
            try
            {
                var baseDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "CodeExamples", "DragDrop", "ListDragDrop");

                // 加载基础 XAML 示例
                var basicXamlPath = Path.Combine(baseDir, "Basic.xaml.txt");
                if (File.Exists(basicXamlPath))
                {
                    BasicXamlExample = File.ReadAllText(basicXamlPath);
                }

                // 加载基础 C# 示例
                var basicCSharpPath = Path.Combine(baseDir, "Basic.cs.txt");
                if (File.Exists(basicCSharpPath))
                {
                    BasicCSharpExample = File.ReadAllText(basicCSharpPath);
                }

                // 加载高级 XAML 示例
                var advancedXamlPath = Path.Combine(baseDir, "Advanced.xaml.txt");
                if (File.Exists(advancedXamlPath))
                {
                    AdvancedXamlExample = File.ReadAllText(advancedXamlPath);
                }

                // 加载高级 C# 示例
                var advancedCSharpPath = Path.Combine(baseDir, "Advanced.cs.txt");
                if (File.Exists(advancedCSharpPath))
                {
                    AdvancedCSharpExample = File.ReadAllText(advancedCSharpPath);
                }

                // 加载样式 XAML 示例
                var stylesXamlPath = Path.Combine(baseDir, "Styles.xaml.txt");
                if (File.Exists(stylesXamlPath))
                {
                    StylesXamlExample = File.ReadAllText(stylesXamlPath);
                }

                _logger.Info("代码示例加载完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"加载代码示例失败: {ex.Message}");

                // 设置默认示例
                BasicXamlExample = "<!-- 代码示例加载失败，请检查文件路径 -->";
                BasicCSharpExample = "// 代码示例加载失败，请检查文件路径";
                AdvancedXamlExample = "<!-- 代码示例加载失败，请检查文件路径 -->";
                AdvancedCSharpExample = "// 代码示例加载失败，请检查文件路径";
                StylesXamlExample = "<!-- 代码示例加载失败，请检查文件路径 -->";
            }
        }

        #endregion
    }

    /// <summary>
    /// 拖拽项目数据模型
    /// 表示可以被拖拽的数据项
    /// </summary>
    public partial class DragDropItem : ObservableObject
    {
        /// <summary>
        /// 项目唯一标识符
        /// </summary>
        [ObservableProperty]
        private int id;

        /// <summary>
        /// 项目显示名称
        /// </summary>
        [ObservableProperty]
        private string name = string.Empty;

        /// <summary>
        /// 项目详细描述
        /// </summary>
        [ObservableProperty]
        private string description = string.Empty;

        /// <summary>
        /// 项目分类
        /// </summary>
        [ObservableProperty]
        private string category = string.Empty;

        /// <summary>
        /// 项目优先级
        /// </summary>
        [ObservableProperty]
        private int priority;

        /// <summary>
        /// 是否被选中
        /// </summary>
        [ObservableProperty]
        private bool isSelected;

        /// <summary>
        /// 重写ToString方法，便于调试和日志记录
        /// </summary>
        /// <returns>项目的字符串表示</returns>
        public override string ToString()
        {
            return $"{Name} (ID: {Id}, Priority: {Priority})";
        }
    }
}
