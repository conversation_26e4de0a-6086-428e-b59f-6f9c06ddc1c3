// ListView 基础功能 C# 代码示例
// 展示如何在 ViewModel 中管理 ListView 数据

using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;

public partial class BasicListViewViewModel : ObservableObject
{
    /// <summary>
    /// 简单项集合
    /// </summary>
    [ObservableProperty]
    private ObservableCollection<string> simpleItems = new();

    /// <summary>
    /// 选择的项
    /// </summary>
    [ObservableProperty]
    private string? selectedSimpleItem;

    /// <summary>
    /// 构造函数
    /// </summary>
    public BasicListViewViewModel()
    {
        InitializeData();
        
        // 监听选择变化
        PropertyChanged += OnPropertyChanged;
    }

    /// <summary>
    /// 初始化数据
    /// </summary>
    private void InitializeData()
    {
        SimpleItems.Clear();
        for (int i = 1; i <= 10; i++)
        {
            SimpleItems.Add($"项目 {i}");
        }
    }

    /// <summary>
    /// 添加新项命令
    /// </summary>
    [RelayCommand]
    private void AddItem()
    {
        var newItem = $"新项目 {SimpleItems.Count + 1}";
        SimpleItems.Add(newItem);
    }

    /// <summary>
    /// 删除选中项命令
    /// </summary>
    [RelayCommand]
    private void RemoveSelectedItem()
    {
        if (SelectedSimpleItem != null)
        {
            SimpleItems.Remove(SelectedSimpleItem);
            SelectedSimpleItem = null;
        }
    }

    /// <summary>
    /// 属性变化处理
    /// </summary>
    private void OnPropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(SelectedSimpleItem))
        {
            // 处理选择变化
            Console.WriteLine($"选择了: {SelectedSimpleItem}");
        }
    }
}

/*
关键概念：
1. ObservableCollection 自动通知 UI 更新
2. ObservableProperty 自动生成属性和通知
3. RelayCommand 简化命令实现
4. PropertyChanged 事件监听属性变化
5. 双向绑定实现数据同步
*/
