<UserControl x:Class="WPFTest.Views.ButtonControls.DropDownButtonPageView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:codeExample="clr-namespace:Zylo.WPF.Controls.CodeExample;assembly=Zylo.WPF"
             xmlns:mvvm="http://prismlibrary.com/"
             xmlns:buttonControls="clr-namespace:WPFTest.ViewModels.ButtonControls"
             mc:Ignorable="d"
             d:DesignHeight="2000" d:DesignWidth="1200"
             mvvm:ViewModelLocator.AutoWireViewModel="True"
             d:DataContext="{d:DesignInstance buttonControls:DropDownButtonPageViewModel}">

    <UserControl.Resources>
        <!-- 🎨 所有样式来自 Zylo.WPF 项目 -->
        <!-- DropDownButton 样式已在 Zylo.WPF/Resources/DropDownButton/ 目录下定义 -->
        <!-- 可用样式：DropDownButtonStyle, SmallDropDownButtonStyle, LargeDropDownButtonStyle -->
        <!-- 特殊样式：PrimaryDropDownButtonStyle, TransparentDropDownButtonStyle, ModernDropDownButtonStyle -->
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" 
                  HorizontalScrollBarVisibility="Disabled"
                  Padding="24">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>    <!-- 标题区域 -->
                <RowDefinition Height="*"/>       <!-- 主要内容 -->
                <RowDefinition Height="Auto"/>    <!-- 底部操作栏 -->
            </Grid.RowDefinitions>

            <!-- 标题区域 -->
            <StackPanel Grid.Row="0" Margin="0,0,0,20">
                <TextBlock Text="🎨 DropDownButton 控件展示"
                           FontSize="32"
                           FontWeight="Bold"
                           Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                           Margin="0,0,0,8"/>
                <TextBlock Text="展示 DropDownButton 控件的各种样式和交互效果，包括分离式操作和动态菜单" 
                           FontSize="16" 
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                           TextWrapping="Wrap"/>
                
                <!-- 状态显示 -->
                <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                        BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Padding="16,12"
                        Margin="0,16,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <ui:SymbolIcon Symbol="Info24" 
                                       FontSize="20" 
                                       Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                       VerticalAlignment="Center"
                                       Margin="0,0,12,0"/>
                        <TextBlock Grid.Column="1" 
                                   Text="{Binding StatusMessage}" 
                                   FontSize="14"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                   VerticalAlignment="Center"
                                   TextWrapping="Wrap"/>
                    </Grid>
                </Border>
            </StackPanel>

            <!-- 主要内容区域 -->
            <Border Grid.Row="1" Margin="0,0,0,20">
                <StackPanel>

                    <!-- 基础功能展示 -->
                    <ui:CardExpander Header="🎯 基础功能" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 DropDownButton 的基础功能和用法"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>
                            
                            <!-- 基础控件展示区域 -->
                            <WrapPanel>
                                <!-- 标准 DropDownButton -->
                                <ui:DropDownButton Content="文件操作" 
                                                   Command="{Binding HandleInteractionCommand}"
                                                   CommandParameter="文件操作"
                                                   Margin="8">
                                    <ui:DropDownButton.Flyout>
                                        <ContextMenu>
                                            <MenuItem Header="新建" Command="{Binding HandleInteractionCommand}" CommandParameter="新建"/>
                                            <MenuItem Header="打开" Command="{Binding HandleInteractionCommand}" CommandParameter="打开"/>
                                            <MenuItem Header="保存" Command="{Binding HandleInteractionCommand}" CommandParameter="保存"/>
                                            <Separator/>
                                            <MenuItem Header="退出" Command="{Binding HandleInteractionCommand}" CommandParameter="退出"/>
                                        </ContextMenu>
                                    </ui:DropDownButton.Flyout>
                                </ui:DropDownButton>

                                <!-- 带图标的 DropDownButton -->
                                <ui:DropDownButton Content="编辑" 
                                                   Command="{Binding HandleInteractionCommand}"
                                                   CommandParameter="编辑"
                                                   Margin="8">
                                    <ui:DropDownButton.Icon>
                                        <ui:SymbolIcon Symbol="Edit24"/>
                                    </ui:DropDownButton.Icon>
                                    <ui:DropDownButton.Flyout>
                                        <ContextMenu>
                                            <MenuItem Header="复制" Command="{Binding HandleInteractionCommand}" CommandParameter="复制">
                                                <MenuItem.Icon>
                                                    <ui:SymbolIcon Symbol="Copy24"/>
                                                </MenuItem.Icon>
                                            </MenuItem>
                                            <MenuItem Header="剪切" Command="{Binding HandleInteractionCommand}" CommandParameter="剪切">
                                                <MenuItem.Icon>
                                                    <ui:SymbolIcon Symbol="Cut24"/>
                                                </MenuItem.Icon>
                                            </MenuItem>
                                            <MenuItem Header="粘贴" Command="{Binding HandleInteractionCommand}" CommandParameter="粘贴">
                                                <MenuItem.Icon>
                                                    <ui:SymbolIcon Symbol="ClipboardPaste24"/>
                                                </MenuItem.Icon>
                                            </MenuItem>
                                        </ContextMenu>
                                    </ui:DropDownButton.Flyout>
                                </ui:DropDownButton>

                                <!-- 禁用状态 -->
                                <ui:DropDownButton Content="禁用状态" 
                                                   IsEnabled="False"
                                                   Margin="8">
                                    <ui:DropDownButton.Flyout>
                                        <ContextMenu>
                                            <MenuItem Header="选项1"/>
                                            <MenuItem Header="选项2"/>
                                        </ContextMenu>
                                    </ui:DropDownButton.Flyout>
                                </ui:DropDownButton>
                            </WrapPanel>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="基础功能代码示例"
                                Language="XAML"
                                Description="展示 DropDownButton 的基础用法"
                                ShowTabs="True"
                                XamlCode="{Binding BasicXamlExample}"
                                CSharpCode="{Binding BasicCSharpExample}"
                                IsExpanded="False"
                                Margin="0,16,0,0"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 高级功能展示 -->
                    <ui:CardExpander Header="⚡ 高级功能" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 DropDownButton 的高级功能和分离式操作"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 高级控件展示区域 -->
                            <WrapPanel>
                                <!-- 分离式操作 DropDownButton -->
                                <ui:DropDownButton Content="格式化"
                                                   Command="{Binding HandleInteractionCommand}"
                                                   CommandParameter="格式化"
                                                   Margin="8">
                                    <ui:DropDownButton.Icon>
                                        <ui:SymbolIcon Symbol="TextBold24"/>
                                    </ui:DropDownButton.Icon>
                                    <ui:DropDownButton.Flyout>
                                        <ContextMenu>
                                            <MenuItem Header="字体">
                                                <MenuItem Header="加粗" Command="{Binding HandleInteractionCommand}" CommandParameter="加粗">
                                                    <MenuItem.Icon>
                                                        <ui:SymbolIcon Symbol="TextBold24"/>
                                                    </MenuItem.Icon>
                                                </MenuItem>
                                                <MenuItem Header="斜体" Command="{Binding HandleInteractionCommand}" CommandParameter="斜体">
                                                    <MenuItem.Icon>
                                                        <ui:SymbolIcon Symbol="TextItalic24"/>
                                                    </MenuItem.Icon>
                                                </MenuItem>
                                                <MenuItem Header="下划线" Command="{Binding HandleInteractionCommand}" CommandParameter="下划线">
                                                    <MenuItem.Icon>
                                                        <ui:SymbolIcon Symbol="TextUnderline24"/>
                                                    </MenuItem.Icon>
                                                </MenuItem>
                                            </MenuItem>
                                            <MenuItem Header="段落">
                                                <MenuItem Header="左对齐" Command="{Binding HandleInteractionCommand}" CommandParameter="左对齐">
                                                    <MenuItem.Icon>
                                                        <ui:SymbolIcon Symbol="TextAlignLeft24"/>
                                                    </MenuItem.Icon>
                                                </MenuItem>
                                                <MenuItem Header="居中" Command="{Binding HandleInteractionCommand}" CommandParameter="居中">
                                                    <MenuItem.Icon>
                                                        <ui:SymbolIcon Symbol="TextAlignCenter24"/>
                                                    </MenuItem.Icon>
                                                </MenuItem>
                                                <MenuItem Header="右对齐" Command="{Binding HandleInteractionCommand}" CommandParameter="右对齐">
                                                    <MenuItem.Icon>
                                                        <ui:SymbolIcon Symbol="TextAlignRight24"/>
                                                    </MenuItem.Icon>
                                                </MenuItem>
                                            </MenuItem>
                                        </ContextMenu>
                                    </ui:DropDownButton.Flyout>
                                </ui:DropDownButton>

                                <!-- 视图切换 DropDownButton -->
                                <ui:DropDownButton Content="视图"
                                                   Command="{Binding HandleInteractionCommand}"
                                                   CommandParameter="视图"
                                                   Margin="8">
                                    <ui:DropDownButton.Icon>
                                        <ui:SymbolIcon Symbol="Eye24"/>
                                    </ui:DropDownButton.Icon>
                                    <ui:DropDownButton.Flyout>
                                        <ContextMenu>
                                            <MenuItem Header="列表视图" Command="{Binding HandleInteractionCommand}" CommandParameter="列表视图">
                                                <MenuItem.Icon>
                                                    <ui:SymbolIcon Symbol="List24"/>
                                                </MenuItem.Icon>
                                            </MenuItem>
                                            <MenuItem Header="网格视图" Command="{Binding HandleInteractionCommand}" CommandParameter="网格视图">
                                                <MenuItem.Icon>
                                                    <ui:SymbolIcon Symbol="Apps24"/>
                                                </MenuItem.Icon>
                                            </MenuItem>
                                            <MenuItem Header="详细视图" Command="{Binding HandleInteractionCommand}" CommandParameter="详细视图">
                                                <MenuItem.Icon>
                                                    <ui:SymbolIcon Symbol="Table24"/>
                                                </MenuItem.Icon>
                                            </MenuItem>
                                        </ContextMenu>
                                    </ui:DropDownButton.Flyout>
                                </ui:DropDownButton>

                                <!-- 导出功能 DropDownButton -->
                                <ui:DropDownButton Content="导出"
                                                   Command="{Binding HandleInteractionCommand}"
                                                   CommandParameter="导出"
                                                   Margin="8">
                                    <ui:DropDownButton.Icon>
                                        <ui:SymbolIcon Symbol="Share24"/>
                                    </ui:DropDownButton.Icon>
                                    <ui:DropDownButton.Flyout>
                                        <ContextMenu>
                                            <MenuItem Header="导出为 PDF" Command="{Binding HandleInteractionCommand}" CommandParameter="导出PDF">
                                                <MenuItem.Icon>
                                                    <ui:SymbolIcon Symbol="Document24"/>
                                                </MenuItem.Icon>
                                            </MenuItem>
                                            <MenuItem Header="导出为 Excel" Command="{Binding HandleInteractionCommand}" CommandParameter="导出Excel">
                                                <MenuItem.Icon>
                                                    <ui:SymbolIcon Symbol="Table24"/>
                                                </MenuItem.Icon>
                                            </MenuItem>
                                            <MenuItem Header="导出为 Word" Command="{Binding HandleInteractionCommand}" CommandParameter="导出Word">
                                                <MenuItem.Icon>
                                                    <ui:SymbolIcon Symbol="Document24"/>
                                                </MenuItem.Icon>
                                            </MenuItem>
                                            <Separator/>
                                            <MenuItem Header="自定义导出..." Command="{Binding HandleInteractionCommand}" CommandParameter="自定义导出"/>
                                        </ContextMenu>
                                    </ui:DropDownButton.Flyout>
                                </ui:DropDownButton>
                            </WrapPanel>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="高级功能代码示例"
                                Language="XAML"
                                Description="展示 DropDownButton 的高级用法和嵌套菜单"
                                ShowTabs="True"
                                XamlCode="{Binding AdvancedXamlExample}"
                                CSharpCode="{Binding AdvancedCSharpExample}"
                                IsExpanded="False"
                                Margin="0,16,0,0"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 样式展示 -->
                    <ui:CardExpander Header="🎨 样式展示" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示来自 Zylo.WPF 的各种 DropDownButton 样式"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 不同外观样式 -->
                            <GroupBox Header="不同外观样式" Padding="15" Margin="0,0,0,16">
                                <WrapPanel>
                                    <!-- Primary 样式 -->
                                    <ui:DropDownButton Content="Primary"
                                                       Appearance="Primary"
                                                       Command="{Binding HandleInteractionCommand}"
                                                       CommandParameter="Primary样式"
                                                       Margin="8">
                                        <ui:DropDownButton.Flyout>
                                            <ContextMenu>
                                                <MenuItem Header="选项1" Command="{Binding HandleInteractionCommand}" CommandParameter="Primary选项1"/>
                                                <MenuItem Header="选项2" Command="{Binding HandleInteractionCommand}" CommandParameter="Primary选项2"/>
                                            </ContextMenu>
                                        </ui:DropDownButton.Flyout>
                                    </ui:DropDownButton>

                                    <!-- Secondary 样式 -->
                                    <ui:DropDownButton Content="Secondary"
                                                       Appearance="Secondary"
                                                       Command="{Binding HandleInteractionCommand}"
                                                       CommandParameter="Secondary样式"
                                                       Margin="8">
                                        <ui:DropDownButton.Flyout>
                                            <ContextMenu>
                                                <MenuItem Header="选项1" Command="{Binding HandleInteractionCommand}" CommandParameter="Secondary选项1"/>
                                                <MenuItem Header="选项2" Command="{Binding HandleInteractionCommand}" CommandParameter="Secondary选项2"/>
                                            </ContextMenu>
                                        </ui:DropDownButton.Flyout>
                                    </ui:DropDownButton>

                                    <!-- Success 样式 -->
                                    <ui:DropDownButton Content="Success"
                                                       Appearance="Success"
                                                       Command="{Binding HandleInteractionCommand}"
                                                       CommandParameter="Success样式"
                                                       Margin="8">
                                        <ui:DropDownButton.Flyout>
                                            <ContextMenu>
                                                <MenuItem Header="确认操作" Command="{Binding HandleInteractionCommand}" CommandParameter="确认操作"/>
                                                <MenuItem Header="完成任务" Command="{Binding HandleInteractionCommand}" CommandParameter="完成任务"/>
                                            </ContextMenu>
                                        </ui:DropDownButton.Flyout>
                                    </ui:DropDownButton>

                                    <!-- Danger 样式 -->
                                    <ui:DropDownButton Content="Danger"
                                                       Appearance="Danger"
                                                       Command="{Binding HandleInteractionCommand}"
                                                       CommandParameter="Danger样式"
                                                       Margin="8">
                                        <ui:DropDownButton.Flyout>
                                            <ContextMenu>
                                                <MenuItem Header="删除文件" Command="{Binding HandleInteractionCommand}" CommandParameter="删除文件"/>
                                                <MenuItem Header="清空数据" Command="{Binding HandleInteractionCommand}" CommandParameter="清空数据"/>
                                                <Separator/>
                                                <MenuItem Header="重置系统" Command="{Binding HandleInteractionCommand}" CommandParameter="重置系统"/>
                                            </ContextMenu>
                                        </ui:DropDownButton.Flyout>
                                    </ui:DropDownButton>
                                </WrapPanel>
                            </GroupBox>

                            <!-- 不同尺寸样式 -->
                            <GroupBox Header="不同尺寸样式" Padding="15" Margin="0,0,0,16">
                                <WrapPanel>
                                    <!-- 小型 DropDownButton -->
                                    <ui:DropDownButton Content="小型"
                                                       FontSize="12"
                                                       Padding="8,4"
                                                       Command="{Binding HandleInteractionCommand}"
                                                       CommandParameter="小型按钮"
                                                       Margin="8">
                                        <ui:DropDownButton.Icon>
                                            <ui:SymbolIcon Symbol="Settings24" FontSize="14"/>
                                        </ui:DropDownButton.Icon>
                                        <ui:DropDownButton.Flyout>
                                            <ContextMenu>
                                                <MenuItem Header="小型选项1" Command="{Binding HandleInteractionCommand}" CommandParameter="小型选项1"/>
                                                <MenuItem Header="小型选项2" Command="{Binding HandleInteractionCommand}" CommandParameter="小型选项2"/>
                                            </ContextMenu>
                                        </ui:DropDownButton.Flyout>
                                    </ui:DropDownButton>

                                    <!-- 标准 DropDownButton -->
                                    <ui:DropDownButton Content="标准"
                                                       Command="{Binding HandleInteractionCommand}"
                                                       CommandParameter="标准按钮"
                                                       Margin="8">
                                        <ui:DropDownButton.Icon>
                                            <ui:SymbolIcon Symbol="Settings24"/>
                                        </ui:DropDownButton.Icon>
                                        <ui:DropDownButton.Flyout>
                                            <ContextMenu>
                                                <MenuItem Header="标准选项1" Command="{Binding HandleInteractionCommand}" CommandParameter="标准选项1"/>
                                                <MenuItem Header="标准选项2" Command="{Binding HandleInteractionCommand}" CommandParameter="标准选项2"/>
                                            </ContextMenu>
                                        </ui:DropDownButton.Flyout>
                                    </ui:DropDownButton>

                                    <!-- 大型 DropDownButton -->
                                    <ui:DropDownButton Content="大型"
                                                       FontSize="16"
                                                       Padding="16,8"
                                                       Command="{Binding HandleInteractionCommand}"
                                                       CommandParameter="大型按钮"
                                                       Margin="8">
                                        <ui:DropDownButton.Icon>
                                            <ui:SymbolIcon Symbol="Settings24" FontSize="20"/>
                                        </ui:DropDownButton.Icon>
                                        <ui:DropDownButton.Flyout>
                                            <ContextMenu>
                                                <MenuItem Header="大型选项1" Command="{Binding HandleInteractionCommand}" CommandParameter="大型选项1"/>
                                                <MenuItem Header="大型选项2" Command="{Binding HandleInteractionCommand}" CommandParameter="大型选项2"/>
                                            </ContextMenu>
                                        </ui:DropDownButton.Flyout>
                                    </ui:DropDownButton>
                                </WrapPanel>
                            </GroupBox>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="样式代码示例"
                                Language="XAML"
                                Description="展示如何使用 Zylo.WPF 中的 DropDownButton 样式"
                                ShowTabs="True"
                                XamlCode="{Binding StylesXamlExample}"
                                IsExpanded="False"
                                Margin="0,16,0,0"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 数据绑定示例 -->
                    <ui:CardExpander Header="📊 数据绑定示例" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示如何使用数据绑定动态生成 DropDownButton 的菜单项"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 数据绑定控件展示区域 -->
                            <WrapPanel>
                                <!-- 最近文件 DropDownButton -->
                                <ui:DropDownButton Content="最近文件"
                                                   Command="{Binding HandleInteractionCommand}"
                                                   CommandParameter="最近文件"
                                                   Margin="8">
                                    <ui:DropDownButton.Icon>
                                        <ui:SymbolIcon Symbol="History24"/>
                                    </ui:DropDownButton.Icon>
                                    <ui:DropDownButton.Flyout>
                                        <ContextMenu ItemsSource="{Binding RecentFiles}">
                                            <ContextMenu.ItemTemplate>
                                                <DataTemplate>
                                                    <MenuItem Header="{Binding Name}"
                                                              Command="{Binding DataContext.HandleRecentFileSelectionCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                              CommandParameter="{Binding}">
                                                        <MenuItem.Icon>
                                                            <ui:SymbolIcon Symbol="{Binding Icon}"/>
                                                        </MenuItem.Icon>
                                                        <MenuItem.ToolTip>
                                                            <StackPanel>
                                                                <TextBlock Text="{Binding Name}" FontWeight="Bold"/>
                                                                <TextBlock Text="{Binding Path}" FontSize="11" Foreground="Gray"/>
                                                                <TextBlock Text="{Binding DisplayTime}" FontSize="10" Foreground="Gray"/>
                                                            </StackPanel>
                                                        </MenuItem.ToolTip>
                                                    </MenuItem>
                                                </DataTemplate>
                                            </ContextMenu.ItemTemplate>
                                        </ContextMenu>
                                    </ui:DropDownButton.Flyout>
                                </ui:DropDownButton>

                                <!-- 导出格式 DropDownButton -->
                                <ui:DropDownButton Content="导出格式"
                                                   Command="{Binding HandleInteractionCommand}"
                                                   CommandParameter="导出格式"
                                                   Margin="8">
                                    <ui:DropDownButton.Icon>
                                        <ui:SymbolIcon Symbol="Share24"/>
                                    </ui:DropDownButton.Icon>
                                    <ui:DropDownButton.Flyout>
                                        <ContextMenu ItemsSource="{Binding ExportFormats}">
                                            <ContextMenu.ItemTemplate>
                                                <DataTemplate>
                                                    <MenuItem Header="{Binding Name}"
                                                              Command="{Binding DataContext.HandleExportFormatSelectionCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                              CommandParameter="{Binding}">
                                                        <MenuItem.Icon>
                                                            <ui:SymbolIcon Symbol="{Binding Icon}"/>
                                                        </MenuItem.Icon>
                                                        <MenuItem.ToolTip>
                                                            <StackPanel>
                                                                <TextBlock Text="{Binding Name}" FontWeight="Bold"/>
                                                                <TextBlock Text="{Binding Description}" FontSize="12"/>
                                                                <TextBlock Text="{Binding Extension}" FontSize="11" Foreground="Gray"/>
                                                            </StackPanel>
                                                        </MenuItem.ToolTip>
                                                    </MenuItem>
                                                </DataTemplate>
                                            </ContextMenu.ItemTemplate>
                                        </ContextMenu>
                                    </ui:DropDownButton.Flyout>
                                </ui:DropDownButton>

                                <!-- 工具选择 DropDownButton -->
                                <ui:DropDownButton Content="开发工具"
                                                   Command="{Binding HandleInteractionCommand}"
                                                   CommandParameter="开发工具"
                                                   Margin="8">
                                    <ui:DropDownButton.Icon>
                                        <ui:SymbolIcon Symbol="Wrench24"/>
                                    </ui:DropDownButton.Icon>
                                    <ui:DropDownButton.Flyout>
                                        <ContextMenu>
                                            <ContextMenu.Resources>
                                                <CollectionViewSource x:Key="GroupedTools" Source="{Binding ToolItems}">
                                                    <CollectionViewSource.GroupDescriptions>
                                                        <PropertyGroupDescription PropertyName="Category"/>
                                                    </CollectionViewSource.GroupDescriptions>
                                                </CollectionViewSource>
                                            </ContextMenu.Resources>
                                            <ContextMenu.ItemsSource>
                                                <Binding Source="{StaticResource GroupedTools}"/>
                                            </ContextMenu.ItemsSource>
                                            <ContextMenu.GroupStyle>
                                                <GroupStyle>
                                                    <GroupStyle.HeaderTemplate>
                                                        <DataTemplate>
                                                            <TextBlock Text="{Binding Name}" FontWeight="Bold" FontSize="12" Margin="0,4,0,2"/>
                                                        </DataTemplate>
                                                    </GroupStyle.HeaderTemplate>
                                                </GroupStyle>
                                            </ContextMenu.GroupStyle>
                                            <ContextMenu.ItemTemplate>
                                                <DataTemplate>
                                                    <MenuItem Header="{Binding Name}"
                                                              Command="{Binding DataContext.HandleToolSelectionCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                              CommandParameter="{Binding}">
                                                        <MenuItem.Icon>
                                                            <ui:SymbolIcon Symbol="{Binding Icon}"/>
                                                        </MenuItem.Icon>
                                                        <MenuItem.ToolTip>
                                                            <StackPanel>
                                                                <TextBlock Text="{Binding Name}" FontWeight="Bold"/>
                                                                <TextBlock Text="{Binding Description}" FontSize="12"/>
                                                                <TextBlock Text="{Binding Category}" FontSize="11" Foreground="Gray"/>
                                                            </StackPanel>
                                                        </MenuItem.ToolTip>
                                                    </MenuItem>
                                                </DataTemplate>
                                            </ContextMenu.ItemTemplate>
                                        </ContextMenu>
                                    </ui:DropDownButton.Flyout>
                                </ui:DropDownButton>
                            </WrapPanel>

                            <!-- 动态操作演示 -->
                            <GroupBox Header="动态操作演示" Padding="15" Margin="0,16,0,0">
                                <StackPanel>
                                    <TextBlock Text="演示运行时动态添加/删除菜单项" FontSize="12" Margin="0,0,0,10"/>

                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                                        <ui:Button Content="添加文件"
                                                   Command="{Binding AddRecentFileCommand}"
                                                   CommandParameter="新文件.txt"
                                                   Appearance="Primary"
                                                   Margin="0,0,8,0"/>
                                        <ui:Button Content="清空列表"
                                                   Command="{Binding ClearRecentFilesCommand}"
                                                   Appearance="Secondary"/>
                                    </StackPanel>

                                    <TextBlock Text="{Binding RecentFiles.Count, StringFormat='当前文件数量: {0}'}"
                                               FontSize="12"
                                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                                </StackPanel>
                            </GroupBox>

                            <!-- 当前选择状态显示 -->
                            <GroupBox Header="当前选择状态" Padding="15" Margin="0,16,0,0">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <StackPanel Grid.Column="0">
                                        <TextBlock Text="选择的导出格式:" FontWeight="Bold" FontSize="12" Margin="0,0,0,4"/>
                                        <Border Background="{DynamicResource ControlFillColorInputActiveBrush}"
                                                BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                                BorderThickness="1"
                                                CornerRadius="4"
                                                Padding="8">
                                            <StackPanel Orientation="Horizontal">
                                                <ui:SymbolIcon Symbol="{Binding SelectedExportFormat.Icon}" FontSize="16" Margin="0,0,8,0"/>
                                                <StackPanel>
                                                    <TextBlock Text="{Binding SelectedExportFormat.Name}" FontWeight="Medium"/>
                                                    <TextBlock Text="{Binding SelectedExportFormat.Extension}" FontSize="10" Foreground="Gray"/>
                                                </StackPanel>
                                            </StackPanel>
                                        </Border>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1" Margin="16,0,0,0">
                                        <TextBlock Text="最近访问的文件:" FontWeight="Bold" FontSize="12" Margin="0,0,0,4"/>
                                        <Border Background="{DynamicResource ControlFillColorInputActiveBrush}"
                                                BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                                BorderThickness="1"
                                                CornerRadius="4"
                                                Padding="8">
                                            <StackPanel Orientation="Horizontal">
                                                <ui:SymbolIcon Symbol="{Binding RecentFiles[0].Icon}" FontSize="16" Margin="0,0,8,0"/>
                                                <StackPanel>
                                                    <TextBlock Text="{Binding RecentFiles[0].Name}" FontWeight="Medium"/>
                                                    <TextBlock Text="{Binding RecentFiles[0].DisplayTime}" FontSize="10" Foreground="Gray"/>
                                                </StackPanel>
                                            </StackPanel>
                                        </Border>
                                    </StackPanel>
                                </Grid>
                            </GroupBox>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="数据绑定代码示例"
                                Language="XAML"
                                Description="展示如何使用数据绑定动态生成 DropDownButton 菜单项"
                                ShowTabs="True"
                                XamlCode="{Binding DataBindingXamlExample}"
                                CSharpCode="{Binding DataBindingCSharpExample}"
                                IsExpanded="False"
                                Margin="0,16,0,0"/>
                        </StackPanel>
                    </ui:CardExpander>

                </StackPanel>
            </Border>

            <!-- 底部操作栏 -->
            <Border Grid.Row="2" 
                    Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Padding="16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 统计信息 -->
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock Text="{Binding InteractionCount, StringFormat='交互次数: {0}'}" 
                                   FontSize="14" 
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   Margin="0,0,20,0"/>
                        <TextBlock Text="{Binding LastAction, StringFormat='最后操作: {0}'}" 
                                   FontSize="14" 
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                    </StackPanel>

                    <!-- 操作按钮 -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <ui:Button Content="重置计数" 
                                   Command="{Binding ResetCountCommand}"
                                   Appearance="Secondary"
                                   Margin="0,0,8,0"/>
                        <ui:Button Content="清除状态" 
                                   Command="{Binding ClearStatusCommand}"
                                   Appearance="Secondary"/>
                    </StackPanel>
                </Grid>
            </Border>

        </Grid>
    </ScrollViewer>
</UserControl>
