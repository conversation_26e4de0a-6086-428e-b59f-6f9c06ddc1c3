<!-- NumberBox 数据验证示例 -->
<StackPanel>
    <!-- 范围验证 -->
    <StackPanel Margin="0,0,0,16">
        <TextBlock Text="范围验证 (1-100)" FontWeight="Medium" Margin="0,0,0,4"/>
        <ui:NumberBox Value="{Binding RangeValue, UpdateSourceTrigger=PropertyChanged}"
                      PlaceholderText="输入1-100的数值"
                      Minimum="1"
                      Maximum="100"/>
        <TextBlock Text="{Binding RangeValidationMessage}" 
                   FontSize="11" 
                   Foreground="{Binding RangeValidationColor}"
                   Margin="0,2,0,0"/>
    </StackPanel>

    <!-- 必填验证 -->
    <StackPanel Margin="0,0,0,16">
        <TextBlock Text="必填验证" FontWeight="Medium" Margin="0,0,0,4"/>
        <ui:NumberBox Value="{Binding RequiredValue, UpdateSourceTrigger=PropertyChanged}"
                      PlaceholderText="此字段为必填"/>
        <TextBlock Text="{Binding RequiredValidationMessage}" 
                   FontSize="11" 
                   Foreground="{Binding RequiredValidationColor}"
                   Margin="0,2,0,0"/>
    </StackPanel>

    <!-- 自定义验证 -->
    <StackPanel Margin="0,0,0,16">
        <TextBlock Text="自定义验证 (偶数)" FontWeight="Medium" Margin="0,0,0,4"/>
        <ui:NumberBox Value="{Binding CustomValue, UpdateSourceTrigger=PropertyChanged}"
                      PlaceholderText="输入偶数"/>
        <TextBlock Text="{Binding CustomValidationMessage}" 
                   FontSize="11" 
                   Foreground="{Binding CustomValidationColor}"
                   Margin="0,2,0,0"/>
    </StackPanel>

    <!-- 验证操作 -->
    <StackPanel Orientation="Horizontal">
        <ui:Button Content="验证所有"
                   Appearance="Primary"
                   Command="{Binding ValidateAllCommand}"
                   Margin="0,0,8,0"/>
        <ui:Button Content="清除验证"
                   Appearance="Secondary"
                   Command="{Binding ClearValidationCommand}"/>
    </StackPanel>

    <!-- 验证结果显示 -->
    <Border Background="{DynamicResource ControlFillColorTertiaryBrush}"
            CornerRadius="4"
            Padding="12"
            Margin="0,16,0,0">
        <StackPanel>
            <TextBlock Text="验证结果:" FontWeight="Medium" FontSize="12" Margin="0,0,0,4"/>
            <TextBlock Text="{Binding ValidationSummary}" 
                       FontSize="11"
                       TextWrapping="Wrap"/>
        </StackPanel>
    </Border>
</StackPanel>
