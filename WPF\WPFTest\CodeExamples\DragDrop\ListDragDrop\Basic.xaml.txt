<!-- gong-wpf-dragdrop 列表拖拽基础示例 -->
<Grid>
    <Grid.ColumnDefinitions>
        <ColumnDefinition Width="*"/>
        <ColumnDefinition Width="*"/>
    </Grid.ColumnDefinitions>
    
    <!-- 源列表 - 可拖拽 -->
    <ListView Grid.Column="0"
              ItemsSource="{Binding SourceItems}"
              dd:DragDrop.IsDragSource="True"
              dd:DragDrop.UseDefaultDragAdorner="True"
              dd:DragDrop.DefaultDragAdornerOpacity="0.7">
        <ListView.ItemTemplate>
            <DataTemplate>
                <Border Background="LightBlue" 
                        Padding="8" 
                        Margin="2"
                        CornerRadius="4">
                    <StackPanel>
                        <TextBlock Text="{Binding Name}" FontWeight="Medium"/>
                        <TextBlock Text="{Binding Description}" FontSize="11"/>
                    </StackPanel>
                </Border>
            </DataTemplate>
        </ListView.ItemTemplate>
    </ListView>
    
    <!-- 目标列表 - 可放置 -->
    <ListView Grid.Column="1"
              ItemsSource="{Binding TargetItems}"
              dd:DragDrop.IsDropTarget="True"
              dd:DragDrop.DropTargetAdorner="Insert">
        <ListView.ItemTemplate>
            <DataTemplate>
                <Border Background="LightGreen" 
                        Padding="8" 
                        Margin="2"
                        CornerRadius="4">
                    <StackPanel>
                        <TextBlock Text="{Binding Name}" FontWeight="Medium"/>
                        <TextBlock Text="{Binding Description}" FontSize="11"/>
                    </StackPanel>
                </Border>
            </DataTemplate>
        </ListView.ItemTemplate>
    </ListView>
</Grid>

<!-- 命名空间声明 -->
xmlns:dd="urn:gong-wpf-dragdrop"
