using System.ComponentModel.DataAnnotations;
using System.IO;
using CommunityToolkit.Mvvm.ComponentModel;

namespace WPFTest.Models.SQLite;

/// <summary>
/// SQLite 数据库文件模型 - 支持文件信息管理和连接状态监控
/// </summary>
/// <remarks>
/// 设计思路：
/// - 参考 DwgFileTypeModel 的设计模式
/// - 支持文件路径管理和验证
/// - 提供连接状态监控
/// - 支持文件信息缓存
/// - 可扩展的数据库属性
/// </remarks>
public partial class SqliteDbFileModel : ObservableValidator
{
    #region 基本属性

    /// <summary>
    /// 唯一标识ID
    /// </summary>
    [ObservableProperty]
    public partial int Id { get; set; }

    /// <summary>
    /// 数据库文件完整路径
    /// </summary>
    [Required(ErrorMessage = "数据库文件路径不能为空")]
    [ObservableProperty]
    public partial string FilePath { get; set; } = string.Empty;

    /// <summary>
    /// 数据库文件名（不含路径）
    /// </summary>
    [ObservableProperty]
    public partial string FileName { get; set; } = string.Empty;

    /// <summary>
    /// 数据库显示名称（用户自定义）
    /// </summary>
    [MaxLength(100)]
    [ObservableProperty]
    public partial string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// 数据库描述
    /// </summary>
    [MaxLength(500)]
    [ObservableProperty]
    public partial string Description { get; set; } = string.Empty;

    #endregion

    #region 文件信息

    /// <summary>
    /// 文件大小（字节）
    /// </summary>
    [ObservableProperty]
    public partial long FileSize { get; set; }

    /// <summary>
    /// 文件创建时间
    /// </summary>
    [ObservableProperty]
    public partial DateTime CreatedDate { get; set; }

    /// <summary>
    /// 文件最后修改时间
    /// </summary>
    [ObservableProperty]
    public partial DateTime ModifiedDate { get; set; }

    /// <summary>
    /// 最后访问时间
    /// </summary>
    [ObservableProperty]
    public partial DateTime LastAccessDate { get; set; }

    #endregion

    #region 连接状态

    /// <summary>
    /// 连接状态
    /// </summary>
    [ObservableProperty]
    public partial SqliteConnectionStatus ConnectionStatus { get; set; } = SqliteConnectionStatus.Unknown;

    /// <summary>
    /// 最后连接测试时间
    /// </summary>
    [ObservableProperty]
    public partial DateTime? LastConnectionTest { get; set; }

    /// <summary>
    /// 连接错误信息
    /// </summary>
    [ObservableProperty]
    public partial string ConnectionError { get; set; } = string.Empty;

    /// <summary>
    /// 是否为收藏数据库
    /// </summary>
    [ObservableProperty]
    public partial bool IsFavorite { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    [ObservableProperty]
    public partial bool IsEnabled { get; set; } = true;

    #endregion

    #region 数据库信息

    /// <summary>
    /// 数据库版本
    /// </summary>
    [ObservableProperty]
    public partial string DatabaseVersion { get; set; } = string.Empty;

    /// <summary>
    /// 表数量
    /// </summary>
    [ObservableProperty]
    public partial int TableCount { get; set; }

    /// <summary>
    /// 数据库编码
    /// </summary>
    [ObservableProperty]
    public partial string Encoding { get; set; } = string.Empty;

    /// <summary>
    /// 页面大小
    /// </summary>
    [ObservableProperty]
    public partial int PageSize { get; set; }

    #endregion

    #region 计算属性

    /// <summary>
    /// 文件大小显示文本
    /// </summary>
    public string FileSizeText => FormatFileSize(FileSize);

    /// <summary>
    /// 连接状态图标
    /// </summary>
    public string ConnectionStatusIcon => ConnectionStatus switch
    {
        SqliteConnectionStatus.Connected => "✅",
        SqliteConnectionStatus.Disconnected => "❌",
        SqliteConnectionStatus.Error => "⚠️",
        SqliteConnectionStatus.Testing => "🔄",
        _ => "❓"
    };

    /// <summary>
    /// 连接状态颜色
    /// </summary>
    public string ConnectionStatusColor => ConnectionStatus switch
    {
        SqliteConnectionStatus.Connected => "#28A745",
        SqliteConnectionStatus.Disconnected => "#DC3545",
        SqliteConnectionStatus.Error => "#FFC107",
        SqliteConnectionStatus.Testing => "#007BFF",
        _ => "#6C757D"
    };

    /// <summary>
    /// 文件是否存在
    /// </summary>
    public bool FileExists => !string.IsNullOrEmpty(FilePath) && File.Exists(FilePath);

    /// <summary>
    /// 显示标题（优先显示自定义名称）
    /// </summary>
    public string DisplayTitle => !string.IsNullOrEmpty(DisplayName) ? DisplayName : FileName;

    #endregion

    #region 构造函数

    /// <summary>
    /// 默认构造函数
    /// </summary>
    public SqliteDbFileModel()
    {
        // 监听属性变化
        PropertyChanged += OnPropertyChanged;
    }

    /// <summary>
    /// 从文件路径创建实例
    /// </summary>
    /// <param name="filePath">SQLite 文件路径</param>
    public SqliteDbFileModel(string filePath) : this()
    {
        UpdateFromFilePath(filePath);
    }

    #endregion

    #region 公共方法

    /// <summary>
    /// 从文件路径更新信息
    /// </summary>
    /// <param name="filePath">文件路径</param>
    public void UpdateFromFilePath(string filePath)
    {
        if (string.IsNullOrEmpty(filePath)) return;

        FilePath = filePath;
        FileName = Path.GetFileName(filePath);
        
        if (string.IsNullOrEmpty(DisplayName))
        {
            DisplayName = Path.GetFileNameWithoutExtension(filePath);
        }

        UpdateFileInfo();
    }

    /// <summary>
    /// 更新文件信息
    /// </summary>
    public void UpdateFileInfo()
    {
        if (!FileExists) return;

        try
        {
            var fileInfo = new FileInfo(FilePath);
            FileSize = fileInfo.Length;
            CreatedDate = fileInfo.CreationTime;
            ModifiedDate = fileInfo.LastWriteTime;
            LastAccessDate = fileInfo.LastAccessTime;
        }
        catch
        {
            // 忽略文件信息获取错误
        }
    }

    /// <summary>
    /// 克隆对象
    /// </summary>
    /// <returns>克隆的对象</returns>
    public SqliteDbFileModel Clone()
    {
        return new SqliteDbFileModel
        {
            Id = Id,
            FilePath = FilePath,
            FileName = FileName,
            DisplayName = DisplayName,
            Description = Description,
            FileSize = FileSize,
            CreatedDate = CreatedDate,
            ModifiedDate = ModifiedDate,
            LastAccessDate = LastAccessDate,
            ConnectionStatus = ConnectionStatus,
            LastConnectionTest = LastConnectionTest,
            ConnectionError = ConnectionError,
            IsFavorite = IsFavorite,
            IsEnabled = IsEnabled,
            DatabaseVersion = DatabaseVersion,
            TableCount = TableCount,
            Encoding = Encoding,
            PageSize = PageSize
        };
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 属性变化处理
    /// </summary>
    private void OnPropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        switch (e.PropertyName)
        {
            case nameof(FilePath):
                if (!string.IsNullOrEmpty(FilePath))
                {
                    FileName = Path.GetFileName(FilePath);
                    UpdateFileInfo();
                }
                break;
        }
    }

    /// <summary>
    /// 格式化文件大小
    /// </summary>
    private static string FormatFileSize(long bytes)
    {
        if (bytes == 0) return "0 B";

        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        int order = 0;
        double size = bytes;

        while (size >= 1024 && order < sizes.Length - 1)
        {
            order++;
            size /= 1024;
        }

        return $"{size:0.##} {sizes[order]}";
    }

    #endregion
}

/// <summary>
/// SQLite 连接状态枚举
/// </summary>
public enum SqliteConnectionStatus
{
    /// <summary>
    /// 未知状态
    /// </summary>
    Unknown,

    /// <summary>
    /// 已连接
    /// </summary>
    Connected,

    /// <summary>
    /// 未连接
    /// </summary>
    Disconnected,

    /// <summary>
    /// 连接错误
    /// </summary>
    Error,

    /// <summary>
    /// 正在测试连接
    /// </summary>
    Testing
}
