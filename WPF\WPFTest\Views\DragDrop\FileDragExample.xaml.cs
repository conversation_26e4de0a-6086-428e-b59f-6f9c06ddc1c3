using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using WPFTest.ViewModels.DragDrop;

namespace WPFTest.Views.DragDrop
{
    /// <summary>
    /// FileDragExample.xaml 的交互逻辑
    /// </summary>
    public partial class FileDragExample : UserControl
    {
        public FileDragExample()
        {
            InitializeComponent();
        }

        private Point _startPoint;
        private bool _isDragging;

        /// <summary>
        /// ListBox 双击事件处理器
        /// </summary>
        private void ListBox_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            // 确保双击的是 ListBox 项目，而不是空白区域
            if (e.OriginalSource is FrameworkElement element)
            {
                // 查找最近的 ListBoxItem
                var listBoxItem = element.FindAncestor<ListBoxItem>();
                if (listBoxItem != null && listBoxItem.DataContext is DwgFileInfo fileInfo)
                {
                    // 获取 ViewModel 并执行打开文件命令
                    if (DataContext is FileDragExampleViewModel viewModel)
                    {
                        viewModel.OpenFileCommand.Execute(fileInfo);
                    }
                }
            }
        }

        /// <summary>
        /// 鼠标按下事件 - 开始拖拽检测
        /// </summary>
        private void ListBox_MouseDown(object sender, MouseButtonEventArgs e)
        {
            if (e.LeftButton == MouseButtonState.Pressed)
            {
                _startPoint = e.GetPosition(null);
                _isDragging = false;
            }
        }

        /// <summary>
        /// 鼠标移动事件 - 检测是否开始拖拽
        /// </summary>
        private void ListBox_MouseMove(object sender, MouseEventArgs e)
        {
            if (e.LeftButton == MouseButtonState.Pressed && !_isDragging)
            {
                Point currentPosition = e.GetPosition(null);

                // 检查鼠标移动距离是否足够开始拖拽
                if (Math.Abs(currentPosition.X - _startPoint.X) > SystemParameters.MinimumHorizontalDragDistance ||
                    Math.Abs(currentPosition.Y - _startPoint.Y) > SystemParameters.MinimumVerticalDragDistance)
                {
                    StartNativeDragOperation(sender as ListBox, e);
                }
            }
        }

        /// <summary>
        /// 鼠标释放事件
        /// </summary>
        private void ListBox_MouseUp(object sender, MouseButtonEventArgs e)
        {
            _isDragging = false;
        }

        /// <summary>
        /// 启动原生拖拽操作
        /// </summary>
        private void StartNativeDragOperation(ListBox? listBox, MouseEventArgs e)
        {
            if (listBox == null || DataContext is not FileDragExampleViewModel viewModel)
                return;

            _isDragging = true;

            try
            {
                // 获取当前选中的文件
                var selectedFiles = new List<string>();

                if (viewModel.SelectedFiles.Count > 0)
                {
                    selectedFiles.AddRange(viewModel.SelectedFiles.Select(f => f.FullPath));
                }
                else if (viewModel.SelectedFile != null)
                {
                    selectedFiles.Add(viewModel.SelectedFile.FullPath);
                }

                if (selectedFiles.Count == 0) return;

                // 验证文件存在
                var validFiles = selectedFiles.Where(File.Exists).ToArray();
                if (validFiles.Length == 0) return;

                // 创建数据对象
                var dataObject = new DataObject();
                dataObject.SetData(DataFormats.FileDrop, validFiles);

                // 如果只有一个文件，也添加文本格式
                if (validFiles.Length == 1)
                {
                    dataObject.SetData(DataFormats.Text, validFiles[0]);
                    dataObject.SetData(DataFormats.UnicodeText, validFiles[0]);
                }

                // 启动拖拽操作
                var result = System.Windows.DragDrop.DoDragDrop(listBox, dataObject, DragDropEffects.Copy | DragDropEffects.Move);

                // 更新状态
                var fileCount = validFiles.Length;
                var message = fileCount == 1
                    ? $"🎯 拖拽完成: {Path.GetFileName(validFiles[0])}"
                    : $"🎯 拖拽完成: {fileCount} 个文件";

                viewModel.StatusMessage = message;

                // 记录拖拽结果
                System.Diagnostics.Debug.WriteLine($"拖拽结果: {result}");
            }
            catch (Exception ex)
            {
                if (viewModel != null)
                {
                    viewModel.StatusMessage = $"❌ 拖拽失败: {ex.Message}";
                }
                System.Diagnostics.Debug.WriteLine($"拖拽异常: {ex.Message}");
            }
            finally
            {
                _isDragging = false;
            }
        }
    }

    /// <summary>
    /// 扩展方法：查找父级元素
    /// </summary>
    public static class VisualTreeExtensions
    {
        public static T? FindAncestor<T>(this DependencyObject current) where T : DependencyObject
        {
            while (current != null)
            {
                if (current is T ancestor)
                {
                    return ancestor;
                }
                current = VisualTreeHelper.GetParent(current);
            }
            return null;
        }
    }
}
