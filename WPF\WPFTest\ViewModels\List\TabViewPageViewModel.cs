using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.Messaging;
using System.Collections.ObjectModel;
using Wpf.Ui.Controls;
using WPFTest.Models;
using Zylo.WPF.Enums;
using Zylo.WPF.Models.TreeNode;
using Zylo.YLog.Runtime;


namespace WPFTest.ViewModels.List;

/// <summary>
/// TabView页面ViewModel - 使用 Prism 依赖注入和 CommunityToolkit.Mvvm
/// </summary>
public partial class TabViewPageViewModel : ObservableObject
{
    #region 私有字段和依赖注入服务

    // 🔥 使用最详细的日志级别 - 输出所有信息
    private readonly YLoggerInstance _logger = YLogger.ForDebug<TabViewPageViewModel>();

    // 📨 消息传递服务 - 通过依赖注入获取
    private readonly IMessenger _messenger;

    #endregion

    #region 属性

    /// <summary>
    /// 标签页集合
    /// </summary>
    [ObservableProperty]
    [NotifyPropertyChangedFor(nameof(TabItemsInfo))]     // 自动通知信息摘要
    [NotifyPropertyChangedFor(nameof(HasTabs))]          // 自动通知是否有标签页
    private ObservableCollection<TabItemData> tabItems = new();

    /// <summary>
    /// 选中的标签页
    /// </summary>
    [ObservableProperty]
    [NotifyPropertyChangedFor(nameof(HasSelectedTab))]  // 自动通知计算属性
    private TabItemData? selectedTab;

    /// <summary>
    /// 是否正在加载
    /// </summary>
    [ObservableProperty]
    [NotifyPropertyChangedFor(nameof(CanPerformActions))]
    [NotifyPropertyChangedFor(nameof(CanReloadData))]
    private bool isLoading;

    /// <summary>
    /// 状态消息
    /// </summary>
    [ObservableProperty]
    private string statusMessage = "准备就绪";

    /// <summary>
    /// 搜索文本
    /// </summary>
    [ObservableProperty]
    private string searchText = string.Empty;

    /// <summary>
    /// 过滤后的标签页集合
    /// </summary>
    [ObservableProperty]
    private ObservableCollection<TabItemData> filteredTabItems = new();

    /// <summary>
    /// 是否可以执行操作（非加载状态）
    /// </summary>
    public bool CanPerformActions => !IsLoading;

    /// <summary>
    /// 是否可以重新加载数据（按钮启用状态）
    /// </summary>
    public bool CanReloadData => !IsLoading;

    /// <summary>
    /// 标签页信息
    /// </summary>
    public string TabItemsInfo => $"共 {TabItems.Count} 个标签页";

    /// <summary>
    /// 是否有选中的标签页 - 用于UI绑定（按照指南最佳实践）
    /// </summary>
    public bool HasSelectedTab => SelectedTab != null;

    /// <summary>
    /// 是否有标签页 - 用于UI绑定
    /// </summary>
    public bool HasTabs => TabItems.Count > 0;

    #endregion

    #region 构造函数

    /// <summary>
    /// 构造函数 - 使用依赖注入初始化服务
    /// </summary>
    /// <param name="messenger">消息传递服务（可选）</param>
    public TabViewPageViewModel(IMessenger? messenger = null)
    {
        try
        {
            _logger.Info("🚀 TabViewPageViewModel 构造函数开始 - 支持依赖注入");

            // 保存注入的服务（如果提供）
            _messenger = messenger ?? WeakReferenceMessenger.Default;
            _logger.Info("📨 消息传递服务配置完成");

            // 初始化集合
            TabItems = new ObservableCollection<TabItemData>();
            FilteredTabItems = new ObservableCollection<TabItemData>();
            _logger.Info("📋 集合初始化完成");

            _logger.Info("🌱 开始创建TabView测试数据");
            // 创建测试数据
            CreateTestDataProfessional();

            // 监听搜索文本变化
            PropertyChanged += (s, e) =>
            {
                if (e.PropertyName == nameof(SearchText))
                {
                    ApplyFilter();
                }
            };

            StatusMessage = $"✅ 初始化完成，{TabItemsInfo}";
            _logger.Info($"🎯 TabView页面ViewModel初始化完成 - {TabItemsInfo}");
            _logger.Info($"📊 TabItems.Count = {TabItems.Count}, FilteredTabItems.Count = {FilteredTabItems.Count}");

            // 📨 示例：发送初始化完成消息
            _messenger.Send(new TabViewInitializedMessage($"TabView 页面已初始化，共 {TabItems.Count} 个标签页"));
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ TabViewPageViewModel 构造函数失败: {ex}");
            StatusMessage = $"❌ 初始化失败: {ex.Message}";
        }
    }

    #endregion

    #region 命令

    /// <summary>
    /// 重新加载数据命令
    /// </summary>
    [RelayCommand]
    private async Task ReloadData()
    {
        try
        {
            IsLoading = true;
            StatusMessage = "正在重新加载标签页数据...";

            await Task.Delay(200); // 模拟异步操作

            TabItems.Clear();
            CreateTestDataProfessional();
            ApplyFilter();

            StatusMessage = $"✅ 已重新加载标签页数据，{TabItemsInfo}";
            _logger.Info("重新加载标签页数据");
        }
        catch (Exception ex)
        {
            StatusMessage = $"❌ 重新加载失败: {ex.Message}";
            _logger.Error($"重新加载标签页数据失败: {ex}");
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 添加标签页命令
    /// </summary>
    [RelayCommand]
    private void AddTab()
    {
        try
        {
            var nextId = GetNextId();
            var newTab = new TabItemData
            {
                Id = nextId,
                Header = $"新标签页 {DateTime.Now:HH:mm:ss}",
                Content = $"这是第 {nextId} 个标签页的内容",
                NodeType = "document",
                IsClosable = true,
                WpfUiSymbol = SymbolRegular.Document24
            };

            TabItems.Add(newTab);
            SelectedTab = newTab;
            ApplyFilter();

            StatusMessage = $"✅ 已添加新标签页，{TabItemsInfo}";
            _logger.Info($"添加新标签页: {newTab.Header}");
        }
        catch (Exception ex)
        {
            StatusMessage = $"❌ 添加标签页失败: {ex.Message}";
            _logger.Error($"添加标签页失败: {ex}");
        }
    }

    /// <summary>
    /// 选择标签页命令
    /// </summary>
    [RelayCommand]
    private void SelectTab(TabItemData? tab)
    {
        if (tab == null) return;

        try
        {
            SelectedTab = tab;
            StatusMessage = $"✅ 已选择标签页: {tab.Header}";
            _logger.Info($"选择标签页: {tab.Header}");
        }
        catch (Exception ex)
        {
            StatusMessage = $"❌ 选择标签页失败: {ex.Message}";
            _logger.Error($"选择标签页失败: {ex}");
        }
    }

    /// <summary>
    /// 关闭标签页命令
    /// </summary>
    [RelayCommand]
    private void CloseTab(TabItemData? tab)
    {
        if (tab == null || !tab.IsClosable) return;

        try
        {
            TabItems.Remove(tab);

            // 如果关闭的是当前选中的标签页，选择下一个
            if (SelectedTab == tab)
            {
                SelectedTab = TabItems.FirstOrDefault();
            }

            ApplyFilter();
            StatusMessage = $"✅ 已关闭标签页: {tab.Header}，{TabItemsInfo}";
            _logger.Info($"关闭标签页: {tab.Header}");
        }
        catch (Exception ex)
        {
            StatusMessage = $"❌ 关闭标签页失败: {ex.Message}";
            _logger.Error($"关闭标签页失败: {ex}");
        }
    }

    #endregion

    #region 搜索和过滤

    /// <summary>
    /// 当搜索文本改变时
    /// </summary>
    partial void OnSearchTextChanged(string value)
    {
        ApplyFilter();
    }

    /// <summary>
    /// 应用过滤器 - 修复数据绑定问题
    /// </summary>
    private void ApplyFilter()
    {
        // 清空现有集合
        FilteredTabItems.Clear();

        // 获取过滤后的数据
        IEnumerable<TabItemData> itemsToShow;
        if (string.IsNullOrWhiteSpace(SearchText))
        {
            itemsToShow = TabItems;
        }
        else
        {
            itemsToShow = TabItems.Where(tab =>
                tab.Header.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                tab.Content.Contains(SearchText, StringComparison.OrdinalIgnoreCase));
        }

        // 添加到现有集合中，保持数据绑定
        foreach (var item in itemsToShow)
        {
            FilteredTabItems.Add(item);
        }

        OnPropertyChanged(nameof(TabItemsInfo));
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 创建测试数据 - 参考TreeViewPageViewModel的专业组装方式
    /// </summary>
    private void CreateTestDataProfessional()
    {
        _logger.Info("🔄 开始创建TabView测试数据 - 使用专业组装方式");

        // 🎯 定义标签页数据结构 - 四种图标类型演示
        var tabDataArray = new TabItemDataInfo[]
        {
            // === WPF-UI图标标签页 ===
            new("1", "用户管理", "用户管理系统界面", "user-management", true)
                { WpfUiSymbol = SymbolRegular.Person24 },
            new("2", "系统设置", "系统配置和设置界面", "settings", true)
                { WpfUiSymbol = SymbolRegular.Settings24 },
            new("3", "数据分析", "数据分析和报表界面", "analytics", true)
                { WpfUiSymbol = SymbolRegular.ChartMultiple24 },

            // === Zylo图标标签页 ===
            new("4", "Zylo图标1", "Zylo自定义图标演示", "zylo-test", true)
                { ZyloSymbol = ZyloSymbol.ICO },
            new("5", "Zylo图标2", "另一个Zylo图标演示", "zylo-test", true)
                { ZyloSymbol = ZyloSymbol.ICO },

            // === Emoji图标标签页 ===
            new("6", "工作台", "工作台界面", "workspace", true)
                { Emoji = "💼" },
            new("7", "学习中心", "学习资源中心", "learning", true)
                { Emoji = "📚" },
            new("8", "娱乐区", "娱乐和游戏区域", "entertainment", true)
                { Emoji = "🎮" },

            // === 默认图标标签页 ===
            new("9", "JavaScript编辑器", "JavaScript代码编辑器", "javascript", true),
            new("10", "Python编辑器", "Python代码编辑器", "python", true),
            new("11", "数据库管理", "MySQL数据库管理", "mysql", false), // 不可关闭
            new("12", "API文档", "API接口文档", "api", true),
        };

        // 📋 使用专业的组装方法构建标签页
        TabItems = AssembleTabsByData(tabDataArray);

        // 🔍 应用过滤器
        ApplyFilter();

        // 设置默认选中第一个标签页
        SelectedTab = TabItems.FirstOrDefault();

        _logger.Info($"✅ TabView测试数据创建完成，共 {TabItems.Count} 个标签页");
    }

    /// <summary>
    /// 根据数据组装标签页集合
    /// </summary>
    private ObservableCollection<TabItemData> AssembleTabsByData(TabItemDataInfo[] tabDataArray)
    {
        var result = new ObservableCollection<TabItemData>();

        foreach (var info in tabDataArray)
        {
            var tab = new TabItemData
            {
                Id = int.Parse(info.Id),
                Header = info.Header,
                Content = info.Content,
                NodeType = info.NodeType,
                IsClosable = info.IsClosable,
                WpfUiSymbol = info.WpfUiSymbol,
                ZyloSymbol = info.ZyloSymbol,
                Emoji = info.Emoji
            };

            result.Add(tab);
        }

        return result;
    }

    /// <summary>
    /// 获取下一个ID
    /// </summary>
    private int GetNextId()
    {
        return TabItems.Count > 0 ? TabItems.Max(t => t.Id) + 1 : 1;
    }

    #endregion
}

/// <summary>
/// 标签页数据模型 - 严格按照CommunityToolkit.Mvvm最佳实践
/// </summary>
public partial class TabItemData : ObservableObject
{
    /// <summary>
    /// 标签页ID
    /// </summary>
    [ObservableProperty]
    private int id;

    /// <summary>
    /// 标签页标题
    /// </summary>
    [ObservableProperty]
    private string header = string.Empty;

    /// <summary>
    /// 标签页内容
    /// </summary>
    [ObservableProperty]
    private string content = string.Empty;

    /// <summary>
    /// 节点类型（用于默认图标）
    /// </summary>
    [ObservableProperty]
    private string nodeType = "document";

    /// <summary>
    /// 是否可关闭
    /// </summary>
    [ObservableProperty]
    private bool isClosable = true;

    /// <summary>
    /// WPF-UI 图标 - 使用正确的字段声明
    /// </summary>
    [ObservableProperty]
    private SymbolRegular? wpfUiSymbol;

    /// <summary>
    /// Zylo 自定义图标 - 使用正确的字段声明
    /// </summary>
    [ObservableProperty]
    private ZyloSymbol? zyloSymbol;

    /// <summary>
    /// Emoji 图标字符 - 使用正确的字段声明
    /// </summary>
    [ObservableProperty]
    private string? emoji;

    /// <summary>
    /// 默认图标（基于NodeType）- 计算属性
    /// </summary>
    public string DefaultIcon => TreeNodeIconHelper.GetDefaultIconByType(NodeType);
}

/// <summary>
/// 标签页数据信息（用于组装）
/// </summary>
public class TabItemDataInfo
{
    public string Id { get; set; } = string.Empty;
    public string Header { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public string NodeType { get; set; } = "document";
    public bool IsClosable { get; set; } = true;

    // 三图标支持
    public SymbolRegular? WpfUiSymbol { get; set; }
    public ZyloSymbol? ZyloSymbol { get; set; }
    public string? Emoji { get; set; }

    public TabItemDataInfo(string id, string header, string content, string nodeType, bool isClosable = true)
    {
        Id = id;
        Header = header;
        Content = content;
        NodeType = nodeType;
        IsClosable = isClosable;
    }
}

/// <summary>
/// TabView 初始化完成消息 - 用于演示 CommunityToolkit.Mvvm 消息传递
/// </summary>
/// <param name="Message">消息内容</param>
public record TabViewInitializedMessage(string Message);
