// NumberBox 数据验证 C# 示例
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Windows.Media;

namespace WPFTest.Examples
{
    public partial class NumberBoxValidationViewModel : ObservableObject
    {
        #region 验证属性

        /// <summary>
        /// 范围验证值
        /// </summary>
        [ObservableProperty]
        private double? rangeValue;

        /// <summary>
        /// 范围验证消息
        /// </summary>
        [ObservableProperty]
        private string rangeValidationMessage = string.Empty;

        /// <summary>
        /// 范围验证颜色
        /// </summary>
        [ObservableProperty]
        private Brush rangeValidationColor = Brushes.Gray;

        /// <summary>
        /// 必填验证值
        /// </summary>
        [ObservableProperty]
        private double? requiredValue;

        /// <summary>
        /// 必填验证消息
        /// </summary>
        [ObservableProperty]
        private string requiredValidationMessage = string.Empty;

        /// <summary>
        /// 必填验证颜色
        /// </summary>
        [ObservableProperty]
        private Brush requiredValidationColor = Brushes.Gray;

        /// <summary>
        /// 自定义验证值
        /// </summary>
        [ObservableProperty]
        private double? customValue;

        /// <summary>
        /// 自定义验证消息
        /// </summary>
        [ObservableProperty]
        private string customValidationMessage = string.Empty;

        /// <summary>
        /// 自定义验证颜色
        /// </summary>
        [ObservableProperty]
        private Brush customValidationColor = Brushes.Gray;

        /// <summary>
        /// 验证摘要
        /// </summary>
        [ObservableProperty]
        private string validationSummary = "等待验证...";

        #endregion

        #region 构造函数

        public NumberBoxValidationViewModel()
        {
            // 设置属性变化监听
            PropertyChanged += OnPropertyChanged;
        }

        #endregion

        #region 属性变化处理

        private void OnPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            switch (e.PropertyName)
            {
                case nameof(RangeValue):
                    ValidateRangeValue();
                    break;
                case nameof(RequiredValue):
                    ValidateRequiredValue();
                    break;
                case nameof(CustomValue):
                    ValidateCustomValue();
                    break;
            }
        }

        /// <summary>
        /// 验证范围值
        /// </summary>
        private void ValidateRangeValue()
        {
            if (RangeValue.HasValue)
            {
                if (RangeValue.Value >= 1 && RangeValue.Value <= 100)
                {
                    RangeValidationMessage = "✅ 范围验证通过";
                    RangeValidationColor = new SolidColorBrush(Colors.Green);
                }
                else
                {
                    RangeValidationMessage = "❌ 数值必须在1-100之间";
                    RangeValidationColor = new SolidColorBrush(Colors.Red);
                }
            }
            else
            {
                RangeValidationMessage = string.Empty;
                RangeValidationColor = Brushes.Gray;
            }
        }

        /// <summary>
        /// 验证必填值
        /// </summary>
        private void ValidateRequiredValue()
        {
            if (RequiredValue.HasValue)
            {
                RequiredValidationMessage = "✅ 必填验证通过";
                RequiredValidationColor = new SolidColorBrush(Colors.Green);
            }
            else
            {
                RequiredValidationMessage = "❌ 此字段为必填";
                RequiredValidationColor = new SolidColorBrush(Colors.Red);
            }
        }

        /// <summary>
        /// 验证自定义值（偶数）
        /// </summary>
        private void ValidateCustomValue()
        {
            if (CustomValue.HasValue)
            {
                if (CustomValue.Value % 2 == 0)
                {
                    CustomValidationMessage = "✅ 偶数验证通过";
                    CustomValidationColor = new SolidColorBrush(Colors.Green);
                }
                else
                {
                    CustomValidationMessage = "❌ 必须输入偶数";
                    CustomValidationColor = new SolidColorBrush(Colors.Red);
                }
            }
            else
            {
                CustomValidationMessage = string.Empty;
                CustomValidationColor = Brushes.Gray;
            }
        }

        #endregion

        #region 命令

        /// <summary>
        /// 验证所有命令
        /// </summary>
        [RelayCommand]
        private void ValidateAll()
        {
            var validationResults = new List<string>();

            // 执行所有验证
            ValidateRangeValue();
            ValidateRequiredValue();
            ValidateCustomValue();

            // 收集验证结果
            if (RangeValue.HasValue)
                validationResults.Add(RangeValidationMessage.Contains("✅") ? "范围验证: 通过" : "范围验证: 失败");
            else
                validationResults.Add("范围验证: 未输入");

            if (RequiredValue.HasValue)
                validationResults.Add("必填验证: 通过");
            else
                validationResults.Add("必填验证: 失败");

            if (CustomValue.HasValue)
                validationResults.Add(CustomValidationMessage.Contains("✅") ? "自定义验证: 通过" : "自定义验证: 失败");
            else
                validationResults.Add("自定义验证: 未输入");

            ValidationSummary = string.Join("\n", validationResults);
        }

        /// <summary>
        /// 清除验证命令
        /// </summary>
        [RelayCommand]
        private void ClearValidation()
        {
            RangeValue = null;
            RangeValidationMessage = string.Empty;
            RangeValidationColor = Brushes.Gray;

            RequiredValue = null;
            RequiredValidationMessage = string.Empty;
            RequiredValidationColor = Brushes.Gray;

            CustomValue = null;
            CustomValidationMessage = string.Empty;
            CustomValidationColor = Brushes.Gray;

            ValidationSummary = "验证已清除";
        }

        #endregion
    }
}
