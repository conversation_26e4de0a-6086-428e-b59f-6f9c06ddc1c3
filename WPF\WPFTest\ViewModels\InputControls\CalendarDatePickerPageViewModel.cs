using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Windows;
using System.IO;
using System.Reflection;
using System.ComponentModel;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels.InputControls
{
    /// <summary>
    /// CalendarDatePicker 页面的 ViewModel，演示 CalendarDatePicker 控件的各种功能
    /// </summary>
    public partial class CalendarDatePickerPageViewModel : ObservableObject
    {
        private readonly YLoggerInstance _logger = YLogger.For<CalendarDatePickerPageViewModel>();

        #region 属性

        /// <summary>
        /// 交互次数
        /// </summary>
        [ObservableProperty]
        private int interactionCount = 0;

        /// <summary>
        /// 最后执行的操作
        /// </summary>
        [ObservableProperty]
        private string lastAction = "无操作";

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        private string statusMessage = "欢迎使用 CalendarDatePicker 示例库！";

        #region 日期属性

        /// <summary>
        /// 选择的日期
        /// </summary>
        [ObservableProperty]
        private DateTime? selectedDate = DateTime.Today;

        /// <summary>
        /// 生日日期
        /// </summary>
        [ObservableProperty]
        private DateTime? birthDate = new DateTime(1990, 1, 1);

        /// <summary>
        /// 活动日期
        /// </summary>
        [ObservableProperty]
        private DateTime? eventDate = DateTime.Today.AddDays(7);

        /// <summary>
        /// 开始日期（用于日期范围选择）
        /// </summary>
        [ObservableProperty]
        private DateTime? startDate = DateTime.Today;

        /// <summary>
        /// 结束日期（用于日期范围选择）
        /// </summary>
        [ObservableProperty]
        private DateTime? endDate = DateTime.Today.AddDays(7);

        /// <summary>
        /// 程序化控制的日期
        /// </summary>
        [ObservableProperty]
        private DateTime? programmaticDate = DateTime.Today;

        /// <summary>
        /// 日历是否打开
        /// </summary>
        [ObservableProperty]
        private bool isCalendarOpen = false;

        /// <summary>
        /// 日期范围间隔天数
        /// </summary>
        public int DateRangeDays
        {
            get
            {
                if (StartDate.HasValue && EndDate.HasValue)
                {
                    return Math.Abs((EndDate.Value - StartDate.Value).Days);
                }
                return 0;
            }
        }

        #endregion

        #region 代码示例属性

        /// <summary>
        /// 基础功能 XAML 示例
        /// </summary>
        [ObservableProperty]
        private string basicXamlExample = string.Empty;

        /// <summary>
        /// 基础功能 C# 示例
        /// </summary>
        [ObservableProperty]
        private string basicCSharpExample = string.Empty;

        /// <summary>
        /// 高级功能 XAML 示例
        /// </summary>
        [ObservableProperty]
        private string advancedXamlExample = string.Empty;

        /// <summary>
        /// 高级功能 C# 示例
        /// </summary>
        [ObservableProperty]
        private string advancedCSharpExample = string.Empty;

        /// <summary>
        /// 样式 XAML 示例
        /// </summary>
        [ObservableProperty]
        private string stylesXamlExample = string.Empty;

        #endregion

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化 CalendarDatePickerPageViewModel
        /// </summary>
        public CalendarDatePickerPageViewModel()
        {
            try
            {
                _logger.Info("🚀 CalendarDatePicker 页面 ViewModel 开始初始化");

                StatusMessage = "CalendarDatePicker 示例库已加载，开始体验日期选择功能！";
                
                // 监听属性变化
                PropertyChanged += OnPropertyChanged;
                
                InitializeCodeExamples();

                _logger.Info("✅ CalendarDatePicker 页面 ViewModel 初始化完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ CalendarDatePicker 页面 ViewModel 初始化失败: {ex.Message}");
                StatusMessage = "初始化失败，请检查日志";
                SetDefaultCodeExamples();
            }
        }

        #endregion

        #region 命令

        /// <summary>
        /// 设置今天日期命令
        /// </summary>
        [RelayCommand]
        private void SetToday()
        {
            InteractionCount++;
            LastAction = "设置今天";
            
            SelectedDate = DateTime.Today;
            StatusMessage = $"📅 已设置为今天: {DateTime.Today:yyyy-MM-dd}";
            
            _logger.Info($"设置今天日期: {DateTime.Today:yyyy-MM-dd}");
        }

        /// <summary>
        /// 清除日期命令
        /// </summary>
        [RelayCommand]
        private void ClearDate()
        {
            InteractionCount++;
            LastAction = "清除日期";
            
            SelectedDate = null;
            StatusMessage = "🗑️ 已清除选择的日期";
            
            _logger.Info("清除了选择的日期");
        }

        /// <summary>
        /// 随机日期命令
        /// </summary>
        [RelayCommand]
        private void RandomDate()
        {
            InteractionCount++;
            LastAction = "随机日期";
            
            var random = new Random();
            var startDate = DateTime.Today.AddYears(-5);
            var endDate = DateTime.Today.AddYears(2);
            var range = (endDate - startDate).Days;
            var randomDate = startDate.AddDays(random.Next(range));
            
            SelectedDate = randomDate;
            StatusMessage = $"🎲 随机生成日期: {randomDate:yyyy-MM-dd}";
            
            _logger.Info($"生成随机日期: {randomDate:yyyy-MM-dd}");
        }

        /// <summary>
        /// 重置计数命令
        /// </summary>
        [RelayCommand]
        private void ResetCount()
        {
            InteractionCount = 0;
            StatusMessage = "计数已重置";
            _logger.Info("重置了交互计数");
        }

        /// <summary>
        /// 清除状态命令
        /// </summary>
        [RelayCommand]
        private void ClearStatus()
        {
            LastAction = "无操作";
            StatusMessage = "状态已清除";
            _logger.Info("清除了状态信息");
        }

        /// <summary>
        /// 打开日历命令
        /// </summary>
        [RelayCommand]
        private void OpenCalendar()
        {
            InteractionCount++;
            LastAction = "打开日历";

            IsCalendarOpen = true;
            StatusMessage = "📅 日历已打开";

            _logger.Info("程序化打开日历");
        }

        /// <summary>
        /// 关闭日历命令
        /// </summary>
        [RelayCommand]
        private void CloseCalendar()
        {
            InteractionCount++;
            LastAction = "关闭日历";

            IsCalendarOpen = false;
            StatusMessage = "📅 日历已关闭";

            _logger.Info("程序化关闭日历");
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 属性变化处理
        /// </summary>
        private void OnPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            switch (e.PropertyName)
            {
                case nameof(SelectedDate):
                    HandleDateChanged("主要日期", SelectedDate);
                    break;
                case nameof(BirthDate):
                    HandleDateChanged("生日", BirthDate);
                    break;
                case nameof(EventDate):
                    HandleDateChanged("活动日期", EventDate);
                    break;
                case nameof(StartDate):
                case nameof(EndDate):
                    OnPropertyChanged(nameof(DateRangeDays));
                    HandleDateRangeChanged();
                    break;
                case nameof(ProgrammaticDate):
                    HandleDateChanged("程序化日期", ProgrammaticDate);
                    break;
            }
        }

        /// <summary>
        /// 处理日期变化
        /// </summary>
        private void HandleDateChanged(string dateType, DateTime? newDate)
        {
            InteractionCount++;

            if (newDate.HasValue)
            {
                StatusMessage = $"📅 {dateType}已更改为: {newDate.Value:yyyy-MM-dd}";
                _logger.Info($"{dateType}变化: {newDate.Value:yyyy-MM-dd}");
            }
            else
            {
                StatusMessage = $"🗑️ {dateType}已清除";
                _logger.Info($"{dateType}已清除");
            }
        }

        /// <summary>
        /// 处理日期范围变化
        /// </summary>
        private void HandleDateRangeChanged()
        {
            if (StartDate.HasValue && EndDate.HasValue)
            {
                var days = DateRangeDays;
                StatusMessage = $"📅 日期范围更新: {StartDate.Value:MM-dd} 到 {EndDate.Value:MM-dd} (间隔 {days} 天)";
                _logger.Info($"日期范围变化: {StartDate.Value:yyyy-MM-dd} 到 {EndDate.Value:yyyy-MM-dd}, 间隔 {days} 天");
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化代码示例
        /// </summary>
        private void InitializeCodeExamples()
        {
            try
            {
                // 获取代码示例文件的基础路径
                var baseDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
                var codeExamplesPath = Path.Combine(baseDirectory, "CodeExamples", "Controls", "CalendarDatePicker");

                _logger.Info($"基础目录: {baseDirectory}");
                _logger.Info($"代码示例路径: {codeExamplesPath}");
                _logger.Info($"目录是否存在: {Directory.Exists(codeExamplesPath)}");

                // 读取各种示例文件
                BasicXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.xaml.txt"));
                BasicCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Basic.cs.txt"));
                AdvancedXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Advanced.xaml.txt"));
                AdvancedCSharpExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Advanced.cs.txt"));
                StylesXamlExample = ReadCodeExampleFile(Path.Combine(codeExamplesPath, "Styles.xaml.txt"));

                _logger.Info("CalendarDatePicker 代码示例加载完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"加载 CalendarDatePicker 代码示例失败: {ex.Message}");
                SetDefaultCodeExamples();
            }
        }

        /// <summary>
        /// 读取代码示例文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件内容</returns>
        private string ReadCodeExampleFile(string filePath)
        {
            _logger.Info($"尝试读取文件: {filePath}");
            
            if (File.Exists(filePath))
            {
                var content = File.ReadAllText(filePath);
                _logger.Info($"成功读取文件，内容长度: {content.Length}");
                return content;
            }
            else
            {
                _logger.Warning($"代码示例文件不存在: {filePath}");
                return $"// 代码示例文件不存在: {Path.GetFileName(filePath)}\n// 完整路径: {filePath}";
            }
        }

        /// <summary>
        /// 设置默认代码示例（当文件读取失败时使用）
        /// </summary>
        private void SetDefaultCodeExamples()
        {
            BasicXamlExample = "<!-- CalendarDatePicker 基础示例 -->\n<ui:CalendarDatePicker Date=\"{Binding SelectedDate, Mode=TwoWay}\" />";
            BasicCSharpExample = "// CalendarDatePicker C# 基础示例\n[ObservableProperty]\nprivate DateTime? selectedDate = DateTime.Today;";
            AdvancedXamlExample = "<!-- CalendarDatePicker 高级示例 -->\n<!-- 在这里添加高级用法示例 -->";
            AdvancedCSharpExample = "// CalendarDatePicker C# 高级示例\n// 在这里添加高级 C# 代码示例";
            StylesXamlExample = "<!-- CalendarDatePicker 样式示例 -->\n<!-- 在这里添加样式使用示例 -->";
        }

        #endregion
    }
}
