<!-- AutoSuggestBox 基础功能示例 -->
<UserControl x:Class="WPFTest.Views.Controls.BasicAutoSuggestBoxExample"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="20"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="20"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="20"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="20"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 基础城市搜索 -->
        <StackPanel Grid.Row="0">
            <TextBlock Text="城市搜索" FontWeight="Bold" Margin="0,0,0,10"/>
            <ui:AutoSuggestBox x:Name="CityAutoSuggestBox"
                               PlaceholderText="请输入城市名称..."
                               Text="{Binding CitySearchText, UpdateSourceTrigger=PropertyChanged}"
                               ItemsSource="{Binding CitySuggestions}"
                               MaxSuggestionListHeight="200"
                               UpdateTextOnSelect="True"
                               ClearButtonEnabled="True">
                <ui:AutoSuggestBox.Icon>
                    <ui:SymbolIcon Symbol="Location24"/>
                </ui:AutoSuggestBox.Icon>
            </ui:AutoSuggestBox>
        </StackPanel>

        <!-- 编程语言搜索 -->
        <StackPanel Grid.Row="2">
            <TextBlock Text="编程语言搜索" FontWeight="Bold" Margin="0,0,0,10"/>
            <ui:AutoSuggestBox x:Name="LanguageAutoSuggestBox"
                               PlaceholderText="请输入编程语言..."
                               Text="{Binding LanguageSearchText, UpdateSourceTrigger=PropertyChanged}"
                               ItemsSource="{Binding LanguageSuggestions}"
                               MaxSuggestionListHeight="200"
                               UpdateTextOnSelect="True"
                               ClearButtonEnabled="True">
                <ui:AutoSuggestBox.Icon>
                    <ui:SymbolIcon Symbol="Code24"/>
                </ui:AutoSuggestBox.Icon>
            </ui:AutoSuggestBox>
        </StackPanel>

        <!-- 国家搜索 -->
        <StackPanel Grid.Row="4">
            <TextBlock Text="国家搜索" FontWeight="Bold" Margin="0,0,0,10"/>
            <ui:AutoSuggestBox x:Name="CountryAutoSuggestBox"
                               PlaceholderText="请输入国家名称..."
                               Text="{Binding CountrySearchText, UpdateSourceTrigger=PropertyChanged}"
                               ItemsSource="{Binding CountrySuggestions}"
                               MaxSuggestionListHeight="200"
                               UpdateTextOnSelect="True"
                               ClearButtonEnabled="True">
                <ui:AutoSuggestBox.Icon>
                    <ui:SymbolIcon Symbol="Globe24"/>
                </ui:AutoSuggestBox.Icon>
            </ui:AutoSuggestBox>
        </StackPanel>

        <!-- 产品搜索 -->
        <StackPanel Grid.Row="6">
            <TextBlock Text="产品搜索" FontWeight="Bold" Margin="0,0,0,10"/>
            <ui:AutoSuggestBox x:Name="ProductAutoSuggestBox"
                               PlaceholderText="请输入产品名称..."
                               Text="{Binding ProductSearchText, UpdateSourceTrigger=PropertyChanged}"
                               ItemsSource="{Binding ProductSuggestions}"
                               MaxSuggestionListHeight="200"
                               UpdateTextOnSelect="True"
                               ClearButtonEnabled="True">
                <ui:AutoSuggestBox.Icon>
                    <ui:SymbolIcon Symbol="ShoppingBag24"/>
                </ui:AutoSuggestBox.Icon>
            </ui:AutoSuggestBox>
        </StackPanel>

        <!-- 用户搜索 -->
        <StackPanel Grid.Row="8">
            <TextBlock Text="用户搜索" FontWeight="Bold" Margin="0,0,0,10"/>
            <ui:AutoSuggestBox x:Name="UserAutoSuggestBox"
                               PlaceholderText="请输入用户名称..."
                               Text="{Binding UserSearchText, UpdateSourceTrigger=PropertyChanged}"
                               ItemsSource="{Binding UserSuggestions}"
                               MaxSuggestionListHeight="200"
                               UpdateTextOnSelect="True"
                               ClearButtonEnabled="True">
                <ui:AutoSuggestBox.Icon>
                    <ui:SymbolIcon Symbol="Person24"/>
                </ui:AutoSuggestBox.Icon>
            </ui:AutoSuggestBox>
        </StackPanel>
    </Grid>
</UserControl>

<!-- 
AutoSuggestBox 基础属性说明：

1. Text - 当前文本内容，支持双向绑定
2. PlaceholderText - 占位符文本，当输入框为空时显示
3. ItemsSource - 建议项数据源，通常绑定到 ObservableCollection<string>
4. MaxSuggestionListHeight - 建议列表的最大高度
5. UpdateTextOnSelect - 选择建议项时是否更新文本框内容
6. ClearButtonEnabled - 是否显示清除按钮
7. Icon - 输入框左侧显示的图标

事件处理：
- TextChanged - 文本变化时触发
- QuerySubmitted - 提交查询时触发（按回车或点击建议项）
- SuggestionChosen - 选择建议项时触发

数据绑定模式：
- UpdateSourceTrigger=PropertyChanged 实现实时搜索
- 建议列表通过 ViewModel 中的搜索逻辑动态更新
-->
