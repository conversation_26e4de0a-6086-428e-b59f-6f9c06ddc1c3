using System.Collections.ObjectModel;
using System.ComponentModel;

namespace Zylo.WPF.Controls.YFile.Model;

/// <summary>
/// 文件夹树项目模型
/// </summary>
public class FolderTreeItem : INotifyPropertyChanged
{
    private string _name = string.Empty;
    private string _fullPath = string.Empty;
    private string _icon = "📁";
    private bool _isExpanded;
    private bool _isSelected;
    private bool _isVirtual;

    /// <summary>
    /// 文件夹名称
    /// </summary>
    public string Name
    {
        get => _name;
        set
        {
            _name = value;
            OnPropertyChanged(nameof(Name));
        }
    }

    /// <summary>
    /// 完整路径
    /// </summary>
    public string FullPath
    {
        get => _fullPath;
        set
        {
            _fullPath = value;
            OnPropertyChanged(nameof(FullPath));
        }
    }

    /// <summary>
    /// 图标
    /// </summary>
    public string Icon
    {
        get => _icon;
        set
        {
            _icon = value;
            OnPropertyChanged(nameof(Icon));
        }
    }

    /// <summary>
    /// 是否展开
    /// </summary>
    public bool IsExpanded
    {
        get => _isExpanded;
        set
        {
            _isExpanded = value;
            OnPropertyChanged(nameof(IsExpanded));
        }
    }

    /// <summary>
    /// 是否选中
    /// </summary>
    public bool IsSelected
    {
        get => _isSelected;
        set
        {
            _isSelected = value;
            OnPropertyChanged(nameof(IsSelected));
        }
    }

    /// <summary>
    /// 是否为虚拟节点（用于动态加载）
    /// </summary>
    public bool IsVirtual
    {
        get => _isVirtual;
        set
        {
            _isVirtual = value;
            OnPropertyChanged(nameof(IsVirtual));
        }
    }

    /// <summary>
    /// 子文件夹
    /// </summary>
    public ObservableCollection<FolderTreeItem> Children { get; set; } = new();

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged(string propertyName)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
} 