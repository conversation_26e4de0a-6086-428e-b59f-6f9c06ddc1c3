# 🧪 Zylo.YData 测试指南

本指南详细介绍如何为使用 Zylo.YData 的应用程序编写高质量的测试，包括单元测试、集成测试和性能测试。

## 📋 测试策略概览

| 测试类型 | 目标 | 数据库 | 执行速度 | 覆盖范围 |
|---------|------|--------|----------|----------|
| 单元测试 | 业务逻辑 | 内存/Mock | ⚡ 很快 | 单个方法 |
| 集成测试 | 数据访问 | 临时文件 | 🐌 较慢 | 完整流程 |
| 性能测试 | 性能基准 | 真实数据 | 🐢 最慢 | 系统级别 |

## 🚀 快速开始

### 安装测试包
```xml
<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
<PackageReference Include="xunit" Version="2.4.2" />
<PackageReference Include="xunit.runner.visualstudio" Version="2.4.5" />
<PackageReference Include="Moq" Version="4.20.69" />
<PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.0" />
```

### 基础测试类
```csharp
using Xunit;
using Zylo.YData;

public class YDataTestBase : IDisposable
{
    protected readonly string TestDbPath;
    
    public YDataTestBase()
    {
        // 为每个测试创建独立的数据库
        TestDbPath = Path.Combine(Path.GetTempPath(), $"test_{Guid.NewGuid():N}.db");
        YData.ConfigureAuto($"Data Source={TestDbPath}");
        
        // 创建测试表结构
        InitializeDatabase();
    }
    
    protected virtual void InitializeDatabase()
    {
        YData.FreeSql.CodeFirst.SyncStructure<User>();
        YData.FreeSql.CodeFirst.SyncStructure<Order>();
        YData.FreeSql.CodeFirst.SyncStructure<Product>();
    }
    
    public void Dispose()
    {
        YData.FreeSql?.Dispose();
        if (File.Exists(TestDbPath))
        {
            File.Delete(TestDbPath);
        }
    }
}
```

## 🔬 单元测试

### 1. 基础 CRUD 测试

#### 测试插入操作
```csharp
public class UserCrudTests : YDataTestBase
{
    [Fact]
    public async Task InsertAsync_ShouldCreateUserWithValidId()
    {
        // Arrange
        var user = new User
        {
            Name = "张三",
            Email = "<EMAIL>",
            Age = 25
        };
        
        // Act
        var result = await YData.InsertAsync(user);
        
        // Assert
        Assert.Equal(1, result);  // 影响行数
        Assert.True(user.Id > 0);  // 自动生成ID
        Assert.Equal("张三", user.Name);
    }
    
    [Fact]
    public async Task InsertAsync_WithDuplicateEmail_ShouldThrowException()
    {
        // Arrange
        var user1 = new User { Name = "用户1", Email = "<EMAIL>", Age = 25 };
        var user2 = new User { Name = "用户2", Email = "<EMAIL>", Age = 30 };
        
        await YData.InsertAsync(user1);
        
        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => YData.InsertAsync(user2));
    }
}
```

#### 测试查询操作
```csharp
public class UserQueryTests : YDataTestBase
{
    [Fact]
    public async Task GetAsync_WithValidId_ShouldReturnUser()
    {
        // Arrange
        var user = await CreateTestUser();
        
        // Act
        var result = await YData.GetAsync<User>(user.Id);
        
        // Assert
        Assert.NotNull(result);
        Assert.Equal(user.Id, result.Id);
        Assert.Equal(user.Name, result.Name);
    }
    
    [Fact]
    public async Task GetAsync_WithInvalidId_ShouldReturnNull()
    {
        // Act
        var result = await YData.GetAsync<User>(999);
        
        // Assert
        Assert.Null(result);
    }
    
    [Theory]
    [InlineData(true, 2)]   // 活跃用户
    [InlineData(false, 1)]  // 非活跃用户
    public async Task Select_WithIsActiveFilter_ShouldReturnCorrectCount(bool isActive, int expectedCount)
    {
        // Arrange
        await CreateTestUsers();
        
        // Act
        var users = await YData.Select<User>()
            .Where(u => u.IsActive == isActive)
            .ToListAsync();
        
        // Assert
        Assert.Equal(expectedCount, users.Count);
    }
    
    private async Task<User> CreateTestUser()
    {
        var user = new User { Name = "测试用户", Email = "<EMAIL>", Age = 25 };
        await YData.InsertAsync(user);
        return user;
    }
    
    private async Task CreateTestUsers()
    {
        var users = new[]
        {
            new User { Name = "用户1", Email = "<EMAIL>", Age = 25, IsActive = true },
            new User { Name = "用户2", Email = "<EMAIL>", Age = 30, IsActive = true },
            new User { Name = "用户3", Email = "<EMAIL>", Age = 35, IsActive = false }
        };
        
        foreach (var user in users)
        {
            await YData.InsertAsync(user);
        }
    }
}
```

### 2. 扩展方法测试

#### 测试分页功能
```csharp
public class PaginationTests : YDataTestBase
{
    [Fact]
    public async Task ToPagedResultAsync_ShouldReturnCorrectPageInfo()
    {
        // Arrange
        await CreateTestUsers(25);  // 创建25个用户
        
        // Act
        var result = await YData.Select<User>()
            .Where(u => u.IsActive)
            .OrderBy(u => u.Id)
            .ToPagedResultAsync(pageIndex: 2, pageSize: 10);
        
        // Assert
        Assert.Equal(2, result.PageIndex);
        Assert.Equal(10, result.PageSize);
        Assert.Equal(25, result.TotalCount);
        Assert.Equal(3, result.TotalPages);
        Assert.Equal(10, result.Items.Count);
        Assert.True(result.HasPreviousPage);
        Assert.True(result.HasNextPage);
    }
    
    [Fact]
    public async Task ToPagedResultAsync_LastPage_ShouldReturnCorrectItems()
    {
        // Arrange
        await CreateTestUsers(25);
        
        // Act
        var result = await YData.Select<User>()
            .OrderBy(u => u.Id)
            .ToPagedResultAsync(pageIndex: 3, pageSize: 10);
        
        // Assert
        Assert.Equal(3, result.PageIndex);
        Assert.Equal(5, result.Items.Count);  // 最后一页只有5个
        Assert.True(result.HasPreviousPage);
        Assert.False(result.HasNextPage);
    }
    
    private async Task CreateTestUsers(int count)
    {
        var users = Enumerable.Range(1, count)
            .Select(i => new User 
            { 
                Name = $"用户{i}", 
                Email = $"user{i}@example.com", 
                Age = 20 + (i % 40),
                IsActive = true
            })
            .ToList();
            
        await YData.Insert<User>()
            .AppendData(users)
            .ExecuteAffrowsAsync();
    }
}
```

#### 测试条件查询扩展
```csharp
public class WhereIfTests : YDataTestBase
{
    [Theory]
    [InlineData("张", true, 1)]    // 有名称过滤
    [InlineData("", true, 3)]     // 无名称过滤
    [InlineData("不存在", true, 0)] // 不存在的名称
    public async Task WhereIf_WithNameFilter_ShouldReturnCorrectCount(
        string nameFilter, bool applyFilter, int expectedCount)
    {
        // Arrange
        await CreateTestUsersWithNames();
        
        // Act
        var users = await YData.Select<User>()
            .WhereIf(applyFilter && !string.IsNullOrEmpty(nameFilter), 
                     u => u.Name.Contains(nameFilter))
            .ToListAsync();
        
        // Assert
        Assert.Equal(expectedCount, users.Count);
    }
    
    private async Task CreateTestUsersWithNames()
    {
        var users = new[]
        {
            new User { Name = "张三", Email = "<EMAIL>", Age = 25 },
            new User { Name = "李四", Email = "<EMAIL>", Age = 30 },
            new User { Name = "王五", Email = "<EMAIL>", Age = 35 }
        };
        
        foreach (var user in users)
        {
            await YData.InsertAsync(user);
        }
    }
}
```

### 3. 聚合查询测试

```csharp
public class AggregateTests : YDataTestBase
{
    [Fact]
    public async Task YCountAsync_ShouldReturnCorrectCount()
    {
        // Arrange
        await CreateTestUsers(10);
        
        // Act
        var totalCount = await YData.Select<User>().YCountAsync();
        var activeCount = await YData.Select<User>().YCountAsync(u => u.IsActive);
        
        // Assert
        Assert.Equal(10, totalCount);
        Assert.Equal(8, activeCount);  // 假设8个活跃用户
    }
    
    [Fact]
    public async Task YAverageAsync_ShouldReturnCorrectAverage()
    {
        // Arrange
        var users = new[]
        {
            new User { Name = "用户1", Email = "<EMAIL>", Age = 20 },
            new User { Name = "用户2", Email = "<EMAIL>", Age = 30 },
            new User { Name = "用户3", Email = "<EMAIL>", Age = 40 }
        };
        
        foreach (var user in users)
        {
            await YData.InsertAsync(user);
        }
        
        // Act
        var avgAge = await YData.Select<User>().YAverageAsync(u => u.Age);
        
        // Assert
        Assert.Equal(30m, avgAge);
    }
}
```

## 🔗 集成测试

### 1. 服务层集成测试

```csharp
public class UserServiceIntegrationTests : YDataTestBase
{
    private readonly UserService _userService;
    private readonly ILogger<UserService> _logger;
    
    public UserServiceIntegrationTests()
    {
        _logger = Mock.Of<ILogger<UserService>>();
        _userService = new UserService(YData.Context, _logger);
    }
    
    [Fact]
    public async Task CreateUserAsync_ShouldCreateUserAndReturnWithId()
    {
        // Arrange
        var request = new CreateUserRequest
        {
            Name = "新用户",
            Email = "<EMAIL>",
            Age = 28
        };
        
        // Act
        var user = await _userService.CreateUserAsync(request);
        
        // Assert
        Assert.NotNull(user);
        Assert.True(user.Id > 0);
        Assert.Equal(request.Name, user.Name);
        Assert.Equal(request.Email, user.Email);
        
        // 验证数据库中确实存在
        var dbUser = await YData.GetAsync<User>(user.Id);
        Assert.NotNull(dbUser);
        Assert.Equal(user.Name, dbUser.Name);
    }
    
    [Fact]
    public async Task GetUsersAsync_WithPagination_ShouldReturnCorrectPage()
    {
        // Arrange
        await CreateTestUsers(25);
        
        // Act
        var result = await _userService.GetUsersAsync(pageIndex: 2, pageSize: 10);
        
        // Assert
        Assert.Equal(2, result.PageIndex);
        Assert.Equal(10, result.Items.Count);
        Assert.Equal(25, result.TotalCount);
    }
}
```

### 2. 事务测试

```csharp
public class TransactionTests : YDataTestBase
{
    [Fact]
    public async Task TransactionAsync_WithSuccess_ShouldCommitAllChanges()
    {
        // Act
        var userId = await YData.TransactionAsync(async () =>
        {
            var user = new User { Name = "事务用户", Email = "<EMAIL>", Age = 25 };
            await YData.InsertAsync(user);
            
            var profile = new UserProfile { UserId = user.Id, Bio = "用户简介" };
            await YData.InsertAsync(profile);
            
            return user.Id;
        });
        
        // Assert
        var user = await YData.GetAsync<User>(userId);
        var profile = await YData.Select<UserProfile>()
            .Where(p => p.UserId == userId)
            .FirstAsync();
            
        Assert.NotNull(user);
        Assert.NotNull(profile);
        Assert.Equal(userId, profile.UserId);
    }
    
    [Fact]
    public async Task TransactionAsync_WithException_ShouldRollbackAllChanges()
    {
        // Arrange
        var initialCount = await YData.Select<User>().YCountAsync();
        
        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(async () =>
        {
            await YData.TransactionAsync(async () =>
            {
                var user = new User { Name = "事务用户", Email = "<EMAIL>", Age = 25 };
                await YData.InsertAsync(user);
                
                // 模拟异常
                throw new InvalidOperationException("模拟异常");
            });
        });
        
        // Assert - 数据应该回滚
        var finalCount = await YData.Select<User>().YCountAsync();
        Assert.Equal(initialCount, finalCount);
    }
}
```

## 🌐 Web API 集成测试

### 1. ASP.NET Core 测试

```csharp
public class UsersControllerTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;
    
    public UsersControllerTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureServices(services =>
            {
                // 替换为测试数据库
                services.RemoveAll<IYDataContext>();
                services.AddYDataAuto("Data Source=:memory:");
            });
        });
        
        _client = _factory.CreateClient();
    }
    
    [Fact]
    public async Task GetUsers_ShouldReturnPagedResult()
    {
        // Arrange
        await SeedTestData();
        
        // Act
        var response = await _client.GetAsync("/api/users?pageIndex=1&pageSize=10");
        
        // Assert
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<PagedResult<User>>(content);
        
        Assert.NotNull(result);
        Assert.True(result.Items.Count > 0);
    }
    
    [Fact]
    public async Task CreateUser_WithValidData_ShouldReturnCreated()
    {
        // Arrange
        var user = new { Name = "新用户", Email = "<EMAIL>", Age = 25 };
        var json = JsonSerializer.Serialize(user);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        
        // Act
        var response = await _client.PostAsync("/api/users", content);
        
        // Assert
        Assert.Equal(HttpStatusCode.Created, response.StatusCode);
    }
    
    private async Task SeedTestData()
    {
        // 初始化测试数据
        using var scope = _factory.Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<IYDataContext>();
        
        var users = new[]
        {
            new User { Name = "用户1", Email = "<EMAIL>", Age = 25 },
            new User { Name = "用户2", Email = "<EMAIL>", Age = 30 }
        };
        
        foreach (var user in users)
        {
            await context.InsertAsync(user);
        }
    }
}
```

## ⚡ 性能测试

### 1. 批量操作性能测试

```csharp
public class PerformanceTests : YDataTestBase
{
    [Fact]
    public async Task BatchInsert_Performance_ShouldBeEfficient()
    {
        // Arrange
        var users = GenerateUsers(10000);
        var stopwatch = Stopwatch.StartNew();
        
        // Act
        await YData.Insert<User>().YBatchInsertAsync(users, batchSize: 1000);
        
        // Assert
        stopwatch.Stop();
        Assert.True(stopwatch.ElapsedMilliseconds < 5000, 
            $"批量插入10000条记录耗时 {stopwatch.ElapsedMilliseconds}ms，超过预期");
        
        var count = await YData.Select<User>().YCountAsync();
        Assert.Equal(10000, count);
    }
    
    [Theory]
    [InlineData(100)]
    [InlineData(1000)]
    [InlineData(5000)]
    public async Task BatchInsert_DifferentBatchSizes_ShouldComplete(int batchSize)
    {
        // Arrange
        var users = GenerateUsers(5000);
        
        // Act
        var stopwatch = Stopwatch.StartNew();
        await YData.Insert<User>().YBatchInsertAsync(users, batchSize);
        stopwatch.Stop();
        
        // Assert
        var count = await YData.Select<User>().YCountAsync();
        Assert.Equal(5000, count);
        
        // 记录性能数据
        Console.WriteLine($"批次大小 {batchSize}: {stopwatch.ElapsedMilliseconds}ms");
    }
    
    private List<User> GenerateUsers(int count)
    {
        return Enumerable.Range(1, count)
            .Select(i => new User
            {
                Name = $"用户{i}",
                Email = $"user{i}@example.com",
                Age = 20 + (i % 50)
            })
            .ToList();
    }
}
```

## 📊 测试数据管理

### 1. 测试数据构建器

```csharp
public class UserBuilder
{
    private User _user = new User();
    
    public static UserBuilder Create() => new UserBuilder();
    
    public UserBuilder WithName(string name)
    {
        _user.Name = name;
        return this;
    }
    
    public UserBuilder WithEmail(string email)
    {
        _user.Email = email;
        return this;
    }
    
    public UserBuilder WithAge(int age)
    {
        _user.Age = age;
        return this;
    }
    
    public UserBuilder Active(bool isActive = true)
    {
        _user.IsActive = isActive;
        return this;
    }
    
    public User Build() => _user;
    
    public async Task<User> SaveAsync()
    {
        await YData.InsertAsync(_user);
        return _user;
    }
}

// 使用示例
var user = await UserBuilder.Create()
    .WithName("测试用户")
    .WithEmail("<EMAIL>")
    .WithAge(25)
    .Active()
    .SaveAsync();
```

### 2. 测试数据清理

```csharp
public class DatabaseCleaner
{
    public static async Task CleanAsync()
    {
        await YData.Delete<OrderItem>().Where(x => true).ExecuteAffrowsAsync();
        await YData.Delete<Order>().Where(x => true).ExecuteAffrowsAsync();
        await YData.Delete<UserProfile>().Where(x => true).ExecuteAffrowsAsync();
        await YData.Delete<User>().Where(x => true).ExecuteAffrowsAsync();
        await YData.Delete<Product>().Where(x => true).ExecuteAffrowsAsync();
    }
}

// 在测试基类中使用
public class CleanDatabaseTestBase : YDataTestBase
{
    public override async Task InitializeAsync()
    {
        await base.InitializeAsync();
        await DatabaseCleaner.CleanAsync();
    }
}
```

## 🎯 测试最佳实践

### 1. 测试命名规范
```csharp
// 格式：MethodName_Scenario_ExpectedResult
[Fact]
public async Task GetUserById_WithValidId_ShouldReturnUser() { }

[Fact]
public async Task GetUserById_WithInvalidId_ShouldReturnNull() { }

[Fact]
public async Task CreateUser_WithDuplicateEmail_ShouldThrowException() { }
```

### 2. 测试组织
```csharp
// 使用 Trait 组织测试
[Trait("Category", "Unit")]
[Trait("Feature", "UserManagement")]
public class UserServiceTests { }

[Trait("Category", "Integration")]
[Trait("Feature", "Database")]
public class DatabaseIntegrationTests { }
```

### 3. 测试数据隔离
```csharp
// 每个测试类使用独立的数据库
public class IsolatedTestBase : IAsyncLifetime
{
    protected string TestDbPath { get; private set; } = string.Empty;
    
    public async Task InitializeAsync()
    {
        TestDbPath = Path.Combine(Path.GetTempPath(), $"test_{Guid.NewGuid():N}.db");
        YData.ConfigureAuto($"Data Source={TestDbPath}");
        await InitializeDatabase();
    }
    
    public async Task DisposeAsync()
    {
        YData.FreeSql?.Dispose();
        if (File.Exists(TestDbPath))
        {
            File.Delete(TestDbPath);
        }
    }
}
```

---

**🎯 记住：** 好的测试是代码质量的保证。投入时间编写测试，长期来看会节省更多的调试时间！
