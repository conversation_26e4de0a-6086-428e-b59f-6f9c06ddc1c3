using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Threading;
using Wpf.Ui.Controls;
using Button = Wpf.Ui.Controls.Button;

namespace Zylo.WPF.Controls;

/// <summary>
/// 自定义 Snackbar 控件 - 支持强调色和依赖属性
/// </summary>
[TemplatePart(Name = PART_CloseButton, Type = typeof(Button))]
public class ZyloSnackbar : ContentControl
{
    #region Template Parts

    private const string PART_CloseButton = "PART_CloseButton";
    private Button? _closeButton;

    #endregion

    #region 依赖属性

    /// <summary>
    /// 标题属性
    /// </summary>
    public static readonly DependencyProperty TitleProperty =
        DependencyProperty.Register(
            nameof(Title),
            typeof(string),
            typeof(ZyloSnackbar),
            new FrameworkPropertyMetadata(
                string.Empty,
                FrameworkPropertyMetadataOptions.AffectsRender
            )
        );

    /// <summary>
    /// 消息内容属性
    /// </summary>
    public static readonly DependencyProperty MessageProperty =
        DependencyProperty.Register(
            nameof(Message),
            typeof(string),
            typeof(ZyloSnackbar),
            new FrameworkPropertyMetadata(
                string.Empty,
                FrameworkPropertyMetadataOptions.AffectsRender
            )
        );

    /// <summary>
    /// 图标属性
    /// </summary>
    public static readonly DependencyProperty IconProperty =
        DependencyProperty.Register(
            nameof(Icon),
            typeof(SymbolRegular),
            typeof(ZyloSnackbar),
            new FrameworkPropertyMetadata(
                SymbolRegular.Info24,
                FrameworkPropertyMetadataOptions.AffectsRender
            )
        );

    /// <summary>
    /// 外观类型属性
    /// </summary>
    public static readonly DependencyProperty AppearanceProperty =
        DependencyProperty.Register(
            nameof(Appearance),
            typeof(ControlAppearance),
            typeof(ZyloSnackbar),
            new FrameworkPropertyMetadata(
                ControlAppearance.Primary,
                FrameworkPropertyMetadataOptions.AffectsRender,
                OnAppearanceChanged
            )
        );

    /// <summary>
    /// 是否显示属性
    /// </summary>
    public static readonly DependencyProperty IsShownProperty =
        DependencyProperty.Register(
            nameof(IsShown),
            typeof(bool),
            typeof(ZyloSnackbar),
            new FrameworkPropertyMetadata(
                false,
                FrameworkPropertyMetadataOptions.AffectsRender,
                OnIsShownChanged
            )
        );

    /// <summary>
    /// 超时时间属性（毫秒）
    /// </summary>
    public static readonly DependencyProperty TimeoutProperty =
        DependencyProperty.Register(
            nameof(Timeout),
            typeof(int),
            typeof(ZyloSnackbar),
            new FrameworkPropertyMetadata(
                3000,
                FrameworkPropertyMetadataOptions.None,
                null,
                CoerceTimeout
            ),
            ValidateTimeout
        );

    /// <summary>
    /// 是否使用强调色属性
    /// </summary>
    public static readonly DependencyProperty UseAccentColorProperty =
        DependencyProperty.Register(
            nameof(UseAccentColor),
            typeof(bool),
            typeof(ZyloSnackbar),
            new FrameworkPropertyMetadata(
                true,
                FrameworkPropertyMetadataOptions.AffectsRender,
                OnUseAccentColorChanged
            )
        );

    /// <summary>
    /// 自定义背景色属性
    /// </summary>
    public static readonly DependencyProperty CustomBackgroundProperty =
        DependencyProperty.Register(
            nameof(CustomBackground),
            typeof(Brush),
            typeof(ZyloSnackbar),
            new FrameworkPropertyMetadata(
                null,
                FrameworkPropertyMetadataOptions.AffectsRender
            )
        );

    /// <summary>
    /// 是否显示关闭按钮属性
    /// </summary>
    public static readonly DependencyProperty ShowCloseButtonProperty =
        DependencyProperty.Register(
            nameof(ShowCloseButton),
            typeof(bool),
            typeof(ZyloSnackbar),
            new FrameworkPropertyMetadata(
                true,
                FrameworkPropertyMetadataOptions.AffectsRender
            )
        );

    /// <summary>
    /// 圆角半径属性
    /// </summary>
    public static readonly DependencyProperty CornerRadiusProperty =
        DependencyProperty.Register(
            nameof(CornerRadius),
            typeof(CornerRadius),
            typeof(ZyloSnackbar),
            new FrameworkPropertyMetadata(
                new CornerRadius(6),
                FrameworkPropertyMetadataOptions.AffectsRender
            )
        );

    #endregion

    #region 公共属性

    /// <summary>
    /// 获取或设置标题
    /// </summary>
    public string Title
    {
        get => (string)GetValue(TitleProperty);
        set => SetValue(TitleProperty, value);
    }

    /// <summary>
    /// 获取或设置消息内容
    /// </summary>
    public string Message
    {
        get => (string)GetValue(MessageProperty);
        set => SetValue(MessageProperty, value);
    }

    /// <summary>
    /// 获取或设置图标
    /// </summary>
    public SymbolRegular Icon
    {
        get => (SymbolRegular)GetValue(IconProperty);
        set => SetValue(IconProperty, value);
    }

    /// <summary>
    /// 获取或设置外观类型
    /// </summary>
    public ControlAppearance Appearance
    {
        get => (ControlAppearance)GetValue(AppearanceProperty);
        set => SetValue(AppearanceProperty, value);
    }

    /// <summary>
    /// 获取或设置是否显示
    /// </summary>
    public bool IsShown
    {
        get => (bool)GetValue(IsShownProperty);
        set => SetValue(IsShownProperty, value);
    }

    /// <summary>
    /// 获取或设置超时时间（毫秒）
    /// </summary>
    public int Timeout
    {
        get => (int)GetValue(TimeoutProperty);
        set => SetValue(TimeoutProperty, value);
    }

    /// <summary>
    /// 获取或设置是否使用强调色
    /// </summary>
    public bool UseAccentColor
    {
        get => (bool)GetValue(UseAccentColorProperty);
        set => SetValue(UseAccentColorProperty, value);
    }

    /// <summary>
    /// 获取或设置自定义背景色
    /// </summary>
    public Brush? CustomBackground
    {
        get => (Brush?)GetValue(CustomBackgroundProperty);
        set => SetValue(CustomBackgroundProperty, value);
    }

    /// <summary>
    /// 获取或设置是否显示关闭按钮
    /// </summary>
    public bool ShowCloseButton
    {
        get => (bool)GetValue(ShowCloseButtonProperty);
        set => SetValue(ShowCloseButtonProperty, value);
    }

    /// <summary>
    /// 获取或设置圆角半径
    /// </summary>
    public CornerRadius CornerRadius
    {
        get => (CornerRadius)GetValue(CornerRadiusProperty);
        set => SetValue(CornerRadiusProperty, value);
    }

    #endregion

    #region 路由事件

    /// <summary>
    /// 关闭事件
    /// </summary>
    public static readonly RoutedEvent ClosedEvent =
        EventManager.RegisterRoutedEvent(
            nameof(Closed),
            RoutingStrategy.Bubble,
            typeof(RoutedEventHandler),
            typeof(ZyloSnackbar)
        );

    /// <summary>
    /// 关闭事件
    /// </summary>
    public event RoutedEventHandler Closed
    {
        add => AddHandler(ClosedEvent, value);
        remove => RemoveHandler(ClosedEvent, value);
    }

    #endregion

    #region 私有字段

    private DispatcherTimer? _autoHideTimer;
    private Storyboard? _showStoryboard;
    private Storyboard? _hideStoryboard;

    #endregion

    #region 构造函数

    /// <summary>
    /// 静态构造函数
    /// </summary>
    static ZyloSnackbar()
    {
        DefaultStyleKeyProperty.OverrideMetadata(
            typeof(ZyloSnackbar),
            new FrameworkPropertyMetadata(typeof(ZyloSnackbar))
        );
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    public ZyloSnackbar()
    {
        Loaded += OnLoaded;
        Unloaded += OnUnloaded;
    }

    #endregion

    #region 重写方法

    /// <summary>
    /// 应用模板
    /// </summary>
    public override void OnApplyTemplate()
    {
        base.OnApplyTemplate();

        // 获取关闭按钮
        if (_closeButton != null)
        {
            _closeButton.Click -= OnCloseButtonClick;
        }

        _closeButton = GetTemplateChild(PART_CloseButton) as Button;

        if (_closeButton != null)
        {
            _closeButton.Click += OnCloseButtonClick;
        }

        // 创建动画
        CreateAnimations();

        // 更新背景色
        UpdateBackground();
    }

    #endregion

    #region 事件处理

    /// <summary>
    /// 控件加载事件
    /// </summary>
    private void OnLoaded(object sender, RoutedEventArgs e)
    {
        if (IsShown)
        {
            ShowWithAnimation();
        }
    }

    /// <summary>
    /// 控件卸载事件
    /// </summary>
    private void OnUnloaded(object sender, RoutedEventArgs e)
    {
        _autoHideTimer?.Stop();
        _autoHideTimer = null;
    }

    /// <summary>
    /// 关闭按钮点击事件
    /// </summary>
    private void OnCloseButtonClick(object sender, RoutedEventArgs e)
    {
        Hide();
    }

    #endregion

    #region 依赖属性回调

    /// <summary>
    /// 外观类型改变回调
    /// </summary>
    private static void OnAppearanceChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is ZyloSnackbar snackbar)
        {
            snackbar.UpdateBackground();
        }
    }

    /// <summary>
    /// 是否显示改变回调
    /// </summary>
    private static void OnIsShownChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is ZyloSnackbar snackbar)
        {
            if ((bool)e.NewValue)
            {
                snackbar.ShowWithAnimation();
            }
            else
            {
                snackbar.HideWithAnimation();
            }
        }
    }

    /// <summary>
    /// 是否使用强调色改变回调
    /// </summary>
    private static void OnUseAccentColorChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is ZyloSnackbar snackbar)
        {
            snackbar.UpdateBackground();
        }
    }

    /// <summary>
    /// 超时时间强制回调
    /// </summary>
    private static object CoerceTimeout(DependencyObject d, object value)
    {
        var timeout = (int)value;
        return Math.Max(500, Math.Min(30000, timeout)); // 限制在 500ms - 30s 之间
    }

    /// <summary>
    /// 超时时间验证回调
    /// </summary>
    private static bool ValidateTimeout(object value)
    {
        return value is int timeout && timeout > 0;
    }

    #endregion

    #region 公共方法

    /// <summary>
    /// 显示 Snackbar
    /// </summary>
    public void Show()
    {
        IsShown = true;
    }

    /// <summary>
    /// 隐藏 Snackbar
    /// </summary>
    public void Hide()
    {
        IsShown = false;
    }

    /// <summary>
    /// 显示 Snackbar 并设置内容
    /// </summary>
    public void Show(string title, string message, SymbolRegular icon = SymbolRegular.Info24,
                     ControlAppearance appearance = ControlAppearance.Primary, int timeout = 3000)
    {
        Title = title;
        Message = message;
        Icon = icon;
        Appearance = appearance;
        Timeout = timeout;
        Show();
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 创建动画 - 自适应高度
    /// </summary>
    private void CreateAnimations()
    {
        // 显示动画 - 从下方滑入
        _showStoryboard = new Storyboard();
        var showTransform = new DoubleAnimation
        {
            From = 100, // 增加初始偏移，适应可能的更大高度
            To = 0,
            Duration = TimeSpan.FromMilliseconds(300),
            EasingFunction = new BackEase { EasingMode = EasingMode.EaseOut, Amplitude = 0.3 }
        };
        Storyboard.SetTarget(showTransform, this);
        Storyboard.SetTargetProperty(showTransform, new PropertyPath("(UIElement.RenderTransform).(TranslateTransform.Y)"));
        _showStoryboard.Children.Add(showTransform);

        var showOpacity = new DoubleAnimation
        {
            From = 0,
            To = 1,
            Duration = TimeSpan.FromMilliseconds(250)
        };
        Storyboard.SetTarget(showOpacity, this);
        Storyboard.SetTargetProperty(showOpacity, new PropertyPath("Opacity"));
        _showStoryboard.Children.Add(showOpacity);

        // 隐藏动画 - 向下滑出
        _hideStoryboard = new Storyboard();
        var hideTransform = new DoubleAnimation
        {
            From = 0,
            To = 100, // 增加结束偏移，适应可能的更大高度
            Duration = TimeSpan.FromMilliseconds(250),
            EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseIn }
        };
        Storyboard.SetTarget(hideTransform, this);
        Storyboard.SetTargetProperty(hideTransform, new PropertyPath("(UIElement.RenderTransform).(TranslateTransform.Y)"));
        _hideStoryboard.Children.Add(hideTransform);

        var hideOpacity = new DoubleAnimation
        {
            From = 1,
            To = 0,
            Duration = TimeSpan.FromMilliseconds(200)
        };
        Storyboard.SetTarget(hideOpacity, this);
        Storyboard.SetTargetProperty(hideOpacity, new PropertyPath("Opacity"));
        _hideStoryboard.Children.Add(hideOpacity);

        _hideStoryboard.Completed += (s, e) =>
        {
            Visibility = Visibility.Collapsed;
            RaiseEvent(new RoutedEventArgs(ClosedEvent));
        };

        // 设置 RenderTransform
        RenderTransform = new TranslateTransform();
    }

    /// <summary>
    /// 带动画显示
    /// </summary>
    private void ShowWithAnimation()
    {
        if (!IsLoaded) return;

        Visibility = Visibility.Visible;
        _showStoryboard?.Begin();

        // 启动自动隐藏定时器
        StartAutoHideTimer();
    }

    /// <summary>
    /// 带动画隐藏
    /// </summary>
    private void HideWithAnimation()
    {
        if (!IsLoaded) return;

        _autoHideTimer?.Stop();
        _hideStoryboard?.Begin();
    }

    /// <summary>
    /// 启动自动隐藏定时器
    /// </summary>
    private void StartAutoHideTimer()
    {
        _autoHideTimer?.Stop();
        _autoHideTimer = new DispatcherTimer
        {
            Interval = TimeSpan.FromMilliseconds(Timeout)
        };
        _autoHideTimer.Tick += (s, e) =>
        {
            _autoHideTimer.Stop();
            Hide();
        };
        _autoHideTimer.Start();
    }

    /// <summary>
    /// 更新背景色 - 主题适应性设计
    /// </summary>
    private void UpdateBackground()
    {
        if (CustomBackground != null)
        {
            Background = CustomBackground;
            return;
        }

        // 主题适应性设计：始终使用应用程序背景色
        // 状态通过左侧的状态指示器来表示，而不是整个背景色
        Background = (Brush?)FindResource("ApplicationBackgroundBrush")
                    ?? (Brush?)FindResource("SystemControlBackgroundBaseMediumBrush")
                    ?? new SolidColorBrush(Color.FromRgb(248, 248, 248)); // 浅色主题默认值

        // 确保前景色与背景色形成良好对比
        Foreground = (Brush?)FindResource("TextFillColorPrimaryBrush")
                    ?? new SolidColorBrush(Color.FromRgb(32, 32, 32)); // 深色文字
    }

    #endregion
}
