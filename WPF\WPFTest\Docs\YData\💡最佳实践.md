# 💡 Zylo.YData 最佳实践

本文档汇总了使用 Zylo.YData 的最佳实践，帮助您编写高效、安全、可维护的代码。

## 🚀 性能优化

### 1. 查询优化

#### ✅ 使用索引友好的查询
```csharp
// ✅ 好的做法 - 使用索引字段查询
var users = await YData.Select<User>()
    .Where(u => u.Email == "<EMAIL>")  // Email有索引
    .ToListAsync();

// ❌ 避免 - 函数调用破坏索引
var users = await YData.Select<User>()
    .Where(u => u.Email.ToLower() == "<EMAIL>")
    .ToListAsync();
```

#### ✅ 合理使用分页
```csharp
// ✅ 好的做法 - 始终使用分页
var result = await YData.Select<User>()
    .Where(u => u.IsActive)
    .OrderBy(u => u.CreateTime)
    .ToPagedResultAsync(pageIndex: 1, pageSize: 20);

// ❌ 避免 - 查询大量数据
var allUsers = await YData.GetAllAsync<User>();  // 可能返回百万条记录
```

#### ✅ 选择必要的字段
```csharp
// ✅ 好的做法 - 只选择需要的字段
var userSummary = await YData.Select<User>()
    .Where(u => u.IsActive)
    .ToListAsync(u => new UserSummary 
    { 
        Id = u.Id, 
        Name = u.Name, 
        Email = u.Email 
    });

// ❌ 避免 - 查询所有字段
var users = await YData.Select<User>()
    .Where(u => u.IsActive)
    .ToListAsync();  // 包含大字段如 ProfileImage
```

### 2. 批量操作优化

#### ✅ 使用批量插入
```csharp
// ✅ 好的做法 - 批量插入
var users = GenerateUsers(10000);
await YData.Insert<User>().YBatchInsertAsync(users, batchSize: 1000);

// ❌ 避免 - 逐个插入
foreach (var user in users)
{
    await YData.InsertAsync(user);  // 性能很差
}
```

#### ✅ 合理设置批次大小
```csharp
// 根据数据大小调整批次
if (users.Count < 100)
{
    await YData.Insert<User>().AppendData(users).ExecuteAffrowsAsync();
}
else if (users.Count < 10000)
{
    await YData.Insert<User>().YBatchInsertAsync(users, batchSize: 500);
}
else
{
    await YData.Insert<User>().YBatchInsertAsync(users, batchSize: 2000);
}
```

### 3. 连接池优化

#### ✅ 合理配置连接池
```csharp
// 根据应用规模配置连接池
builder.Services.AddYData(options =>
{
    // 小型应用
    options.MaxConnectionPoolSize = 50;
    options.MinConnectionPoolSize = 5;
    
    // 大型应用
    // options.MaxConnectionPoolSize = 200;
    // options.MinConnectionPoolSize = 20;
});
```

## 🔒 安全最佳实践

### 1. 连接字符串安全

#### ✅ 使用配置管理
```csharp
// ✅ 好的做法 - 使用配置文件
builder.Services.AddYData(options =>
{
    options.ConnectionString = builder.Configuration.GetConnectionString("DefaultConnection")!;
});

// ❌ 避免 - 硬编码连接字符串
builder.Services.AddYDataAuto("Server=localhost;Database=MyApp;User=sa;Password=******;");
```

#### ✅ 使用用户机密和环境变量
```bash
# 开发环境 - 使用用户机密
dotnet user-secrets set "ConnectionStrings:DefaultConnection" "Server=...;Password=..."

# 生产环境 - 使用环境变量
export DATABASE_CONNECTION_STRING="Server=...;Password=..."
```

### 2. 敏感数据保护

#### ✅ 生产环境配置
```csharp
if (builder.Environment.IsProduction())
{
    builder.Services.AddYData(options =>
    {
        options.EnableSensitiveDataLogging = false;  // 关闭敏感数据日志
        options.EnableDetailedErrors = false;        // 关闭详细错误信息
    });
}
```

### 3. SQL 注入防护

#### ✅ 使用参数化查询
```csharp
// ✅ 好的做法 - 自动参数化
var users = await YData.Select<User>()
    .Where(u => u.Name == userName)  // 自动参数化
    .ToListAsync();

// ❌ 避免 - 字符串拼接（如果使用原生SQL）
// var sql = $"SELECT * FROM Users WHERE Name = '{userName}'";  // 危险！
```

## 🏗️ 架构最佳实践

### 1. 依赖注入模式

#### ✅ 服务层设计
```csharp
public interface IUserService
{
    Task<PagedResult<User>> GetUsersAsync(int pageIndex, int pageSize, string? nameFilter = null);
    Task<User?> GetUserByIdAsync(int id);
    Task<User> CreateUserAsync(CreateUserRequest request);
    Task<bool> UpdateUserAsync(int id, UpdateUserRequest request);
    Task<bool> DeleteUserAsync(int id);
}

public class UserService : IUserService
{
    private readonly IYDataContext _context;
    private readonly ILogger<UserService> _logger;
    
    public UserService(IYDataContext context, ILogger<UserService> logger)
    {
        _context = context;
        _logger = logger;
    }
    
    public async Task<PagedResult<User>> GetUsersAsync(int pageIndex, int pageSize, string? nameFilter = null)
    {
        try
        {
            var query = _context.Select<User>()
                .Where(u => u.IsActive)
                .WhereIf(!string.IsNullOrEmpty(nameFilter), u => u.Name.Contains(nameFilter))
                .OrderBy(u => u.CreateTime);
                
            return await query.ToPagedResultAsync(pageIndex, pageSize);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户列表失败");
            throw;
        }
    }
}
```

### 2. 仓储模式（可选）

#### ✅ 通用仓储接口
```csharp
public interface IRepository<T> where T : class
{
    Task<T?> GetByIdAsync(object id);
    Task<List<T>> GetAllAsync();
    Task<PagedResult<T>> GetPagedAsync(int pageIndex, int pageSize, Expression<Func<T, bool>>? predicate = null);
    Task<T> AddAsync(T entity);
    Task<bool> UpdateAsync(T entity);
    Task<bool> DeleteAsync(object id);
}

public class Repository<T> : IRepository<T> where T : class
{
    private readonly IYDataContext _context;
    
    public Repository(IYDataContext context)
    {
        _context = context;
    }
    
    public async Task<T?> GetByIdAsync(object id)
    {
        return await _context.GetAsync<T>(id);
    }
    
    public async Task<PagedResult<T>> GetPagedAsync(int pageIndex, int pageSize, Expression<Func<T, bool>>? predicate = null)
    {
        return await _context.GetPagedAsync<T>(pageIndex, pageSize, predicate);
    }
    
    // ... 其他方法实现
}
```

### 3. 工作单元模式

#### ✅ 事务管理
```csharp
public class OrderService
{
    private readonly IYDataContext _context;
    
    public OrderService(IYDataContext context)
    {
        _context = context;
    }
    
    public async Task<Order> CreateOrderAsync(CreateOrderRequest request)
    {
        return await _context.TransactionAsync(async () =>
        {
            // 创建订单
            var order = new Order
            {
                UserId = request.UserId,
                TotalAmount = request.Items.Sum(i => i.Price * i.Quantity),
                CreateTime = DateTime.Now
            };
            await _context.InsertAsync(order);
            
            // 创建订单项
            var orderItems = request.Items.Select(i => new OrderItem
            {
                OrderId = order.Id,
                ProductId = i.ProductId,
                Quantity = i.Quantity,
                Price = i.Price
            }).ToList();
            
            await _context.Insert<OrderItem>()
                .AppendData(orderItems)
                .ExecuteAffrowsAsync();
            
            // 更新库存
            foreach (var item in request.Items)
            {
                await _context.Update<Product>()
                    .Set(p => p.Stock, p => p.Stock - item.Quantity)
                    .Where(p => p.Id == item.ProductId)
                    .ExecuteAffrowsAsync();
            }
            
            return order;
        });
    }
}
```

## 📊 数据建模最佳实践

### 1. 实体设计

#### ✅ 良好的实体设计
```csharp
[Table("users")]
public class User
{
    [Key]
    [Column("id")]
    public int Id { get; set; }
    
    [Required]
    [StringLength(50)]
    [Column("name")]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    [EmailAddress]
    [StringLength(100)]
    [Column("email")]
    public string Email { get; set; } = string.Empty;
    
    [Range(0, 150)]
    [Column("age")]
    public int Age { get; set; }
    
    [Column("is_active")]
    public bool IsActive { get; set; } = true;
    
    [Column("create_time")]
    public DateTime CreateTime { get; set; } = DateTime.Now;
    
    [Column("update_time")]
    public DateTime? UpdateTime { get; set; }
    
    // 导航属性
    public virtual List<Order> Orders { get; set; } = new();
    public virtual UserProfile? Profile { get; set; }
}
```

### 2. 索引设计

#### ✅ 合理的索引策略
```csharp
[Table("users")]
[Index(nameof(Email), IsUnique = true)]  // 唯一索引
[Index(nameof(IsActive), nameof(CreateTime))]  // 复合索引
public class User
{
    // ... 属性定义
}
```

## 🧪 测试最佳实践

### 1. 单元测试

#### ✅ 使用内存数据库
```csharp
public class UserServiceTests : IDisposable
{
    private readonly IYDataContext _context;
    
    public UserServiceTests()
    {
        // 使用内存数据库进行测试
        YData.ConfigureAuto("Data Source=:memory:");
        _context = YData.Context;
        
        // 创建测试表结构
        YData.FreeSql.CodeFirst.SyncStructure<User>();
    }
    
    [Fact]
    public async Task CreateUser_ShouldReturnValidUser()
    {
        // Arrange
        var service = new UserService(_context, Mock.Of<ILogger<UserService>>());
        var request = new CreateUserRequest { Name = "测试用户", Email = "<EMAIL>" };
        
        // Act
        var user = await service.CreateUserAsync(request);
        
        // Assert
        Assert.NotNull(user);
        Assert.True(user.Id > 0);
        Assert.Equal(request.Name, user.Name);
    }
    
    public void Dispose()
    {
        _context?.Dispose();
    }
}
```

### 2. 集成测试

#### ✅ 测试数据库隔离
```csharp
public class IntegrationTestBase : IDisposable
{
    protected readonly string TestDbPath;
    protected readonly IYDataContext Context;
    
    public IntegrationTestBase()
    {
        TestDbPath = Path.Combine(Path.GetTempPath(), $"test_{Guid.NewGuid():N}.db");
        YData.ConfigureAuto($"Data Source={TestDbPath}");
        Context = YData.Context;
        
        // 初始化测试数据
        InitializeTestData();
    }
    
    private void InitializeTestData()
    {
        YData.FreeSql.CodeFirst.SyncStructure<User>();
        // 插入测试数据...
    }
    
    public void Dispose()
    {
        Context?.Dispose();
        if (File.Exists(TestDbPath))
        {
            File.Delete(TestDbPath);
        }
    }
}
```

## 🔧 调试和监控

### 1. SQL 监控

#### ✅ 开发环境监控
```csharp
if (builder.Environment.IsDevelopment())
{
    builder.Services.AddYData(options =>
    {
        options.EnableMonitorCommand = true;
        options.EnableSensitiveDataLogging = true;
    });
    
    // 添加SQL日志
    YData.FreeSql.Aop.CommandBefore += (sender, e) =>
    {
        Console.WriteLine($"SQL: {e.Command.CommandText}");
    };
}
```

### 2. 性能监控

#### ✅ 慢查询监控
```csharp
builder.Services.AddYData(options =>
{
    options.SlowQueryThreshold = TimeSpan.FromMilliseconds(500);
});

// 监控慢查询
YData.FreeSql.Aop.CommandAfter += (sender, e) =>
{
    if (e.ElapsedMilliseconds > 500)
    {
        var logger = serviceProvider.GetService<ILogger<Program>>();
        logger?.LogWarning("慢查询检测: {ElapsedMs}ms - {Sql}", 
            e.ElapsedMilliseconds, e.Command.CommandText);
    }
};
```

## ❌ 常见错误和避免方法

### 1. 内存泄漏
```csharp
// ❌ 错误 - 未释放上下文
public async Task<List<User>> GetUsersAsync()
{
    var context = YData.Manager.GetDatabase("main");
    return await context.Select<User>().ToListAsync();
    // context 未释放
}

// ✅ 正确 - 使用依赖注入
public class UserService
{
    private readonly IYDataContext _context;  // 由DI容器管理生命周期
    
    public async Task<List<User>> GetUsersAsync()
    {
        return await _context.Select<User>().ToListAsync();
    }
}
```

### 2. N+1 查询问题
```csharp
// ❌ 错误 - N+1 查询
var users = await YData.Select<User>().ToListAsync();
foreach (var user in users)
{
    var orders = await YData.Select<Order>().Where(o => o.UserId == user.Id).ToListAsync();
    // 每个用户都执行一次查询
}

// ✅ 正确 - 使用 Include
var users = await YData.Select<User>()
    .YInclude(u => u.Orders)
    .ToListAsync();
```

### 3. 事务使用错误
```csharp
// ❌ 错误 - 嵌套事务
await YData.TransactionAsync(async () =>
{
    await YData.InsertAsync(user);
    
    await YData.TransactionAsync(async () =>  // 嵌套事务可能有问题
    {
        await YData.InsertAsync(profile);
    });
});

// ✅ 正确 - 单一事务
await YData.TransactionAsync(async () =>
{
    await YData.InsertAsync(user);
    await YData.InsertAsync(profile);
});
```

---

**🎯 记住：** 好的实践需要在项目中持续应用和改进。定期回顾和优化您的代码！
