<!-- ListView WPF-UI 标准样式示例 -->
<!-- 展示如何使用 WPF-UI 库的标准 ListView 样式 -->

<StackPanel>
    <!-- 基础样式 -->
    <TextBlock Text="基础样式：" FontWeight="Medium" Margin="0,0,0,8"/>
    
    <!-- 标准样式 -->
    <ListView ItemsSource="{Binding SimpleItems}"
              Style="{StaticResource ListViewStyle}"
              Height="100"
              Margin="0,0,0,16">
        <!-- 标准的 ListView 样式 -->
    </ListView>

    <!-- 尺寸变体 -->
    <TextBlock Text="尺寸变体：" FontWeight="Medium" Margin="0,0,0,8"/>
    
    <!-- 小型样式 -->
    <ListView ItemsSource="{Binding SimpleItems}"
              Style="{StaticResource SmallListViewStyle}"
              Height="80"
              Margin="0,0,0,8">
        <!-- 小型 ListView，适合紧凑布局 -->
    </ListView>
    
    <!-- 大型样式 -->
    <ListView ItemsSource="{Binding SimpleItems}"
              Style="{StaticResource LargeListViewStyle}"
              Height="120"
              Margin="0,0,0,16">
        <!-- 大型 ListView，适合重要内容 -->
    </ListView>

    <!-- 外观变体 -->
    <TextBlock Text="外观变体：" FontWeight="Medium" Margin="0,0,0,8"/>
    
    <!-- 强调色样式 -->
    <ListView ItemsSource="{Binding SimpleItems}"
              Style="{StaticResource AccentListViewStyle}"
              Height="100"
              Margin="0,0,0,8">
        <!-- 使用系统强调色的 ListView -->
    </ListView>
    
    <!-- 成功样式 -->
    <ListView ItemsSource="{Binding SimpleItems}"
              Style="{StaticResource SuccessListViewStyle}"
              Height="100"
              Margin="0,0,0,8">
        <!-- 绿色边框，表示成功状态 -->
    </ListView>
    
    <!-- 警告样式 -->
    <ListView ItemsSource="{Binding SimpleItems}"
              Style="{StaticResource WarningListViewStyle}"
              Height="100"
              Margin="0,0,0,8">
        <!-- 橙色边框，表示警告状态 -->
    </ListView>
    
    <!-- 危险样式 -->
    <ListView ItemsSource="{Binding SimpleItems}"
              Style="{StaticResource DangerListViewStyle}"
              Height="100"
              Margin="0,0,0,8">
        <!-- 红色边框，表示危险状态 -->
    </ListView>

    <!-- 特殊效果 -->
    <TextBlock Text="特殊效果：" FontWeight="Medium" Margin="0,0,0,8"/>
    
    <!-- 透明样式 -->
    <ListView ItemsSource="{Binding SimpleItems}"
              Style="{StaticResource TransparentListViewStyle}"
              Height="100"
              Margin="0,0,0,8">
        <!-- 透明背景，融入父容器 -->
    </ListView>
    
    <!-- 紧凑样式 -->
    <ListView ItemsSource="{Binding SimpleItems}"
              Style="{StaticResource CompactListViewStyle}"
              Height="80">
        <!-- 最小化间距，最大化内容密度 -->
    </ListView>
</StackPanel>

<!-- 
样式使用指南：

1. 基础样式：
   - ListViewStyle: 标准样式，适合大多数场景
   - SmallListViewStyle: 小型样式，适合紧凑布局
   - LargeListViewStyle: 大型样式，适合重要内容

2. 状态样式：
   - AccentListViewStyle: 强调色样式，突出显示
   - SuccessListViewStyle: 成功状态，绿色边框
   - WarningListViewStyle: 警告状态，橙色边框
   - DangerListViewStyle: 危险状态，红色边框

3. 特殊效果：
   - TransparentListViewStyle: 透明背景
   - CompactListViewStyle: 紧凑布局

4. 选择建议：
   - 数据展示：使用 ListViewStyle 或 LargeListViewStyle
   - 状态指示：根据内容选择对应的状态样式
   - 空间受限：使用 SmallListViewStyle 或 CompactListViewStyle
   - 现代界面：使用 TransparentListViewStyle 或 AccentListViewStyle
-->
