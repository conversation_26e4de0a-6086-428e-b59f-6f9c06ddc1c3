<!-- gong-wpf-dragdrop 列表拖拽样式和主题 XAML 示例 -->

<!-- 1. 自定义拖拽装饰器样式 -->
<ListView.Resources>
    <!-- 自定义拖拽装饰器模板 -->
    <DataTemplate x:Key="CustomDragAdorner">
        <Border Background="{DynamicResource AccentFillColorDefaultBrush}"
                BorderBrush="{DynamicResource AccentStrokeColorDefaultBrush}"
                BorderThickness="2"
                CornerRadius="8"
                Padding="12,8"
                Opacity="0.9">
            <StackPanel Orientation="Horizontal">
                <ui:SymbolIcon Symbol="ArrowMove24" 
                               FontSize="16"
                               Foreground="White"
                               Margin="0,0,8,0"/>
                <TextBlock Text="{Binding Name}" 
                           Foreground="White"
                           FontWeight="Medium"/>
                <Border Background="White"
                        CornerRadius="10"
                        Padding="6,2"
                        Margin="8,0,0,0">
                    <TextBlock Text="{Binding Priority, StringFormat='优先级: {0}'}"
                               FontSize="10"
                               Foreground="{DynamicResource AccentFillColorDefaultBrush}"/>
                </Border>
            </StackPanel>
        </Border>
    </DataTemplate>
    
    <!-- 放置目标高亮样式 -->
    <Style x:Key="DropTargetHighlightStyle" TargetType="Border">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Style.Triggers>
            <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType=ListView}, Path=(dd:DragDrop.IsDropTarget)}" Value="True">
                <Setter Property="Background" Value="{DynamicResource SystemFillColorSuccessBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource SystemFillColorSuccessBrush}"/>
                <Setter Property="Opacity" Value="0.3"/>
            </DataTrigger>
        </Style.Triggers>
    </Style>
</ListView.Resources>

<!-- 2. 使用自定义装饰器的 ListView -->
<ListView ItemsSource="{Binding SourceItems}"
          dd:DragDrop.IsDragSource="True"
          dd:DragDrop.IsDropTarget="True"
          dd:DragDrop.DragHandler="{Binding}"
          dd:DragDrop.DropHandler="{Binding}"
          dd:DragDrop.DragAdornerTemplate="{StaticResource CustomDragAdorner}"
          dd:DragDrop.UseDefaultDragAdorner="False">
    <ListView.ItemTemplate>
        <DataTemplate>
            <Border Style="{StaticResource DropTargetHighlightStyle}"
                    Padding="8" 
                    Margin="2"
                    CornerRadius="4">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- 拖拽图标 -->
                    <ui:SymbolIcon Grid.Column="0"
                                   Symbol="ArrowMove24" 
                                   FontSize="16"
                                   Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                   Margin="0,0,8,0"
                                   Cursor="Hand"/>
                    
                    <!-- 项目信息 -->
                    <StackPanel Grid.Column="1">
                        <TextBlock Text="{Binding Name}" 
                                   FontWeight="Medium"
                                   FontSize="14"/>
                        <TextBlock Text="{Binding Description}" 
                                   FontSize="12"
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   TextWrapping="Wrap"/>
                        <TextBlock Text="{Binding Category}" 
                                   FontSize="11"
                                   Foreground="{DynamicResource TextFillColorTertiaryBrush}"/>
                    </StackPanel>
                    
                    <!-- 优先级标签 -->
                    <Border Grid.Column="2"
                            Background="{DynamicResource AccentFillColorDefaultBrush}"
                            CornerRadius="12"
                            Padding="8,4"
                            MinWidth="24">
                        <TextBlock Text="{Binding Priority}" 
                                   FontSize="12"
                                   Foreground="White"
                                   FontWeight="Bold"
                                   HorizontalAlignment="Center"/>
                    </Border>
                </Grid>
            </Border>
        </DataTemplate>
    </ListView.ItemTemplate>
</ListView>

<!-- 3. 不同状态的视觉样式 -->
<ListView ItemsSource="{Binding Items}"
          dd:DragDrop.IsDragSource="True"
          dd:DragDrop.IsDropTarget="True">
    <ListView.ItemTemplate>
        <DataTemplate>
            <Border Padding="8" Margin="2" CornerRadius="4">
                <Border.Style>
                    <Style TargetType="Border">
                        <!-- 默认样式 -->
                        <Setter Property="Background" Value="{DynamicResource CardBackgroundFillColorDefaultBrush}"/>
                        <Setter Property="BorderBrush" Value="{DynamicResource CardStrokeColorDefaultBrush}"/>
                        <Setter Property="BorderThickness" Value="1"/>
                        
                        <Style.Triggers>
                            <!-- 选中状态 -->
                            <DataTrigger Binding="{Binding IsSelected}" Value="True">
                                <Setter Property="Background" Value="{DynamicResource AccentFillColorSecondaryBrush}"/>
                                <Setter Property="BorderBrush" Value="{DynamicResource AccentFillColorDefaultBrush}"/>
                                <Setter Property="BorderThickness" Value="2"/>
                            </DataTrigger>
                            
                            <!-- 锁定状态 -->
                            <DataTrigger Binding="{Binding IsLocked}" Value="True">
                                <Setter Property="Background" Value="{DynamicResource SystemFillColorCriticalBrush}"/>
                                <Setter Property="Opacity" Value="0.6"/>
                            </DataTrigger>
                            
                            <!-- 只读状态 -->
                            <DataTrigger Binding="{Binding IsReadOnly}" Value="True">
                                <Setter Property="Background" Value="{DynamicResource SystemFillColorNeutralBrush}"/>
                            </DataTrigger>
                            
                            <!-- 拖拽进行中 -->
                            <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType=ListView}, Path=(dd:DragDrop.IsDragInProgress)}" Value="True">
                                <Setter Property="BorderBrush" Value="{DynamicResource SystemFillColorAttentionBrush}"/>
                                <Setter Property="BorderThickness" Value="2"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Border.Style>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- 状态图标 -->
                    <ui:SymbolIcon Grid.Column="0" Margin="0,0,8,0">
                        <ui:SymbolIcon.Style>
                            <Style TargetType="ui:SymbolIcon">
                                <Setter Property="Symbol" Value="Circle24"/>
                                <Setter Property="Foreground" Value="{DynamicResource TextFillColorSecondaryBrush}"/>
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsLocked}" Value="True">
                                        <Setter Property="Symbol" Value="Lock24"/>
                                        <Setter Property="Foreground" Value="{DynamicResource SystemFillColorCriticalBrush}"/>
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding IsReadOnly}" Value="True">
                                        <Setter Property="Symbol" Value="Eye24"/>
                                        <Setter Property="Foreground" Value="{DynamicResource SystemFillColorNeutralBrush}"/>
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding IsSelected}" Value="True">
                                        <Setter Property="Symbol" Value="CheckmarkCircle24"/>
                                        <Setter Property="Foreground" Value="{DynamicResource AccentTextFillColorPrimaryBrush}"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </ui:SymbolIcon.Style>
                    </ui:SymbolIcon>
                    
                    <!-- 内容 -->
                    <StackPanel Grid.Column="1">
                        <TextBlock Text="{Binding Name}" FontWeight="Medium"/>
                        <TextBlock Text="{Binding Category}" 
                                   FontSize="11"
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                    </StackPanel>
                    
                    <!-- 优先级 -->
                    <TextBlock Grid.Column="2"
                               Text="{Binding Priority}"
                               FontWeight="Bold"
                               VerticalAlignment="Center"/>
                </Grid>
            </Border>
        </DataTemplate>
    </ListView.ItemTemplate>
</ListView>

<!-- 4. 响应式拖拽区域 -->
<ListView ItemsSource="{Binding Items}"
          dd:DragDrop.IsDragSource="True"
          dd:DragDrop.IsDropTarget="True">
    <ListView.Style>
        <Style TargetType="ListView">
            <Setter Property="Background" Value="{DynamicResource CardBackgroundFillColorDefaultBrush}"/>
            <Setter Property="BorderBrush" Value="{DynamicResource CardStrokeColorDefaultBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="8"/>
            
            <Style.Triggers>
                <!-- 拖拽悬停状态 -->
                <Trigger Property="dd:DragDrop.IsDropTarget" Value="True">
                    <Setter Property="Background" Value="{DynamicResource SystemFillColorSuccessBrush}"/>
                    <Setter Property="BorderBrush" Value="{DynamicResource SystemFillColorSuccessBrush}"/>
                    <Setter Property="BorderThickness" Value="2"/>
                    <Setter Property="Opacity" Value="0.8"/>
                </Trigger>
                
                <!-- 拖拽进行中状态 -->
                <Trigger Property="dd:DragDrop.IsDragInProgress" Value="True">
                    <Setter Property="BorderBrush" Value="{DynamicResource SystemFillColorAttentionBrush}"/>
                    <Setter Property="BorderThickness" Value="2"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </ListView.Style>
</ListView>

<!-- 5. 动画效果 -->
<ListView ItemsSource="{Binding Items}"
          dd:DragDrop.IsDragSource="True"
          dd:DragDrop.IsDropTarget="True">
    <ListView.ItemContainerStyle>
        <Style TargetType="ListViewItem">
            <Setter Property="RenderTransform">
                <Setter.Value>
                    <ScaleTransform ScaleX="1" ScaleY="1"/>
                </Setter.Value>
            </Setter>
            <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
            
            <Style.Triggers>
                <!-- 鼠标悬停动画 -->
                <Trigger Property="IsMouseOver" Value="True">
                    <Trigger.EnterActions>
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                               To="1.05" Duration="0:0:0.2"/>
                                <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                               To="1.05" Duration="0:0:0.2"/>
                            </Storyboard>
                        </BeginStoryboard>
                    </Trigger.EnterActions>
                    <Trigger.ExitActions>
                        <BeginStoryboard>
                            <Storyboard>
                                <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                               To="1" Duration="0:0:0.2"/>
                                <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                               To="1" Duration="0:0:0.2"/>
                            </Storyboard>
                        </BeginStoryboard>
                    </Trigger.ExitActions>
                </Trigger>
            </Style.Triggers>
        </Style>
    </ListView.ItemContainerStyle>
</ListView>

<!-- 命名空间声明 -->
xmlns:dd="urn:gong-wpf-dragdrop"
xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"

<!-- 样式要点说明 -->
<!--
1. 自定义拖拽装饰器：使用 DragAdornerTemplate 属性
2. 放置目标视觉反馈：通过 DropTargetAdorner 和样式触发器
3. 状态样式：根据数据绑定属性显示不同状态
4. 响应式设计：根据拖拽状态改变外观
5. 动画效果：使用 Storyboard 添加平滑过渡
-->
