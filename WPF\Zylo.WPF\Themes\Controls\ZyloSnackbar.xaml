<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
                    xmlns:controls="clr-namespace:Zylo.WPF.Controls">

    <!-- ZyloSnackbar 默认样式 - 自适应内容大小 -->
    <Style TargetType="{x:Type controls:ZyloSnackbar}">
        <Setter Property="MinHeight" Value="48"/>
        <Setter Property="HorizontalAlignment" Value="Stretch"/>
        <Setter Property="VerticalAlignment" Value="Bottom"/>
        <Setter Property="Margin" Value="0"/>
        <Setter Property="Padding" Value="24,12"/>
        <Setter Property="Background" Value="{DynamicResource ApplicationBackgroundBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ControlElevationBorderBrush}"/>
        <Setter Property="Visibility" Value="Collapsed"/>
        <Setter Property="Opacity" Value="0"/>
        <Setter Property="RenderTransform">
            <Setter.Value>
                <TranslateTransform Y="100"/>
            </Setter.Value>
        </Setter>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="{DynamicResource ControlShadowColor}"
                                  Opacity="0.15"
                                  ShadowDepth="2"
                                  BlurRadius="12"/>
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type controls:ZyloSnackbar}">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{TemplateBinding CornerRadius}"
                            Padding="{TemplateBinding Padding}">
                        <!-- 简洁风格布局 - 全宽设计 -->
                        <Grid MinHeight="48">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- 内容区域 - 居中设计 -->
                            <StackPanel Grid.Column="0"
                                        VerticalAlignment="Center"
                                        HorizontalAlignment="Center"
                                        Margin="16,12">
                                <!-- 标题 -->
                                <TextBlock Text="{TemplateBinding Title}"
                                           FontWeight="SemiBold"
                                           FontSize="15"
                                           Foreground="{TemplateBinding Foreground}"
                                           Margin="0,0,0,4"
                                           TextAlignment="Left"
                                           TextTrimming="CharacterEllipsis"
                                           TextWrapping="Wrap">
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Style.Triggers>
                                                <Trigger Property="Text" Value="">
                                                    <Setter Property="Visibility" Value="Collapsed"/>
                                                </Trigger>
                                                <Trigger Property="Text" Value="{x:Null}">
                                                    <Setter Property="Visibility" Value="Collapsed"/>
                                                </Trigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>

                                <!-- 消息内容 - 居中对齐 -->
                                <TextBlock Text="{TemplateBinding Message}"
                                           FontSize="{TemplateBinding FontSize}"
                                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                           TextAlignment="Center"
                                           TextWrapping="Wrap"
                                           LineHeight="20"
                                           MaxWidth="600"
                                           Margin="0,0,0,4"/>

                                <!-- 自定义内容 -->
                                <ContentPresenter Content="{TemplateBinding Content}"
                                                  ContentTemplate="{TemplateBinding ContentTemplate}"
                                                  HorizontalAlignment="Left"
                                                  Margin="0,4,0,0">
                                    <ContentPresenter.Style>
                                        <Style TargetType="ContentPresenter">
                                            <Style.Triggers>
                                                <Trigger Property="Content" Value="{x:Null}">
                                                    <Setter Property="Visibility" Value="Collapsed"/>
                                                </Trigger>
                                            </Style.Triggers>
                                        </Style>
                                    </ContentPresenter.Style>
                                </ContentPresenter>
                            </StackPanel>

                            <!-- 关闭按钮 - 绝对右边 -->
                            <ui:Button x:Name="PART_CloseButton"
                                       Grid.Column="1"
                                       Content="&#xE8BB;"
                                       Appearance="Transparent"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       FontFamily="Segoe MDL2 Assets"
                                       FontSize="12"
                                       Width="32"
                                       Height="32"
                                       Padding="0"
                                       Margin="0,0,12,0"
                                       VerticalAlignment="Center"
                                       HorizontalAlignment="Right"
                                       ToolTip="关闭">
                                <ui:Button.Style>
                                    <Style TargetType="ui:Button" BasedOn="{StaticResource {x:Type ui:Button}}">
                                        <Setter Property="Visibility" Value="Visible"/>
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding ShowCloseButton, RelativeSource={RelativeSource TemplatedParent}}"
                                                         Value="False">
                                                <Setter Property="Visibility" Value="Collapsed"/>
                                            </DataTrigger>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </ui:Button.Style>
                            </ui:Button>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>

        <!-- 状态触发器 - 主题适应性设计 -->
        <Style.Triggers>
            <!-- 不使用强调色时的样式 -->
            <Trigger Property="UseAccentColor" Value="False">
                <Setter Property="Background" Value="{DynamicResource ApplicationBackgroundBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource ControlElevationBorderBrush}"/>
            </Trigger>

            <!-- 自定义背景色优先级最高 -->
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="CustomBackground" Value="{x:Null}"/>
                </MultiTrigger.Conditions>
            </MultiTrigger>
        </Style.Triggers>
    </Style>

</ResourceDictionary>
