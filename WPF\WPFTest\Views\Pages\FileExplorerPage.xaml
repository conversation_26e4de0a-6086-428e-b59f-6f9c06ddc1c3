<UserControl
    d:DesignHeight="800"
    d:DesignWidth="1200"
    mc:Ignorable="d"
    x:Class="WPFTest.Views.Pages.FileExplorerPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:converters="clr-namespace:Zylo.WPF.Converters;assembly=Zylo.WPF"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:yFile="clr-namespace:Zylo.WPF.Controls.YFile;assembly=Zylo.WPF">

    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
        <converters:BooleanToSelectionModeConverter x:Key="BooleanToSelectionModeConverter" />
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!--  页面标题  -->
        <Border
            Background="{DynamicResource ControlFillColorDefaultBrush}"
            BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
            BorderThickness="0,0,0,1"
            Grid.Row="0"
            Padding="16,12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <!--  标题  -->
                <StackPanel
                    Grid.Column="0"
                    Orientation="Horizontal"
                    VerticalAlignment="Center">
                    <ui:SymbolIcon
                        FontSize="20"
                        Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                        Margin="0,0,8,0"
                        Symbol="Folder24" />
                    <TextBlock
                        FontSize="16"
                        FontWeight="SemiBold"
                        Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                        Text="文件浏览器控件演示" />
                </StackPanel>

                <!--  工具栏按钮  -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <ui:Button Appearance="Secondary" ToolTip="关于文件浏览器">
                        <ui:Button.Icon>
                            <ui:SymbolIcon Foreground="{DynamicResource TextFillColorPrimaryBrush}" Symbol="Info24" />
                        </ui:Button.Icon>
                    </ui:Button>
                </StackPanel>
            </Grid>
        </Border>

        <!--  控制面板  -->
        <Border
            Background="{DynamicResource ControlFillColorSecondaryBrush}"
            BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
            BorderThickness="0,0,0,1"
            Grid.Row="1"
            Padding="16,12">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <!--  路径控制  -->
                <StackPanel
                    Grid.Row="0"
                    Margin="0,0,0,10"
                    Orientation="Vertical">

                    <!--  路径输入行  -->
                    <StackPanel Margin="0,0,0,8" Orientation="Horizontal">
                        <TextBlock
                            Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                            Margin="0,0,10,0"
                            Text="当前路径:"
                            VerticalAlignment="Center" />
                        <ui:TextBox
                            Margin="0,0,10,0"
                            PlaceholderText="输入或选择文件夹路径"
                            Text="{Binding CurrentPath, ElementName=FileExplorer, UpdateSourceTrigger=PropertyChanged}"
                            Width="300" />
                        <ui:Button
                            Appearance="Secondary"
                            Command="{Binding BrowseCommand, ElementName=FileExplorer}"
                            Content="浏览"
                            Margin="0,0,10,0">
                            <ui:Button.Icon>
                                <ui:SymbolIcon Foreground="{DynamicResource TextFillColorPrimaryBrush}" Symbol="FolderOpen24" />
                            </ui:Button.Icon>
                        </ui:Button>
                        <ui:Button
                            Appearance="Secondary"
                            Command="{Binding RefreshCommand, ElementName=FileExplorer}"
                            Content="刷新">
                            <ui:Button.Icon>
                                <ui:SymbolIcon Foreground="{DynamicResource TextFillColorPrimaryBrush}" Symbol="ArrowClockwise24" />
                            </ui:Button.Icon>
                        </ui:Button>
                    </StackPanel>

                    <!--  搜索控制行  -->
                    <StackPanel Orientation="Horizontal">
                        <TextBlock
                            Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                            Margin="0,0,10,0"
                            Text="搜索文本:"
                            VerticalAlignment="Center" />
                        <ui:TextBox
                            Margin="0,0,10,0"
                            PlaceholderText="输入搜索关键词..."
                            Text="{Binding SearchText, ElementName=FileExplorer, UpdateSourceTrigger=PropertyChanged}"
                            Width="200">
                            <ui:TextBox.Icon>
                                <ui:SymbolIcon Symbol="Search24" />
                            </ui:TextBox.Icon>
                        </ui:TextBox>
                        <ui:Button
                            Appearance="Secondary"
                            Click="ClearSearch_Click"
                            Content="清除搜索">
                            <ui:Button.Icon>
                                <ui:SymbolIcon Foreground="{DynamicResource TextFillColorPrimaryBrush}" Symbol="Dismiss24" />
                            </ui:Button.Icon>
                        </ui:Button>
                    </StackPanel>
                </StackPanel>

                <!--  显示控制  -->
                <WrapPanel Grid.Row="1" Orientation="Horizontal">
                    <ui:ToggleSwitch
                        Content="显示工具栏"
                        IsChecked="{Binding ShowToolbar, ElementName=FileExplorer}"
                        Margin="0,0,15,0" />
                    <ui:ToggleSwitch
                        Content="显示地址栏"
                        IsChecked="{Binding ShowAddressBar, ElementName=FileExplorer}"
                        Margin="0,0,15,0" />
                    <ui:ToggleSwitch
                        Content="显示面包屑导航"
                        IsChecked="{Binding ShowBreadcrumb, ElementName=FileExplorer}"
                        Margin="0,0,15,0" />
                    <ui:ToggleSwitch
                        Content="显示搜索框"
                        IsChecked="{Binding ShowSearchBox, ElementName=FileExplorer}"
                        Margin="0,0,15,0" />
                    <ui:ToggleSwitch
                        Content="显示状态栏"
                        IsChecked="{Binding ShowStatusBar, ElementName=FileExplorer}"
                        Margin="0,0,15,0" />
                    <ui:ToggleSwitch
                        Content="显示文件夹树"
                        IsChecked="{Binding ShowFolderTree, ElementName=FileExplorer}"
                        Margin="0,0,15,0" />
                    <ui:ToggleSwitch
                        Content="允许文件选择"
                        IsChecked="{Binding AllowFileSelection, ElementName=FileExplorer}"
                        Margin="0,0,15,0" />
                    <ui:ToggleSwitch
                        Content="允许文件夹选择"
                        IsChecked="{Binding AllowFolderSelection, ElementName=FileExplorer}"
                        Margin="0,0,15,0" />

                    <Border
                        Background="{DynamicResource ControlStrokeColorDefaultBrush}"
                        Height="20"
                        Margin="10,0"
                        Width="1" />

                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock
                            Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                            Margin="0,0,8,0"
                            Text="文件夹树宽度:"
                            VerticalAlignment="Center" />
                        <ui:NumberBox
                            Margin="0,0,8,0"
                            Maximum="500"
                            Minimum="100"
                            Value="{Binding FolderTreeWidth, ElementName=FileExplorer}"
                            Width="80" />
                        <TextBlock
                            Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                            Text="px"
                            VerticalAlignment="Center" />
                    </StackPanel>

                    <Border
                        Background="{DynamicResource ControlStrokeColorDefaultBrush}"
                        Height="20"
                        Margin="10,0"
                        Width="1" />

                    <!--  快速操作按钮  -->
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                        <ui:Button
                            Appearance="Secondary"
                            Click="ResetDefaults_Click"
                            Content="重置默认"
                            Margin="0,0,8,0"
                            ToolTip="重置所有设置到默认值">
                            <ui:Button.Icon>
                                <ui:SymbolIcon Symbol="ArrowReset24" />
                            </ui:Button.Icon>
                        </ui:Button>
                        <ui:Button
                            Appearance="Secondary"
                            Click="MinimizeInterface_Click"
                            Content="最小界面"
                            Margin="0,0,8,0"
                            ToolTip="隐藏所有可选元素">
                            <ui:Button.Icon>
                                <ui:SymbolIcon Symbol="ArrowMinimize20" />
                            </ui:Button.Icon>
                        </ui:Button>
                        <ui:Button
                            Appearance="Secondary"
                            Click="ReadOnlyMode_Click"
                            Content="只读模式"
                            ToolTip="禁用文件选择功能">
                            <ui:Button.Icon>
                                <ui:SymbolIcon Symbol="LockClosed24" />
                            </ui:Button.Icon>
                        </ui:Button>
                    </StackPanel>
                </WrapPanel>
            </Grid>
        </Border>

        <!--  主内容  -->
        <Grid Grid.Row="2" Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <!--  说明文字  -->
            <StackPanel Grid.Row="0" Margin="0,0,0,20">
                <TextBlock
                    FontSize="24"
                    FontWeight="Bold"
                    Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                    Margin="0,0,0,10"
                    Text="Windows风格文件浏览器控件 - 完整功能演示" />
                <TextBlock
                    FontSize="14"
                    Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                    Margin="0,0,0,8"
                    Text="这是一个类似Windows资源管理器的文件浏览器控件，支持浏览文件夹、查看文件、双击打开等功能。通过依赖属性可以灵活控制控件的显示和行为。"
                    TextWrapping="Wrap" />
                <TextBlock
                    FontSize="13"
                    Foreground="{DynamicResource TextFillColorTertiaryBrush}"
                    Text="✨ 新功能：独立的面包屑导航控制、集成搜索框、实时搜索过滤、更细粒度的UI控制"
                    TextWrapping="Wrap" />
            </StackPanel>

            <!--  文件浏览器控件  -->
            <Border
                Background="{DynamicResource ControlFillColorDefaultBrush}"
                BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                BorderThickness="1"
                CornerRadius="8"
                Grid.Row="1">
                <yFile:FileExplorerControl
                    AllowFileSelection="True"
                    AllowFolderSelection="True"
                    CurrentPath="C:\Users\<USER>\Documents"
                    FolderTreeWidth="250"
                    ShowAddressBar="True"
                    ShowBreadcrumb="True"
                    ShowFolderTree="True"
                    ShowSearchBox="True"
                    ShowStatusBar="True"
                    ShowToolbar="True"
                    x:Name="FileExplorer" />
            </Border>
        </Grid>
    </Grid>
</UserControl> 