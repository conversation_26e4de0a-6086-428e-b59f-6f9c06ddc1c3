<UserControl x:Class="WPFTest.Views.LayoutControls.ScrollViewerView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:codeExample="clr-namespace:Zylo.WPF.Controls.CodeExample;assembly=Zylo.WPF"
             xmlns:mvvm="http://prismlibrary.com/"
             xmlns:layoutControls="clr-namespace:WPFTest.ViewModels.LayoutControls"
             mc:Ignorable="d"
             d:DesignHeight="800" d:DesignWidth="1200"
             mvvm:ViewModelLocator.AutoWireViewModel="True"
             d:DataContext="{d:DesignInstance layoutControls:ScrollViewerViewModel}">

    <UserControl.Resources>
        <!-- 🎨 所有样式来自 Zylo.WPF 项目 -->
        <!-- ScrollViewer 样式已在 Zylo.WPF/Resources/ScrollViewer/ 目录下定义 -->
        <!-- 可用样式：ScrollViewerBaseStyle, ModernScrollViewerStyle, CompactScrollViewerStyle -->
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" 
                  HorizontalScrollBarVisibility="Disabled"
                  Padding="24">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>    <!-- 标题区域 -->
                <RowDefinition Height="*"/>       <!-- 主要内容 -->
                <RowDefinition Height="Auto"/>    <!-- 底部操作栏 -->
            </Grid.RowDefinitions>

            <!-- 标题区域 -->
            <StackPanel Grid.Row="0" Margin="0,0,0,20">
                <TextBlock Text="📜 ScrollViewer 控件展示"
                           FontSize="32"
                           FontWeight="Bold"
                           Foreground="{DynamicResource SystemAccentColorPrimaryBrush}"
                           Margin="0,0,0,8"/>
                <TextBlock Text="展示 WPF-UI 增强的 ScrollViewer 控件的各种滚动功能和样式" 
                           FontSize="16" 
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                           TextWrapping="Wrap"/>
                
                <!-- 状态显示 -->
                <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                        BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Padding="16,12"
                        Margin="0,16,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <ui:SymbolIcon Symbol="Info24" 
                                       FontSize="20" 
                                       Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                                       VerticalAlignment="Center"
                                       Margin="0,0,12,0"/>
                        <TextBlock Grid.Column="1" 
                                   Text="{Binding StatusMessage}" 
                                   FontSize="14"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                   VerticalAlignment="Center"
                                   TextWrapping="Wrap"/>
                    </Grid>
                </Border>
            </StackPanel>

            <!-- 主要内容区域 -->
            <Border Grid.Row="1" Margin="0,0,0,20">
                <StackPanel>

                    <!-- 基础功能展示 -->
                    <ui:CardExpander Header="🎯 基础功能" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 ScrollViewer 的基础滚动功能和配置"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>
                            
                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="基础 ScrollViewer 示例：" FontWeight="Medium" Margin="0,0,0,16"/>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <!-- 左侧示例 -->
                                        <StackPanel Grid.Column="0" Margin="0,0,12,0">
                                            <!-- 垂直滚动示例 -->
                                            <TextBlock Text="垂直滚动示例" FontWeight="Bold" Margin="0,0,0,8"/>
                                            <Border BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                                    BorderThickness="1"
                                                    CornerRadius="4"
                                                    Margin="0,0,0,16">
                                                <ScrollViewer Height="200"
                                                              VerticalScrollBarVisibility="Auto"
                                                              HorizontalScrollBarVisibility="Disabled"
                                                              Background="{DynamicResource ControlFillColorDefaultBrush}">
                                                    <StackPanel Margin="16">
                                                        <TextBlock Text="这是一个垂直滚动的示例。" FontWeight="Bold" Margin="0,0,0,12"/>
                                                        <TextBlock Text="当内容超过容器高度时，会自动显示垂直滚动条。" Margin="0,0,0,8"/>
                                                        <TextBlock Text="第 1 行内容" Margin="0,0,0,4"/>
                                                        <TextBlock Text="第 2 行内容" Margin="0,0,0,4"/>
                                                        <TextBlock Text="第 3 行内容" Margin="0,0,0,4"/>
                                                        <TextBlock Text="第 4 行内容" Margin="0,0,0,4"/>
                                                        <TextBlock Text="第 5 行内容" Margin="0,0,0,4"/>
                                                        <TextBlock Text="第 6 行内容" Margin="0,0,0,4"/>
                                                        <TextBlock Text="第 7 行内容" Margin="0,0,0,4"/>
                                                        <TextBlock Text="第 8 行内容" Margin="0,0,0,4"/>
                                                        <TextBlock Text="第 9 行内容" Margin="0,0,0,4"/>
                                                        <TextBlock Text="第 10 行内容" Margin="0,0,0,4"/>
                                                        <TextBlock Text="第 11 行内容" Margin="0,0,0,4"/>
                                                        <TextBlock Text="第 12 行内容" Margin="0,0,0,4"/>
                                                        <TextBlock Text="第 13 行内容" Margin="0,0,0,4"/>
                                                        <TextBlock Text="第 14 行内容" Margin="0,0,0,4"/>
                                                        <TextBlock Text="第 15 行内容" Margin="0,0,0,4"/>
                                                        <Button Content="点击测试" 
                                                                Command="{Binding HandleInteractionCommand}"
                                                                CommandParameter="垂直滚动测试"
                                                                Margin="0,12,0,0"/>
                                                    </StackPanel>
                                                </ScrollViewer>
                                            </Border>

                                            <!-- 滚动条配置 -->
                                            <TextBlock Text="滚动条配置" FontWeight="Bold" Margin="0,0,0,8"/>
                                            <Border Background="{DynamicResource ControlFillColorSecondaryBrush}"
                                                    BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                                    BorderThickness="1"
                                                    CornerRadius="4"
                                                    Padding="12">
                                                <StackPanel>
                                                    <CheckBox Content="显示垂直滚动条" 
                                                              IsChecked="{Binding ShowVerticalScrollBar}"
                                                              Margin="0,0,0,8"/>
                                                    <CheckBox Content="显示水平滚动条" 
                                                              IsChecked="{Binding ShowHorizontalScrollBar}"
                                                              Margin="0,0,0,8"/>
                                                    <CheckBox Content="启用滚动条预览" 
                                                              IsChecked="{Binding EnableScrollBarPreview}"
                                                              Margin="0,0,0,8"/>
                                                    <Button Content="应用设置" 
                                                            Command="{Binding ApplyScrollSettingsCommand}"
                                                            HorizontalAlignment="Left"/>
                                                </StackPanel>
                                            </Border>
                                        </StackPanel>
                                        
                                        <!-- 右侧示例 -->
                                        <StackPanel Grid.Column="1" Margin="12,0,0,0">
                                            <!-- 水平滚动示例 -->
                                            <TextBlock Text="水平滚动示例" FontWeight="Bold" Margin="0,0,0,8"/>
                                            <Border BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                                    BorderThickness="1"
                                                    CornerRadius="4"
                                                    Margin="0,0,0,16">
                                                <ScrollViewer Height="150"
                                                              VerticalScrollBarVisibility="Disabled"
                                                              HorizontalScrollBarVisibility="Auto"
                                                              Background="{DynamicResource ControlFillColorDefaultBrush}">
                                                    <StackPanel Orientation="Horizontal" Margin="16">
                                                        <Border Background="{DynamicResource AccentFillColorDefaultBrush}"
                                                                Width="100" Height="80" Margin="0,0,8,0" CornerRadius="4">
                                                            <TextBlock Text="卡片 1" 
                                                                       Foreground="White" 
                                                                       HorizontalAlignment="Center" 
                                                                       VerticalAlignment="Center"/>
                                                        </Border>
                                                        <Border Background="{DynamicResource AccentFillColorSecondaryBrush}"
                                                                Width="100" Height="80" Margin="0,0,8,0" CornerRadius="4">
                                                            <TextBlock Text="卡片 2" 
                                                                       Foreground="White" 
                                                                       HorizontalAlignment="Center" 
                                                                       VerticalAlignment="Center"/>
                                                        </Border>
                                                        <Border Background="{DynamicResource AccentFillColorTertiaryBrush}"
                                                                Width="100" Height="80" Margin="0,0,8,0" CornerRadius="4">
                                                            <TextBlock Text="卡片 3" 
                                                                       Foreground="White" 
                                                                       HorizontalAlignment="Center" 
                                                                       VerticalAlignment="Center"/>
                                                        </Border>
                                                        <Border Background="{DynamicResource AccentFillColorDefaultBrush}"
                                                                Width="100" Height="80" Margin="0,0,8,0" CornerRadius="4">
                                                            <TextBlock Text="卡片 4" 
                                                                       Foreground="White" 
                                                                       HorizontalAlignment="Center" 
                                                                       VerticalAlignment="Center"/>
                                                        </Border>
                                                        <Border Background="{DynamicResource AccentFillColorSecondaryBrush}"
                                                                Width="100" Height="80" Margin="0,0,8,0" CornerRadius="4">
                                                            <TextBlock Text="卡片 5" 
                                                                       Foreground="White" 
                                                                       HorizontalAlignment="Center" 
                                                                       VerticalAlignment="Center"/>
                                                        </Border>
                                                        <Border Background="{DynamicResource AccentFillColorTertiaryBrush}"
                                                                Width="100" Height="80" CornerRadius="4">
                                                            <TextBlock Text="卡片 6" 
                                                                       Foreground="White" 
                                                                       HorizontalAlignment="Center" 
                                                                       VerticalAlignment="Center"/>
                                                        </Border>
                                                    </StackPanel>
                                                </ScrollViewer>
                                            </Border>

                                            <!-- 滚动信息显示 -->
                                            <TextBlock Text="滚动信息" FontWeight="Bold" Margin="0,0,0,8"/>
                                            <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                                                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                                    BorderThickness="1"
                                                    CornerRadius="4"
                                                    Padding="12">
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                    </Grid.RowDefinitions>
                                                    
                                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="垂直位置:" FontWeight="Bold"/>
                                                    <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding VerticalOffset, StringFormat='{}{0:F1}'}" HorizontalAlignment="Right"/>
                                                    
                                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="水平位置:" FontWeight="Bold"/>
                                                    <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding HorizontalOffset, StringFormat='{}{0:F1}'}" HorizontalAlignment="Right"/>
                                                    
                                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="可视高度:" FontWeight="Bold"/>
                                                    <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding ViewportHeight, StringFormat='{}{0:F1}'}" HorizontalAlignment="Right"/>
                                                    
                                                    <TextBlock Grid.Row="3" Grid.Column="0" Text="内容高度:" FontWeight="Bold"/>
                                                    <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding ExtentHeight, StringFormat='{}{0:F1}'}" HorizontalAlignment="Right"/>
                                                </Grid>
                                            </Border>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="基础功能代码示例"
                                Language="XAML"
                                Description="展示 ScrollViewer 的基础用法"
                                ShowTabs="True"
                                XamlCode="{Binding BasicXamlExample}"
                                CSharpCode="{Binding BasicCSharpExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 高级功能展示 -->
                    <ui:CardExpander Header="⚡ 高级功能" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示 ScrollViewer 的高级功能和自定义滚动行为"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="高级 ScrollViewer 示例：" FontWeight="Medium" Margin="0,0,0,16"/>

                                    <!-- 双向滚动示例 -->
                                    <TextBlock Text="双向滚动示例" FontWeight="Bold" Margin="0,0,0,8"/>
                                    <Border BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                            BorderThickness="1"
                                            CornerRadius="4"
                                            Margin="0,0,0,16">
                                        <ScrollViewer Height="200"
                                                      Width="400"
                                                      VerticalScrollBarVisibility="Auto"
                                                      HorizontalScrollBarVisibility="Auto"
                                                      Background="{DynamicResource ControlFillColorDefaultBrush}"
                                                      HorizontalAlignment="Left">
                                            <Grid Margin="16">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="200"/>
                                                    <ColumnDefinition Width="200"/>
                                                    <ColumnDefinition Width="200"/>
                                                    <ColumnDefinition Width="200"/>
                                                </Grid.ColumnDefinitions>
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="40"/>
                                                    <RowDefinition Height="40"/>
                                                    <RowDefinition Height="40"/>
                                                    <RowDefinition Height="40"/>
                                                    <RowDefinition Height="40"/>
                                                    <RowDefinition Height="40"/>
                                                    <RowDefinition Height="40"/>
                                                    <RowDefinition Height="40"/>
                                                </Grid.RowDefinitions>

                                                <!-- 创建表格数据 -->
                                                <Border Grid.Row="0" Grid.Column="0" Background="{DynamicResource AccentFillColorDefaultBrush}" Margin="2">
                                                    <TextBlock Text="A1" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                                <Border Grid.Row="0" Grid.Column="1" Background="{DynamicResource AccentFillColorSecondaryBrush}" Margin="2">
                                                    <TextBlock Text="B1" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                                <Border Grid.Row="0" Grid.Column="2" Background="{DynamicResource AccentFillColorTertiaryBrush}" Margin="2">
                                                    <TextBlock Text="C1" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                                <Border Grid.Row="0" Grid.Column="3" Background="{DynamicResource AccentFillColorDefaultBrush}" Margin="2">
                                                    <TextBlock Text="D1" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>

                                                <!-- 更多行数据 -->
                                                <Border Grid.Row="1" Grid.Column="0" Background="{DynamicResource ControlFillColorSecondaryBrush}" Margin="2">
                                                    <TextBlock Text="A2" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                                <Border Grid.Row="1" Grid.Column="1" Background="{DynamicResource ControlFillColorSecondaryBrush}" Margin="2">
                                                    <TextBlock Text="B2" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                                <Border Grid.Row="1" Grid.Column="2" Background="{DynamicResource ControlFillColorSecondaryBrush}" Margin="2">
                                                    <TextBlock Text="C2" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                                <Border Grid.Row="1" Grid.Column="3" Background="{DynamicResource ControlFillColorSecondaryBrush}" Margin="2">
                                                    <TextBlock Text="D2" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>

                                                <!-- 继续添加更多行以演示滚动 -->
                                                <Border Grid.Row="2" Grid.Column="0" Background="{DynamicResource ControlFillColorDefaultBrush}" Margin="2">
                                                    <TextBlock Text="A3" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                                <Border Grid.Row="2" Grid.Column="1" Background="{DynamicResource ControlFillColorDefaultBrush}" Margin="2">
                                                    <TextBlock Text="B3" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                                <Border Grid.Row="2" Grid.Column="2" Background="{DynamicResource ControlFillColorDefaultBrush}" Margin="2">
                                                    <TextBlock Text="C3" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                                <Border Grid.Row="2" Grid.Column="3" Background="{DynamicResource ControlFillColorDefaultBrush}" Margin="2">
                                                    <TextBlock Text="D3" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>

                                                <!-- 添加更多行 -->
                                                <Border Grid.Row="3" Grid.Column="0" Background="{DynamicResource AccentFillColorSecondaryBrush}" Margin="2">
                                                    <TextBlock Text="A4" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                                <Border Grid.Row="3" Grid.Column="1" Background="{DynamicResource AccentFillColorSecondaryBrush}" Margin="2">
                                                    <TextBlock Text="B4" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                                <Border Grid.Row="3" Grid.Column="2" Background="{DynamicResource AccentFillColorSecondaryBrush}" Margin="2">
                                                    <TextBlock Text="C4" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                                <Border Grid.Row="3" Grid.Column="3" Background="{DynamicResource AccentFillColorSecondaryBrush}" Margin="2">
                                                    <TextBlock Text="D4" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>

                                                <!-- 继续添加行以确保有足够内容滚动 -->
                                                <Border Grid.Row="4" Grid.Column="0" Background="{DynamicResource ControlFillColorTertiaryBrush}" Margin="2">
                                                    <TextBlock Text="A5" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                                <Border Grid.Row="4" Grid.Column="1" Background="{DynamicResource ControlFillColorTertiaryBrush}" Margin="2">
                                                    <TextBlock Text="B5" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                                <Border Grid.Row="4" Grid.Column="2" Background="{DynamicResource ControlFillColorTertiaryBrush}" Margin="2">
                                                    <TextBlock Text="C5" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                                <Border Grid.Row="4" Grid.Column="3" Background="{DynamicResource ControlFillColorTertiaryBrush}" Margin="2">
                                                    <TextBlock Text="D5" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>

                                                <Border Grid.Row="5" Grid.Column="0" Background="{DynamicResource AccentFillColorTertiaryBrush}" Margin="2">
                                                    <TextBlock Text="A6" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                                <Border Grid.Row="5" Grid.Column="1" Background="{DynamicResource AccentFillColorTertiaryBrush}" Margin="2">
                                                    <TextBlock Text="B6" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                                <Border Grid.Row="5" Grid.Column="2" Background="{DynamicResource AccentFillColorTertiaryBrush}" Margin="2">
                                                    <TextBlock Text="C6" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                                <Border Grid.Row="5" Grid.Column="3" Background="{DynamicResource AccentFillColorTertiaryBrush}" Margin="2">
                                                    <TextBlock Text="D6" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>

                                                <Border Grid.Row="6" Grid.Column="0" Background="{DynamicResource ControlFillColorDefaultBrush}" Margin="2">
                                                    <TextBlock Text="A7" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                                <Border Grid.Row="6" Grid.Column="1" Background="{DynamicResource ControlFillColorDefaultBrush}" Margin="2">
                                                    <TextBlock Text="B7" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                                <Border Grid.Row="6" Grid.Column="2" Background="{DynamicResource ControlFillColorDefaultBrush}" Margin="2">
                                                    <TextBlock Text="C7" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                                <Border Grid.Row="6" Grid.Column="3" Background="{DynamicResource ControlFillColorDefaultBrush}" Margin="2">
                                                    <TextBlock Text="D7" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>

                                                <Border Grid.Row="7" Grid.Column="0" Background="{DynamicResource AccentFillColorDefaultBrush}" Margin="2">
                                                    <TextBlock Text="A8" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                                <Border Grid.Row="7" Grid.Column="1" Background="{DynamicResource AccentFillColorDefaultBrush}" Margin="2">
                                                    <TextBlock Text="B8" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                                <Border Grid.Row="7" Grid.Column="2" Background="{DynamicResource AccentFillColorDefaultBrush}" Margin="2">
                                                    <TextBlock Text="C8" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                                <Border Grid.Row="7" Grid.Column="3" Background="{DynamicResource AccentFillColorDefaultBrush}" Margin="2">
                                                    <TextBlock Text="D8" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                            </Grid>
                                        </ScrollViewer>
                                    </Border>

                                    <!-- 滚动控制按钮 -->
                                    <TextBlock Text="滚动控制" FontWeight="Bold" Margin="0,0,0,8"/>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                                        <Button Content="滚动到顶部"
                                                Command="{Binding ScrollToTopCommand}"
                                                Margin="0,0,8,0"/>
                                        <Button Content="滚动到底部"
                                                Command="{Binding ScrollToBottomCommand}"
                                                Margin="0,0,8,0"/>
                                        <Button Content="滚动到左侧"
                                                Command="{Binding ScrollToLeftCommand}"
                                                Margin="0,0,8,0"/>
                                        <Button Content="滚动到右侧"
                                                Command="{Binding ScrollToRightCommand}"/>
                                    </StackPanel>
                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="高级功能代码示例"
                                Language="XAML"
                                Description="展示 ScrollViewer 的高级用法和滚动控制"
                                ShowTabs="True"
                                XamlCode="{Binding AdvancedXamlExample}"
                                CSharpCode="{Binding AdvancedCSharpExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                    <!-- 样式展示 -->
                    <ui:CardExpander Header="🎨 样式展示" IsExpanded="True" Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock Text="展示来自 Zylo.WPF 的各种 ScrollViewer 样式"
                                       FontSize="14"
                                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                       Margin="0,0,0,16"/>

                            <!-- 示例展示区域 -->
                            <ui:Card Padding="20" Margin="0,0,0,16">
                                <StackPanel>
                                    <TextBlock Text="样式展示示例：" FontWeight="Medium" Margin="0,0,0,16"/>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- 默认样式 -->
                                        <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                            <TextBlock Text="默认样式" FontWeight="Bold" Margin="0,0,0,8"/>
                                            <Border BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                                    BorderThickness="1"
                                                    CornerRadius="4"
                                                    MinHeight="120">
                                                <ScrollViewer Height="120"
                                                              VerticalScrollBarVisibility="Auto"
                                                              HorizontalScrollBarVisibility="Disabled"
                                                              Background="{DynamicResource ControlFillColorDefaultBrush}">
                                                    <StackPanel Margin="12">
                                                        <TextBlock Text="这是默认样式的 ScrollViewer。" FontWeight="Bold" Margin="0,0,0,8"/>
                                                        <TextBlock Text="内容 1" Margin="0,0,0,4"/>
                                                        <TextBlock Text="内容 2" Margin="0,0,0,4"/>
                                                        <TextBlock Text="内容 3" Margin="0,0,0,4"/>
                                                        <TextBlock Text="内容 4" Margin="0,0,0,4"/>
                                                        <TextBlock Text="内容 5" Margin="0,0,0,4"/>
                                                        <TextBlock Text="内容 6" Margin="0,0,0,4"/>
                                                        <TextBlock Text="内容 7"/>
                                                    </StackPanel>
                                                </ScrollViewer>
                                            </Border>
                                        </StackPanel>

                                        <!-- 现代化样式 -->
                                        <StackPanel Grid.Column="1" Margin="4,0,4,0">
                                            <TextBlock Text="现代化样式" FontWeight="Bold" Margin="0,0,0,8"/>
                                            <Border BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                                                    BorderThickness="0"
                                                    CornerRadius="8"
                                                    MinHeight="120">
                                                <Border.Effect>
                                                    <DropShadowEffect Color="Black"
                                                                      Opacity="0.1"
                                                                      ShadowDepth="4"
                                                                      BlurRadius="8"/>
                                                </Border.Effect>
                                                <ScrollViewer Height="120"
                                                              VerticalScrollBarVisibility="Auto"
                                                              HorizontalScrollBarVisibility="Disabled"
                                                              Background="{DynamicResource CardBackgroundFillColorDefaultBrush}">
                                                    <StackPanel Margin="12">
                                                        <TextBlock Text="这是现代化样式的 ScrollViewer。" FontWeight="Bold" Margin="0,0,0,8"/>
                                                        <TextBlock Text="内容 1" Margin="0,0,0,4"/>
                                                        <TextBlock Text="内容 2" Margin="0,0,0,4"/>
                                                        <TextBlock Text="内容 3" Margin="0,0,0,4"/>
                                                        <TextBlock Text="内容 4" Margin="0,0,0,4"/>
                                                        <TextBlock Text="内容 5" Margin="0,0,0,4"/>
                                                        <TextBlock Text="内容 6" Margin="0,0,0,4"/>
                                                        <TextBlock Text="内容 7"/>
                                                    </StackPanel>
                                                </ScrollViewer>
                                            </Border>
                                        </StackPanel>

                                        <!-- 紧凑样式 -->
                                        <StackPanel Grid.Column="2" Margin="8,0,0,0">
                                            <TextBlock Text="紧凑样式" FontWeight="Bold" Margin="0,0,0,8"/>
                                            <Border BorderBrush="{DynamicResource AccentStrokeColorDefaultBrush}"
                                                    BorderThickness="2"
                                                    CornerRadius="4"
                                                    MinHeight="120">
                                                <ScrollViewer Height="120"
                                                              VerticalScrollBarVisibility="Auto"
                                                              HorizontalScrollBarVisibility="Disabled"
                                                              Background="{DynamicResource AccentFillColorDefaultBrush}">
                                                    <StackPanel Margin="8">
                                                        <TextBlock Text="这是紧凑样式的 ScrollViewer。"
                                                                   FontWeight="Bold"
                                                                   Foreground="White"
                                                                   Margin="0,0,0,6"/>
                                                        <TextBlock Text="内容 1" Foreground="White" Margin="0,0,0,2"/>
                                                        <TextBlock Text="内容 2" Foreground="White" Margin="0,0,0,2"/>
                                                        <TextBlock Text="内容 3" Foreground="White" Margin="0,0,0,2"/>
                                                        <TextBlock Text="内容 4" Foreground="White" Margin="0,0,0,2"/>
                                                        <TextBlock Text="内容 5" Foreground="White" Margin="0,0,0,2"/>
                                                        <TextBlock Text="内容 6" Foreground="White" Margin="0,0,0,2"/>
                                                        <TextBlock Text="内容 7" Foreground="White"/>
                                                    </StackPanel>
                                                </ScrollViewer>
                                            </Border>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </ui:Card>

                            <!-- 代码示例 -->
                            <codeExample:CodeExampleControl
                                Title="样式代码示例"
                                Language="XAML"
                                Description="展示如何使用 Zylo.WPF 中的 ScrollViewer 样式"
                                ShowTabs="True"
                                XamlCode="{Binding StylesXamlExample}"/>
                        </StackPanel>
                    </ui:CardExpander>

                </StackPanel>
            </Border>

            <!-- 底部操作栏 -->
            <Border Grid.Row="2" 
                    Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Padding="16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 统计信息 -->
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock Text="{Binding InteractionCount, StringFormat='交互次数: {0}'}" 
                                   FontSize="14" 
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                   Margin="0,0,20,0"/>
                        <TextBlock Text="{Binding LastAction, StringFormat='最后操作: {0}'}" 
                                   FontSize="14" 
                                   Foreground="{DynamicResource TextFillColorSecondaryBrush}"/>
                    </StackPanel>

                    <!-- 操作按钮 -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <ui:Button Content="重置计数" 
                                   Command="{Binding ResetCountCommand}"
                                   Appearance="Secondary"
                                   Margin="0,0,8,0"/>
                        <ui:Button Content="清除状态" 
                                   Command="{Binding ClearStatusCommand}"
                                   Appearance="Secondary"/>
                    </StackPanel>
                </Grid>
            </Border>

        </Grid>
    </ScrollViewer>
</UserControl>
