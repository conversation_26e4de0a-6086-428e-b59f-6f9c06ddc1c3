// AutoSuggestBox 高级功能 C# 实现
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows.Threading;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace WPFTest.ViewModels.Controls;

/// <summary>
/// AutoSuggestBox 高级功能示例 ViewModel
/// 使用 CommunityToolkit.MVVM 8.4.0 的 Partial Properties 语法
/// </summary>
public partial class AdvancedAutoSuggestBoxViewModel : ObservableObject
{
    private readonly DispatcherTimer _searchTimer;
    private readonly Random _random = new();

    #region 搜索文本属性

    [ObservableProperty]
    public partial string SmartSearchText { get; set; } = string.Empty;

    [ObservableProperty]
    public partial string MultiTypeSearchText { get; set; } = string.Empty;

    [ObservableProperty]
    public partial string AsyncSearchText { get; set; } = string.Empty;

    [ObservableProperty]
    public partial string CustomSearchText { get; set; } = string.Empty;

    #endregion

    #region 建议列表属性

    [ObservableProperty]
    public partial ObservableCollection<string> SmartSuggestions { get; set; } = new();

    [ObservableProperty]
    public partial ObservableCollection<string> MultiTypeSuggestions { get; set; } = new();

    [ObservableProperty]
    public partial ObservableCollection<string> AsyncSuggestions { get; set; } = new();

    [ObservableProperty]
    public partial ObservableCollection<string> CustomSuggestions { get; set; } = new();

    #endregion

    #region 状态属性

    [ObservableProperty]
    public partial string SearchHistory { get; set; } = "搜索历史：无";

    [ObservableProperty]
    public partial string SearchTypeInfo { get; set; } = "输入内容以识别类型";

    [ObservableProperty]
    public partial string AsyncSearchStatus { get; set; } = "准备就绪";

    [ObservableProperty]
    public partial bool IsAsyncSearching { get; set; } = false;

    [ObservableProperty]
    public partial bool IsAsyncSearchEnabled { get; set; } = true;

    [ObservableProperty]
    public partial bool HasNetworkDelay { get; set; } = false;

    #endregion

    #region 数据源

    private readonly List<string> _searchHistoryList = new();

    private readonly Dictionary<string, List<string>> _categorizedData = new()
    {
        ["城市"] = new() { "北京", "上海", "广州", "深圳", "杭州", "南京", "苏州", "成都", "武汉", "西安" },
        ["语言"] = new() { "C#", "Java", "Python", "JavaScript", "TypeScript", "C++", "Go", "Rust", "Swift", "Kotlin" },
        ["国家"] = new() { "中国", "美国", "日本", "德国", "英国", "法国", "意大利", "加拿大", "澳大利亚", "韩国" },
        ["产品"] = new() { "笔记本电脑", "台式电脑", "平板电脑", "智能手机", "智能手表", "耳机", "音响", "键盘", "鼠标", "显示器" }
    };

    private readonly List<string> _customData = new()
    {
        "自定义搜索项 1", "自定义搜索项 2", "自定义搜索项 3", "自定义搜索项 4", "自定义搜索项 5",
        "特殊功能 A", "特殊功能 B", "特殊功能 C", "高级选项 X", "高级选项 Y", "高级选项 Z"
    };

    #endregion

    #region 构造函数

    public AdvancedAutoSuggestBoxViewModel()
    {
        // 初始化搜索计时器
        _searchTimer = new DispatcherTimer
        {
            Interval = TimeSpan.FromMilliseconds(300) // 300ms 延迟
        };
        _searchTimer.Tick += OnSearchTimerTick;

        // 监听属性变化
        PropertyChanged += OnPropertyChanged;
    }

    #endregion

    #region 搜索命令

    [RelayCommand]
    private void SubmitQuery(string? query)
    {
        if (string.IsNullOrWhiteSpace(query)) return;

        // 添加到搜索历史
        if (!_searchHistoryList.Contains(query))
        {
            _searchHistoryList.Insert(0, query);
            if (_searchHistoryList.Count > 5)
                _searchHistoryList.RemoveAt(5);
        }

        UpdateSearchHistory();
        AsyncSearchStatus = $"已提交查询：{query}";
    }

    [RelayCommand]
    private void ChooseSuggestion()
    {
        AsyncSearchStatus = "已选择建议项";
    }

    [RelayCommand]
    private void ClearAllSearches()
    {
        SmartSearchText = string.Empty;
        MultiTypeSearchText = string.Empty;
        AsyncSearchText = string.Empty;
        CustomSearchText = string.Empty;

        SmartSuggestions.Clear();
        MultiTypeSuggestions.Clear();
        AsyncSuggestions.Clear();
        CustomSuggestions.Clear();

        SearchTypeInfo = "输入内容以识别类型";
        AsyncSearchStatus = "所有搜索已清除";
    }

    [RelayCommand]
    private void ResetHistory()
    {
        _searchHistoryList.Clear();
        UpdateSearchHistory();
        AsyncSearchStatus = "搜索历史已重置";
    }

    [RelayCommand]
    private void ToggleNetworkDelay()
    {
        HasNetworkDelay = !HasNetworkDelay;
        AsyncSearchStatus = HasNetworkDelay ? "已启用网络延迟模拟" : "已禁用网络延迟模拟";
    }

    #endregion

    #region 私有方法

    private void OnPropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        switch (e.PropertyName)
        {
            case nameof(SmartSearchText):
                UpdateSmartSuggestions();
                break;
            case nameof(MultiTypeSearchText):
                UpdateMultiTypeSuggestions();
                break;
            case nameof(AsyncSearchText):
                StartAsyncSearch();
                break;
            case nameof(CustomSearchText):
                UpdateCustomSuggestions();
                break;
        }
    }

    private void UpdateSmartSuggestions()
    {
        SmartSuggestions.Clear();

        if (string.IsNullOrWhiteSpace(SmartSearchText))
            return;

        // 合并历史记录和数据源
        var allSuggestions = new List<string>();
        
        // 添加历史记录匹配项
        allSuggestions.AddRange(_searchHistoryList
            .Where(h => h.Contains(SmartSearchText, StringComparison.OrdinalIgnoreCase)));

        // 添加数据源匹配项
        foreach (var category in _categorizedData.Values)
        {
            allSuggestions.AddRange(category
                .Where(item => item.Contains(SmartSearchText, StringComparison.OrdinalIgnoreCase)));
        }

        // 去重并限制数量
        var uniqueSuggestions = allSuggestions.Distinct().Take(10);
        foreach (var suggestion in uniqueSuggestions)
        {
            SmartSuggestions.Add(suggestion);
        }
    }

    private void UpdateMultiTypeSuggestions()
    {
        MultiTypeSuggestions.Clear();

        if (string.IsNullOrWhiteSpace(MultiTypeSearchText))
        {
            SearchTypeInfo = "输入内容以识别类型";
            return;
        }

        var matchedCategories = new List<string>();
        var allMatches = new List<string>();

        foreach (var category in _categorizedData)
        {
            var matches = category.Value
                .Where(item => item.Contains(MultiTypeSearchText, StringComparison.OrdinalIgnoreCase))
                .ToList();

            if (matches.Any())
            {
                matchedCategories.Add(category.Key);
                allMatches.AddRange(matches.Take(3)); // 每个类别最多3个
            }
        }

        foreach (var match in allMatches.Take(10))
        {
            MultiTypeSuggestions.Add(match);
        }

        SearchTypeInfo = matchedCategories.Any() 
            ? $"识别类型：{string.Join(", ", matchedCategories)}" 
            : "未识别到匹配类型";
    }

    private void StartAsyncSearch()
    {
        _searchTimer.Stop();

        if (string.IsNullOrWhiteSpace(AsyncSearchText))
        {
            AsyncSuggestions.Clear();
            AsyncSearchStatus = "准备就绪";
            return;
        }

        IsAsyncSearching = true;
        IsAsyncSearchEnabled = false;
        AsyncSearchStatus = "正在搜索...";

        _searchTimer.Start();
    }

    private async void OnSearchTimerTick(object? sender, EventArgs e)
    {
        _searchTimer.Stop();

        try
        {
            // 模拟网络延迟
            if (HasNetworkDelay)
            {
                await Task.Delay(_random.Next(500, 2000));
            }

            // 模拟搜索结果
            var results = _categorizedData.Values
                .SelectMany(list => list)
                .Where(item => item.Contains(AsyncSearchText, StringComparison.OrdinalIgnoreCase))
                .Take(8)
                .ToList();

            AsyncSuggestions.Clear();
            foreach (var result in results)
            {
                AsyncSuggestions.Add(result);
            }

            AsyncSearchStatus = $"找到 {results.Count} 个结果";
        }
        catch (Exception ex)
        {
            AsyncSearchStatus = $"搜索失败：{ex.Message}";
        }
        finally
        {
            IsAsyncSearching = false;
            IsAsyncSearchEnabled = true;
        }
    }

    private void UpdateCustomSuggestions()
    {
        CustomSuggestions.Clear();

        if (string.IsNullOrWhiteSpace(CustomSearchText))
            return;

        var suggestions = _customData
            .Where(item => item.Contains(CustomSearchText, StringComparison.OrdinalIgnoreCase))
            .Take(6)
            .ToList();

        foreach (var suggestion in suggestions)
        {
            CustomSuggestions.Add(suggestion);
        }
    }

    private void UpdateSearchHistory()
    {
        SearchHistory = _searchHistoryList.Any() 
            ? $"搜索历史：{string.Join(", ", _searchHistoryList.Take(3))}" 
            : "搜索历史：无";
    }

    #endregion
}

/*
AutoSuggestBox 高级功能实现要点：

1. 智能搜索：
   - 历史记录管理
   - 多数据源合并
   - 去重和排序

2. 多类型搜索：
   - 分类数据管理
   - 类型自动识别
   - 结果分类显示

3. 异步搜索：
   - DispatcherTimer 延迟搜索
   - 异步操作状态管理
   - 网络延迟模拟

4. 自定义功能：
   - 专用数据源
   - 样式定制支持
   - 用户体验优化

5. 性能优化：
   - 搜索防抖动
   - 结果数量限制
   - 内存管理

6. 用户交互：
   - 实时状态反馈
   - 历史记录管理
   - 多种操作模式
*/
