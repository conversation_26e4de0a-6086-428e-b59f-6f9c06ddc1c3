using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using Zylo.YLog.Runtime;

namespace WPFTest.ViewModels.ButtonControls
{
    /// <summary>
    /// HyperlinkButton 页面的 ViewModel，演示各种超链接按钮功能
    /// </summary>
    public partial class HyperlinkButtonPageViewModel : ObservableObject
    {
        private readonly YLoggerInstance _logger = YLogger.For<HyperlinkButtonPageViewModel>();

        #region 属性

        /// <summary>
        /// 交互次数
        /// </summary>
        [ObservableProperty]
        private int interactionCount = 0;

        /// <summary>
        /// 最后执行的操作
        /// </summary>
        [ObservableProperty]
        private string lastAction = "无操作";

        /// <summary>
        /// 状态消息
        /// </summary>
        [ObservableProperty]
        private string statusMessage = "欢迎使用 HyperlinkButton 超链接按钮示例！点击任意链接查看效果。";

        #region 代码示例属性

        /// <summary>
        /// 基础 HyperlinkButton XAML 示例
        /// </summary>
        [ObservableProperty]
        private string basicHyperlinkButtonXamlExample = string.Empty;

        /// <summary>
        /// 基础 HyperlinkButton C# 示例
        /// </summary>
        [ObservableProperty]
        private string basicHyperlinkButtonCSharpExample = string.Empty;

        /// <summary>
        /// 带图标 HyperlinkButton XAML 示例
        /// </summary>
        [ObservableProperty]
        private string iconHyperlinkButtonXamlExample = string.Empty;

        /// <summary>
        /// 带图标 HyperlinkButton C# 示例
        /// </summary>
        [ObservableProperty]
        private string iconHyperlinkButtonCSharpExample = string.Empty;

        /// <summary>
        /// 高级功能 HyperlinkButton XAML 示例
        /// </summary>
        [ObservableProperty]
        private string advancedHyperlinkButtonXamlExample = string.Empty;

        /// <summary>
        /// 高级功能 HyperlinkButton C# 示例
        /// </summary>
        [ObservableProperty]
        private string advancedHyperlinkButtonCSharpExample = string.Empty;

        /// <summary>
        /// 应用场景 XAML 示例
        /// </summary>
        [ObservableProperty]
        private string applicationScenarioXamlExample = string.Empty;

        /// <summary>
        /// 应用场景 C# 示例
        /// </summary>
        [ObservableProperty]
        private string applicationScenarioCSharpExample = string.Empty;

        #endregion

        #endregion

        #region 构造函数

        public HyperlinkButtonPageViewModel()
        {
            StatusMessage = "HyperlinkButton 示例库已加载，开始体验各种超链接功能！";
            LoadCodeExamples();
            _logger.Info("HyperlinkButtonPageViewModel 初始化完成");
        }

        #endregion

        #region 命令

        /// <summary>
        /// 通用交互命令
        /// </summary>
        /// <param name="parameter">操作参数</param>
        [RelayCommand]
        private void HandleInteraction(string? parameter)
        {
            InteractionCount++;
            LastAction = parameter ?? "未知操作";
            
            // 根据不同的操作显示不同的状态消息和执行不同的逻辑
            StatusMessage = parameter switch
            {
                "基础超链接" => "🔗 点击了基础超链接",
                "访问官网" => "🌐 正在访问官网...",
                "查看文档" => "📚 正在查看文档...",
                "外部链接" => "🔗 点击了外部链接",
                "下载文件" => "📥 开始下载文件...",
                "邮件联系" => "📧 正在打开邮件客户端...",
                "设置选项" => "⚙️ 正在打开设置选项...",
                _ => $"🎯 执行了操作: {parameter}"
            };

            // 执行具体的业务逻辑
            ExecuteAction(parameter);
            
            _logger.Info($"HyperlinkButton 交互: {parameter}, 总计数: {InteractionCount}");
        }

        /// <summary>
        /// 重置计数命令
        /// </summary>
        [RelayCommand]
        private void ResetCount()
        {
            InteractionCount = 0;
            StatusMessage = "计数已重置";
            _logger.Info("重置了交互计数");
        }

        #endregion

        #region 方法

        /// <summary>
        /// 执行具体的操作
        /// </summary>
        /// <param name="action">操作类型</param>
        private void ExecuteAction(string? action)
        {
            try
            {
                switch (action)
                {
                    case "访问官网":
                        OpenUrl("https://github.com/lepoco/wpfui");
                        break;
                    case "查看文档":
                        OpenUrl("https://wpfui.lepo.co/documentation/");
                        break;
                    case "外部链接":
                        OpenUrl("https://www.microsoft.com/");
                        break;
                    case "下载文件":
                        // 模拟下载操作
                        StatusMessage = "📥 文件下载已开始（模拟操作）";
                        break;
                    case "邮件联系":
                        OpenUrl("mailto:<EMAIL>");
                        break;
                    case "设置选项":
                        StatusMessage = "⚙️ 设置选项已打开（模拟操作）";
                        break;
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"❌ 操作失败: {ex.Message}";
                _logger.Error($"执行操作失败: {action}, 错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 打开 URL
        /// </summary>
        /// <param name="url">要打开的 URL</param>
        private void OpenUrl(string url)
        {
            try
            {
                Process.Start(new ProcessStartInfo
                {
                    FileName = url,
                    UseShellExecute = true
                });
                StatusMessage = $"🌐 已在默认浏览器中打开: {url}";
            }
            catch (Exception ex)
            {
                StatusMessage = $"❌ 无法打开链接: {ex.Message}";
                _logger.Error($"打开 URL 失败: {url}, 错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载代码示例
        /// </summary>
        private void LoadCodeExamples()
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                var basePath = Path.GetDirectoryName(assembly.Location) ?? "";
                var examplesPath = Path.Combine(basePath, "CodeExamples", "HyperlinkButton");

                // 基础 HyperlinkButton 示例
                BasicHyperlinkButtonXamlExample = LoadCodeExample(examplesPath, "BasicHyperlinkButton.xaml") ??
                    GetDefaultBasicHyperlinkButtonXamlExample();
                BasicHyperlinkButtonCSharpExample = LoadCodeExample(examplesPath, "BasicHyperlinkButton.cs") ??
                    GetDefaultBasicHyperlinkButtonCSharpExample();

                // 带图标 HyperlinkButton 示例
                IconHyperlinkButtonXamlExample = LoadCodeExample(examplesPath, "IconHyperlinkButton.xaml") ??
                    GetDefaultIconHyperlinkButtonXamlExample();
                IconHyperlinkButtonCSharpExample = LoadCodeExample(examplesPath, "IconHyperlinkButton.cs") ??
                    GetDefaultIconHyperlinkButtonCSharpExample();

                // 高级功能示例
                AdvancedHyperlinkButtonXamlExample = LoadCodeExample(examplesPath, "AdvancedHyperlinkButton.xaml") ??
                    GetDefaultAdvancedHyperlinkButtonXamlExample();
                AdvancedHyperlinkButtonCSharpExample = LoadCodeExample(examplesPath, "AdvancedHyperlinkButton.cs") ??
                    GetDefaultAdvancedHyperlinkButtonCSharpExample();

                // 应用场景示例
                ApplicationScenarioXamlExample = LoadCodeExample(examplesPath, "ApplicationScenario.xaml") ??
                    GetDefaultApplicationScenarioXamlExample();
                ApplicationScenarioCSharpExample = LoadCodeExample(examplesPath, "ApplicationScenario.cs") ??
                    GetDefaultApplicationScenarioCSharpExample();

                _logger.Info("代码示例加载完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"加载代码示例失败: {ex.Message}");
                LoadDefaultExamples();
            }
        }

        /// <summary>
        /// 从文件加载代码示例
        /// </summary>
        private string? LoadCodeExample(string basePath, string fileName)
        {
            try
            {
                var filePath = Path.Combine(basePath, fileName);
                return File.Exists(filePath) ? File.ReadAllText(filePath) : null;
            }
            catch (Exception ex)
            {
                _logger.Warning($"无法加载代码示例文件 {fileName}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 加载默认示例
        /// </summary>
        private void LoadDefaultExamples()
        {
            BasicHyperlinkButtonXamlExample = GetDefaultBasicHyperlinkButtonXamlExample();
            BasicHyperlinkButtonCSharpExample = GetDefaultBasicHyperlinkButtonCSharpExample();
            IconHyperlinkButtonXamlExample = GetDefaultIconHyperlinkButtonXamlExample();
            IconHyperlinkButtonCSharpExample = GetDefaultIconHyperlinkButtonCSharpExample();
            AdvancedHyperlinkButtonXamlExample = GetDefaultAdvancedHyperlinkButtonXamlExample();
            AdvancedHyperlinkButtonCSharpExample = GetDefaultAdvancedHyperlinkButtonCSharpExample();
            ApplicationScenarioXamlExample = GetDefaultApplicationScenarioXamlExample();
            ApplicationScenarioCSharpExample = GetDefaultApplicationScenarioCSharpExample();
        }

        #endregion

        #region 默认代码示例

        private string GetDefaultBasicHyperlinkButtonXamlExample()
        {
            return @"<!-- 基础 HyperlinkButton 示例 - MVVM 模式 -->
<ui:HyperlinkButton Content=""基础超链接""
                    Command=""{Binding HandleInteractionCommand}""
                    CommandParameter=""基础超链接""/>

<ui:HyperlinkButton Content=""访问官网""
                    Command=""{Binding HandleInteractionCommand}""
                    CommandParameter=""访问官网""/>

<ui:HyperlinkButton Content=""查看文档""
                    Command=""{Binding HandleInteractionCommand}""
                    CommandParameter=""查看文档""/>

<ui:HyperlinkButton Content=""禁用状态""
                    IsEnabled=""False""/>";
        }

        private string GetDefaultBasicHyperlinkButtonCSharpExample()
        {
            return @"// 基础 HyperlinkButton MVVM 命令处理
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Diagnostics;

public partial class HyperlinkButtonPageViewModel : ObservableObject
{
    /// <summary>
    /// 交互次数统计
    /// </summary>
    [ObservableProperty]
    private int interactionCount = 0;

    /// <summary>
    /// 通用交互命令
    /// </summary>
    [RelayCommand]
    private void HandleInteraction(string? parameter)
    {
        InteractionCount++;
        
        StatusMessage = parameter switch
        {
            ""基础超链接"" => ""🔗 点击了基础超链接"",
            ""访问官网"" => ""🌐 正在访问官网..."",
            ""查看文档"" => ""📚 正在查看文档..."",
            _ => $""🎯 执行了操作: {parameter}""
        };

        // 执行具体的业务逻辑
        ExecuteAction(parameter);
    }

    private void ExecuteAction(string? action)
    {
        switch (action)
        {
            case ""访问官网"":
                OpenUrl(""https://github.com/lepoco/wpfui"");
                break;
            case ""查看文档"":
                OpenUrl(""https://wpfui.lepo.co/documentation/"");
                break;
        }
    }

    private void OpenUrl(string url)
    {
        try
        {
            Process.Start(new ProcessStartInfo
            {
                FileName = url,
                UseShellExecute = true
            });
        }
        catch (Exception ex)
        {
            StatusMessage = $""❌ 无法打开链接: {ex.Message}"";
        }
    }
}";
        }

        private string GetDefaultIconHyperlinkButtonXamlExample()
        {
            return @"<!-- 带图标的 HyperlinkButton 示例 - MVVM 模式 -->
<ui:HyperlinkButton Command=""{Binding HandleInteractionCommand}""
                    CommandParameter=""外部链接"">
    <StackPanel Orientation=""Horizontal"">
        <ui:SymbolIcon Symbol=""Link24"" FontSize=""16"" Margin=""0,0,4,0""/>
        <TextBlock Text=""外部链接""/>
    </StackPanel>
</ui:HyperlinkButton>

<ui:HyperlinkButton Command=""{Binding HandleInteractionCommand}""
                    CommandParameter=""下载文件"">
    <StackPanel Orientation=""Horizontal"">
        <ui:SymbolIcon Symbol=""ArrowDownload24"" FontSize=""16"" Margin=""0,0,4,0""/>
        <TextBlock Text=""下载文件""/>
    </StackPanel>
</ui:HyperlinkButton>

<ui:HyperlinkButton Command=""{Binding HandleInteractionCommand}""
                    CommandParameter=""邮件联系"">
    <StackPanel Orientation=""Horizontal"">
        <ui:SymbolIcon Symbol=""Mail24"" FontSize=""16"" Margin=""0,0,4,0""/>
        <TextBlock Text=""邮件联系""/>
    </StackPanel>
</ui:HyperlinkButton>";
        }

        private string GetDefaultIconHyperlinkButtonCSharpExample()
        {
            return @"// 带图标 HyperlinkButton MVVM 命令处理示例
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Diagnostics;

public partial class HyperlinkButtonPageViewModel : ObservableObject
{
    /// <summary>
    /// 通用交互命令 - 支持不同类型的链接操作
    /// </summary>
    [RelayCommand]
    private void HandleInteraction(string? parameter)
    {
        InteractionCount++;
        
        // 根据不同的操作显示不同的状态消息
        StatusMessage = parameter switch
        {
            ""外部链接"" => ""🔗 点击了外部链接"",
            ""下载文件"" => ""📥 开始下载文件..."",
            ""邮件联系"" => ""📧 正在打开邮件客户端..."",
            ""设置选项"" => ""⚙️ 正在打开设置选项..."",
            _ => $""🎯 执行了操作: {parameter}""
        };

        // 执行具体的业务逻辑
        ExecuteAction(parameter);
    }

    private void ExecuteAction(string? action)
    {
        try
        {
            switch (action)
            {
                case ""外部链接"":
                    OpenUrl(""https://www.microsoft.com/"");
                    break;
                case ""下载文件"":
                    // 模拟下载操作
                    StatusMessage = ""📥 文件下载已开始（模拟操作）"";
                    break;
                case ""邮件联系"":
                    OpenUrl(""mailto:<EMAIL>"");
                    break;
                case ""设置选项"":
                    StatusMessage = ""⚙️ 设置选项已打开（模拟操作）"";
                    break;
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $""❌ 操作失败: {ex.Message}"";
        }
    }

    private void OpenUrl(string url)
    {
        Process.Start(new ProcessStartInfo
        {
            FileName = url,
            UseShellExecute = true
        });
        StatusMessage = $""🌐 已在默认浏览器中打开: {url}"";
    }
}";
        }

        private string GetDefaultAdvancedHyperlinkButtonXamlExample()
        {
            return @"<!-- 高级功能 HyperlinkButton 示例 -->
<!-- 不同字体大小 -->
<ui:HyperlinkButton Content=""小字体链接""
                    FontSize=""10""
                    Command=""{Binding HandleInteractionCommand}""
                    CommandParameter=""小字体""/>

<ui:HyperlinkButton Content=""标准字体链接""
                    FontSize=""14""
                    Command=""{Binding HandleInteractionCommand}""
                    CommandParameter=""标准字体""/>

<ui:HyperlinkButton Content=""大字体链接""
                    FontSize=""18""
                    Command=""{Binding HandleInteractionCommand}""
                    CommandParameter=""大字体""/>

<!-- 不同颜色 -->
<ui:HyperlinkButton Content=""蓝色链接""
                    Foreground=""Blue""
                    Command=""{Binding HandleInteractionCommand}""
                    CommandParameter=""蓝色链接""/>

<ui:HyperlinkButton Content=""绿色链接""
                    Foreground=""Green""
                    Command=""{Binding HandleInteractionCommand}""
                    CommandParameter=""绿色链接""/>

<!-- 文本装饰 -->
<ui:HyperlinkButton Command=""{Binding HandleInteractionCommand}""
                    CommandParameter=""下划线链接"">
    <TextBlock Text=""下划线链接"" TextDecorations=""Underline""/>
</ui:HyperlinkButton>

<ui:HyperlinkButton Command=""{Binding HandleInteractionCommand}""
                    CommandParameter=""粗体链接"">
    <TextBlock Text=""粗体链接"" FontWeight=""Bold""/>
</ui:HyperlinkButton>";
        }

        private string GetDefaultAdvancedHyperlinkButtonCSharpExample()
        {
            return @"// 高级功能 HyperlinkButton MVVM 命令处理示例
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

public partial class HyperlinkButtonPageViewModel : ObservableObject
{
    /// <summary>
    /// 通用交互命令 - 支持不同样式的链接
    /// </summary>
    [RelayCommand]
    private void HandleInteraction(string? parameter)
    {
        InteractionCount++;

        // 根据不同的操作显示不同的状态消息
        StatusMessage = parameter switch
        {
            ""小字体"" => ""🔤 点击了小字体链接"",
            ""标准字体"" => ""🔤 点击了标准字体链接"",
            ""大字体"" => ""🔤 点击了大字体链接"",
            ""蓝色链接"" => ""🔵 点击了蓝色链接"",
            ""绿色链接"" => ""🟢 点击了绿色链接"",
            ""红色链接"" => ""🔴 点击了红色链接"",
            ""下划线链接"" => ""📝 点击了下划线链接"",
            ""粗体链接"" => ""💪 点击了粗体链接"",
            ""斜体链接"" => ""📐 点击了斜体链接"",
            _ => $""🎯 执行了操作: {parameter}""
        };

        // 可以根据链接类型执行特定的业务逻辑
        ExecuteStyleSpecificAction(parameter);
    }

    private void ExecuteStyleSpecificAction(string? action)
    {
        // 根据不同的样式执行不同的逻辑
        switch (action)
        {
            case ""小字体"":
            case ""标准字体"":
            case ""大字体"":
                // 字体相关的操作
                break;
            case ""蓝色链接"":
            case ""绿色链接"":
            case ""红色链接"":
                // 颜色相关的操作
                break;
            case ""下划线链接"":
            case ""粗体链接"":
            case ""斜体链接"":
                // 文本装饰相关的操作
                break;
        }
    }
}";
        }

        private string GetDefaultApplicationScenarioXamlExample()
        {
            return @"<!-- 实际应用场景 HyperlinkButton 示例 -->
<!-- 导航链接 -->
<GroupBox Header=""🧭 导航链接"">
    <WrapPanel Orientation=""Horizontal"">
        <ui:HyperlinkButton Command=""{Binding HandleInteractionCommand}""
                          CommandParameter=""首页"">
            <StackPanel Orientation=""Horizontal"">
                <ui:SymbolIcon Symbol=""Home24"" FontSize=""14"" Margin=""0,0,4,0""/>
                <TextBlock Text=""首页""/>
            </StackPanel>
        </ui:HyperlinkButton>

        <ui:HyperlinkButton Command=""{Binding HandleInteractionCommand}""
                          CommandParameter=""关于我们"">
            <StackPanel Orientation=""Horizontal"">
                <ui:SymbolIcon Symbol=""Info24"" FontSize=""14"" Margin=""0,0,4,0""/>
                <TextBlock Text=""关于我们""/>
            </StackPanel>
        </ui:HyperlinkButton>

        <ui:HyperlinkButton Command=""{Binding HandleInteractionCommand}""
                          CommandParameter=""联系我们"">
            <StackPanel Orientation=""Horizontal"">
                <ui:SymbolIcon Symbol=""ContactCard24"" FontSize=""14"" Margin=""0,0,4,0""/>
                <TextBlock Text=""联系我们""/>
            </StackPanel>
        </ui:HyperlinkButton>
    </WrapPanel>
</GroupBox>

<!-- 操作链接 -->
<GroupBox Header=""⚡ 操作链接"">
    <WrapPanel Orientation=""Horizontal"">
        <ui:HyperlinkButton Command=""{Binding HandleInteractionCommand}""
                          CommandParameter=""编辑资料"">
            <StackPanel Orientation=""Horizontal"">
                <ui:SymbolIcon Symbol=""Edit24"" FontSize=""14"" Margin=""0,0,4,0""/>
                <TextBlock Text=""编辑资料""/>
            </StackPanel>
        </ui:HyperlinkButton>

        <ui:HyperlinkButton Command=""{Binding HandleInteractionCommand}""
                          CommandParameter=""查看详情"">
            <StackPanel Orientation=""Horizontal"">
                <ui:SymbolIcon Symbol=""Eye24"" FontSize=""14"" Margin=""0,0,4,0""/>
                <TextBlock Text=""查看详情""/>
            </StackPanel>
        </ui:HyperlinkButton>

        <ui:HyperlinkButton Command=""{Binding HandleInteractionCommand}""
                          CommandParameter=""删除项目""
                          Foreground=""Red"">
            <StackPanel Orientation=""Horizontal"">
                <ui:SymbolIcon Symbol=""Delete24"" FontSize=""14"" Margin=""0,0,4,0""/>
                <TextBlock Text=""删除项目""/>
            </StackPanel>
        </ui:HyperlinkButton>
    </WrapPanel>
</GroupBox>";
        }

        private string GetDefaultApplicationScenarioCSharpExample()
        {
            return @"// 实际应用场景 HyperlinkButton MVVM 完整示例
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Diagnostics;

public partial class ApplicationViewModel : ObservableObject
{
    #region 属性

    [ObservableProperty]
    private int interactionCount = 0;

    [ObservableProperty]
    private string statusMessage = ""准备就绪"";

    #endregion

    #region 命令

    /// <summary>
    /// 处理所有超链接按钮的统一命令
    /// </summary>
    [RelayCommand]
    private async Task HandleInteraction(string? parameter)
    {
        try
        {
            InteractionCount++;

            // 更新状态消息
            StatusMessage = parameter switch
            {
                ""首页"" => ""🏠 正在导航到首页..."",
                ""关于我们"" => ""ℹ️ 正在加载关于我们页面..."",
                ""联系我们"" => ""📞 正在打开联系我们页面..."",
                ""编辑资料"" => ""✏️ 正在打开编辑资料对话框..."",
                ""查看详情"" => ""👁️ 正在加载详细信息..."",
                ""删除项目"" => ""🗑️ 正在执行删除操作..."",
                _ => $""🎯 执行了操作: {parameter}""
            };

            // 执行具体的业务逻辑
            await ExecuteBusinessLogic(parameter);
        }
        catch (Exception ex)
        {
            StatusMessage = $""❌ 操作失败: {ex.Message}"";
        }
    }

    #endregion

    #region 私有方法

    private async Task ExecuteBusinessLogic(string? action)
    {
        switch (action)
        {
            case ""首页"":
                // 导航到首页
                NavigateToHome();
                break;

            case ""关于我们"":
                // 显示关于我们页面
                ShowAboutPage();
                break;

            case ""联系我们"":
                // 打开联系我们页面
                ShowContactPage();
                break;

            case ""编辑资料"":
                // 打开编辑资料对话框
                await ShowEditProfileDialog();
                break;

            case ""查看详情"":
                // 加载并显示详细信息
                await LoadAndShowDetails();
                break;

            case ""删除项目"":
                // 执行删除操作（需要确认）
                await ConfirmAndDeleteItem();
                break;
        }
    }

    private void NavigateToHome()
    {
        // 实际的导航逻辑
        StatusMessage = ""🏠 已导航到首页"";
    }

    private void ShowAboutPage()
    {
        // 显示关于我们页面的逻辑
        StatusMessage = ""ℹ️ 关于我们页面已打开"";
    }

    private void ShowContactPage()
    {
        // 显示联系我们页面的逻辑
        StatusMessage = ""📞 联系我们页面已打开"";
    }

    private async Task ShowEditProfileDialog()
    {
        // 打开编辑资料对话框的逻辑
        await Task.Delay(100); // 模拟异步操作
        StatusMessage = ""✏️ 编辑资料对话框已打开"";
    }

    private async Task LoadAndShowDetails()
    {
        // 加载详细信息的逻辑
        await Task.Delay(200); // 模拟异步加载
        StatusMessage = ""👁️ 详细信息已加载"";
    }

    private async Task ConfirmAndDeleteItem()
    {
        // 确认并删除项目的逻辑
        // 通常需要显示确认对话框
        await Task.Delay(100); // 模拟异步操作
        StatusMessage = ""🗑️ 项目删除操作已完成"";
    }

    #endregion
}";
        }

        #endregion
    }
}
