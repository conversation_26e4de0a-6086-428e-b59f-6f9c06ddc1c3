<UserControl x:Class="WPFTest.Views.DragDrop.CrossControlDragExample"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:dd="urn:gong-wpf-dragdrop"
             xmlns:local="clr-namespace:WPFTest.Views.DragDrop"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1400">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题和状态区域 -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="🔄 跨控件拖拽示例" 
                       FontSize="24" 
                       FontWeight="Bold" 
                       Margin="0,0,0,10"/>
            
            <TextBlock Text="演示 gong-wpf-dragdrop 库在不同控件间的拖拽应用，支持数据类型转换和复杂的拖拽规则。" 
                       FontSize="14" 
                       Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                       TextWrapping="Wrap"
                       Margin="0,0,0,15"/>

            <!-- 状态信息 -->
            <Border Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Padding="15">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" 
                               Text="{Binding StatusMessage}" 
                               VerticalAlignment="Center"
                               FontWeight="Medium"/>

                    <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="20,0">
                        <ui:SymbolIcon Symbol="ArrowMove24" FontSize="16" Margin="0,0,5,0"/>
                        <TextBlock Text="{Binding InteractionCount, StringFormat='操作次数: {0}'}" 
                                   VerticalAlignment="Center"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2" Orientation="Horizontal">
                        <ui:Button Content="重置数据" 
                                   Command="{Binding ResetDataCommand}"
                                   Icon="{ui:SymbolIcon ArrowReset24}"
                                   Appearance="Secondary"
                                   Margin="0,0,10,0"/>
                        
                        <ui:Button Content="清空回收站" 
                                   Command="{Binding ClearRecycleCommand}"
                                   Icon="{ui:SymbolIcon Delete24}"
                                   Appearance="Danger"/>
                    </StackPanel>
                </Grid>
            </Border>
        </StackPanel>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 左上：ListView -->
            <Border Grid.Row="0" Grid.Column="0"
                    Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Margin="0,0,10,10">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 标题 -->
                    <Border Grid.Row="0"
                            Background="{DynamicResource AccentFillColorSecondaryBrush}"
                            CornerRadius="8,8,0,0"
                            Padding="12,8">
                        <StackPanel Orientation="Horizontal">
                            <ui:SymbolIcon Symbol="List24" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="ListView" FontWeight="Bold" Foreground="White"/>
                        </StackPanel>
                    </Border>

                    <!-- ListView 控件 -->
                    <ListView Grid.Row="1"
                              ItemsSource="{Binding ListViewItems}"
                              SelectedItem="{Binding SelectedListViewItem}"
                              dd:DragDrop.IsDragSource="True"
                              dd:DragDrop.IsDropTarget="True"
                              dd:DragDrop.DragHandler="{Binding}"
                              dd:DragDrop.DropHandler="{Binding}"
                              dd:DragDrop.UseDefaultDragAdorner="True"
                              Margin="8">
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <Border Background="LightBlue" 
                                        Padding="8" 
                                        Margin="2"
                                        CornerRadius="4"
                                        Cursor="Hand">
                                    <StackPanel>
                                        <TextBlock Text="{Binding Name}" FontWeight="Medium"/>
                                        <TextBlock Text="{Binding Type}" FontSize="11"/>
                                        <TextBlock Text="{Binding Description}" FontSize="10" 
                                                   Foreground="Gray" TextWrapping="Wrap"/>
                                    </StackPanel>
                                </Border>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>

                    <!-- 操作按钮 -->
                    <ui:Button Grid.Row="2" 
                               Content="添加项目" 
                               Command="{Binding AddListViewItemCommand}"
                               Icon="{ui:SymbolIcon Add24}"
                               Appearance="Primary"
                               HorizontalAlignment="Stretch"
                               Margin="8"/>
                </Grid>
            </Border>

            <!-- 中上：TreeView -->
            <Border Grid.Row="0" Grid.Column="1"
                    Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Margin="5,0,5,10">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 标题 -->
                    <Border Grid.Row="0"
                            Background="{DynamicResource SystemFillColorSuccessBrush}"
                            CornerRadius="8,8,0,0"
                            Padding="12,8">
                        <StackPanel Orientation="Horizontal">
                            <ui:SymbolIcon Symbol="TreeDeciduous24" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="TreeView" FontWeight="Bold" Foreground="White"/>
                        </StackPanel>
                    </Border>

                    <!-- TreeView 控件 -->
                    <TreeView Grid.Row="1"
                              ItemsSource="{Binding TreeViewItems}"
                              dd:DragDrop.IsDragSource="True"
                              dd:DragDrop.IsDropTarget="True"
                              dd:DragDrop.DragHandler="{Binding}"
                              dd:DragDrop.DropHandler="{Binding}"
                              Margin="8">
                        <TreeView.ItemTemplate>
                            <HierarchicalDataTemplate ItemsSource="{Binding Children}">
                                <Border Background="LightGreen" 
                                        Padding="6" 
                                        Margin="1"
                                        CornerRadius="3"
                                        Cursor="Hand">
                                    <StackPanel Orientation="Horizontal">
                                        <ui:SymbolIcon Symbol="Folder24" FontSize="14" Margin="0,0,6,0"/>
                                        <TextBlock Text="{Binding Name}" FontWeight="Medium"/>
                                    </StackPanel>
                                </Border>
                            </HierarchicalDataTemplate>
                        </TreeView.ItemTemplate>
                    </TreeView>
                </Grid>
            </Border>

            <!-- 右上：DataGrid -->
            <Border Grid.Row="0" Grid.Column="2"
                    Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Margin="10,0,0,10">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 标题 -->
                    <Border Grid.Row="0"
                            Background="{DynamicResource SystemFillColorCautionBrush}"
                            CornerRadius="8,8,0,0"
                            Padding="12,8">
                        <StackPanel Orientation="Horizontal">
                            <ui:SymbolIcon Symbol="Table24" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="DataGrid" FontWeight="Bold" Foreground="White"/>
                        </StackPanel>
                    </Border>

                    <!-- DataGrid 控件 -->
                    <DataGrid Grid.Row="1"
                              ItemsSource="{Binding DataGridItems}"
                              SelectedItem="{Binding SelectedDataGridItem}"
                              dd:DragDrop.IsDragSource="True"
                              dd:DragDrop.IsDropTarget="True"
                              dd:DragDrop.DragHandler="{Binding}"
                              dd:DragDrop.DropHandler="{Binding}"
                              AutoGenerateColumns="False"
                              CanUserAddRows="False"
                              Margin="8">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="名称" Binding="{Binding Name}" Width="*"/>
                            <DataGridTextColumn Header="类别" Binding="{Binding Category}" Width="60"/>
                            <DataGridTextColumn Header="值" Binding="{Binding Value}" Width="50"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- 操作按钮 -->
                    <ui:Button Grid.Row="2" 
                               Content="添加数据" 
                               Command="{Binding AddDataGridItemCommand}"
                               Icon="{ui:SymbolIcon Add24}"
                               Appearance="Primary"
                               HorizontalAlignment="Stretch"
                               Margin="8"/>
                </Grid>
            </Border>

            <!-- 左下：ListBox -->
            <Border Grid.Row="1" Grid.Column="0"
                    Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Margin="0,5,10,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 标题 -->
                    <Border Grid.Row="0"
                            Background="{DynamicResource SystemFillColorNeutralBrush}"
                            CornerRadius="8,8,0,0"
                            Padding="12,8">
                        <StackPanel Orientation="Horizontal">
                            <ui:SymbolIcon Symbol="RectangleLandscape24" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="ListBox" FontWeight="Bold" Foreground="White"/>
                        </StackPanel>
                    </Border>

                    <!-- ListBox 控件 -->
                    <ListBox Grid.Row="1"
                             ItemsSource="{Binding ListBoxItems}"
                             dd:DragDrop.IsDragSource="True"
                             dd:DragDrop.IsDropTarget="True"
                             dd:DragDrop.DragHandler="{Binding}"
                             dd:DragDrop.DropHandler="{Binding}"
                             Margin="8">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <Border Background="LightCoral" 
                                        Padding="8" 
                                        Margin="2"
                                        CornerRadius="4"
                                        Cursor="Hand">
                                    <StackPanel Orientation="Horizontal">
                                        <Ellipse Width="12" Height="12" Fill="Red" Margin="0,0,8,0"/>
                                        <TextBlock Text="{Binding Text}" FontWeight="Medium"/>
                                        <TextBlock Text="{Binding Color, StringFormat='({0})'}" 
                                                   FontSize="11" Margin="8,0,0,0"/>
                                    </StackPanel>
                                </Border>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                </Grid>
            </Border>

            <!-- 右下：回收站 -->
            <Border Grid.Row="1" Grid.Column="1" Grid.ColumnSpan="2"
                    Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Margin="5,5,0,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 标题 -->
                    <Border Grid.Row="0"
                            Background="{DynamicResource SystemFillColorCriticalBrush}"
                            CornerRadius="8,8,0,0"
                            Padding="12,8">
                        <StackPanel Orientation="Horizontal">
                            <ui:SymbolIcon Symbol="Delete24" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="回收站 (可接受任何类型)" FontWeight="Bold" Foreground="White"/>
                            <TextBlock Text="{Binding RecycleItems.Count, StringFormat='({0} 项)'}" 
                                       FontWeight="Bold" Foreground="White" Margin="8,0,0,0"/>
                        </StackPanel>
                    </Border>

                    <!-- 回收站内容 -->
                    <ScrollViewer Grid.Row="1" Margin="8" VerticalScrollBarVisibility="Auto">
                        <ItemsControl ItemsSource="{Binding RecycleItems}"
                                      dd:DragDrop.IsDropTarget="True"
                                      dd:DragDrop.DropHandler="{Binding}"
                                      MinHeight="100">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <WrapPanel Orientation="Horizontal"/>
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Background="Gray" 
                                            Padding="6" 
                                            Margin="3"
                                            CornerRadius="3"
                                            Opacity="0.7">
                                        <TextBlock Text="{Binding}" 
                                                   Foreground="White" 
                                                   FontSize="11"
                                                   MaxWidth="100"
                                                   TextWrapping="Wrap"/>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </Grid>
            </Border>
        </Grid>

        <!-- 底部：使用说明 -->
        <Border Grid.Row="2"
                Background="{DynamicResource CardBackgroundFillColorSecondaryBrush}"
                BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                BorderThickness="1"
                CornerRadius="8"
                Padding="15"
                Margin="0,20,0,0">
            <StackPanel>
                <TextBlock Text="💡 使用说明" 
                           FontWeight="Bold" 
                           FontSize="14"
                           Margin="0,0,0,8"/>
                
                <TextBlock TextWrapping="Wrap" 
                           FontSize="12"
                           Foreground="{DynamicResource TextFillColorSecondaryBrush}">
                    <Run Text="• 跨控件拖拽：可以在 ListView、TreeView、DataGrid、ListBox 之间拖拽项目"/>
                    <LineBreak/>
                    <Run Text="• 自动转换：系统会自动将不同类型的数据转换为目标控件支持的格式"/>
                    <LineBreak/>
                    <Run Text="• 回收站：可以将任何类型的项目拖拽到回收站进行删除"/>
                    <LineBreak/>
                    <Run Text="• 智能验证：系统会验证拖拽操作的有效性，防止无效操作"/>
                    <LineBreak/>
                    <Run Text="• 实时反馈：拖拽过程中会显示视觉反馈和状态更新"/>
                </TextBlock>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
