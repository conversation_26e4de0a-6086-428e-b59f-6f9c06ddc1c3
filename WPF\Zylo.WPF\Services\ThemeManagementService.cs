using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media;
using Wpf.Ui.Appearance;
using Zylo.YLog.Runtime;

namespace Zylo.WPF.Services;

/// <summary>
/// 主题管理服务
/// </summary>
/// <remarks>
/// 负责主题和强调色的加载、保存、应用等功能
/// 使用Windows官方的强调色算法生成颜色变体
/// </remarks>
public interface IThemeManagementService
{
    /// <summary>
    /// 主题配置变化事件
    /// </summary>
    event EventHandler? ThemeConfigurationChanged;

    /// <summary>
    /// 当前主题配置
    /// </summary>
    ThemeConfiguration CurrentConfiguration { get; }

    /// <summary>
    /// 加载保存的主题设置
    /// </summary>
    void LoadSavedThemeSettings();

    /// <summary>
    /// 保存主题设置
    /// </summary>
    /// <param name="theme">主题模式</param>
    /// <param name="accentColor">强调色</param>
    void SaveThemeSettings(ApplicationTheme theme, Color accentColor);

    /// <summary>
    /// 应用主题模式
    /// </summary>
    /// <param name="theme">主题模式</param>
    void ApplyTheme(ApplicationTheme theme);

    /// <summary>
    /// 应用强调色
    /// </summary>
    /// <param name="color">强调色</param>
    void ApplyAccentColor(Color color);

    /// <summary>
    /// 获取当前强调色
    /// </summary>
    /// <returns>当前强调色</returns>
    Color GetCurrentAccentColor();

    /// <summary>
    /// 获取当前主题设置
    /// </summary>
    /// <returns>当前主题设置</returns>
    ThemeSettings? GetCurrentThemeSettings();

    /// <summary>
    /// 异步更新主题模式
    /// </summary>
    /// <param name="themeMode">主题模式</param>
    /// <returns>是否成功</returns>
    Task<bool> UpdateThemeModeAsync(string themeMode);

    /// <summary>
    /// 异步更新强调色
    /// </summary>
    /// <param name="accentColor">强调色</param>
    /// <param name="useCustom">是否使用自定义颜色</param>
    /// <returns>是否成功</returns>
    Task<bool> UpdateAccentColorAsync(string accentColor, bool useCustom = false);

    /// <summary>
    /// 重置到默认设置
    /// </summary>
    /// <returns>是否成功</returns>
    Task<bool> ResetToDefaultAsync();
}

/// <summary>
/// 主题管理服务实现
/// </summary>
public class ThemeManagementService : IThemeManagementService
{
    public readonly YLoggerInstance _logger = YLogger.ForSilent<ThemeManagementService>();

    #region 常量和字段

    private static readonly string SettingsFilePath = Path.Combine(
        Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
        "WPFTest",
        "theme-settings.json");

    private ThemeSettings? _currentSettings;
    private ThemeConfiguration _currentConfiguration;

    #endregion

    #region 事件

    /// <summary>
    /// 主题配置变化事件
    /// </summary>
    public event EventHandler? ThemeConfigurationChanged;

    #endregion

    #region 属性

    /// <summary>
    /// 当前主题配置
    /// </summary>
    public ThemeConfiguration CurrentConfiguration => _currentConfiguration;

    #endregion

    #region 构造函数

    public ThemeManagementService()
    {
        // 初始化默认配置
        _currentConfiguration = new ThemeConfiguration
        {
            ThemeMode = "Auto",
            AccentColor = "#0078D4"
        };
    }

    #endregion

    #region 公共方法

    /// <summary>
    /// 加载保存的主题设置
    /// </summary>
    public void LoadSavedThemeSettings()
    {
        try
        {
            _logger.Info("LoadSavedThemeSettings" + "🎨 正在加载保存的主题设置...");

            if (File.Exists(SettingsFilePath))
            {
                var json = File.ReadAllText(SettingsFilePath);
                var settings = JsonSerializer.Deserialize<ThemeSettings>(json);

                if (settings != null)
                {
                    _currentSettings = settings;

                    // 应用主题模式
                    _logger.Info("LoadSavedThemeSettings" + $"📱 应用主题模式: {settings.Theme}");
                    ApplyTheme(settings.Theme);

                    // 应用强调色
                    if (settings.AccentColor != null)
                    {
                        var color = Color.FromArgb(
                            settings.AccentColor.A,
                            settings.AccentColor.R,
                            settings.AccentColor.G,
                            settings.AccentColor.B);

                        _logger.Info("LoadSavedThemeSettings" + $"🎨 应用强调色: #{color.R:X2}{color.G:X2}{color.B:X2}");
                        ApplyAccentColor(color);
                    }

                    _logger.Info("LoadSavedThemeSettings" + "✅ 主题设置加载完成");
                }
                else
                {
                    _logger.Warning("LoadSavedThemeSettings" + "⚠️ 主题设置文件格式无效，使用默认设置");
                    ApplyDefaultThemeSettings();
                }
            }
            else
            {
                _logger.Info("📄 未找到主题设置文件，使用默认设置");
                ApplyDefaultThemeSettings();
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 加载主题设置失败: {ex.Message}");
            ApplyDefaultThemeSettings();
        }
    }

    /// <summary>
    /// 保存主题设置
    /// </summary>
    /// <param name="theme">主题模式</param>
    /// <param name="accentColor">强调色</param>
    public void SaveThemeSettings(ApplicationTheme theme, Color accentColor)
    {
        try
        {
            var settings = new ThemeSettings
            {
                Theme = theme,
                AccentColor = new ColorData
                {
                    R = accentColor.R,
                    G = accentColor.G,
                    B = accentColor.B,
                    A = accentColor.A
                }
            };

            _currentSettings = settings;

            var directory = Path.GetDirectoryName(SettingsFilePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            var json = JsonSerializer.Serialize(settings, new JsonSerializerOptions { WriteIndented = true });
            File.WriteAllText(SettingsFilePath, json);

            _logger.Info($"💾 主题设置已保存: {theme}, #{accentColor.R:X2}{accentColor.G:X2}{accentColor.B:X2}");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 保存主题设置失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 应用主题模式
    /// </summary>
    /// <param name="theme">主题模式</param>
    public void ApplyTheme(ApplicationTheme theme)
    {
        try
        {
            ApplicationThemeManager.Apply(theme);

            // 主题切换后，重新应用ListView悬停背景色
            var app = System.Windows.Application.Current;
            if (app?.Resources != null)
            {
                var hoverBackgroundColor = GenerateHoverBackgroundColor(theme);
                app.Resources["ListViewItemBackgroundPointerOver"] = new SolidColorBrush(hoverBackgroundColor);
            }

            _logger.Info($"✅ 主题模式已应用: {theme}");
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 应用主题模式失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 应用强调色
    /// </summary>
    /// <param name="color">强调色</param>
    public void ApplyAccentColor(Color color)
    {
        try
        {
            // 应用到WPF-UI主题系统
            ApplicationAccentColorManager.Apply(color);

            // 生成并应用强调色变体
            ApplyAccentColorVariants(color);

            _logger.Info($"✅ 强调色已应用: #{color.R:X2}{color.G:X2}{color.B:X2}");

            // 🔍 调试：输出ListView相关资源
            LogListViewResources();

            // 触发主题配置变化事件，通知所有监听者
            ThemeConfigurationChanged?.Invoke(this, EventArgs.Empty);
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 应用强调色失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取当前强调色
    /// </summary>
    /// <returns>当前强调色</returns>
    public Color GetCurrentAccentColor()
    {
        try
        {
            var app = System.Windows.Application.Current;
            if (app?.Resources?.Contains("SystemAccentColorPrimaryBrush") == true)
            {
                if (app.Resources["SystemAccentColorPrimaryBrush"] is SolidColorBrush brush)
                {
                    return brush.Color;
                }
            }

            // 如果找不到，返回默认蓝色
            return Colors.Blue;
        }
        catch (Exception ex)
        {
            _logger.Warning($"⚠️ 获取当前强调色失败: {ex.Message}");
            return Colors.Blue;
        }
    }

    /// <summary>
    /// 根据背景色获取对比度最佳的文字颜色
    /// </summary>
    /// <param name="backgroundColor">背景色</param>
    /// <returns>文字颜色（白色或黑色）</returns>
    private Color GetContrastingTextColor(Color backgroundColor)
    {
        // 计算亮度 (使用 W3C 推荐的公式)
        double luminance = (0.299 * backgroundColor.R + 0.587 * backgroundColor.G + 0.114 * backgroundColor.B) / 255;

        // 如果背景较暗，使用白色文字；如果背景较亮，使用黑色文字
        return luminance > 0.5 ? Colors.Black : Colors.White;
    }

    /// <summary>
    /// 获取当前主题设置
    /// </summary>
    /// <returns>当前主题设置</returns>
    public ThemeSettings? GetCurrentThemeSettings()
    {
        return _currentSettings;
    }

    /// <summary>
    /// 异步更新主题模式
    /// </summary>
    /// <param name="themeMode">主题模式</param>
    /// <returns>是否成功</returns>
    public async Task<bool> UpdateThemeModeAsync(string themeMode)
    {
        try
        {
            var theme = themeMode switch
            {
                "Light" => ApplicationTheme.Light,
                "Dark" => ApplicationTheme.Dark,
                _ => ApplicationTheme.Unknown
            };

            ApplyTheme(theme);

            // 更新当前配置
            _currentConfiguration.ThemeMode = themeMode;

            // 保存设置
            if (_currentSettings != null)
            {
                _currentSettings.Theme = theme;
                var color = Color.FromArgb(
                    _currentSettings.AccentColor?.A ?? 255,
                    _currentSettings.AccentColor?.R ?? 0,
                    _currentSettings.AccentColor?.G ?? 120,
                    _currentSettings.AccentColor?.B ?? 212);
                SaveThemeSettings(theme, color);
            }

            // 延迟触发事件，确保 WPF-UI 主题系统完全更新
            await Task.Delay(500);

            // 触发事件通知所有监听者
            ThemeConfigurationChanged?.Invoke(this, EventArgs.Empty);

            return true;
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 更新主题模式失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 异步更新强调色
    /// </summary>
    /// <param name="accentColor">强调色</param>
    /// <param name="useCustom">是否使用自定义颜色</param>
    /// <returns>是否成功</returns>
    public async Task<bool> UpdateAccentColorAsync(string accentColor, bool useCustom = false)
    {
        try
        {
            // 解析颜色字符串
            var color = ParseColorString(accentColor);
            ApplyAccentColor(color);

            // 更新当前配置
            _currentConfiguration.AccentColor = accentColor;

            // 保存设置
            var currentTheme = _currentSettings?.Theme ?? ApplicationTheme.Unknown;
            SaveThemeSettings(currentTheme, color);

            // 触发事件
            ThemeConfigurationChanged?.Invoke(this, EventArgs.Empty);

            await Task.Delay(100); // 模拟异步操作
            return true;
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 更新强调色失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 重置到默认设置
    /// </summary>
    /// <returns>是否成功</returns>
    public async Task<bool> ResetToDefaultAsync()
    {
        try
        {
            // 重置到默认设置
            ApplyTheme(ApplicationTheme.Unknown);
            ApplyAccentColor(Colors.Blue);

            // 更新当前配置
            _currentConfiguration.ThemeMode = "Auto";
            _currentConfiguration.AccentColor = "#0078D4";

            // 保存设置
            SaveThemeSettings(ApplicationTheme.Unknown, Colors.Blue);

            // 触发事件
            ThemeConfigurationChanged?.Invoke(this, EventArgs.Empty);

            await Task.Delay(100); // 模拟异步操作
            return true;
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 重置主题失败: {ex.Message}");
            return false;
        }
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 解析颜色字符串
    /// </summary>
    /// <param name="colorString">颜色字符串（如 #0078D4）</param>
    /// <returns>颜色对象</returns>
    private Color ParseColorString(string colorString)
    {
        try
        {
            if (colorString.StartsWith("#") && colorString.Length == 7)
            {
                var r = Convert.ToByte(colorString.Substring(1, 2), 16);
                var g = Convert.ToByte(colorString.Substring(3, 2), 16);
                var b = Convert.ToByte(colorString.Substring(5, 2), 16);
                return Color.FromArgb(255, r, g, b);
            }

            return Colors.Blue; // 默认颜色
        }
        catch
        {
            return Colors.Blue; // 解析失败时返回默认颜色
        }
    }

    /// <summary>
    /// 应用默认主题设置
    /// </summary>
    private void ApplyDefaultThemeSettings()
    {
        try
        {
            _logger.Info("🎨 应用默认主题设置...");

            // 应用默认主题（跟随系统）
            ApplyTheme(ApplicationTheme.Unknown);

            // 应用默认强调色（蓝色）
            var defaultColor = Colors.Blue;
            ApplyAccentColor(defaultColor);

            // 保存默认设置
            SaveThemeSettings(ApplicationTheme.Unknown, defaultColor);

            _logger.Info("✅ 默认主题设置应用完成");

            // 🔍 调试：输出当前ListView相关资源
            LogListViewResources();
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 应用默认主题设置失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 调试：输出ListView相关资源的当前值
    /// </summary>
    private void LogListViewResources()
    {
        try
        {
            var app = System.Windows.Application.Current;
            if (app?.Resources != null)
            {
                _logger.Debug("🔍 ListView 相关资源状态：");

                if (app.Resources.Contains("ListViewItemPillFillBrush"))
                {
                    var brush = app.Resources["ListViewItemPillFillBrush"] as SolidColorBrush;
                    _logger.Debug($"   ListViewItemPillFillBrush: #{brush?.Color.R:X2}{brush?.Color.G:X2}{brush?.Color.B:X2}");
                }

                if (app.Resources.Contains("ListViewItemBackgroundPointerOver"))
                {
                    var brush = app.Resources["ListViewItemBackgroundPointerOver"] as SolidColorBrush;
                    _logger.Debug($"   ListViewItemBackgroundPointerOver: #{brush?.Color.A:X2}{brush?.Color.R:X2}{brush?.Color.G:X2}{brush?.Color.B:X2}");
                }

                if (app.Resources.Contains("ListViewItemForeground"))
                {
                    var brush = app.Resources["ListViewItemForeground"] as SolidColorBrush;
                    _logger.Debug($"   ListViewItemForeground: #{brush?.Color.A:X2}{brush?.Color.R:X2}{brush?.Color.G:X2}{brush?.Color.B:X2}");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.Warning($"⚠️ 输出ListView资源状态失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 应用强调色变体（使用Windows官方算法）
    /// </summary>
    private void ApplyAccentColorVariants(Color baseColor)
    {
        try
        {
            var app = System.Windows.Application.Current;
            if (app?.Resources != null)
            {
                // 生成强调色变体
                var variants = GenerateAccentColorVariants(baseColor);

                // 更新基础强调色资源
                app.Resources["SystemAccentColorPrimaryBrush"] = new SolidColorBrush(baseColor);
                app.Resources["SystemAccentColorSecondaryBrush"] = new SolidColorBrush(variants.Light1);
                app.Resources["SystemAccentColorTertiaryBrush"] = new SolidColorBrush(variants.Light2);
                app.Resources["AccentFillColorDefaultBrush"] = new SolidColorBrush(baseColor);
                app.Resources["AccentFillColorSecondaryBrush"] = new SolidColorBrush(variants.Light1);
                app.Resources["AccentFillColorTertiaryBrush"] = new SolidColorBrush(variants.Light2);
                app.Resources["AccentFillColorDisabledBrush"] = new SolidColorBrush(variants.Light3);

                // 🎯 更新 ListView 特定的颜色资源
                app.Resources["ListViewItemPillFillBrush"] = new SolidColorBrush(baseColor);

                // 根据当前主题生成合适的悬停背景色
                var currentTheme = ApplicationThemeManager.GetAppTheme();
                var hoverBackgroundColor = GenerateHoverBackgroundColor(currentTheme);
                app.Resources["ListViewItemBackgroundPointerOver"] = new SolidColorBrush(hoverBackgroundColor);

                // 🎯 更新所有控件的强调色相关资源

                // 🎨 文字颜色资源 - 确保按钮文字可见
                var textOnAccentColor = GetContrastingTextColor(baseColor);
                app.Resources["TextOnAccentFillColorPrimaryBrush"] = new SolidColorBrush(textOnAccentColor);
                app.Resources["TextOnAccentFillColorSecondaryBrush"] = new SolidColorBrush(textOnAccentColor);
                app.Resources["TextOnAccentFillColorDisabledBrush"] = new SolidColorBrush(Color.FromArgb(128, textOnAccentColor.R, textOnAccentColor.G, textOnAccentColor.B));

                // 🏷️ 强调色文字资源 - 用于分类标签等
                app.Resources["AccentTextFillColorPrimaryBrush"] = new SolidColorBrush(textOnAccentColor);
                app.Resources["AccentTextFillColorSecondaryBrush"] = new SolidColorBrush(Color.FromArgb(192, textOnAccentColor.R, textOnAccentColor.G, textOnAccentColor.B));
                app.Resources["AccentTextFillColorDisabledBrush"] = new SolidColorBrush(Color.FromArgb(96, textOnAccentColor.R, textOnAccentColor.G, textOnAccentColor.B));

                // 列表和导航控件
                app.Resources["ComboBoxItemPillFillBrush"] = new SolidColorBrush(baseColor);
                app.Resources["ComboBoxBorderBrushFocused"] = new SolidColorBrush(variants.Light1);
                app.Resources["NavigationViewSelectionIndicatorForeground"] = new SolidColorBrush(baseColor);
                app.Resources["TreeViewItemSelectionIndicatorForeground"] = new SolidColorBrush(baseColor);
                app.Resources["ListBoxItemSelectedBackgroundThemeBrush"] = new SolidColorBrush(baseColor);

                // 输入控件
                app.Resources["CheckBoxCheckBackgroundFillChecked"] = new SolidColorBrush(baseColor);
                app.Resources["CheckBoxCheckBackgroundFillCheckedPointerOver"] = new SolidColorBrush(variants.Light1);
                app.Resources["RadioButtonOuterEllipseCheckedStroke"] = new SolidColorBrush(baseColor);
                app.Resources["RadioButtonOuterEllipseCheckedStrokePointerOver"] = new SolidColorBrush(variants.Light1);
                app.Resources["TextControlFocusedBorderBrush"] = new SolidColorBrush(baseColor);

                // ToggleSwitch 控件
                app.Resources["ToggleSwitchStrokeOn"] = new SolidColorBrush(baseColor);
                app.Resources["ToggleSwitchStrokeOnPointerOver"] = new SolidColorBrush(variants.Light1);
                app.Resources["ToggleSwitchFillOn"] = new SolidColorBrush(baseColor);
                app.Resources["ToggleSwitchFillOnPointerOver"] = new SolidColorBrush(variants.Light1);

                // 按钮控件
                app.Resources["AccentButtonBackground"] = new SolidColorBrush(baseColor);
                app.Resources["AccentButtonBackgroundPointerOver"] = new SolidColorBrush(variants.Light1);
                app.Resources["AccentButtonBackgroundPressed"] = new SolidColorBrush(variants.Light2);
                app.Resources["ToggleButtonBackgroundChecked"] = new SolidColorBrush(baseColor);
                app.Resources["ToggleButtonForegroundCheckedPointerOver"] = new SolidColorBrush(variants.Light1);
                app.Resources["ToggleButtonBackgroundCheckedPressed"] = new SolidColorBrush(variants.Light2);
                app.Resources["HyperlinkButtonForeground"] = new SolidColorBrush(baseColor);
                app.Resources["HyperlinkButtonForegroundPointerOver"] = new SolidColorBrush(variants.Light1);
                app.Resources["HyperlinkButtonForegroundPressed"] = new SolidColorBrush(variants.Light2);

                // 进度和评级控件
                app.Resources["ProgressBarForeground"] = new SolidColorBrush(baseColor);
                app.Resources["ProgressRingForegroundThemeBrush"] = new SolidColorBrush(baseColor);
                app.Resources["SliderThumbBackground"] = new SolidColorBrush(baseColor);
                app.Resources["SliderThumbBackgroundPointerOver"] = new SolidColorBrush(variants.Light1);
                app.Resources["RatingControlSelectedForeground"] = new SolidColorBrush(baseColor);
                app.Resources["ThumbRateForeground"] = new SolidColorBrush(baseColor);

                // 日期时间控件
                app.Resources["CalendarViewSelectedBackground"] = new SolidColorBrush(baseColor);
                app.Resources["CalendarViewSelectedBorderBrush"] = new SolidColorBrush(baseColor);
                app.Resources["CalendarViewTodayBackground"] = new SolidColorBrush(baseColor);

                _logger.Info("✅ 强调色变体和ListView颜色应用完成");

                // 🔄 强制刷新UI以确保颜色变化立即生效
                ForceUIRefresh();
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 应用强调色变体失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 强制刷新UI以确保主题变化立即生效
    /// </summary>
    private void ForceUIRefresh()
    {
        try
        {
            var app = System.Windows.Application.Current;
            if (app?.MainWindow != null)
            {
                // 强制重新计算样式
                app.MainWindow.InvalidateVisual();
                app.MainWindow.UpdateLayout();

                // 递归刷新所有子控件
                RefreshVisualTree(app.MainWindow);

                _logger.Debug("🔄 UI刷新完成");
            }
        }
        catch (Exception ex)
        {
            _logger.Warning($"⚠️ UI刷新失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 递归刷新可视化树中的所有控件
    /// </summary>
    private void RefreshVisualTree(DependencyObject parent)
    {
        try
        {
            if (parent == null) return;

            var childCount = VisualTreeHelper.GetChildrenCount(parent);
            for (int i = 0; i < childCount; i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);

                if (child is FrameworkElement element)
                {
                    element.InvalidateVisual();
                    element.UpdateLayout();
                }

                RefreshVisualTree(child);
            }
        }
        catch (Exception ex)
        {
            _logger.Warning($"⚠️ 刷新可视化树失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 根据当前主题生成合适的悬停背景色
    /// </summary>
    private Color GenerateHoverBackgroundColor(ApplicationTheme theme)
    {
        // 根据主题返回合适的悬停背景色
        return theme switch
        {
            ApplicationTheme.Light => Color.FromArgb(9, 0, 0, 0),      // Light主题：黑色4%透明度
            ApplicationTheme.Dark => Color.FromArgb(15, 255, 255, 255), // Dark主题：白色6%透明度
            _ => Color.FromArgb(9, 0, 0, 0) // 默认使用Light主题颜色
        };
    }

    #region Windows强调色算法

    /// <summary>
    /// 使用Windows官方算法生成强调色变体
    /// </summary>
    private AccentColorVariants GenerateAccentColorVariants(Color baseColor)
    {
        // 转换为HSL颜色空间
        var hsl = RgbToHsl(baseColor);

        // 根据Windows设计规范生成变体
        var light1 = HslToRgb(hsl.H, hsl.S, Math.Min(1.0, hsl.L + 0.15));
        var light2 = HslToRgb(hsl.H, hsl.S, Math.Min(1.0, hsl.L + 0.30));
        var light3 = HslToRgb(hsl.H, hsl.S * 0.6, Math.Min(1.0, hsl.L + 0.45));

        return new AccentColorVariants
        {
            Base = baseColor,
            Light1 = light1,
            Light2 = light2,
            Light3 = light3
        };
    }

    /// <summary>
    /// RGB转HSL颜色空间
    /// </summary>
    private (double H, double S, double L) RgbToHsl(Color rgb)
    {
        double r = rgb.R / 255.0;
        double g = rgb.G / 255.0;
        double b = rgb.B / 255.0;

        double max = Math.Max(r, Math.Max(g, b));
        double min = Math.Min(r, Math.Min(g, b));
        double delta = max - min;

        double h = 0;
        double s = 0;
        double l = (max + min) / 2.0;

        if (delta != 0)
        {
            s = l > 0.5 ? delta / (2.0 - max - min) : delta / (max + min);

            if (max == r)
                h = (g - b) / delta + (g < b ? 6 : 0);
            else if (max == g)
                h = (b - r) / delta + 2;
            else if (max == b)
                h = (r - g) / delta + 4;

            h /= 6.0;
        }

        return (h, s, l);
    }

    /// <summary>
    /// HSL转RGB颜色空间
    /// </summary>
    private Color HslToRgb(double h, double s, double l)
    {
        double r, g, b;

        if (s == 0)
        {
            r = g = b = l;
        }
        else
        {
            double q = l < 0.5 ? l * (1 + s) : l + s - l * s;
            double p = 2 * l - q;
            r = HueToRgb(p, q, h + 1.0 / 3.0);
            g = HueToRgb(p, q, h);
            b = HueToRgb(p, q, h - 1.0 / 3.0);
        }

        return Color.FromArgb(255,
            (byte)Math.Round(r * 255),
            (byte)Math.Round(g * 255),
            (byte)Math.Round(b * 255));
    }

    /// <summary>
    /// HSL辅助函数：色调转RGB分量
    /// </summary>
    private double HueToRgb(double p, double q, double t)
    {
        if (t < 0) t += 1;
        if (t > 1) t -= 1;
        if (t < 1.0 / 6.0) return p + (q - p) * 6 * t;
        if (t < 1.0 / 2.0) return q;
        if (t < 2.0 / 3.0) return p + (q - p) * (2.0 / 3.0 - t) * 6;
        return p;
    }

    #endregion

    #endregion
}

/// <summary>
/// 主题设置
/// </summary>
public class ThemeSettings
{
    public ApplicationTheme Theme { get; set; }
    public ColorData? AccentColor { get; set; }
}

/// <summary>
/// 颜色数据
/// </summary>
public class ColorData
{
    public byte R { get; set; }
    public byte G { get; set; }
    public byte B { get; set; }
    public byte A { get; set; }
}

/// <summary>
/// 强调色变体
/// </summary>
public class AccentColorVariants
{
    public Color Base { get; set; }
    public Color Light1 { get; set; }
    public Color Light2 { get; set; }
    public Color Light3 { get; set; }
}

/// <summary>
/// 主题配置
/// </summary>
public class ThemeConfiguration
{
    /// <summary>
    /// 自动
    /// </summary>
    public string ThemeMode { get; set; } = "Auto";

    /// <summary>
    /// 强调色
    /// </summary>
    public string AccentColor { get; set; } = "#0078D4";

    /// <summary>
    /// 是否使用自定义强调色
    /// </summary>
    public bool UseCustomAccentColor { get; set; } = false;
}