<UserControl x:Class="Zylo.WPF.Controls.Navigation.MenuBarControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             mc:Ignorable="d" 
             d:DesignHeight="40" d:DesignWidth="800">
    
    <UserControl.Resources>
        <!-- MenuBar 样式资源 -->
        <Style x:Key="MenuBarStyle" TargetType="Menu">
            <Setter Property="Background" Value="{DynamicResource ApplicationBackgroundBrush}"/>
            <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
            <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="2,1"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontFamily" Value="{DynamicResource ContentControlThemeFontFamily}"/>
        </Style>

        <!-- MenuItem 顶级样式 -->
        <Style x:Key="TopLevelMenuItemStyle" TargetType="MenuItem">
            <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="Margin" Value="1,0"/>
            <Setter Property="FontWeight" Value="Normal"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="MenuItem">
                        <Grid>
                            <Border x:Name="Border"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="4"
                                    Padding="{TemplateBinding Padding}">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- 图标 -->
                                    <ContentPresenter x:Name="Icon"
                                                      Grid.Column="0"
                                                      Content="{TemplateBinding Icon}"
                                                      Margin="0,0,8,0"
                                                      VerticalAlignment="Center"/>

                                    <!-- 文本 -->
                                    <ContentPresenter x:Name="HeaderHost"
                                                      Grid.Column="1"
                                                      ContentSource="Header"
                                                      VerticalAlignment="Center"/>

                                    <!-- 子菜单箭头 -->
                                    <Path x:Name="ArrowPath"
                                          Grid.Column="2"
                                          Data="M0,0 L4,4 L0,8 Z"
                                          Fill="{TemplateBinding Foreground}"
                                          Margin="8,0,0,0"
                                          VerticalAlignment="Center"
                                          Visibility="Collapsed"/>
                                </Grid>
                            </Border>

                            <!-- 子菜单弹出窗口 -->
                            <Popup x:Name="PART_Popup"
                                   Placement="Bottom"
                                   IsOpen="{Binding IsSubmenuOpen, RelativeSource={RelativeSource TemplatedParent}}"
                                   AllowsTransparency="False"
                                   Focusable="False"
                                   PopupAnimation="Fade">
                                <Border Background="{DynamicResource ApplicationBackgroundBrush}"
                                        BorderBrush="{DynamicResource ControlStrokeColorSecondaryBrush}"
                                        BorderThickness="1"
                                        CornerRadius="2"
                                        Padding="2">
                                    <ScrollViewer Style="{DynamicResource {ComponentResourceKey ResourceId=MenuScrollViewer, TypeInTargetAssembly={x:Type FrameworkElement}}}">
                                        <ItemsPresenter KeyboardNavigation.DirectionalNavigation="Cycle"/>
                                    </ScrollViewer>
                                </Border>
                            </Popup>
                        </Grid>
                        
                        <ControlTemplate.Triggers>
                            <!-- 鼠标悬停效果 -->
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="{DynamicResource SubtleFillColorSecondaryBrush}"/>
                            </Trigger>

                            <!-- 按下效果 -->
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="{DynamicResource SubtleFillColorTertiaryBrush}"/>
                            </Trigger>

                            <!-- 子菜单打开状态 -->
                            <Trigger Property="IsSubmenuOpen" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="{DynamicResource SubtleFillColorSecondaryBrush}"/>
                            </Trigger>
                            
                            <!-- 有子菜单时显示箭头 -->
                            <Trigger Property="HasItems" Value="True">
                                <Setter TargetName="ArrowPath" Property="Visibility" Value="Visible"/>
                            </Trigger>
                            
                            <!-- 禁用状态 -->
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Foreground" Value="{DynamicResource TextFillColorDisabledBrush}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- MenuItem 子级样式 -->
        <Style x:Key="SubMenuItemStyle" TargetType="MenuItem">
            <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="Margin" Value="1,0"/>
            <Setter Property="MinWidth" Value="160"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="MenuItem">
                        <Grid>
                            <Border x:Name="Border"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="4"
                                    Padding="{TemplateBinding Padding}">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- 图标 -->
                                    <ContentPresenter x:Name="Icon"
                                                      Grid.Column="0"
                                                      Content="{TemplateBinding Icon}"
                                                      Width="16"
                                                      Height="16"
                                                      Margin="0,0,8,0"
                                                      VerticalAlignment="Center"/>

                                    <!-- 文本 -->
                                    <ContentPresenter x:Name="HeaderHost"
                                                      Grid.Column="1"
                                                      ContentSource="Header"
                                                      VerticalAlignment="Center"/>

                                    <!-- 快捷键 -->
                                    <TextBlock x:Name="InputGestureText"
                                               Grid.Column="2"
                                               Text="{TemplateBinding InputGestureText}"
                                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                               Margin="16,0,0,0"
                                               VerticalAlignment="Center"/>

                                    <!-- 子菜单箭头 -->
                                    <Path x:Name="ArrowPath"
                                          Grid.Column="3"
                                          Data="M0,0 L4,4 L0,8 Z"
                                          Fill="{TemplateBinding Foreground}"
                                          Margin="8,0,0,0"
                                          VerticalAlignment="Center"
                                          Visibility="Collapsed"/>
                                </Grid>
                            </Border>

                            <!-- 子菜单弹出窗口 -->
                            <Popup x:Name="PART_Popup"
                                   Placement="Right"
                                   IsOpen="{Binding IsSubmenuOpen, RelativeSource={RelativeSource TemplatedParent}}"
                                   AllowsTransparency="False"
                                   Focusable="False"
                                   PopupAnimation="Fade">
                                <Border Background="{DynamicResource ApplicationBackgroundBrush}"
                                        BorderBrush="{DynamicResource ControlStrokeColorSecondaryBrush}"
                                        BorderThickness="1"
                                        CornerRadius="2"
                                        Padding="2">
                                    <ScrollViewer Style="{DynamicResource {ComponentResourceKey ResourceId=MenuScrollViewer, TypeInTargetAssembly={x:Type FrameworkElement}}}">
                                        <ItemsPresenter KeyboardNavigation.DirectionalNavigation="Cycle"/>
                                    </ScrollViewer>
                                </Border>
                            </Popup>
                        </Grid>
                        
                        <ControlTemplate.Triggers>
                            <!-- 鼠标悬停效果 -->
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="{DynamicResource SubtleFillColorSecondaryBrush}"/>
                            </Trigger>

                            <!-- 按下效果 -->
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="{DynamicResource SubtleFillColorTertiaryBrush}"/>
                            </Trigger>
                            
                            <!-- 有子菜单时显示箭头 -->
                            <Trigger Property="HasItems" Value="True">
                                <Setter TargetName="ArrowPath" Property="Visibility" Value="Visible"/>
                            </Trigger>
                            
                            <!-- 禁用状态 -->
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Foreground" Value="{DynamicResource TextFillColorDisabledBrush}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <!-- MenuBar 主体 -->
    <Border Background="{DynamicResource ApplicationBackgroundBrush}"
            BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
            BorderThickness="0">
        <Menu x:Name="MainMenuBar"
              Style="{StaticResource MenuBarStyle}"
              ItemsSource="{Binding MenuItems, RelativeSource={RelativeSource AncestorType=UserControl}}"
              HorizontalAlignment="Stretch"
              VerticalAlignment="Center">
            
            <!-- 顶级菜单项模板 -->
            <Menu.ItemContainerStyle>
                <Style TargetType="MenuItem" BasedOn="{StaticResource TopLevelMenuItemStyle}">
                    <Setter Property="Header" Value="{Binding Header}"/>
                    <Setter Property="Icon" Value="{Binding Icon}"/>
                    <Setter Property="Command" Value="{Binding Path=MenuItemClickCommand, RelativeSource={RelativeSource AncestorType={x:Type UserControl}}}"/>
                    <Setter Property="CommandParameter" Value="{Binding CommandParameter}"/>
                    <Setter Property="IsEnabled" Value="{Binding IsEnabled}"/>
                    <Setter Property="ItemsSource" Value="{Binding Children}"/>
                    
                    <!-- 子菜单项样式 -->
                    <Setter Property="ItemContainerStyle">
                        <Setter.Value>
                            <Style TargetType="MenuItem" BasedOn="{StaticResource SubMenuItemStyle}">
                                <Setter Property="Header" Value="{Binding Header}"/>
                                <Setter Property="Icon" Value="{Binding Icon}"/>
                                <Setter Property="Command" Value="{Binding Path=MenuItemClickCommand, RelativeSource={RelativeSource AncestorType={x:Type UserControl}}}"/>
                                <Setter Property="CommandParameter" Value="{Binding CommandParameter}"/>
                                <Setter Property="IsEnabled" Value="{Binding IsEnabled}"/>
                                <Setter Property="InputGestureText" Value="{Binding InputGestureText}"/>
                                <Setter Property="ItemsSource" Value="{Binding Children}"/>
                                <Style.Triggers>
                                    <!-- 分隔线样式 -->
                                    <DataTrigger Binding="{Binding IsSeparator}" Value="True">
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="MenuItem">
                                                    <Separator Style="{DynamicResource {x:Static MenuItem.SeparatorStyleKey}}"
                                                               Margin="0,1"/>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </Setter.Value>
                    </Setter>
                </Style>
            </Menu.ItemContainerStyle>
        </Menu>
    </Border>
</UserControl>
