# GongSolutions.WPF.DragDrop 完整指南

## 概述

`gong-wpf-dragdrop` 是一个功能强大且易于使用的 WPF 拖拽框架，由 Jan Karger、<PERSON> Kirk 等开发者维护。它简化了 WPF 应用程序中的拖拽操作实现，支持 MVVM 模式，无需编写复杂的代码后台逻辑。

### 🌟 核心特性

- **MVVM 友好**：拖拽逻辑可以完全放在 ViewModel 中，通过附加属性绑定
- **多选支持**：支持多个项目同时拖拽
- **跨控件拖拽**：支持在同一控件内重新排序，或在不同控件间移动
- **广泛兼容**：支持 `ListBox`、`ListView`、`TreeView`、`DataGrid` 等所有 `ItemsControl`
- **灵活操作**：支持插入、移动、复制等多种操作
- **视觉反馈**：提供装饰器（Adorners）显示拖拽过程的视觉反馈
- **预览功能**：可显示被拖拽项目的预览
- **合理默认值**：为常见操作提供默认行为，减少代码量

### 📦 版本信息

- **当前版本**：4.0.0 (2024年12月)
- **支持框架**：.NET Framework 4.6.2+, .NET 6+ (Windows)
- **许可证**：BSD 3-Clause License
- **NuGet 包**：`gong-wpf-dragdrop`

## 安装和配置

### 1. 安装 NuGet 包

```xml
<PackageReference Include="gong-wpf-dragdrop" Version="4.0.0" />
```

或通过 Package Manager Console：
```powershell
Install-Package gong-wpf-dragdrop
```

### 2. 添加命名空间

在 XAML 文件中添加命名空间引用：

```xml
xmlns:dd="urn:gong-wpf-dragdrop"
```

## 基础用法

### 1. 简单的拖拽启用

最简单的用法是在控件上设置附加属性：

```xml
<ListBox ItemsSource="{Binding Items}"
         dd:DragDrop.IsDragSource="True"
         dd:DragDrop.IsDropTarget="True" />
```

### 2. 基础拖拽示例

```xml
<Grid>
    <Grid.ColumnDefinitions>
        <ColumnDefinition Width="*"/>
        <ColumnDefinition Width="*"/>
    </Grid.ColumnDefinitions>
    
    <!-- 源列表 -->
    <ListView Grid.Column="0"
              ItemsSource="{Binding SourceItems}"
              dd:DragDrop.IsDragSource="True"
              dd:DragDrop.UseDefaultDragAdorner="True">
        <ListView.ItemTemplate>
            <DataTemplate>
                <TextBlock Text="{Binding Name}" Padding="4"/>
            </DataTemplate>
        </ListView.ItemTemplate>
    </ListView>
    
    <!-- 目标列表 -->
    <ListView Grid.Column="1"
              ItemsSource="{Binding TargetItems}"
              dd:DragDrop.IsDropTarget="True" />
</Grid>
```

## 高级用法

### 1. 自定义拖拽处理器

实现 `IDragSource` 和 `IDropTarget` 接口来自定义拖拽行为：

```csharp
public class CustomDragDropHandler : IDragSource, IDropTarget
{
    // IDragSource 实现
    public void StartDrag(IDragInfo dragInfo)
    {
        dragInfo.Data = dragInfo.SourceItem;
        dragInfo.Effects = DragDropEffects.Move;
    }

    public bool CanStartDrag(IDragInfo dragInfo)
    {
        return dragInfo.SourceItem != null;
    }

    public void Dropped(IDropInfo dropInfo)
    {
        // 拖拽完成后的处理
    }

    public void DragDropOperationFinished(DragDropEffects operationResult, IDragInfo dragInfo)
    {
        // 操作完成后的清理工作
    }

    public bool TryCatchOccurredException(Exception exception)
    {
        // 异常处理
        return true; // 返回 true 表示异常已处理
    }

    public void DragCancelled()
    {
        // 拖拽取消处理
    }

    // IDropTarget 实现
    public void DragOver(IDropInfo dropInfo)
    {
        if (dropInfo.Data != null)
        {
            dropInfo.DropTargetAdorner = DropTargetAdorners.Highlight;
            dropInfo.Effects = DragDropEffects.Move;
        }
    }

    public void Drop(IDropInfo dropInfo)
    {
        var sourceItem = dropInfo.Data;
        var targetCollection = dropInfo.TargetCollection as IList;
        
        if (sourceItem != null && targetCollection != null)
        {
            targetCollection.Add(sourceItem);
        }
    }
}
```

### 2. 在 XAML 中绑定处理器

```xml
<ListView ItemsSource="{Binding Items}"
          dd:DragDrop.IsDragSource="True"
          dd:DragDrop.IsDropTarget="True"
          dd:DragDrop.DragHandler="{Binding DragDropHandler}"
          dd:DragDrop.DropHandler="{Binding DragDropHandler}" />
```

### 3. ViewModel 实现

```csharp
public partial class MyViewModel : ObservableObject, IDragSource, IDropTarget
{
    [ObservableProperty]
    private ObservableCollection<MyItem> sourceItems = new();

    [ObservableProperty]
    private ObservableCollection<MyItem> targetItems = new();

    // 实现 IDragSource 和 IDropTarget 接口...
}
```

## 核心接口详解

### IDragSource 接口

| 方法 | 说明 |
|------|------|
| `StartDrag(IDragInfo)` | 开始拖拽时调用，设置拖拽数据和效果 |
| `CanStartDrag(IDragInfo)` | 判断是否可以开始拖拽 |
| `Dropped(IDropInfo)` | 拖拽完成后调用 |
| `DragDropOperationFinished(DragDropEffects, IDragInfo)` | 拖拽操作完成后调用 |
| `TryCatchOccurredException(Exception)` | 处理拖拽过程中的异常 |
| `DragCancelled()` | 拖拽被取消时调用 |

### IDropTarget 接口

| 方法 | 说明 |
|------|------|
| `DragOver(IDropInfo)` | 拖拽悬停时调用，设置视觉反馈和允许的操作 |
| `Drop(IDropInfo)` | 执行放置操作 |

## 附加属性详解

### 基础属性

| 属性 | 类型 | 说明 |
|------|------|------|
| `IsDragSource` | bool | 启用拖拽源功能 |
| `IsDropTarget` | bool | 启用放置目标功能 |
| `DragHandler` | IDragSource | 自定义拖拽处理器 |
| `DropHandler` | IDropTarget | 自定义放置处理器 |

### 视觉效果属性

| 属性 | 类型 | 说明 |
|------|------|------|
| `UseDefaultDragAdorner` | bool | 使用默认拖拽装饰器 |
| `DefaultDragAdornerOpacity` | double | 默认装饰器透明度 |
| `DragAdornerTemplate` | DataTemplate | 自定义拖拽装饰器模板 |
| `DropTargetAdorner` | DropTargetAdorners | 放置目标装饰器类型 |

### 行为控制属性

| 属性 | 类型 | 说明 |
|------|------|------|
| `CanDragWithMouseRightButton` | bool | 允许右键拖拽 |
| `DragMouseAnchorPoint` | Point | 拖拽锚点 |
| `DragDirectlySelectedOnly` | bool | 只拖拽直接选中的项目 |
| `SelectDroppedItems` | bool | 放置后选中项目 |

## 装饰器类型

### DropTargetAdorners 枚举

- **None**：无装饰器
- **Insert**：插入位置指示器
- **Highlight**：高亮整个目标区域
- **Touch**：触摸友好的装饰器

## 实际应用场景

### 1. 文件管理器

```xml
<ListView ItemsSource="{Binding Files}"
          dd:DragDrop.IsDragSource="True"
          dd:DragDrop.IsDropTarget="True"
          dd:DragDrop.DragHandler="{Binding}"
          dd:DragDrop.DropHandler="{Binding}"
          dd:DragDrop.UseDefaultDragAdorner="True">
    <ListView.ItemTemplate>
        <DataTemplate>
            <StackPanel Orientation="Horizontal">
                <Image Source="{Binding Icon}" Width="16" Height="16"/>
                <TextBlock Text="{Binding Name}" Margin="4,0"/>
            </StackPanel>
        </DataTemplate>
    </ListView.ItemTemplate>
</ListView>
```

### 2. 任务管理

```xml
<Grid>
    <Grid.ColumnDefinitions>
        <ColumnDefinition Width="*"/>
        <ColumnDefinition Width="*"/>
        <ColumnDefinition Width="*"/>
    </Grid.ColumnDefinitions>
    
    <!-- 待办 -->
    <ListView Grid.Column="0" Header="待办"
              ItemsSource="{Binding TodoTasks}"
              dd:DragDrop.IsDragSource="True"
              dd:DragDrop.IsDropTarget="True"
              dd:DragDrop.DragHandler="{Binding}"
              dd:DragDrop.DropHandler="{Binding}" />
    
    <!-- 进行中 -->
    <ListView Grid.Column="1" Header="进行中"
              ItemsSource="{Binding InProgressTasks}"
              dd:DragDrop.IsDragSource="True"
              dd:DragDrop.IsDropTarget="True"
              dd:DragDrop.DragHandler="{Binding}"
              dd:DragDrop.DropHandler="{Binding}" />
    
    <!-- 已完成 -->
    <ListView Grid.Column="2" Header="已完成"
              ItemsSource="{Binding CompletedTasks}"
              dd:DragDrop.IsDropTarget="True"
              dd:DragDrop.DropHandler="{Binding}" />
</Grid>
```

### 3. 树形结构拖拽

```xml
<TreeView ItemsSource="{Binding TreeItems}"
          dd:DragDrop.IsDragSource="True"
          dd:DragDrop.IsDropTarget="True"
          dd:DragDrop.DragHandler="{Binding}"
          dd:DragDrop.DropHandler="{Binding}"
          dd:DragDrop.DropTargetAdorner="Highlight">
    <TreeView.ItemTemplate>
        <HierarchicalDataTemplate ItemsSource="{Binding Children}">
            <TextBlock Text="{Binding Name}"/>
        </HierarchicalDataTemplate>
    </TreeView.ItemTemplate>
</TreeView>
```

## 性能优化建议

### 1. 大数据集优化

```csharp
public void DragOver(IDropInfo dropInfo)
{
    // 避免在 DragOver 中执行耗时操作
    if (dropInfo.Data is MyItem item && IsValidTarget(dropInfo.TargetItem))
    {
        dropInfo.Effects = DragDropEffects.Move;
        dropInfo.DropTargetAdorner = DropTargetAdorners.Insert;
    }
}

private bool IsValidTarget(object target)
{
    // 快速验证逻辑
    return target != null;
}
```

### 2. 内存管理

```csharp
public void Drop(IDropInfo dropInfo)
{
    try
    {
        // 执行拖放操作
        PerformDrop(dropInfo);
    }
    finally
    {
        // 清理临时资源
        CleanupResources();
    }
}
```

## 常见问题和解决方案

### Q1: 拖拽不工作？
**解决方案**：
1. 确保已添加命名空间 `xmlns:dd="urn:gong-wpf-dragdrop"`
2. 检查 `IsDragSource` 和 `IsDropTarget` 属性设置
3. 验证数据绑定是否正确

### Q2: 如何禁用某些项目的拖拽？
**解决方案**：
```csharp
public bool CanStartDrag(IDragInfo dragInfo)
{
    var item = dragInfo.SourceItem as MyItem;
    return item?.CanDrag == true;
}
```

### Q3: 如何自定义拖拽预览？
**解决方案**：
```xml
<ListView dd:DragDrop.DragAdornerTemplate="{StaticResource CustomDragTemplate}">
    <ListView.Resources>
        <DataTemplate x:Key="CustomDragTemplate">
            <Border Background="LightBlue" CornerRadius="4" Padding="8">
                <TextBlock Text="{Binding Name}" FontWeight="Bold"/>
            </Border>
        </DataTemplate>
    </ListView.Resources>
</ListView>
```

### Q4: 如何处理跨应用程序拖拽？
**解决方案**：
```csharp
public void StartDrag(IDragInfo dragInfo)
{
    var item = dragInfo.SourceItem as MyItem;
    if (item != null)
    {
        dragInfo.Data = new DataObject(DataFormats.Text, item.ToString());
        dragInfo.Effects = DragDropEffects.Copy;
    }
}
```

## 最佳实践

### 1. MVVM 模式
- 将拖拽逻辑放在 ViewModel 中
- 使用命令模式处理拖拽事件
- 保持 View 和 ViewModel 的分离

### 2. 用户体验
- 提供清晰的视觉反馈
- 使用合适的装饰器类型
- 考虑触摸设备的使用体验

### 3. 性能考虑
- 避免在 `DragOver` 中执行重操作
- 合理使用虚拟化
- 及时清理资源

### 4. 错误处理
- 实现 `TryCatchOccurredException` 方法
- 提供用户友好的错误信息
- 记录详细的日志信息

## 社区和支持

- **GitHub 仓库**：https://github.com/punker76/gong-wpf-dragdrop
- **问题反馈**：GitHub Issues
- **讨论社区**：GitHub Discussions
- **Gitter 聊天**：https://gitter.im/punker76/gong-wpf-dragdrop

## 高级特性

### 1. 多选拖拽

```csharp
public void StartDrag(IDragInfo dragInfo)
{
    // 获取所有选中的项目
    var selectedItems = dragInfo.SourceItems.Cast<MyItem>().ToList();
    if (selectedItems.Any())
    {
        dragInfo.Data = selectedItems;
        dragInfo.Effects = DragDropEffects.Move;
    }
}

public void Drop(IDropInfo dropInfo)
{
    if (dropInfo.Data is IEnumerable<MyItem> items)
    {
        var targetCollection = dropInfo.TargetCollection as ObservableCollection<MyItem>;
        foreach (var item in items)
        {
            targetCollection?.Add(item);
        }
    }
}
```

### 2. 条件拖拽

```csharp
public bool CanStartDrag(IDragInfo dragInfo)
{
    var item = dragInfo.SourceItem as MyItem;

    // 根据业务逻辑判断是否允许拖拽
    return item != null &&
           item.IsEditable &&
           !item.IsLocked &&
           CurrentUser.HasPermission(Permission.Edit);
}

public void DragOver(IDropInfo dropInfo)
{
    var sourceItem = dropInfo.Data as MyItem;
    var targetItem = dropInfo.TargetItem as MyItem;

    // 根据业务规则判断是否允许放置
    if (sourceItem != null && IsValidDropTarget(sourceItem, targetItem))
    {
        dropInfo.Effects = DragDropEffects.Move;
        dropInfo.DropTargetAdorner = DropTargetAdorners.Insert;
    }
    else
    {
        dropInfo.Effects = DragDropEffects.None;
    }
}

private bool IsValidDropTarget(MyItem source, MyItem target)
{
    // 实现具体的业务验证逻辑
    return target?.Category == source?.Category;
}
```

### 3. 自定义拖拽效果

```xml
<!-- 自定义拖拽装饰器模板 -->
<DataTemplate x:Key="CustomDragAdorner">
    <Border Background="{DynamicResource AccentFillColorDefaultBrush}"
            BorderBrush="{DynamicResource AccentStrokeColorDefaultBrush}"
            BorderThickness="2"
            CornerRadius="8"
            Padding="12,8"
            Opacity="0.9">
        <StackPanel Orientation="Horizontal">
            <ui:SymbolIcon Symbol="ArrowMove24"
                           FontSize="16"
                           Foreground="White"
                           Margin="0,0,8,0"/>
            <TextBlock Text="{Binding Name}"
                       Foreground="White"
                       FontWeight="Medium"/>
            <Border Background="White"
                    CornerRadius="10"
                    Padding="6,2"
                    Margin="8,0,0,0">
                <TextBlock Text="{Binding Count, StringFormat='{0} 项'}"
                           FontSize="10"
                           Foreground="{DynamicResource AccentFillColorDefaultBrush}"/>
            </Border>
        </StackPanel>
    </Border>
</DataTemplate>

<!-- 使用自定义装饰器 -->
<ListView dd:DragDrop.DragAdornerTemplate="{StaticResource CustomDragAdorner}"
          dd:DragDrop.UseDefaultDragAdorner="False" />
```

### 4. 拖拽数据转换

```csharp
public class DataFormatConverter : IDropTarget
{
    public void DragOver(IDropInfo dropInfo)
    {
        // 处理不同数据格式
        if (dropInfo.Data is string text)
        {
            dropInfo.Effects = DragDropEffects.Copy;
        }
        else if (dropInfo.Data is MyItem item)
        {
            dropInfo.Effects = DragDropEffects.Move;
        }
        else if (dropInfo.Data is IDataObject dataObject)
        {
            if (dataObject.GetDataPresent(DataFormats.FileDrop))
            {
                dropInfo.Effects = DragDropEffects.Copy;
            }
        }
    }

    public void Drop(IDropInfo dropInfo)
    {
        switch (dropInfo.Data)
        {
            case string text:
                HandleTextDrop(text, dropInfo);
                break;
            case MyItem item:
                HandleItemDrop(item, dropInfo);
                break;
            case IDataObject dataObject when dataObject.GetDataPresent(DataFormats.FileDrop):
                var files = (string[])dataObject.GetData(DataFormats.FileDrop);
                HandleFilesDrop(files, dropInfo);
                break;
        }
    }
}
```

## 与其他框架集成

### 1. 与 Prism 集成

```csharp
// 在 Prism ViewModel 中使用
public class PrismDragDropViewModel : BindableBase, IDragSource, IDropTarget
{
    private readonly IEventAggregator _eventAggregator;

    public PrismDragDropViewModel(IEventAggregator eventAggregator)
    {
        _eventAggregator = eventAggregator;
    }

    public void Drop(IDropInfo dropInfo)
    {
        // 执行拖放操作
        PerformDrop(dropInfo);

        // 发布事件通知其他模块
        _eventAggregator.GetEvent<ItemMovedEvent>()
                       .Publish(new ItemMovedEventArgs(dropInfo.Data));
    }
}
```

### 2. 与 CommunityToolkit.Mvvm 集成

```csharp
public partial class ModernDragDropViewModel : ObservableObject, IDragSource, IDropTarget
{
    [ObservableProperty]
    private ObservableCollection<MyItem> items = new();

    [ObservableProperty]
    private string statusMessage = string.Empty;

    [RelayCommand]
    private void ResetItems()
    {
        Items.Clear();
        // 重新加载数据
    }

    public void StartDrag(IDragInfo dragInfo)
    {
        var item = dragInfo.SourceItem as MyItem;
        if (item != null)
        {
            dragInfo.Data = item;
            dragInfo.Effects = DragDropEffects.Move;
            StatusMessage = $"开始拖拽: {item.Name}";
        }
    }

    public void Drop(IDropInfo dropInfo)
    {
        // 实现拖放逻辑
        var item = dropInfo.Data as MyItem;
        if (item != null)
        {
            StatusMessage = $"拖放完成: {item.Name}";
        }
    }
}
```

## 测试策略

### 1. 单元测试

```csharp
[TestClass]
public class DragDropHandlerTests
{
    private DragDropHandler _handler;
    private Mock<IDragInfo> _mockDragInfo;
    private Mock<IDropInfo> _mockDropInfo;

    [TestInitialize]
    public void Setup()
    {
        _handler = new DragDropHandler();
        _mockDragInfo = new Mock<IDragInfo>();
        _mockDropInfo = new Mock<IDropInfo>();
    }

    [TestMethod]
    public void CanStartDrag_WithValidItem_ReturnsTrue()
    {
        // Arrange
        var item = new MyItem { Name = "Test" };
        _mockDragInfo.Setup(x => x.SourceItem).Returns(item);

        // Act
        var result = _handler.CanStartDrag(_mockDragInfo.Object);

        // Assert
        Assert.IsTrue(result);
    }

    [TestMethod]
    public void Drop_WithValidData_AddsItemToCollection()
    {
        // Arrange
        var item = new MyItem { Name = "Test" };
        var targetCollection = new ObservableCollection<MyItem>();

        _mockDropInfo.Setup(x => x.Data).Returns(item);
        _mockDropInfo.Setup(x => x.TargetCollection).Returns(targetCollection);

        // Act
        _handler.Drop(_mockDropInfo.Object);

        // Assert
        Assert.AreEqual(1, targetCollection.Count);
        Assert.AreEqual(item, targetCollection[0]);
    }
}
```

### 2. UI 自动化测试

```csharp
[TestClass]
public class DragDropUITests
{
    private Application _app;
    private Window _window;

    [TestInitialize]
    public void Setup()
    {
        _app = Application.Launch("MyApp.exe");
        _window = _app.GetWindow("MainWindow");
    }

    [TestMethod]
    public void DragDrop_BetweenLists_MovesItem()
    {
        // Arrange
        var sourceList = _window.Get<ListView>("SourceList");
        var targetList = _window.Get<ListView>("TargetList");
        var firstItem = sourceList.Items[0];

        // Act
        Mouse.StartDrag(firstItem);
        Mouse.Drop(targetList);

        // Assert
        Assert.AreEqual(0, sourceList.Items.Count);
        Assert.AreEqual(1, targetList.Items.Count);
    }
}
```

## 调试技巧

### 1. 启用调试输出

```csharp
public class DebuggableDragDropHandler : IDragSource, IDropTarget
{
    private static readonly ILogger Logger = LogManager.GetCurrentClassLogger();

    public void StartDrag(IDragInfo dragInfo)
    {
        Logger.Debug($"StartDrag: Source={dragInfo.SourceItem}, Effects={dragInfo.Effects}");
        // 实现拖拽逻辑
    }

    public void DragOver(IDropInfo dropInfo)
    {
        Logger.Debug($"DragOver: Data={dropInfo.Data}, Target={dropInfo.TargetItem}");
        // 实现悬停逻辑
    }

    public void Drop(IDropInfo dropInfo)
    {
        Logger.Info($"Drop: Data={dropInfo.Data}, Target={dropInfo.TargetItem}, Index={dropInfo.InsertIndex}");
        // 实现放置逻辑
    }
}
```

### 2. 可视化调试

```xml
<!-- 添加调试边框 -->
<ListView dd:DragDrop.IsDropTarget="True">
    <ListView.Style>
        <Style TargetType="ListView">
            <Style.Triggers>
                <Trigger Property="dd:DragDrop.IsDragInProgress" Value="True">
                    <Setter Property="BorderBrush" Value="Red"/>
                    <Setter Property="BorderThickness" Value="2"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </ListView.Style>
</ListView>
```

## 迁移指南

### 从原生 WPF 拖拽迁移

```csharp
// 原生 WPF 方式
private void ListView_PreviewMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
{
    var item = GetItemUnderMouse();
    if (item != null)
    {
        DragDrop.DoDragDrop(listView, item, DragDropEffects.Move);
    }
}

// gong-wpf-dragdrop 方式
public void StartDrag(IDragInfo dragInfo)
{
    dragInfo.Data = dragInfo.SourceItem;
    dragInfo.Effects = DragDropEffects.Move;
}
```

### 版本升级注意事项

- **3.x → 4.x**：检查接口变更，更新 .NET 版本要求
- **2.x → 3.x**：更新命名空间引用，检查过时的 API

## 总结

`gong-wpf-dragdrop` 是一个成熟、稳定且功能丰富的 WPF 拖拽框架。它的 MVVM 友好设计、丰富的自定义选项和良好的性能表现，使其成为 WPF 应用程序中实现拖拽功能的首选解决方案。

### 主要优势

1. **简化开发**：通过附加属性大大简化了拖拽功能的实现
2. **MVVM 支持**：完美支持 MVVM 模式，保持代码的可测试性
3. **高度可定制**：提供丰富的接口和属性进行自定义
4. **性能优秀**：经过优化，适用于大数据集场景
5. **社区活跃**：持续维护，问题响应及时

### 适用场景

- 文件管理器
- 任务管理系统
- 数据表格排序
- 树形结构操作
- 工作流设计器
- 图形编辑器

通过合理使用其提供的接口和属性，可以轻松实现从简单的列表重排序到复杂的跨控件数据传输等各种拖拽场景。
